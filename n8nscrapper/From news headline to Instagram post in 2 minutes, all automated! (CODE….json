{"name": "News poster (ENGLISH)", "nodes": [{"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/exports", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"design_id\": \"{{ $('Design job done?').item.json.job.result.design.id }}\",\n  \"format\": {\n    \"type\": \"png\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 560], "id": "49fb605f-a3c6-40d2-8bb8-5581e4d1839d", "name": "Create export job", "credentials": {"oAuth2Api": {"id": "bWv1B5JaqBFfkb9P", "name": "Canva Oauth2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "aeadbb77-e6e5-402c-8578-d0a81cd01e0c", "leftValue": "={{ $json.job.status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1100, 280], "id": "1a91cd69-15e0-4cc2-b97c-07f66b8e136e", "name": "Design job done?"}, {"parameters": {"url": "=https://api.canva.com/rest/v1/autofills/{{ $('Create design job').item.json.job.id }}", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, 280], "id": "898ebfc9-7026-4525-aaf3-3520a3e3e118", "name": "Get design job status", "credentials": {"oAuth2Api": {"id": "bWv1B5JaqBFfkb9P", "name": "Canva Oauth2"}}}, {"parameters": {"url": "=https://api.canva.com/rest/v1/exports/{{ $json.job.id }}", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [880, 560], "id": "de83216c-fe2c-4159-b86a-7548d503af06", "name": "Get export job status", "credentials": {"oAuth2Api": {"id": "bWv1B5JaqBFfkb9P", "name": "Canva Oauth2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "74fe206b-9d44-45ab-978c-a4c200d0cb0b", "leftValue": "={{ $json.job.status }}", "rightValue": "success", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1100, 560], "id": "a90492d4-81cd-451b-8988-d638acd1c3b1", "name": "Export job done?"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-200, 100], "id": "b49c6cc3-3cea-4697-a84d-b936f7663e55", "name": "Loop Over Items"}, {"parameters": {"jsCode": "const pages = $input.first().json.choices[0].message.content.pages;\nreturn pages.map(page => ({ json: page }))"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-420, 100], "id": "27812dc1-c1cf-48a8-ba46-122ef8ecb436", "name": "Split out pages"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.type }}", "rightValue": "cover", "operator": {"type": "string", "operation": "equals"}, "id": "dcd493ed-b2a6-49c6-a939-fd11c69b8dc0"}], "combinator": "and"}, "renameOutput": true, "outputKey": "cover"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "770f225e-dd12-420e-9930-bb90532d23e7", "leftValue": "={{ $json.type }}", "rightValue": "body", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "body"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [0, 280], "id": "f41719a9-7381-40cd-8ca2-42c68f225b40", "name": "Switch"}, {"parameters": {"assignments": {"assignments": [{"id": "5db73f6a-b945-4df3-8764-71d790b28442", "name": "request_body", "value": "={\n  \"brand_template_id\": \"{{ $('Set run variables').item.json.coverBrandTemplateId }}\",\n  \"data\": {\n    \"page_number\": {\n      \"type\": \"text\",\n      \"text\": \"{{ $json.page_number }}\"\n    },\n    \"title\": {\n      \"type\": \"text\",\n      \"text\": {{ JSON.stringify($json.title) }}\n    }\n  }\n}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 180], "id": "1a68fe54-b755-48f2-ba7f-a613e84613dd", "name": "Set cover fields"}, {"parameters": {"assignments": {"assignments": [{"id": "5db73f6a-b945-4df3-8764-71d790b28442", "name": "request_body", "value": "={\n  \"brand_template_id\": \"{{ $('Set run variables').item.json.bodyBrandTemplateId }}\",\n  \"data\": {\n    \"page_number\": {\n      \"type\": \"text\",\n      \"text\": \"{{ $json.page_number }}\"\n    },\n    \"body\": {\n      \"type\": \"text\",\n      \"text\": {{ JSON.stringify($json.body) }}\n    }\n  }\n}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 380], "id": "0201d3d4-e0e7-4d3b-988e-be70e03ac9e1", "name": "Set body fields"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [660, 560], "id": "4f52705c-9457-4e1e-bf3f-5d84da82749a", "name": "Wait (1s)", "webhookId": "6fe31313-1f24-48bf-9e69-cf7f3dbbb540"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [660, 280], "id": "0bc348d0-a406-4fdc-b3fb-c1b5a5a23e87", "name": "Wait (2s)", "webhookId": "adeef8e1-a4ea-4188-b044-a79677bdf756"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "={{ $('Set run variables').item.json.instagramAccountID }}", "edge": "media", "options": {"queryParameters": {"parameter": [{"name": "image_url", "value": "={{ $json.job.urls[0] }}"}, {"name": "is_carousel_item", "value": "true"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1320, 620], "id": "f4d2e9bb-4921-4e72-a64a-3e533ee97c41", "name": "Create page container", "credentials": {"facebookGraphApi": {"id": "lhGNyyzLNlHxgmT4", "name": "@companiamedios Instagram"}}}, {"parameters": {"jsCode": "// Este Function Node toma múltiples items con campos \"id\"\n// y genera un solo item con un array \"children\"\n\nconst children = items.map(item => item.json.id);\n\nreturn [\n  {\n    json: {\n      children: children\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, -60], "id": "a272c4bc-7a0f-4d48-850a-0764762f6c6b", "name": "Build children table"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "={{ $('Set run variables').first().json.instagramAccountID }}", "edge": "media", "options": {"queryParameters": {"parameter": [{"name": "media_type", "value": "CAROUSEL"}, {"name": "children", "value": "={{ $json.children }}"}, {"name": "caption", "value": "={{ $('Generate post').first().json.choices[0].message.content.caption }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [220, -60], "id": "b67f80a6-29ca-4f8a-a0d4-67d1ad4bb3bc", "name": "Create carousel container", "credentials": {"facebookGraphApi": {"id": "lhGNyyzLNlHxgmT4", "name": "@companiamedios Instagram"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "={{ $('Set run variables').first().json.instagramAccountID }}", "edge": "media_publish", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $('Create carousel container').item.json.id }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [440, -60], "id": "ca2bb78d-73b6-4fb2-bebb-ae26cc760a70", "name": "Post carousel", "credentials": {"facebookGraphApi": {"id": "lhGNyyzLNlHxgmT4", "name": "@companiamedios Instagram"}}, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/autofills", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.request_body }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 280], "id": "fa05549c-5635-4ce7-b29b-bac57a6dabc4", "name": "Create design job", "credentials": {"oAuth2Api": {"id": "bWv1B5JaqBFfkb9P", "name": "Canva Oauth2"}}}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/folders/move", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "to_folder_id", "value": "={{ $('Create generation folder').item.json.folder.id }}"}, {"name": "item_id", "value": "={{ $json.job.result.design.id }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 340], "id": "c11dec10-b9ff-4ad1-a7d2-1dbbb92a5067", "name": "Add to generation folder", "credentials": {"oAuth2Api": {"id": "bWv1B5JaqBFfkb9P", "name": "Canva Oauth2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "67f4ef4d-d9be-4198-89f8-160bb5472acd", "name": "instagramAccountID", "value": "Get this from your Facebook Developer portal!", "type": "string"}, {"id": "6c645fc6-5ef4-4356-8f8b-171a0f8e47bc", "name": "coverBrandTemplateId", "value": "Get this from the Canva list brand templates API!", "type": "string"}, {"id": "2ee0e51b-c257-4918-9418-bf08b9534a20", "name": "bodyBrandTemplateId", "value": "Get this from the Canva list brand templates API!", "type": "string"}, {"id": "68629cfc-971b-4955-8e94-6ca2c67150ba", "name": "generationParentFolderId", "value": "Get this from the Canva list folders API!", "type": "string"}]}, "includeOtherFields": true, "include": "selected", "includeFields": "<PERSON><PERSON>", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1620, 100], "id": "09a1366d-2cb6-422c-8582-075ea8824a8e", "name": "Set run variables"}, {"parameters": {"formTitle": "Create a news post", "formFields": {"values": [{"fieldLabel": "Topic", "fieldType": "textarea", "placeholder": "Ukraine's assault on Russian targets using drones", "requiredField": true}, {"fieldLabel": "Questions to answer", "fieldType": "textarea", "placeholder": "Why did he do this?"}, {"fieldLabel": "Search recommendations", "fieldType": "textarea", "placeholder": "Look for reputable sources like The New York Times..."}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-1840, 100], "id": "4ee94e74-9a36-4378-9c2a-eaa6b3fc3054", "name": "Form", "webhookId": "500d0884-f614-459f-a3ec-7901462d6b3e"}, {"parameters": {"modelId": {"__rl": true, "value": "google/gemini-2.5-pro-preview", "mode": "list", "cachedResultName": "GOOGLE/GEMINI-2.5-PRO-PREVIEW"}, "messages": {"values": [{"content": "=<prompt>\n  <system>\n    You are an editorial assistant specialized in digital communication. Your task is to transform a news summary into an informative post optimized for a Canva carousel. The content must be visually clear, professional, and easy to understand for a general audience. \n    Follow the data provided in the source exactly: respect **dates, figures, places, proper names, and chronological sequences**. Do not omit or alter relevant facts.\n  </system>\n\n  <output_format>\n    Return <only>valid JSON</only> (no markdown, no comments).\n\n    The JSON must have exactly this top-level structure:\n    {\n      \"caption\": \"…\",\n      \"pages\": [ … ]\n    }\n\n    <caption>\n      Brief text for social media platforms like Instagram or TikTok.\n      <requirements>\n        <max_characters>2200</max_characters>\n        <ideal_characters>≤300</ideal_characters>\n        <hashtags>maximum 10, minimum 3</hashtags>\n        <emojis>none</emojis>\n        <cta>Include a call to action like “Follow us for more news.” Be creative with the call to action. It must appear in the caption.</cta>\n        <style>informative, clear, neutral, and easy to understand</style>\n      </requirements>\n    </caption>\n\n    <pages>\n      Each element must have:\n      <fields>\n        <type>\"cover\" or \"body\"</type>\n        <page_number>format \"n/N\"</page_number>\n        <if_cover>\n          <title>maximum 64 characters (ideal ≤ 50)</title>\n        </if_cover>\n        <if_body>\n          <body>maximum 240 characters</body>\n        </if_body>\n      </fields>\n    </pages>\n  </output_format>\n\n  <constraints>\n    <rule>There must be exactly ONE \"cover\" page</rule>\n    <rule>There must be between 1 and 11 \"body\" pages</rule>\n    <rule>Total maximum: 12 pages</rule>\n    <rule>Ideal total number of pages: 6 to 8</rule>\n    <rule>Do not use emojis</rule>\n    <rule>Do not use hashtags on the pages (only in the caption)</rule>\n    <rule>Do not include quotes or textual references</rule>\n    <rule>Calculate the total page numbering correctly in the format \"n/N\"</rule>\n    <rule>Use informative, direct, neutral, and easy-to-follow language</rule>\n    <rule>Include statistics, figures, or relevant data exactly as they appear in the collected information. Do not invent or modify them</rule>\n    <rule>Preserve dates, names, and key data exactly as they appear in the source</rule>\n    <rule>Avoid value judgments; maintain an objective and professional tone</rule>\n  </constraints>\n\n  <input>\n    <topic>{{ $('Form').item.json.Topic }}</topic>\n    <questions_to_answer>\n      {{ $('Form').item.json['Questions to answer'] }}\n    </questions_to_answer>\n    <collected_information>\n      {{ $json.choices[0].message.content }}\n    </collected_information>\n  </input>\n\n  <output>\n    Return only the JSON.\n  </output>\n</prompt>", "role": "system"}]}, "simplify": false, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-800, 100], "id": "545777a8-847d-4e95-9c41-3485c8702e4d", "name": "Generate post", "credentials": {"openAiApi": {"id": "6EP5FRSkgeyD1crg", "name": "OpenRouter"}}}, {"parameters": {"modelId": {"__rl": true, "value": "perplexity/sonar", "mode": "list", "cachedResultName": "PERPLEXITY/SONAR"}, "messages": {"values": [{"content": "=<prompt>\n  <system>\n    You are a helpful research assistant with access to tools for searching the internet and extracting text from articles.\n  </system>\n\n  <instructions>\n    <step1><PERSON><PERSON> updated and accurate information on the topic specified by the user using your internal knowledge.</step1>\n    <step2>Perform between 1 and 3 searches in English or Spanish, depending on the most relevant language for the topic.</step2>\n    <step3>Read the body of promising articles.</step3>\n    <step4>Write a summary based on what you found, citing dates and sources.</step4>\n  </instructions>\n  \n  <rules>\n    <rule1>Include dates, years, and concrete sources in the final summary.</rule1>\n    <rule3>If the article does not contain relevant content, discard it.</rule3>\n    <rule4>Prefer recent and well-structured articles.</rule4>\n    <rule5>Write the summary in English if the topic has better sources in English; otherwise, use Spanish.</rule5>\n    <rule6>Do not use tables or lists; be clear and direct.</rule6>\n  </rules>\n\n  <input>\n    <topic>{{ $('Form').item.json.Topic }}</topic>\n    <questions_to_answer>\n      {{ $('Form').item.json['Questions to answer'] || \"Not specified\" }}\n    </questions_to_answer>\n    <search_recommendations>\n      {{ $('Form').item.json['Search recommendations'] || \"Not specified\" }}\n    </search_recommendations>\n  </input>\n\n  <output>\n    Return your detailed research with cited dates and sources.\n  </output>\n</prompt>", "role": "system"}]}, "simplify": false, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1180, 100], "id": "d4af09fc-c671-4d7d-82d8-ee44a87aaa06", "name": "Retrieve information", "credentials": {"openAiApi": {"id": "6EP5FRSkgeyD1crg", "name": "OpenRouter"}}}, {"parameters": {"content": "# Automated Insta News Account\nThis is a workflow that creates news posts for specified topics by using the Perplexity API to look for information and then processing said information into design templates with the Canva API to then post them on Instagram.\n\n## What do I need?\n* An OpenRouter API Key (for the OpenAI 'Message Model' blocks) \n* A Canva integration set up with Oauth2\n* An Instagram token set up with a Facebook page", "height": 300, "width": 700}, "type": "n8n-nodes-base.stickyNote", "position": [-1840, -280], "typeVersion": 1, "id": "fa61c6ea-0d28-49ca-a7f3-edb5140c97c5", "name": "<PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "https://api.canva.com/rest/v1/folders", "authentication": "genericCredentialType", "genericAuthType": "oAuth2Api", "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "generation"}, {"name": "parent_folder_id", "value": "={{ $('Set run variables').item.json.generationParentFolderId }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1400, 100], "id": "096f16e9-fef9-4c5a-9097-84e27dce2b37", "name": "Create generation folder", "credentials": {"oAuth2Api": {"id": "bWv1B5JaqBFfkb9P", "name": "Canva Oauth2"}}}], "pinData": {}, "connections": {"Create export job": {"main": [[{"node": "Wait (1s)", "type": "main", "index": 0}]]}, "Design job done?": {"main": [[{"node": "Add to generation folder", "type": "main", "index": 0}], [{"node": "Wait (2s)", "type": "main", "index": 0}]]}, "Get design job status": {"main": [[{"node": "Design job done?", "type": "main", "index": 0}]]}, "Get export job status": {"main": [[{"node": "Export job done?", "type": "main", "index": 0}]]}, "Export job done?": {"main": [[{"node": "Create page container", "type": "main", "index": 0}], [{"node": "Wait (1s)", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Build children table", "type": "main", "index": 0}], [{"node": "Switch", "type": "main", "index": 0}]]}, "Split out pages": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Set cover fields", "type": "main", "index": 0}], [{"node": "Set body fields", "type": "main", "index": 0}]]}, "Set body fields": {"main": [[{"node": "Create design job", "type": "main", "index": 0}]]}, "Set cover fields": {"main": [[{"node": "Create design job", "type": "main", "index": 0}]]}, "Wait (1s)": {"main": [[{"node": "Get export job status", "type": "main", "index": 0}]]}, "Wait (2s)": {"main": [[{"node": "Get design job status", "type": "main", "index": 0}]]}, "Create page container": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Build children table": {"main": [[{"node": "Create carousel container", "type": "main", "index": 0}]]}, "Create carousel container": {"main": [[{"node": "Post carousel", "type": "main", "index": 0}]]}, "Create design job": {"main": [[{"node": "Wait (2s)", "type": "main", "index": 0}]]}, "Add to generation folder": {"main": [[{"node": "Create export job", "type": "main", "index": 0}]]}, "Set run variables": {"main": [[{"node": "Create generation folder", "type": "main", "index": 0}]]}, "Form": {"main": [[{"node": "Set run variables", "type": "main", "index": 0}]]}, "Generate post": {"main": [[{"node": "Split out pages", "type": "main", "index": 0}]]}, "Retrieve information": {"main": [[{"node": "Generate post", "type": "main", "index": 0}]]}, "Post carousel": {"main": [[]]}, "Create generation folder": {"main": [[{"node": "Retrieve information", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "executionTimeout": 390}, "versionId": "94f9b6bf-2ccf-460b-b038-29268691ccf3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ffed4dcd350beadc352cb0370a59ec55e15bd43b9d2e31253e1d07d45fb3e581"}, "id": "06m384bjdsJi5tMr", "tags": []}