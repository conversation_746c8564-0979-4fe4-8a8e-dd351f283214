{"nodes": [{"parameters": {"resource": "webhook", "operation": "receive", "path": "call-center-incoming", "responseCode": 200}, "name": "[Trigger]_Incoming_Call", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 100], "id": "1", "webhookId": "your-webhook-id"}, {"parameters": {"functionCode": "const callData = $input.first().json;\nlet category = 'unknown';\nif (callData.status === 'answered') {\n  category = 'hot_lead';\n} else if (callData.status === 'declined') {\n  category = 'cold_lead';\n} else {\n  category = 'callback_needed';\n}\nreturn [{ json: { ...callData, category } }];"}, "name": "[Logic]_Categorize_Lead", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [400, 100], "id": "2"}, {"parameters": {"conditions": {"string": [{"value1": "{{$json.category}}", "operation": "equal", "value2": "hot_lead"}]}}, "name": "[Filter]_Hot_Lead", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 50], "id": "3"}, {"parameters": {"conditions": {"string": [{"value1": "{{$json.category}}", "operation": "equal", "value2": "cold_lead"}]}}, "name": "[Filter]_Cold_Lead", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 150], "id": "4"}, {"parameters": {"conditions": {"string": [{"value1": "{{$json.category}}", "operation": "equal", "value2": "callback_needed"}]}}, "name": "[Filter]_Callback_Needed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 250], "id": "5"}, {"parameters": {"url": "https://your-crm-api-endpoint.com/leads", "method": "POST", "jsonParameters": true, "options": {"bodyContentType": "json"}, "authentication": "predefinedCredentialType", "credentialType": "httpBasicAuth", "credentials": {"id": "your-crm-credentials-id"}}, "name": "[CRM]_Update_Hot_Lead", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [800, 50], "id": "6"}, {"parameters": {"phoneNumbers": "{{$json.phone}}", "message": "Hello, we noticed you declined our call. Can we assist you with anything? Reply to chat!", "credentials": {"id": "your-sms-credentials-id"}}, "name": "[SMS]_Cold_Lead_Followup", "type": "n8n-nodes-base.twilio", "typeVersion": 1, "position": [800, 150], "id": "7"}, {"parameters": {"triggerTimes": {"item": [{"mode": "custom", "value": "in 24 hours"}]}}, "name": "[Schedule]_Callback_Reminder", "type": "n8n-nodes-base.schedule", "typeVersion": 1, "position": [800, 250], "id": "8"}, {"parameters": {"phoneNumbers": "{{$json.phone}}", "message": "Reminder: We're trying to reach you for a follow-up. Please call us back at your convenience.", "credentials": {"id": "your-sms-credentials-id"}}, "name": "[SMS]_Callback_Followup", "type": "n8n-nodes-base.twilio", "typeVersion": 1, "position": [1000, 250], "id": "9"}, {"parameters": {"operation": "merge", "mode": "mergeByIndex"}, "name": "[Merge]_All_Streams", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [1200, 100], "id": "10"}, {"parameters": {"url": "https://your-analytics-endpoint.com/insights", "method": "POST", "jsonParameters": true, "options": {"bodyContentType": "json"}}, "name": "[Analytics]_Insights_Loop", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1400, 100], "id": "11"}, {"parameters": {"operation": "send", "to": "<EMAIL>", "subject": "Workflow Error: AI Call Center", "text": "An error occurred in the AI Call Center workflow. Details: {{$json.errorMessage}}", "credentials": {"id": "your-email-credentials-id"}}, "name": "[Error]_Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [600, 400], "id": "12"}], "connections": {"1": {"main": [{"node": "2", "type": "main", "index": 0}]}, "2": {"main": [{"node": "3", "type": "main", "index": 0}, {"node": "4", "type": "main", "index": 0}, {"node": "5", "type": "main", "index": 0}]}, "3": {"main": [{"node": "6", "type": "main", "index": 0}]}, "4": {"main": [{"node": "7", "type": "main", "index": 0}]}, "5": {"main": [{"node": "8", "type": "main", "index": 0}]}, "8": {"main": [{"node": "9", "type": "main", "index": 0}]}, "6": {"main": [{"node": "10", "type": "main", "index": 0}]}, "7": {"main": [{"node": "10", "type": "main", "index": 1}]}, "9": {"main": [{"node": "10", "type": "main", "index": 2}]}, "10": {"main": [{"node": "11", "type": "main", "index": 0}]}}, "id": "your-workflow-id", "name": "AI_Call_Center_Automation_v1", "active": false, "settings": {}, "versionId": "your-version-id", "staticData": {}, "createdAt": 1687046400000, "updatedAt": 1687046400000}