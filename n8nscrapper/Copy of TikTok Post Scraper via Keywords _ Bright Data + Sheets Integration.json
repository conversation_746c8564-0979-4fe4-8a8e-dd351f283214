{"name": "TikTok Post Scraper via Keywords | Bright Data + Sheets Integration", "nodes": [{"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "mode": "list", "value": "YOUR_GOOGLE_SHEET_ID", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_GOOGLE_SHEET_ID/edit?usp=drivesdk", "cachedResultName": "Tiktok by keyword data Scraping "}, "sheetName": {"__rl": true, "mode": "list", "value": 1067534155, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_GOOGLE_SHEET_ID/edit#gid=1067534155", "cachedResultName": "Tiktok by keyword"}, "columns": {"value": {"url": "={{ $json.url }}", "input": "={{ $json.input }}", "music": "={{ $json.music }}", "ratio": "={{ $json.ratio }}", "width": "={{ $json.width }}", "region": "={{ $json.region }}", "cdn_url": "={{ $json.cdn_url }}", "post_id": "={{ $json.post_id }}", "secu_id": "={{ $json.secu_id }}", "hashtags": "={{ $json.hashtags }}", "post_type": "={{ $json.post_type }}", "shortcode": "={{ $json.shortcode }}", "timestamp": "={{ $json.timestamp }}", "video_url": "={{ $json.video_url }}", "account_id": "={{ $json.account_id }}", "digg_count": "={{ $json.digg_count }}", "play_count": "={{ $json.play_count }}", "profile_id": "={{ $json.profile_id }}", "create_time": "={{ $json.create_time }}", "description": "={{ $json.description }}", "is_verified": "={{ $json.is_verified }}", "profile_url": "={{ $json.profile_url }}", "share_count": "={{ $json.share_count }}", "offical_item": "={{ $json.offical_item }}", "collect_count": "={{ $json.collect_count }}", "comment_count": "={{ $json.comment_count }}", "original_item": "={{ $json.original_item }}", "preview_image": "={{ $json.preview_image }}", "original_sound": "={{ $json.original_sound }}", "profile_avatar": "={{ $json.profile_avatar }}", "tt_chain_token": "={{ $json.tt_chain_token }}", "video_duration": "={{ $json.video_duration }}", "discovery_input": "={{ $json.discovery_input }}", "profile_username": "={{ $json.profile_username }}", "profile_biography": "={{ $json.profile_biography }}", "profile_followers": "={{ $json.profile_followers }}"}, "schema": [{"id": "url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "post_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "post_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "description", "type": "string", "display": true, "removed": false, "required": false, "displayName": "description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "create_time", "type": "string", "display": true, "removed": false, "required": false, "displayName": "create_time", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "digg_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "digg_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "share_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "share_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "collect_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "collect_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "comment_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "comment_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "play_count", "type": "string", "display": true, "removed": false, "required": false, "displayName": "play_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_duration", "type": "string", "display": true, "removed": false, "required": false, "displayName": "video_duration", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "hashtags", "type": "string", "display": true, "removed": false, "required": false, "displayName": "hashtags", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_sound", "type": "string", "display": true, "removed": false, "required": false, "displayName": "original_sound", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_username", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_username", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_avatar", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_avatar", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_biography", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_biography", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "preview_image", "type": "string", "display": true, "removed": false, "required": false, "displayName": "preview_image", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "post_type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "post_type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "discovery_input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "discovery_input", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "offical_item", "type": "string", "display": true, "removed": false, "required": false, "displayName": "offical_item", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "secu_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "secu_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "original_item", "type": "string", "display": true, "removed": false, "required": false, "displayName": "original_item", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "shortcode", "type": "string", "display": true, "removed": false, "required": false, "displayName": "shortcode", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "width", "type": "string", "display": true, "removed": false, "required": false, "displayName": "width", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "ratio", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ratio", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "video_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "music", "type": "string", "display": true, "removed": false, "required": false, "displayName": "music", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "cdn_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "cdn_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "is_verified", "type": "string", "display": true, "removed": false, "required": false, "displayName": "is_verified", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "account_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "account_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "profile_followers", "type": "string", "display": true, "removed": false, "required": false, "displayName": "profile_followers", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tt_chain_token", "type": "string", "display": true, "removed": false, "required": false, "displayName": "tt_chain_token", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "region", "type": "string", "display": true, "removed": false, "required": false, "displayName": "region", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "timestamp", "type": "string", "display": true, "removed": false, "required": false, "displayName": "timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "input", "type": "string", "display": true, "removed": false, "required": false, "displayName": "input", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["url"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "3c96466a-128d-403a-8449-b54001944b1d", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [2500, 360], "typeVersion": 4.6}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/s_mbghznsg225nzgyhz8", "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "options": {}}, "id": "e7b9dde9-0cd6-429e-b5ea-1e8269d7df7e", "name": "Decode Snapshot from Response", "type": "n8n-nodes-base.httpRequest", "position": [2140, 360], "typeVersion": 4.2}, {"parameters": {"amount": 1, "unit": "minutes"}, "id": "461df00a-925d-4bbf-b28e-7aa367ad54d5", "name": "Wait 1 minute", "type": "n8n-nodes-base.wait", "position": [1500, 380], "webhookId": "10b45556-5e91-4387-abd0-f9078b220286", "typeVersion": 1.1}, {"parameters": {"formTitle": "TikTok ", "formFields": {"values": [{"fieldLabel": "Keyword"}]}, "options": {}}, "id": "f15192de-4d53-4716-a2d1-876494712c81", "name": "Accepts keyword input from the user.", "type": "n8n-nodes-base.formTrigger", "position": [460, 380], "webhookId": "781b36e7-1076-4ddf-9541-bab8c9d4386c", "typeVersion": 2.2}, {"parameters": {"method": "POST", "url": "https://api.brightdata.com/datasets/v3/trigger", "sendQuery": true, "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_lu702nij2f790tmv9h"}, {"name": "include_errors", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "keyword"}, {"name": "limit_per_input", "value": "2"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "=[\n  {\n    \"search_keyword\": \"{{ $json.Keyword }}\",\n    \"country\": \"\"\n  },\n  {\n    \"search_keyword\": \"{{ $json.Keyword }}\",\n    \"country\": \"\"\n  },\n  {\n    \"search_keyword\": \"{{ $json.Keyword }}\",\n    \"country\": \"\"\n  }\n]", "options": {}}, "id": "a1bcfe64-ac31-43c2-b0d5-33caa9ef50a6", "name": "Sends keyword to Bright Data's Discover API to start scraping.", "type": "n8n-nodes-base.httpRequest", "position": [760, 380], "typeVersion": 4.2}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "options": {}}, "id": "da3fc0d4-98a6-48a3-b691-6e29db0969c0", "name": "Checks the scraping status using snapshot ID.", "type": "n8n-nodes-base.httpRequest", "position": [1160, 380], "typeVersion": 4.2, "alwaysOutputData": true}, {"parameters": {"content": "🟥 1. Form Trigger Node\n📌 Purpose:\n\nUser submits a keyword input.\n\nThis starts the scraping process.\n\n", "height": 340, "width": 280, "color": 3}, "id": "76797c1e-d329-4003-af3f-fac14a3fbdc8", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"content": "🟩 2. HTTP Request (Trigger Scraping on Bright Data)\n🔗 POST to Bright Data\n\nSends the keyword to Bright Data.\n\nTriggers scraping using dataset ID.\n\nTikTok keyword-based discovery initiated.\n\n", "height": 420, "width": 340, "color": 4}, "id": "b849575e-2a93-42e3-a084-f536e634e035", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [680, 160], "typeVersion": 1}, {"parameters": {"content": "🟦 3. Snapshot Progress Check\n🔍 GET - snapshot status\n\nFetches the current snapshot_id.\n\nChecks if the scraping task is finished (status == ready).", "height": 360, "width": 320, "color": 5}, "id": "e3648a2b-a0e7-41ab-9794-bd8fa1faf16a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, 220], "typeVersion": 1}, {"parameters": {"content": "🟪 4. IF Node (Check Completion)\n📌 Condition:\n\nIf snapshot is ready → move forward.\n\nElse → wait and retry.", "height": 360, "width": 260, "color": 2}, "id": "850f3c9d-2184-42b7-b6c5-e155c2d43f57", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1720, 200], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "35ed620d-b5d5-4e97-bcc5-52b283d85616", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "ready"}]}, "options": {}}, "id": "3a54a9d4-07ff-4e04-831b-f618e9bdd48b", "name": "Check Final Status", "type": "n8n-nodes-base.if", "position": [1800, 380], "typeVersion": 2.2}, {"parameters": {"content": "🟥 5. Wait Node\n🕒 Delays by 1 minute\n\nIf snapshot is not ready, pause for 1 minute.\n\nThen return to recheck loop.", "height": 380, "width": 280, "color": 6}, "id": "5cca9c7d-d10c-4817-9d10-d0c904ad059b", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1400, 200], "typeVersion": 1}, {"parameters": {"content": "🟨 6. Final Data Storage\n📌 Google Sheets Integration:\n\nStores extracted TikTok data.\n\nIncludes all post metrics and profile info.\n\n", "height": 380, "width": 300, "color": 3}, "id": "75b635ea-6e6c-41e7-8cae-168b2c6f8c00", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2400, 180], "typeVersion": 1}], "pinData": {}, "connections": {"Google Sheets": {"main": [[]]}, "Wait 1 minute": {"main": [[{"node": "Check Final Status", "type": "main", "index": 0}]]}, "Check Final Status": {"main": [[{"node": "Decode Snapshot from Response", "type": "main", "index": 0}], [{"node": "Checks the scraping status using snapshot ID.", "type": "main", "index": 0}]]}, "Decode Snapshot from Response": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Accepts keyword input from the user.": {"main": [[{"node": "Sends keyword to Bright Data's Discover API to start scraping.", "type": "main", "index": 0}]]}, "Checks the scraping status using snapshot ID.": {"main": [[{"node": "Wait 1 minute", "type": "main", "index": 0}]]}, "Sends keyword to Bright Data's Discover API to start scraping.": {"main": [[{"node": "Checks the scraping status using snapshot ID.", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "5153", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}