# N8N Workflow Scraper

A comprehensive scraper for downloading n8n workflows from n8nworkflows.xyz with Cloudflare bypass capabilities.

## Features

- 🚀 Cloudflare protection bypass using multiple techniques
- 🔄 Automatic retry mechanisms with exponential backoff
- 📊 Progress tracking and detailed logging
- 🛡️ Ethical scraping with rate limiting
- 📁 Organized workflow storage with metadata
- 🔍 Workflow validation and parsing
- 🎯 CLI interface with configuration options
- 🌐 Proxy support for enhanced anonymity

## Installation

### Node.js Setup
```bash
npm install
```

### Python Setup (optional, for additional bypass methods)
```bash
pip install -r requirements.txt
```

## Configuration

1. Copy `.env.example` to `.env`
2. Modify configuration values as needed

## Usage

### Basic Usage
```bash
npm start
```

### CLI Options
```bash
node src/scraper.js --help
```

### Advanced Usage
```bash
# Scrape with custom output directory
node src/scraper.js --output ./my-workflows

# Use specific concurrency
node src/scraper.js --concurrent 5

# Enable verbose logging
node src/scraper.js --verbose
```

## Output Structure

```
workflows/
├── metadata.json
├── categories/
│   ├── ai/
│   ├── automation/
│   └── integration/
└── raw/
    ├── workflow-1.json
    ├── workflow-2.json
    └── ...
```

## Ethical Usage

This scraper is designed for educational and personal use. Please:
- Respect the website's terms of service
- Use reasonable rate limits
- Don't overload the target server
- Consider reaching out to the site owner for bulk access

## License

MIT License - see LICENSE file for details.
