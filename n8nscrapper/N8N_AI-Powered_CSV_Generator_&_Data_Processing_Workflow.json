{
  "name": "AI-Powered CSV Generator & Data Processing",
  "nodes": [
    {
      "parameters": {},
      "name": "Start",
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [
        250,
        300
      ],
      "id": "d7f5a597-a29a-43e0-8f0e-09f5e9f8f7e1"
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "ai_model",
              "value": "gpt-3.5-turbo-instruct"
            },
            {
              "name": "data_topic",
              "value": "synthetic customer reviews for a new brand of eco-friendly sneakers"
            },
            {
              "name": "csv_columns_string",
              "value": "ReviewID,ProductName,CustomerName,RatingOutOf5,ReviewText,DateSubmitted"
            },
            {
              "name": "ai_instruction_detail",
              "value": "Ensure customer names are fictional and diverse. Ratings should be integers between 1 and 5. Review text should be between 20 and 100 words. Dates should be in YYYY-MM-DD format and within the last 60 days. ProductName should be one of 'EcoS<PERSON><PERSON> Walker', 'TerraFlex Runner', 'BioGrip Trail'."
            },
            {
              "name": "output_filename",
              "value": "ai_generated_sneaker_reviews.csv"
            }
          ],
          "number": [
            {
              "name": "record_count",
              "value": 10
            }
          ]
        },
        "options": {}
      },
      "name": "Set Parameters",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [
        450,
        300
      ],
      "id": "b2c3d4e5-f6a7-b8c9-d0e1-f2a3b4c5d6e7"
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "ai_prompt",
              "value": "={{ 'Please generate ' + $json.record_count + ' sample data records about \'' + $json.data_topic + '\'.\nThe data should strictly include the following columns, in this exact order: ' + $json.csv_columns_string + '.\nAdditional instructions for data generation: ' + $json.ai_instruction_detail + '.\nYour response MUST be a valid JSON array of objects. Each object in the array represents a single record, and each object\'s keys must exactly match the column names specified (e.g., if \'ReviewID\' is a column, the key should be \'ReviewID\'). Do not include any explanatory text before or after the JSON array. Just the JSON array itself.' }}"
            }
          ]
        },
        "options": {}
      },
      "name": "Construct AI Prompt",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [
        650,
        300
      ],
      "id": "a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6"
    },
    {
      "parameters": {
        "authentication": "openAiAccount",
        "resource": "chat",
        "model": "={{ $json.ai_model }}",
        "messages": [
          {
            "role": "user",
            "content": "={{ $('Construct AI Prompt').item.json.ai_prompt }}"
          }
        ],
        "options": {}
      },
      "name": "AI Generate Data (OpenAI)",
      "type": "n8n-nodes-ai.openAi",
      "typeVersion": 1,
      "position": [
        850,
        300
      ],
      "credentials": {
        "openAiApi": {
          "id": "PLEASE_REPLACE_WITH_YOUR_OPENAI_CREDENTIAL_ID",
          "name": "PLEASE_REPLACE_WITH_YOUR_OPENAI_CREDENTIAL_NAME"
        }
      },
      "id": "f9e8d7c6-b5a4-c3d2-e1f0-a9b8c7d6e5f4"
    },
    {
      "parameters": {
        "functionCode": "// Assuming the AI response is in the 'content' of the first choice's message (OpenAI Chat Completion format)\n// Adjust the path if your AI node or model returns data differently.\nconst aiResponseContent = items[0].json.choices[0].message.content;\nlet parsedData = [];\n\ntry {\n  // Attempt to parse the content directly as JSON\n  // The AI was instructed to return a JSON array of objects\n  parsedData = JSON.parse(aiResponseContent);\n\n  // Basic validation: ensure it's an array\n  if (!Array.isArray(parsedData)) {\n    // If not an array, try to find a JSON array within the string (simple heuristic)\n    const arrayMatch = aiResponseContent.match(/\[\\s*\\S[\\s\\S]*?\]/);\n    if (arrayMatch && arrayMatch[0]) {\n      parsedData = JSON.parse(arrayMatch[0]);\n      if (!Array.isArray(parsedData)) {\n        throw new Error(\"Extracted content is not a JSON array after attempting to find one.\");\n      }\n    } else {\n      throw new Error(\"AI response is not a JSON array and no array could be extracted.\");\n    }\n  }\n\n  // Further validation: ensure each item is an object (optional, but good practice)\n  parsedData = parsedData.filter(item => typeof item === 'object' && item !== null);\n\n} catch (error) {\n  console.error(\"Error parsing AI response:\", error.message);\n  console.error(\"Raw AI Response Content:\", aiResponseContent);\n  // Return an empty array or an error structure if parsing fails catastrophically\n  return [{ json: { error: \"Failed to parse AI response\", details: error.message, raw_response: aiResponseContent, parsed_data: [] } }];\n}\n\n// Ensure the output is in the format n8n expects for subsequent nodes (each item becomes an n8n item)\nreturn parsedData.map(item => ({ json: item }));"
      },
      "name": "Parse AI Response",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        1050,
        300
      ],
      "id": "c7d6e5f4-a3b2-c1d0-e9f8-a7b6c5d4e3f2"
    },
    {
      "parameters": {
        "fileFormat": "csv",
        "options": {
          "header": true,
          "delimiter": ","
        }
      },
      "name": "Create CSV",
      "type": "n8n-nodes-base.spreadsheetFile",
      "typeVersion": 1.2,
      "position": [
        1250,
        300
      ],
      "id": "b5a4c3d2-e1f0-a9b8-c7d6-e5f4a3b2c1d0"
    },
    {
      "parameters": {
        "fileName": "={{ $('Set Parameters').item.json.output_filename }}",
        "path": "/home/<USER>/output_files/",
        "binaryPropertyName": "data"
      },
      "name": "Save CSV to Local",
      "type": "n8n-nodes-base.writeBinaryFile",
      "typeVersion": 1,
      "position": [
        1450,
        300
      ],
      "id": "a3b2c1d0-e9f8-a7b6-c5d4-e3f2a1b0c9d8"
    }
  ],
  "connections": {
    "Start": {
      "main": [
        [
          {
            "node": "Set Parameters",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Set Parameters": {
      "main": [
        [
          {
            "node": "Construct AI Prompt",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Construct AI Prompt": {
      "main": [
        [
          {
            "node": "AI Generate Data (OpenAI)",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Generate Data (OpenAI)": {
      "main": [
        [
          {
            "node": "Parse AI Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Parse AI Response": {
      "main": [
        [
          {
            "node": "Create CSV",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create CSV": {
      "main": [
        [
          {
            "node": "Save CSV to Local",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {},
  "staticData": null,
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "placeholder_instance_id"
  },
  "pinData": {}
}

