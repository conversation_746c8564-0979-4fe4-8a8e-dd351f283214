{"name": "Real Estate Vapi Assistant", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.body.message.toolCalls[0].function.arguments }}", "options": {"systemMessage": "=You are a customer support AI Agent designed to handle various tasks efficiently. Your primary role is to book appointments,  You have access to a tools to help you fulfill requests\n\n\nInstructions:\nyou get : name, date and time\nwhen you receive details run BookAppointment to book the appointmnt\n\n- use BookAppointment tool to book a calendar appointment based on the details received by the user.\n- add a description and a summary for the calendar booking.\n- use leadDb tool to add the lead details to the google sheet\n- output only one sentence no special characters to confirm the booking\n- received date is old so turn it to the present date before booking the appointment\n- Here is the current time/date: {{$now.format('yyyy-MM-dd')}} .\n\n\n"}}, "id": "965dc12a-cf92-4d2e-bbd1-3f9c432ccc53", "name": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-488, -100], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-620, 100], "id": "8e1d6eb9-9b7f-4111-bd26-1a1b61b58a40", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "FkyxsFdN12E7mcvc", "name": "OpenAi account"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [-80, 100], "id": "e99111f7-dcaa-4013-9abf-6278ef83d7fb", "name": "bookAppointment", "credentials": {"googleCalendarOAuth2Api": {"id": "eyXxo2KRfojOJzoF", "name": "Google Calendar account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"results\": [\n    {\n      \"toolCallId\": \"{{ $('Webhook').item.json.body.message.toolCalls[0].id }}\",\n      \"result\": \"{{ $json.output }}\"\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-80, -100], "id": "9accd1ef-beb5-47d7-aedb-da3a4a4f841d", "name": "Respond to Webhook"}, {"parameters": {"httpMethod": "POST", "path": "82718d3d-5c98-4b54-9652-74a927f68967", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-740, -100], "id": "0fa71e24-b8fa-4dae-9ad0-207f895e0f92", "name": "Webhook", "webhookId": "82718d3d-5c98-4b54-9652-74a927f68967"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1dahrasFMmhWL6pT_8RQkM-YHICZlffRvdRQTTt-1RPQ", "mode": "list", "cachedResultName": "Real Estate Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1dahrasFMmhWL6pT_8RQkM-YHICZlffRvdRQTTt-1RPQ/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1dahrasFMmhWL6pT_8RQkM-YHICZlffRvdRQTTt-1RPQ/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Name": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Name', ``, 'string') }}", "date": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('date', ``, 'string') }}", "time": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('time', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "date", "displayName": "date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "time", "displayName": "time", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.6, "position": [-280, 120], "id": "48ab0d46-49e7-46bb-8bc2-6f7fe1410159", "name": "LeadDB", "credentials": {"googleSheetsOAuth2Api": {"id": "hIu6wQEY6Kr7qYOT", "name": "Google Sheets account"}}}], "pinData": {}, "connections": {"AI Calendar Assistant (LangChain)\t": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_languageModel", "index": 0}]]}, "bookAppointment": {"ai_tool": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_tool", "index": 0}]]}, "Webhook": {"main": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "main", "index": 0}]]}, "LeadDB": {"ai_tool": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "timezone": "Africa/Algiers", "callerPolicy": "workflowsFromSameOwner", "executionTimeout": -1}, "versionId": "108f9b0d-1718-4748-9769-273b9fae2cc9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "55782604fab58986ba2af883d663acd3d9de26c844bfc12fc98df190431b6e83"}, "id": "WPqlQ5my7ZIUY0Yh", "tags": [{"createdAt": "2025-06-09T13:17:23.735Z", "updatedAt": "2025-06-09T13:17:23.735Z", "id": "h4oBBSOTpdM8iPsB", "name": "ai-agent"}, {"createdAt": "2025-06-09T13:17:23.742Z", "updatedAt": "2025-06-09T13:17:23.742Z", "id": "xbHJl6qcHzAuOAUJ", "name": "telegram-bot"}, {"createdAt": "2025-06-09T13:17:24.137Z", "updatedAt": "2025-06-09T13:17:24.137Z", "id": "YLbhf5fS5RIKFVlW", "name": "video-search"}]}