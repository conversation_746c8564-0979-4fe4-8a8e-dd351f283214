{"name": "Google Maps Scraper", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1340, -180], "id": "52a4964f-1667-4654-adae-3b10900c5e23", "name": "When clicking ‘Test workflow’"}, {"parameters": {"promptType": "define", "text": "=You are a Google Maps search assistant designed to create highly specific and localized search queries.\n\nYou will be provided with a business type and a city name. Your task is to generate a comprehensive list of search queries by incorporating the subregions and neighborhoods of the given city.\n\nRequirements:\nUse the format: <business type> + in + <subregion> for each query.\nInclude all relevant subregions or neighborhoods of the specified city.\nEnsure proper capitalization and spelling in all search queries.\nAim to cover every subregion within the city to maximize search accuracy and reach.\n\nExample:\nBusiness: Plumbers\nCity: Los Angeles\n\nOutput:\nplumbers+in+crestview  \nplumbers+in+downtown  \nplumbers+in+brentwood  \nplumbers+in+hollywood  \nplumbers+in+echo+park  \n... and so on, until all subregions are listed.  \n\nYour goal is to provide a complete and exhaustive list for any city and business type provided.\nPlease create search queries in {{ $json.City }} city and {{ $json.Business }} business.\n\n**IMPORTANT**\n\nAlways include + signs instead of spaces in the seach queries.\nOutput in a valid JSON format, for example:\n\n{\n\"locations\": [\"plumbers+in+crestview\",\"plumbers+in+downtown\"]\n}", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-1020, -200], "id": "5122bd17-0943-4568-b64d-7c3d333c7f51", "name": "Basic LLM Chain"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"output\": [\"plumbers+in+chinatown\",\"plumbers+in+westlake\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-840, -360], "id": "6cd0fa25-0865-4b29-8df2-69007e516a83", "name": "Structured Output Parser"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1000, -360], "id": "d82cde80-dcf4-47cc-bd8a-04d4e6a3c3e8", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "BrIEgYW5zIX9SXA1", "name": "NEW API"}}}, {"parameters": {"fieldToSplitOut": "output.locations", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1160, -60], "id": "2053afa9-c2a4-4682-b406-09bcdcfa4a73", "name": "Split Out"}, {"parameters": {"assignments": {"assignments": [{"id": "5222727f-131b-418e-826d-4f9e5166703d", "name": "output", "value": "={{ $json['output.locations'] }}", "type": "string"}, {"id": "3840b137-beca-4463-b0e5-676bb162115a", "name": "location", "value": "={{ $('Edit Fields1').item.json.City }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1020, -60], "id": "7dd286ae-108f-403d-9414-cd6e386479c2", "name": "<PERSON>"}, {"parameters": {"options": {"reset": false}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-520, -340], "id": "02db26b7-83f5-4105-8fac-7ab0b632e76f", "name": "Loop Over Items", "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "780c4ae0-34ed-49eb-90f5-363a07dc1e0b", "name": "email", "value": "={{ $json.emails }}", "type": "string"}, {"id": "bb6c6ef9-70ba-4cac-ab0d-8bd3c2432b02", "name": "search term", "value": "={{ $('Loop Over Items').item.json.output }}", "type": "string"}, {"id": "e46a2626-34c0-4a0b-b5aa-e9997016d890", "name": "location", "value": "={{ $('Loop Over Items').item.json.location }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, -220], "id": "e46dfc93-b050-4c40-8587-b39f92ceb7b7", "name": "Edit Fields3"}, {"parameters": {"maxItems": 5}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-860, -60], "id": "ed1fcf26-faec-4bf5-953c-109091d66f25", "name": "Limit"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [300, -60], "id": "f500213e-5bd2-4a80-83b4-d9abdccbd777", "name": "Wait", "webhookId": "19cc6ed4-4fe7-485b-b879-c679e4b3374d"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [160, -360], "id": "aef27460-7f05-4107-9a99-635993409cdc", "name": "Wait1", "webhookId": "0fe34756-6e43-4603-8891-5747a9a6500a"}, {"parameters": {"jsCode": "const input = $input.first().json.data\nconst regex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.(?!jpeg|jpg|png|gif|webp|svg)[a-zA-Z]{2,}/g\nconst emails = input.match(regex)\nreturn {json: {emails:emails}}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, -60], "id": "95c27654-64f7-49bf-a10f-3a868de60cb5", "name": "Extract Emails", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "const input = $input.first().json.data\nconst regex = /https?:\\/\\/[^\\/\\s\"'>]+/g\nconst websites = input.match(regex)\nreturn websites.map(website => ({json:{website}}))"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-520, -200], "id": "9ec0d6d5-af41-42b2-aac2-1ab2ac86a405", "name": "Extract URLs"}, {"parameters": {"url": "={{ $json.website }}", "options": {"redirect": {"redirect": {"followRedirects": false}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, -60], "id": "17db6faf-6e5e-44e7-ac44-13b7dbcc12c9", "name": "Scrape Site", "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6786c58-424a-409a-b87f-8a7592cb7944", "leftValue": "={{ $json.emails }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [300, -360], "id": "77559263-7dad-49c1-a1e2-cc780b94bdda", "name": "Filter Out Empties"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bf0a5053-9660-457c-9581-964793bb6d7d", "leftValue": "={{ $json.website }}", "rightValue": "schema", "operator": {"type": "string", "operation": "notContains"}}, {"id": "9110b9e0-12aa-45cc-bde0-9eda8c10970e", "leftValue": "={{ $json.website }}", "rightValue": "google", "operator": {"type": "string", "operation": "notContains"}}, {"id": "fb9b6ed6-96a5-4560-ab10-b8a4b9a61a2b", "leftValue": "={{ $json.website }}", "rightValue": "gg", "operator": {"type": "string", "operation": "notContains"}}, {"id": "10500c0b-cdbd-4816-aba3-df60d69845dc", "leftValue": "={{ $json.website }}", "rightValue": "gstatic", "operator": {"type": "string", "operation": "notContains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-340, -200], "id": "1337785d-4720-44ad-9f09-46fbfe30b0d9", "name": "Filter Google URLs"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [160, -220], "id": "a8596dc1-4783-45e0-9d02-7f8829f7649b", "name": "Remove Duplicates (2)"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [-520, -60], "id": "c322448b-e691-4b31-b3a2-5a0a92ccd480", "name": "Remove Duplicates2"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-20, -220], "id": "9f562186-4e43-4b95-87fb-47e38e438b59", "name": "Loop Over Items3"}, {"parameters": {"maxItems": 20}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-340, -60], "id": "122ace50-72b7-407c-97d5-83d91bd04304", "name": "Limit1"}, {"parameters": {"fieldToSplitOut": "emails", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [440, -360], "id": "1ee214ea-3f23-4cc4-8a6a-5e1b5fc31594", "name": "Split Out2"}, {"parameters": {"assignments": {"assignments": [{"id": "5dd87647-6e4f-4a51-98d6-10d4965b5b23", "name": "City", "value": "California", "type": "string"}, {"id": "d85aeff3-6a3d-479c-b3a0-01c7d7c33b37", "name": "Business", "value": "Marketing Agencies", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1160, -200], "id": "596d0533-d75a-4137-bb53-7e8308ed95f6", "name": "Edit Fields1"}, {"parameters": {"content": "# Step-1 \n## Generating targeted search queries covering every neighborhood in selected city.", "height": 660, "width": 720}, "type": "n8n-nodes-base.stickyNote", "position": [-1400, -520], "typeVersion": 1, "id": "082208eb-a921-4302-96dd-880cde133436", "name": "<PERSON><PERSON>"}, {"parameters": {"url": "=https://www.google.com/maps/search/{{ $json.output }}", "options": {"allowUnauthorizedCerts": true, "response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-340, -340], "id": "e9a85106-63f8-4f82-b25f-45df78833de7", "name": "Scrape Google Maps"}, {"parameters": {"content": "# Step-2\n## Visit Google Maps & scrape", "height": 660, "width": 440, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-600, -520], "typeVersion": 1, "id": "33546ade-894c-498c-a1a4-a922bb431b06", "name": "Sticky Note1"}, {"parameters": {"content": "# Step-3\n## Visit website and scrape unique emails", "height": 660, "width": 720, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-80, -520], "typeVersion": 1, "id": "8503702e-26cd-43c6-857f-bda58fed7632", "name": "Sticky Note2"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Scrape Google Maps", "type": "main", "index": 0}]]}, "Edit Fields3": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Extract Emails", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Filter Out Empties", "type": "main", "index": 0}]]}, "Extract Emails": {"main": [[{"node": "Loop Over Items3", "type": "main", "index": 0}]]}, "Extract URLs": {"main": [[{"node": "Filter Google URLs", "type": "main", "index": 0}]]}, "Scrape Site": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Filter Out Empties": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Filter Google URLs": {"main": [[{"node": "Remove Duplicates2", "type": "main", "index": 0}]]}, "Remove Duplicates (2)": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}, "Remove Duplicates2": {"main": [[{"node": "Limit1", "type": "main", "index": 0}]]}, "Loop Over Items3": {"main": [[{"node": "Wait1", "type": "main", "index": 0}], [{"node": "Scrape Site", "type": "main", "index": 0}]]}, "Limit1": {"main": [[{"node": "Loop Over Items3", "type": "main", "index": 0}]]}, "Split Out2": {"main": [[{"node": "Remove Duplicates (2)", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Scrape Google Maps": {"main": [[{"node": "Extract URLs", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a34493d4-54eb-4ce9-a5a2-a73acd9d1f38", "meta": {"templateCredsSetupCompleted": true, "instanceId": "8aa92e41e673d2ee2e9840d86fa9e9e74cd1aeae89c0cf2543605789a1564646"}, "id": "YHZcoAEcgoUKlrQ8", "tags": []}