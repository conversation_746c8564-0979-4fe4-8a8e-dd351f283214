{"id": "147", "meta": {"instanceId": "dfdeafd1c3ed2ee08eeab8c2fa0c3f522066931ed8138ccd35dc20a1e69decd3"}, "name": "OpenAI-model-examples", "tags": [], "nodes": [{"id": "ad6dc2cd-21cc-4563-86ba-f78cc4a55543", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [-640, 380], "parameters": {}, "typeVersion": 1}, {"id": "b370da23-ead4-4221-b7fe-a9d943f7fbb9", "name": "davinci-003-complete", "type": "n8n-nodes-base.openAi", "position": [1160, 60], "parameters": {"prompt": "={{ $json.text }}\n\nTl;dr:", "options": {"maxTokens": 500}}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "5e04f355-36c0-4540-8e65-68118cb73135", "name": "ChatGPT-ex2", "type": "n8n-nodes-base.openAi", "position": [1160, 740], "parameters": {"prompt": {"messages": [{"role": "system", "content": "=You are an assistant. Always add 5 emojis to the end of your answer."}, {"content": "=Write tl;dr of the wollowing text: {{ $json.text}}"}]}, "options": {"maxTokens": 500, "temperature": 0.8}, "resource": "chat"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "16a7cf80-16e3-44f9-b15c-7501417fe38f", "name": "davinci-003-edit", "type": "n8n-nodes-base.openAi", "position": [1340, 60], "parameters": {"input": "={{ $json.text }}", "options": {}, "operation": "edit", "instruction": "translate to German"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "********-65c3-4714-83fb-20ba2c0ca007", "name": "ChatGPT-ex1.1", "type": "n8n-nodes-base.openAi", "position": [1160, 380], "parameters": {"prompt": {"messages": [{"content": "=Write a Tl;dr of the followint text: {{ $json.text }}"}]}, "options": {"maxTokens": 500}, "resource": "chat"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "be9c4820-18b0-46fd-a5a0-51a5dc3ebed5", "name": "ChatGPT-ex1.2", "type": "n8n-nodes-base.openAi", "position": [1340, 380], "parameters": {"prompt": {"messages": [{"content": "=Translate to German the following text: {{ $json.message.content }}"}]}, "options": {"maxTokens": 500}, "resource": "chat"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "c52c875b-5270-44ac-bfca-ce25124e3d04", "name": "Text-example", "type": "n8n-nodes-base.code", "position": [540, 380], "parameters": {"jsCode": "return [\n  {\n    \"text\": \"Science Underground with your host, <PERSON><PERSON>. In this episode, how to stop your bathroom mirror from fogging up with a little dash of science. I'm <PERSON><PERSON> and this is Science Underground. We've all been there. You come out of the shower and you go to the mirror and you can't see yourself because the mirror is fogged up. You can't see anything until you first clear off the surface. Every morning it's the same thing. Shower, fog, shower, fog, shower, fog. There's gotta be a better way. Well, there is. Before you take the next shower, wipe a bit of shaving cream on the surface of the mirror and keep it there for about 30 seconds. Then wipe it off. The next time you take a shower, that part of the mirror that was covered with shaving cream will be amazingly fog free. And the shaving cream will keep the water from fogging up for a few weeks. So what's going on? Well, the fog on your mirror is made out of little itty bitty water droplets. If you were to look at the surface of the mirror under the microscope, you will see that the surface looks like a newly waxed car. The water forms beads, preventing you from seeing yourself in the mirror. When you add shaving cream to the surface of the mirror, the water droplets are no longer beads. They are a thin, smoothed out layer of water. Just like the surface of an old car that hasn't been waxed. Scientists would say that the shaving cream has changed the surface tension of the mirror. So there you have it. There's the answer. The secret to fogless mirrors is shaving cream. A little dab of science will do you. I'm <PERSON><PERSON>, and this was Science Underground.\"\n  }\n];"}, "typeVersion": 1}, {"id": "45d3bad7-0e9a-426b-b4e9-b3568181d9dc", "name": "Code-ex3.1", "type": "n8n-nodes-base.code", "position": [1160, 1100], "parameters": {"jsCode": "var intext =  $input.first().json;\n\nvar messages = [\n  {\"role\": \"system\", \"content\": \"You are a helpful assistant. Write a Tl;dr of each user message\"},\n  {\"role\": \"user\", \"content\": intext.text}\n];\n\nreturn {\"messages\":messages};"}, "typeVersion": 1}, {"id": "4db3de05-51a7-46ea-a818-508bdcb04582", "name": "ChatGPT-ex3.1", "type": "n8n-nodes-base.httpRequest", "position": [1340, 1100], "parameters": {"url": "https://api.openai.com/v1/chat/completions", "method": "POST", "options": {}, "sendBody": true, "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-3.5-turbo"}, {"name": "temperature", "value": "={{ parseFloat(0.8) }}"}, {"name": "n", "value": "={{ Number(1) }}"}, {"name": "max_tokens", "value": "={{ Number(500) }}"}, {"name": "messages", "value": "={{ $json.messages }}"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 3}, {"id": "709fcd7c-deb3-469d-b16b-62d4d36d100d", "name": "ChatGPT-ex3.2", "type": "n8n-nodes-base.openAi", "position": [1880, 1100], "parameters": {"prompt": {"messages": [{"role": "system", "content": "=You are now a DALLE-2 prompt generation tool that will generate a suitable prompt. Write a promt to create a cover image relevant to the user input. The image should be in a comic style of the 60-s."}, {"content": "={{ $json.choices[0].message.content }}"}]}, "options": {"maxTokens": 500, "temperature": 0.8}, "resource": "chat"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "6b32cc45-5ba2-4605-b690-3929ec9acecf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [900, -60], "parameters": {"width": 746.*************, "height": 295.**************, "content": "## The old way of using text completion and text edit\n### Davinci model is 10 times more expensive then ChatGPT, consider switching to the new API:\nhttps://openai.com/blog/introducing-chatgpt-and-whisper-apis\n"}, "typeVersion": 1}, {"id": "3cc74d77-7b02-40fd-83d8-f540d5ff34ab", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-160, 260], "parameters": {"width": 428.*************, "height": 316.*************, "content": "## Whisper-1 example\n### Prepare your audio file and send it to whisper-1 transcription model"}, "typeVersion": 1}, {"id": "6ba8069a-485c-497c-8b27-4c7562fbccab", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [380, 280], "parameters": {"width": 421.*************, "height": 302.4086532331564, "content": "## An example of transcribed text\n### Please pause this node when using real audio files"}, "typeVersion": 1}, {"id": "c71001e6-b80f-41dd-bcdd-10927014b374", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [900, 280], "parameters": {"width": 747.8556016477869, "height": 288.**************, "content": "## ChatGPT example 1.1 and 1.2 \n### Write a Tl;dr of the text input\n### Translate it to German\n### only user content provided"}, "typeVersion": 1}, {"id": "4605be68-4c57-404f-8624-e095c8e86ff9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [900, 620], "parameters": {"width": 742.9723747088658, "height": 288.**************, "content": "## ChatGPT example 2 \n### Use system content to provide general instruction\n### Manual setup of system and user content"}, "typeVersion": 1}, {"id": "f5b72d7a-655a-4cc9-b722-b75429889d1d", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [900, 960], "parameters": {"width": 739.************, "height": 288.**************, "content": "## ChatGPT example 3.1\n### When using ChatGPT programmatically, create an array of system / user / assistant contents and append them one after another\n### Call ChatGPT API via HTTP Request node to provide all messages at once"}, "typeVersion": 1}, {"id": "a003a4db-1960-4867-8dfe-3114cf0742f3", "name": "DALLE-ex3.3", "type": "n8n-nodes-base.openAi", "position": [2060, 1100], "parameters": {"prompt": "={{ $json.message.content }}", "options": {"n": 4, "size": "512x512"}, "resource": "image"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "d71a01ff-4d47-4675-964c-c47820d3989b", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1720, 960], "parameters": {"width": 611.*************, "height": 284.**************, "content": "## ChatGPT example 3.2 & DALLE-2 example 3.3\n### Use ChatGPT to create a prompt for a cover image of the Tl;dr message\n### Use OpenAI node to generate 4 images using the auto-generated prompt"}, "typeVersion": 1}, {"id": "f5a55cfe-c110-4833-9668-1f1ba895860f", "name": "ChatGPT-ex4", "type": "n8n-nodes-base.openAi", "position": [1240, 1420], "parameters": {"model": "gpt-3.5-turbo-0301", "prompt": {"messages": [{"content": "={{ $json.prompt }}"}]}, "options": {"maxTokens": 500, "temperature": 0.5}, "resource": "chat"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "8a9f7a20-187c-4494-8005-b10d066d04e2", "name": "Set-ex4", "type": "n8n-nodes-base.set", "position": [1060, 1420], "parameters": {"values": {"string": [{"name": "model", "value": "code-davinci-002"}, {"name": "suffix", "value": "</svg>"}, {"name": "prompt", "value": "=Create an HTML code with and SVG tag that contains random shapes of various colors. Include triangles, lines, ellipses and other shapes"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "68fcc6a2-761c-42ac-8778-313c8db7d53c", "name": "HTML-ex4", "type": "n8n-nodes-base.html", "position": [1420, 1420], "parameters": {"html": "{{$json.message.content }}"}, "typeVersion": 1}, {"id": "1f70cf3f-b6a9-4ea7-9486-c7565e6951b7", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [900, 1300], "parameters": {"width": 739.************, "height": 288.**************, "content": "## ChatGPT example 4\n### Generate HTML code that contains SVG image"}, "typeVersion": 1}, {"id": "d857acd9-ea74-44d2-ac89-66b1fac4645f", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [900, 1640], "parameters": {"width": 739.************, "height": 288.**************, "content": "## ChatGPT example 5\n### Provide several outputs. Useful for quick replies (i.e. in Gmail / Outlook)"}, "typeVersion": 1}, {"id": "fe64533a-4cd4-4adc-a48a-8abf3f2d61d7", "name": "ChatGPT-ex", "type": "n8n-nodes-base.openAi", "position": [1160, 1760], "parameters": {"model": "gpt-3.5-turbo-0301", "prompt": {"messages": [{"role": "system", "content": "Act as an e-mail client. Provide a five to eight word answers to a given user messages."}, {"content": "Hi There! My name is <PERSON>.\n\nI'm sending you an overview of my pricelist attached.\nCould you please reply to me within 3 days?\n\nBest regards and have a nice day,\n<PERSON>"}]}, "options": {"n": 3, "maxTokens": 15, "temperature": 0.8}, "resource": "chat"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "6c9f8a70-99ae-4310-8e6a-26cc6f75b3a2", "name": "LoadMP3", "type": "n8n-nodes-base.readBinaryFiles", "disabled": true, "position": [-80, 380], "parameters": {"fileSelector": "/home/<USER>/.n8n/OpenAI-article/Using Science to Stop Your Mirror From Fogging Up.mp3"}, "typeVersion": 1}, {"id": "0edc1996-6484-4e62-a47b-5666dfbb3546", "name": "Whisper-transcribe", "type": "n8n-nodes-base.httpRequest", "disabled": true, "position": [100, 380], "parameters": {"url": "https://api.openai.com/v1/audio/transcriptions", "method": "POST", "options": {}, "sendBody": true, "contentType": "multipart-form-data", "authentication": "predefinedCredentialType", "bodyParameters": {"parameters": [{"name": "model", "value": "whisper-1"}, {"name": "file", "parameterType": "formBinaryData", "inputDataFieldName": "data"}]}, "nodeCredentialType": "openAiApi"}, "credentials": {"openAiApi": {"id": "63", "name": "OpenAi account"}}, "typeVersion": 3}, {"id": "c12ba294-bdcd-4ece-8370-fa6a83a8ef0b", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-840, 260], "parameters": {"width": 596.*************, "height": 320.**************, "content": "## Do not run the whole workflow, it's rather slow\n### Better execute the last node of each branch or simply disconnect branches that are not needed"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"callerPolicy": "workflowsFromSameOwner", "saveManualExecutions": false, "saveDataSuccessExecution": "none"}, "versionId": "972cd971-9e7e-4a1d-b3fb-6f061e23e96f", "connections": {"LoadMP3": {"main": [[{"node": "Whisper-transcribe", "type": "main", "index": 0}]]}, "Set-ex4": {"main": [[{"node": "ChatGPT-ex4", "type": "main", "index": 0}]]}, "Code-ex3.1": {"main": [[{"node": "ChatGPT-ex3.1", "type": "main", "index": 0}]]}, "ChatGPT-ex4": {"main": [[{"node": "HTML-ex4", "type": "main", "index": 0}]]}, "Text-example": {"main": [[{"node": "davinci-003-complete", "type": "main", "index": 0}, {"node": "ChatGPT-ex1.1", "type": "main", "index": 0}, {"node": "ChatGPT-ex2", "type": "main", "index": 0}, {"node": "Code-ex3.1", "type": "main", "index": 0}]]}, "ChatGPT-ex1.1": {"main": [[{"node": "ChatGPT-ex1.2", "type": "main", "index": 0}]]}, "ChatGPT-ex3.1": {"main": [[{"node": "ChatGPT-ex3.2", "type": "main", "index": 0}]]}, "ChatGPT-ex3.2": {"main": [[{"node": "DALLE-ex3.3", "type": "main", "index": 0}]]}, "Whisper-transcribe": {"main": [[{"node": "Text-example", "type": "main", "index": 0}]]}, "davinci-003-complete": {"main": [[{"node": "davinci-003-edit", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "LoadMP3", "type": "main", "index": 0}, {"node": "Set-ex4", "type": "main", "index": 0}, {"node": "ChatGPT-ex", "type": "main", "index": 0}]]}}}