{"name": "YT Translator App", "flow": [{"id": 1, "module": "tally:watchNewResponse", "version": 1, "parameters": {"__IMTHOOK__": 1915259}, "mapper": {}, "metadata": {"designer": {"x": -255, "y": 2}, "restore": {"parameters": {"__IMTHOOK__": {"data": {"editable": "false"}, "label": "My New Response webhook"}}}, "parameters": [{"name": "__IMTHOOK__", "type": "hook:tally", "label": "Webhook", "required": true}], "interface": [{"name": "eventId", "type": "text", "label": "Event ID"}, {"name": "responseId", "type": "text", "label": "Response ID"}, {"name": "submissionId", "type": "text", "label": "Submission ID"}, {"name": "respondentId", "type": "text", "label": "Respondent ID"}, {"name": "formId", "type": "text", "label": "Form ID"}, {"name": "formName", "type": "text", "label": "Form Name"}, {"name": "createdAt", "type": "date", "label": "Created at"}, {"name": "fields", "spec": [{"name": "Your Email", "type": "email"}, {"name": "Name", "type": "text"}, {"name": "YouTube Video", "type": "text"}, {"name": "Language", "spec": {"type": "text"}, "type": "array"}, {"name": "Payment 💸 (price)", "type": "number"}, {"name": "Payment 💸 (currency)", "type": "text"}, {"name": "Payment 💸 (name)", "type": "name"}, {"name": "Payment 💸 (email)", "type": "email"}, {"name": "Payment 💸 (link)", "type": "url"}], "type": "collection", "label": "Fields"}, {"name": "fieldsById", "spec": [{"name": "question_RxO0ej", "type": "email", "label": "Your Email"}, {"name": "question_oEJyNx", "type": "text", "label": "Name"}, {"name": "question_G6MzEk", "type": "text", "label": "YouTube Video"}, {"name": "question_OaRzqg", "spec": {"type": "text"}, "type": "array", "label": "Language"}, {"name": "question_5bNN6M_price", "type": "number", "label": "Payment 💸 (price)"}, {"name": "question_5bNN6M_currency", "type": "text", "label": "Payment 💸 (currency)"}, {"name": "question_5bNN6M_name", "type": "name", "label": "Payment 💸 (name)"}, {"name": "question_5bNN6M_email", "type": "email", "label": "Payment 💸 (email)"}, {"name": "question_5bNN6M_link", "type": "url", "label": "Payment 💸 (link)"}], "type": "collection", "label": "Fields by ID"}]}}, {"id": 2, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"url": "https://api.elevenlabs.io/v1/dubbing", "serializeUrl": false, "method": "post", "headers": [{"name": "Content-Type", "value": "multipart/form-data"}, {"name": "xi-api-key", "value": "{insert API key}"}], "qs": [], "bodyType": "multipart_form_data", "parseResponse": false, "authUser": "", "authPass": "", "timeout": "", "shareCookies": false, "ca": "", "rejectUnauthorized": true, "followRedirect": true, "useQuerystring": false, "gzip": true, "useMtls": false, "formDataFields": [{"fieldType": "text", "key": "source_url", "value": "{{1.fields.`YouTube Video`}}"}, {"fieldType": "text", "key": "name", "value": "Translate101"}, {"fieldType": "text", "key": "target_lang", "value": "{{1.fields.Language[]}}"}, {"fieldType": "text", "key": "source_lang", "value": "en"}, {"fieldType": "text", "key": "num_speakers", "value": "1"}, {"fieldType": "text", "key": "watermark", "value": "true"}], "followAllRedirects": false}, "metadata": {"designer": {"x": 45, "y": 2}, "restore": {"expect": {"method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null, null]}, "qs": {"mode": "chose"}, "bodyType": {"label": "Multipart/form-data"}, "formDataFields": {"mode": "chose", "items": [{"fieldType": {"mode": "chose", "label": "Text"}}, {"fieldType": {"mode": "chose", "label": "Text"}}, {"fieldType": {"mode": "chose", "label": "Text"}}, {"fieldType": {"mode": "chose", "label": "Text"}}, {"fieldType": {"mode": "chose", "label": "Text"}}, {"fieldType": {"mode": "chose", "label": "Text"}}]}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "qs", "type": "array", "label": "Query String", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "formDataFields", "type": "array", "label": "Fields", "spec": [{"name": "fieldType", "label": "Field type", "type": "select", "required": true, "options": [{"value": "text", "label": "Text", "nested": [{"name": "key", "label": "Key", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"value": "file", "label": "File", "nested": [{"name": "key", "label": "Key", "type": "text", "required": true}, {"name": "data", "label": "Data", "type": "buffer", "semantic": "file:data"}, {"name": "fileName", "label": "File name", "help": "File name, including the suffix, e.g. invoice, xml", "type": "filename", "semantic": "file:name"}]}]}]}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 3, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{{2.data}}"}, "metadata": {"designer": {"x": 328, "y": 2}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}}, {"id": 4, "module": "util:FunctionSleep", "version": 1, "parameters": {}, "mapper": {"duration": "120"}, "metadata": {"designer": {"x": 602, "y": 2}, "restore": {}, "expect": [{"name": "duration", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delay", "required": true, "validate": {"max": 300, "min": 1}}]}}, {"id": 6, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"url": "https://api.elevenlabs.io/v1/dubbing/{{3.dubbing_id}}/audio/{{1.fields.Language[]}}", "serializeUrl": false, "method": "get", "headers": [{"name": "xi-api-key", "value": "{insert API key}"}], "qs": [], "bodyType": "", "parseResponse": false, "authUser": "", "authPass": "", "timeout": "", "shareCookies": false, "ca": "", "rejectUnauthorized": true, "followRedirect": true, "useQuerystring": false, "gzip": true, "useMtls": false, "followAllRedirects": false}, "metadata": {"designer": {"x": 864, "y": 1}, "restore": {"expect": {"method": {"mode": "chose", "label": "GET"}, "headers": {"mode": "chose", "items": [null]}, "qs": {"mode": "chose"}, "bodyType": {"label": "Empty"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "qs", "type": "array", "label": "Query String", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 7, "module": "google-drive:uploadAFile", "version": 4, "parameters": {"__IMTCONN__": 2483316}, "mapper": {"data": "{{6.data}}", "select": "value", "convert": false, "filename": "{{1.fields.Name}}({{1.fields.Language[]}}).mp4", "folderId": "/1nzRJyRbkCbW6nZnTGnHECNKbF7uSG-AC/1SJoFShsMsbDEeT3qnnhb47mKQLCuXfcJ/14_EI0mTd0yRni5FOloyc1NfsGxNetm3T/18nzGfa4PDfleVpQfVyd38hdlHoXj8A2_", "destination": "drive"}, "metadata": {"designer": {"x": 1134, "y": 2}, "restore": {"expect": {"select": {"label": "Select from the list"}, "folderId": {"mode": "chose", "path": ["2. Content Ideas ", "1. <PERSON><PERSON><PERSON>", "36 YouTube Translator ", "Videos for sharing "]}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "select", "type": "select", "label": "Enter a Folder ID", "required": true, "validate": {"enum": ["map", "value"]}}, {"name": "title", "type": "text", "label": "New File Name"}, {"name": "filename", "type": "text", "label": "File Name", "required": true}, {"name": "data", "type": "buffer", "label": "Data", "required": true}, {"name": "convert", "type": "boolean", "label": "Convert a File", "required": true}, {"name": "destination", "type": "select", "label": "New Drive Location", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "New Folder Location", "required": true}]}}, {"id": 8, "module": "google-email:ActionCreateDraft", "version": 2, "parameters": {"account": 2483316}, "mapper": {"cc": [], "to": ["{{1.fields.`Your Email`}}"], "bcc": [], "html": "<div>\n<h1>🎉 <strong>It's Here! Your Translation is Ready!</strong> 🚀</h1>\n<p><strong>Hey there!</strong></p>\n<p>The <strong>magic is done</strong>, and your YouTube translation is ready to rock and roll! 🌟</p>\n<p>Click the link below to view it live in action and make your day awesome! ✨</p>\n<div><a href=\"https://drive.google.com/file/d/{{7.id}}/view?usp=sharing\"> 👉 <strong>View Your Translation</strong> 👈 </a></div>\n<p>If you have any questions or just want to share how awesome it looks, feel free to reach out. 😊</p>\n<p>Best regards,<br /> <strong>Cal</strong></p>\n</div>", "folder": "[Gmail]/Drafts", "subject": "{{1.fieldsById.question_oEJyNx}}'s Translation 🌍", "attachments": []}, "metadata": {"designer": {"x": 1434, "y": 2}, "restore": {"expect": {"cc": {"mode": "chose"}, "to": {"mode": "chose", "items": [null]}, "bcc": {"mode": "chose"}, "folder": {"mode": "chose", "path": ["Drafts"]}, "attachments": {"mode": "chose"}}, "parameters": {"account": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (<EMAIL>)"}}}, "parameters": [{"name": "account", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"name": "folder", "type": "folder", "label": "Folder", "required": true}, {"name": "to", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "To"}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "html", "type": "text", "label": "Content"}, {"name": "attachments", "spec": [{"name": "fileName", "type": "filename", "label": "File name", "required": true, "semantic": "file:name"}, {"name": "data", "type": "buffer", "label": "Data", "required": true, "semantic": "file:data"}], "type": "array", "label": "Attachments"}, {"name": "cc", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "Copy recipient"}, {"name": "bcc", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "Blind copy recipient"}]}}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us1.make.com"}}