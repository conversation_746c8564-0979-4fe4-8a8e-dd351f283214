{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "AWS Textract", "type": "n8n-nodes-base.awsTextract", "position": [650, 300], "parameters": {}, "credentials": {"aws": {"id": "12", "name": "AWS account"}}, "typeVersion": 1}, {"name": "AWS S3", "type": "n8n-nodes-base.awsS3", "position": [450, 300], "parameters": {"fileKey": "Rechnung.jpg", "bucketName": "textract-demodata"}, "credentials": {"aws": {"id": "12", "name": "AWS account"}}, "typeVersion": 1}], "connections": {"AWS S3": {"main": [[{"node": "AWS Textract", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "AWS S3", "type": "main", "index": 0}]]}}}