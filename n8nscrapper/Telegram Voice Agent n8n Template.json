{"name": "Basic Agent Prebuild - With Telegram", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.output }}", "options": {"systemMessage": "You are a helpful assistant. Always be concise."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [220, 0], "id": "2e90bd26-0cb0-4a2b-8150-e1f44ca1da8f", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [20, 300], "id": "f71fb633-3405-4b8c-8932-b93<PERSON><PERSON>fe11", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "v1aNtjBTbfVqtezA", "name": "OpenRouter account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [220, 320], "id": "9ded5c35-0a84-487a-95c7-a1a5b0c61c5e", "name": "Simple Memory"}, {"parameters": {"base": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Base', ``, 'string') }}", "mode": "id"}, "table": {"__rl": true, "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Table', ``, 'string') }}", "mode": "id"}, "id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Record_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [540, 360], "id": "4103619a-ea19-4f41-8426-cbfe85e2d194", "name": "Read Contacts", "credentials": {"airtableTokenApi": {"id": "D53dUMGWmMNsvlaf", "name": "Generic Base"}}}, {"parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [700, 320], "id": "b0934608-ccfd-482f-8c34-61df36e6f3c6", "name": "Send Email", "webhookId": "edf32003-4875-4d6f-a6a1-98c954da419d", "credentials": {"gmailOAuth2": {"id": "dZqxzU8pDMnYVBfx", "name": "<EMAIL> Gmail"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [840, 280], "id": "2aebf732-f46b-4d5b-834b-6a2bccfb6fed", "name": "Add Calendar Event", "credentials": {"googleCalendarOAuth2Api": {"id": "JB1h4pGxD05iQpBP", "name": "Google Calendar account"}}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1240, -20], "id": "29a57dd0-9184-49f4-a072-60f507bbb78a", "name": "<PERSON>eg<PERSON>", "webhookId": "cd78bbfc-f005-43a8-b6ac-0ef55e626a2f", "credentials": {"telegramApi": {"id": "qAo3e0ODQn6Qw06V", "name": "Matts N8N Test <PERSON>"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1c3ff13d-bf05-407b-9dbc-bbadf949d941", "leftValue": "={{ $json.isVoice }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Is Voice"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "3036daea-5c1d-434f-a45f-778e418504f7", "leftValue": "={{ $json.isVoice }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Is Not Voice"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-800, -20], "id": "fe6dabd2-8a99-462a-9534-40471d9e5b58", "name": "Switch"}, {"parameters": {"resource": "file", "fileId": "={{ $('Telegram Trigger').item.json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-560, -300], "id": "960abfa2-9440-4af4-969c-3ab8ac8ee6d9", "name": "Telegram", "webhookId": "fe409271-5ec5-4918-8788-31a372ad0261", "credentials": {"telegramApi": {"id": "qAo3e0ODQn6Qw06V", "name": "Matts N8N Test <PERSON>"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-300, -300], "id": "12089b12-927a-4891-a170-0d0e1a515e09", "name": "OpenAI", "credentials": {"openAiApi": {"id": "smb36YjJ8Bu6Al0K", "name": "Test OpenAI Account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "9ed9ddff-6196-42e4-9102-324604bc39cb", "name": "isVoice", "value": "={{ !!$json.message.voice }}", "type": "boolean"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1020, -20], "id": "cb8d8d31-25bc-4132-b23a-022be95e5945", "name": "Is Voice?"}, {"parameters": {"assignments": {"assignments": [{"id": "58a2e201-ea7e-458c-bf9d-dc80b191936c", "name": "output", "value": "={{ $json.text }}{{ $('Telegram Trigger').item.json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-40, -20], "id": "48fc26c0-2cc7-4c31-8a7b-f0a205918d05", "name": "Agent Input"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Is Voice?').item.json.isVoice }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "a92170e0-a269-4749-9328-7760a8c968ba"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e9ed5396-82f1-4092-a846-8c8476c2aa7a", "leftValue": "={{ $('Is Voice?').item.json.isVoice }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [680, 0], "id": "038e0202-68bf-43cc-89f9-0fec3122db75", "name": "Switch1"}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $('AI Agent').item.json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1400, 20], "id": "c8397d67-ae31-4c0e-bc40-d2226f72799f", "name": "Telegram1", "webhookId": "0047b4fc-5dcb-4031-885e-104a86077795", "credentials": {"telegramApi": {"id": "qAo3e0ODQn6Qw06V", "name": "Matts N8N Test <PERSON>"}}}, {"parameters": {"resource": "audio", "input": "={{ $('AI Agent').item.json.output }}", "options": {"response_format": "opus"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [980, -260], "id": "8e69d0a6-1e14-4273-8d87-c383f42e1538", "name": "OpenAI1", "credentials": {"openAiApi": {"id": "smb36YjJ8Bu6Al0K", "name": "Test OpenAI Account"}}}, {"parameters": {"operation": "sendAudio", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "binaryData": true, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1400, -260], "id": "9c3dad2f-0486-4da1-9809-08bac2958a34", "name": "Telegram2", "webhookId": "10f3cc6a-5d09-4939-9268-635cb19535c6", "credentials": {"telegramApi": {"id": "qAo3e0ODQn6Qw06V", "name": "Matts N8N Test <PERSON>"}}}, {"parameters": {"cityName": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('City', ``, 'string') }}"}, "type": "n8n-nodes-base.openWeatherMapTool", "typeVersion": 1, "position": [380, 360], "id": "d276bae4-628a-4bfa-8a6e-9d9afc7a8ecc", "name": "OpenWeatherMap", "credentials": {"openWeatherMapApi": {"id": "IqBVKfQ90bPzsy4y", "name": "OpenWeatherMap account"}}}], "pinData": {}, "connections": {"OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Read Contacts": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Send Email": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Add Calendar Event": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Is Voice?", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Telegram", "type": "main", "index": 0}], [{"node": "Agent Input", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Agent Input", "type": "main", "index": 0}]]}, "Is Voice?": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Agent Input": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}], [{"node": "Telegram1", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "Telegram2", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "49534ca7-1a59-4c9d-9ca7-d073114a28e1", "meta": {"templateCredsSetupCompleted": true, "instanceId": "45b0405b1f07eaf385b90bc15a028abff5aeb76441e1bf2e441a789f87e150ed"}, "id": "iwheZ4tj8kMQcvBh", "tags": []}