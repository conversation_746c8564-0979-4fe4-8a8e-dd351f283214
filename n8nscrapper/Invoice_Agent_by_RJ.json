{"name": "Invoice Agent", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-280, -40], "id": "c413a1ee-e027-4b24-a9ba-b4004659c01c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"documentId": {"__rl": true, "value": "1d7CWcM_Ge6s2iaoGXOsxykLQFsDa_iyEFyP0qxJ9Qbs", "mode": "list", "cachedResultName": "Client Invoices", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d7CWcM_Ge6s2iaoGXOsxykLQFsDa_iyEFyP0qxJ9Qbs/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d7CWcM_Ge6s2iaoGXOsxykLQFsDa_iyEFyP0qxJ9Qbs/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-80, -40], "id": "b7c153b8-d2b5-4e50-a947-383bd197e7dd", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "tw8iJbHLeqfMESfX", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "afe0ae0a-9f88-4605-b064-223db0c34522", "leftValue": "={{ $json.Status }}", "rightValue": "Pending", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [140, -40], "id": "b989b68b-7ef1-4aea-8293-57e27bf2e7ed", "name": "Filter"}, {"parameters": {"assignments": {"assignments": [{"id": "eae2796d-a5bb-4bc1-a4f6-26abc64e2185", "name": "\tInvoice ID", "value": "={{ $json['\tInvoice ID'] }}", "type": "string"}, {"id": "72f18799-575f-4c4c-9f67-02a9dc9a45ab", "name": "Client Name", "value": "={{ $json['Client Name'] }}", "type": "string"}, {"id": "66338ec2-a287-4319-b0a2-89c6e2915d07", "name": "Client Address", "value": "={{ $json['Client Address'] }}", "type": "string"}, {"id": "9e9dceea-5fe0-4be8-8ddb-826b9e569dea", "name": "Project Name\t", "value": "={{ $json['Project Name\t'] }}", "type": "string"}, {"id": "82d90cd2-1075-41d0-a251-242498884069", "name": "Amount (USD)", "value": "={{ $json['Amount (USD)'] }}", "type": "number"}, {"id": "b84b6393-bfc2-494a-b8e1-3f918734088f", "name": "Invoice Date", "value": "={{ $now.format('yyyy-MM-dd') }}", "type": "string"}, {"id": "889bc329-2802-4670-8d0e-88e97f25be65", "name": "Due Date", "value": "={{ $now.plus({day: 7}).format('yyyy-MM-dd') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [360, -40], "id": "e937483d-feb4-4a83-bdfd-0dcd6a3b6eb6", "name": "<PERSON>"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [580, -40], "id": "652f24ab-0c9a-48c4-8110-242666894643", "name": "Loop Over Items"}, {"parameters": {"promptType": "define", "text": "=You are a professional invoicing assistant for a creative agency called \"Upward Engine\". Generate a polite, professional email to a client who has received an invoice as a PDF attachment. Include the client name, project name, invoice amount, invoice ID, invoice date, and due date.\n\nThe tone should be friendly but business-oriented, and the purpose is to inform the client that the invoice is attached, and to kindly request timely payment. End with a thank-you message and sign off as \"Upward Engine Team\".\n\nHere is the invoice data:\n- Client name: {{ $json['Client Name'] }}\n- Project Name: {{ $json['Project Name\t'] }}\n- Invoice Amount: {{ $json['Amount (USD)'] }}\n- Invoice ID: {{ $json['\tInvoice ID'] }}\n- Invoice Date: {{ $json['Invoice Date'] }}\n- Due Date: {{ $json['Due Date'] }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [880, -140], "id": "d2443d6f-6b22-4463-b6b6-a8f17c8aff4f", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [900, -140], "id": "bdf4dc12-aa63-41c1-afa4-874c18342ceb", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "2Pt837d3ZMKdLsqW", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"text": "={{ $json.output }}", "attributes": {"attributes": [{"name": "email subject", "description": "subject of the mail"}, {"name": "email body", "description": "email body without the subject"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [1240, -140], "id": "04bdc8be-9de7-4dc2-bc15-58f3e8c540d1", "name": "Information Extractor"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1260, -140], "id": "ae1bed8d-425d-4310-a74f-c989e1be93cd", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "2Pt837d3ZMKdLsqW", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"resource": "pdf", "templateId": "YOUR_TEMPLATE_ID", "data": "={\n\"company_name\": \"Upward Engine\",\n    \"company_address\": \"Silicon Valey, USA\",\n    \"company_email\": \"<EMAIL>\",\n    \"bill_to\": \"{{ $('Edit Fields').item.json['Client Name'] }}\",\n    \"bill_to_address\": \"{{ $('Edit Fields').item.json['Client Address'] }}\",\n    \"invoice_no\": \"{{ $('Edit Fields').item.json['\tInvoice ID'] }}\",\n    \"invoice_date\": \"{{ $('Edit Fields').item.json['Invoice Date'] }}\",\n    \"invoice_due_date\": \"{{ $('Edit Fields').item.json['Due Date'] }}\",\n    \"footer\": \"Thank you for working with Upward Engine\",\n    \"balance\": \"{{ $('Edit Fields').item.json['Amount (USD)'] }}\",\n    \"project_name\": \"{{ $('Edit Fields').item.json['Project Name\t'] }}\",\n    \"currency\": \"$\"\n\n}", "export_type": "file"}, "type": "n8n-nodes-craftmypdf.craftMyPdf", "typeVersion": 1, "position": [1600, -140], "id": "64d49d91-558b-4c36-a0f6-39d8aca3ebd8", "name": "CraftMyPDF", "credentials": {"craftMyPdfApi": {"id": "mbOsEBF7TfyaBFJX", "name": "CraftMyPDF account"}}}, {"parameters": {"sendTo": "={{ $('Google Sheets').item.json['Client Email'] }}", "subject": "={{ $('Information Extractor').item.json.output['email subject'] }}", "emailType": "text", "message": "={{ $('Information Extractor').item.json.output['email body'] }}", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{"property": "output.pdf"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1820, -140], "id": "ae1157a2-ff62-42d4-b277-b99e5593d9b1", "name": "Gmail", "webhookId": "85f9cc55-4019-4f9f-a7b8-852ba7f38d1b", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1d7CWcM_Ge6s2iaoGXOsxykLQFsDa_iyEFyP0qxJ9Qbs", "mode": "list", "cachedResultName": "Client Invoices", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d7CWcM_Ge6s2iaoGXOsxykLQFsDa_iyEFyP0qxJ9Qbs/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1d7CWcM_Ge6s2iaoGXOsxykLQFsDa_iyEFyP0qxJ9Qbs/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"\tInvoice ID": "={{ $('Edit Fields').item.json['\tInvoice ID'] }}", "Status": "Completed", "Invoice Date": "={{ $('Edit Fields').item.json['Invoice Date'] }}", "Due Date": "={{ $('Edit Fields').item.json['Due Date'] }}"}, "matchingColumns": ["\tInvoice ID"], "schema": [{"id": "\tInvoice ID", "displayName": "\tInvoice ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Client Name", "displayName": "Client Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Client Email", "displayName": "Client Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Client Address", "displayName": "Client Address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Project Name\t", "displayName": "Project Name\t", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Amount (USD)", "displayName": "Amount (USD)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Invoice Date", "displayName": "Invoice Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Due Date", "displayName": "Due Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [2060, -40], "id": "36370bdf-9b88-4519-9cdd-b0a7a1488f26", "name": "Google Sheets1", "credentials": {"googleSheetsOAuth2Api": {"id": "tw8iJbHLeqfMESfX", "name": "Google Sheets account"}}}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "CraftMyPDF", "type": "main", "index": 0}]]}, "CraftMyPDF": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Gmail": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "04246f43-f866-4844-ab5c-f33cb373c00f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5c2f5b5b4cf20114a6c53aaed9430bfdabad5c4604b5a73e1363b96c75e842ec"}, "id": "reQn6NEXvZ9iellF", "tags": []}