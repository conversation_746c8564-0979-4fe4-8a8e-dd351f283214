{"name": "WhatsApp Agent Pro", "nodes": [{"parameters": {"device": "[DEVICE_ID_HIDDEN]", "events": ["message:in:new"]}, "type": "n8n-nodes-wassenger.wassengerTrigger", "typeVersion": 1, "position": [380, 240], "id": "3e55c9a4-ca16-4fb5-8d6a-220b8ad01c9d", "name": "<PERSON><PERSON><PERSON>", "webhookId": "[WEBHOOK_ID_HIDDEN]", "credentials": {"wassengerApiKey": {"id": "[CREDENTIAL_ID_HIDDEN]", "name": "WhatsApp API key"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.data.body }}", "options": {"systemMessage": "=You are a helpful assistant called <PERSON> with a frendly and kinkd tone. \n\nYou are currently talking to {{ $('Switch').item.json.data.chat.name }}\n\nThe current time is {{ $now.toISO() }} "}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [2700, 260], "id": "41d0158b-ef6b-43ab-9c46-bad27fcac028", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2640, 460], "id": "87e73037-c99f-42a6-86eb-e49015c0ad8c", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "[OPENAI_CREDENTIAL_ID_HIDDEN]", "name": "OpenAi account"}}}, {"parameters": {"device": "[DEVICE_ID_HIDDEN]", "phone": "={{ $('Wassenger Trigger').item.json.data.fromNumber }}", "message": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-wassenger.wassenger", "typeVersion": 1, "position": [3540, 420], "id": "f9b6dbd2-0494-441d-9993-2180259473c5", "name": "Wassenger", "credentials": {"wassengerApiKey": {"id": "[CREDENTIAL_ID_HIDDEN]", "name": "WhatsApp API key"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.data.media.type }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals"}, "id": "94a56d29-cb88-4eb0-8d6b-07b1b91bfc3f"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice message"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e14c0da3-f892-46c2-af90-c81c963a0df5", "leftValue": "={{ $json.data.media.type }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image message"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6ce0d87f-bdfc-4c65-a3fb-8d86bb682c4e", "leftValue": "={{ $json.data.type }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text message"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [1120, 240], "id": "3961ea74-1586-444b-9796-44f911fc8790", "name": "Switch"}, {"parameters": {"resource": "chat-files", "operation": "getDeviceFileDetails", "device": "={{ $json.device.id }}", "fileId": "={{ $json.data.media.id }}"}, "type": "n8n-nodes-wassenger.wassenger", "typeVersion": 1, "position": [1660, -160], "id": "8e5f176a-62fd-4067-b7be-fd4e611d8e41", "name": "Download Audio", "credentials": {"wassengerApiKey": {"id": "[CREDENTIAL_ID_HIDDEN]", "name": "WhatsApp API key"}}}, {"parameters": {"url": "=https://api.wassenger.com{{ $('Wassenger Trigger').item.json.data.media.links.download }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1880, -160], "id": "5f99188d-8cf2-46d4-a756-790e7f34c74e", "name": "Download Audio1", "credentials": {"httpHeaderAuth": {"id": "[HTTP_AUTH_CREDENTIAL_ID_HIDDEN]", "name": "Header Auth account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "binaryPropertyName": "=data", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2100, -160], "id": "3c455966-a4b7-4108-84e0-84e416ca6bdf", "name": "Audio transcription", "credentials": {"openAiApi": {"id": "[OPENAI_CREDENTIAL_ID_HIDDEN]", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "4aeaa661-b3e8-4587-851e-dac366a37d0c", "name": "data.body", "value": "={{ $json.text }}", "type": "string"}]}, "includeOtherFields": "=", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2320, -160], "id": "8b274388-06dc-4edf-94eb-084f3abf2851", "name": "<PERSON>"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "13087e41-3b49-4b44-96ed-5ff74651c371", "leftValue": "={{ $('Switch').item.json.data.media.type }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3200, 260], "id": "1c8d7147-9e91-4e76-b96d-9d88b5220aae", "name": "If"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Switch') }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [2820, 460], "id": "ce935651-35bc-4caf-bdd2-a84ad509d3fc", "name": "Simple Memory"}, {"parameters": {"resource": "audio", "input": "={{ $('AI Agent').item.json.output }}", "voice": "nova", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [3540, 160], "id": "2888d451-832d-44f6-93ca-b3a9342cb9ad", "name": "OpenAI", "credentials": {"openAiApi": {"id": "[OPENAI_CREDENTIAL_ID_HIDDEN]", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// Loop over input items and change the MIME type of binary data\nfor (const item of $input.all()) {\n  // Check if the item has binary data\n  if (item.binary) {\n    // Find the binary property name (assuming there's at least one)\n    const binaryPropertyNames = Object.keys(item.binary);\n\n    for (const propName of binaryPropertyNames) {\n      // If the MIME type is 'audio/mp3', change it to 'audio/mpeg'\n      if (item.binary[propName].mimeType === \"audio/mp3\") {\n        item.binary[propName].mimeType = \"audio/mpeg\";\n      }\n    }\n  }\n}\n\nreturn $input.all();"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3760, 160], "id": "f2a0b34a-ec2e-4984-9c5c-8bc3ed823d95", "name": "Code"}, {"parameters": {"name": "={{ $json.fileName }}", "driveId": {"__rl": true, "value": "My Drive", "mode": "list", "cachedResultName": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive"}, "folderId": {"__rl": true, "value": "[GOOGLE_DRIVE_FOLDER_ID_HIDDEN]", "mode": "list", "cachedResultName": "n8n whatsapp audios", "cachedResultUrl": "[GOOGLE_DRIVE_FOLDER_URL_HIDDEN]"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [3980, 160], "id": "bfff5e16-8ebd-40bd-91f9-b9ab8b572e54", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "[GOOGLE_DRIVE_CREDENTIAL_ID_HIDDEN]", "name": "Google Drive account"}}}, {"parameters": {"operation": "sendMedia", "device": "[DEVICE_ID_HIDDEN]", "phone": "={{ $('Switch').item.json.data.fromNumber }}", "mediaUrl": "={{ $json.webContentLink }}", "options": {}}, "type": "n8n-nodes-wassenger.wassenger", "typeVersion": 1, "position": [4200, 160], "id": "02d42a3e-c444-409f-a20b-2ad4c48b723e", "name": "Wassenger1", "credentials": {"wassengerApiKey": {"id": "[CREDENTIAL_ID_HIDDEN]", "name": "WhatsApp API key"}}}, {"parameters": {"operation": "deleteFile", "fileId": {"__rl": true, "value": "={{ $('Google Drive').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [4420, 160], "id": "dcd55245-6092-4599-bea9-03e1faf06c6a", "name": "Google Drive1", "credentials": {"googleDriveOAuth2Api": {"id": "[GOOGLE_DRIVE_CREDENTIAL_ID_HIDDEN]", "name": "Google Drive account"}}}, {"parameters": {"resource": "chat-files", "operation": "downloadDeviceFileDetails", "device": "={{ $json.device.id }}", "fileId": "={{ $json.data.media.id }}"}, "type": "n8n-nodes-wassenger.wassenger", "typeVersion": 1, "position": [1660, 100], "id": "e86972a8-a14e-4718-a63d-ef3064888f10", "name": "Download image", "credentials": {"wassengerApiKey": {"id": "[CREDENTIAL_ID_HIDDEN]", "name": "WhatsApp API key"}}}, {"parameters": {"url": "=https://api.wassenger.com{{ $('Wassenger Trigger').item.json.data.media.links.download }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1880, 100], "id": "58f5b3c9-b599-4d05-98df-7fafbea560d8", "name": "Download API audio", "credentials": {"httpHeaderAuth": {"id": "[HTTP_AUTH_CREDENTIAL_ID_HIDDEN]", "name": "Header Auth account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "dcf61ad5-2b19-411d-8e84-b9a4cd9bc74c", "name": "data.body", "value": "=# The user provided the following image and text.\n\n## Image Description:\n{{ $json.content }}\n\n## User Message:\n{{ $('Wassenger Trigger').item.json.data.media.caption }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2320, 100], "id": "a2922296-70bd-4998-9da3-0d35f12ffd47", "name": "Image + Text Prompt"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "text": "Analyze the image", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2100, 100], "id": "1a9206d8-960d-45e7-8144-c1b8b88fb762", "name": "Analyze Image", "credentials": {"openAiApi": {"id": "[OPENAI_CREDENTIAL_ID_HIDDEN]", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"Wassenger Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Download Audio", "type": "main", "index": 0}], [{"node": "Download image", "type": "main", "index": 0}], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "Download Audio": {"main": [[{"node": "Download Audio1", "type": "main", "index": 0}]]}, "Download Audio1": {"main": [[{"node": "Audio transcription", "type": "main", "index": 0}]]}, "Audio transcription": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}], [{"node": "Wassenger", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Wassenger1", "type": "main", "index": 0}]]}, "Wassenger1": {"main": [[{"node": "Google Drive1", "type": "main", "index": 0}]]}, "Download image": {"main": [[{"node": "Download API audio", "type": "main", "index": 0}]]}, "Download API audio": {"main": [[{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Image + Text Prompt": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Image + Text Prompt", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "[VERSION_ID_HIDDEN]", "meta": {"templateId": "9000001", "templateCredsSetupCompleted": true, "instanceId": "[INSTANCE_ID_HIDDEN]"}, "id": "[WORKFLOW_ID_HIDDEN]", "tags": []}