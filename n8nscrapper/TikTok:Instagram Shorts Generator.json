{"name": "AI Facebook Ad Spy Tool", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1300, -140], "id": "18536cf3-1626-44ee-95d5-6eb28baa712b", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.snapshot.videos[0].video_sd_url }}", "rightValue": "=https://video-", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "id": "9dbbe6e7-fd80-44ab-88f0-ab438cfd997e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Video"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0b7cee75-ca57-4946-971e-1f526b49324f", "leftValue": "={{ $json.snapshot.images[0].original_image_url }}", "rightValue": "https://scontent-ho", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image"}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "Text"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-640, -140], "id": "e201ba62-cc45-45e6-b50c-08f913a27c87", "name": "Switch"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c1d74591-13e8-4c79-9a34-117cc6816dfb", "leftValue": "={{ $json.advertiser.ad_library_page_info.page_info.likes }}", "rightValue": 1000, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-860, -140], "id": "ca9ee8b5-c562-4339-b6ad-6c7e8d31e4ef", "name": "Filter For Likes"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [380, 160], "id": "abdf6521-2599-4741-9dab-f6ce60775ec7", "name": "Wait", "webhookId": "1cda7407-6321-4d8d-b532-19f42514eb63"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [640, -140], "id": "56c2f9ea-5427-4cd1-a7ce-0f3b918960d2", "name": "Wait1", "webhookId": "1cda7407-6321-4d8d-b532-19f42514eb63"}, {"parameters": {"url": "={{ $json.snapshot.videos[0].video_sd_url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-120, -520], "id": "1b41ce15-b4b1-4189-940e-f4d38062619e", "name": "Download Video"}, {"parameters": {"name": "Example File", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [100, -520], "id": "9a1730f8-c73c-4771-a718-a6729f68374c", "name": "Upload Video to Drive", "credentials": {"googleDriveOAuth2Api": {"id": "8R2NiLt8owCA0E2O", "name": "Google Drive account"}}}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2100, -440], "id": "decfca56-689f-4332-9813-00edf6ec00b5", "name": "Wait2", "webhookId": "1cda7407-6321-4d8d-b532-19f42514eb63"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/XtaWFhbtfxyzqrFmd/run-sync-get-dataset-items", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <your<PERSON><PERSON><PERSON><PERSON>>"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"count\": 200,\n    \"period\": \"last7d\",\n    \"scrapeAdDetails\": true,\n    \"scrapePageAds.activeStatus\": \"active\",\n    \"urls\": [\n        {\n            \"url\": \"https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=US&is_targeted_country=false&media_type=all&q=%22ai%20automation%22&search_type=keyword_exact_phrase&start_date[min]=2025-06-12&start_date[max]\",\n            \"method\": \"GET\"\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1080, -140], "id": "6adbf90c-a132-4ef1-88f7-9ab798c955a3", "name": "Run Ad Library Scraper"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-420, -140], "id": "1380cc04-7732-4070-8306-7c986c513ce7", "name": "Loop Over Image Ads"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-420, 160], "id": "dafb1f75-ba45-4c08-8581-5e6554de9089", "name": "Loop Over Text Ads"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-420, -445], "id": "66fc9090-11f2-4da1-bdec-9a24be1f5669", "name": "Loop Over Video Ads"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/upload/v1beta/files", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "<yourApi<PERSON>ey>"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Goog-Upload-Protocol", "value": "resumable"}, {"name": "X-Goog-Upload-Command", "value": "start"}, {"name": "X-Goog-Upload-Header-Content-Length", "value": "={{ $json.size }}"}, {"name": "Content-Type", "value": "application/json"}]}, "options": {"response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [300, -520], "id": "2a753af7-acfd-4813-9fc7-1feeae3e38ec", "name": "Begin Gemini Upload Session"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Upload Video to Drive').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [520, -520], "id": "fed903ba-79f3-466f-b1e3-6cc72f05f341", "name": "Redownload Video", "credentials": {"googleDriveOAuth2Api": {"id": "8R2NiLt8owCA0E2O", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "={{ $json.headers['x-goog-upload-url'] }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "<yourApi<PERSON>ey>"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Length", "value": "={{ $('Upload Video to Drive').item.json.size }}"}, {"name": "X-Goog-Upload-Offset", "value": "0"}, {"name": "X-Goog-Upload-Command", "value": "upload, finalize"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "=data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [740, -520], "id": "bf9a84ef-a025-4d08-bc70-0627c1e5f553", "name": "Upload Video to Gemini"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "<yourApi<PERSON>ey>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"Describe this video in excruciating detail. Do not output anything but the description of the video.\"\n        },\n        {\n          \"file_data\": {\n            \"mime_type\": \"{{ $json.file.mimeType }}\",\n            \"file_uri\": \"{{ $json.file.uri }}\"\n          }\n        }\n      ]\n    }\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1220, -520], "id": "9f389a74-0097-4e4f-847f-c80494d9b8a8", "name": "Analyze Video with Gemini", "retryOnFail": true, "waitBetweenTries": 15}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent advertisement analysis bot. You analyze advertisements.", "role": "system"}, {"content": "Your task is to take as input a scraped javascript object from an advertisement in the FB ad library, and then summarize it + spin (repurpose, rewrite) the ad copy. \n\nYou're doing this for strategic intelligence. We run an advertising agency and we're always looking at what our competitor advertisers are doing.\n\nOutput your results in this JSON format:\n\n{\"summary\":\"\",\"rewrittenAdCopy\":\"\"}\n\nRules:\n- The intended style and attitude is generally analytical, inquisitive, and precise, despite exploring complex topics, in the “classic style” of Western writing.\nthe level of formality should be inverse to the topic’s novelty: the weirder something is, the more formal. For ‘safer’ topics, one should cut loose with the humor, epigraphs, typographical stunts and experiments, etc.\n- Avoid hedging and qualifying, even at the risk of making overly-strong claims. It is a slippery slope.\n- Use casual abbreviations (like San Francisco -> SF, thanks -> thx, question -> q ), casual contractions (like \"I've\"), shortened forms of common nouns (like \"info\", \"vid\", \"app\") as this signals more human written speech. Do not use em dashes (eliminate — from your vocabulary completely!\n- No rhetorical questions.\n- Make your summary extremely comprehensive and analytical."}, {"content": "=JSON scrape:\n\n{{ $('Loop Over Video Ads').item.json.toJsonString() }}\n\nVideo description:\n\n{{ $json.candidates[0].content.parts[0].text }}"}]}, "jsonOutput": true, "options": {"temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1500, -520], "id": "1528dc3d-fff4-452c-ad42-2d2e9bdf1f4e", "name": "Output Video Summary", "credentials": {"openAiApi": {"id": "oLLACDYfGm4C4ouV", "name": "YouTube "}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo", "mode": "list", "cachedResultName": "Facebook Ad Library Analyzer DB", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Ads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"ad_archive_id": "={{ $('Loop Over Video Ads').item.json.ad_archive_id }}", "page_id": "={{ $('Loop Over Video Ads').item.json.page_id }}", "date_added": "={{ $now }}", "page_name": "={{ $('Loop Over Video Ads').item.json.snapshot.page_name }}", "summary": "={{ $json.message.content.summary }}", "rewritten_ad_copy": "={{ $json.message.content.rewrittenAdCopy }}", "page_url": "={{ $('Loop Over Video Ads').item.json.snapshot.page_profile_uri }}", "image_prompt": "=", "type": "video", "video_prompt": "={{ $('Analyze Video with Gemini').item.json.candidates[0].content.parts[0].text }}"}, "matchingColumns": [], "schema": [{"id": "ad_archive_id", "displayName": "ad_archive_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_id", "displayName": "page_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "type", "displayName": "type", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "date_added", "displayName": "date_added", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_name", "displayName": "page_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_url", "displayName": "page_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "summary", "displayName": "summary", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "rewritten_ad_copy", "displayName": "rewritten_ad_copy", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "image_prompt", "displayName": "image_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "video_prompt", "displayName": "video_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1860, -520], "id": "e3fc12fa-d845-435c-95e7-1fc121f0bb48", "name": "Add as Type = Video", "credentials": {"googleSheetsOAuth2Api": {"id": "EOibXIc4U8wcXyRR", "name": "YouTube"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "text": "What's in this image? Be extremely comprehensive.", "imageUrls": "={{ $json.snapshot.images[0].original_image_url }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-120, -220], "id": "47291e4b-ae46-4bff-8134-8cf330cfb7aa", "name": "Analyze Image", "credentials": {"openAiApi": {"id": "oLLACDYfGm4C4ouV", "name": "YouTube "}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent advertisement analysis bot. You analyze advertisements.", "role": "system"}, {"content": "Your task is to take as input a scraped javascript object from an advertisement in the FB ad library, and then summarize it + spin (repurpose, rewrite) the ad copy. \n\nYou're doing this for strategic intelligence. We run an advertising agency and we're always looking at what our competitor advertisers are doing.\n\nOutput your results in this JSON format:\n\n{\"summary\":\"\",\"rewrittenAdCopy\":\"\"}\n\nRules:\n- The intended style and attitude is generally analytical, inquisitive, and precise, despite exploring complex topics, in the “classic style” of Western writing.\nthe level of formality should be inverse to the topic’s novelty: the weirder something is, the more formal. For ‘safer’ topics, one should cut loose with the humor, epigraphs, typographical stunts and experiments, etc.\n- Avoid hedging and qualifying, even at the risk of making overly-strong claims. It is a slippery slope.\n- Use casual abbreviations (like San Francisco -> SF, thanks -> thx, question -> q ), casual contractions (like \"I've\"), shortened forms of common nouns (like \"info\", \"vid\", \"app\") as this signals more human written speech. Do not use em dashes (eliminate — from your vocabulary completely!\n- No rhetorical questions.\n- Make your summary extremely comprehensive and analytical."}, {"content": "=JSON scrape:\n\n{{ $('Loop Over Image Ads').item.json.toJsonString() }}\n\nImage description:\n\n{{ $json.content }}"}]}, "jsonOutput": true, "options": {"temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [80, -220], "id": "34ee9e0e-4be8-41f2-b3fc-1868ec912fb8", "name": "Output Image Summary", "credentials": {"openAiApi": {"id": "oLLACDYfGm4C4ouV", "name": "YouTube "}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo", "mode": "list", "cachedResultName": "Facebook Ad Library Analyzer DB", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Ads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"ad_archive_id": "={{ $('Loop Over Image Ads').item.json.ad_archive_id }}", "page_id": "={{ $('Loop Over Image Ads').item.json.page_id }}", "date_added": "={{ $now }}", "page_name": "={{ $('Loop Over Image Ads').item.json.snapshot.page_name }}", "summary": "={{ $json.message.content.summary }}", "rewritten_ad_copy": "={{ $json.message.content.rewrittenAdCopy }}", "page_url": "={{ $('Loop Over Image Ads').item.json.snapshot.page_profile_uri }}", "image_prompt": "={{ $('Analyze Image').item.json.content }}", "type": "image"}, "matchingColumns": [], "schema": [{"id": "ad_archive_id", "displayName": "ad_archive_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_id", "displayName": "page_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "type", "displayName": "type", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "date_added", "displayName": "date_added", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_name", "displayName": "page_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_url", "displayName": "page_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "summary", "displayName": "summary", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "rewritten_ad_copy", "displayName": "rewritten_ad_copy", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "image_prompt", "displayName": "image_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "video_prompt", "displayName": "video_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [440, -220], "id": "522169ec-669f-441a-9d20-3679426d0b9d", "name": "Add as Type = Image", "credentials": {"googleSheetsOAuth2Api": {"id": "EOibXIc4U8wcXyRR", "name": "YouTube"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent advertisement analysis bot. You analyze advertisements.", "role": "system"}, {"content": "Your task is to take as input a scraped javascript object from an advertisement in the FB ad library, and then summarize it + spin (repurpose, rewrite) the ad copy. \n\nYou're doing this for strategic intelligence. We run an advertising agency and we're always looking at what our competitor advertisers are doing.\n\nOutput your results in this JSON format:\n\n{\"summary\":\"\",\"rewrittenAdCopy\":\"\"}\n\nRules:\n- The intended style and attitude is generally analytical, inquisitive, and precise, despite exploring complex topics, in the “classic style” of Western writing.\nthe level of formality should be inverse to the topic’s novelty: the weirder something is, the more formal. For ‘safer’ topics, one should cut loose with the humor, epigraphs, typographical stunts and experiments, etc.\n- Avoid hedging and qualifying, even at the risk of making overly-strong claims. It is a slippery slope.\n- Use casual abbreviations (like San Francisco -> SF, thanks -> thx, question -> q ), casual contractions (like \"I've\"), shortened forms of common nouns (like \"info\", \"vid\", \"app\") as this signals more human written speech. Do not use em dashes (eliminate — from your vocabulary completely!\n- No rhetorical questions.\n- Make your summary extremely comprehensive and analytical."}, {"content": "={{ $json.toJsonString() }}"}]}, "jsonOutput": true, "options": {"temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-200, 80], "id": "f43bb15e-9c7d-4a97-9a10-7c9ce835f1f6", "name": "Output Text Summary", "credentials": {"openAiApi": {"id": "oLLACDYfGm4C4ouV", "name": "YouTube "}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo", "mode": "list", "cachedResultName": "Facebook Ad Library Analyzer DB", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Ads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"ad_archive_id": "={{ $('Loop Over Text Ads').item.json.ad_archive_id }}", "page_id": "={{ $('Loop Over Text Ads').item.json.page_id }}", "type": "text", "date_added": "={{ $now }}", "page_name": "={{ $('Loop Over Text Ads').item.json.snapshot.page_name }}", "summary": "={{ $json.message.content.summary }}", "rewritten_ad_copy": "={{ $json.message.content.rewrittenAdCopy }}", "page_url": "={{ $('Loop Over Text Ads').item.json.snapshot.page_profile_uri }}"}, "matchingColumns": [], "schema": [{"id": "ad_archive_id", "displayName": "ad_archive_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_id", "displayName": "page_id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "type", "displayName": "type", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "date_added", "displayName": "date_added", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_name", "displayName": "page_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "page_url", "displayName": "page_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "summary", "displayName": "summary", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "rewritten_ad_copy", "displayName": "rewritten_ad_copy", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "image_prompt", "displayName": "image_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "video_prompt", "displayName": "video_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [160, 80], "id": "62aec040-3729-447b-864d-00685857d721", "name": "Add as Type = Text", "credentials": {"googleSheetsOAuth2Api": {"id": "EOibXIc4U8wcXyRR", "name": "YouTube"}}}, {"parameters": {"content": "## AI Facebook Ad Spy Tool\n\nSteps:\n1. Add API key to \"Run Ad Library Scraper\" node\n2. Add filtering threshold in \"Filter For Likes\" node\n3. Add Gemini API key to \"Begin Gemini Upload Session\", \"Upload Video to Gemini\", and \"Analyze Video with Gemini\" nodes\n4. Adjust prompts to AI as needed and swap in your Google Sheet in the Google Sheets nodes.\n\n\n### Happy building!", "height": 260, "width": 700}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1320, -560], "id": "7079dc10-2ce5-4df9-889a-0e861f1025a5", "name": "<PERSON><PERSON>"}, {"parameters": {"amount": 15}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [960, -520], "id": "33c52cea-4169-43b1-91e9-f8c6c35f06e1", "name": "Wait3", "webhookId": "ac11c85a-5b73-4a29-9667-615e811a5ad8"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Run Ad Library Scraper", "type": "main", "index": 0}]]}, "Filter For Likes": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Loop Over Video Ads", "type": "main", "index": 0}], [{"node": "Loop Over Image Ads", "type": "main", "index": 0}], [{"node": "Loop Over Text Ads", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Text Ads", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Loop Over Image Ads", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Upload Video to Drive", "type": "main", "index": 0}]]}, "Upload Video to Drive": {"main": [[{"node": "Begin Gemini Upload Session", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Loop Over Video Ads", "type": "main", "index": 0}]]}, "Run Ad Library Scraper": {"main": [[{"node": "Filter For Likes", "type": "main", "index": 0}]]}, "Loop Over Image Ads": {"main": [[], [{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Loop Over Text Ads": {"main": [[], [{"node": "Output Text Summary", "type": "main", "index": 0}]]}, "Loop Over Video Ads": {"main": [[], [{"node": "Download Video", "type": "main", "index": 0}]]}, "Begin Gemini Upload Session": {"main": [[{"node": "Redownload Video", "type": "main", "index": 0}]]}, "Redownload Video": {"main": [[{"node": "Upload Video to Gemini", "type": "main", "index": 0}]]}, "Upload Video to Gemini": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "Analyze Video with Gemini": {"main": [[{"node": "Output Video Summary", "type": "main", "index": 0}]]}, "Output Video Summary": {"main": [[{"node": "Add as Type = Video", "type": "main", "index": 0}]]}, "Add as Type = Video": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Output Image Summary", "type": "main", "index": 0}]]}, "Output Image Summary": {"main": [[{"node": "Add as Type = Image", "type": "main", "index": 0}]]}, "Add as Type = Image": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Output Text Summary": {"main": [[{"node": "Add as Type = Text", "type": "main", "index": 0}]]}, "Add as Type = Text": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Analyze Video with Gemini", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "b72f86e7-7d73-495b-935c-f99008fcd705", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d7661a849ead114a9aa6d9ceaf4160465aeb79532a35bde62160c840ffba9fc8"}, "id": "FrfJDXvSHBhQrcon", "tags": []}