# N8N Workflow Scraper - Usage Guide

## Quick Start

### 1. Installation
```bash
# Run the installation script
./install.sh

# Or install manually
npm install
```

### 2. Configuration
```bash
# Copy and edit the environment file
cp .env.example .env
# Edit .env with your preferred settings
```

### 3. Run the Scraper
```bash
# Simple run
npm start

# Or with custom options
npm run scrape -- --output ./my-workflows --verbose
```

## Command Line Interface

### Basic Usage
```bash
# Default scraping
node src/scraper.js

# With custom output directory
node src/scraper.js --output ./custom-workflows

# With increased concurrency
node src/scraper.js --concurrent 5

# Verbose logging
node src/scraper.js --verbose

# Run in non-headless mode (show browser)
node src/scraper.js --headless false
```

### Advanced CLI
```bash
# Full CLI with all options
node src/cli.js scrape --help

# Analyze existing workflows
node src/cli.js analyze ./workflows/raw

# Show current configuration
node src/cli.js config
```

## Configuration Options

### Environment Variables (.env file)
```bash
# Target website
TARGET_URL=https://n8nworkflows.xyz

# Output settings
OUTPUT_DIR=./workflows

# Scraping behavior
MAX_CONCURRENT_REQUESTS=3
REQUEST_DELAY_MS=2000
REQUESTS_PER_MINUTE=30
BURST_LIMIT=5

# Browser settings
HEADLESS=true
USER_DATA_DIR=./browser-data

# Cloudflare bypass
USE_STEALTH=true
WAIT_FOR_CLOUDFLARE=true
CLOUDFLARE_TIMEOUT=30000

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/scraper.log
```

## Output Structure

After running the scraper, you'll get:

```
workflows/
├── metadata.json          # Complete metadata and statistics
├── duplicates.json        # Report of duplicate workflows found
├── raw/                   # All downloaded workflows
│   ├── workflow_1.json
│   ├── workflow_2.json
│   └── ...
└── categories/            # Workflows organized by category
    ├── ai/               # AI-related workflows
    ├── automation/       # Automation workflows
    ├── integration/      # Integration workflows
    ├── data-processing/  # Data processing workflows
    └── ...
```

## Understanding the Results

### Metadata File
The `metadata.json` file contains:
- Total workflow counts
- Category breakdown
- Node type statistics
- Complexity analysis
- Feature detection (AI, webhooks, scheduling)

### Categories
Workflows are automatically categorized based on:
- Node types used
- Workflow names and descriptions
- Detected patterns

Categories include:
- **ai**: AI/ML workflows (OpenAI, Anthropic, etc.)
- **automation**: Scheduled and triggered workflows
- **integration**: API and service integrations
- **data-processing**: Data transformation workflows
- **communication**: Email, Slack, messaging workflows
- **social-media**: Social platform integrations
- **productivity**: Office and productivity tools
- **ecommerce**: E-commerce and payment workflows
- **monitoring**: Monitoring and alerting workflows
- **file-management**: File operations workflows

## Troubleshooting

### Common Issues

#### 1. Cloudflare Blocking
```
Error: Still blocked by Cloudflare
```
**Solutions:**
- Try running with `--headless false` to see what's happening
- Increase delays: `--delay 5000`
- Use a proxy: `--proxy http://your-proxy:port`

#### 2. Rate Limiting
```
Error: Rate limit reached
```
**Solutions:**
- Reduce concurrency: `--concurrent 1`
- Increase delays: `--delay 3000`
- Lower requests per minute in .env file

#### 3. No Workflows Found
```
No workflows discovered
```
**Solutions:**
- Check if the website is accessible
- Try running in non-headless mode
- Check logs for detailed error messages

#### 4. Browser Issues
```
Error: Failed to launch browser
```
**Solutions:**
- Install Chrome/Chromium
- Check browser permissions
- Try different user data directory

### Debugging

#### Enable Verbose Logging
```bash
node src/scraper.js --verbose
```

#### Check Log Files
```bash
# View main log
tail -f logs/scraper.log

# View error log
tail -f logs/error.log
```

#### Run Tests
```bash
npm test
```

## Ethical Usage

This scraper is designed with ethical considerations:

### Built-in Protections
- Respects robots.txt automatically
- Rate limiting to avoid overloading servers
- Proper User-Agent identification
- Configurable delays between requests

### Best Practices
1. **Use reasonable rate limits** (default: 30 requests/minute)
2. **Don't run multiple instances** simultaneously
3. **Respect the website's terms of service**
4. **Consider reaching out** to site owners for bulk access
5. **Use for personal/educational purposes** primarily

### Checking Ethical Compliance
The scraper automatically checks ethical guidelines and warns about violations.

## Advanced Usage

### Custom User Agent
```bash
# Set in .env file
USER_AGENT="MyBot/1.0 (+https://mysite.com/bot)"
```

### Proxy Usage
```bash
# HTTP proxy
PROXY_URL=http://proxy.example.com:8080

# With authentication
PROXY_USERNAME=user
PROXY_PASSWORD=pass
```

### Custom Output Processing
```javascript
// Example: Process downloaded workflows
const fs = require('fs-extra');
const path = require('path');

async function processWorkflows() {
    const workflowsDir = './workflows/raw';
    const files = await fs.readdir(workflowsDir);
    
    for (const file of files) {
        if (file.endsWith('.json')) {
            const workflow = await fs.readJson(path.join(workflowsDir, file));
            // Process workflow...
        }
    }
}
```

## Performance Tips

### Optimize for Speed
```bash
# Increase concurrency (be careful not to overload)
node src/scraper.js --concurrent 5

# Reduce delays (if not rate limited)
node src/scraper.js --delay 1000
```

### Optimize for Reliability
```bash
# Reduce concurrency
node src/scraper.js --concurrent 1

# Increase delays
node src/scraper.js --delay 5000

# Increase timeout
node src/scraper.js --timeout 60000
```

## Support

### Getting Help
1. Check this usage guide
2. Review the logs in `./logs/`
3. Run the test suite: `npm test`
4. Check the GitHub issues (if applicable)

### Reporting Issues
When reporting issues, include:
- Command used
- Error messages
- Log files (scraper.log, error.log)
- System information (OS, Node.js version)

## License

This project is licensed under the MIT License. See LICENSE file for details.
