{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 300], "parameters": {}, "typeVersion": 1}, {"name": "Extract domain name", "type": "n8n-nodes-base.function", "position": [700, 300], "parameters": {"functionCode": "// Take email and extract the domain name \nvar email = ($json[\"email\"]);\nvar name   = email.substring(0, email.lastIndexOf(\"@\"));\nvar domain = email.substring(email.lastIndexOf(\"@\") +1);\n\n//To display the final domain name. (result)\n\nreturn [{\n  json: { domain }\n}]"}, "typeVersion": 1}, {"name": "Sample email", "type": "n8n-nodes-base.set", "position": [460, 300], "parameters": {"values": {"string": [{"name": "email", "value": "<EMAIL>"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}], "connections": {"Sample email": {"main": [[{"node": "Extract domain name", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Sample email", "type": "main", "index": 0}]]}}}