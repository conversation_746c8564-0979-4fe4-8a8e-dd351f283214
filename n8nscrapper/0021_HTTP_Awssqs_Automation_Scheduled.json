{"nodes": [{"name": "AWS SQS", "type": "n8n-nodes-base.awsSqs", "position": [1050, 360], "parameters": {"queue": "", "options": {}}, "credentials": {"aws": "AWS SQS Credentials"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [850, 360], "parameters": {"values": {"number": [{"name": "Latitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"latitude\"]}}"}, {"name": "Longitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"longitude\"]}}"}, {"name": "Timestamp", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"timestamp\"]}}"}], "string": [{"name": "Name", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"name\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [650, 360], "parameters": {"url": "https://api.wheretheiss.at/v1/satellites/25544/positions", "options": {}, "queryParametersUi": {"parameter": [{"name": "timestamps", "value": "={{Date.now();}}"}]}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [450, 360], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "AWS SQS", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}