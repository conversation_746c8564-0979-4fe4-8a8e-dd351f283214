{"name": "SOP agent", "nodes": [{"parameters": {"promptType": "define", "text": "=You are a helpful customer service agent for TechFlow Solutions. Handle greetings, pleasantries, and general conversation naturally and professionally.\n\n**For specific questions requiring factual information or procedural guidance:**\n\nONLY respond if you have a clear, accurate answer from the SOP documents in the vector store. Do NOT make up, assume, or infer information that isn't explicitly stated in the retrieved context.\n\n**Response Guidelines:**\n- Format your response in clean markdown\n- Use bullet points, numbered lists, and headers where appropriate\n- Include specific details like timeframes, approval amounts, and step-by-step procedures exactly as stated in the SOP\n- If information is partially available but incomplete, state what you know and mention what information is missing\n- **ALWAYS include source references at the end of your response**\n\n**Source Reference Format:**\nEnd your response with:\n\nSource: [Document Name] - Section: [Section Name if available]\nView Document: [Link to original document]\n\n**If you don't have a clear response:**\nSimply say: \"I don't have a response to that question in our current SOPs.\"\n\n\n**Question:** {{ $json.user_message }}\n\nRemember: Accuracy over helpfulness. Never fabricate policy details or procedures. Always cite your sources with clickable links", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [460, 1500], "id": "1e6a9b76-513d-4f66-8149-6815b752e077", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [320, 1660], "id": "31d28de8-a69f-4cf7-afb7-cbdace5a5516", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "SSq0cp91ixVWVXVd", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Listen slack messages').item.json.user }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [440, 1660], "id": "37c263a4-c9ca-4a83-abf1-b768e29f0d18", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "W1J0AwWNEYzfmUiD", "name": "Postgres account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolDescription": "Retrieves the matching context from the vector database", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "topK": 3, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [580, 1660], "id": "1475ad8f-24c7-478c-b18c-f4221e3a6a11", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "eD3gDH4BfuUStVcj", "name": "Supabase account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [660, 1820], "id": "6c39e743-90e4-4344-bf8a-db76aa55d79c", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "SSq0cp91ixVWVXVd", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"inputText": "={{ $json.output }}", "categories": {"categories": [{"category": "response_success", "description": "This contains relevant info from a company SOP"}, {"category": "notfound", "description": "If the information is invalid. eg: \"not found\""}]}, "options": {"fallback": "other"}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1.1, "position": [940, 1560], "id": "a9d21de5-6993-47f5-bceb-9baa98c72648", "name": "Text Classifier"}, {"parameters": {"dataType": "binary", "loader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textSplittingMode": "custom", "options": {"metadata": {"metadataValues": [{"name": "url", "value": "={{ $('set metadata').item.json.url }}"}, {"name": "file_name", "value": "={{ $('set metadata').item.json['file name'] }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1.1, "position": [1000, 1040], "id": "079a4b2a-218d-4a72-828f-e63841b43264", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [720, 1040], "id": "f995cd67-b1bb-40d0-84f8-85bd2703e8e0", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "SSq0cp91ixVWVXVd", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"chunkOverlap": 150, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [860, 1140], "id": "02d8cdba-fd16-409f-b9b4-fd444a3e6e7c", "name": "Recursive Character Text Splitter"}, {"parameters": {"content": "# Document Processing\n\n## Watches Google Drive folder, downloads new SOPs, and stores them in a searchable database\n", "height": 700, "width": 1540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-120, 640], "id": "bffdcc7f-62aa-4748-b0c5-d08ca9d7dc03", "name": "<PERSON><PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1XHH0o3ws-4FmyfL8aW3eGC1gqDeayrxF", "mode": "list", "cachedResultName": "n8n", "cachedResultUrl": "https://drive.google.com/drive/folders/1XHH0o3ws-4FmyfL8aW3eGC1gqDeayrxF"}, "event": "fileCreated", "options": {"fileType": "application/vnd.google-apps.document"}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [20, 840], "id": "4c674612-484e-490a-a5ce-250d74cec6b3", "name": "Watch for new docs", "credentials": {"googleDriveOAuth2Api": {"id": "5U5SxbsubT7RnSk2", "name": "Google Drive account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [240, 840], "id": "b307ee57-eb71-4dbf-b4e7-1245cc8ea2a1", "name": "Loop Over Docs"}, {"parameters": {"assignments": {"assignments": [{"id": "25e39a44-ab0e-4e6b-9a10-3af86f82a94f", "name": "file name", "value": "={{ $json.name }}", "type": "string"}, {"id": "2e9598a1-9130-432a-a957-1f0db06b9e6b", "name": "url", "value": "={{ $json.webViewLink }}", "type": "string"}, {"id": "********-c213-46f2-8c25-dfc77a2227b9", "name": "id", "value": "={{ $json.id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 840], "id": "9c53905a-5b21-4884-aed5-98e7d24e89d5", "name": "set metadata"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [680, 840], "id": "766cd76c-b109-42fb-acae-298c769a9749", "name": "Download File", "credentials": {"googleDriveOAuth2Api": {"id": "5U5SxbsubT7RnSk2", "name": "Google Drive account"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [900, 840], "id": "0b852d54-9c68-44f3-8d3c-a0e52ba200c9", "name": "Store in vector database", "credentials": {"supabaseApi": {"id": "eD3gDH4BfuUStVcj", "name": "Supabase account"}}}, {"parameters": {"trigger": ["app_mention", "message"], "channelId": {"__rl": true, "value": "C092FE00F7E", "mode": "list", "cachedResultName": "new-channel"}, "options": {}}, "type": "n8n-nodes-base.slackTrigger", "typeVersion": 1, "position": [-120, 1560], "id": "0580bf97-e0c2-4ee6-8de6-45f54ccc31ee", "name": "Listen slack messages", "webhookId": "4ebfea61-13f5-4429-bc74-09e327d451a6", "credentials": {"slackApi": {"id": "L9H0KilbxwmAJK0p", "name": "Slack account 5"}}}, {"parameters": {"assignments": {"assignments": [{"id": "8bf8eed7-fca0-4904-ad71-a24e396c6418", "name": "user_message", "value": "={{ $json.blocks[0].elements[0].elements[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [100, 1560], "id": "d2bfefc6-33c5-43f4-a5fe-8209fb192c80", "name": "Set message"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "15bhN9Ksp18X4sdq398CVqSI3uGrqjVJZ3ASeHt3b7c8", "mode": "list", "cachedResultName": "vectorStoreInvalid", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15bhN9Ksp18X4sdq398CVqSI3uGrqjVJZ3ASeHt3b7c8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/15bhN9Ksp18X4sdq398CVqSI3uGrqjVJZ3ASeHt3b7c8/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"date": "={{ $now.format('dd-LLL-yy => HH:mm') }}", "user": "={{ $('Listen slack messages').item.json.user }}", "query": "={{ $('Listen slack messages').item.json.text }}"}, "matchingColumns": [], "schema": [{"id": "user", "displayName": "user", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "date", "displayName": "date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1440, 1700], "id": "a49f1f50-65ad-4caf-9f05-6001721d56c6", "name": "store invalid runs", "credentials": {"googleSheetsOAuth2Api": {"id": "lZgvibN9Tvd4Jk3g", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "const markdown = $input.first().json.output;\n\n// Prepare Slack blocks\nconst blocks = [];\n\n// Header block\nblocks.push({\n  type: \"section\",\n  text: {\n    type: \"mrkdwn\",\n    text: \"*📋 SOP Response*\"\n  }\n});\n\n// Divider\nblocks.push({ type: \"divider\" });\n\n// Check if it's a \"no response\" message\nif (markdown.toLowerCase().includes(\"i don't have a response\")) {\n  blocks.push({\n    type: \"section\",\n    text: {\n      type: \"mrkdwn\",\n      text: \"❌ \" + markdown\n    }\n  });\n  \n  blocks.push({\n    type: \"context\",\n    elements: [{\n      type: \"mrkdwn\",\n      text: \"_This question will be added to our review queue for future updates._\"\n    }]\n  });\n} else {\n  // Convert markdown to Slack mrkdwn format\n  let slackText = markdown\n    // Convert bullet points\n    .replace(/^- (.+)/gm, '• $1')\n    .replace(/^\\* (.+)/gm, '• $1')\n    // Convert numbered lists (preserve numbering)\n    .replace(/^(\\d+)\\. (.+)/gm, '$1. $2')\n    // Convert bold text\n    .replace(/\\*\\*(.+?)\\*\\*/g, '*$1*')\n    // Convert headers (different levels)\n    .replace(/^### (.+)/gm, '\\n*$1*')\n    .replace(/^## (.+)/gm, '\\n*$1*')\n    .replace(/^# (.+)/gm, '\\n*$1*')\n    // Clean up extra newlines\n    .replace(/\\n{3,}/g, '\\n\\n')\n    .trim();\n\n  // Add main content block\n  blocks.push({\n    type: \"section\",\n    text: {\n      type: \"mrkdwn\",\n      text: slackText\n    }\n  });\n}\n\n// Footer context\nblocks.push({\n  type: \"context\",\n  elements: [{\n    type: \"mrkdwn\",\n    text: \"_Generated from company SOPs • Need help? Ask in #hr-support_\"\n  }]\n});\n\n// Return the blocks\nreturn [{\n  x: {\n    blocks: blocks\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1440, 1520], "id": "fc042ab4-685d-412a-b715-90f34649d4a9", "name": "Convert to slack block"}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C092FE00F7E", "mode": "list", "cachedResultName": "new-channel"}, "messageType": "block", "blocksUi": "={{ $json.x }}", "text": "=", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1640, 1520], "id": "236c48d9-7a6d-46b1-a3a4-6f3eb070472f", "name": "send message", "webhookId": "55fb3e30-0144-4860-ab00-c4315f5feec0", "credentials": {"slackApi": {"id": "L9H0KilbxwmAJK0p", "name": "Slack account 5"}}}, {"parameters": {"content": "# Slack Bot\n\n## Answers employee questions by searching the database and responding in Slack", "height": 620, "width": 1920, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-140, 1360], "id": "e4ef3e57-d1d6-4580-a2fc-13c38d4d6f95", "name": "Sticky Note1"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Convert to slack block", "type": "main", "index": 0}], [{"node": "store invalid runs", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Store in vector database", "type": "ai_document", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Store in vector database", "type": "ai_embedding", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Watch for new docs": {"main": [[{"node": "Loop Over Docs", "type": "main", "index": 0}]]}, "Loop Over Docs": {"main": [[], [{"node": "set metadata", "type": "main", "index": 0}]]}, "set metadata": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Store in vector database", "type": "main", "index": 0}]]}, "Store in vector database": {"main": [[{"node": "Loop Over Docs", "type": "main", "index": 0}]]}, "Listen slack messages": {"main": [[{"node": "Set message", "type": "main", "index": 0}]]}, "Set message": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Convert to slack block": {"main": [[{"node": "send message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3961ced1-287a-4949-a9ad-88b3378d1103", "meta": {"templateCredsSetupCompleted": true, "instanceId": "211c993497b7ce6b46c49bc2f0c193843c9d41d06902347755445b121fa6ea2c"}, "id": "23brXmnCVHLg9laF", "tags": [{"createdAt": "2025-06-23T07:49:04.851Z", "updatedAt": "2025-06-23T07:49:04.851Z", "id": "37P6wQDZy5HngMlf", "name": "yt"}]}