{"name": "G-mail inbox assistant", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {"readStatus": "unread"}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-2200, 100], "id": "2f2ee261-940d-4745-9967-cc4f593a3fcd", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"text": "={{ $json.from.value[0].name }}", "attributes": {"attributes": [{"name": "sender<PERSON>ame", "description": "Extract the sender's name from the email. If the name is not available, return an empty string.", "required": true}]}, "options": {"systemPromptTemplate": "You are an extraction expert. Identify and extract only the key information from the text. If a required attribute is not present, return an empty string as its value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [-1980, 100], "id": "3cbe06d4-4b6b-4565-a76a-dc1484ac81f5", "name": "Information Extractor"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "efaf396c-e36b-4369-b904-4726787fa9b7", "leftValue": "={{ $json.output.senderName }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1604, 100], "id": "c1269d05-e6dd-49d9-9d9a-407628679498", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "9472688f-14a8-415c-864e-1ad245ff0d6d", "name": "introduction", "value": "Hey,", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1384, 200], "id": "6eeab6a3-51de-4338-bb1e-045c221eee98", "name": "No Name"}, {"parameters": {"assignments": {"assignments": [{"id": "2a305a52-3ce0-4586-a900-038a6612178e", "name": "introduction", "value": "=Dear {{ $json.output.senderName }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1384, 0], "id": "6c35f30b-7c04-486f-976f-a14e810a3d9d", "name": "Name Found"}, {"parameters": {"mode": "combine", "fieldsToMatchString": "introduction", "joinMode": "keepNonMatches", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [-1164, 100], "id": "6113e8c9-edd8-456c-86ed-d788af0d65f2", "name": "<PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "1d374039-a5c3-4047-b19c-4d05f485cceb", "name": "introduction", "value": "={{ $json.introduction }}", "type": "string"}, {"id": "eafcf4be-81e1-42af-83e7-1407680585fc", "name": "emailBody", "value": "={{ $('G<PERSON> Trigger').item.json.text }}", "type": "string"}, {"id": "0f9e0296-2c71-47d9-a526-b29ca30a41d2", "name": "messageId", "value": "={{ $('Gmail Trigger').item.json.id }}", "type": "string"}, {"id": "ea2e0681-abb6-49ae-92f5-aa08f6c1cb79", "name": "threadId", "value": "={{ $('Gmail Trigger').item.json.threadId }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-944, 100], "id": "f83ecff9-deac-4a1f-875e-e2ed34cd1491", "name": "<PERSON>"}, {"parameters": {"inputText": "={{ $json.emailBody }}", "categories": {"categories": [{"category": "Course Requests", "description": "This includes inquiries about course services, pricing, availability, or related details."}, {"category": "Consultation Requests", "description": "This includes scheduling initial consultations or follow-up appointments."}, {"category": "Collabs", "description": "Inquiries about paid collab, payments, or refund requests."}, {"category": "Miscellaneous", "description": "Emails that don't fall into any of the predefined categories."}]}, "options": {"systemPromptTemplate": "Please share with me the specific {categories} that you would like to use so I can perform the analysis correctly. Once you have provided the categories, I will deliver the answer in JSON format, without explanations."}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [-724, 100], "id": "04f164e0-9ab9-4c57-a700-a2f9fdef6a78", "name": "Text Classifier"}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.messageId }}", "labelIds": ["Label_672250486686597277"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-348, -200], "id": "0ea55e42-1a2d-478e-a059-d791fe13e000", "name": "Gmail", "webhookId": "af462a09-091d-4ffa-9c2a-2d9dd1f15f88", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.messageId }}", "labelIds": ["Label_7644202721179154661"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-348, 0], "id": "0aa785b3-f496-463c-84b5-32b67ef733d5", "name": "Gmail1", "webhookId": "af462a09-091d-4ffa-9c2a-2d9dd1f15f88", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.messageId }}", "labelIds": ["Label_1736130074886611713"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-348, 200], "id": "f5490830-5bc4-4d28-855e-ffb71ab64021", "name": "Gmail2", "webhookId": "af462a09-091d-4ffa-9c2a-2d9dd1f15f88", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $json.messageId }}", "labelIds": ["CATEGORY_PROMOTIONS"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-348, 400], "id": "6e7abbd5-5946-4f5b-8576-f58634c3a561", "name": "Gmail3", "webhookId": "af462a09-091d-4ffa-9c2a-2d9dd1f15f88", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $json.id }}", "emailType": "text", "message": "={{ $('Text Classifier').item.json.introduction }}\n\nThank you for contacting us! We're excited to learn more about your needs and discuss how we can help you.\n\nYour request has been received, and a member of our team is reviewing the details. We'll get back to you within 24 hours with more information.\n\nIn the meantime, feel free to visit our website to learn more about our services.\n\nWe look forward to connecting with you!", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-128, -200], "id": "34d1496a-4a23-45a6-bec4-fd2774276b09", "name": "Gmail4", "webhookId": "b02440bb-d0ae-4f5e-a0f0-bd46c75ffb74", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $json.id }}", "emailType": "text", "message": "={{ $('Text Classifier').item.json.introduction }}\n\nThank you for contacting us. We're excited to learn more about your needs and discuss how we can help you.\n\nTo schedule your consultation, please reply to this email with your preferred date and time.\n\nOnce we receive your preferred time, we'll confirm the appointment or suggest alternative options if needed.\n\nIf you have specific topics you'd like to discuss during the consultation, feel free to mention them in your reply.\n\nWe look forward to be connecting with you!", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-128, 0], "id": "d724f9d6-0bbe-4cd1-98eb-435c983cfd05", "name": "Gmail5", "webhookId": "b02440bb-d0ae-4f5e-a0f0-bd46c75ffb74", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $json.id }}", "emailType": "text", "message": "={{ $('Text Classifier').item.json.introduction }}\n\nThank you for contacting us regarding your paid collab/billing inquiry. \n\nA member of our team will reach out to you within 2 business days to provide the necessary information or assistance.\n\nIf this is an urgent matter, please call us directly at +*********.\n\nThank you for your patience and for choosing us.", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-128, 200], "id": "74ff6b14-07bc-44a4-8506-20fa9c65fa3e", "name": "Gmail6", "webhookId": "b02440bb-d0ae-4f5e-a0f0-bd46c75ffb74", "credentials": {"gmailOAuth2": {"id": "bb2P97O6jFrvOQbu", "name": "Gmail account"}}}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1892, 320], "id": "cd66b66d-2781-430b-a02a-c16a81876e9d", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "nscaoDHmHaSsUvs0", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-636, 320], "id": "b4a76414-5245-455d-af78-cc8a70793b64", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "nscaoDHmHaSsUvs0", "name": "Google Gemini(PaLM) Api account"}}}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Name Found", "type": "main", "index": 0}], [{"node": "No Name", "type": "main", "index": 0}]]}, "Name Found": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "No Name": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Gmail", "type": "main", "index": 0}], [{"node": "Gmail1", "type": "main", "index": 0}], [{"node": "Gmail2", "type": "main", "index": 0}], [{"node": "Gmail3", "type": "main", "index": 0}]]}, "Gmail": {"main": [[{"node": "Gmail4", "type": "main", "index": 0}]]}, "Gmail1": {"main": [[{"node": "Gmail5", "type": "main", "index": 0}]]}, "Gmail2": {"main": [[{"node": "Gmail6", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "98e36463-580b-4bfe-897f-e705d8e3ab92", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0f6b06392e6ba9de49771585b7e4cdeacdf7cdc06484fbe6acd8ba3e84e73b87"}, "id": "sOqXx4X9ZEX43Q10", "tags": []}