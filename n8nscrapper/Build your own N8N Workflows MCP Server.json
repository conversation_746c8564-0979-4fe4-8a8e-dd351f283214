{"meta": {"instanceId": "408f9fb9940c3cb18ffdef0e0150fe342d6e655c3a9fac21f0f644e8bedabcd9", "templateCredsSetupCompleted": true}, "nodes": [{"id": "e3ed1048-bad0-4e91-bfb5-aef3e1883de4", "name": "Simplify Workflows", "type": "n8n-nodes-base.set", "position": [-1740, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "821226b0-12ad-4d1d-81c3-dfa3c286cce4", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "629d95d6-2501-4ad4-a5ed-e557237e1cc2", "name": "name", "type": "string", "value": "={{ $json.name }}"}, {"id": "30699f7c-98d3-44ee-9749-c5528579f7e6", "name": "description", "type": "string", "value": "={{\n$json.nodes\n  .filter(node => node.type === 'n8n-nodes-base.stickyNote')\n  .filter(node => node.parameters.content.toLowerCase().includes('try it out'))\n  .map(node => node.parameters.content.substr(0,255) + '...')\n  .join('\\n')\n}}"}, {"id": "6199c275-1ced-4f72-ba59-cb068db54c1b", "name": "parameters", "type": "string", "value": "={{\n(function(node) {\n  if (!node) return {};\n  const inputs = node.parameters.workflowInputs.values;\n  return {\n    \"type\": \"object\",\n    \"required\": inputs.map(input => input.name),\n    \"properties\": inputs.reduce((acc, input) => ({\n      ...acc,\n      [input.name]: { type: input.type ?? 'string' }\n    }), {})\n  }\n})(\n$json.nodes\n  .filter(node => node.type === 'n8n-nodes-base.executeWorkflowTrigger')\n  .first()\n)\n.toJsonString()\n}}"}]}}, "executeOnce": false, "typeVersion": 3.4}, {"id": "a935f5b6-3a35-49e7-870c-87e4daf0ad13", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-3060, 600], "parameters": {"workflowInputs": {"values": [{"name": "operation"}, {"name": "workflowIds"}, {"name": "parameters", "type": "object"}]}}, "typeVersion": 1.1}, {"id": "2ff5e521-5288-47a9-af49-55a1bbbfb4f4", "name": "Operations", "type": "n8n-nodes-base.switch", "position": [-2660, 560], "parameters": {"rules": {"values": [{"outputKey": "Add", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3254a8f9-5fd3-4089-be16-cc3fd20639b8", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').first().json.operation }}", "rightValue": "addWorkflow"}]}, "renameOutput": true}, {"outputKey": "remove", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a33dd02d-5192-48c9-b569-eafddabd2462", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').first().json.operation }}", "rightValue": "removeWorkflow"}]}, "renameOutput": true}, {"outputKey": "list", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2d68dc3f-a213-47f8-8453-1bceae404653", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').first().json.operation }}", "rightValue": "listWorkflows"}]}, "renameOutput": true}, {"outputKey": "search", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2146a87e-1a50-4caa-a2ee-f7f6fc2b19c9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').first().json.operation }}", "rightValue": "searchWorkflows"}]}, "renameOutput": true}, {"outputKey": "execute", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "98b25a51-2cb5-49af-9609-827245595dc9", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('When Executed by Another Workflow').first().json.operation }}", "rightValue": "executeWorkflow"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "5b78271a-6474-4d87-a344-72f7f63822dc", "name": "Get MCP-tagged Workflows", "type": "n8n-nodes-base.n8n", "position": [-2400, 200], "parameters": {"filters": {"tags": "mcp"}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "5vELmsVPmK4Bkqkg", "name": "n8n account"}}, "typeVersion": 1}, {"id": "1197d29e-b124-4576-846d-876ad16de6e9", "name": "Filter Matching Ids", "type": "n8n-nodes-base.filter", "position": [-2180, 200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "90c97733-48de-4402-8388-5d49e3534388", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{\n$json.id\n  ? $('When Executed by Another Workflow').first().json.workflowIds.split(',').includes($json.id)\n  : false\n}}", "rightValue": "={{ $json.id }}"}]}}, "executeOnce": false, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "********-c3e7-4e20-86a9-d2587b302f28", "name": "Store In Memory", "type": "n8n-nodes-base.redis", "position": [-1520, 0], "parameters": {"key": "mcp_n8n_tools", "value": "={{\n($('Get Memory').item.json.data?.parseJson() ?? [])\n  .concat($input.all().map(item => item.json))\n  .toJsonString()\n}}", "operation": "set"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "executeOnce": true, "typeVersion": 1}, {"id": "5ff0ea2f-a2ee-4cc3-bdf9-153ce5973770", "name": "AddTool Success", "type": "n8n-nodes-base.set", "position": [-1300, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d921063f-e8ed-44a8-95a0-4402ecde6c5d", "name": "=response", "type": "string", "value": "={{ $('Simplify Workflows').all().length }} tools were added successfully."}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "1d3169cc-15cd-4296-9e63-bb162322e5e2", "name": "AddTool Error", "type": "n8n-nodes-base.set", "position": [-1740, 200], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8c4e0763-a4ff-4e8a-a992-13e4e12a5685", "name": "response", "type": "string", "value": "Expected Tools matching Ids given, but none found."}]}}, "executeOnce": true, "typeVersion": 3.4}, {"id": "6149a950-c1ed-44b4-aee6-3daeabf8ba01", "name": "Get Memory", "type": "n8n-nodes-base.redis", "position": [-2860, 600], "parameters": {"key": "mcp_n8n_tools", "options": {}, "operation": "get", "propertyName": "data"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "typeVersion": 1}, {"id": "3c538002-45f7-4a2f-9ef4-5aede63235ab", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [-2180, 400], "parameters": {"options": {}, "fieldToSplitOut": "data"}, "typeVersion": 1}, {"id": "d41e48e0-d610-4e18-9942-842419c99c83", "name": "Filter Matching IDs", "type": "n8n-nodes-base.filter", "position": [-1960, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d2c149fb-d115-449b-9b74-f3c2f8ff7950", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "leftValue": "={{\n$json.id\n  ? $('Operations').first().json.workflowIds.split(',').includes($json.id)\n  : false\n}}", "rightValue": ""}]}}, "typeVersion": 2.2, "alwaysOutputData": true}, {"id": "21d8cdda-bb47-42cd-a056-809a5556b438", "name": "Store In Memory1", "type": "n8n-nodes-base.redis", "position": [-1520, 500], "parameters": {"key": "mcp_n8n_tools", "value": "={{ $input.all().flatMap(item => item.json.data).compact() }}", "operation": "set"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "executeOnce": true, "typeVersion": 1}, {"id": "5a391d0a-ba13-4d54-85fd-eb2f6a935614", "name": "Remove Tool Success", "type": "n8n-nodes-base.set", "position": [-1300, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1368947f-6625-4e2e-ae27-0fcad0a1d12a", "name": "response", "type": "string", "value": "={{ $('When Executed by Another Workflow').first().json.workflowIds.split(',').length }} tool(s) removed successfully."}]}}, "typeVersion": 3.4}, {"id": "65dfecc4-43ba-4518-adbf-9676c5cb1377", "name": "Convert to JSON", "type": "n8n-nodes-base.set", "position": [-2400, 400], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bce29a06-cff6-4409-96d2-04cc858a0e98", "name": "data", "type": "array", "value": "={{ $json.data.parseJson() }}"}]}}, "typeVersion": 3.4}, {"id": "b8b64fc2-63cf-4b17-9b6d-9d94aec10065", "name": "listTools Success", "type": "n8n-nodes-base.set", "position": [-2400, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bce29a06-cff6-4409-96d2-04cc858a0e98", "name": "response", "type": "array", "value": "={{\n$json.data\n  ? $json.data.parseJson()\n  : []\n}}"}]}}, "typeVersion": 3.4}, {"id": "d4fd9e74-f040-4b3c-8ce0-371315a0d130", "name": "Get MCP-tagged Workflows1", "type": "n8n-nodes-base.n8n", "position": [-2180, 600], "parameters": {"filters": {"tags": "mcp"}, "requestOptions": {}}, "credentials": {"n8nApi": {"id": "5vELmsVPmK4Bkqkg", "name": "n8n account"}}, "typeVersion": 1}, {"id": "d58922c4-b721-4228-83cb-0b1d9632bbc6", "name": "Simplify Workflows1", "type": "n8n-nodes-base.set", "position": [-1960, 600], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "821226b0-12ad-4d1d-81c3-dfa3c286cce4", "name": "id", "type": "string", "value": "={{ $json.id }}"}, {"id": "629d95d6-2501-4ad4-a5ed-e557237e1cc2", "name": "name", "type": "string", "value": "={{ $json.name }}"}, {"id": "30699f7c-98d3-44ee-9749-c5528579f7e6", "name": "description", "type": "string", "value": "={{\n$json.nodes\n  .filter(node => node.type === 'n8n-nodes-base.stickyNote')\n  .filter(node => node.parameters.content.toLowerCase().includes('try it out'))\n  .map(node => node.parameters.content.substr(0,255) + '...')\n  .join('\\n')\n}}"}, {"id": "137221ef-f0a3-4441-bae7-d9d4a22e05b7", "name": "parameters", "type": "string", "value": "={{\n(function(node) {\n  if (!node) return {};\n  const inputs = node.parameters.workflowInputs.values;\n  return {\n    \"type\": \"object\",\n    \"required\": inputs.map(input => input.name),\n    \"properties\": inputs.reduce((acc, input) => ({\n      ...acc,\n      [input.name]: { type: input.type ?? 'string' }\n    }), {})\n  }\n})(\n$json.nodes\n  .filter(node => node.type === 'n8n-nodes-base.executeWorkflowTrigger')\n  .first()\n)\n.toJsonString()\n}}"}]}}, "executeOnce": false, "typeVersion": 3.4}, {"id": "b44b1115-5153-4b98-979f-219a32b693de", "name": "listTools Success1", "type": "n8n-nodes-base.aggregate", "position": [-1740, 600], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "e9d9a33c-ebfa-4f74-a4da-fb6b6f6f6df5", "name": "Get Parameters", "type": "n8n-nodes-base.set", "position": [-1920, 1020], "parameters": {"mode": "raw", "options": {}, "jsonOutput": "={{ $('When Executed by Another Workflow').first().json.parameters }}"}, "typeVersion": 3.4}, {"id": "c028324a-5c79-4ce9-afd6-097b6f75a74a", "name": "executeTool Result", "type": "n8n-nodes-base.aggregate", "position": [-1440, 1020], "parameters": {"options": {}, "aggregate": "aggregateAllItemData", "destinationFieldName": "response"}, "typeVersion": 1}, {"id": "9eeac3cb-af7e-44c7-8891-6ff4367d0b54", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-3600, 1040], "parameters": {"options": {"systemMessage": "=You are a helpful assistant who only uses workflows to answer questions and perform tasks.\n* When asked to complete a task, you will assume there is a workflow which you can use. Do not generate custom scripts or raw commands - use a workflow isntead.\n* Only use the available active workflows as listed using the \"listWorkflows\" tool.\n* Workflows returned by \"listWorkflows\" tool are active in the pool and do not need to be added again.\n* Always ask the user before adding workflows to the availble pool.\n* You do not need to confirm each step with the user when executing workflows.\n* When executing the workflow, the required parameters schema for it is listed in the workflow's profile. To get this profile, use the listWokflows tool.\n* If no available workflows are suitable and you are not able to complete the task, simply let the user know.\n* Do not search for workflows in the directory unless the user requests."}}, "typeVersion": 1.8}, {"id": "23601548-7863-403e-a671-267bf592b824", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-3840, 1040], "webhookId": "86a50552-8058-4896-bd7e-ab95eba073ce", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "54ed210d-e1b8-4bd7-85e4-88678111a45e", "name": "MCP Client", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [-3360, 1240], "parameters": {"sseEndpoint": "=<Production URL of MCP Server>"}, "typeVersion": 1}, {"id": "c612da64-9cc1-4601-a987-cd2023fd1863", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-3500, 1240], "parameters": {"contextWindowLength": 30}, "typeVersion": 1.3}, {"id": "77a9fd22-c31c-49e4-9d5f-af572b137925", "name": "Convert to JSON1", "type": "n8n-nodes-base.set", "position": [-2360, 1120], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bce29a06-cff6-4409-96d2-04cc858a0e98", "name": "data", "type": "array", "value": "={{ $json.data.parseJson() }}"}]}}, "typeVersion": 3.4}, {"id": "3377aa25-4190-4bdc-be20-b4e324212060", "name": "Has Workflow Available?", "type": "n8n-nodes-base.if", "position": [-2140, 1120], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9c9df00b-b090-4773-8012-1824b4eeb13f", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{\n$json.data.find(d => d.id === $('When Executed by Another Workflow').item.json.workflowIds)\n}}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "92b1bb21-d739-47f0-a278-92ffa5a10cbf", "name": "ExecuteTool Error", "type": "n8n-nodes-base.set", "position": [-1920, 1220], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "2fa3e311-e836-42f4-922a-fae19d8e0267", "name": "response", "type": "string", "value": "=Expected workflow to be available but not yet added. You can only use workflows which have been added to the available pool. Use the listWorkflows tool to see available workflows."}]}}, "typeVersion": 3.4}, {"id": "529e35e0-cf11-405a-9011-e6f7f2122a4e", "name": "Workflow Exists?", "type": "n8n-nodes-base.if", "position": [-1960, 200], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "15aef770-639e-4df0-900f-29013ccd00c4", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "ba278834-c774-4a3d-8ebc-f64ac77317c2", "name": "N8N Workflows MCP Server", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "position": [-3720, 240], "webhookId": "4625bcf4-0dd9-4562-a70f-6fee41f6f12d", "parameters": {"path": "4625bcf4-0dd9-4562-a70f-6fee41f6f12d"}, "typeVersion": 1}, {"id": "ed940612-4772-4377-afe2-5484a8978665", "name": "Add Workflow", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-3800, 460], "parameters": {"name": "addWorkflow", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Adds one or more workflows by ID to the available pool of workflows for the agent. You can get a list of workflows by calling the listTool tool.", "workflowInputs": {"value": {"operation": "addWorkflow", "parameters": "null", "workflowIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('workflowIds', ``, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowIds", "type": "string", "display": true, "required": false, "displayName": "workflowIds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "parameters", "type": "object", "display": true, "required": false, "displayName": "parameters", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "e7d5096c-3545-43fd-aa1f-495dc041ccce", "name": "RemoveWorkflow", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-3700, 560], "parameters": {"name": "removeWorkflow", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Removes one or more workflows by ID from the available pool of workflows for the agent.", "workflowInputs": {"value": {"operation": "removeWorkflow", "parameters": "null", "workflowIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('workflowIds', ``, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowIds", "type": "string", "display": true, "required": false, "displayName": "workflowIds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "parameters", "type": "object", "display": true, "required": false, "displayName": "parameters", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "c20b63dc-e768-4529-a08c-5370853fc4c9", "name": "List Workflows", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-3580, 660], "parameters": {"name": "listTool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Lists the available pool of workflows for the agent.", "workflowInputs": {"value": {"operation": "listWorkflows", "parameters": "null", "workflowIds": "null"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowIds", "type": "string", "display": true, "required": false, "displayName": "workflowIds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "parameters", "type": "object", "display": true, "required": false, "displayName": "parameters", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "88fb8a1e-2f4c-4ff1-8be9-0f7afee2dd4d", "name": "SearchWorkflows", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-3460, 560], "parameters": {"name": "searchTool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Returns all workflows which can be added to the pool of available workflows for the agent.", "workflowInputs": {"value": {"operation": "searchWorkflows", "parameters": "null", "workflowIds": "null"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowIds", "type": "string", "display": true, "required": false, "displayName": "workflowIds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "parameters", "type": "object", "display": true, "required": false, "displayName": "parameters", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "c643c007-de89-4d94-9739-aeb2032c792f", "name": "ExecuteWorkflow", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [-3340, 460], "parameters": {"name": "executeTool", "workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "description": "Executes a workflow which has been added to the pool of available workflows for the agent.", "workflowInputs": {"value": {"operation": "executeWorkflow", "parameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters', ``, 'string') }}", "workflowIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('workflowIds', ``, 'string') }}"}, "schema": [{"id": "operation", "type": "string", "display": true, "required": false, "displayName": "operation", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "workflowIds", "type": "string", "display": true, "required": false, "displayName": "workflowIds", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "parameters", "type": "object", "display": true, "required": false, "displayName": "parameters", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "typeVersion": 2.1}, {"id": "4f1c1559-8d50-48b1-94f2-542e0bb4d494", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-3920, 80], "parameters": {"color": 7, "width": 720, "height": 740, "content": "## 1. Add MCP Server Trigger\n[Read more about the MCP server trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger/)"}, "typeVersion": 1}, {"id": "54d61491-04dc-4263-96e0-67827842ca07", "name": "Execute Workflow with PassThrough Variables", "type": "n8n-nodes-base.executeWorkflow", "position": [-1660, 1020], "parameters": {"options": {"waitForSubWorkflow": true}, "workflowId": {"__rl": true, "mode": "id", "value": "={{ $('When Executed by Another Workflow').first().json.workflowIds }}"}, "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}}, "executeOnce": false, "typeVersion": 1.2}, {"id": "1042884f-a44c-4757-9ff9-3a5cc81058f2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-2600, -140], "parameters": {"color": 7, "width": 740, "height": 300, "content": "## 2. Dynamically manage a list of \"Available\" Workflows\n[Learn more about the n8n node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.n8n)\n\nThe idea is to limit the number of workflows the agent has access to in order to ensure undesired workflows or duplication of similar workflows are avoided. Here, we do this by managing a virtual list of workflows in memory using Redis - under the hood, it's just an array to store Workflow details.\n\nGood to note, the intended workflows must have **Subworkflow triggers** and ideally, with input schema set as well. This template analyses each workflow's JSON and captures its input schema as part of the workflow's description. Doing so,  when it comes time to execute, the agent will know in what format to set the parameters when calling the subworkflow.\n"}, "typeVersion": 1}, {"id": "903ead44-3eab-4606-aa4e-e66378bb5f7e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-2420, 820], "parameters": {"color": 7, "width": 1160, "height": 600, "content": "## 3. Let the Agent execute any N8N Workflow\n[Learn more about the Execute Workflow node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow/)\n\nFinally once the agent has gathered the required workflows, it will start performing the requested task by executing one or more available workflows. The desired behaviour is that the agent will use \"listWorkflows\" to see which workflows are \"active\" and then plan out how to use them. Attempts to use a workflow before adding it to the available pool will result in an error response."}, "typeVersion": 1}, {"id": "194fbcbc-a7bb-41c8-9289-a214b1415386", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-3660, 1240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "aee33258-cf30-4cb4-ab58-7bef7ba27b65", "name": "Is Empty Array?", "type": "n8n-nodes-base.if", "position": [-1740, 400], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2cd1b233-fb24-45d5-9efd-1db44b817809", "operator": {"type": "array", "operation": "empty", "singleValue": true}, "leftValue": "={{ $input.all().flatMap(item => item.json.data).compact() }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "b367a25f-e679-4a71-910e-27f1aa686816", "name": "Delete Key", "type": "n8n-nodes-base.redis", "position": [-1520, 300], "parameters": {"key": "mcp_n8n_tools", "operation": "delete"}, "credentials": {"redis": {"id": "zU4DA70qSDrZM1El", "name": "Redis account (localhost)"}}, "executeOnce": true, "typeVersion": 1}, {"id": "eec527e1-db4d-4294-a076-379ebd9640a9", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-3920, 860], "parameters": {"color": 7, "width": 740, "height": 560, "content": "## 4. Connect any Agent with a MCP Client\nUse this agent to test your MCP server. Note, i"}, "typeVersion": 1}, {"id": "c9b51f36-f9bd-4a60-b195-8da229462331", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-2880, 820], "parameters": {"color": 5, "width": 320, "height": 400, "content": "* **AddWorkflow**\n  This tool adds (or rather, appends) workflows to our \"available\" list.\n* **RemoveWorkflow**\n  This tool removes a workflow entry from our list.\n* **listWorkflows**\n  This tool displays the current state of the workflows list and the available workflows within it. Useful for checking which workflows have been added to the list.\n* **searchWorkflows**\n  For now, this tools just pulls the existing workflows from the n8n instance and returns it to the agent. Given more resources, you may want to swap this out for a indexed search instead (you'll need to build this yourself!)."}, "typeVersion": 1}, {"id": "91b2859a-7563-4ebd-ae61-c9a487e18d81", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-4600, -180], "parameters": {"width": 600, "height": 1440, "content": "## Try it out!\n### This n8n template shows you how to create an MCP server out of your existing n8n workflows. With this, any MCP client connected can get more done with powerful end-to-end workflows rather than just simple tools.\n\nDesigning agent tools for outcome rather than utility has been a long recommended practice of mine and it applies well when it comes to building MCP servers; In gist, it prefers agents to be making the least calls possible to complete a task.\n\nThis is why n8n can be a great fit for MCP servers! This template connects your agent/MCP client (like Claude <PERSON>) to your existing workflows by allowing the AI to discover, manage and run these workflows indirectly.\n\n### How it works\n* An MCP trigger is used and attaches 4 custom workflow tools to discover and manage existing workflows to use and 1 custom workflow tool to execute them.\n* We'll introduce an idea of \"available\" workflows which the agent is allowed to use. This will help limit and avoid some issues when trying to use every workflow such as clashes or non-production.\n* The n8n node is a core node which taps into your n8n instance API and is able to retrieve all workflows or filter by tag. For our example, we've tagged the workflows we want to use with \"mcp\" and these are exposed through the tool \"search workflows\".\n* Redis is used as our main memory for keeping track of which workflows are \"available\". The tools we have are \"add Workflow\", \"remove workflow\" and \"list workflows\". The agent should be able to manage this autonomously.\n* Our approach to allow the agent to execute workflows is to use the Subworkflow trigger. The tricky part is figuring out the input schema for each but was eventually solved by pulling this information out of the workflow's template JSON and adding it as part of the \"available\" workflow's description. To pass parameters through the Subworkflow trigger, we can do so via the passthrough method - which is that incoming data is used when parameters are not explicitly set within the node.\n* When running, the agent will not see the \"available\" workflows immediately but will need to discover them via \"list\" and \"search\". The human will need to make the agent aware that these workflows will be preferred when answering queries or completing tasks.\n\n### How to use\n* First, decide which workflows will be made visible to the MCP server. This example uses the tag of \"mcp\" but you can all workflows or filter in other ways.\n* Next, ensure these workflows have Subworkflow triggers with input schema set. This is how the MCP server will run them.\n* Set the MCP server to \"active\" which turns on production mode and makes available to production URL.\n* Use this production URL in your MCP client. For Claude Desktop, see the instructions here - https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.mcptrigger/#integrating-with-claude-desktop.\n* There is a small learning curve which will shape how you communicate with this MCP server so be patient and test. The MCP server will work better if there is a focused goal in mind ie. Research and report, rather than just a collection of unrelated tools.\n\n### Requirements\n* N8N API key to filter for selected workflows.\n* N8N workflows with Subworkflow triggers!\n* Redis for memory and tracking the \"available\" workflows.\n* MCP Client or Agent for usage such as Claude Desktop - https://claude.ai/download\n\n### Customising this workflow\n* If your targeted workflows do not use the subworkflow trigger, it is possible to amend the executeTool to use HTTP requests for webhooks.\n* Managing available workflows helps if you have many workflows where some may be too similar for the agent. If this isn't a problem for you however, feel free to remove the concept of \"available\" and let the agent discover and use all workflows!"}, "typeVersion": 1}, {"id": "ec3194d2-90c8-4019-a1b5-576c61e9a8b0", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-2600, -280], "parameters": {"color": 5, "width": 380, "height": 120, "content": "### How many existing workflows can I use?\nWell, as many as you want really! For this example, I've limited it for workflows which are tagged as \"mcp\" but you can remove this filter to allow all."}, "typeVersion": 1}, {"id": "5f587241-5604-4724-bc01-3c9bc3f7bdc2", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-1720, 1000], "parameters": {"height": 440, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨 Ensure this node does not set the input schema!\nFor passthrough parameters to work, this node should not make available input schema fields. ie. the input fields should not be visible.\n\nIf there are, the node needs to be reset!"}, "typeVersion": 1}], "pinData": {}, "connections": {"Split Out": {"main": [[{"node": "Filter Matching IDs", "type": "main", "index": 0}]]}, "Delete Key": {"main": [[{"node": "Remove Tool Success", "type": "main", "index": 0}]]}, "Get Memory": {"main": [[{"node": "Operations", "type": "main", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Operations": {"main": [[{"node": "Get MCP-tagged Workflows", "type": "main", "index": 0}], [{"node": "Convert to JSON", "type": "main", "index": 0}], [{"node": "listTools Success", "type": "main", "index": 0}], [{"node": "Get MCP-tagged Workflows1", "type": "main", "index": 0}], [{"node": "Convert to JSON1", "type": "main", "index": 0}]]}, "Add Workflow": {"ai_tool": [[{"node": "N8N Workflows MCP Server", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Get Parameters": {"main": [[{"node": "Execute Workflow with PassThrough Variables", "type": "main", "index": 0}]]}, "List Workflows": {"ai_tool": [[{"node": "N8N Workflows MCP Server", "type": "ai_tool", "index": 0}]]}, "RemoveWorkflow": {"ai_tool": [[{"node": "N8N Workflows MCP Server", "type": "ai_tool", "index": 0}]]}, "Convert to JSON": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "ExecuteWorkflow": {"ai_tool": [[{"node": "N8N Workflows MCP Server", "type": "ai_tool", "index": 0}]]}, "Is Empty Array?": {"main": [[{"node": "Delete Key", "type": "main", "index": 0}], [{"node": "Store In Memory1", "type": "main", "index": 0}]]}, "SearchWorkflows": {"ai_tool": [[{"node": "N8N Workflows MCP Server", "type": "ai_tool", "index": 0}]]}, "Store In Memory": {"main": [[{"node": "AddTool Success", "type": "main", "index": 0}]]}, "Convert to JSON1": {"main": [[{"node": "Has Workflow Available?", "type": "main", "index": 0}]]}, "Store In Memory1": {"main": [[{"node": "Remove Tool Success", "type": "main", "index": 0}]]}, "Workflow Exists?": {"main": [[{"node": "Simplify Workflows", "type": "main", "index": 0}], [{"node": "AddTool Error", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simplify Workflows": {"main": [[{"node": "Store In Memory", "type": "main", "index": 0}]]}, "Filter Matching IDs": {"main": [[{"node": "Is Empty Array?", "type": "main", "index": 0}]]}, "Filter Matching Ids": {"main": [[{"node": "Workflow Exists?", "type": "main", "index": 0}]]}, "Simplify Workflows1": {"main": [[{"node": "listTools Success1", "type": "main", "index": 0}]]}, "Has Workflow Available?": {"main": [[{"node": "Get Parameters", "type": "main", "index": 0}], [{"node": "ExecuteTool Error", "type": "main", "index": 0}]]}, "Get MCP-tagged Workflows": {"main": [[{"node": "Filter Matching Ids", "type": "main", "index": 0}]]}, "Get MCP-tagged Workflows1": {"main": [[{"node": "Simplify Workflows1", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Get Memory", "type": "main", "index": 0}]]}, "Execute Workflow with PassThrough Variables": {"main": [[{"node": "executeTool Result", "type": "main", "index": 0}]]}}}