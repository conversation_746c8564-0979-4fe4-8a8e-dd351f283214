{"name": "Youtube Scraper", "nodes": [{"parameters": {"url": "https://api.apify.com/v2/acts/akash9078~youtube-video-comment-scraper/run-sync-get-dataset-items?token=<enter your apify api key here>", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"maxComments\": 10,\n    \"videoLink\": \"https://youtu.be/5kcaHAuGxmY\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-160, 800], "id": "11f8d0b3-f5fe-46c9-802e-49c47e9b2b6b", "name": "youtube video comment scraper"}, {"parameters": {"url": "https://api.apify.com/v2/acts/akash9078~youtube-channel-video-scraper/run-sync-get-dataset-items?token=<enter your apify api key here>", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"channelUrl\": \"https://www.youtube.com/@apify\",\n    \"includeMetadata\": true\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-440, 800], "id": "7ddfdf53-cf68-4d1b-a140-31ce383494d9", "name": "youtube channel video scraper"}, {"parameters": {"url": "https://api.apify.com/v2/acts/akash9078~indian-stocks-financial-ratios-api/run-sync-get-dataset-items?token=<enter your apify api key here>", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"symbol\": \"reliance\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-400, 520], "id": "7993883d-12c2-4286-8fd5-fff93b78cd68", "name": "indian stock scraper"}, {"parameters": {"url": "https://api.apify.com/v2/acts/akash9078~full-website-screenshot-generator/run-sync-get-dataset-items?token=<enter your apify api key here>", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"fullPage\": true,\n    \"url\": \"https://yesintelligent.com/\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-680, 520], "id": "3cde9175-3b29-409d-9319-ff10bac12f12", "name": "screenshot scraper"}, {"parameters": {"url": "https://api.apify.com/v2/acts/akash9078~youtube-transcript-extractor-pro/run-sync-get-dataset-items?token=<enter your apify api key here>", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"videoUrl\": \"https://youtu.be/1z1IcvZ-pvA?si=4VKPUXl2X9dIZi8y\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-700, 800], "id": "5917e0a5-5b68-48f4-ba04-966bbd45baa4", "name": "youtube transcript scraper"}, {"parameters": {"content": "Get your apify key here: \nhttps://www.apify.com?fpr=12vqj\n\n", "width": 300}, "type": "n8n-nodes-base.stickyNote", "position": [-1000, 280], "typeVersion": 1, "id": "fbd0cc3f-caf9-42df-b642-c429dd31fadb", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "242ce5c0-51c1-48b9-bdb1-d08664a716d0", "meta": {"instanceId": "b6d0384ceaa512c62c6ed3d552d6788e2c507d509518a50872d7cdc005f831f6"}, "id": "rA7bxt3YmpHUIKVG", "tags": []}