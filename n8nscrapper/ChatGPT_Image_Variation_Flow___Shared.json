{"name": "ChatGPT Image Variation Flow - Shared", "nodes": [{"parameters": {"formTitle": "Ad Image Generator", "formDescription": "Provide brand and product info, and an optional reference image to generate image variations with AI.", "formFields": {"values": [{"fieldLabel": "Brand Name", "requiredField": true}, {"fieldLabel": "Brand Website", "requiredField": true}, {"fieldLabel": "Ad Image", "fieldType": "file", "multipleFiles": false, "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [2780, 20], "id": "773e2254-4858-4d49-956f-845c8bd6294e", "name": "Brand Image Generator Form Submission1", "webhookId": "da177997-c71a-4127-816e-d0e3a442b94a"}, {"parameters": {"promptType": "define", "text": "=Brand Name: {{ $('Brand Image Generator Form Submission1').item.json['Brand Name'] }}\nWebsite: {{ $('Brand Image Generator Form Submission1').item.json['Brand Website'] }}\nReference Ad Description: {{ $('OpenAI2').item.json.content }}\nVisual Style Overview: {{ $json.message.content }}", "hasOutputParser": true, "options": {"systemMessage": "=Brand Name: {{ $('Brand Image Generator Form Submission1').item.json['Brand Name'] }}\nWebsite: {{ $('Brand Image Generator Form Submission1').item.json['Brand Website'] }}\nReference Ad Description: {{ $('OpenAI2').item.json.content }}\nVisual Style Overview: {{ $json.message.content }}\n\nYour task is to generate **10 tightly related visual variations** of a reference ad image — not new concepts. These prompts are for testing subtle creative changes on Facebook/Instagram ads (e.g., backdrop, mood, color, lighting) while preserving the original subject and composition.\n\n### Reference Materials:\n\n**Brand Info**: {{ $('Brand Image Generator Form Submission1').item.json['Brand Name'] }}\n**Website**:  {{ $('Brand Image Generator Form Submission1').item.json['Brand Website'] }}\n**Visual Aesthetics Guideline**: {{ $json.message.content }}\n**Reference Image Description**: {{ $('OpenAI2').item.json.content }}\n\nUse the Reference Image Description to ground your concepts in visual reality — the new prompts should **feel like believable variations** of this original ad image in terms of camera angle, lighting, and context, while introducing fresh concepts or creative twists.\n\n### For each of the 10 prompts:\n\n- **Preserve** the reference image's subject (product, camera angle, core framing)\n- **Only vary** the background, environment, mood, lighting, or color treatment\n- Examples of variation types:\n  - Daylight vs sunset lighting\n  - Poolside vs marble countertop\n  - Lavender tones vs beach sand tones\n  - Summer vibe vs spa-like calm\n- Use vivid, sensory language to describe each variation\n- Always include **aspect ratio** (1:1 or 4:5)\n- Never introduce logos, overlays, or major subject changes\n\nThe goal is to create **subtle, performance-testable image variations**, not entirely new compositions.\n\nPlease return **only** a JSON array of 10 objects, each with a single property `\"prompt\"` containing the image prompt. Example output structure:\n\n[\n  {\n    \"prompt\": \"Sun-drenched poolside shot of the product on a marble ledge at golden hour, with soft shadows and warm tones. Aspect ratio 1:1.\"\n  },\n  {\n    \"prompt\": \"Cool lavender-tinted sunset beach backdrop behind the product, highlighting reflective metallic accents. Aspect ratio 4:5.\"\n  },\n  {\n    \"prompt\": \"...\"\n  }\n]"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [4560, 20], "id": "e1f4d0e4-da60-4604-a7c6-38e417464215", "name": "AI Agent1"}, {"parameters": {"inputDataFieldName": "Ad_Image", "name": "={{ $json['Ad Image'].filename }} (Original)", "driveId": {"__rl": true, "value": "My Drive", "mode": "list", "cachedResultName": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive"}, "folderId": {"__rl": true, "value": "1R5bTxrKmi9NDMFJIh3aQgbNuZwmCybLV", "mode": "list", "cachedResultName": "n8n Testing", "cachedResultUrl": "https://drive.google.com/drive/folders/1R5bTxrKmi9NDMFJIh3aQgbNuZwmCybLV"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [3160, 20], "id": "cfc4698c-0670-493e-b68a-98b8df81afa5", "name": "Google Drive13", "credentials": {"googleDriveOAuth2Api": {"id": "F0paTzO7SuCiPOHb", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Google Drive13').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [5200, 20], "id": "ffd50451-a1a2-487d-b570-d108e5c85cb9", "name": "Google Drive14", "credentials": {"googleDriveOAuth2Api": {"id": "F0paTzO7SuCiPOHb", "name": "Google Drive account"}}}, {"parameters": {"content": "## Form Trigger", "height": 280, "width": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2680, -80], "id": "79fd6d9a-697b-4c12-9efe-606b2c633bb9", "name": "Sticky Note24"}, {"parameters": {"content": "## Upload Reference Ad", "height": 280, "width": 280}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3080, -80], "id": "f307db59-ac5f-472b-b3f9-778b50a77841", "name": "Sticky Note25"}, {"parameters": {"content": "## Get Branding Data", "height": 280, "width": 360, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4100, -80], "id": "ec35d37c-f40d-40a4-b235-82e20f2fef00", "name": "<PERSON><PERSON> Note26"}, {"parameters": {"content": "## Download Reference Ad", "height": 280, "width": 260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5120, -80], "id": "fa6a6909-f2e5-493b-9aac-7042ce37f3b1", "name": "Sticky Note27"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "text": "Describe the visual style, subject matter, and composition of this image. Is it a lifestyle image, a product-only shot, or a combination? Include lighting style and camera angle if possible.", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [3880, 20], "id": "e28898e2-9b6f-4636-9cae-e4423a1b1417", "name": "OpenAI2", "credentials": {"openAiApi": {"id": "oh0Z4G6r5neaRiER", "name": "<PERSON>"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Google Drive13').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [3500, 20], "id": "580b2d2b-89ca-4619-a3c4-77007ceea31b", "name": "Google Drive15", "credentials": {"googleDriveOAuth2Api": {"id": "F0paTzO7SuCiPOHb", "name": "Google Drive account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "=You are a visual brand strategist and art director for direct-to-consumer (DTC) e-commerce brands.\n\nPlease analyze the following brand website. Focus **only** on the brand’s **visual aesthetic**, including:\n\n- Color palette\n- Photography style and lighting\n- Imagery themes (e.g. lifestyle vs. product shots)\n- Mood or tone evoked by visuals\n- Any repeating design elements or layout patterns\n\nBe descriptive but concise. The output will be used to help design consistent and creative AI-generated images for ad creatives — so focus entirely on the visual look and feel.\n\n\nBrand Website: {{ $('Brand Image Generator Form Submission1').item.json['Brand Website'] }}\nBrand Name: {{ $('Brand Image Generator Form Submission1').item.json['Brand Name'] }}\n"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [4160, 20], "id": "a2cc2e20-c179-4d19-a6cc-51607e4ed606", "name": "OpenAI3", "credentials": {"openAiApi": {"id": "oh0Z4G6r5neaRiER", "name": "<PERSON>"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-20250219", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [4540, 200], "id": "ab4a4454-bbff-4ef5-b5a6-12a851640329", "name": "Anthropic Chat Model1", "credentials": {"anthropicApi": {"id": "caeURtm3tne7cj3i", "name": "<PERSON>"}}}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/edits", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "image", "inputDataFieldName": "data"}, {"name": "prompt", "value": "={{ $json.prompt }}"}, {"name": "model", "value": "gpt-image-1"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [5800, 20], "id": "d2709b59-8db9-4b66-87be-1fb5402bd366", "name": "HTTP Request10", "credentials": {"httpHeaderAuth": {"id": "Ymr1VPZvGvLCCTw9", "name": "ChatGPT Image Gen"}}}, {"parameters": {"name": "={{ \n  $('Brand Image Generator Form Submission1').item.json['Ad Image'].filename\n    .replace(/\\.[^.]+$/, '') \n}}-variation{{ $runIndex + 1}}.{{ $binary.data.fileExtension }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1R5bTxrKmi9NDMFJIh3aQgbNuZwmCybLV", "mode": "list", "cachedResultName": "n8n Testing", "cachedResultUrl": "https://drive.google.com/drive/folders/1R5bTxrKmi9NDMFJIh3aQgbNuZwmCybLV"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [6380, 20], "id": "e41e71f2-2bf7-4857-be4e-f8c2ed676620", "name": "Google Drive16", "credentials": {"googleDriveOAuth2Api": {"id": "F0paTzO7SuCiPOHb", "name": "Google Drive account"}}}, {"parameters": {"content": "## Image Generation", "height": 280, "width": 480, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5720, -80], "id": "a8f35261-1677-4e53-9535-a1c74808c896", "name": "Sticky Note28"}, {"parameters": {"content": "## Upload Edited Image", "height": 280, "width": 260}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [6300, -80], "id": "2d157c54-1cc1-43d9-919f-e1dd72837138", "name": "Sticky Note29"}, {"parameters": {"content": "## Download Reference Ad", "height": 280, "width": 280}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3400, -80], "id": "11bcb098-d239-45ac-8194-038a6725f05c", "name": "Sticky Note30"}, {"parameters": {"content": "## Get Image Description", "height": 280, "width": 280, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3780, -80], "id": "b0c15c2d-aa92-4873-9cb0-fbbb32ca664d", "name": "Sticky Note31"}, {"parameters": {"content": "## Generate variations", "height": 440, "width": 360, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4500, -80], "id": "26aa4dd8-4481-4acf-b58e-1ef244a7bd01", "name": "Sticky Note32"}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"prompt\": \"Sun-drenched poolside shot of the product on a marble ledge at golden hour, with soft shadows and warm tones. Aspect ratio 1:1.\"\n  },\n  {\n    \"prompt\": \"Cool lavender-tinted sunset beach backdrop behind the product, highlighting reflective metallic accents. Aspect ratio 4:5.\"\n  },\n  {\n    \"prompt\": \"...\"\n  }\n]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [4740, 200], "id": "53222f0a-0f73-4446-bcd0-e34998bac6ab", "name": "Structured Output Parser"}, {"parameters": {"fieldToSplitOut": "output", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [4940, 20], "id": "74c685a9-9822-426f-b2cc-7f86fdea4915", "name": "Split Out"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [5500, 20], "id": "2906d31a-3e7e-4b58-b4a6-53721efefb55", "name": "Loop Over Items"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [6020, 20], "id": "3997468c-e406-4cf0-a082-3481ffeba6bf", "name": "Convert to File10"}], "pinData": {}, "connections": {"Brand Image Generator Form Submission1": {"main": [[{"node": "Google Drive13", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Google Drive13": {"main": [[{"node": "Google Drive15", "type": "main", "index": 0}]]}, "OpenAI2": {"main": [[{"node": "OpenAI3", "type": "main", "index": 0}]]}, "Google Drive15": {"main": [[{"node": "OpenAI2", "type": "main", "index": 0}]]}, "OpenAI3": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Anthropic Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "HTTP Request10": {"main": [[{"node": "Convert to File10", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent1", "type": "ai_outputParser", "index": 0}]]}, "Split Out": {"main": [[{"node": "Google Drive14", "type": "main", "index": 0}]]}, "Google Drive14": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "HTTP Request10", "type": "main", "index": 0}]]}, "Google Drive16": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Convert to File10": {"main": [[{"node": "Google Drive16", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a8dd6885-520c-4aa4-b672-dabbbd1655e7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e0f3afdfa8cdf759f3628c3983ef08ff7c153a90839a4c34ddd866854a12653a"}, "id": "KlZ6mfqqUkMa8C2w", "tags": []}