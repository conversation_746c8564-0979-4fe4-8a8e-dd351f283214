{"name": "POV Video Super Team Agent", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-420, -900], "id": "7362255a-bb0b-4788-9550-0cac1b05360a", "name": "Schedule Trigger"}, {"parameters": {"jsonSchemaExample": "{\n  \"Name\": \"<Insert catchy title>\",\n  \"Concept\": \"<Insert detailed scenario>\",\n  \"Theme\": \"<Insert theme>\",\n  \"Tone/Mood\": \"<Insert tone/mood>\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [60, -520], "id": "e5f0a70f-c183-496d-87c8-70611d76ae87", "name": "Structured Output Parser"}, {"parameters": {"promptType": "define", "text": "Please generate a new, unique POV content idea following the instructions provided and return in JSON format. \n", "hasOutputParser": true, "options": {"systemMessage": "=# Role\nYou are a POV Daily Idea Agent responsible for generating fresh, highly engaging, and viral POV content ideas for social media. Your ideas should capture attention by exploring unconventional, imaginative perspectives with vivid, provocative language.\n\n# Task\n1. **Fetch Existing Ideas:** Use the searchAirtable tool to retrieve the current ideas and ensure that your generated idea is completely unique and does not duplicate any of the existing entries.\n2. **Generate a New Idea:** Create a new POV idea that leverages diverse contexts and creative twists. Think outside the box—consider scenarios like \"POV of a poor man in Egypt,\" \"POV of <PERSON><PERSON>’s son,\" or other unexpected perspectives. Your idea should combine elements of pop culture, shock value, and emotionally engaging storytelling.\n3. **Output in JSON Format:** Return your idea in the exact JSON format below:\n\n{\n  \"Name\": \"<Insert catchy title>\",\n  \"Concept\": \"<Insert detailed scenario>\",\n  \"Theme\": \"<Insert theme>\",\n  \"Tone/Mood\": \"<Insert tone/mood>\"\n}\n\n# Examples\n\n**Example 1:**\n- **Name:** \"Dawn of the Desert Nomad\"\n- **Concept:** \"Experience the raw, unfiltered life of a young nomad in Egypt, capturing the struggles and beauty of surviving the harsh desert while embracing unexpected moments of kindness and wonder.\"\n- **Theme:** \"Resilience & Discovery\"\n- **Tone/Mood:** \"Gritty, Inspiring, Mysterious\"\n\n**Example 2:**\n- **Name:** \"The Billionaire's Rebellion\"\n- **Concept:** \"Step into the shoes of Elon Musk’s son as he navigates the pressures of immense wealth and societal expectations, challenging the status quo with bold defiance and surprising acts of compassion.\"\n- **Theme:** \"Contrasts & Rebellion\"\n- **Tone/Mood:** \"Bold, Provocative, Intriguing\"\n\n**Example 3:**\n- **Name:** \"Midnight City Dreams\"\n- **Concept:** \"Dive into the nocturnal journey of an urban wanderer experiencing the hidden, surreal side of a bustling metropolis—a mix of loneliness, hope, and the magic of unexpected encounters.\"\n- **Theme:** \"Urban Mystique & Hope\"\n- **Tone/Mood:** \"Ethereal, Melancholic, Enigmatic\""}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-200, -900], "id": "f9a6a9e8-ed85-49d9-845c-f3326d0cadbf", "name": "POV Daily Idea Agent"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "=appJ3fDAoRBETWkA2", "mode": "id"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "=", "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [-120, -500], "id": "0dfc69ed-e601-4995-ac86-09bdef56f8e7", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"model": "qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-300, -500], "id": "25a7e759-34a9-4296-acc6-3606afeefb0b", "name": "<PERSON><PERSON>", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ba80200a-0997-47dd-a71b-6d565c09cc0a", "leftValue": "={{ $json.data.approved }}", "rightValue": "false", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [280, -900], "id": "e7c74913-1f7f-4e48-b547-616453d020cb", "name": "If"}, {"parameters": {"chatId": "116486546", "text": "=😥 Oof! That one didn’t hit the mark, huh?  \n\nNo worries boss — brewing up a brand new idea for you as we speak... 🔄✨", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [540, -760], "id": "25dd4958-ee73-42c6-98d9-1838567b5c4b", "name": "Declined", "webhookId": "6a410eaf-bb2a-4707-a410-06b2e084f58f", "credentials": {"telegramApi": {"id": "WEvCeN3tuBOZpG0G", "name": "<PERSON>le <PERSON>"}}}, {"parameters": {"chatId": "116486546", "text": "=🔥 You’ve got *legendary* taste, boss!   \n\nYour POV video is locked in — this one’s gonna be a banger! 🎥💥   \n\nStay tuned, magic incoming... ✨🚀", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [460, -1040], "id": "f41de06e-e304-4c74-b8cf-85d8f8cf33cb", "name": "Approved", "webhookId": "1f51e708-1e48-496b-8a1a-644a574806e0", "credentials": {"telegramApi": {"id": "WEvCeN3tuBOZpG0G", "name": "<PERSON>le <PERSON>"}}}, {"parameters": {"operation": "sendAndWait", "chatId": "116486546", "message": "=🎬 Hey boss! Here's your fresh POV video idea for the day — hit me with your thoughts! 💭🔥\n\n🧠 *Title:* \n{{ $json.output.Name }} \n\n💡 *Concept:*  \n{{ $json.output.Concept }}\n\n🎭 *Theme:*\n{{ $json.output.Theme }} \n\n🎨 *Tone:*\n{{ $json.output['Tone/Mood'] }}  ", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [120, -900], "id": "f5203cae-75f9-4acc-a613-4ed5489681af", "name": "Human Approval", "webhookId": "cbc5cf98-3f59-4f11-9718-4e06fac4197b", "credentials": {"telegramApi": {"id": "WEvCeN3tuBOZpG0G", "name": "<PERSON>le <PERSON>"}}}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [320, -760], "id": "63c212d0-3004-4477-b4ca-674c466a0302", "name": "Wait", "webhookId": "06b8856b-ba5e-4495-9356-eb4425a59c7a"}, {"parameters": {"content": "## 🎥  POV Idea Agent  \n**This agent generates creative, high-concept **POV video ideas** tailored for daily inspiration.**\n\nEach run produces a unique idea with:\n- 🔤 A compelling **Title**\n- 💡 A rich **Concept**\n- 🎭 A thoughtful **Theme**\n- 🎨 A distinct **Tone/Mood**\n\nThe idea is then sent to Telegram for approval:\n- ✅ If approved: it’s saved to Airtable.\n- ❌ If declined: a new idea is automatically generated.\n\n**Perfect for creators who want a steady stream of storytelling ideas without the burnout. 🚀**\n", "height": 980, "width": 1400, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-520, -1280], "id": "02841aef-2660-4662-ad7a-1c1734f316e9", "name": "<PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "=Here is the POV idea:\n\n{\n  \"Name\": \"{{ $json.Name }}\"\"\n  \"Concept\": \"{{ $json['Concept (Detailed Scenario)'] }}\"\n  \"Theme\": \"{{ $json.Theme }}\"  \n  \"Tone/Mood\": \"{{ $json['Tone/Mood'] }}\"\n}\n\nPlease generate a new set of 7 scene descriptions based on this idea, following the instructions provided.", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Role\nYou are a POV Scenes Creation Agent responsible for generating a sequence of 7 distinct scene descriptions for a POV video. Each scene should be concise (suitable for a 5-second clip) and written as a caption that captures the essence of the POV idea provided.\n\n# Task\n1. **Input Idea:** The POV idea is provided from Airtable and includes the following fields:\n   - Name\n   - Concept\n   - Theme\n   - Tone/Mood\n\n2. **Generate Scenes:**  \nBased on the provided idea, create exactly 7 scene descriptions that form a logical narrative:\n   - **Scene 1:** Should be longer, starting with \"POV:\" and set the tone of the video.\n   - **Scenes 2–7:** Must be short captions (less than 7–10 words each) that continue the narrative.\n\n\n```json\n[\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" }\n]\n\n\n#Examples: \n## Example 1: (Based on a historical POV idea titled \"Waking in the Plague\")\n\nProvided Idea:\n1. Name: \"Waking in the Plague\"\n2. Concept: \"Experience a surreal morning in 14th-century London during the height of the Black Plague, where every moment is a fight for survival and unexpected acts of humanity shine through.\"\n3. Theme: \"Desperation & Hope\"\n4. Tone/Mood: \"Gritty, Haunting, Poignant\"\n\nGenerated Scenes:\n1. \"POV: Awakening to eerie silence in a dim, plague-ridden room.\"\n2. \"Stumbling out into a foggy, deserted street.\"\n3. \"Glimpsing shadowy figures moving in the distance.\"\n4. \"Searching desperately for signs of life or medicine.\"\n5. \"A brief encounter with a kind stranger offering help.\"\n6. \"Clutching a precious vial of medicine in trembling hands.\"\n7. \"Fading hope as you retreat into the night.\"\n\n## Example 2: (Based on a futuristic POV idea titled \"The Neon Rebellion\")\n\nProvided Idea:\n1. Name: \"The Neon Rebellion\"\n2. Concept: \"Step into a futuristic cityscape where the underdog fights against a corrupt system. The narrative unfolds through neon-lit alleyways and high-tech hideouts.\"\n3. Theme: \"Rebellion & Resilience\"\n4. Tone/Mood: \"Edgy, Dynamic, Uplifting\"\n\nGenerated Scenes:\n1. \"POV: Awakening in a cramped, neon-lit apartment.\"\n2.\"Sneaking through bustling, rain-soaked streets.\"\n3. \"Dodging surveillance in dark alleys.\"\n4. \"Meeting a mysterious ally in a shadowy corner.\"\n5. \"Brief moment of high-speed escape on futuristic hovercars.\"\n6. \"Overlooking the city from a secret rooftop.\"\n7. \"Steeling yourself for the next act of defiance.\"\n\n## Example 3: (Based on a modern, emotional POV idea titled \"A Day of Hidden Struggles\")\n\nProvided Idea:\n1. Name: \"A Day of Hidden Struggles\"\n2. Concept: \"Capture the intimate moments of someone battling internal challenges while maintaining a facade of normalcy, revealing the unsaid emotions behind everyday actions.\"\n3. Theme: \"Inner Turmoil & Resilience\"\n4. Tone/Mood: \"Subtle, Reflective, Poignant\"\n\nGenerated Scenes:\n1. \"POV: Opening your eyes to a seemingly ordinary morning.\"\n2. \"Staring blankly at a mirror, questioning reality.\"\n3. \"A hesitant step into a busy, indifferent crowd.\"\n4. \"Clutching a small memento that holds deep meaning.\"\n5. \"A fleeting smile masking hidden pain.\"\n6. \"Passing a moment of solitude in a quiet park.\"\n7. \"Closing your eyes as the day fades, with silent determination.\"\n\n#Note\n- \n- Return the output only with the scenes generated in the exact JSON format"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [1620, -1480], "id": "2f7e27f5-2afd-4016-b0cf-669dae2bce0d", "name": "POV Scenes Agent"}, {"parameters": {"fieldToSplitOut": "output", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1960, -1480], "id": "af55552f-7cca-47ef-969b-c01605394420", "name": "Split Out"}, {"parameters": {"batchSize": 7, "options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2180, -1480], "id": "c1d51fcb-aeb3-4729-b6c0-37b25e8a4295", "name": "Loop Over Items"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "columns": {"mappingMode": "defineBelow", "value": {"Scene Number": "={{ $json.scenenumber }}", "Description": "={{ $json.description }}", "POV Main": "={{ [$('Get Video Idea').item.json.id] }}"}, "matchingColumns": [], "schema": [{"id": "Scene Number", "displayName": "Scene Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "POV Main", "displayName": "POV Main", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Name (from POV Main)", "displayName": "Name (from POV Main)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1980, -1320], "id": "4c2de435-dfcc-49d8-93d8-55dc73afb633", "name": "Store Scenes", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"assignments": {"assignments": [{"id": "4b68a2b7-3b3b-4f0f-8a8d-74db96044174", "name": "scenenumber", "value": "={{ $itemIndex+1 }}", "type": "string"}, {"id": "e0203c9f-011b-4fc6-b495-de05894703ec", "name": "description", "value": "={{ $json.scene }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2300, -1320], "id": "13a078c4-4d97-4055-b36a-1ccc7eef225e", "name": "<PERSON>"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e3cff8bf-f13a-4124-ae26-f872e0757e0a", "leftValue": "={{ $json.Status }}", "rightValue": "Pending", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1420, -1460], "id": "764449e8-013e-4252-a578-1009ad8bbd78", "name": "If1"}, {"parameters": {"jsonSchemaExample": "[\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" },\n  { \"scene\": \"\" }\n]\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1760, -1260], "id": "4a2c064e-4fb7-4512-a804-a541acee6e97", "name": "Structured Output Parser1"}, {"parameters": {"content": "## POV Scenes Agent \n", "height": 500, "width": 1700, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [920, -1580], "id": "5c07ee54-d0e4-4b1f-9234-a07076a66834", "name": "Sticky Note1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5cfd004a-1108-4b51-a177-ea2204320ca8", "leftValue": "={{ $json.Status }}", "rightValue": "Scenes Generated", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1580, -720], "id": "cebeeeba-cfcd-4bcc-98c8-fe3864bf0741", "name": "If2"}, {"parameters": {"jsonSchemaExample": "{\n  \"prompt\": \"<insert detailed image prompt here>\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2280, -500], "id": "3b63e2f8-3ced-4cca-8bb1-0bde9da312d7", "name": "Structured Output Parser2"}, {"parameters": {"content": "## POV Image Agent\n", "height": 720, "width": 2040}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [920, -1020], "id": "0fc4ac6f-0686-4bca-be5d-056ea7429dc7", "name": "Sticky Note2"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "Status = \"Pending\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1220, -1460], "id": "e485a9c8-ed52-4282-b241-64a5d67dfe84", "name": "Get Video Idea", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "Status = \"Scenes Generated\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1240, -740], "id": "aab39767-a115-4036-86c4-1294d4592ed4", "name": "Get Scenes", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1460, -960], "id": "d2246e08-80d1-43cb-ab6b-61f614f567c4", "name": "Get Scenes Data", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"fieldToSplitOut": "['POV Scenes']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1640, -960], "id": "836e8699-8065-454f-8572-5f4604c05b8a", "name": "Split Scenes"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1860, -960], "id": "a5daac4b-d401-45a7-a939-1b3d1a17c1be", "name": "Loop Scenes"}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json['[\\'POV Scenes\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1900, -720], "id": "7051dd16-b8ad-4c3f-9fa6-b055dcf64c30", "name": "Retrieve Scenes", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"model": "=qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [2060, -500], "id": "a0f0b3c2-cee9-457d-8dfd-d18ea9f2ae00", "name": "<PERSON><PERSON>  ", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"model": "qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1540, -1260], "id": "11fa0f8e-a28d-4bc2-86df-dedd8a6a6ac2", "name": "Qwen1", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"method": "POST", "url": "https://api.replicate.com/v1/models/black-forest-labs/flux-1.1-pro/predictions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Prefer", "value": "wait"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"input\": {\n    \"prompt\": \"{{ $json.output.prompt }}\",\n    \"prompt_upsampling\": true,\n    \"aspect_ratio\": \"9:16\"\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2420, -740], "id": "f6491764-13aa-4b4f-9136-2555e45907d8", "name": "Generate Images", "credentials": {"httpHeaderAuth": {"id": "xlhyRcOYSjjkmrtL", "name": "Replicate"}}}, {"parameters": {"url": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2600, -740], "id": "5e05b6aa-f22f-4d8d-aca0-1ebdf088aeac", "name": "Download Images"}, {"parameters": {"name": "={{ $('Get Scenes').item.json.Name }}_{{ $('Retrieve Scenes').item.json['Scene Number'] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1dKT0N587hztFdE_KhaCQZnZphD1i0ljF", "mode": "list", "cachedResultName": "1. POV Images", "cachedResultUrl": "https://drive.google.com/drive/folders/1dKT0N587hztFdE_KhaCQZnZphD1i0ljF"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2780, -740], "id": "9f88d3b7-d4b3-4fae-b25e-3ae9c8fd28d0", "name": "Upload Scene Images", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsT61yq58HsS2ZM", "mode": "list", "cachedResultName": "POV Images", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsT61yq58HsS2ZM"}, "columns": {"mappingMode": "defineBelow", "value": {"Image Prompt": "={{ $('Download Images').item.json.input.prompt }}", "Image URL": "={{ $json.webContentLink }}", "POV Scenes": "={{ [$('Loop Scenes').item.json['[\\'POV Scenes\\']']] }}", "POV Main": "={{[ $('Get Scenes').item.json['record id']] }}"}, "matchingColumns": [], "schema": [{"id": "Scene Number", "displayName": "Scene Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Image Prompt", "displayName": "Image Prompt", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Image URL", "displayName": "Image URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "POV Main", "displayName": "POV Main", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2680, -520], "id": "3e626577-fdd4-4944-ae51-f608da2d1841", "name": "Update Image Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.fields['POV Main'][0] }}", "Status": "Scenes Generated"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Music", "displayName": "POV Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Voiceovers", "displayName": "POV Voiceovers", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated ", "value": "Videos Generated "}, {"name": "Music Generated", "value": "Music Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Voicevers Generated", "value": "Voicevers Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2400, -1500], "id": "07d3c62f-3e44-4c02-a9b9-5d02bec0f8ff", "name": "Update Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "=appJ3fDAoRBETWkA2", "mode": "id"}, "table": {"__rl": true, "value": "=tblsK6gpL3sX3Xk3I", "mode": "id"}, "columns": {"mappingMode": "defineBelow", "value": {"Name": "={{ $('POV Daily Idea Agent').item.json.output.Name }}", "Concept (Detailed Scenario)": "={{ $('POV Daily Idea Agent').item.json.output.Concept }}", "Theme": "={{ $('POV Daily Idea Agent').item.json.output.Theme }}", "Tone/Mood": "={{ $('POV Daily Idea Agent').item.json.output['Tone/Mood'] }}", "Status": "Pending"}, "matchingColumns": [], "schema": [{"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [660, -1040], "id": "9f147131-cc0c-49db-9a99-e66a9711c212", "name": "Update Airtable1", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"httpMethod": "POST", "path": "48361d7a-381e-4a89-84c0-a9834bbf685e", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1040, -740], "id": "225266c5-db21-4b8b-a341-54bd0a45d340", "name": "generateImages", "webhookId": "48361d7a-381e-4a89-84c0-a9834bbf685e"}, {"parameters": {"httpMethod": "POST", "path": "795195d4-fb9a-4911-a20e-5e0cb21bfd54", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1020, -1460], "id": "a262860c-ac8a-4f5b-9d92-0ae62d3107d8", "name": "generateScenes", "webhookId": "795195d4-fb9a-4911-a20e-5e0cb21bfd54"}, {"parameters": {"promptType": "define", "text": "=Here is the POV scene:\n\nScene Description: {{ $json['Image Prompt'] }}\nTheme: {{ $('Get Image Url').item.json.Theme }}\nTone/Mood: {{ $('Get Image Url').item.json['Tone/Mood'] }}\n\nPlease generate an video prompt to animate this specific scene to life.", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Role\n\nYou are a Video Prompt Creation Agent specialized in generating first-person (POV) video prompts for an AI video generator.\n\n\n# Task\nUsing the provided input, craft a concise prompt (under 50 words) that instructs the AI to produce a video with a clear first-person perspective. Your prompt must:\n\n1. Emphasize First-Person POV: Clearly state that the scene is viewed from a first-person perspective (e.g., “First-person view of…” or “POV of…”).\n\n2. Indicate Visible Body Parts: When relevant, specify that hands, arms, or feet should be visible to enhance the POV effect.\n\n3. Include Scene Details: Mention key elements such as lighting, atmosphere, environment, and objects.\n\n4. Reflect Theme & Tone: Integrate the provided theme and tone/mood to ensure the video evokes the desired emotions.\n\n5. Maintain Visual Style: Specify a consistent style (e.g., cinematic, documentary, stylized) that matches the theme or setting.\n\n\n\n# Input\n\nYou will receive:\n\n- **Scene Description:** A POV caption describing the scene.  \n- **Theme:** The underlying theme.  \n- **Tone/Mood:** The intended emotional tone.\n\n# Output\n\nReturn your generated prompt in the following JSON format:\n```json\n{\n  \"prompt\": \"<insert detailed image prompt here>\"\n}\n\n\n#Example\n##Example 1\nInput: \nScene Description: \"First-person view of entering a dim, dust-filled room filled with ancient archives. A hand reaches out, fingers brushing old, dusty scrolls, surrounded by a mystical, thought-provoking, and haunting atmosphere.\"\nTheme: \"Mystery & Legacy\"\nTone/Mood: \"Mystical, Thought-Provoking, Haunting\"\n\nOutput:\n{\n  \"prompt\": \"POV entering a dusty archive. Flickering light illuminates swirling dust as a hand carefully unrolls an ancient scroll, evoking a haunting, mystical sense of mystery and legacy.\"\n}\n"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [680, 80], "id": "19e9ff79-2dda-4799-a745-d99c2325f887", "name": "Video Prompt Generator"}, {"parameters": {"method": "POST", "url": "https://api.dev.runwayml.com/v1/image_to_video", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $('Get Image For Each Scene').item.json['Image URL'] }}"}, {"name": "promptText", "value": "={{ $json.output.prompt }}"}, {"name": "model", "value": "gen3a_turbo"}, {"name": "ratio", "value": "768:1280"}, {"name": "duration", "value": "5"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1000, 80], "id": "3c243036-3f65-4472-ac7f-be757ac6531f", "name": "Generate Video", "credentials": {"httpHeaderAuth": {"id": "jRDusgPWYlKZodhd", "name": "Runway"}}}, {"parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 80], "id": "ac6904e8-a5af-469d-9b66-424a73973a2a", "name": "Get Video", "credentials": {"httpHeaderAuth": {"id": "jRDusgPWYlKZodhd", "name": "Runway"}}}, {"parameters": {"url": "={{ $json.output[0] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 80], "id": "7e7d7657-de51-4a86-83e7-6d9246cd85e8", "name": "Download Video"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "Status = \"Image Generated\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-280, 20], "id": "1307a108-64f0-45b5-b32f-daa9c2ff6324", "name": "Get Image", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsT61yq58HsS2ZM", "mode": "list", "cachedResultName": "POV Images", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsT61yq58HsS2ZM"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [40, -140], "id": "15a57e55-cb61-4eb2-9440-a5c9b6fd7f2e", "name": "Get Image Url", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"fieldToSplitOut": "['POV Images']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [220, -140], "id": "f8955d23-1d99-4c83-8eeb-c2e4b3b202e3", "name": "Split Image"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [440, -140], "id": "6e4359d7-b718-41f0-8bf6-f3d350cdb10e", "name": "Loop Image"}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsT61yq58HsS2ZM", "mode": "list", "cachedResultName": "POV Images", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsT61yq58HsS2ZM"}, "id": "={{ $json['[\\'POV Images\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [480, 80], "id": "928083ef-0535-4a8b-837d-1fde48075ad0", "name": "Get Image For Each Scene", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"model": "=qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [620, 320], "id": "2568ed8a-ed54-454e-a74f-1798c3ea17a8", "name": "<PERSON><PERSON> ", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"name": "={{ $('Get Image').item.json.Name }}_{{ $('Get Image For Each Scene').item.json['Scene Number'] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1s1wunylEah6ti8HFeH2H0o0i0iHy_hwX", "mode": "list", "cachedResultName": "2. POV Video", "cachedResultUrl": "https://drive.google.com/drive/folders/1s1wunylEah6ti8HFeH2H0o0i0iHy_hwX"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1860, 80], "id": "7a5c5831-b59b-4f4a-8086-2f0287b0c3c7", "name": "Upload Video1", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblfMydfHVH18UHdN", "mode": "list", "cachedResultName": "POV Videos", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblfMydfHVH18UHdN"}, "columns": {"mappingMode": "defineBelow", "value": {"Video Prompt": "={{ $('Video Prompt Generator').item.json.output.prompt }}", "Video URL": "={{ $json.webContentLink }}", "POV Main": "={{[ $('Get Image').item.json.id] }}", "POV Images": "={{[ $('Loop Image').item.json['[\\'POV Images\\']'] ]}}"}, "matchingColumns": [], "schema": [{"id": "Scene Number", "displayName": "Scene Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Video Prompt", "displayName": "Video Prompt", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Video URL", "displayName": "Video URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "POV Main", "displayName": "POV Main", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1560, 320], "id": "19ea9dcc-48f3-49c7-98c1-0d867b014938", "name": "Upload Video Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "Videos Generated", "id": "={{ $('Upload Video Airtable').item.json.fields['POV Main'][0] }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [660, -140], "id": "aa3bcc36-13e5-4440-b39a-a8ed4abb7b54", "name": "Update Airtable 3", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5cfd004a-1108-4b51-a177-ea2204320ca8", "leftValue": "={{ $json.Status }}", "rightValue": "Image Generated", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-120, 20], "id": "23e983c6-488b-4755-8649-535ee52b309c", "name": "If3"}, {"parameters": {"jsonSchemaExample": "{\n  \"prompt\": \"<insert detailed video prompt here>\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [820, 320], "id": "12742d5b-aa05-4928-a357-6adedb016a24", "name": "Structured Output Parser3"}, {"parameters": {"content": "## POV Video Agent\n", "height": 880, "width": 2580, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-520, -240], "id": "759295b7-1d9e-4810-b610-2bc6becf5ca4", "name": "Sticky Note3"}, {"parameters": {"amount": 60}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1180, 80], "id": "8b8d631e-cd3d-4ef9-98b1-6f957bb1644d", "name": "Wait1", "webhookId": "13f36db0-9e02-4d80-9ad3-fe3dfd8a7612"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Get Scenes Data').item.json.id }}", "Status": "Image Generated"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2200, -980], "id": "24db0763-4442-4b4d-8045-347ae9d407e1", "name": "Update Airtable2", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"promptType": "define", "text": "=Here is the POV scene:\n\nScene Description: {{ $json.Description }}\nTheme: {{ $('Get Scenes 2').item.json.Theme }}\nTone/Mood: {{ $('Get Scenes 2').item.json['Tone/Mood'] }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Role  \nYou are an **SFX Sound Effect Agent** specialized in generating immersive, cinematic soundscapes. Your job is to transform scene descriptions into vivid, under-25-word sound effect prompts that evoke the emotion, setting, and atmosphere of the scene.\n\n# Task  \nUsing the provided inputs, generate a short, sensory-rich SFX prompt made up of environmental, ambient, or character-driven sounds. The result should bring the scene to life and reflect its tone and theme through audio cues. The prompt must be concise, descriptive, and formatted as a comma-separated list.\n\n# Input  \nYou will receive:  \n- **Scene Description:** A caption describing the POV scene and its key auditory cues.  \n- **Theme:** The underlying theme.  \n- **Tone/Mood:** The intended emotional tone.\n\n# Output  \nReturn your generated prompt in the following JSON format:\n```json\n{\n  \"prompt\": \"<insert detailed SFX prompt here>\"\n}\n```\n\n# Example  \n\n## Example 1  \n**Input:**  \nScene Description: POV: Entering a dimly lit, dust-filled room filled with ancient archives.  \nTheme: Mystery & Legacy  \nTone/Mood: Mystical, Thought-Provoking, Haunting  \n\n**Output:**  \n```json\n{\n  \"prompt\": \"creaking door, soft footsteps, distant dripping, rustling paper, faint whisper of wind\"\n}\n```\n\n## Example 2  \n**Input:**  \nScene Description: POV: Running through a neon-lit alley during a sudden downpour, chased by unknown figures.  \nTheme: Escape & Futurism  \nTone/Mood: Intense, Gritty, Urgent  \n\n**Output:**  \n```json\n{\n  \"prompt\": \"pounding rain, hurried footsteps, splashing puddles, distant sirens, heavy breathing, electric hum\"\n}\n```\n\n## Example 3  \n**Input:**  \nScene Description: POV: Sitting by a peaceful lake at sunrise, watching mist roll over the water.  \nTheme: Reflection & Tranquility  \nTone/Mood: Calm, Serene, Introspective  \n\n**Output:**  \n```json\n{\n  \"prompt\": \"gentle water lapping, birdsong, breeze in trees, distant loon call, soft rustle of reeds\"\n}\n```\n\n# Notes  \n- Keep the total output under 25 words.  \n- Use evocative and sensory-specific sound descriptions.  \n- Focus on ambient, environmental, and character-related audio cues.  \n- Do not include dialogue or lyrics.  \n- Format all outputs in lowercase, comma-separated strings inside a JSON object.  \n- Ensure the mood, theme, and setting are reflected clearly in the sound choices.  \n- One scene = one output.  \n"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [3240, 160], "id": "cb1ea7cc-4d40-4b37-a568-ba1a2f933c0a", "name": "SFX Prompt Generator"}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2600, -60], "id": "e5d3d496-2c50-414d-8bdc-b43666fb10ac", "name": "Get Scenes 2", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "Status = \"Videos Generated\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2420, 200], "id": "313426f4-7db5-4dc1-aa99-0b91456399b6", "name": "Check Video Status", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [3000, -60], "id": "4d535cce-4dea-4bec-b980-21b6044b6caf", "name": "Loop Over Scenes"}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json['[\\'POV Scenes\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [3040, 160], "id": "efe187d6-fefe-4b92-a0c5-e778ec298f8f", "name": "Get Scenes For SFX", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/sound-generation", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.output.prompt }}"}, {"name": "duration_seconds", "value": "5"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3660, 160], "id": "0dfe6c88-158c-4219-8506-42182a0811f5", "name": "Generate SFX", "credentials": {"httpHeaderAuth": {"id": "ve0EEdVJ5xWTT2KM", "name": "Elevenlabs"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblbabp38S8Q0t93T", "mode": "list", "cachedResultName": "POV SFX", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblbabp38S8Q0t93T"}, "columns": {"mappingMode": "defineBelow", "value": {"SFX Prompt": "={{ $('Generate SFX').item.json.output.prompt }}", "URL": "={{ $json.webContentLink }}", "POV Main": "={{ [ $('Check Video Status').item.json.id ]}}", "POV Scenes": "={{ [$('Loop Over Scenes').item.json['[\\'POV Scenes\\']']] }}"}, "matchingColumns": [], "schema": [{"id": "Scene Number", "displayName": "Scene Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Music Prompt", "displayName": "Music Prompt", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "SFX Prompt", "displayName": "SFX Prompt", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "POV Main", "displayName": "POV Main", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [4120, 400], "id": "03a3e797-f3f7-44b8-9e1a-e2ed5210cc7b", "name": "Upload SFX Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"name": "={{ $('Check Video Status').item.json.Name }}_{{ $('Get Scenes For SFX').item.json['Scene Number'] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1y4kHZaLCHjALVIO96h3-sJoVZKFgwOUV", "mode": "list", "cachedResultName": "4. POV SFX", "cachedResultUrl": "https://drive.google.com/drive/folders/1y4kHZaLCHjALVIO96h3-sJoVZKFgwOUV"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [3880, 160], "id": "c0af19ab-05da-481a-a801-9e6caba17b45", "name": "Uploade SFX", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "SFX Generated", "id": "={{ $('Check Video Status').item.json.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [3260, -60], "id": "cb0146ea-df87-4599-983a-691046155753", "name": "Update Airtable 4", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5cfd004a-1108-4b51-a177-ea2204320ca8", "leftValue": "={{ $json.Status }}", "rightValue": "Videos Generated", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2700, 220], "id": "cc04428f-acc2-456b-a2b5-b904464d4b41", "name": "If4"}, {"parameters": {"jsonSchemaExample": "{\n  \"prompt\": \"<insert detailed SFX prompt here>\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [3380, 400], "id": "bb5e1155-a73b-4c6c-a60f-76e082789a4a", "name": "Structured Output Parser4"}, {"parameters": {"fieldToSplitOut": "['POV Scenes']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2780, -60], "id": "b1d8904f-6744-4515-a1b3-ce663ba32404", "name": "Split Scenes1"}, {"parameters": {"model": "=qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [3180, 400], "id": "0d48ec19-62f7-4507-ae76-8c70048b03ca", "name": "Qwen2", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"content": "## SFX Agent", "height": 760, "width": 2300, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2100, -120], "id": "080faf47-7d6e-40a8-90b0-9c9a5a0119ee", "name": "Sticky Note4"}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblbabp38S8Q0t93T", "mode": "list", "cachedResultName": "POV SFX", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblbabp38S8Q0t93T"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [120, 1200], "id": "763c9822-56e8-48cf-9868-81c1234e0628", "name": "Get SFX ", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblfMydfHVH18UHdN", "mode": "list", "cachedResultName": "POV Videos", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblfMydfHVH18UHdN"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [120, 1000], "id": "b2c5f346-ffc3-4447-b18a-3c76e27ea060", "name": "Get Videos", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [120, 800], "id": "34d7c370-3b4f-4728-a542-e9cec6a2a0ae", "name": "Get Scenes Captions", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json['[\\'POV Scenes\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [520, 800], "id": "ff1991dc-525f-4dbf-9972-074ffbefeabd", "name": "Get Record", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblbabp38S8Q0t93T", "mode": "list", "cachedResultName": "POV SFX", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblbabp38S8Q0t93T"}, "id": "={{ $json['[\\'POV SFX\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [520, 1200], "id": "e00419da-26ea-43ab-b86d-ce1f51cabda1", "name": "Get SFX", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [840, 1040], "id": "bdd93060-31f9-41cd-a3f9-82f390400d55", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "return [\n  {\n    json: (() => {\n      const output = {};\n     \n      // Grab main record ID from the first item\n      output[\"mainRecordId\"] = items[0].json[\"POV Main\"][0];\n      \n      items.forEach((item, index) => {\n        const i = index + 1;\n        output[`scene${i}`] = item.json[\"Description\"];\n        output[`sfx${i}`] = item.json[\"URL\"];\n        output[`video${i}`] = item.json[\"Video URL\"];\n      });\n      return output;\n    })()\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 1040], "id": "1eb9d2e9-881b-4dd8-a434-2d9e4cccaf28", "name": "Code"}, {"parameters": {"method": "POST", "url": "https://api.creatomate.com/v1/renders", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"template_id\": \"d62dd01f-276d-4a59-a0c4-ac26ae3b233d\",\n  \"modifications\": {\n    \"sfx-1.source\": \"{{ $json.sfx1 }}\",\n    \"sfx-2.source\": \"{{ $json.sfx2 }}\",\n    \"sfx-3.source\": \"{{ $json.sfx3 }}\",\n    \"sfx-4.source\": \"{{ $json.sfx4 }}\",\n    \"sfx-5.source\": \"{{ $json.sfx5 }}\",\n    \"sfx-6.source\": \"{{ $json.sfx6 }}\",\n    \"sfx-7.source\": \"{{ $json.sfx7 }}\",\n    \"video-1.source\": \"{{ $json.video1 }}\",\n    \"video-2.source\": \"{{ $json.video2 }}\",\n    \"video-3.source\": \"{{ $json.video3 }}\",\n    \"video-4.source\": \"{{ $json.video4 }}\",\n    \"video-5.source\": \"{{ $json.video5 }}\",\n    \"video-6.source\": \"{{ $json.video6 }}\",\n    \"video-6-6VP.source\": \"{{ $json.video7 }}\",\n    \"Text-2D5.text\": \"{{ $json.scene1 }}\",\n    \"Text-M48.text\": \"{{ $json.scene2 }}\",\n    \"Text-MZ7.text\": \"{{ $json.scene3 }}\",\n    \"Text-BLX.text\": \"{{ $json.scene4 }}\",\n    \"Text-N9W.text\": \"{{ $json.scene5 }}\",\n    \"Text-PH9.text\": \"{{ $json.scene6 }}\",\n    \"Text-Z7N.text\": \"{{ $json.scene7 }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1260, 1040], "id": "a53ef812-9e4c-404c-b7c3-2bbe5f116100", "name": "Render Video", "credentials": {"httpHeaderAuth": {"id": "t6PCmpwKxrPqVMjO", "name": "Creatomate"}}}, {"parameters": {"url": "={{ $json.url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1700, 1040], "id": "c5abd469-4a5a-46fe-ad50-0e7e8527c4e3", "name": "HTTP Request"}, {"parameters": {"name": "={{ $('Code').item.json.mainRecordId }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1glYo-6nh02LP3RdAJqHOMXbAGIa5lgBJ", "mode": "list", "cachedResultName": "6. <PERSON><PERSON><PERSON>", "cachedResultUrl": "https://drive.google.com/drive/folders/1glYo-6nh02LP3RdAJqHOMXbAGIa5lgBJ"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1900, 1040], "id": "119d3079-c1aa-4081-a260-b5ac2b140dcb", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Code').item.json.mainRecordId }}", "Status": "Materials Uploaded", "Final Render Video": "={{ $json.webContentLink }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2140, 1040], "id": "d4bfd142-5f94-4ba0-95e6-4be4fcece5a6", "name": "Upload To Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5cfd004a-1108-4b51-a177-ea2204320ca8", "leftValue": "={{ $json.Status }}", "rightValue": "SFX Generated", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-180, 1160], "id": "daaef9e8-c3b3-4642-86fa-711c4def43ae", "name": "If5"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "Status = \"SFX Generated\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-320, 1160], "id": "78effd9d-d0fa-473a-8e24-c376bcd941b4", "name": "Airtable1", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblfMydfHVH18UHdN", "mode": "list", "cachedResultName": "POV Videos", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblfMydfHVH18UHdN"}, "id": "={{ $json['[\\'POV VIdeos\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [520, 1000], "id": "51dd0832-5d80-48b3-a782-3967de1306b7", "name": "Get Video1", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"fieldToSplitOut": "['POV Scenes']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [340, 800], "id": "c384c219-0812-4395-b084-8c4cb207e31d", "name": "Split Out3"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1480, 1040], "id": "3b05c541-0723-48ec-b8d8-0bb4a26715cd", "name": "Wait2", "webhookId": "a1ddfe13-4686-4200-a8ba-90596d7b63a3"}, {"parameters": {"content": "## Render Video Agent", "height": 720, "width": 2900, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-520, 700], "id": "73aab75d-eb46-4bbc-83a1-1c1b6e55d80f", "name": "Sticky Note5"}, {"parameters": {"httpMethod": "POST", "path": "fdca225d-8d35-4a80-bbce-7b1c6014deab", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-400, 940], "id": "4e36cedd-d873-44f6-b820-f9a6024855b2", "name": "renderVideo", "webhookId": "fdca225d-8d35-4a80-bbce-7b1c6014deab"}, {"parameters": {"httpMethod": "POST", "path": "63f63834-0c70-4570-b214-019b1b17373a", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [2220, 200], "id": "3cc5264f-49c5-4508-83a5-294c9388dee7", "name": "generateSFX", "webhookId": "63f63834-0c70-4570-b214-019b1b17373a"}, {"parameters": {"httpMethod": "POST", "path": "ad24e0f2-48d1-4fc7-b7fb-f1720971842d", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-460, 20], "id": "0dc1bcd4-c16c-4c01-8cf1-4d6f51d28fc7", "name": "generateVideo", "webhookId": "ad24e0f2-48d1-4fc7-b7fb-f1720971842d"}, {"parameters": {"fieldToSplitOut": "['POV <PERSON>']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [340, 1000], "id": "95371983-0294-4630-bc71-ec19faffc3f6", "name": "Split Out1"}, {"parameters": {"fieldToSplitOut": "['POV SFX']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [340, 1200], "id": "2918d53f-1b26-4beb-94c7-80579727d8c4", "name": "Split Out2"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "17841459721592041", "edge": "media", "options": {"queryParameters": {"parameter": [{"name": "video_url", "value": "={{ $json[\"Final Render Video\"] }}"}, {"name": "caption", "value": "={{ $('Publishing Agent').item.json.output.instagram.description }}"}, {"name": "media_type", "value": "REELS"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [700, 1980], "id": "4d0ca7d1-e4b4-47a6-8c30-87758dca2a3a", "name": "Create Container", "credentials": {"facebookGraphApi": {"id": "vBYfs9LRR8t8BNVY", "name": "Lumivaux Instagram"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "=17841459721592041", "edge": "media_publish", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [860, 1980], "id": "a072a310-d946-4ef0-ba18-00207eb1d4cd", "name": "Instagram POST", "credentials": {"facebookGraphApi": {"id": "vBYfs9LRR8t8BNVY", "name": "Lumivaux Instagram"}}}, {"parameters": {"promptType": "define", "text": "=You are an AI specialized in crafting high-impact, viral titles and descriptions specifically optimized for Instagram Reels, YouTube Shorts, and Facebook Reels. You will receive a JSON input containing two fields:\n- **Name of Story**: {{$json.Name}}\n- **Description**: {{ $json['Concept (Detailed Scenario)'] }}\n\nYour task is to generate platform-specific content tailored to the unique characteristics and audience engagement styles of each platform:\n\n### 1. Instagram Reels:\n- **Description**: Brief, visually descriptive, incorporating relevant hashtags (max 5 hashtags) and a strong call-to-action for liking and sharing.\n\n### 2. YouTube Shorts:\n- **Title (under 50 chars)**: Direct, suspenseful, or surprising—focused heavily on clickability.\n- **Description**: Concise and compelling, including relevant keywords, and encouraging viewers to subscribe, like, and comment.\n\n### 3. Facebook Reels:\n- **Title (under 50 chars)**: Emotionally engaging, relatable, designed to instantly resonate.\n- **Description**: Engaging short description with an emotional hook, designed to encourage reactions, comments, and shares.\n\n---\n\nOutput your response strictly in valid JSON format as follows:\n\n```json\n{\n  \"instagram\": {\n    \"description\": \"...\"\n  },\n  \"youtube\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  },\n  \"facebook\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  }\n}\n```\n", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-20, 1980], "id": "410cdf07-c8bc-4a14-b6d7-b52b2c8067a0", "name": "Publishing Agent"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Download Video1').item.json.id }}", "Youtube": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [980, 1720], "id": "f69087f0-ea07-4404-a1fe-291afa522411", "name": "Youtube Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Download Video1').item.json.id }}", "Instagram": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1020, 1980], "id": "1a8aefd0-c462-4a52-963c-a55c4185eac5", "name": "Instagram Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Download Video1').item.json.id }}", "Facebook": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated", "value": "Videos Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [980, 2240], "id": "2376449b-b633-401b-917c-c03657918fad", "name": "Facebook Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"model": "deepseek/deepseek-r1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-40, 2180], "id": "31e5fdec-924e-499a-acd0-443ebd07b949", "name": "Deepseek R1", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "{Status} = \"Ready To Upload\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-220, 1980], "id": "c83d18bb-b259-425d-a72e-4f641ccf2e11", "name": "Search Record", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('Publishing Agent').item.json.output.youtube.title }}", "regionCode": "SG", "categoryId": "1", "options": {"description": "={{ $('Publishing Agent').item.json.output.youtube.description }}"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [740, 1720], "id": "2f62bfa1-addf-4d24-a531-65e98fb53ece", "name": "Youtube Post", "credentials": {"youTubeOAuth2Api": {"id": "INhltBeDLkn5aZ0Z", "name": "YouTube account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.id }}", "Status": "Completed"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Final Video", "displayName": "Final Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1480, 1980], "id": "3fd4fc09-2a34-4115-af4e-06a1cb102bdb", "name": "Video Published", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "me", "edge": "videos", "sendBinaryData": true, "binaryPropertyName": "=data", "options": {"queryParameters": {"parameter": [{"name": "title", "value": "={{ $('Publishing Agent').item.json.output.youtube.title }}"}, {"name": "description", "value": "={{ $('Publishing Agent').item.json.output.youtube.description }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [760, 2240], "id": "67944a3a-5c9e-4535-815e-2116082706ce", "name": "Facebook Graph API", "credentials": {"facebookGraphApi": {"id": "qJ4oysKvcjQPQOuO", "name": "Facebook Graph (Extended May 25)"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"instagram\": {\n    \"description\": \"...\"\n  },\n  \"youtube\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  },\n  \"facebook\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [120, 2180], "id": "a8be3088-f4af-42ce-8bdd-8c66bc7905b7", "name": "Structured Output Parser5"}, {"parameters": {"url": "={{ $json['Final Render Video'] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 1980], "id": "d6d5f313-c410-484f-99e3-9addbc2705cb", "name": "Download Video1"}, {"parameters": {"content": "## Youtube Agent\n", "height": 220, "width": 540, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [640, 1660], "id": "9a15366a-073f-4b87-9def-8b80be600fb4", "name": "Sticky Note6"}, {"parameters": {"content": "## Instagram Agent", "height": 240, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [640, 1900], "id": "3c5ab602-94d3-4452-ad14-52e15acf7483", "name": "Sticky Note7"}, {"parameters": {"content": "## Facebook Agent\n", "height": 220, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [640, 2160], "id": "fa4cdaf2-cc85-43fc-81ff-0d506c3c0df8", "name": "Sticky Note8"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c087ebbf-22a5-45f4-82d5-ddabfd921889", "leftValue": "={{ $json.fields.Youtube }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "301baec8-c772-4042-bdc1-7d2d99f07043", "leftValue": "={{ $json.fields.Instagram }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "c7469347-06e8-4163-a280-d85ed47d167b", "leftValue": "={{ $json.fields.Facebook }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1260, 1980], "id": "5fa9bb43-77f3-4bbe-95cc-09819120a1d4", "name": "If6"}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "id": "={{ $('Search Record').item.json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [300, 1980], "id": "16317110-9189-4a2a-8ba9-1ba898acfa32", "name": "Get Video2", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"content": "## 🚀 Ultimate Reels Publishing Agent  \n**Get Millions of Views Across All Platforms — And Turn Them Into Income!**\n\n- 🎯 One-click scheduling across **Instagram Reels**, **YouTube Shorts**, and **Facebook Reels**  \n- 💬 Auto-generate **viral captions**, **engaging descriptions**, and **platform-optimized hashtags**  \n- 💸 No need for costly APIs — **100% budget-friendly**  \n- 🧠 Smart content engine tailored for **maximum reach & engagement**\n\n**Grow faster. Post smarter. Earn more.**\n\n(dainami ai workflows)", "height": 840, "width": 2260, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-520, 1600], "id": "a623cf43-cd09-4aaa-bdef-88669eecfdb6", "name": "Sticky Note9"}, {"parameters": {"httpMethod": "POST", "path": "6e0462b5-5b88-4772-89e2-f10f7e9c0562", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-420, 1980], "id": "36b433d5-bd84-4e64-988f-c3bc543377e0", "name": "publishVideo", "webhookId": "6e0462b5-5b88-4772-89e2-f10f7e9c0562"}, {"parameters": {"promptType": "define", "text": "=Here is the POV scene:\n\nScene Description: {{ $json.Description }}\nTheme: {{ $('Get Scenes Data').item.json.Theme }}\nTone/Mood: {{ $('Get Scenes Data').item.json['Tone/Mood'] }}\n\nPlease generate an image prompt to fully depict this specific scene in a POV manner. ", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Role\n\nYou are an Image Prompt Creation Agent specialized in generating first-person (POV) image prompts for an AI image generator.\n\n# Task\n\nUsing the provided input, craft a **concise prompt (under 50 words)** that instructs the AI to produce an image with a clear first-person perspective. Your prompt must:\n\n- **Emphasize First-Person POV:** Clearly state that the scene is viewed from a first-person perspective (e.g., “First-person view of…” or “POV view of…”).  \n- **Indicate Visible Body Parts:** When relevant, specify that hands, arms, or feet should be visible to enhance the POV effect.  \n- **Include Scene Details:** Mention key elements such as lighting, atmosphere, environment, and objects.  \n- **Reflect Theme & Tone:** Integrate the provided theme and tone/mood to ensure the image evokes the desired emotions.  \n- **Maintain Visual Style:** Specify a consistent style (e.g., photorealistic, cinematic, digital art) that matches the theme.\n\n# Input\n\nYou will receive:\n\n- **Scene Description:** A POV caption describing the scene.  \n- **Theme:** The underlying theme.  \n- **Tone/Mood:** The intended emotional tone.\n\n# Output\n\nReturn your generated prompt in the following JSON format:\n```json\n{\n  \"prompt\": \"<insert detailed image prompt here>\"\n}\n\n\n#Example\n##Example 1\nInput: \nScene Description: \"POV: Entering a dimly lit, dust-filled room with ancient archives.\"\nTheme: \"Mystery & Legacy\"\nTone/Mood: \"Haunting, Thought-Provoking\"\n\nOutput:\n{\n  \"prompt\": \"First-person view of entering a dim, dust-filled archive room. A hand reaches for ancient scrolls under soft lantern light, evoking mystery and legacy.\"\n}\n\n##Example 2\nInput:\nScene Description: \"POV: Standing on a crumbling castle wall at dawn.\"\nTheme: \"Hope & Renewal\"\nTone/Mood: \"Epic, Uplifting\"\n\nOutput:\n{\n  \"prompt\": \"First-person view from a crumbling castle wall at dawn. A hand grips cold stone as sunrise bathes the scene in epic, uplifting light.\"\n}\n\n##Example 3:\nInput:\nScene Description: \"POV: Walking through a neon-lit alley in a futuristic metropolis.\"\nTheme: \"Rebellion & Futurism\"\nTone/Mood: \"Edgy, Intense\"\n\nOutput:\n{\n  \"prompt\": \"First-person view of a neon-lit alley in a futuristic city. A visible hand holds a high-tech gadget, capturing an edgy, intense vibe of rebellion.\"\n}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [2080, -740], "id": "3452325a-4fa0-44fb-a988-955d71e008b2", "name": "Image Agent"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "POV Daily Idea Agent", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "POV Daily Idea Agent", "type": "ai_outputParser", "index": 0}]]}, "POV Daily Idea Agent": {"main": [[{"node": "Human Approval", "type": "main", "index": 0}]]}, "Airtable": {"ai_tool": [[{"node": "POV Daily Idea Agent", "type": "ai_tool", "index": 0}]]}, "Qwen": {"ai_languageModel": [[{"node": "POV Daily Idea Agent", "type": "ai_languageModel", "index": 0}]]}, "If": {"main": [[{"node": "Approved", "type": "main", "index": 0}], [{"node": "Declined", "type": "main", "index": 0}]]}, "Declined": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Approved": {"main": [[{"node": "Update Airtable1", "type": "main", "index": 0}]]}, "Human Approval": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "POV Daily Idea Agent", "type": "main", "index": 0}]]}, "POV Scenes Agent": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Update Airtable", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Store Scenes": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Store Scenes", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "POV Scenes Agent", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "POV Scenes Agent", "type": "ai_outputParser", "index": 0}]]}, "If2": {"main": [[{"node": "Get Scenes Data", "type": "main", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "Image Agent", "type": "ai_outputParser", "index": 0}]]}, "Get Video Idea": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Get Scenes": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "Get Scenes Data": {"main": [[{"node": "Split Scenes", "type": "main", "index": 0}]]}, "Split Scenes": {"main": [[{"node": "Loop Scenes", "type": "main", "index": 0}]]}, "Loop Scenes": {"main": [[{"node": "Update Airtable2", "type": "main", "index": 0}], [{"node": "Retrieve Scenes", "type": "main", "index": 0}]]}, "Retrieve Scenes": {"main": [[{"node": "Image Agent", "type": "main", "index": 0}]]}, "Qwen  ": {"ai_languageModel": [[{"node": "Image Agent", "type": "ai_languageModel", "index": 0}]]}, "Qwen1": {"ai_languageModel": [[{"node": "POV Scenes Agent", "type": "ai_languageModel", "index": 0}]]}, "Generate Images": {"main": [[{"node": "Download Images", "type": "main", "index": 0}]]}, "Download Images": {"main": [[{"node": "Upload Scene Images", "type": "main", "index": 0}]]}, "Upload Scene Images": {"main": [[{"node": "Update Image Airtable", "type": "main", "index": 0}]]}, "Update Image Airtable": {"main": [[{"node": "Loop Scenes", "type": "main", "index": 0}]]}, "generateImages": {"main": [[{"node": "Get Scenes", "type": "main", "index": 0}]]}, "generateScenes": {"main": [[{"node": "Get Video Idea", "type": "main", "index": 0}]]}, "Video Prompt Generator": {"main": [[{"node": "Generate Video", "type": "main", "index": 0}]]}, "Generate Video": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Get Video": {"main": [[{"node": "Download Video", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Upload Video1", "type": "main", "index": 0}]]}, "Get Image": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "Get Image Url": {"main": [[{"node": "Split Image", "type": "main", "index": 0}]]}, "Split Image": {"main": [[{"node": "Loop Image", "type": "main", "index": 0}]]}, "Loop Image": {"main": [[{"node": "Update Airtable 3", "type": "main", "index": 0}], [{"node": "Get Image For Each Scene", "type": "main", "index": 0}]]}, "Get Image For Each Scene": {"main": [[{"node": "Video Prompt Generator", "type": "main", "index": 0}]]}, "Qwen ": {"ai_languageModel": [[{"node": "Video Prompt Generator", "type": "ai_languageModel", "index": 0}]]}, "Upload Video1": {"main": [[{"node": "Upload Video Airtable", "type": "main", "index": 0}]]}, "Upload Video Airtable": {"main": [[{"node": "Loop Image", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Get Image Url", "type": "main", "index": 0}]]}, "Structured Output Parser3": {"ai_outputParser": [[{"node": "Video Prompt Generator", "type": "ai_outputParser", "index": 0}]]}, "Wait1": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "SFX Prompt Generator": {"main": [[{"node": "Generate SFX", "type": "main", "index": 0}]]}, "Get Scenes 2": {"main": [[{"node": "Split Scenes1", "type": "main", "index": 0}]]}, "Check Video Status": {"main": [[{"node": "If4", "type": "main", "index": 0}]]}, "Loop Over Scenes": {"main": [[{"node": "Update Airtable 4", "type": "main", "index": 0}], [{"node": "Get Scenes For SFX", "type": "main", "index": 0}]]}, "Get Scenes For SFX": {"main": [[{"node": "SFX Prompt Generator", "type": "main", "index": 0}]]}, "Generate SFX": {"main": [[{"node": "Uploade SFX", "type": "main", "index": 0}]]}, "Upload SFX Airtable": {"main": [[{"node": "Loop Over Scenes", "type": "main", "index": 0}]]}, "Uploade SFX": {"main": [[{"node": "Upload SFX Airtable", "type": "main", "index": 0}]]}, "If4": {"main": [[{"node": "Get Scenes 2", "type": "main", "index": 0}]]}, "Structured Output Parser4": {"ai_outputParser": [[{"node": "SFX Prompt Generator", "type": "ai_outputParser", "index": 0}]]}, "Split Scenes1": {"main": [[{"node": "Loop Over Scenes", "type": "main", "index": 0}]]}, "Qwen2": {"ai_languageModel": [[{"node": "SFX Prompt Generator", "type": "ai_languageModel", "index": 0}]]}, "Get SFX ": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Get Videos": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Get Scenes Captions": {"main": [[{"node": "Split Out3", "type": "main", "index": 0}]]}, "Get Record": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get SFX": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Render Video", "type": "main", "index": 0}]]}, "Render Video": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Upload To Airtable", "type": "main", "index": 0}]]}, "If5": {"main": [[{"node": "Get Videos", "type": "main", "index": 0}, {"node": "Get Scenes Captions", "type": "main", "index": 0}, {"node": "Get SFX ", "type": "main", "index": 0}]]}, "Airtable1": {"main": [[{"node": "If5", "type": "main", "index": 0}]]}, "Get Video1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Split Out3": {"main": [[{"node": "Get Record", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "renderVideo": {"main": [[{"node": "Airtable1", "type": "main", "index": 0}]]}, "generateSFX": {"main": [[{"node": "Check Video Status", "type": "main", "index": 0}]]}, "generateVideo": {"main": [[{"node": "Get Image", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Get Video1", "type": "main", "index": 0}]]}, "Split Out2": {"main": [[{"node": "Get SFX", "type": "main", "index": 0}]]}, "Create Container": {"main": [[{"node": "Instagram POST", "type": "main", "index": 0}]]}, "Instagram POST": {"main": [[{"node": "Instagram Posted", "type": "main", "index": 0}]]}, "Publishing Agent": {"main": [[{"node": "Get Video2", "type": "main", "index": 0}]]}, "Youtube Posted": {"main": [[{"node": "If6", "type": "main", "index": 0}]]}, "Instagram Posted": {"main": [[{"node": "If6", "type": "main", "index": 0}]]}, "Facebook Posted": {"main": [[{"node": "If6", "type": "main", "index": 0}]]}, "Deepseek R1": {"ai_languageModel": [[{"node": "Publishing Agent", "type": "ai_languageModel", "index": 0}]]}, "Search Record": {"main": [[{"node": "Publishing Agent", "type": "main", "index": 0}]]}, "Youtube Post": {"main": [[{"node": "Youtube Posted", "type": "main", "index": 0}]]}, "Facebook Graph API": {"main": [[{"node": "Facebook Posted", "type": "main", "index": 0}]]}, "Structured Output Parser5": {"ai_outputParser": [[{"node": "Publishing Agent", "type": "ai_outputParser", "index": 0}]]}, "Download Video1": {"main": [[{"node": "Create Container", "type": "main", "index": 0}, {"node": "Youtube Post", "type": "main", "index": 0}, {"node": "Facebook Graph API", "type": "main", "index": 0}]]}, "If6": {"main": [[{"node": "Video Published", "type": "main", "index": 0}]]}, "Get Video2": {"main": [[{"node": "Download Video1", "type": "main", "index": 0}]]}, "publishVideo": {"main": [[{"node": "Search Record", "type": "main", "index": 0}]]}, "Image Agent": {"main": [[{"node": "Generate Images", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "805a2768-1057-427c-bbb4-479b3f34557f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d8c459f4ebcadd20c3258ade09582fe0f02de933363f6184259e0c9a36784090"}, "id": "i8s2oTVgHbkO8og7", "tags": [{"createdAt": "2025-03-24T09:10:16.506Z", "updatedAt": "2025-03-24T09:10:16.506Z", "id": "RqZ45jZ8VcYyMMZW", "name": "W4: POV Content Machine"}]}