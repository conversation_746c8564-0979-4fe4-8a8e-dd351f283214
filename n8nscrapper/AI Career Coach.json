{"nodes": [{"parameters": {"path": "career-coach", "httpMethod": "POST", "responseMode": "onReceived", "authentication": "headerAuth"}, "id": 1, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 300], "credentials": {"httpHeaderAuth": {"id": "your-header-auth-credentials", "name": "Header <PERSON>"}}}, {"parameters": {"resource": "database", "operation": "insert", "table": "logs", "columns": ["timestamp", "user_input"], "values": [["={{ new Date().toISOString() }}", "={{ $json[\"message\"] }}"]]}, "id": 2, "name": "Log Input to NeonDB", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [250, 200], "credentials": {"postgres": {"id": "your-neon-credentials", "name": "Neon Database"}}}, {"parameters": {"prompt": "You are an AI Career Coach. Use the user's profile and past responses if available. Help them with tailored job paths, skill gaps, and resources.\nUser Input: {{$json[\"message\"]}}", "model": "gpt-4", "temperature": 0.7, "maxTokens": 800, "memory": true}, "id": 3, "name": "OpenAI - GPT Response", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [400, 300], "credentials": {"openAiApi": {"id": "your-openai-api-credentials", "name": "OpenAI Credentials"}}}, {"parameters": {"options": {}, "jsonParameters": false, "responseData": "={{ $json[\"choices\"][0][\"text\"] }}", "responseCode": 200}, "id": 4, "name": "Respond to User", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [600, 300]}, {"parameters": {"resource": "user", "operation": "get", "id": "={{ $json[\"user_id\"] }}"}, "id": 5, "name": "Fetch Clerk User", "type": "n8n-nodes-base.clerk", "typeVersion": 1, "position": [250, 400], "credentials": {"clerkApi": {"id": "your-clerk-credentials", "name": "Clerk API"}}}], "connections": {"Webhook Trigger": {"main": [[{"node": "Log Input to NeonDB", "type": "main", "index": 0}, {"node": "Fetch Clerk User", "type": "main", "index": 0}]]}, "Log Input to NeonDB": {"main": [[{"node": "OpenAI - GPT Response", "type": "main", "index": 0}]]}, "Fetch Clerk User": {"main": [[{"node": "OpenAI - GPT Response", "type": "main", "index": 1}]]}, "OpenAI - GPT Response": {"main": [[{"node": "Respond to User", "type": "main", "index": 0}]]}}}