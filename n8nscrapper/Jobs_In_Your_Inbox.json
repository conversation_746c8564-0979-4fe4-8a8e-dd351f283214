{"name": "Jobs-In-Your-Inbox", "nodes": [{"parameters": {"url": "https://himalayas.app/jobs/rss", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [-580, 80], "id": "bfc721f5-c7b0-4278-8ffc-c8e088415ba3", "name": "Himalayas"}, {"parameters": {"url": "https://remotive.com/remote-jobs/feed", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [-580, 480], "id": "4d51baf4-182c-423d-8525-e0d6379e3755", "name": "Remotive"}, {"parameters": {"url": "https://weworkremotely.com/remote-jobs.rss", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [-580, 280], "id": "d4c4f6fc-39ff-412e-b1d2-5561a8383df6", "name": "WWR"}, {"parameters": {"url": "https://jobicy.com/?feed=job_feed", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [-580, 640], "id": "bf74dcad-48dd-434d-a895-a5ef3a3d18dd", "name": "JobIcy"}, {"parameters": {"url": "https://www.smartremotejobs.com/feed/all.rss", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.1, "position": [-580, -100], "id": "8ebc4c22-199c-40a7-9a31-0bcdbc9e6389", "name": "SJ"}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 12}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-980, 280], "id": "e5006e64-1676-446d-a069-e3fabf89b4df", "name": "Schedule Trigger1"}, {"parameters": {"jsonSchemaExample": "{\n  \"allJobs\": [\n    {\n      \"title\": \"\",\n      \"match-percentage\": \"\",\n      \"final-advice\": \"\"\n    },\n    {\n       \"title\": \"\",\n       \"match-percentage\": \"\",\n       \"final-advice\": \"\"\n    }\n  ]\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [660, 620], "id": "815a5c5f-a08c-444d-a106-57288ba0ca52", "name": "Structured Output Parser5"}, {"parameters": {"modelName": "models/gemini-1.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [540, 620], "id": "5c11bc66-a57d-4356-82c6-ecba79f4c0de", "name": "Google Gemini Chat Model5", "credentials": {"googlePalmApi": {"id": "dvqEioWYlDCNRsLV", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "title", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [740, 240], "id": "de6f516d-559e-4e5c-a533-fd46ff8b16ec", "name": "Remove Duplicates"}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 10}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-880, 1300], "id": "b19ad2b7-118f-48c1-b101-8ed86d89cb57", "name": "Schedule Trigger"}, {"parameters": {"assignments": {"assignments": [{"id": "060e28e0-0174-4f33-8447-c7e967b9bd8f", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "14bd4316-ccb0-46e6-8761-860f02825007", "name": "job-description", "value": "={{ $json.content }}", "type": "string"}, {"id": "037a1441-1100-4f1d-9f1e-333ae4b7bfdd", "name": "url", "value": "={{ $json.link }}", "type": "string"}, {"id": "1d0181cf-3cb1-492e-a32d-b09493f8e342", "name": "date", "value": "={{ new Date($json.pubDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, -100], "id": "9ed12733-e3e4-45da-99ac-c255f0918463", "name": "extractor_1"}, {"parameters": {"assignments": {"assignments": [{"id": "060e28e0-0174-4f33-8447-c7e967b9bd8f", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "14bd4316-ccb0-46e6-8761-860f02825007", "name": "job-description", "value": "={{ $json['content:encodedSnippet'] }}", "type": "string"}, {"id": "037a1441-1100-4f1d-9f1e-333ae4b7bfdd", "name": "url", "value": "={{ $json.link }}", "type": "string"}, {"id": "1d0181cf-3cb1-492e-a32d-b09493f8e342", "name": "date", "value": "={{ new Date($json.pubDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, 80], "id": "51d007e9-d9e3-4835-95f6-cb5ce16e708d", "name": "extractor_2"}, {"parameters": {"assignments": {"assignments": [{"id": "060e28e0-0174-4f33-8447-c7e967b9bd8f", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "14bd4316-ccb0-46e6-8761-860f02825007", "name": "job-description", "value": "={{ $json.contentSnippet }}", "type": "string"}, {"id": "037a1441-1100-4f1d-9f1e-333ae4b7bfdd", "name": "url", "value": "={{ $json.link }}", "type": "string"}, {"id": "1d0181cf-3cb1-492e-a32d-b09493f8e342", "name": "date", "value": "={{ new Date($json.pubDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, 280], "id": "a78965f0-6ae5-4efd-a1e1-d57ec31052c1", "name": "extractor_3"}, {"parameters": {"assignments": {"assignments": [{"id": "060e28e0-0174-4f33-8447-c7e967b9bd8f", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "14bd4316-ccb0-46e6-8761-860f02825007", "name": "job-description", "value": "={{ $json.contentSnippet }}", "type": "string"}, {"id": "037a1441-1100-4f1d-9f1e-333ae4b7bfdd", "name": "url", "value": "={{ $json.link }}", "type": "string"}, {"id": "1d0181cf-3cb1-492e-a32d-b09493f8e342", "name": "date", "value": "={{ new Date($json.pubDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, 480], "id": "06871932-132f-4def-bf22-1f1c23a84259", "name": "extractor_4"}, {"parameters": {"assignments": {"assignments": [{"id": "060e28e0-0174-4f33-8447-c7e967b9bd8f", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "14bd4316-ccb0-46e6-8761-860f02825007", "name": "job-description", "value": "={{ $json.contentSnippet }}", "type": "string"}, {"id": "037a1441-1100-4f1d-9f1e-333ae4b7bfdd", "name": "url", "value": "={{ $json.link }}", "type": "string"}, {"id": "1d0181cf-3cb1-492e-a32d-b09493f8e342", "name": "date", "value": "={{ new Date($json.pubDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-360, 640], "id": "ff39a32e-aac7-4450-8074-9e22e484029a", "name": "extractor_5"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "516e8c74-d818-4796-a49a-7112c497d636", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() )).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-140, -100], "id": "1d8a5409-f939-461e-8678-ce2085714bcd", "name": "date-filter-1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "516e8c74-d818-4796-a49a-7112c497d636", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() )).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-140, 80], "id": "03c88f82-691b-4f91-b854-26c639117b42", "name": "date-filter-2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "516e8c74-d818-4796-a49a-7112c497d636", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() )).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-140, 280], "id": "908af165-d854-4e2a-b9ce-9f119c09e220", "name": "date-filter-3"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "516e8c74-d818-4796-a49a-7112c497d636", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() )).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-140, 480], "id": "65ccf9d6-343c-45be-becf-37e05c7197ba", "name": "date-filter-4"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "516e8c74-d818-4796-a49a-7112c497d636", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() )).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-140, 640], "id": "15bf6d59-41e9-430d-ad48-80a6438ce281", "name": "date-filter-5"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "afc515d8-4c45-49d7-81db-f702ef328309", "leftValue": "={{ $json.title }}", "rightValue": "writing", "operator": {"type": "string", "operation": "contains"}}, {"id": "e306e6bd-b6cb-4ef8-9f85-21491610189f", "leftValue": "={{ $json.title }}", "rightValue": "content writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "d5f1ce3f-6902-4ae3-b142-f21f65ded7be", "leftValue": "={{ $json.title }}", "rightValue": "writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "486264de-7310-469f-b10a-19f9b496764e", "leftValue": "={{ $json.title }}", "rightValue": "marketing", "operator": {"type": "string", "operation": "contains"}}, {"id": "15f6011e-ee65-4399-b215-86c4953cf492", "leftValue": "={{ $json.title }}", "rightValue": "seo", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "2f5292b7-cb4b-4e5d-bc52-833e20eb900f", "leftValue": "{{ $json.title }}", "rightValue": "content editor", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "74a26f77-13b0-4f6a-aaeb-6fe348a5ff03", "leftValue": "{{ $json.title }}", "rightValue": "content manager", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "a9e583cf-7f84-4c47-a59d-486cc8f6a17a", "leftValue": "{{ $json.title }}", "rightValue": "sr. content writer", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "4bf1f053-d336-41e2-a51f-4a2f63720916", "leftValue": "{{ $json.title }}", "rightValue": "senior content writer", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [100, -120], "id": "e6e2ee5e-57e4-4353-971f-c4e323f03233", "name": "designation-filter-1"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "afc515d8-4c45-49d7-81db-f702ef328309", "leftValue": "={{ $json.title }}", "rightValue": "writing", "operator": {"type": "string", "operation": "contains"}}, {"id": "e306e6bd-b6cb-4ef8-9f85-21491610189f", "leftValue": "={{ $json.title }}", "rightValue": "content writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "d5f1ce3f-6902-4ae3-b142-f21f65ded7be", "leftValue": "={{ $json.title }}", "rightValue": "writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "486264de-7310-469f-b10a-19f9b496764e", "leftValue": "={{ $json.title }}", "rightValue": "marketing", "operator": {"type": "string", "operation": "contains"}}, {"id": "15f6011e-ee65-4399-b215-86c4953cf492", "leftValue": "={{ $json.title }}", "rightValue": "seo", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [100, 60], "id": "63c308c0-537e-441a-9308-c5e5a72ff9a8", "name": "designation-filter-2"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "afc515d8-4c45-49d7-81db-f702ef328309", "leftValue": "={{ $json.title }}", "rightValue": "writing", "operator": {"type": "string", "operation": "contains"}}, {"id": "e306e6bd-b6cb-4ef8-9f85-21491610189f", "leftValue": "={{ $json.title }}", "rightValue": "content writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "d5f1ce3f-6902-4ae3-b142-f21f65ded7be", "leftValue": "={{ $json.title }}", "rightValue": "writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "486264de-7310-469f-b10a-19f9b496764e", "leftValue": "={{ $json.title }}", "rightValue": "marketing", "operator": {"type": "string", "operation": "contains"}}, {"id": "15f6011e-ee65-4399-b215-86c4953cf492", "leftValue": "={{ $json.title }}", "rightValue": "seo", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [100, 260], "id": "9e01358a-bb0d-4b55-b6bd-a9f1d34ba6f8", "name": "designation-filter-3"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "afc515d8-4c45-49d7-81db-f702ef328309", "leftValue": "={{ $json.title }}", "rightValue": "writing", "operator": {"type": "string", "operation": "contains"}}, {"id": "e306e6bd-b6cb-4ef8-9f85-21491610189f", "leftValue": "={{ $json.title }}", "rightValue": "content writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "d5f1ce3f-6902-4ae3-b142-f21f65ded7be", "leftValue": "={{ $json.title }}", "rightValue": "writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "486264de-7310-469f-b10a-19f9b496764e", "leftValue": "={{ $json.title }}", "rightValue": "marketing", "operator": {"type": "string", "operation": "contains"}}, {"id": "15f6011e-ee65-4399-b215-86c4953cf492", "leftValue": "={{ $json.title }}", "rightValue": "seo", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [100, 460], "id": "b65abcae-32ee-4247-aa1e-1cca706005e7", "name": "designation-filter-4"}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "afc515d8-4c45-49d7-81db-f702ef328309", "leftValue": "={{ $json.title }}", "rightValue": "writing", "operator": {"type": "string", "operation": "contains"}}, {"id": "e306e6bd-b6cb-4ef8-9f85-21491610189f", "leftValue": "={{ $json.title }}", "rightValue": "content writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "d5f1ce3f-6902-4ae3-b142-f21f65ded7be", "leftValue": "={{ $json.title }}", "rightValue": "writer", "operator": {"type": "string", "operation": "contains"}}, {"id": "486264de-7310-469f-b10a-19f9b496764e", "leftValue": "={{ $json.title }}", "rightValue": "marketing", "operator": {"type": "string", "operation": "contains"}}, {"id": "15f6011e-ee65-4399-b215-86c4953cf492", "leftValue": "={{ $json.title }}", "rightValue": "seo", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {"ignoreCase": true}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [120, 620], "id": "5dc52291-598d-4239-90e5-fa592b5c1365", "name": "designation-filter-5"}, {"parameters": {"numberInputs": 5}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [500, 200], "id": "47dd408f-ff68-45f1-8b59-e6ea1493e23c", "name": "accumulate-all-matches"}, {"parameters": {"assignments": {"assignments": [{"id": "060e28e0-0174-4f33-8447-c7e967b9bd8f", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "14bd4316-ccb0-46e6-8761-860f02825007", "name": "job-description", "value": "={{ $json['job-description'] }}", "type": "string"}, {"id": "037a1441-1100-4f1d-9f1e-333ae4b7bfdd", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "1d0181cf-3cb1-492e-a32d-b09493f8e342", "name": "date", "value": "={{ $json.date }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [940, 240], "id": "c478ad93-9e40-4582-8e7a-6e0325fc7d4e", "name": "set-final-data"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA", "mode": "list", "cachedResultName": "Job Matches", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Core_dump", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit#gid=**********"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": ["title"], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "job-description", "displayName": "job-description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "date", "displayName": "date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "USER_ENTERED"}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1140, 240], "id": "82abf96c-0e52-48d3-ae4b-f880daccfee6", "name": "update_core_dump", "credentials": {"googleSheetsOAuth2Api": {"id": "Z8BhoATs7STEcEwt", "name": "Google Sheets account"}}}, {"parameters": {"promptType": "define", "text": "=Job Title: {{ $json.title }}\nJob Summary: {{ $json['job-description'] }}", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=#Role\nYou are an expert in job-resume matching and review. Your task is to analyze job descriptions and compare them to a user's resume, providing a match percentage and personalized advice for each job. Your analysis should include whether the candidate is overqualified, underqualified, or perfectly qualified for the role, and assess whether their resume/skills have enough coverage for the job. You can access the job descriptions and other details \n\n#User's Resume : <add your details here>\n\n\n#Task\nFor Each Job Listing, do the following:\nMatching: Compare the job requirements with the candidate’s experience, skills, and qualifications. Identify areas of strong alignment, partial alignment, and mismatches. Consider the following:\nSkill Coverage: Does the candidate possess the necessary skills required by the job? Is there sufficient bandwidth in their skills for the role?\nExperience Alignment: Does the candidate have the right level and type of experience for the role?\nIndustry Fit: Does the candidate's previous work align with the specific industry the job is in?\n\nMatch Percentage (0-100%): Assign a percentage score based on the candidate's overall suitability for the job. The score should reflect:\nRelevance of the candidate's skills and experience.\nWhether the candidate is overqualified, underqualified, or perfectly qualified based on the following criteria:\nOverqualified: If the candidate’s skills, experience, and qualifications exceed the requirements for the role to the point that applying might be a career step back. In such cases, consider whether the role would offer enough career progression.\nUnderqualified: If the candidate lacks some key skills or experience required for the role, suggesting that they may need more time to gain the required expertise.\nPerfectly Qualified: If the candidate meets the requirements well without being too advanced or lacking in key areas.\n\nFinal Advice: Based on the match percentage, give actionable advice with a focus on:\nOverqualified/Underqualified/Perfect Fit: Clearly state whether the candidate is a good fit, or if they may face challenges due to being overqualified or underqualified.\nSkill Gaps: If there are gaps, mention specific skills the candidate may need to develop to be a better fit for the role.\nCareer Progression: If the candidate is overqualified, advise them on whether this role aligns with their career trajectory or if they may need a higher-level role for further growth.\nFit in the Industry: Make sure to consider whether the candidate is suitable for the industry and role type (e.g., B2B, B2C, SaaS, Web3).\n\nKeep advice short, clear, and actionable. Tailor it to the individual’s career growth and skill set.\n\n#Example of thought process\nJob: Junior writer\nUser experience: 7+ years\nReasoning: overqualified\nAdvice: best to look for other options, can apply if career progression doesn't matter."}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [560, 480], "id": "d46f9e3c-2c87-4fcb-bf03-ed4a4eeed4f6", "name": "Job-Matching-Agent🤖"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA", "mode": "list", "cachedResultName": "Job Matches", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Final_advice", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $json.output.allJobs[0].title }}", "Match Percent": "={{ $json.output.allJobs[0]['match-percentage'] }}", "Final Advice": "={{ $json.output.allJobs[0]['final-advice'] }}"}, "matchingColumns": ["title"], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "job-description", "displayName": "job-description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "date", "displayName": "date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Match Percent", "displayName": "Match Percent", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Final Advice", "displayName": "Final Advice", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [920, 480], "id": "be1a1eac-3d98-45b4-a1e0-68d7c9f0e865", "name": "update-final-advice", "credentials": {"googleSheetsOAuth2Api": {"id": "Z8BhoATs7STEcEwt", "name": "Google Sheets account"}}}, {"parameters": {"sortFieldsUi": {"sortField": [{"fieldName": "Match Percent", "order": "descending"}]}, "options": {}}, "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [1120, 480], "id": "cdf71046-2c0d-4bef-9d04-677f565db7b1", "name": "sort-by-highest-match"}, {"parameters": {"jsCode": "// Get input data from the previous node\nconst jobs = items.map(item => item.json);\n\n// Sort jobs by \"Match Percent\" in descending order\nconst topJobs = jobs\n    .sort((a, b) => parseFloat(b[\"Match Percent\"]) - parseFloat(a[\"Match Percent\"]))\n    .slice(0, 3); // Get only the top 3\n\n// Format the jobs as HTML for email\nconst emailBody = topJobs.map(job => `\n  <h3>${job.title} - ${job[\"Match Percent\"]}</h3>\n  <p><strong>Final Advice:</strong> ${job[\"Final Advice\"]}</p>\n  <hr>\n`).join(\"\");\n\n// Return the formatted email content\nreturn [{ json: { emailBody } }];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1040, 760], "id": "2b1a4f95-a3ab-471e-a4b5-3aa1f4b64263", "name": "select-top-matches"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=Your Job Matches for {{ $now.toFormat('MMM dd, yyyy') }}", "message": "=Hi,\n\nHere's the list of your top 3 job matches for {{ $now.toFormat('MMM dd, yyyy') }}. Best of luck 🤞🍀\n{{ $json.emailBody }}\nView the full list here: <add your Google Sheets Link>\n\nCheers,\n<Your Name>\n\n\n", "options": {"appendAttribution": false, "senderName": "=<PERSON><PERSON>al"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1220, 760], "id": "44c408c9-3c32-460a-926e-e5de8bd7fef3", "name": "send-email", "webhookId": "85ede649-5edb-4d30-ab5b-7c699e871b3f", "credentials": {"gmailOAuth2": {"id": "rEj3KmB961PL5o53", "name": "Gmail account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA", "mode": "list", "cachedResultName": "Job Matches", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Core_dump", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit#gid=**********"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-460, 1200], "id": "0e221970-751e-4fe1-99e7-25bcdf7c1f4b", "name": "check-core-dump", "credentials": {"googleSheetsOAuth2Api": {"id": "Z8BhoATs7STEcEwt", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA", "mode": "list", "cachedResultName": "Job Matches", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Final_advice", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit#gid=**********"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-460, 1420], "id": "78265e52-0f6b-4a0c-aabf-e4458fb9c8a0", "name": "check-final-advice", "credentials": {"googleSheetsOAuth2Api": {"id": "Z8BhoATs7STEcEwt", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f97e897a-6110-4d4b-8c81-77af1ff645a6", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() - 1)).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}}", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {"ignoreCase": false}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-240, 1200], "id": "bd7c3926-8785-404e-b6a8-d06d89e0d2c4", "name": "backdate-filter-1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f97e897a-6110-4d4b-8c81-77af1ff645a6", "leftValue": "={{ $json.date }}", "rightValue": "={{ new Date(new Date().setDate(new Date().getDate() - 1)).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}}", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {"ignoreCase": false}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-240, 1420], "id": "1b3fd965-da14-4f3a-bd0d-d17259dcc12f", "name": "backdate-filter-2"}, {"parameters": {"fieldToSplitOut": "date", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [20, 1180], "id": "f046917b-3a08-4ae6-84fa-e1e26a423878", "name": "clean-data-1"}, {"parameters": {"fieldToSplitOut": "date", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [20, 1400], "id": "53b1f32c-69fa-4d8b-b463-0a92c9d8ce3d", "name": "clean-data-2"}, {"parameters": {"operation": "delete", "documentId": {"__rl": true, "value": "1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA", "mode": "list", "cachedResultName": "Job Matches", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Core_dump", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit#gid=**********"}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [300, 1180], "id": "bde25a2a-08d5-4ade-a6cb-811ae77c3a83", "name": "clear-past-records-1", "alwaysOutputData": true, "retryOnFail": true, "credentials": {"googleSheetsOAuth2Api": {"id": "Z8BhoATs7STEcEwt", "name": "Google Sheets account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "delete", "documentId": {"__rl": true, "value": "1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA", "mode": "list", "cachedResultName": "Job Matches", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "Final_advice", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit#gid=**********"}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [300, 1400], "id": "e2098174-5e59-4d55-9d51-caab717d2dab", "name": "clear-past-records-2", "alwaysOutputData": true, "retryOnFail": true, "credentials": {"googleSheetsOAuth2Api": {"id": "Z8BhoATs7STEcEwt", "name": "Google Sheets account"}}, "onError": "continueRegularOutput"}, {"parameters": {"content": "## Use this to gather the latest jobs\n\n1. Swap RSS feeds as required\n2. Create a Google Sheets. [Copy this template](https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=sharing).\n", "height": 140, "width": 560}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1440, 80], "id": "fa86399c-dcc5-4df1-a094-dcf2044aca81", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Use this to gather the latest jobs\n\n1. Create a Google Sheets. [Copy this template](https://docs.google.com/spreadsheets/d/1o389bIWshMbtm3SBBl3W1JAvnnMN0UlbKBuaDeE8VFA/edit?usp=sharing).\n", "height": 140, "width": 560}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1340, 1100], "id": "7f784983-f3c3-42ce-a1b3-d9981cbe7089", "name": "Sticky Note1"}, {"parameters": {"content": "## Change keywords as needed (your job roles)", "height": 80, "width": 340}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [280, -160], "id": "74f55090-a1c7-478c-af5c-a437549f06a8", "name": "Sticky Note2"}, {"parameters": {"content": "## Add your job preferences, experience, and other relevant details in System Prompt: #User's Resume : <add your details here>", "height": 180, "width": 460}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [420, 760], "id": "4312ffb6-69df-4117-91da-1c4169bd447e", "name": "Sticky Note3"}, {"parameters": {"content": "## Add the Google SHeets link in the email body (and your signature if you want to).", "height": 120, "width": 420}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1220, 920], "id": "0d8f40c3-e435-45eb-96c4-95214955aafc", "name": "Sticky Note4"}], "pinData": {}, "connections": {"Himalayas": {"main": [[{"node": "extractor_2", "type": "main", "index": 0}]]}, "Remotive": {"main": [[{"node": "extractor_4", "type": "main", "index": 0}]]}, "WWR": {"main": [[{"node": "extractor_3", "type": "main", "index": 0}]]}, "JobIcy": {"main": [[{"node": "extractor_5", "type": "main", "index": 0}]]}, "SJ": {"main": [[{"node": "extractor_1", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "SJ", "type": "main", "index": 0}, {"node": "Himalayas", "type": "main", "index": 0}, {"node": "WWR", "type": "main", "index": 0}, {"node": "Remotive", "type": "main", "index": 0}, {"node": "JobIcy", "type": "main", "index": 0}]]}, "Structured Output Parser5": {"ai_outputParser": [[{"node": "Job-Matching-Agent🤖", "type": "ai_outputParser", "index": 0}]]}, "Google Gemini Chat Model5": {"ai_languageModel": [[{"node": "Job-Matching-Agent🤖", "type": "ai_languageModel", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "set-final-data", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "check-core-dump", "type": "main", "index": 0}, {"node": "check-final-advice", "type": "main", "index": 0}]]}, "extractor_1": {"main": [[{"node": "date-filter-1", "type": "main", "index": 0}]]}, "extractor_2": {"main": [[{"node": "date-filter-2", "type": "main", "index": 0}]]}, "extractor_3": {"main": [[{"node": "date-filter-3", "type": "main", "index": 0}]]}, "extractor_4": {"main": [[{"node": "date-filter-4", "type": "main", "index": 0}]]}, "extractor_5": {"main": [[{"node": "date-filter-5", "type": "main", "index": 0}]]}, "date-filter-1": {"main": [[{"node": "designation-filter-1", "type": "main", "index": 0}]]}, "date-filter-2": {"main": [[{"node": "designation-filter-2", "type": "main", "index": 0}]]}, "date-filter-3": {"main": [[{"node": "designation-filter-3", "type": "main", "index": 0}]]}, "date-filter-4": {"main": [[{"node": "designation-filter-4", "type": "main", "index": 0}]]}, "date-filter-5": {"main": [[{"node": "designation-filter-5", "type": "main", "index": 0}]]}, "designation-filter-1": {"main": [[{"node": "accumulate-all-matches", "type": "main", "index": 0}]]}, "designation-filter-2": {"main": [[{"node": "accumulate-all-matches", "type": "main", "index": 1}]]}, "designation-filter-3": {"main": [[{"node": "accumulate-all-matches", "type": "main", "index": 2}]]}, "designation-filter-4": {"main": [[{"node": "accumulate-all-matches", "type": "main", "index": 3}]]}, "designation-filter-5": {"main": [[{"node": "accumulate-all-matches", "type": "main", "index": 4}]]}, "accumulate-all-matches": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "set-final-data": {"main": [[{"node": "update_core_dump", "type": "main", "index": 0}]]}, "update_core_dump": {"main": [[{"node": "Job-Matching-Agent🤖", "type": "main", "index": 0}]]}, "Job-Matching-Agent🤖": {"main": [[{"node": "update-final-advice", "type": "main", "index": 0}]]}, "update-final-advice": {"main": [[{"node": "sort-by-highest-match", "type": "main", "index": 0}]]}, "sort-by-highest-match": {"main": [[{"node": "select-top-matches", "type": "main", "index": 0}]]}, "select-top-matches": {"main": [[{"node": "send-email", "type": "main", "index": 0}]]}, "check-core-dump": {"main": [[{"node": "backdate-filter-1", "type": "main", "index": 0}]]}, "check-final-advice": {"main": [[{"node": "backdate-filter-2", "type": "main", "index": 0}]]}, "backdate-filter-1": {"main": [[{"node": "clean-data-1", "type": "main", "index": 0}]]}, "backdate-filter-2": {"main": [[{"node": "clean-data-2", "type": "main", "index": 0}]]}, "clean-data-1": {"main": [[{"node": "clear-past-records-1", "type": "main", "index": 0}]]}, "clean-data-2": {"main": [[{"node": "clear-past-records-2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0594655d-f484-41ed-8a7a-544108b47ac3", "meta": {"instanceId": "25fec5c524c2b8e915ff528d2056970a3ef84a66427a602a175c1c0afe2326d4"}, "id": "nTxDGCDA2yFwySbV", "tags": [{"createdAt": "2025-02-25T14:44:59.395Z", "updatedAt": "2025-02-25T14:44:59.395Z", "id": "KZ09pZRCG3nBSU4o", "name": "koonai🦝"}]}