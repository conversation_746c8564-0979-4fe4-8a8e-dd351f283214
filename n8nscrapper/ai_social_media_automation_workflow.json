{"`name`": "AI Social Media Automation", "`nodes`": [{"`id`": "1", "`name`": "Schedule Trigger", "`type`": "n8n-nodes-base.cron", "`typeVersion`": 1, "`position`": [100, 300], "`parameters`": {"`triggerTimes`": [{"`mode`": "everyHour"}]}}, {"`id`": "2", "`name`": "Execute Trigger", "`type`": "n8n-nodes-base.manualTrigger", "`typeVersion`": 1, "`position`": [100, 150], "`parameters`": {}}, {"`id`": "3", "`name`": "Google Sheets Trigger", "`type`": "n8n-nodes-base.googleSheetsTrigger", "`typeVersion`": 1, "`position`": [100, 450], "`parameters`": {"`sheetId`": "your_google_sheet_id", "`range`": "Sheet1!A:B"}}, {"`id`": "4", "`name`": "Generate Text", "`type`": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "`typeVersion`": 1, "`position`": [400, 250], "`parameters`": {"`model`": "gpt-4", "`userPrompt`": "Create a viral social media post for topic: {{$json[\"topic\"]}}"}, "`credentials`": {"`openAiApi`": {"`id`": "openai_creds", "`name`": "OpenAI Account"}}}, {"`id`": "5", "`name`": "Generate Prompt", "`type`": "n8n-nodes-base.set", "`typeVersion`": 1, "`position`": [600, 250], "`parameters`": {"`values`": {"`string`": [{"`name`": "prompt", "`value`": "Generate an image for {{$json[\"text\"]}}"}]}}}, {"`id`": "6", "`name`": "OpenAI Generates Image", "`type`": "n8n-nodes-base.httpRequest", "`typeVersion`": 1, "`position`": [800, 250], "`parameters`": {"`url`": "https://api.openai.com/v1/images/generations", "`method`": "POST", "`bodyParametersUi`": {"`parameter`": [{"`name`": "prompt", "`value`": "{{$json[\"prompt\"]}}"}, {"`name`": "n", "`value`": "1"}, {"`name`": "size", "`value`": "1024x1024"}]}, "`authentication`": "headerAuth", "`headerParametersUi`": {"`parameter`": [{"`name`": "Authorization", "`value`": "Bearer YOUR_API_KEY"}]}}}, {"`id`": "7", "`name`": "Spit Out", "`type`": "n8n-nodes-base.noOp", "`typeVersion`": 1, "`position`": [1000, 250], "`parameters`": {}}, {"`id`": "8", "`name`": "Telegram", "`type`": "n8n-nodes-base.telegram", "`typeVersion`": 1, "`position`": [1200, 100], "`parameters`": {"`chatId`": "YOUR_CHAT_ID", "`text`": "{{$json[\"text\"]}}"}, "`credentials`": {"`telegramApi`": {"`id`": "telegram_api_id", "`name`": "Telegram Account"}}}, {"`id`": "9", "`name`": "Facebook", "`type`": "n8n-nodes-base.httpRequest", "`typeVersion`": 1, "`position`": [1200, 200], "`parameters`": {"`url`": "https://graph.facebook.com/v12.0/YOUR_PAGE_ID/feed", "`method`": "POST", "`bodyParametersUi`": {"`parameter`": [{"`name`": "message", "`value`": "{{$json[\"text\"]}}"}, {"`name`": "access_token", "`value`": "YOUR_FB_ACCESS_TOKEN"}]}}}, {"`id`": "10", "`name`": "LinkedIn", "`type`": "n8n-nodes-base.httpRequest", "`typeVersion`": 1, "`position`": [1200, 300], "`parameters`": {"`url`": "https://api.linkedin.com/v2/ugcPosts", "`method`": "POST", "`bodyParametersUi`": {"`parameter`": [{"`name`": "author", "`value`": "urn:li:person:YOUR_ID"}, {"`name`": "lifecycleState", "`value`": "PUBLISHED"}, {"`name`": "specificContent", "`value`": "{\"com.linkedin.ugc.ShareContent\":{\"shareCommentary\":{\"text\":\"{{$json[\"text\"]}}\"},\"shareMediaCategory\":\"NONE\"}}"}, {"`name`": "visibility", "`value`": "{\"com.linkedin.ugc.MemberNetworkVisibility\":\"PUBLIC\"}"}]}, "`headerParametersUi`": {"`parameter`": [{"`name`": "Authorization", "`value`": "Bearer YOUR_LI_TOKEN"}]}}}, {"`id`": "11", "`name`": "X", "`type`": "n8n-nodes-base.httpRequest", "`typeVersion`": 1, "`position`": [1200, 400], "`parameters`": {"`url`": "https://api.twitter.com/2/tweets", "`method`": "POST", "`bodyParametersUi`": {"`parameter`": [{"`name`": "text", "`value`": "{{$json[\"text\"]}}"}]}, "`headerParametersUi`": {"`parameter`": [{"`name`": "Authorization", "`value`": "Bearer YOUR_TWITTER_BEARER_TOKEN"}]}}}], "`connections`": {"`Schedule Trigger`": {"`main`": [["Generate Text"]]}, "`Execute Trigger`": {"`main`": [["Generate Text"]]}, "`Google Sheets Trigger`": {"`main`": [["Generate Text"]]}, "`Generate Text`": {"`main`": [["Generate Prompt"]]}, "`Generate Prompt`": {"`main`": [["OpenAI Generates Image"]]}, "`OpenAI Generates Image`": {"`main`": [["Spit Out"]]}, "`Spit Out`": {"`main`": [["Telegram"], ["Facebook"], ["LinkedIn"], ["X"]]}}}