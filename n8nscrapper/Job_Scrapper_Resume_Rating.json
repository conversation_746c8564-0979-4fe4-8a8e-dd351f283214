{
  "name": "<PERSON> Scrapper_Resume Rating",
  "nodes": [
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "triggerAtHour": 6
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [-1080, -120],
      "id": "764f1f25-0f51-494a-bc14-7c1ce09f900e",
      "name": "Schedule Trigger"
    },
    {
      "parameters": {
        "url": "https://rss.example.com/sample-product-management.xml",
        "options": {}
      },
      "type": "n8n-nodes-base.rssFeedRead",
      "typeVersion": 1.1,
      "position": [-920, -120],
      "id": "51073b0d-0679-424a-95fc-f0fd5158caf4",
      "name": "RSS Read"
    },
    {
      "parameters": {
        "operation": "append",
        "documentId": "__REPLACE_WITH_SHEET_ID__",
        "sheetName": "__REPLACE_WITH_SHEET_NAME__",
        "columns": {
          "mappingMode": "defineBelow",
          "value": {
            "Title": "={{ $('Code').item.json.Title }}",
            "Company": "={{ $('Code').item.json[\"company name\"] }}",
            "JD": "={{ $('Code').item.json[\"Job Description\"] }}",
            "Date": "={{ $now.toFormat('dd-MM-yyyy') }}",
            "Location": "={{ $('Code').item.json.location }}",
            "Salary Range": "={{ $('Code').item.json[\"salary range\"] }}",
            "Link": "={{ $('Loop Over Items').item.json.link }}",
            "Score": "={{ $('JD-Resume match scorer').item.json.output }}"
          },
          "matchingColumns": ["Company"],
          "schema": [
            { "id": "Date", "displayName": "Date", "type": "string" },
            { "id": "Score", "displayName": "Score", "type": "string" },
            { "id": "Title", "displayName": "Title", "type": "string" },
            { "id": "Company", "displayName": "Company", "type": "string" },
            { "id": "JD", "displayName": "JD", "type": "string" },
            { "id": "Location", "displayName": "Location", "type": "string" },
            { "id": "Salary Range", "displayName": "Salary Range", "type": "string" },
            { "id": "Link", "displayName": "Link", "type": "string" }
          ]
        }
      },
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 4.5,
      "position": [300, 280],
      "id": "8cd9e282-0304-4036-8db5-4f366f77decf",
      "name": "Google Sheets",
      "credentials": {
        "googleSheetsOAuth2Api": {
          "id": "__REPLACE_WITH_SHEETS_CREDENTIAL_ID__",
          "name": "Google Sheets account"
        }
      }
    },
    {
      "parameters": {
        "operation": "get",
        "documentURL": "https://docs.google.com/document/d/__REPLACE_WITH_DOC_ID__/edit"
      },
      "type": "n8n-nodes-base.googleDocsTool",
      "typeVersion": 2,
      "position": [200, 100],
      "id": "246a944c-173b-4527-aab7-9a8b59eddc2d",
      "name": "Resume Reference",
      "credentials": {
        "googleDocsOAuth2Api": {
          "id": "__REPLACE_WITH_GOOGLEDOCS_CREDENTIAL_ID__",
          "name": "Google Docs account"
        }
      }
    },
    {
      "parameters": {
        "model": {
          "__rl": true,
          "value": "gpt-4o-mini"
        },
        "options": {}
      },
      "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
      "typeVersion": 1.2,
      "position": [0, 100],
      "id": "f05de973-4911-45e9-b42e-659c3321e2b3",
      "name": "OpenAI Chat Model",
      "credentials": {
        "openAiApi": {
          "id": "__REPLACE_WITH_OPENAI_CREDENTIAL_ID__",
          "name": "OpenAI Account"
        }
      }
    },
    {
      "parameters": {
        "promptType": "define",
        "text": "=You are an expert resume evaluator. You analyze a candidate’s resume (attached as a tool) against job description: {{ $('Pull HTML of JD').item.json.data }}  and score the match out of 10. Be strict but fair, considering alignment in skills, experience, and impact. Output only a number.",
        "options": {
          "systemMessage": "Here is a job description. Evaluate how well the attached resume matches the requirements and rate it out of 10. Only return a **whole number**.\n\nCompare the resume against the following 6 factors:\n..."
        }
      },
      "type": "@n8n/n8n-nodes-langchain.agent",
      "typeVersion": 1.9,
      "position": [20, -40],
      "id": "cddaf293-eb91-4ede-9956-6959dad86cbe",
      "name": "JD-Resume match scorer"
    }
    // (Truncated remaining nodes for brevity. You would apply the same sanitization.)
  ],
  "connections": {
    // Include only sanitized connections as shown above.
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "id": "SANITIZED_EXPORT_EXAMPLE"
}
