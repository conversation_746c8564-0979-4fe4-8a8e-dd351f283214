{"name": "Chiropractor Voice AI Scheduler with AI Agent & Error Handling", "nodes": [{"parameters": {}, "id": "error-trigger", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [100, 300]}, {"parameters": {"subject": "Workflow Error Alert", "text": "Workflow {{$workflow.name}} encountered an error in node {{$node.name}}. Message: {{$error.message}}", "toEmail": "<EMAIL>", "fromEmail": "<EMAIL>"}, "id": "error-email", "name": "Send Error <PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [300, 300], "credentials": {"smtp": {"id": "smtp-cred-id", "name": "SMTP Credential"}}}, {"parameters": {"httpMethod": "POST", "path": "appointment-request"}, "id": "webhook-entry", "name": "Receive Call Info", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [300, 100]}, {"parameters": {"functionCode": "const data = $json.body || $json;\nreturn [{ json: {\n  name: data.name || 'Unknown',\n  phone: data.phone || '',\n  reason: data.reason || '',\n  preferredTime: data.preferredTime || '',\n  timestamp: new Date().toISOString()\n}}];"}, "id": "data-cleaner", "name": "Normalize Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 100]}, {"parameters": {"sheetId": "{{ $env.SHEET_ID }}", "range": "Appointments!A1", "valueInputMode": "RAW", "options": {}, "values": "={{ [\n  $json.name,\n  $json.phone,\n  $json.reason,\n  $json.preferredTime,\n  $json.timestamp\n] }}"}, "id": "google-sheets-log", "name": "Log to Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [700, 100], "credentials": {"googleApi": {"id": "your-credential-id", "name": "Google Sheets Credential"}}}, {"parameters": {"resource": "message", "operation": "sendMessage", "chatId": "@yourClinicBot", "text": "New appointment request from {{$json.name}} ({{$json.phone}}). Reason: {{$json.reason}}. Preferred Time: {{$json.preferredTime}}."}, "id": "telegram-alert", "name": "Notify Staff", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [900, 100], "credentials": {"telegramApi": {"id": "your-telegram-id", "name": "Telegram API"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "New Chiro Appointment Request", "text": "You have a new appointment request from {{$json.name}}. Contact: {{$json.phone}}. Reason: {{$json.reason}}. Preferred Time: {{$json.preferredTime}}."}, "id": "email-reception", "name": "Email Reception", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1100, 100], "credentials": {"smtp": {"id": "smtp-cred-id", "name": "SMTP Credential"}}}, {"parameters": {"text": "={{ $json.reason }}", "options": {"systemMessage": "You are a friendly and helpful AI receptionist for a chiropractic clinic. Re<PERSON>ond only with a friendly summary and request confirmation of the proposed appointment.", "promptType": "define"}}, "id": "ai-agent", "name": "AI Agent Response", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [900, 250]}], "connections": {"error-trigger": {"main": [[{"node": "error-email", "type": "main", "index": 0}]]}, "webhook-entry": {"main": [[{"node": "data-cleaner", "type": "main", "index": 0}]]}, "data-cleaner": {"main": [[{"node": "google-sheets-log", "type": "main", "index": 0}]]}, "google-sheets-log": {"main": [[{"node": "telegram-alert", "type": "main", "index": 0}, {"node": "email-reception", "type": "main", "index": 0}, {"node": "ai-agent", "type": "main", "index": 0}]]}}}