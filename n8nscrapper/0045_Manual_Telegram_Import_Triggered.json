{"id": "110", "name": "Get SSL Certificate", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 290], "parameters": {}, "typeVersion": 1}, {"name": "Create Domain Item", "type": "n8n-nodes-base.functionItem", "position": [450, 290], "parameters": {"functionCode": "item.domain = \"n8n.io\";\nreturn item;"}, "typeVersion": 1}, {"name": "Get SSL Certificate", "type": "n8n-nodes-base.uproc", "position": [650, 290], "parameters": {"tool": "getDomainCertificate", "group": "internet", "domain": "= {{$node[\"Create Domain Item\"].json[\"domain\"]}}", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}, {"name": "Send Expired Alarm", "type": "n8n-nodes-base.telegram", "position": [1070, 270], "parameters": {"text": "=The certificate of the domain {{$node[\"Create Domain Item\"].json[\"domain\"]}} has expired!", "chatId": "-1415703867", "additionalFields": {}}, "credentials": {"telegramApi": "test killia bot"}, "typeVersion": 1}, {"name": "Certificate  has  expired?", "type": "n8n-nodes-base.if", "position": [840, 290], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Get SSL Certificate\"].json[\"message\"][\"valid\"]+\"\"}}", "value2": "false"}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Create Domain Item": {"main": [[{"node": "Get SSL Certificate", "type": "main", "index": 0}]]}, "Get SSL Certificate": {"main": [[{"node": "Certificate  has  expired?", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Create Domain Item", "type": "main", "index": 0}]]}, "Certificate  has  expired?": {"main": [[{"node": "Send Expired Alarm", "type": "main", "index": 0}]]}}}