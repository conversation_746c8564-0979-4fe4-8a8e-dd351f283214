{"name": "AI LOFI Generator", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-440, 20], "id": "bb3f38c5-fcd1-446e-8179-24cef52c3283", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"promptType": "define", "text": "Respond to this prompt according to the instructions", "hasOutputParser": true, "options": {"systemMessage": "You are a helpful AI assistant designed to generate creative assets for a lo-fi animated music video using Runway. Your role includes:\n\n---\n\n#### 1. **Generate a Lo-fi Music Theme**\n\n* Always use the **lo-fi music genre** (chill, relaxing, beat-driven).\n* Select a **theme** such as **love, travel, nostalgia, rainy days, solitude, dreams**, etc.\n* Define the **tempo** (slow, medium) and the **mood** (e.g., mellow, cozy, wistful, romantic).\n*Generate the prompt to generate the lofi music based on the title\n\n---\n\n#### 2. **Write Original Lo-fi Style Lyrics**\n\n* Compose **1–2 short verses and a chorus**, with simple, relatable lines.\n* Lyrics should match the theme and maintain a reflective or dreamy tone.\n* Keep it subtle, emotional, and atmospheric—ideal for lo-fi visuals.\n\n---\n\n#### 3. **Generate a 200 character Prompt for a Cartoonized Animated Character (Lo-fi Girl Inspired)**\n\n* Create a **female character** styled similarly to the classic *Lo-fi Girl*:\n\n  * Cozy indoor setting with warm lighting\n  * Dressed in casual, comfy clothes (e.g., oversized sweater, scarf, headphones)\n  * Engaged in relaxing tasks (e.g., writing, sketching, daydreaming)\n  * Surrounded by soft, lived-in objects (books, cat, desk lamp, window view of city at night)\n* Describe her **look, clothing, hair, and accessories**, plus the **animation style** (e.g., Studio Ghibli or hand-drawn aesthetic).\n* Mention ambiance: **soft glowing lights, animated night city outside the window, lo-fi dreaminess**.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-200, 20], "id": "4c1692b7-bc12-47f1-8a51-a4ba305a1ef5", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-300, 260], "id": "a96ceed4-5c9b-40f2-92a8-22fb8b2eb0e6", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "MMw5WkceTrbujzcn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $workflow.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-140, 260], "id": "55f47195-4e4c-4d9e-b674-53ca0457f45c", "name": "Simple Memory"}, {"parameters": {"jsonSchemaExample": "{\n  \"music_theme\": {\n    \"title\": \"Letters in the Rain\",\n    \"genre\": \"Lo-fi\",\n    \"theme\": \"Love\",\n    \"tempo\": \"Slow to Medium\",\n    \"mood\": \"Warm, reflective, romantic\",\n    \"description\": \"A rainy evening filled with thoughts of a loved one, scribbling memories into a journal while lo-fi beats play softly.\"\n  },\n  \"lyrics\": {\n    \"full\": \"Coffee’s cold, the sky is grey\\nThinking 'bout you every day\\nLittle notes I never send\\nFolded words I can't pretend\\n\\nLove in lines I never say\\nDrift with lo-fi beats and rain\\nIn my mind, you’re here again\\nDancing in this soft refrain\"\n  },\n  \"animation_prompt\": \"Create a hand-drawn, Studio Ghibli-inspired animated character: a cartoonized girl with brown hair in a loose bun, wearing a forest green oversized sweater, red scarf, and large headphones. She is seated at a cozy wooden desk beside a rainy window at night, writing thoughtfully in her journal. A sleeping ginger cat rests on the windowsill next to a small potted plant. The room is softly lit by a warm amber desk lamp, with a glowing cityscape visible outside. Gentle rain effects on the window, flickering lamp, and subtle movements like writing and cat tail flicking bring the scene to life. The animation should feel peaceful, reflective, and cozy, ideal for a lo-fi love music video.\"\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [20, 260], "id": "dd9066a0-4b36-4f21-9105-0c6e87fc35cf", "name": "Structured Output Parser"}, {"parameters": {"assignments": {"assignments": [{"id": "00d0d22a-a682-41d3-b3d0-73dfec09d34f", "name": "Music Genre", "value": "={{ $json.output.music_theme.genre }}", "type": "string"}, {"id": "02568b2a-6eed-4480-a7f2-fadb8d865b07", "name": "Lyrics for music", "value": "={{ $json.output.lyrics.full }}", "type": "string"}, {"id": "9779071f-d8e3-4ab7-8e74-4dcfaca7066d", "name": "Date", "value": "={{ $now }}", "type": "string"}, {"id": "38d0fe0b-e81b-4c83-b2a2-a49fab53c838", "name": "Animation prompt", "value": "={{ $json.output.animation_prompt }}", "type": "string"}, {"id": "318cafb0-c134-4cf4-8b44-f982d9eef29e", "name": "Title", "value": "={{ $json.output.music_theme.title }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [160, 20], "id": "4b4c6c51-0adf-466d-a831-c264a8a90e0e", "name": "<PERSON>"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1NiYIdam_cOJGLZY0sRy5gnmdQYifD4bVU40uDanDeQ0", "mode": "list", "cachedResultName": "<PERSON><PERSON>-Music tracker", "cachedResultUrl": ""}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NiYIdam_cOJGLZY0sRy5gnmdQYifD4bVU40uDanDeQ0/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Date": "={{ $json.Date }}", "Music Title": "={{ $json.Title }}", "Animation prompt": "={{ $json['Animation prompt'] }}", "Lyrics": "={{ $json['Lyrics for music'] }}"}, "matchingColumns": ["Date"], "schema": [{"id": "Date", "displayName": "Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Music Title", "displayName": "Music Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Animation prompt", "displayName": "Animation prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Lyrics", "displayName": "Lyrics", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [380, 20], "id": "9c371a43-0ed2-4858-b5f7-6187dd357dc0", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "EV69pUdGupZzVOz4", "name": "Google Sheets account 2"}}}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": ""}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json['Animation prompt'] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"sampleCount\": 1\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 20], "id": "5cff666b-602e-4e7a-912d-c67f893794c6", "name": "HTTP Request"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {"fileName": "Image"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [820, 20], "id": "f66338de-0efb-4945-b009-7985b5f1b9f9", "name": "Convert to File"}, {"parameters": {"name": "={{ $('Edit Fields').item.json.Title }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1vVCa0XuNmiz8qclNiRf17v3CVmAiKlrx", "mode": "list", "cachedResultName": "Lofi video images", "cachedResultUrl": ""}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1040, 20], "id": "023865b4-6805-4aea-bad1-b0902dc54cfd", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"operation": "share", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "permissionsUi": {"permissionsValues": {"role": "reader", "type": "anyone", "allowFileDiscovery": true}}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1260, 20], "id": "f3fbb531-552d-4bf5-b823-bd3bc66d1c9a", "name": "Google Drive1", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1700, 20], "id": "40f630ca-3ae1-4e46-99d1-bc95fef3deb6", "name": "Wait", "webhookId": "85445dc9-7ea5-44c9-9b6e-d4dd27675df2"}, {"parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}, {"name": "X-Runway-Version", "value": "2024-11-06"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1920, 20], "id": "********-f670-4376-9130-22122c3a3f57", "name": "Get details of generated video"}, {"parameters": {"method": "POST", "url": "https://api.dev.runwayml.com/v1/image_to_video", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}, {"name": "X-Runway-Version", "value": "2024-11-06"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $('Google Drive').item.json.webContentLink }}"}, {"name": "seed", "value": "4294967295"}, {"name": "model", "value": "gen3a_turbo"}, {"name": "promptText", "value": "={{ $('Edit Fields').item.json['Animation prompt'] }}"}, {"name": "duration", "value": "5"}, {"name": "ratio", "value": "1280:768"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1500, 20], "id": "1e5e0c14-b34a-456b-9eba-3e4b839b2fd7", "name": "Generate videos 1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ab70af23-8e0e-42d9-b1d5-24ea6468e467", "leftValue": "={{ $json.status }}", "rightValue": "SUCCEEDED", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [2140, 20], "id": "7e68da73-ac1a-4ec3-a1c1-400cb9a43db1", "name": "If"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2320, 220], "id": "e6d35957-7bd0-4e80-ada9-8594d6ba1acf", "name": "Wait1", "webhookId": "d74fc8a7-0161-4c34-ba87-d299d80e13ab"}, {"parameters": {"url": "={{ $json.output[0] }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "videofile"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2360, -80], "id": "7d5c8d78-00ba-4fb6-8238-63235f8ef1bd", "name": "HTTP Request1"}, {"parameters": {"operation": "write", "fileName": "/tmp/video.mp4", "dataPropertyName": "videofile", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2580, -80], "id": "645212af-4b0e-4c05-9770-74f822c08398", "name": "Read/Write Files from Disk"}, {"parameters": {"assignments": {"assignments": [{"id": "5f244076-c91d-40a4-afbd-648ac1d3ad5c", "name": "musicgen_prompt", "value": "={{ $json.output.music_theme.description }}", "type": "string"}, {"id": "87d322ae-4a51-4dcc-8aeb-e8de931e109d", "name": "Music duration", "value": 15, "type": "number"}, {"id": "65b71749-e769-481a-ab48-265b0154445c", "name": "seed", "value": "42", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [580, 460], "id": "95f6e283-ca8c-4346-a5a8-94db6e807000", "name": "Edit Fields1"}, {"parameters": {"method": "POST", "url": "https://api.segmind.com/v1/meta-musicgen-medium", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": ""}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.musicgen_prompt }}"}, {"name": "duration", "value": "={{ $json['Music duration'] }}"}, {"name": "seed", "value": "={{ $json.seed }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 460], "id": "c0beb333-60bc-41ff-a1ab-68c12741ddcd", "name": "HTTP Request2"}, {"parameters": {"operation": "write", "fileName": "/tmp/music.mp3", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1020, 460], "id": "9feb3ae4-df33-4c06-a8d0-78aaae851c5f", "name": "Read/Write Files from Disk1"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [2820, 480], "id": "a66c6f23-739d-4ca1-95b3-5f10d2acd480", "name": "<PERSON><PERSON>"}, {"parameters": {"command": "ffmpeg -y -stream_loop -1 -i /tmp/video.mp4 -i /tmp/music.mp3 -shortest -c:v libx264 -preset veryfast -crf 23 -c:a aac -b:a 192k -movflags +faststart /tmp/final_lofi_video.mp4"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3040, 480], "id": "bc9234e8-4523-4b98-91bc-5ecbb1c9cdcf", "name": "Execute Command"}, {"parameters": {"fileSelector": "/tmp/final_lofi_video.mp4", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3260, 480], "id": "be24886f-5fab-42d9-9553-b17b72278654", "name": "Read/Write Files from Disk2"}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('Edit Fields').item.json['Music title'] }}", "regionCode": "CA", "options": {"description": "={{ $('Edit Fields').item.json['Music Description'] }}", "privacyStatus": "unlisted"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [3480, 480], "id": "4f149031-3761-4687-bf22-cd8c460dd9c4", "name": "YouTube", "credentials": {"youTubeOAuth2Api": {"id": "jnkGw3zbfMMg9MCG", "name": "YouTube account AI With Lenny"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ $('Edit Fields').item.json.Title }}", "message": "=Hello ,your lofi music video has been unlisted on youtube here is the link: https://www.youtube.com/watch?v={{ $json.uploadId }}", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [3700, 480], "id": "84dddc98-58c0-4471-9ebb-66978888bf18", "name": "Gmail", "webhookId": "2ed9df8b-ffcf-4d19-a04f-0db465180c9f", "credentials": {"gmailOAuth2": {"id": "u2GrwzDNu3KRPkqV", "name": "Gmail account"}}}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "AI Agent": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}, {"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Google Drive1", "type": "main", "index": 0}]]}, "Google Drive1": {"main": [[{"node": "Generate videos 1", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get details of generated video", "type": "main", "index": 0}]]}, "Generate videos 1": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Get details of generated video": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Read/Write Files from Disk2", "type": "main", "index": 0}]]}, "Read/Write Files from Disk2": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "YouTube": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "72d76009-19ae-4957-8b8c-da8227cd9df8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f21f53385ae396fcb0c1b69950e1ec16f2dcc4ddca34a170466835249ec1c42c"}, "id": "jhu0xQgeZuhWuqvZ", "tags": []}