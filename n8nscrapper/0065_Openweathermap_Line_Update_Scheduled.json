{"id": "114", "name": "Send daily weather updates via a message in Line", "nodes": [{"name": "Line", "type": "n8n-nodes-base.line", "position": [890, 380], "parameters": {"message": "=Hey! The temperature outside is {{$node[\"OpenWeatherMap\"].json[\"main\"][\"temp\"]}}°C.", "additionalFields": {}}, "credentials": {"lineNotifyOAuth2Api": "line-credentials"}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [490, 380], "parameters": {"triggerTimes": {"item": [{"hour": 9}]}}, "typeVersion": 1}, {"name": "OpenWeatherMap", "type": "n8n-nodes-base.openWeatherMap", "position": [690, 380], "parameters": {"cityName": "berlin"}, "credentials": {"openWeatherMapApi": "owm"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Cron": {"main": [[{"node": "OpenWeatherMap", "type": "main", "index": 0}]]}, "OpenWeatherMap": {"main": [[{"node": "Line", "type": "main", "index": 0}]]}}}