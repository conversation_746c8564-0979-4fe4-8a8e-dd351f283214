{"name": "Multi Ai Agent Node", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [368, -1328], "id": "0d23d5bf-1373-4eb5-bca4-4fc4631c3bad", "name": "When chat message received", "webhookId": "f8a44d57-f3ac-4561-a23a-92767bba83e8"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1200, -1056], "id": "fd2f8c42-d436-4426-9423-38da3085592d", "name": "Simple Memory"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [864, -624], "id": "384d71e6-b4be-40c1-adbc-15ba1d190248", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "6dTFypqHZvE8ET3Z", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [256, -624], "id": "ee5fc0cc-2935-4472-98d3-cd1667357993", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "6dTFypqHZvE8ET3Z", "name": "OpenAi account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "app3FicX0eHhX6vOT", "mode": "list", "cachedResultName": "Contact - demo", "cachedResultUrl": "https://airtable.com/app3FicX0eHhX6vOT"}, "table": {"__rl": true, "value": "tbljMFXSMrR9HIibW", "mode": "list", "cachedResultName": "Contact", "cachedResultUrl": "https://airtable.com/app3FicX0eHhX6vOT/tbljMFXSMrR9HIibW"}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2.1, "position": [416, -624], "id": "350891e0-ab93-4d6a-a4eb-0681a29fca99", "name": "contact", "credentials": {"airtableTokenApi": {"id": "XkNzce2k5M9iu7lT", "name": "Airtable"}}}, {"parameters": {"toolDescription": "contract - use this agent to get contact details in particular email address for sending email", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [224, -832], "id": "60f9f179-ec8d-4dc8-b0ba-484608985c16", "name": "contact agent"}, {"parameters": {"operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "simple": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Simplify', ``, 'boolean') }}", "filters": {}, "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1024, -624], "id": "b47a2fbf-5207-44b2-80c8-888758ad78ff", "name": "Get", "webhookId": "d38c9d0d-31bd-40a8-826c-96006bdf30a7", "credentials": {"gmailOAuth2": {"id": "m6lg39f10QCQTWLI", "name": "Gmail account"}}}, {"parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [1152, -624], "id": "1c50b5c4-dc8c-49a4-a070-d2b14b5df968", "name": "Send", "webhookId": "1f44b2c4-fc12-4b45-9c34-885d1e975c8e", "credentials": {"gmailOAuth2": {"id": "m6lg39f10QCQTWLI", "name": "Gmail account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [576, -624], "id": "651edbb6-afbb-462b-aca0-d1a521a65c79", "name": "OpenAI Chat Model3", "credentials": {"openAiApi": {"id": "6dTFypqHZvE8ET3Z", "name": "OpenAi account"}}}, {"parameters": {"options": {"systemMessage": "=You are a helpful assistant. You have this tool you can use for user query.\n\nEmail Agent- Use this agent to read my email, draft an email or send an email.\ncontract agent - use this agent to get contact details in particular email address for sending email\nResearch tool - use this tool for conducting any research in the web\n\n\n{{ $now }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [624, -1328], "id": "e1af7b7b-6713-4b87-9f98-4485a6928315", "name": "Main Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [224, -1056], "id": "7a291b9a-f771-4654-914d-085176cb4967", "name": "GPT 4.1", "credentials": {"openAiApi": {"id": "6dTFypqHZvE8ET3Z", "name": "OpenAi account"}}}, {"parameters": {"content": "## LLM", "height": 192, "width": 208}, "type": "n8n-nodes-base.stickyNote", "position": [192, -1120], "typeVersion": 1, "id": "a4aef3ad-4bfd-4142-ab25-a49280929bde", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Memory", "height": 192, "width": 208, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [1104, -1120], "typeVersion": 1, "id": "692dad14-a2f6-4d94-a6d4-373cf39bf9b7", "name": "Sticky Note1"}, {"parameters": {"content": "## Calendar Agent", "height": 432, "width": 368, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [944, -912], "typeVersion": 1, "id": "bd492544-3168-426a-bf69-378ff2c6575d", "name": "Sticky Note2"}, {"parameters": {"content": "## Contact Agent", "height": 432, "width": 368, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [176, -912], "typeVersion": 1, "id": "7557aea4-afbd-4298-9033-a15dc01ade8f", "name": "Sticky Note3"}, {"parameters": {"content": "## Research Agent", "height": 432, "width": 368, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [560, -912], "typeVersion": 1, "id": "839b23ba-c93b-4eb0-a85a-f11a062e01cb", "name": "Sticky Note4"}, {"parameters": {"model": "sonar", "messages": {"message": [{"content": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('message0_Text', ``, 'string') }}"}]}, "options": {}, "requestOptions": {}}, "type": "n8n-nodes-base.perplexityTool", "typeVersion": 1, "position": [736, -624], "id": "cb2d0334-3368-48de-9867-fa9e6402f1c1", "name": "Perplexity", "credentials": {"perplexityApi": {"id": "5SrxQwph2fxNghIA", "name": "Perplexity"}}}, {"parameters": {"toolDescription": "Research tool - use this tool for conducting any research in the web.", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "Always use both tools for completing the research"}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [560, -832], "id": "7ee350b3-7722-4c26-91e8-392676<PERSON><PERSON>e", "name": "Research Agent"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [864, -832], "id": "883e5ce4-6bb1-4f90-babd-a5683d800b4f", "name": "Think"}, {"parameters": {"toolDescription": "Use this agent to read my email, draft an email or send an email.", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "Always signoff email as \"Aftab\""}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [992, -832], "id": "730bef8d-f4e7-49f7-b853-582f187ad208", "name": "Email Agent"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "Main Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Main Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Email Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "contact agent", "type": "ai_languageModel", "index": 0}]]}, "contact": {"ai_tool": [[{"node": "contact agent", "type": "ai_tool", "index": 0}]]}, "contact agent": {"ai_tool": [[{"node": "Main Agent", "type": "ai_tool", "index": 0}]]}, "Get": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "Send": {"ai_tool": [[{"node": "Email Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Research Agent", "type": "ai_languageModel", "index": 0}]]}, "GPT 4.1": {"ai_languageModel": [[{"node": "Main Agent", "type": "ai_languageModel", "index": 0}]]}, "Perplexity": {"ai_tool": [[{"node": "Research Agent", "type": "ai_tool", "index": 0}]]}, "Research Agent": {"ai_tool": [[{"node": "Main Agent", "type": "ai_tool", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "Main Agent", "type": "ai_tool", "index": 0}]]}, "Email Agent": {"ai_tool": [[{"node": "Main Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "775dd2a3-e95b-4eba-8132-89fa8f411a1e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b20c1a22249a77e3587acdad476e33a7feb1a098de595996887840c1c60c39ba"}, "id": "rC4i6cM6fRmPJ5aQ", "tags": []}