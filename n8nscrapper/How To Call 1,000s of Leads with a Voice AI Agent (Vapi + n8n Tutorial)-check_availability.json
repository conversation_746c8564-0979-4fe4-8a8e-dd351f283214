{"name": "check availability", "nodes": [{"parameters": {"httpMethod": "POST", "path": "f9847162-d95a-450d-984a-80014b299cc6", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "id": "33462e6e-63bf-4888-b101-a4daa8c89484", "name": "Webhook", "webhookId": "f9847162-d95a-450d-984a-80014b299cc6"}, {"parameters": {"promptType": "define", "text": "=Role: \n\nYou are an appointment availability checker. Your job is to get the requested time and use the calendar to check availability for the customer.\n\nThe current date and time is: {{ $now }}\n\nCustomer requested time is:\n{{ $json.body.message.toolCalls[0].function.arguments.time }}\n\nCalendar data:\nWorking hours 8am - 9pm\nLength of each slot: 1 hour\n\nBooking Process\n\n-Check if the customer’s requested time is available using the calendar\n-If the time is available let the AI model know & confirm\n-If the time is unavailable, suggest up to 2 alternative slots close to the requested time, by checking the availability again\n-If there are no slots upon rechecking within that day, search the next weekday from the current time and provide alternative slots\n\nResponse & output:\n\n-If the requested time is available confirm it by saying:\nE.g: The requested time of 10am on June 22nd is available, just to confirm would you like to book this?\n-If you have found no availability or unavailable in the requested time, suggest up to 3 alternative slots closest to the requested time.\n-If no slots exist within the day suggest 3 more times the next weekday.\n-Your output must be natural and conversational e.g. “Hey there, the time that you requested of 10am on June 22nd is available, just to confirm would you like to book this?”\n\nImportant notes:\n\n- You must not output your result in the past, aways check availability in the future, use the current time you have received and the requested data to help make that decision.\n-Your ability to follow these steps precisely is very crucial and can impact the business’s reputation.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [300, 0], "id": "b31e994e-ea4b-40ba-be3e-385981c307e8", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [140, 240], "id": "64510d5f-7306-446e-a74a-************", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "sYGWHRTm0aBGym7M", "name": "OpenAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"results\": [\n    {\n      \"toolCallId\": \"{{ $('Webhook').item.json.body.message.toolCalls[0].id }}\",\n      \"result\": \"{{ $json.output }}\"\n    }\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.3, "position": [660, 0], "id": "b0d048e8-f7cc-4aad-ae5c-4a593c6c3107", "name": "Respond to Webhook"}, {"parameters": {"resource": "calendar", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start_Time', ``, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End_Time', ``, 'string') }}", "options": {"timezone": {"__rl": true, "value": "Australia/Brisbane", "mode": "list", "cachedResultName": "Australia/Brisbane"}}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [340, 240], "id": "8f11d7c0-c36a-45e6-b155-a59270ed4326", "name": "Get availability in a calendar in Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "nGN7h9SIZXxERx6E", "name": "Google Calendar account"}}}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get availability in a calendar in Google Calendar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "803150f3-3894-48f8-92a1-ae930dccd3a0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5e83950c16f424931a11129d242bc57414727cd7495fa9f2a8ca68a3f953658d"}, "id": "SGmASC0m3W1WKXS1", "tags": []}