{"name": "AI_Call_Center_Automation_v1", "nodes": [{"id": "1", "name": "[Trigger]_Incoming_Call", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 100], "parameters": {"resource": "webhook", "operation": "receive", "path": "call-center-incoming", "responseCode": 200}}, {"id": "2", "name": "[Logic]_Categorize_Lead", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [400, 100], "parameters": {"functionCode": "const callData = $input.first().json;\nlet category = 'unknown';\nif (callData.status === 'answered') {\n  category = 'hot_lead';\n} else if (callData.status === 'declined') {\n  category = 'cold_lead';\n} else {\n  category = 'callback_needed';\n}\nreturn [{ json: { ...callData, category } }];"}}, {"id": "3", "name": "[Filter]_Hot_Lead", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 50], "parameters": {"conditions": {"string": [{"value1": "={{$json.category}}", "operation": "equal", "value2": "hot_lead"}]}}}, {"id": "4", "name": "[Filter]_Cold_Lead", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 150], "parameters": {"conditions": {"string": [{"value1": "={{$json.category}}", "operation": "equal", "value2": "cold_lead"}]}}}, {"id": "5", "name": "[Filter]_Callback_Needed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [600, 250], "parameters": {"conditions": {"string": [{"value1": "={{$json.category}}", "operation": "equal", "value2": "callback_needed"}]}}}], "connections": {"[Trigger]_Incoming_Call": {"main": [[{"node": "[Logic]_Categorize_Lead", "type": "main", "index": 0}]]}, "[Logic]_Categorize_Lead": {"main": [[{"node": "[Filter]_Hot_Lead", "type": "main", "index": 0}], [{"node": "[Filter]_Cold_Lead", "type": "main", "index": 0}], [{"node": "[Filter]_Callback_Needed", "type": "main", "index": 0}]]}}, "active": false, "settings": {}}