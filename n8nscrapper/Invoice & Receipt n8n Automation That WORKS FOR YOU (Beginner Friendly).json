{"name": "Invoice Manager", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-1720, -1660], "id": "8eeae690-b9a3-4e78-8be8-764fba6e07ce", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"updates": ["*"], "additionalFields": {"download": true}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1740, -140], "id": "c5597f3d-6a72-43eb-a1b4-b26720b2afe9", "name": "<PERSON>eg<PERSON>", "webhookId": "f8fec15e-ac40-4d63-9e39-5f8f59132cc5", "credentials": {"telegramApi": {"id": "R1wYXQwVv2UlK8ZY", "name": "Telegram account"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=Receipt from {{ $json.vendor }}", "message": "=Attached is receipt from {{ $json.vendor }}", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{"property": "=data"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1120, -140], "id": "c49993bd-0f71-4e99-85dd-bd431389989a", "name": "Gmail5", "webhookId": "594575ce-ddc7-4bdf-bb46-278c60454274", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Vendor": "={{ $('Edit Fields').item.json.vendor }}", "Invoice Date": "={{ $('Edit Fields').item.json.receiptDate }}", "Invoice ID": "={{ $('Edit Fields').item.json.receiptId }}", "Category": "={{ $('Edit Fields').item.json.category }}", "Is Subscription": "=", "Amount": "={{ $('Edit Fields').item.json.amount }}", "Currency": "={{ $('Edit Fields').item.json.currency }}", "Invoice link": "={{ $json.webViewLink }}"}, "matchingColumns": [], "schema": [{"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Invoice Date", "displayName": "Invoice Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Invoice ID", "displayName": "Invoice ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Category", "displayName": "Category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Is Subscription", "displayName": "Is Subscription", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Amount", "displayName": "Amount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Invoice link", "displayName": "Invoice link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-920, 60], "id": "1a7ced33-88ce-4ccb-bf34-d531eaefc420", "name": "Google Sheets2", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini-2024-07-18", "mode": "list", "cachedResultName": "GPT-4O-MINI-2024-07-18"}, "text": "=You are an AI email receipt parser.\n\nAnalyze the following receipt details and extract key invoice information and return a JSON object in the exact format provided. \n\nReturn this JSON\n\n{\n  \"vendor\": \"[company/vendor name]\",\n  \"receiptId\": \"[invoice/reference number]\",\n  \"receiptDate\": \"YYYY-MM-DD\",\n  \"category\": \"[Software/Services/Utilities/Marketing/Office Supplies/Travel/Equipment/Other]\",\n  \"amount\": \"[numeric value only, no currency symbols]\",\n  \"currency\": \"[3-letter currency code]\"\n}\n\n\nFollow these rules:\n- Only return JSON.\n- Detect the vendor from receipt.\n- Extract amount and currency from totals or summary lines.\n- Parse the reference number from receipt.\n\nDon't add comments, don't add '''json and '''to your response", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1580, -40], "id": "3f497cae-4a52-4883-92ad-2618ea035abe", "name": "OpenAI3", "credentials": {"openAiApi": {"id": "1FYH9SozJhkXPr4w", "name": "OpenAi account"}}}, {"parameters": {"mode": "raw", "jsonOutput": "={{ $json.content }}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1420, -140], "id": "0c719eae-9ae3-40d5-97b8-836e4182ab82", "name": "<PERSON>"}, {"parameters": {"resource": "file", "fileId": "={{ $('Telegram Trigger').item.json.message.photo[0].file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1280, -40], "id": "********-40b9-43db-88ac-3c8921fb3a9b", "name": "Telegram", "webhookId": "8520f1dc-2fd3-46df-87bd-708f56a83914", "credentials": {"telegramApi": {"id": "R1wYXQwVv2UlK8ZY", "name": "Telegram account"}}}, {"parameters": {"name": "={{ $('Edit Fields').item.json.receiptId }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1lzLspLnAgDUJHR-t5spAIrnESft0dIt5", "mode": "list", "cachedResultName": "Invoices/Receipts", "cachedResultUrl": "https://drive.google.com/drive/folders/1lzLspLnAgDUJHR-t5spAIrnESft0dIt5"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-1120, 60], "id": "c4ca4bab-0e03-4ac2-8673-5e5a43280055", "name": "Google Drive2", "credentials": {"googleDriveOAuth2Api": {"id": "DzEW531EgrgUFlbl", "name": "Google Drive account"}}}, {"parameters": {"operation": "getAll", "returnAll": true, "simple": false, "filters": {"labelIds": ["INBOX"], "q": "=subject:(invoice OR \"payment has been received\" OR payment OR bill OR billing OR billed OR receipt OR statement OR \"payment confirmation\" OR \"amount due\" OR charge OR charged OR \"sales receipt\" OR \"order confirmation\" OR purchase OR transaction OR subscription OR renewal OR auto-renew OR membership OR \"plan renewed\" OR \"next billing date\" OR \"monthly invoice\")"}, "options": {"downloadAttachments": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1560, -1660], "id": "f3c3411c-7566-4a27-a17c-4890819a97f7", "name": "Gmail1", "webhookId": "9bf83357-73f6-4d22-9f1e-7879c4cdc0fb", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7559170a-6228-4025-a495-7e5fe35d4beb", "leftValue": "={{$binary?.[\"attachment_0\"]?.mimeType}}", "rightValue": "pdf", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1380, -1660], "id": "820031f5-d720-4248-b438-cc4bbf0b3243", "name": "If2"}, {"parameters": {"operation": "pdf", "binaryPropertyName": "attachment_0", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-1240, -1820], "id": "4d81b923-0e5a-4f3b-b46b-************", "name": "Extract from File2"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini-2025-04-14", "mode": "list", "cachedResultName": "GPT-4.1-MINI-2025-04-14"}, "messages": {"values": [{"content": "=You are an AI email invoice parser.\n\nAnalyze the following email details and determine if it is an invoice issued to <PERSON><PERSON>, j<PERSON><PERSON><PERSON>, janis skutanis or skutanis or lv-1010. You make sure that invoice with the same ID doesn't already exist in google sheets. If it is, extract key invoice information and return a JSON object in the exact format provided. IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n\nEmail Subject: {{ $('Gmail1').item.json.subject }}\nEmail Body: {{ $('Gmail1').item.json.text }}\nAttachment Text: {{ $json.text }}\n\nReturn this JSON if it is an invoice to <PERSON><PERSON>:\n\n{\n  \"isinvoice\": true,\n  \"subject\": \"[exact email subject]\",\n  \"vendor\": \"[company/vendor name]\",\n  \"invoiceId\": \"[invoice/reference number]\",\n  \"invoiceDate\": \"YYYY-MM-DD\",\n  \"category\": \"[Software/Services/Utilities/Marketing/Office Supplies/Travel/Equipment/Other]\",\n  \"isSubscription\": true,\n  \"amount\": \"[numeric value only, no currency symbols]\",\n  \"currency\": \"[3-letter currency code]\"\n}\n\nOtherwise, return:\n\n{\n  \"isvalid\": false\n}\n\nFollow these rules:\n- Only return one of the above JSONs.\n- Detect the vendor from signature, email address, or header.\n- Extract amount and currency from totals or summary lines.\n- Parse the invoice/reference number from subject or attachment.\n- If invoice is paid or it is confirmation of payment received then it should be \"isvalid\": true.\n- Match “invoice to Janis, jsautomates, janis skutanis or skutanis or lv-1010” via recipient line, billing details, or mention in body, invoices that are issued by me should be false.\n- If payment failed then return \"isvalid\": false.\n- IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n- If invoice has no value then return \"isvalid\": false.\n- If it is a reminder or past due return \"isvalid\": false\n\ncheck googlesheets if Invoice ID exists then set \"isinvoice\": false"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1100, -1820], "id": "8f469e5d-1c11-4dd0-8f23-5e10fcd6dd4b", "name": "OpenAI5", "credentials": {"openAiApi": {"id": "1FYH9SozJhkXPr4w", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fbab1392-683b-42bf-83ee-be7259eff692", "leftValue": "={{ $json.message.content.isinvoice }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-800, -1820], "id": "43945f38-5418-44c7-a7e6-edb62cd01d34", "name": "Filter4"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ $('Gmail1').item.json.subject }}", "message": "={{ $json.html }}", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{"property": "=attachment_0"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-540, -1940], "id": "03847ace-8a0b-48a6-90fd-27447bc1cdde", "name": "Gmail11", "webhookId": "594575ce-ddc7-4bdf-bb46-278c60454274", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"operation": "get", "messageId": "={{ $('Gmail1').item.json.id }}", "simple": false, "options": {"downloadAttachments": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-660, -1820], "id": "8b1c1df6-ca21-4a9d-8bde-b5a7a3acee10", "name": "Gmail12", "webhookId": "7fb46920-7bf7-4f57-89a8-22bc969b2220", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"inputDataFieldName": "attachment_0", "name": "={{ $('Filter4').item.json.message.content.invoiceId }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1lzLspLnAgDUJHR-t5spAIrnESft0dIt5", "mode": "list", "cachedResultName": "Invoices/Receipts", "cachedResultUrl": "https://drive.google.com/drive/folders/1lzLspLnAgDUJHR-t5spAIrnESft0dIt5"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-540, -1700], "id": "01128d20-d05b-4874-8a3f-340ab5188365", "name": "Google Drive3", "credentials": {"googleDriveOAuth2Api": {"id": "DzEW531EgrgUFlbl", "name": "Google Drive account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Vendor": "={{ $('OpenAI5').item.json.message.content.vendor }}", "Invoice Date": "={{ $('OpenAI5').item.json.message.content.invoiceDate }}", "Invoice ID": "={{ $('OpenAI5').item.json.message.content.invoiceId }}", "Category": "={{ $('OpenAI5').item.json.message.content.category }}", "Is Subscription": "={{ $('OpenAI5').item.json.message.content.isSubscription }}", "Amount": "={{ $('OpenAI5').item.json.message.content.amount }}", "Currency": "={{ $('OpenAI5').item.json.message.content.currency }}", "Invoice URL": "={{ $json.webViewLink }}"}, "matchingColumns": ["id"], "schema": [{"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice Date", "displayName": "Invoice Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice ID", "displayName": "Invoice ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Category", "displayName": "Category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Is Subscription", "displayName": "Is Subscription", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Amount", "displayName": "Amount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice URL", "displayName": "Invoice URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-320, -1800], "id": "158e370d-c2fc-4a56-b138-e7fea3c7d88c", "name": "Google Sheets5", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini-2025-04-14", "mode": "list", "cachedResultName": "GPT-4.1-MINI-2025-04-14"}, "messages": {"values": [{"content": "=You are an AI email invoice parser.\n\nAnalyze the following email details and determine if it is an invoice Issued to <PERSON><PERSON>, j<PERSON><PERSON><PERSON>, janis skutanis or skutanis or lv-1010. You make sure that invoice with the same ID doesn't already exist in google sheets. If it is, extract key invoice information and return a JSON object in the exact format provided. IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n\nEmail Subject: {{ $('Gmail1').item.json.subject }}\nEmail Body: {{ $('Gmail1').item.json.text }}\n\nReturn this JSON if it is an invoice to <PERSON><PERSON>:\n\n{\n  \"isinvoice\": true,\n  \"subject\": \"[exact email subject]\",\n  \"vendor\": \"[company/vendor name]\",\n  \"invoiceId\": \"[invoice/reference number]\",\n  \"invoiceDate\": \"YYYY-MM-DD\",\n  \"category\": \"[Software/Services/Utilities/Marketing/Office Supplies/Travel/Equipment/Other]\",\n  \"isSubscription\": true,\n  \"amount\": \"[numeric value only, no currency symbols]\",\n  \"currency\": \"[3-letter currency code]\"\n}\n\nOtherwise, return:\n\n{\n  \"isvalid\": false\n}\n\nFollow these rules:\n- Only return one of the above JSONs.\n- Detect the vendor from signature, email address, or header.\n- Extract amount and currency from totals or summary lines.\n- Parse the invoice/reference number from subject or attachment.\n- If invoice is paid or it is confirmation of payment received then it should be \"isvalid\": true.\n- Match “invoice to Janis, jsautomates, janis skutanis or skutanis or lv-1010” via recipient line, billing details, or mention in body, invoices that are issued by me should be false.\n- If payment failed then return \"isvalid\": false.\n- IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n- If invoice has no value then return \"isvalid\": false.\n- If it is a reminder or past due return \"isvalid\": false\ncheck googlesheets if Invoice ID exists then set \"isinvoice\": false"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1240, -1500], "id": "1fb5f181-b46c-4fae-9f70-33d2d6eb46c9", "name": "OpenAI6", "credentials": {"openAiApi": {"id": "1FYH9SozJhkXPr4w", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fbab1392-683b-42bf-83ee-be7259eff692", "leftValue": "={{ $json.message.content.isinvoice }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-940, -1440], "id": "********-1aa5-44e4-a468-92ec9daf17c0", "name": "Filter5"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ $('Gmail1').item.json.subject }}", "message": "={{ $('Gmail1').item.json.textAsHtml }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-800, -1500], "id": "a0e1eb94-e158-40bc-89a1-be1a6ae182b5", "name": "Gmail13", "webhookId": "594575ce-ddc7-4bdf-bb46-278c60454274", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Vendor": "={{ $('OpenAI6').item.json.message.content.vendor }}", "Invoice Date": "={{ $('OpenAI6').item.json.message.content.invoiceDate }}", "Invoice ID": "={{ $('OpenAI6').item.json.message.content.invoiceId }}", "Category": "={{ $('OpenAI6').item.json.message.content.category }}", "Is Subscription": "={{ $('OpenAI6').item.json.message.content.isSubscription }}", "Amount": "={{ $('OpenAI6').item.json.message.content.amount }}", "Currency": "={{ $('OpenAI6').item.json.message.content.currency }}", "Invoice URL": "="}, "matchingColumns": ["id"], "schema": [{"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice Date", "displayName": "Invoice Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice ID", "displayName": "Invoice ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Category", "displayName": "Category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Is Subscription", "displayName": "Is Subscription", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Amount", "displayName": "Amount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice URL", "displayName": "Invoice URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-660, -1440], "id": "adfebf26-27c5-4e45-9160-2f048ed4dba8", "name": "Google Sheets6", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {"labelIds": ["INBOX"]}, "options": {"downloadAttachments": true}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-1740, -700], "id": "b73ef29d-3a65-41e1-a353-66a4a8763579", "name": "Gmail Trigger1", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.6, "position": [-1100, -1680], "id": "1afe735b-8ca4-4982-a1f0-0fbba67fae06", "name": "Google Sheets4", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.6, "position": [-1260, -1360], "id": "f4212c04-2aa6-4182-bccb-849f89761cec", "name": "Google Sheets3", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7559170a-6228-4025-a495-7e5fe35d4beb", "leftValue": "={{$binary?.[\"attachment_0\"]?.mimeType}}", "rightValue": "pdf", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1540, -700], "id": "b0064d06-7abc-4467-95ff-30d3556d7f9b", "name": "If"}, {"parameters": {"operation": "pdf", "binaryPropertyName": "attachment_0", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-1360, -860], "id": "633f98d1-2dff-4342-ac77-f5c7cd02883a", "name": "Extract from File"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini-2025-04-14", "mode": "list", "cachedResultName": "GPT-4.1-MINI-2025-04-14"}, "messages": {"values": [{"content": "=You are an AI email invoice parser.\n\nAnalyze the following email details and determine if it is an invoice issued to <PERSON><PERSON>, jsa<PERSON>mates, janis skutanis or skutanis or lv-1010. You make sure that invoice with the same ID doesn't already exist in google sheets. If it is, extract key invoice information and return a JSON object in the exact format provided. IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n\nEmail Subject: {{ $('Gmail Trigger1').item.json.subject }}\nEmail Body: {{ $('Gmail Trigger1').item.json.text }}\nAttachment Text: {{ $json.text }}\n\nReturn this JSON if it is an invoice to <PERSON><PERSON>:\n\n{\n  \"isinvoice\": true,\n  \"subject\": \"[exact email subject]\",\n  \"vendor\": \"[company/vendor name]\",\n  \"invoiceId\": \"[invoice/reference number]\",\n  \"invoiceDate\": \"YYYY-MM-DD\",\n  \"category\": \"[Software/Services/Utilities/Marketing/Office Supplies/Travel/Equipment/Other]\",\n  \"isSubscription\": true,\n  \"amount\": \"[numeric value only, no currency symbols]\",\n  \"currency\": \"[3-letter currency code]\"\n}\n\nOtherwise, return:\n\n{\n  \"isvalid\": false\n}\n\nFollow these rules:\n- Only return one of the above JSONs.\n- Detect the vendor from signature, email address, or header.\n- Extract amount and currency from totals or summary lines.\n- Parse the invoice/reference number from subject or attachment.\n- If invoice is paid or it is confirmation of payment received then it should be \"isvalid\": true.\n- Match “invoice to Janis, jsautomates, janis skutanis or skutanis or lv-1010” via recipient line, billing details, or mention in body, invoices that are issued by me should be false.\n- If payment failed then return \"isvalid\": false.\n- IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n- If invoice has no value then return \"isvalid\": false.\n- If it is a reminder or past due return \"isvalid\": false\n\ncheck googlesheets if Invoice ID exists then set \"isinvoice\": false"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1180, -860], "id": "8a5ec97e-43e3-44c4-aa65-cdcf2a2563e6", "name": "OpenAI", "credentials": {"openAiApi": {"id": "1FYH9SozJhkXPr4w", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fbab1392-683b-42bf-83ee-be7259eff692", "leftValue": "={{ $json.message.content.isinvoice }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-840, -860], "id": "f6eb9ed9-0b88-4e90-bd67-c5c7ba486d82", "name": "Filter"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ $('Gmail Trigger1').item.json.subject }}", "message": "={{ $('Gmail Trigger1').item.json.textAsHtml }}", "options": {"appendAttribution": false, "attachmentsUi": {"attachmentsBinary": [{"property": "=attachment_0"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-540, -1000], "id": "4d5de3d6-3a04-45ac-977b-6c6f948ab6f4", "name": "Gmail", "webhookId": "594575ce-ddc7-4bdf-bb46-278c60454274", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"operation": "get", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "simple": false, "options": {"downloadAttachments": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-680, -860], "id": "ffa129c3-9c70-4a52-98e2-a8ab5ee1ced7", "name": "Gmail14", "webhookId": "7fb46920-7bf7-4f57-89a8-22bc969b2220", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"inputDataFieldName": "attachment_0", "name": "={{ $('Filter').item.json.message.content.invoiceId }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1lzLspLnAgDUJHR-t5spAIrnESft0dIt5", "mode": "list", "cachedResultName": "Invoices/Receipts", "cachedResultUrl": "https://drive.google.com/drive/folders/1lzLspLnAgDUJHR-t5spAIrnESft0dIt5"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-540, -720], "id": "a2a1bf7b-e1a9-4f8a-a952-96c99a2c4d78", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "DzEW531EgrgUFlbl", "name": "Google Drive account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Vendor": "={{ $('OpenAI').item.json.message.content.vendor }}", "Invoice Date": "={{ $('OpenAI').item.json.message.content.invoiceDate }}", "Invoice ID": "={{ $('OpenAI').item.json.message.content.invoiceId }}", "Category": "={{ $('OpenAI').item.json.message.content.category }}", "Is Subscription": "={{ $('OpenAI').item.json.message.content.isSubscription }}", "Amount": "={{ $('OpenAI').item.json.message.content.amount }}", "Currency": "={{ $('OpenAI').item.json.message.content.currency }}", "Invoice URL": "={{ $json.webViewLink }}"}, "matchingColumns": ["id"], "schema": [{"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice Date", "displayName": "Invoice Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice ID", "displayName": "Invoice ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Category", "displayName": "Category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Is Subscription", "displayName": "Is Subscription", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Amount", "displayName": "Amount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice URL", "displayName": "Invoice URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-360, -720], "id": "418e1990-9df6-46b4-a066-2b4d95212bb2", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini-2025-04-14", "mode": "list", "cachedResultName": "GPT-4.1-MINI-2025-04-14"}, "messages": {"values": [{"content": "=You are an AI email invoice parser.\n\nAnalyze the following email details and determine if it is an invoice issued to <PERSON><PERSON>, j<PERSON><PERSON><PERSON>, janis skutanis or skutanis or lv-1010. You make sure that invoice with the same ID doesn't already exist in google sheets. If it is, extract key invoice information and return a JSON object in the exact format provided. IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n\nEmail Subject: {{ $('Gmail Trigger1').item.json.subject }}\nEmail Body: {{ $('Gmail Trigger1').item.json.text }}\n\nReturn this JSON if it is an invoice to <PERSON><PERSON>:\n\n{\n  \"isinvoice\": true,\n  \"subject\": \"[exact email subject]\",\n  \"vendor\": \"[company/vendor name]\",\n  \"invoiceId\": \"[invoice/reference number]\",\n  \"invoiceDate\": \"YYYY-MM-DD\",\n  \"category\": \"[Software/Services/Utilities/Marketing/Office Supplies/Travel/Equipment/Other]\",\n  \"isSubscription\": true,\n  \"amount\": \"[numeric value only, no currency symbols]\",\n  \"currency\": \"[3-letter currency code]\"\n}\n\nOtherwise, return:\n\n{\n  \"isvalid\": false\n}\n\nFollow these rules:\n- Only return one of the above JSONs.\n- Detect the vendor from signature, email address, or header.\n- Extract amount and currency from totals or summary lines.\n- Parse the invoice/reference number from subject or attachment.\n- If invoice is paid or it is confirmation of payment received then it should be \"isvalid\": true.\n- Match “invoice to Janis, jsautomates, janis skutanis or skutanis or lv-1010” via recipient line, billing details, or mention in body, invoices that are issued by me should be false.\n- If payment failed then return \"isvalid\": false.\n- IMPORTANT if invoice total is 0 (don't mistake this with amount due) then return \"isvalid\": false.\n- If invoice has no value then return \"isvalid\": false.\n- If it is a reminder or past due return \"isvalid\": false\ncheck googlesheets if Invoice ID exists then set \"isinvoice\": false"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1380, -540], "id": "5551b3b1-c553-422c-aa73-37b98cbc5c58", "name": "OpenAI7", "credentials": {"openAiApi": {"id": "1FYH9SozJhkXPr4w", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fbab1392-683b-42bf-83ee-be7259eff692", "leftValue": "={{ $json.message.content.isinvoice }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-1040, -460], "id": "3f43d0dc-dfea-44e6-8bdf-1fb37470a0b1", "name": "Filter6"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ $('Gmail Trigger1').item.json.subject }}", "message": "={{ $('Gmail Trigger1').item.json.textAsHtml }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-840, -540], "id": "7c42fdf7-7d15-44e8-a609-8278d5ca2cce", "name": "Gmail15", "webhookId": "594575ce-ddc7-4bdf-bb46-278c60454274", "credentials": {"gmailOAuth2": {"id": "IfHz7Yy8x29GUxwz", "name": "Gmail account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Vendor": "={{ $('OpenAI7').item.json.message.content.vendor }}", "Invoice Date": "={{ $('OpenAI7').item.json.message.content.invoiceDate }}", "Invoice ID": "={{ $('OpenAI7').item.json.message.content.invoiceId }}", "Category": "={{ $('OpenAI7').item.json.message.content.category }}", "Is Subscription": "={{ $('OpenAI7').item.json.message.content.isSubscription }}", "Amount": "={{ $('OpenAI7').item.json.message.content.amount }}", "Currency": "={{ $('OpenAI7').item.json.message.content.currency }}", "Invoice URL": "="}, "matchingColumns": ["id"], "schema": [{"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice Date", "displayName": "Invoice Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice ID", "displayName": "Invoice ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Category", "displayName": "Category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Is Subscription", "displayName": "Is Subscription", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Amount", "displayName": "Amount", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Invoice URL", "displayName": "Invoice URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-620, -460], "id": "b8d5a3d8-12e0-4395-9694-1c9a6547c6fa", "name": "Google Sheets7", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.6, "position": [-1200, -700], "id": "e456f7ea-7165-482f-a25a-9e65e528ecb9", "name": "Google Sheets8", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY", "mode": "list", "cachedResultName": "AI Agent Bookeeping assitand", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ItSk65G_K9wx6-z6_B0Egrn-_zkE7PS6JcnkiebolKY/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.6, "position": [-1440, -380], "id": "dc4a13c7-cee5-4e4f-b940-5dfd5c3873c7", "name": "Google Sheets9", "credentials": {"googleSheetsOAuth2Api": {"id": "lwctJvlkNhdjrS1p", "name": "Google Sheets account"}}}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}, "Gmail5": {"main": [[]]}, "Telegram Trigger": {"main": [[{"node": "OpenAI3", "type": "main", "index": 0}]]}, "OpenAI3": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Gmail5", "type": "main", "index": 0}, {"node": "Google Drive2", "type": "main", "index": 0}]]}, "Google Drive2": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "Gmail1": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Extract from File2", "type": "main", "index": 0}], [{"node": "OpenAI6", "type": "main", "index": 0}]]}, "Extract from File2": {"main": [[{"node": "OpenAI5", "type": "main", "index": 0}]]}, "OpenAI5": {"main": [[{"node": "Filter4", "type": "main", "index": 0}]]}, "Filter4": {"main": [[{"node": "Gmail12", "type": "main", "index": 0}]]}, "Gmail12": {"main": [[{"node": "Gmail11", "type": "main", "index": 0}, {"node": "Google Drive3", "type": "main", "index": 0}]]}, "Gmail11": {"main": [[]]}, "Google Drive3": {"main": [[{"node": "Google Sheets5", "type": "main", "index": 0}]]}, "OpenAI6": {"main": [[{"node": "Filter5", "type": "main", "index": 0}]]}, "Filter5": {"main": [[{"node": "Gmail13", "type": "main", "index": 0}]]}, "Gmail13": {"main": [[{"node": "Google Sheets6", "type": "main", "index": 0}]]}, "Gmail Trigger1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Google Sheets4": {"ai_tool": [[{"node": "OpenAI5", "type": "ai_tool", "index": 0}]]}, "Google Sheets3": {"ai_tool": [[{"node": "OpenAI6", "type": "ai_tool", "index": 0}]]}, "If": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}], [{"node": "OpenAI7", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Gmail14", "type": "main", "index": 0}]]}, "Gmail14": {"main": [[{"node": "Gmail", "type": "main", "index": 0}, {"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "OpenAI7": {"main": [[{"node": "Filter6", "type": "main", "index": 0}]]}, "Filter6": {"main": [[{"node": "Gmail15", "type": "main", "index": 0}]]}, "Gmail15": {"main": [[{"node": "Google Sheets7", "type": "main", "index": 0}]]}, "Google Sheets8": {"ai_tool": [[{"node": "OpenAI", "type": "ai_tool", "index": 0}]]}, "Google Sheets9": {"ai_tool": [[{"node": "OpenAI7", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e1c9dc9c-1852-48f5-881e-8c4def82c613", "meta": {"templateCredsSetupCompleted": true, "instanceId": "22ac386189853c12088699c9d14fb9c0f019429ad370e1d2c85cb76051132eda"}, "id": "mhN4Erc83Dciorze", "tags": []}