{"id": "103", "name": "verify email", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 510], "parameters": {}, "typeVersion": 1}, {"name": "Create <PERSON><PERSON>", "type": "n8n-nodes-base.functionItem", "position": [640, 510], "parameters": {"functionCode": "item.email = \"<EMAIL>\";\nreturn item;"}, "typeVersion": 1}, {"name": "Check Email Exists", "type": "n8n-nodes-base.uproc", "position": [850, 510], "parameters": {"tool": "checkEmailExists", "email": "={{$node[\"Create Email Item\"].json[\"email\"]}}", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}, {"name": "Email Exists?", "type": "n8n-nodes-base.if", "position": [1050, 510], "parameters": {"conditions": {"string": [{"value1": "={{$node[\"Check Email Exists\"].json[\"message\"][\"response\"]}}", "value2": "deliverable"}]}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Create Email Item": {"main": [[{"node": "Check Email Exists", "type": "main", "index": 0}]]}, "Check Email Exists": {"main": [[{"node": "Email Exists?", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Create <PERSON><PERSON>", "type": "main", "index": 0}]]}}}