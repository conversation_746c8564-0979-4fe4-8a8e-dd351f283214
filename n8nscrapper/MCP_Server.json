{"name": "MCP Server", "nodes": [{"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [-420, 320], "id": "4a6a19f0-c60f-420f-adcf-06a31eb08624", "name": "Calculator"}, {"parameters": {"name": "Voice_clone", "description": "Description: Call this tool to clone voices\nReturnMessage:cloned voice", "workflowId": {"__rl": true, "value": "mqI92PoaooDjSgs2", "mode": "list", "cachedResultName": "AI voice <PERSON><PERSON>"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [140, 320], "id": "7556b84a-2643-495f-b51d-e30f4992d845", "name": "AI voice clone"}, {"parameters": {"name": "Translateanything", "description": "Call this tool if you want to translate any document", "workflowId": {"__rl": true, "value": "rU01P73nO8AoZFfq", "mode": "list", "cachedResultName": "Translate anything"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [360, 320], "id": "56738532-ad4a-411f-88ee-32d5f9ff8294", "name": "Translate Anything"}, {"parameters": {"description": "Description: Call this tool to generate comments to a linkedin post\nReturnMessage : Comments generated based on input", "workflowId": {"__rl": true, "value": "8m666gTKCabdCu6k", "mode": "list", "cachedResultName": "Linkedin Comment Generator copy"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('text', `Extract the text description`, 'string') }}", "input": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('input', `Extract the query input`, 'string') }}", "tool": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('tool', `extract the tool name`, 'string') }}"}, "matchingColumns": ["text"], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "tool", "displayName": "tool", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "text", "displayName": "text", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-140, 320], "id": "78618a56-0c24-4e68-b844-d093aabc48ac", "name": "Generate comments tool"}, {"parameters": {"path": "8c152172-cfdf-41c5-9518-f4814baf7da9"}, "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 1, "position": [-180, -80], "id": "9fbdfb3d-ea4d-449c-b36c-29b84602e535", "name": "MCP Server Trigger", "webhookId": "8c152172-cfdf-41c5-9518-f4814baf7da9"}], "pinData": {}, "connections": {"Calculator": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "AI voice clone": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Translate Anything": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Generate comments tool": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "447489fb-239d-4a95-a50c-801d622d146c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f21f53385ae396fcb0c1b69950e1ec16f2dcc4ddca34a170466835249ec1c42c"}, "id": "KUZ7RVyB8mcv6qZ5", "tags": []}