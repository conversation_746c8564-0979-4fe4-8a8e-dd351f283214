{"name": "My workflow 18", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "filters": {}}, "id": "29b3ef02-d9b2-48dd-924a-ad5ffb8c49ee", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.gmailTrigger", "position": [-1060, 60], "typeVersion": 1.2}, {"parameters": {"operation": "get", "messageId": "={{ $json.id }}", "simple": false, "options": {}}, "id": "84b77ab9-56ae-46ba-b4b9-5f849cbc6cb4", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [-840, 100], "webhookId": "[REDACTED_WEBHOOK_ID]", "typeVersion": 2.1}, {"parameters": {"jsonSchemaExample": "{\n\t\"label\": \"Label name\", \n\"label ID\": \"label ID\" \n}"}, "id": "0789b492-e932-41bc-8b29-940ab3b18424", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-40, 220], "typeVersion": 1.2}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail').item.json.id }}", "labelIds": "={{ $json.output['label ID'] }}"}, "id": "c06fab8c-731f-44e3-872f-421c6cdfff9c", "name": "Gmail1", "type": "n8n-nodes-base.gmail", "position": [-20, -100], "webhookId": "[REDACTED_WEBHOOK_ID]", "typeVersion": 2.1}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-sonnet-4-20250514", "cachedResultName": "<PERSON> 4"}, "options": {}}, "id": "277bb2b8-0014-4c33-bd7a-c634b7ab5974", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "position": [-660, 280], "typeVersion": 1.3}, {"parameters": {"promptType": "define", "text": "Run the task.", "hasOutputParser": true, "options": {"systemMessage": "=**Objective:** Analyze the provided email data and classify it with the most appropriate label. **Utilize the capability to check for prior email history (emails sent to or received from the sender by `[REDACTED_EMAIL]`)** to determine if this is a first-time interaction (cold email) or part of an existing relationship. This context is crucial for accurate labeling, especially for distinguishing between Marketing, Notifications, and FYI. Respond with *only* the corresponding Label ID.\n\n**Your Email Address (for context):** `[REDACTED_EMAIL]`\n\n**Key Capabilities (to be used by the system executing this prompt):**\n* **Check Prior Email History:** Before full classification, determine if any prior email correspondence exists between `[REDACTED_EMAIL]` and `{{ $json.from.value[0].address }}`. This check should yield \"Yes\" (history exists) or \"No\" (no history found).\n\n**Input Email Data (from n8n JSON item for the *current* email):**\n* **Sender Email:** `{{ $json.from.value[0].address }}`\n* **Sender Name:** `{{ $json.from.value[0].name }}`\n* **Direct Recipient Emails (To):** `{{ $json.to.value.map(r => r.address).join(', ') }}`\n* **CC Recipient Emails:** `{{ $json.cc.value.map(r => r.address).join(', ') }}` *(Added this based on your JSON, use if populated)*\n* **Subject:** `{{ $json.subject }}`\n* **Body (Plain Text):** `{{ $json.text }}`\n* **Existing Gmail Labels (for current email):** `{{ $json.labelIds.join(', ') }}`\n* **Auto-Submitted Header:** `{{ $json.headers['auto-submitted'] }}`\n* **Original Sender Header (if different from From):** `{{ $json.headers.sender }}`\n* **In-Reply-To Header (for current email):** `{{ $json.headers['in-reply-to'] }}`\n* **References Header (for current email):** `{{ $json.headers.references }}`\n* **List-Unsubscribe Header:** `{{ $json.headers['list-unsubscribe'] }}`\n* **Precedence Header:** `{{ $json.headers.precedence }}` (e.g., \"Bulk\")\n\n**Labels, Descriptions, and Prioritization Logic:**\n\n**Guidance on Using Prior Email History & Unsubscribe Links:**\n* **Prior Email History = \"No\":** Strong indicator of a **cold/unsolicited email**.\n    * If promotional/sales pitch: Likely \"Marketing.\"\n    * If exceptionally personalized & high-value for Trigify: Rare \"To Respond.\"\n* **Prior Email History = \"Yes\":** Indicates an **existing relationship/conversation**.\n    * If it's an update on terms, policies, or service changes from this known entity: Likely \"Notification.\"\n    * If it's a newsletter Max subscribed to: Could be \"FYI\" or \"Marketing\" based on content.\n    * If it's a direct message requiring action: Likely \"To Respond.\"\n* **`List-Unsubscribe` Header or Unsubscribe Links in Body:**\n    * Common in \"Marketing\" emails.\n    * Also present in many legitimate \"Notification\" emails (e.g., service updates, policy changes like the DPA example) and some \"FYI\" newsletters for compliance.\n    * **Therefore, an unsubscribe link alone does not define the category. Consider it alongside Prior Email History and email content/purpose.**\n* **`Precedence: Bulk` Header:** Often indicates mass mailings, common for Marketing, Notifications, and some FYIs.\n\n---\n\n* **To Respond (Label ID: `Label_5151750749488724401`):**\n    * **Primary Criteria:** Requires direct, timely action/reply from `[REDACTED_EMAIL]`.\n    * **Key Indicators (especially if Prior Email History = \"Yes\"):** Direct questions to Max, assigned tasks, requests for info for Trigify, deadlines for Max, part of an active conversation (indicated by `In-Reply-To`/`References`).\n    * **Sales Process Prioritization (Warm Leads/Active Processes - typically Prior Email History = \"Yes\"):** Ongoing, active Trigify sales process discussions.\n    * **High-Value Cold Outreach (Exceptional Cases - Prior Email History = \"No\"):** Highly personalized, strategic opportunity for Trigify requiring Max's personal attention. (Default for cold sales is \"Marketing\").\n\n* **FYI (Label ID: `Label_7518716752151077752`):**\n    * **Primary Criteria:** For Max's awareness; no immediate action/reply required.\n    * **Key Indicators:**\n        * Max is CC'd, primary action for others.\n        * General announcements, *non-promotional* newsletters from *known entities/subscriptions* (Prior Email History = \"Yes\" or sender is clearly a subscribed source) that aren't critical service notifications.\n        * Informational updates within ongoing projects where Max isn't the primary actor.\n    * If an email is a mass mailing (`Precedence: Bulk`, `List-Unsubscribe` present) from a *known entity* (Prior Email History = \"Yes\") and is purely informational without a direct call to action or critical service update, it could be \"FYI.\"\n\n* **Comment (Label ID: `Label_1470120230130814788`):**\n    * Comment/feedback on a document, task, system (e.g., subject \"New comment on...\").\n\n* **Notification (Label ID: `Label_9`):**\n    * **Primary Criteria:** Provides important, often non-promotional, updates or alerts regarding an existing service, account, or system that `[REDACTED_EMAIL]` or Trigify uses or is affected by. Action is typically awareness, potential configuration change, or noting a deadline, rather than a conversational reply.\n    * **Key Indicators:**\n        * **Updates from Known Service Providers (Prior Email History = \"Yes\" is common):**\n            * Changes to Terms of Service, Privacy Policies, Data Processing Agreements (DPAs like the \"Plain\" example).\n            * Critical service availability announcements (outages, maintenance).\n            * Security alerts related to an account Max uses.\n            * Important updates about features or functionality of a service Trigify relies on, which are not primarily marketing new features.\n        * System-generated alerts (e.g., `Auto-Submitted Header` = `auto-generated`) like social media notifications, non-meeting calendar reminders, some financial transaction alerts.\n        * Subject lines may contain: \"Important Update,\" \"Service Notification,\" \"Policy Change,\" \"DPA Update,\" \"Security Alert.\"\n        * Even if a `List-Unsubscribe` link is present (for compliance), if the core content is a critical service/account/legal update from a company Max/Trigify does business with, it's a \"Notification.\"\n        * The `Existing Gmail Labels` might include `CATEGORY_UPDATES`.\n\n* **Meeting Update (Label ID: `Label_7958083858424702466`):**\n    * Update specifically regarding a scheduled meeting (e.g., \"Accepted:\", \"Declined:\", \"Updated Invitation:\", \"Cancelled:\").\n\n* **Marketing (Label ID: `Label_5274550748854913384`):**\n    * **Primary Criteria:** Unsolicited promotional sales pitch, advertisement, or general marketing newsletter, especially if **Prior Email History = \"No.\"**\n    * **Key Indicators:**\n        * **No prior email history found with the `Sender Email`,** AND the email is primarily aimed at selling a product/service or promoting a company/event to Max/Trigify.\n        * Content is generic, focused on features/benefits without strong personalization to Max's known, active projects.\n        * Contains a `List-Unsubscribe Header` AND the content is promotional.\n        * `Existing Gmail Labels` may include `CATEGORY_PROMOTIONS`.\n    * If **Prior Email History = \"Yes\"**: Could still be \"Marketing\" if it's a clearly promotional newsletter/offer from a known contact that doesn't fit \"Notification\" or demand a \"To Respond.\"\n\n---\n\n**Email Data for Classification (Summary):**\n* **Prior Email History with Sender (Yes/No) - From the new capability**\n* Sender Email: `{{ $json.from.value[0].address }}`\n* Sender Name: `{{ $json.from.value[0].name }}`\n* Direct Recipient Emails (To): `{{ $json.to.value.map(r => r.address).join(', ') }}`\n* CC Recipient Emails: `{{ $json.cc.value.map(r => r.address).join(', ') }}`\n* Subject: `{{ $json.subject }}`\n* Body (Plain Text): `{{ $json.text }}`\n* Existing Gmail Labels: `{{ $json.labelIds.join(', ') }}`\n* Auto-Submitted Header: `{{ $json.headers['auto-submitted'] }}`\n* Original Sender Header: `{{ $json.headers.sender }}`\n* In-Reply-To Header: `{{ $json.headers['in-reply-to'] }}`\n* References Header: `{{ $json.headers.references }}`\n* List-Unsubscribe Header: `{{ $json.headers['list-unsubscribe'] }}`\n* Precedence Header: `{{ $json.headers.precedence }}`\n\n**Output:** [Provide ONLY the Label ID here]"}}, "id": "6825100d-c447-44b1-941b-2821e96ff73c", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-540, -20], "typeVersion": 1.9}, {"parameters": {"operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "filters": {"q": "=from:{{ $fromAI('email') }}"}}, "id": "5ab99834-d993-4fd8-b8ba-c3be2e4022da", "name": "Get Email", "type": "n8n-nodes-base.gmailTool", "position": [-280, 260], "webhookId": "[REDACTED_WEBHOOK_ID]", "typeVersion": 2.1}, {"parameters": {"operation": "getAll", "filters": {"labelIds": ["SENT"], "q": "=to:{{ $fromAI('email') }}"}}, "id": "db29cf0c-e542-40c2-a5df-1fefaa56bd4c", "name": "<PERSON>", "type": "n8n-nodes-base.gmailTool", "position": [-480, 300], "webhookId": "[REDACTED_WEBHOOK_ID]", "typeVersion": 2.1}], "pinData": {"Gmail Trigger": [{"json": {"To": "[REDACTED_EMAIL]", "id": "[REDACTED_MESSAGE_ID]", "From": "Coresignal <<EMAIL>>", "labels": [{"id": "INBOX", "name": "INBOX"}, {"id": "IMPORTANT", "name": "IMPORTANT"}, {"id": "CATEGORY_PROMOTIONS", "name": "CATEGORY_PROMOTIONS"}], "Subject": "New MCP server: Direct data access for smarter AI", "payload": {"mimeType": "multipart/alternative"}, "snippet": "What&#39;s new Empower your AI agent with Coresignal&#39;s data We&#39;re excited to unveil our latest news, the Coresignal MCP server that connects your LLM tools directly to our company, employee,", "threadId": "1971723eacc72f08", "historyId": "5524551", "internalDate": "1748439919000", "sizeEstimate": 62610}}, {"json": {"Cc": "", "To": "[REDACTED_EMAIL]", "id": "[REDACTED_MESSAGE_ID]", "From": "Plain <<EMAIL>>", "labels": [{"id": "INBOX", "name": "INBOX"}, {"id": "IMPORTANT", "name": "IMPORTANT"}, {"id": "CATEGORY_UPDATES", "name": "CATEGORY_UPDATES"}, {"id": "UNREAD", "name": "UNREAD"}], "Subject": " Important update to Plain’s Data Processing Agreement (DPA)", "payload": {"mimeType": "multipart/alternative"}, "snippet": "Hi <PERSON>, We&#39;re writing to let you know about an upcoming change to our Data Processing Agreement (DPA). What&#39;s changing? We are simplifying our notification process for subprocessor updates.", "threadId": "1971723cfa14c3df", "historyId": "5524408", "internalDate": "1748439911000", "sizeEstimate": 45077}}]}, "connections": {"Gmail Trigger": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Gmail": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}, "Get Email": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Check Sent": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "94d07b8e-69bb-4c2f-8d92-c0e3c3bf6427", "meta": {"instanceId": "09c3889a987ce2ee0b52569720d3558926746d56a4fcfa02238ff79dd96c4800"}, "id": "QSfhDJLeVYdQRq4J", "tags": []}