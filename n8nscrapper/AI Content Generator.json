{"name": "Marketing: New AI Post Generated -> Upload to Contentful -> Notify Content Manager", "flow": [{"id": 57, "module": "typeform:WatchEventsWithResponses", "version": 2, "parameters": {"__IMTHOOK__": 820880}, "mapper": {}, "metadata": {"designer": {"x": -2530, "y": -393}, "restore": {"parameters": {"__IMTHOOK__": {"data": {"editable": "true"}, "label": "New Content Submission"}}}, "parameters": [{"name": "__IMTHOOK__", "type": "hook:typeform", "label": "Webhook", "required": true}], "interface": [{"name": "form_id", "type": "text", "label": "Form ID"}, {"name": "event", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "type", "type": "text", "label": "Type"}], "type": "collection", "label": "Event"}, {"name": "token", "type": "text", "label": "Token"}, {"name": "submitted_at", "type": "date", "label": "Submitted at"}, {"name": "landed_at", "type": "date", "label": "Landed at"}, {"name": "calculated", "spec": [{"name": "score", "type": "number", "label": "Score"}], "type": "collection", "label": "Calculated"}, {"name": "definition", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "title", "type": "text", "label": "Title"}, {"name": "fields", "spec": {"spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "title", "type": "text", "label": "Title"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "ref", "type": "text", "label": "Internal Name"}, {"name": "allow_multiple_selections", "type": "boolean", "label": "Allow Multiple Selections"}, {"name": "allow_other_choice", "type": "boolean", "label": "Allow Other Choice"}], "type": "collection"}, "type": "array", "label": "Fields"}], "type": "collection", "label": "Definition"}, {"name": "answers", "spec": [{"name": "field", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "ref", "type": "text", "label": "Internal Name"}], "type": "collection", "label": "Field"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "text", "type": "text", "label": "Text"}, {"name": "email", "type": "email", "label": "Email"}, {"name": "boolean", "type": "boolean", "label": "Boolean"}, {"name": "number", "type": "number", "label": "Number"}, {"name": "date", "type": "date", "label": "Date"}, {"name": "long_text", "type": "text", "label": "Long Text"}, {"name": "short_text", "type": "text", "label": "Short Text"}, {"name": "file_url", "type": "url", "label": "File URL"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "phone_number", "type": "text", "label": "Phone Number"}, {"name": "choice", "spec": [{"name": "label", "type": "text", "label": "Label"}, {"name": "other", "type": "text", "label": "Other"}], "type": "collection", "label": "Choice"}, {"name": "choices", "spec": [{"name": "labels", "spec": {"type": "text"}, "type": "array", "label": "Labels"}], "type": "collection", "label": "Choices"}], "type": "array", "label": "Answers"}, {"name": "mappable_answers", "spec": [{"name": "d14caed3-7e48-447d-b8df-0b3aeebd9713", "type": "text", "label": "*What is the title of the content?*"}, {"name": "0a9f22b1-2116-44c7-887f-6e1669087d13", "type": "number", "label": "*How many words should the content have?*"}, {"name": "8adfc057-4d90-44c7-955b-724c408d7c1a", "type": "text", "label": "*What is the primary keyword for the content?*"}, {"name": "2ac8339e-3bec-4eb6-9dde-7d31b4247932", "type": "text", "label": "*What are the secondary keywords for the content?*"}, {"name": "6ce0072a-8ee8-40c7-b4ce-0dc6a1f35d66", "type": "text", "label": "*Are there any internal links that should be included in the content?*"}, {"name": "36395739-ab62-42fa-bc90-0efc9c26dbf1", "type": "text", "label": "*Are there any external links that should be included in the content?*"}, {"name": "9e4c247c-8214-4403-b29a-476b6b010ae7", "type": "text", "label": "*Additional instructions or specific requirements for the content.*"}], "type": "collection", "label": "Mappable Answers"}, {"name": "hidden", "spec": [], "type": "collection", "label": "Hidden"}]}}, {"id": 58, "module": "util:FunctionSleep", "version": 1, "parameters": {}, "mapper": {"duration": "3"}, "metadata": {"designer": {"x": -2230, "y": -393}, "restore": {}, "expect": [{"name": "duration", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delay", "required": true, "validate": {"max": 300, "min": 1}}]}}, {"id": 59, "module": "typeform:ListResponses", "version": 2, "parameters": {"__IMTCONN__": 1134474}, "mapper": {"limit": "1", "formId": "Djdlft47"}, "metadata": {"designer": {"x": -1930, "y": -393}, "restore": {"expect": {"fields": {"mode": "chose"}, "formId": {"mode": "chose", "label": "Content Generator Brief"}, "completed": {"mode": "chose"}, "excluded_response_ids": {"mode": "chose"}, "included_response_ids": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "typeform2"}, "label": "<EMAIL>"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:typeform2", "label": "Connection", "required": true}], "expect": [{"name": "formId", "type": "select", "label": "Form ID", "required": true}, {"name": "completed", "type": "boolean", "label": "Completed"}, {"name": "query", "type": "text", "label": "Query"}, {"name": "since", "type": "date", "label": "Since"}, {"name": "until", "type": "date", "label": "Until"}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "fields", "spec": {"name": "value", "type": "select", "dynamic": true, "options": [], "required": true}, "type": "array", "label": "Fields"}, {"name": "included_response_ids", "type": "select", "label": "Included Response IDs", "multiple": true}, {"name": "excluded_response_ids", "type": "select", "label": "Excluded Response IDs", "multiple": true}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "landing_id", "type": "text", "label": "Landing ID"}, {"name": "token", "type": "text", "label": "Token"}, {"name": "response_id", "type": "text", "label": "Response ID"}, {"name": "landed_at", "type": "date", "label": "Landed at"}, {"name": "submitted_at", "type": "date", "label": "Submitted at"}, {"name": "metadata", "spec": [{"name": "user_agent", "type": "text", "label": "User Agent"}, {"name": "platform", "type": "text", "label": "Platform"}, {"name": "referer", "type": "url", "label": "<PERSON><PERSON><PERSON>"}, {"name": "network_id", "type": "text", "label": "Network ID"}, {"name": "browser", "type": "text", "label": "Browser"}], "type": "collection", "label": "<PERSON><PERSON><PERSON>"}, {"name": "answers", "spec": [{"name": "field", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "ref", "type": "text", "label": "Readable Name"}], "type": "collection", "label": "Field"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "text", "type": "text", "label": "Text"}, {"name": "email", "type": "email", "label": "Email"}, {"name": "boolean", "type": "boolean", "label": "Boolean"}, {"name": "number", "type": "number", "label": "Number"}, {"name": "date", "type": "date", "label": "Date"}, {"name": "long_text", "type": "text", "label": "Long Text"}, {"name": "short_text", "type": "text", "label": "Short Text"}, {"name": "file_url", "type": "url", "label": "File URL"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "phone_number", "type": "text", "label": "Phone Number"}, {"name": "choice", "spec": [{"name": "label", "type": "text", "label": "Label"}, {"name": "other", "type": "text", "label": "Other"}], "type": "collection", "label": "Choice"}, {"name": "choices", "spec": [{"name": "labels", "spec": {"type": "text"}, "type": "array", "label": "Labels"}], "type": "collection", "label": "Choices"}], "type": "array", "label": "Answers"}, {"name": "mappable_answers", "spec": [{"name": "d14caed3-7e48-447d-b8df-0b3aeebd9713", "type": "text", "label": "*What is the title of the content?*"}, {"name": "0a9f22b1-2116-44c7-887f-6e1669087d13", "type": "number", "label": "*How many words should the content have?*"}, {"name": "8adfc057-4d90-44c7-955b-724c408d7c1a", "type": "text", "label": "*What is the primary keyword for the content?*"}, {"name": "2ac8339e-3bec-4eb6-9dde-7d31b4247932", "type": "text", "label": "*What are the secondary keywords for the content?*"}, {"name": "6ce0072a-8ee8-40c7-b4ce-0dc6a1f35d66", "type": "text", "label": "*Are there any internal links that should be included in the content?*"}, {"name": "36395739-ab62-42fa-bc90-0efc9c26dbf1", "type": "text", "label": "*Are there any external links that should be included in the content?*"}, {"name": "9e4c247c-8214-4403-b29a-476b6b010ae7", "type": "text", "label": "*Additional instructions or specific requirements for the content.*"}], "type": "collection", "label": "Mappable Answers"}, {"name": "hidden", "spec": [], "type": "collection", "label": "Hidden"}, {"name": "calculated", "spec": [{"name": "score", "type": "number", "label": "Score"}], "type": "collection", "label": "Calculated"}]}}, {"id": 61, "module": "google-sheets:addRow", "version": 2, "parameters": {"__IMTCONN__": 2154157}, "mapper": {"mode": "map", "values": {"0": "{{59.mappable_answers.`d14caed3-7e48-447d-b8df-0b3aeebd9713`}}", "1": "{{59.mappable_answers.`0a9f22b1-2116-44c7-887f-6e1669087d13`}}", "2": "{{59.mappable_answers.`8adfc057-4d90-44c7-955b-724c408d7c1a`}}", "3": "{{59.mappable_answers.`2ac8339e-3bec-4eb6-9dde-7d31b4247932`}}", "4": "{{59.mappable_answers.`6ce0072a-8ee8-40c7-b4ce-0dc6a1f35d66`}}", "5": "{{59.mappable_answers.`36395739-ab62-42fa-bc90-0efc9c26dbf1`}}", "6": "{{59.mappable_answers.`9e4c247c-8214-4403-b29a-476b6b010ae7`}}"}, "sheetId": "Main", "spreadsheetId": "1ZpEZYezX_9F7lSixYluzrNl_GAjJxJPFPENdmLLGtPE", "tableFirstRow": "A1:Z1", "insertDataOption": "INSERT_ROWS", "valueInputOption": "USER_ENTERED", "insertUnformatted": false}, "metadata": {"designer": {"x": -1629, "y": -391}, "restore": {"expect": {"mode": {"label": "Enter manually"}, "tableFirstRow": {"label": "A-Z", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "A"}, {"name": "1", "type": "text", "label": "B"}, {"name": "2", "type": "text", "label": "C"}, {"name": "3", "type": "text", "label": "D"}, {"name": "4", "type": "text", "label": "E"}, {"name": "5", "type": "text", "label": "F"}, {"name": "6", "type": "text", "label": "G"}, {"name": "7", "type": "text", "label": "H"}, {"name": "8", "type": "text", "label": "I"}, {"name": "9", "type": "text", "label": "J"}, {"name": "10", "type": "text", "label": "K"}, {"name": "11", "type": "text", "label": "L"}, {"name": "12", "type": "text", "label": "M"}, {"name": "13", "type": "text", "label": "N"}, {"name": "14", "type": "text", "label": "O"}, {"name": "15", "type": "text", "label": "P"}, {"name": "16", "type": "text", "label": "Q"}, {"name": "17", "type": "text", "label": "R"}, {"name": "18", "type": "text", "label": "S"}, {"name": "19", "type": "text", "label": "T"}, {"name": "20", "type": "text", "label": "U"}, {"name": "21", "type": "text", "label": "V"}, {"name": "22", "type": "text", "label": "W"}, {"name": "23", "type": "text", "label": "X"}, {"name": "24", "type": "text", "label": "Y"}, {"name": "25", "type": "text", "label": "Z"}], "type": "collection", "label": "Values"}]}, "insertDataOption": {"mode": "chose", "label": "Insert rows"}, "valueInputOption": {"mode": "chose", "label": "User entered"}, "insertUnformatted": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "<EMAIL>"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Choose a Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "insertUnformatted", "type": "boolean", "label": "Unformatted", "required": true}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "insertDataOption", "type": "select", "label": "Insert data option", "validate": {"enum": ["INSERT_ROWS", "OVERWRITE"]}}, {"name": "spreadsheetId", "type": "text", "label": "Spreadsheet", "required": true}, {"name": "sheetId", "type": "text", "label": "Sheet Name", "required": true}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1"]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "A"}, {"name": "1", "type": "text", "label": "B"}, {"name": "2", "type": "text", "label": "C"}, {"name": "3", "type": "text", "label": "D"}, {"name": "4", "type": "text", "label": "E"}, {"name": "5", "type": "text", "label": "F"}, {"name": "6", "type": "text", "label": "G"}, {"name": "7", "type": "text", "label": "H"}, {"name": "8", "type": "text", "label": "I"}, {"name": "9", "type": "text", "label": "J"}, {"name": "10", "type": "text", "label": "K"}, {"name": "11", "type": "text", "label": "L"}, {"name": "12", "type": "text", "label": "M"}, {"name": "13", "type": "text", "label": "N"}, {"name": "14", "type": "text", "label": "O"}, {"name": "15", "type": "text", "label": "P"}, {"name": "16", "type": "text", "label": "Q"}, {"name": "17", "type": "text", "label": "R"}, {"name": "18", "type": "text", "label": "S"}, {"name": "19", "type": "text", "label": "T"}, {"name": "20", "type": "text", "label": "U"}, {"name": "21", "type": "text", "label": "V"}, {"name": "22", "type": "text", "label": "W"}, {"name": "23", "type": "text", "label": "X"}, {"name": "24", "type": "text", "label": "Y"}, {"name": "25", "type": "text", "label": "Z"}], "type": "collection", "label": "Values"}, {"name": "mode", "type": "select", "label": "Choose a Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "insertUnformatted", "type": "boolean", "label": "Unformatted", "required": true}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "insertDataOption", "type": "select", "label": "Insert data option", "validate": {"enum": ["INSERT_ROWS", "OVERWRITE"]}}, {"name": "spreadsheetId", "type": "text", "label": "Spreadsheet", "required": true}, {"name": "sheetId", "type": "text", "label": "Sheet Name", "required": true}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1"]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "A"}, {"name": "1", "type": "text", "label": "B"}, {"name": "2", "type": "text", "label": "C"}, {"name": "3", "type": "text", "label": "D"}, {"name": "4", "type": "text", "label": "E"}, {"name": "5", "type": "text", "label": "F"}, {"name": "6", "type": "text", "label": "G"}, {"name": "7", "type": "text", "label": "H"}, {"name": "8", "type": "text", "label": "I"}, {"name": "9", "type": "text", "label": "J"}, {"name": "10", "type": "text", "label": "K"}, {"name": "11", "type": "text", "label": "L"}, {"name": "12", "type": "text", "label": "M"}, {"name": "13", "type": "text", "label": "N"}, {"name": "14", "type": "text", "label": "O"}, {"name": "15", "type": "text", "label": "P"}, {"name": "16", "type": "text", "label": "Q"}, {"name": "17", "type": "text", "label": "R"}, {"name": "18", "type": "text", "label": "S"}, {"name": "19", "type": "text", "label": "T"}, {"name": "20", "type": "text", "label": "U"}, {"name": "21", "type": "text", "label": "V"}, {"name": "22", "type": "text", "label": "W"}, {"name": "23", "type": "text", "label": "X"}, {"name": "24", "type": "text", "label": "Y"}, {"name": "25", "type": "text", "label": "Z"}], "type": "collection", "label": "Values"}]}}, {"id": 29, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 2154157}, "mapper": {"from": "drive", "limit": "1", "orderBy": "__ROW_NUMBER__", "sheetId": "Main", "fieldType": "number", "sortOrder": "desc", "spreadsheetId": "1ZpEZYezX_9F7lSixYluzrNl_GAjJxJPFPENdmLLGtPE", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": -1337, "y": -389}, "restore": {"expect": {"from": {"label": "Select from My Drive"}, "filter": {"label": []}, "orderBy": {"mode": "chose", "label": "Row number"}, "sheetId": {"mode": "chose", "label": "Main"}, "fieldType": {"mode": "chose", "label": "Number"}, "sortOrder": {"mode": "chose", "label": "Descending"}, "spreadsheetId": {"mode": "chose", "label": "LeftClick Content Generation Tracker"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "<EMAIL> (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Enter a Spreadsheet ID and Sheet Name", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "number", "label": "Maximum number of returned rows"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "fieldType", "type": "select", "label": "Field Type", "validate": {"enum": ["string", "number", "date"]}}, {"name": "from", "type": "select", "label": "Enter a Spreadsheet ID and Sheet Name", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "number", "label": "Maximum number of returned rows"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "fieldType", "type": "select", "label": "Field Type", "validate": {"enum": ["string", "number", "date"]}}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "Title (A)"}, {"name": "1", "type": "text", "label": "Word Count (B)"}, {"name": "2", "type": "text", "label": "Primary Keyword (C)"}, {"name": "3", "type": "text", "label": "Secondary Keyword(s) (D)"}, {"name": "4", "type": "text", "label": "Internal Links (E)"}, {"name": "5", "type": "text", "label": "External Links (F)"}, {"name": "6", "type": "text", "label": "Additional Instructions (G)"}, {"name": "7", "type": "text", "label": "Outline Doc (H)"}, {"name": "8", "type": "text", "label": "Article Doc (I)"}, {"name": "9", "type": "text", "label": "Website URL (J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 30, "module": "util:SetVariables", "version": 1, "parameters": {}, "mapper": {"scope": "roundtrip", "variables": [{"name": "title", "value": "{{29.`0`}}"}, {"name": "wordCount", "value": "{{29.`1`}}"}, {"name": "primaryKeyword", "value": "{{29.`2`}}"}, {"name": "secondaryKeywords", "value": "{{if(contains(29.`3`; \",\"); split(29.`3`; \",\"); \"[\"\"\" + 29.`3` + \"\"\"]\")}}"}, {"name": "internalLinks", "value": "{{if(contains(29.`4`; \",\"); split(29.`4`; \",\"); \"[\"\"\" + 29.`4` + \"\"\"]\")}}"}, {"name": "externalLinks", "value": "{{if(contains(29.`5`; \",\"); split(29.`5`; \",\"); \"[\"\"\" + 29.`5` + \"\"\"]\")}}"}, {"name": "additionalInstructions", "value": "{{29.`6`}}"}]}, "metadata": {"designer": {"x": -1038, "y": -386, "name": "Set Variables"}, "restore": {"expect": {"scope": {"label": "One cycle"}, "variables": {"items": [null, null, null, null, null, null, null]}}}, "expect": [{"name": "variables", "spec": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "value", "type": "any", "label": "Variable value"}], "type": "array", "label": "Variables"}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}], "interface": [{"name": "title", "type": "any", "label": "title"}, {"name": "wordCount", "type": "any", "label": "wordCount"}, {"name": "primaryKeyword", "type": "any", "label": "primaryKeyword"}, {"name": "secondaryKeywords", "type": "any", "label": "secondaryKeywords"}, {"name": "internalLinks", "type": "any", "label": "internalLinks"}, {"name": "externalLinks", "type": "any", "label": "externalLinks"}, {"name": "additionalInstructions", "type": "any", "label": "additionalInstructions"}]}}, {"id": 6, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 1134482}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You're a helpful, intelligent writing assistant."}, {"role": "user", "content": "Use the following JSON brief to write an engaging, comprehensive outline in Markdown format (atx). Tone of voice: 50% spartan, casual."}, {"role": "user", "content": "{\n  \"title\": \"Accessibility in Retail: How to Make Your Store More Accessible\",\n  \"wordCount\": 1000,\n  \"primaryKeyword\": \"accessibility in retail\",\n  \"secondaryKeywords\": [\n    \"accessible e-commerce\",\n    \"how to make your website accessible\"\n  ],\n  \"internalLinks\": [\n    \"https://www.ontario.ca/page/how-make-websites-accessible\"\n  ],\n  \"externalLinks\": [\n    \"https://www.forbes.com/sites/forbesbusinesscouncil/2023/03/20/understanding-the-importance-of-web-accessibility/\"\n  ],\n  \"additionalInstructions\": \"Don't pitch the company at all - make it very informative and provide as much value as you can.\"\n}"}, {"role": "assistant", "content": "# Accessibility in Retail: How to Make Your Store More Accessible\n\nOnline marketplaces have become increasingly popular since the mid-90s when retail juggernauts like Amazon initially burst onto the scene. And while it’s true that nearly everybody buys products in e-commerce stores these days, it’s crucial to understand that not all consumers have the same needs. \n\nOver one billion people have disabilities worldwide, meaning up to 15% of the global population may need assistive technology to use online shopping platforms. Addressing their needs is critical if you want to maximize your reach & customer experience.\n\nSo, how exactly does digital accessibility impact businesses, and what can we (practically) do about it? Read on for answers below.\n\n## Introduction\n\n- Briefly define what \"accessibility in retail\" means \n- Highlight the importance of retail accessibility.\n\n## The Significance of Accessibility in Retail\n\n- Discuss how creating an accessible retail space can broaden market reach \n- Discuss some benefits of retail accessibility. \n- Share valuable insights about retail accessibility from [\"Forbes\"](https://www.forbes.com/sites/forbesbusinesscouncil/2023/03/20/understanding-the-importance-of-web-accessibility/).\n\n## Exploring Accessible E-commerce: The New Normal in Retail \n\n- Explain the rise and importance of e-commerce in today's retail landscape. \n- Highlight how e-commerce has opened up more possibilities for accessibility in retail.\n\n## Guidelines to Make E-commerce Stores Accessible\n\n- Present some of the key accessibility standards for online stores.\n- Offer brief step-by-step guide on how to make your website accessible. \n- Indicate best practices in creating accessible e-commerce stores, citing resources from [\"Ontario.ca\"](https://www.ontario.ca/page/how-make-websites-accessible).\n\n## Physical Store Accessibility: Not to be Overlooked \n\n- Assert the need for physical store accessibility in addition to online stores.\n- Enumerate practical solutions for enhancing physical store accessibility.\n\n## Encouraging Continuous Efforts for Accessibility\n\n- Inspire readers to continually strive for better accessibility in their retail environments.\n- Reiterate the benefits of and the need for accessibility in the retail sector for a more inclusive future.\n\n## Conclusion\n\n- Summarize the key points and lessons learned about retail accessibility.\n- Encourage readers to implement the suggestions provided."}, {"role": "user", "content": "{\n  \"title\": \"{{30.title}}\",\n  \"wordCount\": {{30.wordCount}},\n  \"primaryKeyword\": \"{{30.primaryKeyword}}\",\n  \"secondaryKeywords\":{{30.secondaryKeywords}},\n  \"internalLinks\":{{30.internalLinks}},\n  \"externalLinks\":{{30.externalLinks}},\n  \"additionalInstructions\": \"{{30.additionalInstructions}}\"\n}"}]}, "metadata": {"designer": {"x": -748, "y": -385, "name": "Generate Outline"}, "restore": {"expect": {"echo": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion (GPT Models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}]}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "1SecondCopy"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "echo", "type": "boolean", "label": "Echo"}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content"}], "type": "array", "label": "Messages", "required": true}]}, "onerror": [{"id": 7, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": -751, "y": -640}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 8, "module": "markdown:<PERSON><PERSON><PERSON>", "version": 2, "parameters": {}, "mapper": {"gfm": true, "data": "{{6.choices[].message.content}}", "sanitize": false}, "metadata": {"designer": {"x": -456, "y": -380}, "restore": {"expect": {"gfm": {"mode": "chose"}, "sanitize": {"mode": "chose"}}}, "expect": [{"name": "data", "type": "text", "label": "<PERSON><PERSON>"}, {"name": "gfm", "type": "boolean", "label": "GitHub Flavored Markdown", "required": true}, {"name": "sanitize", "type": "boolean", "label": "Sanitize", "required": true}], "advanced": true}}, {"id": 31, "module": "google-drive:createAFolder", "version": 4, "parameters": {"__IMTCONN__": 2154166}, "mapper": {"name": "{{30.title}}", "role": "writer", "type": "anyone", "shared": true, "folderId": "/1gvPOkXzbxwzSyL2sQlLhil8Kk7hCH_Gr/1zG-MVTy32YkVkvzcRqKc9zk_O8t7Zex-/14v4cCv-ts-hUk-drFwbHfLmRPXf4rF-x", "destination": "drive"}, "metadata": {"designer": {"x": -155, "y": -379}, "restore": {"expect": {"role": {"mode": "chose", "label": "Writer"}, "type": {"mode": "chose", "label": "Anyone"}, "folderId": {"mode": "chose", "path": ["LeftClick", "Sales & Marketing", "SEO"]}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "<EMAIL> (Feb 13 2024)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"name": "destination", "type": "select", "label": "New Drive Location", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "New Folder Location", "required": true}, {"name": "name", "type": "text", "label": "New Folder's Name"}, {"name": "shared", "type": "boolean", "label": "Share Folder", "required": true}, {"name": "role", "type": "select", "label": "Role", "required": true, "validate": {"enum": ["owner", "writer", "commenter", "reader"]}}, {"name": "type", "type": "select", "label": "Type", "required": true, "validate": {"enum": ["user", "group", "domain", "anyone"]}}, {"name": "destination", "type": "select", "label": "New Drive Location", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "New Folder Location", "required": true}, {"name": "name", "type": "text", "label": "New Folder's Name"}, {"name": "shared", "type": "boolean", "label": "Share Folder", "required": true}, {"name": "role", "type": "select", "label": "Role", "required": true, "validate": {"enum": ["owner", "writer", "commenter", "reader"]}}, {"name": "type", "type": "select", "label": "Type", "required": true, "validate": {"enum": ["user", "group", "domain", "anyone"]}}]}}, {"id": 9, "module": "google-docs:createADocument", "version": 1, "parameters": {"__IMTCONN__": 2154157}, "mapper": {"name": "O: {{30.title}}", "footer": false, "header": false, "content": "{{8.data}}", "folderId": "{{31.id}}", "destination": "drive"}, "metadata": {"designer": {"x": 146, "y": -377, "name": "Create Outline"}, "restore": {"expect": {"folderId": {"mode": "edit", "path": []}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "<EMAIL> (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "content", "type": "text", "label": "Content", "required": true}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "header", "type": "boolean", "label": "Insert a Header", "required": true}, {"name": "footer", "type": "boolean", "label": "Insert a Footer", "required": true}, {"name": "folderId", "type": "folder", "label": "New Document's Location", "required": true}, {"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "content", "type": "text", "label": "Content", "required": true}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "header", "type": "boolean", "label": "Insert a Header", "required": true}, {"name": "footer", "type": "boolean", "label": "Insert a Footer", "required": true}, {"name": "folderId", "type": "folder", "label": "New Document's Location", "required": true}]}}, {"id": 11, "module": "util:SetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "inputBrief", "scope": "roundtrip", "value": "{{6.choices[].message.content}}"}, "metadata": {"designer": {"x": 446, "y": -366, "name": "inputBrief"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "inputBrief", "type": "any", "label": "inputBrief"}]}}, {"id": 12, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{split(11.inputBrief; newline)}}"}, "metadata": {"designer": {"x": 748, "y": -366}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 13, "module": "util:SetVariable2", "version": 1, "parameters": {}, "filter": {"name": "Section Starts with ##", "conditions": [[{"a": "{{12.value}}", "b": "## ", "o": "text:startwith"}]]}, "mapper": {"name": "sectionInput", "scope": "roundtrip", "value": "{{replace(11.inputBrief; 12.value; 12.value + \" <--\")}}"}, "metadata": {"designer": {"x": 1117, "y": -368, "name": "Set sectionInput"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "sectionInput", "type": "any", "label": "sectionInput"}]}}, {"id": 14, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 1134482}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You're a helpful, intelligent writing assistant."}, {"role": "user", "content": "The following is an outline for an award-winning article. Your task is to write one section and one section only: the one marked by a \"<--\". Tone of voice: 50% spartan, casual.\n\n---\n\n{{13.sectionInput}}"}]}, "metadata": {"designer": {"x": 1452, "y": -368, "name": "Produce Section Text"}, "restore": {"expect": {"echo": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion (GPT Models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}]}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "1SecondCopy"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "echo", "type": "boolean", "label": "Echo"}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content"}], "type": "array", "label": "Messages", "required": true}]}, "onerror": [{"id": 15, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 1447, "y": -622}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 16, "module": "util:SetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "sectionText", "scope": "roundtrip", "value": "{{14.choices[].message.content}}"}, "metadata": {"designer": {"x": 1744, "y": -366, "name": "sectionText"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "sectionText", "type": "any", "label": "sectionText"}]}}, {"id": 17, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 2030, "y": -367}}, "routes": [{"flow": [{"id": 18, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 1134482}, "filter": {"name": "25%", "conditions": [[{"a": "{{floor(random * (4)) + 1}}", "b": "3", "o": "number:equal"}]]}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You're a helpful, intelligent writing assistant."}, {"role": "user", "content": "Edit the following text to break up the flow. Add bullet points and subheadings where needed for variety. Use Markdown (atx) format."}, {"role": "user", "content": "## Making Use of AI Writing Tools\n\nIncorporating AI into your writing workflow requires a strong understanding of the available tools and how they can boost your productivity. Whether you're a novella writer looking to streamline your plot development process or a content marketer aiming to optimize your SEO, various AI writing tools are at your disposal.\n\nAmong the most versatile AI writing tools is ChatGPT, designed by OpenAI. This tool is designed to assist with content generation, ideation, formatting, and even translation - enabling you to create everything from a creative blog post to a professional report. Thanks to its machine learning capabilities, the quality of the content it helps you generate even improves over time based on your input and feedback.\n\nFor those primarily concerned with editing and proofreading, tools like Grammarly and ProWritingAid are invaluable. Using AI algorithms, these tools assess your writing for grammatical errors, readability, and more, helping you polish your content to perfection."}, {"role": "assistant", "content": "## Making Use of AI Writing Tools\n\nIncorporating AI into your writing workflow requires a strong understanding of the available tools and how they can boost your productivity. Whether you're a novella writer looking to streamline your plot development process or a content marketer aiming to optimize your SEO, below are a few of the hottest AI tools you can use to improve your productivity.\n\n### AI Writing Tools\n\nAmong the most versatile AI writing tools is ChatGPT, designed by OpenAI. This tool is designed to assist with:\n- Content generation,\n- Ideation,\n- Automatic formatting, &\n- Translation\n\nThanks to its machine learning capabilities, the quality of the content it helps you generate even improves over time based on your input and feedback.\n\n## AI Editing Tools\n\nFor those primarily concerned with editing and proofreading, on the other hand, here are a few high-quality tools you can use to skip the QA.\n- Grammarly, \n- ProWritingAid,\n\nUsing natural language processing, these tools instantly assess your writing for grammatical errors, readability, and more, helping you polish your content to perfection."}, {"role": "user", "content": "{{14.choices[].message.content}}"}]}, "metadata": {"designer": {"x": 2331, "y": -630, "name": "Produce Section Text"}, "restore": {"expect": {"echo": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion (GPT Models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}]}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "1SecondCopy"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "echo", "type": "boolean", "label": "Echo"}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content"}], "type": "array", "label": "Messages", "required": true}]}, "onerror": [{"id": 19, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 2325, "y": -934}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 20, "module": "util:SetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "sectionText", "scope": "roundtrip", "value": "{{18.choices[].message.content}}"}, "metadata": {"designer": {"x": 2625, "y": -631, "name": "sectionText"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "sectionText", "type": "any", "label": "sectionText"}]}}]}, {"flow": [{"id": 21, "module": "util:GetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "sectionText"}, "metadata": {"designer": {"x": 2328, "y": -363, "name": "Get sectionText"}, "restore": {}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}], "interface": [{"name": "sectionText", "type": "any", "label": "sectionText"}]}}, {"id": 22, "module": "util:TextAggregator", "version": 1, "parameters": {"feeder": 12, "rowSeparator": "\n"}, "mapper": {"value": "{{21.sectionText}}"}, "metadata": {"designer": {"x": 2621, "y": -365, "name": "Aggregate Section Text"}, "restore": {"extra": {"feeder": {"label": "Iterator [13]"}}, "parameters": {"rowSeparator": {"label": "New row"}}}, "parameters": [{"name": "rowSeparator", "type": "select", "label": "Row separator", "validate": {"enum": ["\n", "\t", "other"]}}], "expect": [{"name": "value", "type": "text", "label": "Text"}], "advanced": true}}, {"id": 23, "module": "markdown:<PERSON><PERSON><PERSON>", "version": 2, "parameters": {}, "mapper": {"gfm": true, "data": "{{get(split(11.inputBrief; \"##\"); 1)}}\n\n{{22.text}}", "sanitize": false}, "metadata": {"designer": {"x": 2915, "y": -366}, "restore": {"expect": {"gfm": {"mode": "chose"}, "sanitize": {"mode": "chose"}}}, "expect": [{"name": "data", "type": "text", "label": "<PERSON><PERSON>"}, {"name": "gfm", "type": "boolean", "label": "GitHub Flavored Markdown", "required": true}, {"name": "sanitize", "type": "boolean", "label": "Sanitize", "required": true}], "advanced": true}}, {"id": 24, "module": "google-docs:createADocument", "version": 1, "parameters": {"__IMTCONN__": 2154157}, "mapper": {"name": "A: {{30.title}}", "footer": false, "header": false, "content": "{{23.data}}", "folderId": "{{31.id}}", "destination": "drive"}, "metadata": {"designer": {"x": 3206, "y": -369, "name": "Create Article"}, "restore": {"expect": {"folderId": {"mode": "edit", "path": []}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "<EMAIL> (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "content", "type": "text", "label": "Content", "required": true}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "header", "type": "boolean", "label": "Insert a Header", "required": true}, {"name": "footer", "type": "boolean", "label": "Insert a Footer", "required": true}, {"name": "folderId", "type": "folder", "label": "New Document's Location", "required": true}, {"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "content", "type": "text", "label": "Content", "required": true}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "header", "type": "boolean", "label": "Insert a Header", "required": true}, {"name": "footer", "type": "boolean", "label": "Insert a Footer", "required": true}, {"name": "folderId", "type": "folder", "label": "New Document's Location", "required": true}]}}, {"id": 37, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 1134482}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You're a helpful, intelligent writing assistant."}, {"role": "user", "content": "Categorize the following post and add the appropriate tags."}, {"role": "user", "content": "# Accessibility in Retail: How to Make Your Store More Accessible\n\nOnline marketplaces have become increasingly popular since the mid-90s when retail juggernauts like Amazon initially burst onto the scene. And while it’s true that nearly everybody buys products in e-commerce stores these days, it’s crucial to understand that not all consumers have the same needs. \n\nOver one billion people have disabilities worldwide, meaning up to 15% of the global population may need assistive technology to use online shopping platforms. Addressing their needs is critical if you want to maximize your reach & customer experience.\n\nSo, how exactly does digital accessibility impact businesses, and what can we (practically) do about it? Read on for answers below.\n\n## Introduction\n\n- Briefly define what \"accessibility in retail\" means \n- Highlight the importance of retail accessibility.\n\n## The Significance of Accessibility in Retail\n\n- Discuss how creating an accessible retail space can broaden market reach \n- Discuss some benefits of retail accessibility. \n- Share valuable insights about retail accessibility from [\"Forbes\"](https://www.forbes.com/sites/forbesbusinesscouncil/2023/03/20/understanding-the-importance-of-web-accessibility/).\n\n## Exploring Accessible E-commerce: The New Normal in Retail \n\n- Explain the rise and importance of e-commerce in today's retail landscape. \n- Highlight how e-commerce has opened up more possibilities for accessibility in retail.\n\n## Guidelines to Make E-commerce Stores Accessible\n\n- Present some of the key accessibility standards for online stores.\n- Offer brief step-by-step guide on how to make your website accessible. \n- Indicate best practices in creating accessible e-commerce stores, citing resources from [\"Ontario.ca\"](https://www.ontario.ca/page/how-make-websites-accessible).\n\n## Physical Store Accessibility: Not to be Overlooked \n\n- Assert the need for physical store accessibility in addition to online stores.\n- Enumerate practical solutions for enhancing physical store accessibility.\n\n## Encouraging Continuous Efforts for Accessibility\n\n- Inspire readers to continually strive for better accessibility in their retail environments.\n- Reiterate the benefits of and the need for accessibility in the retail sector for a more inclusive future.\n\n## Conclusion\n\n- Summarize the key points and lessons learned about retail accessibility.\n- Encourage readers to implement the suggestions provided."}, {"role": "assistant", "content": "{\"category\":\"Retail Accessibility\", \"tags\":[\"e-commerce\", \"digital accessibility\", \"guide\"]}"}, {"role": "user", "content": "{{6.choices[].message.content}}"}], "temperature": "1", "response_format": "text"}, "metadata": {"designer": {"x": 3495, "y": -366, "name": "Categorize"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion (GPT Models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "1SecondCopy"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": [{"name": "token", "type": "text", "label": "Token", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "array", "label": "Token Probability"}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content"}], "type": "array", "label": "Messages", "required": true}]}}, {"id": 50, "module": "regexp:<PERSON>lace", "version": 1, "parameters": {}, "mapper": {"text": "{{22.text}}", "value": "'", "global": true, "pattern": "\"", "multiline": false, "sensitive": true, "singleline": false}, "metadata": {"designer": {"x": 3772, "y": -362}, "restore": {"expect": {"global": {"mode": "chose"}, "multiline": {"mode": "chose"}, "sensitive": {"mode": "chose"}, "singleline": {"mode": "chose"}}}, "expect": [{"name": "pattern", "type": "text", "label": "Pattern", "required": true}, {"name": "value", "type": "text", "label": "New value"}, {"name": "global", "type": "boolean", "label": "Global match", "required": true}, {"name": "sensitive", "type": "boolean", "label": "Case sensitive", "required": true}, {"name": "multiline", "type": "boolean", "label": "Multiline", "required": true}, {"name": "singleline", "type": "boolean", "label": "Singleline", "required": true}, {"name": "text", "type": "text", "label": "Text"}]}}, {"id": 38, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{{37.choices[].message.content}}"}, "metadata": {"designer": {"x": 4057, "y": -364}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}}, {"id": 40, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": false, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "https://us-central1-festive-bazaar-397323.cloudfunctions.net/contentful-markdown-to-rtf", "data": "{\"content\": \"{{replace(50.text; newline; \"\\n\")}}\"}", "gzip": true, "method": "post", "headers": [], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 4371, "y": -361, "name": "Convert to Contentfull RTF"}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose"}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}, "onerror": [{"id": 41, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 4362, "y": -665}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 42, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{\"entry\":{{40.data}}}"}, "metadata": {"designer": {"x": 4676, "y": -356}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}}, {"id": 48, "module": "custom-js:execute", "version": 1, "parameters": {"jscode": "return input.toLowerCase().replace(/[^a-z0-9]+/g,'-').replace(/(^-|-$)/g, '')", "__IMTCONN__": 1461551}, "mapper": {"input": "{{30.title}}"}, "metadata": {"designer": {"x": 4966, "y": -352}, "restore": {"parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "custom-js"}, "label": "My Custom JS connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:custom-js", "label": "Connection", "required": true}, {"name": "jscode", "type": "text", "label": "JavaScript Code", "required": true}], "expect": [{"name": "input", "type": "text", "label": "Input"}]}, "onerror": [{"id": 49, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 4969, "y": -650}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 36, "module": "contentful:createAnEntry", "version": 1, "parameters": {"__IMTCONN__": 1461156}, "mapper": {"fields": {"slug": "{{48.output}}", "title": "{{30.title}}", "content": "{{42.entry}}", "category": "{{38.category}}"}, "locale": "en-US", "contentTypeID": "blogPost"}, "metadata": {"designer": {"x": 5276, "y": -353}, "restore": {"expect": {"tagID": {"mode": "chose"}, "fields": {"nested": {"tags": {"mode": "chose"}, "relatedPosts": {"mode": "chose"}}}, "locale": {"mode": "chose", "label": "English (United States)"}, "contentTypeID": {"label": "Blog post"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "contentful"}, "label": "My Contentful connection (Blank)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:contentful", "label": "Connection", "required": true}], "expect": [{"name": "contentTypeID", "type": "select", "label": "Content Type", "required": true}, {"name": "newID", "type": "text", "label": "Custom Asset ID", "validate": {"pattern": "(^[a-z]|[A-Z0-9])[a-z]*"}}, {"name": "tagID", "type": "select", "label": "Tag", "multiple": true}, {"name": "locale", "type": "select", "label": "Locale", "required": true}, {"name": "fields", "spec": [{"name": "title", "type": "text", "label": "Title", "metadata": {"type": "Symbol", "linkType": null, "itemsType": null}}, {"name": "category", "type": "text", "label": "Category", "metadata": {"type": "Symbol", "linkType": null, "itemsType": null}}, {"name": "date", "type": "date", "label": "Date", "metadata": {"type": "Date", "linkType": null, "itemsType": null}}, {"name": "image", "type": "any", "label": "Image", "metadata": {"type": "Link", "linkType": "<PERSON><PERSON>", "itemsType": null}}, {"name": "content", "type": "any", "label": "Content", "metadata": {"type": "RichText", "linkType": null, "itemsType": null}}, {"name": "author", "type": "any", "label": "Author", "metadata": {"type": "Link", "linkType": "Entry", "itemsType": null}}, {"name": "tags", "spec": {"name": "value", "type": "text"}, "type": "array", "label": "Tags", "metadata": {"type": "Array", "linkType": null, "itemsType": "Symbol"}}, {"name": "relatedPosts", "spec": {"name": "value", "type": "text"}, "type": "array", "label": "Related posts", "metadata": {"type": "Array", "linkType": "Entry", "itemsType": "Link"}}, {"name": "slug", "type": "text", "label": "Slug", "metadata": {"type": "Symbol", "linkType": null, "itemsType": null}, "required": true}], "type": "collection", "label": "Fields"}]}}, {"id": 33, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 2154157}, "mapper": {"from": "share", "mode": "select", "values": {"7": "{{9.webViewLink}}", "8": "{{24.webViewLink}}", "9": "https://leftclick.ai/blog/{{48.output}}"}, "sheetId": "Main", "rowNumber": "{{29.`__ROW_NUMBER__`}}", "spreadsheetId": "/1ZpEZYezX_9F7lSixYluzrNl_GAjJxJPFPENdmLLGtPE", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": 5573, "y": -354}, "restore": {"expect": {"from": {"label": "Shared With Me"}, "mode": {"label": "Select by path"}, "sheetId": {"label": "Main"}, "spreadsheetId": {"path": ["LeftClick Content Generation Tracker"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "Title (A)"}, {"name": "1", "type": "text", "label": "Word Count (B)"}, {"name": "2", "type": "text", "label": "Primary Keyword (C)"}, {"name": "3", "type": "text", "label": "Secondary Keyword(s) (D)"}, {"name": "4", "type": "text", "label": "Internal Links (E)"}, {"name": "5", "type": "text", "label": "External Links (F)"}, {"name": "6", "type": "text", "label": "Additional Instructions (G)"}, {"name": "7", "type": "text", "label": "Outline Doc (H)"}, {"name": "8", "type": "text", "label": "Article Doc (I)"}, {"name": "9", "type": "text", "label": "Website URL (J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "<EMAIL> (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Choose a Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "from", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Title (A)"}, {"name": "1", "type": "text", "label": "Word Count (B)"}, {"name": "2", "type": "text", "label": "Primary Keyword (C)"}, {"name": "3", "type": "text", "label": "Secondary Keyword(s) (D)"}, {"name": "4", "type": "text", "label": "Internal Links (E)"}, {"name": "5", "type": "text", "label": "External Links (F)"}, {"name": "6", "type": "text", "label": "Additional Instructions (G)"}, {"name": "7", "type": "text", "label": "Outline Doc (H)"}, {"name": "8", "type": "text", "label": "Article Doc (I)"}, {"name": "9", "type": "text", "label": "Website URL (J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}, {"name": "mode", "type": "select", "label": "Choose a Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "from", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Title (A)"}, {"name": "1", "type": "text", "label": "Word Count (B)"}, {"name": "2", "type": "text", "label": "Primary Keyword (C)"}, {"name": "3", "type": "text", "label": "Secondary Keyword(s) (D)"}, {"name": "4", "type": "text", "label": "Internal Links (E)"}, {"name": "5", "type": "text", "label": "External Links (F)"}, {"name": "6", "type": "text", "label": "Additional Instructions (G)"}, {"name": "7", "type": "text", "label": "Outline Doc (H)"}, {"name": "8", "type": "text", "label": "Article Doc (I)"}, {"name": "9", "type": "text", "label": "Website URL (J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}, {"id": 54, "module": "slack:Create<PERSON><PERSON><PERSON>", "version": 4, "parameters": {"__IMTCONN__": 1134493}, "mapper": {"parse": false, "blocks": "{\n\t\"blocks\": [\n\t\t{\n\t\t\t\"type\": \"header\",\n\t\t\t\"text\": {\n\t\t\t\t\"type\": \"plain_text\",\n\t\t\t\t\"text\": \"AI generated a new article 🎉\",\n\t\t\t\t\"emoji\": true\n\t\t\t}\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"fields\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*Title*\\n{{30.title}}\\n\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*When*\\n{{formatDate(now; \"HH:mm, DD/MM/YYYY\")}}\"\n\t\t\t\t}\n\t\t\t]\n\t\t},\n\t\t{\n\t\t\t\"type\": \"section\",\n\t\t\t\"fields\": [\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*Google Doc Link*\\n<{{24.webViewLink}}|docs.google.com>\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"type\": \"mrkdwn\",\n\t\t\t\t\t\"text\": \"*Contentful Link*\\n<https://app.contentful.com/spaces/gvffca1rk71p/entries/{{36.sys.id}}|app.contentful.com>\"\n               }\n\t\t\t]\n\t\t}\n\t]\n}", "mrkdwn": true, "channel": "C05R979971B", "icon_url": "https://drive.google.com/uc?export=download&id=1qtoqWU1aae_NojCNRTROCakdMTuaQRME", "username": "LeftClick", "channelType": "public", "channelWType": "list"}, "metadata": {"designer": {"x": 5877, "y": -352}, "restore": {"expect": {"parse": {"mode": "chose"}, "mrkdwn": {"mode": "chose"}, "channel": {"mode": "chose", "label": "seo"}, "link_names": {"mode": "chose"}, "channelType": {"label": "Public channel"}, "channelWType": {"label": "Select from the list"}, "unfurl_links": {"mode": "chose"}, "unfurl_media": {"mode": "chose"}, "reply_broadcast": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "slack3"}, "label": "LeftClick (integromat)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:slack2,slack3", "label": "Connection", "required": true}], "expect": [{"name": "channelWType", "type": "select", "label": "Enter a channel ID or name", "required": true, "validate": {"enum": ["manualy", "list"]}}, {"name": "text", "type": "text", "label": "Text"}, {"name": "blocks", "type": "text", "label": "Blocks"}, {"name": "thread_ts", "type": "text", "label": "Thread message ID (time stamp)"}, {"name": "reply_broadcast", "type": "boolean", "label": "Reply broadcast"}, {"name": "link_names", "type": "boolean", "label": "Link names"}, {"name": "parse", "type": "boolean", "label": "Parse message text"}, {"name": "mrkdwn", "type": "boolean", "label": "Use markdown"}, {"name": "unfurl_links", "type": "boolean", "label": "Unfurl primarily text-based content"}, {"name": "unfurl_media", "type": "boolean", "label": "Unfurl media content"}, {"name": "icon_emoji", "type": "text", "label": "Icon emoji"}, {"name": "icon_url", "type": "url", "label": "Icon url"}, {"name": "username", "type": "text", "label": "User name"}, {"name": "channelType", "type": "select", "label": "Channel type", "required": true, "validate": {"enum": ["public", "private", "im", "mpim"]}}, {"name": "channel", "type": "select", "label": "Public channel", "required": true}], "advanced": true}}]}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": true, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us1.make.com"}}