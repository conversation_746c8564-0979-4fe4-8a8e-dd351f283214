{"name": "GMAIL AGENT", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyHour"}]}, "filters": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-20, -20], "id": "7661e927-1cb9-4646-a631-947c06b39d59", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"inputText": "={{ $json.snippet }}", "categories": {"categories": [{"category": "Sales Inquiry", "description": "Emails asking about your services, pricing, packages, or general sales-related questions."}, {"category": "Meeting Request", "description": "Emails asking to schedule a call, meeting, or any kind of appointment."}, {"category": "Potential Clients", "description": "Emails showing serious interest in working with you, discussing specific problems they want solved, or clearly describing a business opportunity."}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1.1, "position": [200, -20], "id": "3c53ab6d-3986-45e1-817f-d3283154d258", "name": "Text Classifier"}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [260, 160], "id": "f9eec350-f99b-4d15-a70b-6478ebb26b89", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1080, 180], "id": "7729ce8a-29d7-4f40-8590-a0188800d7b5", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1400, 140], "id": "2da184ed-0196-47d5-925b-42484847ea1c", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "labelIds": ["Label_2769463820340569867"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [720, -200], "id": "7858e75f-6598-42f9-b060-6cb7507c964e", "name": "Label 1", "webhookId": "f7cff08d-f0bd-4ac7-abd0-899769786df6", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "labelIds": ["Label_9203220357330232849"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [720, -20], "id": "21f14ae2-8bac-4600-9c1f-1902fc17cfa2", "name": "Label 2", "webhookId": "f7cff08d-f0bd-4ac7-abd0-899769786df6", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "labelIds": ["Label_1534483885587560921"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [720, 180], "id": "e94e9d8d-f4b8-47a4-98ff-e65d9b81daf0", "name": "Label 3", "webhookId": "f7cff08d-f0bd-4ac7-abd0-899769786df6", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('G<PERSON> Trigger').item.json.snippet }}", "options": {"systemMessage": "You are a polite, helpful, and professional email assistant for a (Your_Profession)\nBased on the input category and original email content, generate a short, personalized, and professional reply in a friendly tone.\n\nThere are 3 categories:\n\nSales Inquiry – Provide a warm response thanking them for their interest and mention that you’ll share pricing details or more information soon.\n\nMeeting Request – Acknowledge the request and suggest scheduling a meeting using a link (e.g., <PERSON>ndly).\n\nPotential Clients – Respond with excitement, express that you're eager to help, and ask a follow-up question to better understand their needs.\n\nKeep the response around 3–4 sentences. Always sound professional yet approachable.\n\n\nAbout Me:\n\n\nMy Facebook:\nMy Instagram:\nMy Linkedin\n\ngoogel meeting link:\nCalendly link:"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1040, -20], "id": "07601adb-9ead-4c15-8f91-a08ac2a9057f", "name": "Reply Generator"}, {"parameters": {"text": "={{ $('<PERSON><PERSON> Trigger').item.json.From }}\n{{ $json.output }}", "attributes": {"attributes": [{"name": "G<PERSON> Reply", "description": "get the perfect solid reply"}, {"name": "Sender Mail Address", "description": "find the sender mail address"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [1400, -20], "id": "d2b70a45-3520-4d35-a505-3958580af775", "name": "<PERSON><PERSON> and Email Address Extructor"}, {"parameters": {"resource": "draft", "subject": "Hello Word", "message": "={{ $json.output['Gmail Reply'] }}", "options": {"threadId": "={{ $('Gmail Trigger').item.json.id }}", "sendTo": "={{ $json.output['Sender Mail Address'] }}"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1800, -20], "id": "bab5187f-d197-44ac-90d1-a1b5cc92a597", "name": "Send Draft Message", "webhookId": "8dd0e000-17e1-444e-9037-a2ba87074536", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Label 1", "type": "main", "index": 0}], [{"node": "Label 2", "type": "main", "index": 0}], [{"node": "Label 3", "type": "main", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Reply Generator", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "<PERSON><PERSON> and Email Address Extructor", "type": "ai_languageModel", "index": 0}]]}, "Label 1": {"main": [[{"node": "Reply Generator", "type": "main", "index": 0}]]}, "Label 2": {"main": [[{"node": "Reply Generator", "type": "main", "index": 0}]]}, "Label 3": {"main": [[{"node": "Reply Generator", "type": "main", "index": 0}]]}, "Reply Generator": {"main": [[{"node": "<PERSON><PERSON> and Email Address Extructor", "type": "main", "index": 0}]]}, "Reply and Email Address Extructor": {"main": [[{"node": "Send Draft Message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0bb7e0d3-a434-4ee1-a6c3-b6bd2feb038f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5c2f5b5b4cf20114a6c53aaed9430bfdabad5c4604b5a73e1363b96c75e842ec"}, "id": "GuFX8zDT7vB3nYVo", "tags": []}