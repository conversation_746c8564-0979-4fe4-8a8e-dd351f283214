{"name": "2. Adding Files From Drive To Database", "nodes": [{"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Set File ID').item.json.file_id }}"}]}}}, "id": "951d2424-75e9-4484-9c1c-2d67d871057f", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [340, 220]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "e607cca6-44b0-473f-9c0e-300522428ba2", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [120, 340], "credentials": {"openAiApi": {"id": "Eq5hz9TcNAkYHxaL", "name": "OpenAi account 3"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set File ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "e563171d-c710-4e44-9130-203477cf79ad", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-240, 120], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "p97gYupjHwcLTuKc", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1w-haIDP75kk6OAnQXJdJLF9FJIyrYHSQ", "mode": "list", "cachedResultName": "Template Notes ", "cachedResultUrl": "https://drive.google.com/drive/folders/1w-haIDP75kk6OAnQXJdJLF9FJIyrYHSQ"}, "event": "fileCreated", "options": {}}, "id": "829b7106-f311-4a22-bf01-07fa594a15f6", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-960, 0], "credentials": {"googleDriveOAuth2Api": {"id": "p97gYupjHwcLTuKc", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1w-haIDP75kk6OAnQXJdJLF9FJIyrYHSQ", "mode": "list", "cachedResultName": "Template Notes ", "cachedResultUrl": "https://drive.google.com/drive/folders/1w-haIDP75kk6OAnQXJdJLF9FJIyrYHSQ"}, "event": "fileUpdated", "options": {}}, "id": "43d8ff9a-38cc-4e63-9f09-7a09012d0bcc", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-960, 260], "credentials": {"googleDriveOAuth2Api": {"id": "p97gYupjHwcLTuKc", "name": "Google Drive account"}}}, {"parameters": {"operation": "text", "options": {}}, "id": "15fc7cab-fb4a-4466-ba7e-97c9d8c886ff", "name": "Extract Document Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-20, 120], "alwaysOutputData": true}, {"parameters": {"options": {}}, "id": "d7d13107-1ce5-470c-97b7-353ed045d2ea", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [400, 400]}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "98ef5ef2-da9c-45f2-b22e-133d31d87b26", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-440, 120], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "5zkfZLWcoQNG5AKm", "name": "Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}]}, "options": {}}, "id": "3994876e-eec7-49bc-b698-1b19a09b6870", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-640, 120]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {"queryName": "match_documents"}}, "id": "622e6b17-86d0-4fe5-b123-cb6de1af9e8a", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [260, -20], "credentials": {"supabaseApi": {"id": "5zkfZLWcoQNG5AKm", "name": "Supabase account"}}}], "pinData": {}, "connections": {"Download File": {"main": [[{"node": "Extract Document Text", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Set File ID", "type": "main", "index": 0}]]}, "Extract Document Text": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Set File ID", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3cd3d93b-f790-4c5a-aff4-23301278d8ad", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e9750110772e39d19e520d5411ea772dd5055cd081f0298a554cb86fb57bf55d"}, "id": "REDhx8A9wB9shTr0", "tags": [{"createdAt": "2025-02-11T07:19:10.054Z", "updatedAt": "2025-02-11T07:49:34.353Z", "id": "NPbbeoP4jOohrmIG", "name": "05 Rag Supabase"}]}