const logger = require('../utils/logger');
const fs = require('fs-extra');
const path = require('path');

class WorkflowParser {
    constructor(options = {}) {
        this.outputDir = options.outputDir || './workflows';
        this.categoriesDir = path.join(this.outputDir, 'categories');
        
        // Ensure directories exist
        fs.ensureDirSync(this.categoriesDir);
        
        // Category mappings based on node types and workflow characteristics
        this.categoryMappings = {
            'ai': ['openai', 'anthropic', 'huggingface', 'ai', 'gpt', 'claude', 'llm', 'machine learning', 'ml'],
            'automation': ['schedule', 'cron', 'trigger', 'webhook', 'automation', 'workflow'],
            'integration': ['api', 'rest', 'graphql', 'database', 'sql', 'integration'],
            'data-processing': ['transform', 'filter', 'merge', 'split', 'data', 'csv', 'json', 'xml'],
            'communication': ['email', 'slack', 'discord', 'telegram', 'sms', 'notification'],
            'social-media': ['twitter', 'facebook', 'instagram', 'linkedin', 'social'],
            'productivity': ['google', 'microsoft', 'notion', 'airtable', 'spreadsheet'],
            'ecommerce': ['shopify', 'woocommerce', 'stripe', 'paypal', 'ecommerce', 'payment'],
            'monitoring': ['monitor', 'alert', 'health', 'status', 'uptime'],
            'file-management': ['file', 'upload', 'download', 'storage', 'ftp', 's3']
        };
    }

    async parseWorkflow(filePath) {
        try {
            logger.parser(`Parsing workflow: ${filePath}`);
            
            const workflowData = await fs.readJson(filePath);
            const analysis = this.analyzeWorkflow(workflowData);
            
            return {
                filePath,
                data: workflowData,
                analysis,
                isValid: analysis.isValid,
                errors: analysis.errors,
                warnings: analysis.warnings
            };
            
        } catch (error) {
            logger.error(`Failed to parse workflow ${filePath}:`, error.message);
            return {
                filePath,
                data: null,
                analysis: null,
                isValid: false,
                errors: [error.message],
                warnings: []
            };
        }
    }

    analyzeWorkflow(workflowData) {
        const analysis = {
            isValid: true,
            errors: [],
            warnings: [],
            metadata: {},
            categories: [],
            nodeTypes: [],
            complexity: 'simple',
            hasAI: false,
            hasWebhooks: false,
            hasSchedule: false,
            nodeCount: 0,
            connectionCount: 0
        };

        try {
            // Validate basic structure
            this.validateBasicStructure(workflowData, analysis);
            
            // Extract metadata
            this.extractMetadata(workflowData, analysis);
            
            // Analyze nodes
            this.analyzeNodes(workflowData, analysis);
            
            // Analyze connections
            this.analyzeConnections(workflowData, analysis);
            
            // Determine categories
            this.categorizeWorkflow(analysis);
            
            // Assess complexity
            this.assessComplexity(analysis);
            
        } catch (error) {
            analysis.isValid = false;
            analysis.errors.push(`Analysis failed: ${error.message}`);
        }

        return analysis;
    }

    validateBasicStructure(workflowData, analysis) {
        // Check for required properties
        if (!workflowData) {
            analysis.errors.push('Workflow data is null or undefined');
            analysis.isValid = false;
            return;
        }

        // Handle different workflow formats
        let workflow = workflowData;
        if (workflowData.workflow) {
            workflow = workflowData.workflow;
        }

        // Check for nodes
        if (!workflow.nodes || !Array.isArray(workflow.nodes)) {
            analysis.errors.push('Workflow must have a nodes array');
            analysis.isValid = false;
        }

        // Check for connections (optional but expected)
        if (workflow.connections && !Array.isArray(workflow.connections) && typeof workflow.connections !== 'object') {
            analysis.warnings.push('Connections should be an array or object');
        }

        // Validate node structure
        if (workflow.nodes) {
            workflow.nodes.forEach((node, index) => {
                if (!node.type) {
                    analysis.errors.push(`Node ${index} missing type`);
                    analysis.isValid = false;
                }
                if (!node.name) {
                    analysis.warnings.push(`Node ${index} missing name`);
                }
            });
        }
    }

    extractMetadata(workflowData, analysis) {
        let workflow = workflowData.workflow || workflowData;
        
        analysis.metadata = {
            name: workflow.name || 'Unnamed Workflow',
            description: workflow.description || '',
            tags: workflow.tags || [],
            author: workflow.author || '',
            version: workflow.version || '1.0.0',
            createdAt: workflow.createdAt || workflow.created_at || null,
            updatedAt: workflow.updatedAt || workflow.updated_at || null,
            active: workflow.active !== false, // Default to true
            settings: workflow.settings || {}
        };

        // Extract additional metadata from meta object
        if (workflow.meta) {
            Object.assign(analysis.metadata, workflow.meta);
        }
    }

    analyzeNodes(workflowData, analysis) {
        let workflow = workflowData.workflow || workflowData;
        
        if (!workflow.nodes) return;

        analysis.nodeCount = workflow.nodes.length;
        const nodeTypes = new Set();
        
        workflow.nodes.forEach(node => {
            if (node.type) {
                nodeTypes.add(node.type);
                
                // Check for specific node types
                const nodeType = node.type.toLowerCase();
                
                // AI-related nodes
                if (this.isAINode(nodeType)) {
                    analysis.hasAI = true;
                }
                
                // Webhook nodes
                if (nodeType.includes('webhook')) {
                    analysis.hasWebhooks = true;
                }
                
                // Schedule/Cron nodes
                if (nodeType.includes('schedule') || nodeType.includes('cron')) {
                    analysis.hasSchedule = true;
                }
            }
        });
        
        analysis.nodeTypes = Array.from(nodeTypes);
    }

    analyzeConnections(workflowData, analysis) {
        let workflow = workflowData.workflow || workflowData;
        
        if (!workflow.connections) return;

        // Handle different connection formats
        if (Array.isArray(workflow.connections)) {
            analysis.connectionCount = workflow.connections.length;
        } else if (typeof workflow.connections === 'object') {
            // n8n format: connections is an object with node names as keys
            analysis.connectionCount = Object.keys(workflow.connections).reduce((count, nodeKey) => {
                const nodeConnections = workflow.connections[nodeKey];
                if (nodeConnections && typeof nodeConnections === 'object') {
                    return count + Object.keys(nodeConnections).length;
                }
                return count;
            }, 0);
        }
    }

    categorizeWorkflow(analysis) {
        const categories = new Set();
        
        // Categorize based on node types
        analysis.nodeTypes.forEach(nodeType => {
            const lowerNodeType = nodeType.toLowerCase();
            
            Object.entries(this.categoryMappings).forEach(([category, keywords]) => {
                if (keywords.some(keyword => lowerNodeType.includes(keyword))) {
                    categories.add(category);
                }
            });
        });
        
        // Categorize based on metadata
        const searchText = (
            analysis.metadata.name + ' ' + 
            analysis.metadata.description + ' ' + 
            (analysis.metadata.tags || []).join(' ')
        ).toLowerCase();
        
        Object.entries(this.categoryMappings).forEach(([category, keywords]) => {
            if (keywords.some(keyword => searchText.includes(keyword))) {
                categories.add(category);
            }
        });
        
        // Special categorization
        if (analysis.hasAI) {
            categories.add('ai');
        }
        
        if (analysis.hasWebhooks) {
            categories.add('integration');
        }
        
        if (analysis.hasSchedule) {
            categories.add('automation');
        }
        
        // Default category if none found
        if (categories.size === 0) {
            categories.add('general');
        }
        
        analysis.categories = Array.from(categories);
    }

    assessComplexity(analysis) {
        const nodeCount = analysis.nodeCount;
        const connectionCount = analysis.connectionCount;
        const uniqueNodeTypes = analysis.nodeTypes.length;
        
        // Simple scoring system
        let complexityScore = 0;
        
        complexityScore += nodeCount * 1;
        complexityScore += connectionCount * 2;
        complexityScore += uniqueNodeTypes * 3;
        
        if (analysis.hasAI) complexityScore += 10;
        if (analysis.hasWebhooks) complexityScore += 5;
        if (analysis.hasSchedule) complexityScore += 3;
        
        if (complexityScore < 10) {
            analysis.complexity = 'simple';
        } else if (complexityScore < 30) {
            analysis.complexity = 'medium';
        } else {
            analysis.complexity = 'complex';
        }
    }

    isAINode(nodeType) {
        const aiKeywords = [
            'openai', 'gpt', 'anthropic', 'claude', 'huggingface',
            'ai', 'ml', 'machine learning', 'llm', 'chatgpt',
            'text-davinci', 'text-curie', 'text-babbage', 'text-ada'
        ];
        
        return aiKeywords.some(keyword => nodeType.includes(keyword));
    }

    async organizeWorkflow(parsedWorkflow) {
        if (!parsedWorkflow.isValid) {
            logger.parser(`Skipping invalid workflow: ${parsedWorkflow.filePath}`);
            return false;
        }

        try {
            const filename = path.basename(parsedWorkflow.filePath);
            
            // Copy to category directories
            for (const category of parsedWorkflow.analysis.categories) {
                const categoryDir = path.join(this.categoriesDir, category);
                await fs.ensureDir(categoryDir);
                
                const categoryFilePath = path.join(categoryDir, filename);
                await fs.copy(parsedWorkflow.filePath, categoryFilePath);
            }
            
            logger.parser(`Organized workflow into categories: ${parsedWorkflow.analysis.categories.join(', ')}`);
            return true;
            
        } catch (error) {
            logger.error(`Failed to organize workflow ${parsedWorkflow.filePath}:`, error.message);
            return false;
        }
    }

    async generateMetadataFile(parsedWorkflows) {
        try {
            const metadata = {
                generatedAt: new Date().toISOString(),
                totalWorkflows: parsedWorkflows.length,
                validWorkflows: parsedWorkflows.filter(w => w.isValid).length,
                invalidWorkflows: parsedWorkflows.filter(w => !w.isValid).length,
                categories: {},
                nodeTypes: {},
                complexity: { simple: 0, medium: 0, complex: 0 },
                features: {
                    withAI: 0,
                    withWebhooks: 0,
                    withSchedule: 0
                },
                workflows: []
            };
            
            // Aggregate statistics
            parsedWorkflows.forEach(workflow => {
                if (!workflow.isValid) return;
                
                const analysis = workflow.analysis;
                
                // Count categories
                analysis.categories.forEach(category => {
                    metadata.categories[category] = (metadata.categories[category] || 0) + 1;
                });
                
                // Count node types
                analysis.nodeTypes.forEach(nodeType => {
                    metadata.nodeTypes[nodeType] = (metadata.nodeTypes[nodeType] || 0) + 1;
                });
                
                // Count complexity
                metadata.complexity[analysis.complexity]++;
                
                // Count features
                if (analysis.hasAI) metadata.features.withAI++;
                if (analysis.hasWebhooks) metadata.features.withWebhooks++;
                if (analysis.hasSchedule) metadata.features.withSchedule++;
                
                // Add workflow summary
                metadata.workflows.push({
                    filename: path.basename(workflow.filePath),
                    name: analysis.metadata.name,
                    description: analysis.metadata.description,
                    categories: analysis.categories,
                    nodeCount: analysis.nodeCount,
                    complexity: analysis.complexity,
                    hasAI: analysis.hasAI,
                    hasWebhooks: analysis.hasWebhooks,
                    hasSchedule: analysis.hasSchedule
                });
            });
            
            const metadataPath = path.join(this.outputDir, 'metadata.json');
            await fs.writeJson(metadataPath, metadata, { spaces: 2 });
            
            logger.parser(`Generated metadata file: ${metadataPath}`);
            return metadata;
            
        } catch (error) {
            logger.error('Failed to generate metadata file:', error.message);
            throw error;
        }
    }
}

module.exports = WorkflowParser;
