#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const ProgressBar = require('progress');
const { config, validateConfig, overrideWithOptions } = require('./config/config');
const WorkflowScraper = require('./scrapers/workflow-scraper');
const WorkflowParser = require('./parsers/workflow-parser');
const StorageManager = require('./utils/storage-manager');
const logger = require('./utils/logger');
const fs = require('fs-extra');
const path = require('path');

const program = new Command();

program
    .name('n8n-scraper')
    .description('A comprehensive scraper for n8n workflows from n8nworkflows.xyz')
    .version('1.0.0');

program
    .command('scrape')
    .description('Scrape workflows from n8nworkflows.xyz')
    .option('-o, --output <dir>', 'Output directory for workflows', './workflows')
    .option('-c, --concurrent <num>', 'Maximum concurrent requests', '3')
    .option('-d, --delay <ms>', 'Delay between requests in milliseconds', '2000')
    .option('--headless <bool>', 'Run browser in headless mode', true)
    .option('-v, --verbose', 'Enable verbose logging')
    .option('--proxy <url>', 'Proxy URL to use')
    .option('--timeout <ms>', 'Request timeout in milliseconds', '30000')
    .option('--retries <num>', 'Maximum number of retries', '3')
    .option('--no-dedup', 'Disable deduplication')
    .option('--no-categorize', 'Disable categorization')
    .option('--no-validate', 'Disable validation')
    .action(async (options) => {
        try {
            console.log(chalk.blue.bold('🚀 N8N Workflow Scraper Starting...\n'));
            
            // Override config with CLI options
            const finalConfig = overrideWithOptions(options);
            validateConfig();
            
            // Initialize components
            const storageManager = new StorageManager({
                outputDir: finalConfig.output.dir
            });
            
            const workflowScraper = new WorkflowScraper({
                baseUrl: finalConfig.target.url,
                outputDir: finalConfig.output.dir,
                maxConcurrent: finalConfig.scraping.maxConcurrent,
                requestsPerMinute: finalConfig.scraping.requestsPerMinute,
                burstLimit: finalConfig.scraping.burstLimit,
                minDelay: finalConfig.scraping.requestDelay,
                headless: finalConfig.browser.headless,
                timeout: finalConfig.target.timeout,
                userDataDir: finalConfig.browser.userDataDir
            });
            
            const workflowParser = new WorkflowParser({
                outputDir: finalConfig.output.dir
            });
            
            // Initialize scraper
            await workflowScraper.initialize();
            
            console.log(chalk.green('✅ Scraper initialized successfully\n'));
            
            // Step 1: Discover workflows
            console.log(chalk.yellow('🔍 Discovering workflows...'));
            const discoveredWorkflows = await workflowScraper.discoverWorkflows();
            
            if (discoveredWorkflows.length === 0) {
                console.log(chalk.red('❌ No workflows discovered. The site might be blocking access.'));
                process.exit(1);
            }
            
            console.log(chalk.green(`✅ Discovered ${discoveredWorkflows.length} workflows\n`));
            
            // Step 2: Download workflows
            console.log(chalk.yellow('⬇️  Downloading workflows...'));
            
            const progressBar = new ProgressBar(
                '[:bar] :current/:total :percent :etas',
                {
                    complete: '█',
                    incomplete: '░',
                    width: 40,
                    total: discoveredWorkflows.length
                }
            );
            
            // Override the scraper's download method to update progress
            const originalDownload = workflowScraper.downloadSingleWorkflow.bind(workflowScraper);
            workflowScraper.downloadSingleWorkflow = async (url) => {
                const result = await originalDownload(url);
                progressBar.tick();
                return result;
            };
            
            const downloadResults = await workflowScraper.downloadWorkflows(discoveredWorkflows);
            
            console.log(chalk.green(`\n✅ Download completed: ${downloadResults.successful} successful, ${downloadResults.failed} failed\n`));
            
            // Step 3: Parse and validate workflows
            if (options.validate !== false) {
                console.log(chalk.yellow('🔍 Parsing and validating workflows...'));
                
                const rawDir = path.join(finalConfig.output.dir, 'raw');
                const workflowFiles = await fs.readdir(rawDir);
                const jsonFiles = workflowFiles.filter(file => file.endsWith('.json'));
                
                const parsedWorkflows = [];
                
                for (const file of jsonFiles) {
                    const filePath = path.join(rawDir, file);
                    const parsed = await workflowParser.parseWorkflow(filePath);
                    parsedWorkflows.push(parsed);
                }
                
                const validWorkflows = parsedWorkflows.filter(w => w.isValid);
                const invalidWorkflows = parsedWorkflows.filter(w => !w.isValid);
                
                console.log(chalk.green(`✅ Validation completed: ${validWorkflows.length} valid, ${invalidWorkflows.length} invalid\n`));
                
                // Step 4: Organize by categories
                if (options.categorize !== false) {
                    console.log(chalk.yellow('📁 Organizing workflows by categories...'));
                    
                    const organizationStats = await storageManager.organizeByCategories(parsedWorkflows);
                    
                    console.log(chalk.green(`✅ Organization completed: ${organizationStats.organized} workflows organized\n`));
                    
                    // Display category statistics
                    console.log(chalk.blue('📊 Category Statistics:'));
                    Object.entries(organizationStats.categories).forEach(([category, count]) => {
                        console.log(`  ${category}: ${count} workflows`);
                    });
                    console.log();
                }
                
                // Step 5: Generate metadata
                console.log(chalk.yellow('📄 Generating metadata...'));
                
                const metadata = await workflowParser.generateMetadataFile(parsedWorkflows);
                
                console.log(chalk.green('✅ Metadata generated successfully\n'));
                
                // Display final statistics
                console.log(chalk.blue.bold('📊 Final Statistics:'));
                console.log(`  Total workflows discovered: ${discoveredWorkflows.length}`);
                console.log(`  Successfully downloaded: ${downloadResults.successful}`);
                console.log(`  Failed downloads: ${downloadResults.failed}`);
                console.log(`  Valid workflows: ${metadata.validWorkflows}`);
                console.log(`  Invalid workflows: ${metadata.invalidWorkflows}`);
                console.log(`  Categories found: ${Object.keys(metadata.categories).length}`);
                console.log(`  Unique node types: ${Object.keys(metadata.nodeTypes).length}`);
                console.log(`  AI workflows: ${metadata.features.withAI}`);
                console.log(`  Workflows with webhooks: ${metadata.features.withWebhooks}`);
                console.log(`  Scheduled workflows: ${metadata.features.withSchedule}`);
            }
            
            // Cleanup
            await workflowScraper.close();
            await storageManager.cleanup();
            
            console.log(chalk.green.bold('\n🎉 Scraping completed successfully!'));
            console.log(chalk.blue(`📁 Workflows saved to: ${finalConfig.output.dir}`));
            
        } catch (error) {
            console.error(chalk.red.bold('\n❌ Scraping failed:'), error.message);
            logger.error('CLI scraping failed:', error);
            process.exit(1);
        }
    });

program
    .command('analyze <directory>')
    .description('Analyze existing workflow files')
    .option('-o, --output <file>', 'Output file for analysis report', './analysis-report.json')
    .action(async (directory, options) => {
        try {
            console.log(chalk.blue.bold('🔍 Analyzing workflows...\n'));
            
            const workflowParser = new WorkflowParser();
            const files = await fs.readdir(directory);
            const jsonFiles = files.filter(file => file.endsWith('.json'));
            
            console.log(chalk.yellow(`Found ${jsonFiles.length} JSON files to analyze`));
            
            const parsedWorkflows = [];
            
            for (const file of jsonFiles) {
                const filePath = path.join(directory, file);
                const parsed = await workflowParser.parseWorkflow(filePath);
                parsedWorkflows.push(parsed);
            }
            
            const metadata = await workflowParser.generateMetadataFile(parsedWorkflows);
            
            // Save analysis report
            await fs.writeJson(options.output, metadata, { spaces: 2 });
            
            console.log(chalk.green(`\n✅ Analysis completed and saved to: ${options.output}`));
            
        } catch (error) {
            console.error(chalk.red.bold('\n❌ Analysis failed:'), error.message);
            process.exit(1);
        }
    });

program
    .command('config')
    .description('Show current configuration')
    .action(() => {
        console.log(chalk.blue.bold('📋 Current Configuration:\n'));
        console.log(JSON.stringify(config, null, 2));
    });

// Error handling
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    console.error(chalk.red.bold('❌ Unhandled error occurred. Check logs for details.'));
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    console.error(chalk.red.bold('❌ Critical error occurred. Check logs for details.'));
    process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log(chalk.yellow('\n⏹️  Gracefully shutting down...'));
    process.exit(0);
});

program.parse();
