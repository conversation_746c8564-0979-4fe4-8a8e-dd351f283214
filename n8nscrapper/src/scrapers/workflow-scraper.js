const CloudflareBypass = require('../utils/cloudflare-bypass');
const RateLimiter = require('../utils/rate-limiter');
const EthicalScraper = require('../utils/ethical-scraper');
const logger = require('../utils/logger');
const cheerio = require('cheerio');
const fs = require('fs-extra');
const path = require('path');
const pLimit = require('p-limit');
const pRetry = require('p-retry');

class WorkflowScraper {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || 'https://n8nworkflows.xyz';
        this.outputDir = options.outputDir || './workflows';
        this.maxConcurrent = options.maxConcurrent || 3;
        this.rateLimiter = new RateLimiter({
            requestsPerMinute: options.requestsPerMinute || 30,
            burstLimit: options.burstLimit || 5,
            minDelay: options.minDelay || 2000
        });
        
        this.cloudflareBypass = new CloudflareBypass({
            headless: options.headless !== false,
            timeout: options.timeout || 30000,
            userDataDir: options.userDataDir || './browser-data'
        });

        this.ethicalScraper = new EthicalScraper({
            baseUrl: this.baseUrl,
            userAgent: options.userAgent || 'n8n-workflow-scraper/1.0.0 (+https://github.com/user/n8n-scraper)',
            respectRobotsTxt: options.respectRobotsTxt !== false,
            rateLimiting: {
                requestsPerMinute: options.requestsPerMinute || 30,
                burstLimit: options.burstLimit || 5,
                minDelay: options.minDelay || 2000
            }
        });

        this.limit = pLimit(this.maxConcurrent);
        this.discoveredWorkflows = new Set();
        this.downloadedWorkflows = new Set();
        this.failedWorkflows = new Set();
        
        // Ensure output directory exists
        fs.ensureDirSync(this.outputDir);
        fs.ensureDirSync(path.join(this.outputDir, 'raw'));
        fs.ensureDirSync(path.join(this.outputDir, 'categories'));
    }

    async initialize() {
        logger.scraper('Initializing workflow scraper...');
        await this.cloudflareBypass.initialize();
        await this.ethicalScraper.initialize();

        // Check ethical guidelines
        const ethicalCheck = this.ethicalScraper.checkEthicalGuidelines();
        if (!ethicalCheck.isEthical) {
            logger.warn('Ethical guidelines violations detected:', ethicalCheck.violations);
        }

        logger.scraper('Workflow scraper initialized successfully');
    }

    async discoverWorkflows() {
        logger.scraper('Starting workflow discovery...');
        
        try {
            // First, try to access the main page
            const page = await this.cloudflareBypass.bypassCloudflare(this.baseUrl);
            
            // Get the page content
            const content = await page.content();
            const $ = cheerio.load(content);
            
            // Look for workflow links - this will need to be adjusted based on actual site structure
            const workflowLinks = await this.extractWorkflowLinks($, page);
            
            logger.scraper(`Discovered ${workflowLinks.length} potential workflow links`);
            
            // Try to find pagination or additional pages
            const additionalPages = await this.discoverAdditionalPages($, page);
            
            for (const pageUrl of additionalPages) {
                try {
                    await this.rateLimiter.waitForSlot();
                    const additionalPage = await this.cloudflareBypass.bypassCloudflare(pageUrl);
                    const additionalContent = await additionalPage.content();
                    const $additional = cheerio.load(additionalContent);
                    
                    const moreLinks = await this.extractWorkflowLinks($additional, additionalPage);
                    workflowLinks.push(...moreLinks);
                    
                    logger.scraper(`Found ${moreLinks.length} more workflows on ${pageUrl}`);
                } catch (error) {
                    logger.warn(`Failed to scrape additional page ${pageUrl}:`, error.message);
                }
            }
            
            // Remove duplicates and store
            const uniqueWorkflows = [...new Set(workflowLinks)];
            uniqueWorkflows.forEach(url => this.discoveredWorkflows.add(url));
            
            logger.scraper(`Total unique workflows discovered: ${this.discoveredWorkflows.size}`);
            
            return Array.from(this.discoveredWorkflows);
            
        } catch (error) {
            logger.error('Failed to discover workflows:', error);
            throw error;
        }
    }

    async extractWorkflowLinks($, page) {
        const links = [];
        
        // Common selectors for workflow links - adjust based on actual site structure
        const selectors = [
            'a[href*="workflow"]',
            'a[href*="/w/"]',
            'a[href*="/template/"]',
            '.workflow-card a',
            '.template-card a',
            '[data-workflow-id]',
            'a[href$=".json"]'
        ];
        
        for (const selector of selectors) {
            $(selector).each((i, element) => {
                const href = $(element).attr('href');
                if (href) {
                    const fullUrl = this.resolveUrl(href);
                    if (this.isValidWorkflowUrl(fullUrl)) {
                        links.push(fullUrl);
                    }
                }
            });
        }
        
        // Also try to find download links or API endpoints
        const downloadLinks = await this.findDownloadLinks($, page);
        links.push(...downloadLinks);
        
        return links;
    }

    async findDownloadLinks($, page) {
        const links = [];
        
        // Look for direct download links
        $('a[href$=".json"], a[download], .download-btn').each((i, element) => {
            const href = $(element).attr('href');
            if (href) {
                const fullUrl = this.resolveUrl(href);
                links.push(fullUrl);
            }
        });
        
        // Try to find API endpoints by examining JavaScript
        try {
            const apiEndpoints = await page.evaluate(() => {
                const scripts = Array.from(document.querySelectorAll('script'));
                const endpoints = [];
                
                scripts.forEach(script => {
                    const content = script.textContent || script.innerHTML;
                    
                    // Look for API patterns
                    const apiMatches = content.match(/\/api\/[^"'\s]+/g);
                    if (apiMatches) {
                        endpoints.push(...apiMatches);
                    }
                    
                    // Look for workflow download patterns
                    const downloadMatches = content.match(/download[^"'\s]*\.json/g);
                    if (downloadMatches) {
                        endpoints.push(...downloadMatches);
                    }
                });
                
                return endpoints;
            });
            
            apiEndpoints.forEach(endpoint => {
                const fullUrl = this.resolveUrl(endpoint);
                if (this.isValidWorkflowUrl(fullUrl)) {
                    links.push(fullUrl);
                }
            });
            
        } catch (error) {
            logger.warn('Failed to extract API endpoints:', error.message);
        }
        
        return links;
    }

    async discoverAdditionalPages($, page) {
        const pages = [];
        
        // Look for pagination
        const paginationSelectors = [
            '.pagination a',
            '.page-numbers a',
            'a[href*="page="]',
            'a[href*="/page/"]',
            '.next-page',
            '.load-more'
        ];
        
        for (const selector of paginationSelectors) {
            $(selector).each((i, element) => {
                const href = $(element).attr('href');
                if (href) {
                    const fullUrl = this.resolveUrl(href);
                    pages.push(fullUrl);
                }
            });
        }
        
        // Try to find category pages
        const categorySelectors = [
            'a[href*="category"]',
            'a[href*="tag"]',
            '.category-link',
            '.tag-link'
        ];
        
        for (const selector of categorySelectors) {
            $(selector).each((i, element) => {
                const href = $(element).attr('href');
                if (href) {
                    const fullUrl = this.resolveUrl(href);
                    pages.push(fullUrl);
                }
            });
        }
        
        return [...new Set(pages)];
    }

    async downloadWorkflows(workflowUrls) {
        logger.scraper(`Starting download of ${workflowUrls.length} workflows...`);
        
        const downloadPromises = workflowUrls.map(url => 
            this.limit(() => this.downloadSingleWorkflow(url))
        );
        
        const results = await Promise.allSettled(downloadPromises);
        
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        logger.scraper(`Download completed: ${successful} successful, ${failed} failed`);
        
        return {
            successful,
            failed,
            total: workflowUrls.length
        };
    }

    async downloadSingleWorkflow(url) {
        return pRetry(async () => {
            await this.rateLimiter.waitForSlot();
            
            logger.scraper(`Downloading workflow from: ${url}`);
            
            try {
                const page = await this.cloudflareBypass.bypassCloudflare(url);
                
                // Try to get workflow JSON directly
                let workflowData = await this.extractWorkflowData(page);
                
                if (!workflowData) {
                    // If not found, try to find download link on the page
                    const content = await page.content();
                    const $ = cheerio.load(content);
                    
                    const downloadUrl = await this.findWorkflowDownloadUrl($, page);
                    if (downloadUrl) {
                        const downloadPage = await this.cloudflareBypass.bypassCloudflare(downloadUrl);
                        workflowData = await this.extractWorkflowData(downloadPage);
                    }
                }
                
                if (!workflowData) {
                    throw new Error('Could not extract workflow data');
                }
                
                // Save the workflow
                const filename = this.generateFilename(url, workflowData);
                const filepath = path.join(this.outputDir, 'raw', filename);
                
                await fs.writeJson(filepath, workflowData, { spaces: 2 });
                
                this.downloadedWorkflows.add(url);
                logger.scraper(`Successfully downloaded: ${filename}`);
                
                return { url, filepath, data: workflowData };
                
            } catch (error) {
                this.failedWorkflows.add(url);
                logger.error(`Failed to download workflow from ${url}:`, error.message);
                throw error;
            }
        }, {
            retries: 3,
            factor: 2,
            minTimeout: 1000,
            maxTimeout: 10000
        });
    }

    resolveUrl(href) {
        if (href.startsWith('http')) {
            return href;
        }
        if (href.startsWith('//')) {
            return 'https:' + href;
        }
        if (href.startsWith('/')) {
            return this.baseUrl + href;
        }
        return this.baseUrl + '/' + href;
    }

    isValidWorkflowUrl(url) {
        // Filter out obviously non-workflow URLs
        const invalidPatterns = [
            /\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/i,
            /^mailto:/,
            /^tel:/,
            /#$/,
            /javascript:/
        ];
        
        return !invalidPatterns.some(pattern => pattern.test(url));
    }

    async extractWorkflowData(page) {
        try {
            // Try to get JSON data from the page
            const content = await page.content();

            // Look for JSON in script tags
            const jsonData = await page.evaluate(() => {
                const scripts = Array.from(document.querySelectorAll('script'));

                for (const script of scripts) {
                    const content = script.textContent || script.innerHTML;

                    // Look for workflow JSON patterns
                    const patterns = [
                        /window\.__WORKFLOW__\s*=\s*({.+?});/s,
                        /workflow\s*:\s*({.+?})/s,
                        /"workflow"\s*:\s*({.+?})/s,
                        /workflowData\s*=\s*({.+?});/s
                    ];

                    for (const pattern of patterns) {
                        const match = content.match(pattern);
                        if (match) {
                            try {
                                return JSON.parse(match[1]);
                            } catch (e) {
                                continue;
                            }
                        }
                    }
                }

                return null;
            });

            if (jsonData) {
                return jsonData;
            }

            // Try to find JSON in the page content directly
            const $ = cheerio.load(content);

            // Look for pre-formatted JSON
            const jsonElements = $('pre, code, .json, [data-json]');
            for (let i = 0; i < jsonElements.length; i++) {
                const element = jsonElements.eq(i);
                const text = element.text().trim();

                if (text.startsWith('{') && text.endsWith('}')) {
                    try {
                        const parsed = JSON.parse(text);
                        if (this.isValidWorkflowJson(parsed)) {
                            return parsed;
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }

            // If this is a direct JSON response
            if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
                try {
                    const parsed = JSON.parse(content.trim());
                    if (this.isValidWorkflowJson(parsed)) {
                        return parsed;
                    }
                } catch (e) {
                    // Not valid JSON
                }
            }

            return null;

        } catch (error) {
            logger.warn('Error extracting workflow data:', error.message);
            return null;
        }
    }

    async findWorkflowDownloadUrl($, page) {
        // Look for download buttons or links
        const downloadSelectors = [
            'a[href$=".json"]',
            'a[download]',
            '.download-btn',
            '.download-link',
            'button[data-download]',
            'a[href*="download"]'
        ];

        for (const selector of downloadSelectors) {
            const element = $(selector).first();
            if (element.length) {
                const href = element.attr('href') || element.attr('data-href');
                if (href) {
                    return this.resolveUrl(href);
                }
            }
        }

        // Try to find API endpoint for this workflow
        try {
            const apiUrl = await page.evaluate(() => {
                // Look for API calls in the page
                const scripts = Array.from(document.querySelectorAll('script'));

                for (const script of scripts) {
                    const content = script.textContent || script.innerHTML;

                    // Look for API patterns
                    const apiMatches = content.match(/['"`]\/api\/workflows?\/[^'"`]+['"`]/g);
                    if (apiMatches && apiMatches.length > 0) {
                        return apiMatches[0].replace(/['"`]/g, '');
                    }
                }

                return null;
            });

            if (apiUrl) {
                return this.resolveUrl(apiUrl);
            }
        } catch (error) {
            logger.warn('Error finding API URL:', error.message);
        }

        return null;
    }

    isValidWorkflowJson(data) {
        // Check if this looks like a valid n8n workflow
        if (!data || typeof data !== 'object') {
            return false;
        }

        // Common n8n workflow properties
        const workflowIndicators = [
            'nodes',
            'connections',
            'meta',
            'name',
            'active',
            'settings'
        ];

        // Check if it has at least some workflow properties
        const hasWorkflowProps = workflowIndicators.some(prop => data.hasOwnProperty(prop));

        // Additional checks for n8n specific structure
        if (data.nodes && Array.isArray(data.nodes)) {
            return true;
        }

        if (data.workflow && typeof data.workflow === 'object') {
            return true;
        }

        return hasWorkflowProps;
    }

    generateFilename(url, workflowData) {
        // Try to get a meaningful name from the workflow data
        let name = 'workflow';

        if (workflowData.name) {
            name = workflowData.name;
        } else if (workflowData.meta && workflowData.meta.name) {
            name = workflowData.meta.name;
        } else if (workflowData.workflow && workflowData.workflow.name) {
            name = workflowData.workflow.name;
        } else {
            // Extract from URL
            const urlParts = url.split('/');
            const lastPart = urlParts[urlParts.length - 1];
            if (lastPart && lastPart !== '') {
                name = lastPart.replace(/\.[^/.]+$/, ''); // Remove extension
            }
        }

        // Sanitize filename
        name = name.replace(/[^a-z0-9\-_]/gi, '_').toLowerCase();

        // Add timestamp to avoid conflicts
        const timestamp = Date.now();

        return `${name}_${timestamp}.json`;
    }

    async close() {
        await this.cloudflareBypass.close();
        logger.scraper('Workflow scraper closed');
    }

    getStats() {
        return {
            discovered: this.discoveredWorkflows.size,
            downloaded: this.downloadedWorkflows.size,
            failed: this.failedWorkflows.size,
            rateLimiter: this.rateLimiter.getStats()
        };
    }
}

module.exports = WorkflowScraper;
