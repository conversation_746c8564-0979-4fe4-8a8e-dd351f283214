#!/usr/bin/env node

/**
 * Main scraper entry point
 * This file provides a simple interface to run the scraper
 */

const N8NWorkflowScraperApp = require('./index');
const { config, overrideWithOptions } = require('./config/config');
const logger = require('./utils/logger');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

// Simple argument parsing
for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--output' && args[i + 1]) {
        options.output = args[i + 1];
        i++;
    } else if (arg === '--concurrent' && args[i + 1]) {
        options.concurrent = parseInt(args[i + 1]);
        i++;
    } else if (arg === '--verbose') {
        options.verbose = true;
    } else if (arg === '--headless') {
        options.headless = args[i + 1] !== 'false';
        i++;
    } else if (arg === '--help') {
        showHelp();
        process.exit(0);
    }
}

function showHelp() {
    console.log(`
N8N Workflow Scraper

Usage: node scraper.js [options]

Options:
  --output <dir>        Output directory for workflows (default: ./workflows)
  --concurrent <num>    Maximum concurrent requests (default: 3)
  --verbose             Enable verbose logging
  --headless <bool>     Run browser in headless mode (default: true)
  --help                Show this help message

Examples:
  node scraper.js
  node scraper.js --output ./my-workflows --concurrent 5 --verbose
  node scraper.js --headless false
`);
}

async function main() {
    try {
        console.log('🚀 Starting N8N Workflow Scraper...\n');
        
        // Create app instance with options
        const finalConfig = overrideWithOptions(options);
        const app = new N8NWorkflowScraperApp(finalConfig);
        
        // Initialize and run
        await app.initialize();
        const results = await app.run();
        
        // Display results
        console.log('\n✅ Scraping completed successfully!');
        console.log('\n📊 Results Summary:');
        console.log(`  Workflows discovered: ${results.discovered}`);
        console.log(`  Workflows downloaded: ${results.downloaded.successful}`);
        console.log(`  Failed downloads: ${results.downloaded.failed}`);
        
        if (results.metadata) {
            console.log(`  Valid workflows: ${results.valid}`);
            console.log(`  Invalid workflows: ${results.invalid}`);
            console.log(`  Categories: ${Object.keys(results.metadata.categories).length}`);
            console.log(`  AI workflows: ${results.metadata.features.withAI}`);
        }
        
        console.log(`\n📁 Output directory: ${finalConfig.output.dir}`);
        
        // Cleanup
        await app.cleanup();
        
    } catch (error) {
        console.error('\n❌ Scraping failed:', error.message);
        logger.error('Scraper failed:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n⏹️  Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n⏹️  Shutting down gracefully...');
    process.exit(0);
});

// Run the scraper
if (require.main === module) {
    main();
}

module.exports = main;
