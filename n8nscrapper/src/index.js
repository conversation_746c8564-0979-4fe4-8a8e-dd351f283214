#!/usr/bin/env node

const { config, validateConfig } = require('./config/config');
const WorkflowScraper = require('./scrapers/workflow-scraper');
const WorkflowParser = require('./parsers/workflow-parser');
const StorageManager = require('./utils/storage-manager');
const logger = require('./utils/logger');

class N8NWorkflowScraperApp {
    constructor(options = {}) {
        this.config = { ...config, ...options };
        this.storageManager = null;
        this.workflowScraper = null;
        this.workflowParser = null;
    }

    async initialize() {
        try {
            logger.info('Initializing N8N Workflow Scraper...');
            
            // Validate configuration
            validateConfig();
            
            // Initialize components
            this.storageManager = new StorageManager({
                outputDir: this.config.output.dir
            });
            
            this.workflowScraper = new WorkflowScraper({
                baseUrl: this.config.target.url,
                outputDir: this.config.output.dir,
                maxConcurrent: this.config.scraping.maxConcurrent,
                requestsPerMinute: this.config.scraping.requestsPerMinute,
                burstLimit: this.config.scraping.burstLimit,
                minDelay: this.config.scraping.requestDelay,
                headless: this.config.browser.headless,
                timeout: this.config.target.timeout,
                userDataDir: this.config.browser.userDataDir
            });
            
            this.workflowParser = new WorkflowParser({
                outputDir: this.config.output.dir
            });
            
            // Initialize scraper
            await this.workflowScraper.initialize();
            
            logger.info('N8N Workflow Scraper initialized successfully');
            
        } catch (error) {
            logger.error('Failed to initialize scraper:', error);
            throw error;
        }
    }

    async run() {
        try {
            logger.info('Starting workflow scraping process...');
            
            // Step 1: Discover workflows
            logger.info('Discovering workflows...');
            const discoveredWorkflows = await this.workflowScraper.discoverWorkflows();
            
            if (discoveredWorkflows.length === 0) {
                throw new Error('No workflows discovered. The site might be blocking access.');
            }
            
            logger.info(`Discovered ${discoveredWorkflows.length} workflows`);
            
            // Step 2: Download workflows
            logger.info('Downloading workflows...');
            const downloadResults = await this.workflowScraper.downloadWorkflows(discoveredWorkflows);
            
            logger.info(`Download completed: ${downloadResults.successful} successful, ${downloadResults.failed} failed`);
            
            // Step 3: Parse and validate workflows
            if (this.config.features.validation) {
                logger.info('Parsing and validating workflows...');
                
                const parsedWorkflows = await this.parseAllWorkflows();
                const validWorkflows = parsedWorkflows.filter(w => w.isValid);
                const invalidWorkflows = parsedWorkflows.filter(w => !w.isValid);
                
                logger.info(`Validation completed: ${validWorkflows.length} valid, ${invalidWorkflows.length} invalid`);
                
                // Step 4: Organize by categories
                if (this.config.features.categorization) {
                    logger.info('Organizing workflows by categories...');
                    const organizationStats = await this.storageManager.organizeByCategories(parsedWorkflows);
                    logger.info(`Organization completed: ${organizationStats.organized} workflows organized`);
                }
                
                // Step 5: Generate metadata
                if (this.config.features.metadataGeneration) {
                    logger.info('Generating metadata...');
                    const metadata = await this.workflowParser.generateMetadataFile(parsedWorkflows);
                    logger.info('Metadata generated successfully');
                    
                    return {
                        discovered: discoveredWorkflows.length,
                        downloaded: downloadResults,
                        parsed: parsedWorkflows.length,
                        valid: validWorkflows.length,
                        invalid: invalidWorkflows.length,
                        metadata
                    };
                }
            }
            
            return {
                discovered: discoveredWorkflows.length,
                downloaded: downloadResults
            };
            
        } catch (error) {
            logger.error('Scraping process failed:', error);
            throw error;
        }
    }

    async parseAllWorkflows() {
        const fs = require('fs-extra');
        const path = require('path');
        
        const rawDir = path.join(this.config.output.dir, 'raw');
        const workflowFiles = await fs.readdir(rawDir);
        const jsonFiles = workflowFiles.filter(file => file.endsWith('.json'));
        
        const parsedWorkflows = [];
        
        for (const file of jsonFiles) {
            const filePath = path.join(rawDir, file);
            const parsed = await this.workflowParser.parseWorkflow(filePath);
            parsedWorkflows.push(parsed);
        }
        
        return parsedWorkflows;
    }

    async cleanup() {
        try {
            if (this.workflowScraper) {
                await this.workflowScraper.close();
            }
            
            if (this.storageManager) {
                await this.storageManager.cleanup();
            }
            
            logger.info('Cleanup completed successfully');
            
        } catch (error) {
            logger.error('Error during cleanup:', error);
        }
    }

    getStats() {
        const stats = {
            scraper: this.workflowScraper ? this.workflowScraper.getStats() : null,
            storage: this.storageManager ? this.storageManager.getStats() : null
        };
        
        return stats;
    }
}

// If this file is run directly
if (require.main === module) {
    const app = new N8NWorkflowScraperApp();
    
    app.initialize()
        .then(() => app.run())
        .then((results) => {
            console.log('Scraping completed successfully!');
            console.log('Results:', JSON.stringify(results, null, 2));
            return app.cleanup();
        })
        .catch((error) => {
            console.error('Scraping failed:', error.message);
            logger.error('Main process failed:', error);
            return app.cleanup().then(() => process.exit(1));
        });
}

module.exports = N8NWorkflowScraperApp;
