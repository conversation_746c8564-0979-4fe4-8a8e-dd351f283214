const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');
const logger = require('./logger');

class StorageManager {
    constructor(options = {}) {
        this.outputDir = options.outputDir || './workflows';
        this.rawDir = path.join(this.outputDir, 'raw');
        this.categoriesDir = path.join(this.outputDir, 'categories');
        this.metadataFile = path.join(this.outputDir, 'metadata.json');
        this.duplicatesFile = path.join(this.outputDir, 'duplicates.json');
        
        // Initialize storage structure
        this.initializeStorage();
        
        // Track duplicates
        this.duplicates = new Map();
        this.hashes = new Map();
    }

    async initializeStorage() {
        try {
            // Create directory structure
            await fs.ensureDir(this.outputDir);
            await fs.ensureDir(this.rawDir);
            await fs.ensureDir(this.categoriesDir);
            
            // Create category subdirectories
            const categories = [
                'ai', 'automation', 'integration', 'data-processing',
                'communication', 'social-media', 'productivity',
                'ecommerce', 'monitoring', 'file-management', 'general'
            ];
            
            for (const category of categories) {
                await fs.ensureDir(path.join(this.categoriesDir, category));
            }
            
            logger.storage('Storage structure initialized');
            
        } catch (error) {
            logger.error('Failed to initialize storage:', error.message);
            throw error;
        }
    }

    async storeWorkflow(workflowData, metadata = {}) {
        try {
            // Generate content hash for deduplication
            const contentHash = this.generateContentHash(workflowData);
            
            // Check for duplicates
            if (this.hashes.has(contentHash)) {
                const existingFile = this.hashes.get(contentHash);
                logger.storage(`Duplicate workflow detected: ${metadata.name || 'unnamed'}`);
                
                this.duplicates.set(contentHash, {
                    original: existingFile,
                    duplicate: metadata,
                    hash: contentHash
                });
                
                return { stored: false, reason: 'duplicate', hash: contentHash };
            }
            
            // Generate filename
            const filename = this.generateUniqueFilename(metadata);
            const filePath = path.join(this.rawDir, filename);
            
            // Store the workflow
            await fs.writeJson(filePath, workflowData, { spaces: 2 });
            
            // Track the hash
            this.hashes.set(contentHash, {
                filename,
                filePath,
                metadata,
                hash: contentHash,
                storedAt: new Date().toISOString()
            });
            
            logger.storage(`Stored workflow: ${filename}`);
            
            return {
                stored: true,
                filename,
                filePath,
                hash: contentHash
            };
            
        } catch (error) {
            logger.error('Failed to store workflow:', error.message);
            throw error;
        }
    }

    async organizeByCategories(parsedWorkflows) {
        try {
            logger.storage('Organizing workflows by categories...');
            
            const organizationStats = {
                organized: 0,
                failed: 0,
                categories: {}
            };
            
            for (const parsedWorkflow of parsedWorkflows) {
                if (!parsedWorkflow.isValid) {
                    organizationStats.failed++;
                    continue;
                }
                
                try {
                    const filename = path.basename(parsedWorkflow.filePath);
                    
                    // Copy to each category directory
                    for (const category of parsedWorkflow.analysis.categories) {
                        const categoryDir = path.join(this.categoriesDir, category);
                        const categoryFilePath = path.join(categoryDir, filename);
                        
                        await fs.copy(parsedWorkflow.filePath, categoryFilePath);
                        
                        // Track category stats
                        organizationStats.categories[category] = 
                            (organizationStats.categories[category] || 0) + 1;
                    }
                    
                    organizationStats.organized++;
                    
                } catch (error) {
                    logger.error(`Failed to organize workflow ${parsedWorkflow.filePath}:`, error.message);
                    organizationStats.failed++;
                }
            }
            
            logger.storage(`Organization complete: ${organizationStats.organized} organized, ${organizationStats.failed} failed`);
            return organizationStats;
            
        } catch (error) {
            logger.error('Failed to organize workflows by categories:', error.message);
            throw error;
        }
    }

    async saveDuplicatesReport() {
        try {
            const duplicatesData = {
                generatedAt: new Date().toISOString(),
                totalDuplicates: this.duplicates.size,
                duplicates: Array.from(this.duplicates.values())
            };
            
            await fs.writeJson(this.duplicatesFile, duplicatesData, { spaces: 2 });
            
            logger.storage(`Saved duplicates report: ${this.duplicates.size} duplicates found`);
            return duplicatesData;
            
        } catch (error) {
            logger.error('Failed to save duplicates report:', error.message);
            throw error;
        }
    }

    async generateStorageReport() {
        try {
            const report = {
                generatedAt: new Date().toISOString(),
                directories: {},
                totalFiles: 0,
                totalSize: 0,
                duplicates: this.duplicates.size
            };
            
            // Analyze raw directory
            const rawFiles = await this.analyzeDirectory(this.rawDir);
            report.directories.raw = rawFiles;
            report.totalFiles += rawFiles.fileCount;
            report.totalSize += rawFiles.totalSize;
            
            // Analyze categories
            const categoryDirs = await fs.readdir(this.categoriesDir);
            report.directories.categories = {};
            
            for (const categoryDir of categoryDirs) {
                const categoryPath = path.join(this.categoriesDir, categoryDir);
                const categoryStats = await this.analyzeDirectory(categoryPath);
                report.directories.categories[categoryDir] = categoryStats;
            }
            
            // Format sizes
            report.totalSizeFormatted = this.formatBytes(report.totalSize);
            
            logger.storage(`Generated storage report: ${report.totalFiles} files, ${report.totalSizeFormatted}`);
            return report;
            
        } catch (error) {
            logger.error('Failed to generate storage report:', error.message);
            throw error;
        }
    }

    async analyzeDirectory(dirPath) {
        try {
            const stats = {
                path: dirPath,
                fileCount: 0,
                totalSize: 0,
                files: []
            };
            
            if (!(await fs.pathExists(dirPath))) {
                return stats;
            }
            
            const files = await fs.readdir(dirPath);
            
            for (const file of files) {
                const filePath = path.join(dirPath, file);
                const fileStat = await fs.stat(filePath);
                
                if (fileStat.isFile()) {
                    stats.fileCount++;
                    stats.totalSize += fileStat.size;
                    
                    stats.files.push({
                        name: file,
                        size: fileStat.size,
                        sizeFormatted: this.formatBytes(fileStat.size),
                        modified: fileStat.mtime.toISOString()
                    });
                }
            }
            
            stats.totalSizeFormatted = this.formatBytes(stats.totalSize);
            return stats;
            
        } catch (error) {
            logger.error(`Failed to analyze directory ${dirPath}:`, error.message);
            return {
                path: dirPath,
                fileCount: 0,
                totalSize: 0,
                files: [],
                error: error.message
            };
        }
    }

    generateContentHash(workflowData) {
        // Create a normalized version for hashing
        const normalized = this.normalizeWorkflowForHashing(workflowData);
        const content = JSON.stringify(normalized);
        return crypto.createHash('sha256').update(content).digest('hex');
    }

    normalizeWorkflowForHashing(workflowData) {
        // Remove metadata that shouldn't affect duplicate detection
        const normalized = JSON.parse(JSON.stringify(workflowData));
        
        // Remove timestamps and IDs that might differ between identical workflows
        const removeKeys = ['id', 'createdAt', 'updatedAt', 'created_at', 'updated_at', 'lastModified'];
        
        const removeFromObject = (obj) => {
            if (Array.isArray(obj)) {
                return obj.map(removeFromObject);
            } else if (obj && typeof obj === 'object') {
                const cleaned = {};
                for (const [key, value] of Object.entries(obj)) {
                    if (!removeKeys.includes(key)) {
                        cleaned[key] = removeFromObject(value);
                    }
                }
                return cleaned;
            }
            return obj;
        };
        
        return removeFromObject(normalized);
    }

    generateUniqueFilename(metadata) {
        // Start with workflow name or default
        let baseName = 'workflow';
        
        if (metadata.name) {
            baseName = metadata.name;
        } else if (metadata.title) {
            baseName = metadata.title;
        }
        
        // Sanitize the name
        baseName = baseName
            .replace(/[^a-z0-9\-_\s]/gi, '')
            .replace(/\s+/g, '_')
            .toLowerCase()
            .substring(0, 50); // Limit length
        
        // Add timestamp for uniqueness
        const timestamp = Date.now();
        
        // Add random suffix to avoid collisions
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        
        return `${baseName}_${timestamp}_${randomSuffix}.json`;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async cleanup() {
        try {
            // Save final reports
            await this.saveDuplicatesReport();
            
            logger.storage('Storage manager cleanup completed');
            
        } catch (error) {
            logger.error('Error during storage cleanup:', error.message);
        }
    }

    getStats() {
        return {
            totalStored: this.hashes.size,
            duplicatesFound: this.duplicates.size,
            storageDirectory: this.outputDir
        };
    }
}

module.exports = StorageManager;
