const robotsParser = require('robots-parser');
const axios = require('axios');
const logger = require('./logger');
const RateLimiter = require('./rate-limiter');

class EthicalScraper {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || 'https://n8nworkflows.xyz';
        this.userAgent = options.userAgent || 'n8n-workflow-scraper/1.0.0 (+https://github.com/user/n8n-scraper)';
        this.respectRobotsTxt = options.respectRobotsTxt !== false;
        this.rateLimiter = new RateLimiter(options.rateLimiting || {});
        
        this.robotsRules = null;
        this.robotsLoaded = false;
        this.allowedPaths = new Set();
        this.disallowedPaths = new Set();
    }

    async initialize() {
        try {
            logger.info('Initializing ethical scraper...');
            
            if (this.respectRobotsTxt) {
                await this.loadRobotsTxt();
            }
            
            logger.info('Ethical scraper initialized successfully');
            
        } catch (error) {
            logger.warn('Failed to initialize ethical scraper:', error.message);
            // Continue without robots.txt if it fails
        }
    }

    async loadRobotsTxt() {
        try {
            const robotsUrl = new URL('/robots.txt', this.baseUrl).toString();
            logger.info(`Loading robots.txt from: ${robotsUrl}`);
            
            const response = await axios.get(robotsUrl, {
                timeout: 10000,
                headers: {
                    'User-Agent': this.userAgent
                }
            });
            
            this.robotsRules = robotsParser(robotsUrl, response.data);
            this.robotsLoaded = true;
            
            logger.info('Robots.txt loaded successfully');
            this.logRobotsRules();
            
        } catch (error) {
            logger.warn('Failed to load robots.txt:', error.message);
            // If robots.txt fails to load, we'll be permissive
            this.robotsLoaded = false;
        }
    }

    logRobotsRules() {
        if (!this.robotsRules) return;
        
        try {
            // Log crawl delay
            const crawlDelay = this.robotsRules.getCrawlDelay(this.userAgent);
            if (crawlDelay) {
                logger.info(`Robots.txt crawl delay: ${crawlDelay} seconds`);
            }
            
            // Log sitemap URLs
            const sitemaps = this.robotsRules.getSitemaps();
            if (sitemaps && sitemaps.length > 0) {
                logger.info(`Robots.txt sitemaps found: ${sitemaps.join(', ')}`);
            }
            
        } catch (error) {
            logger.warn('Error parsing robots.txt rules:', error.message);
        }
    }

    async canAccess(url) {
        // Always allow if robots.txt is not loaded or not respected
        if (!this.respectRobotsTxt || !this.robotsLoaded || !this.robotsRules) {
            return true;
        }

        try {
            const allowed = this.robotsRules.isAllowed(url, this.userAgent);
            
            if (!allowed) {
                logger.info(`Access denied by robots.txt: ${url}`);
                this.disallowedPaths.add(url);
            } else {
                this.allowedPaths.add(url);
            }
            
            return allowed;
            
        } catch (error) {
            logger.warn(`Error checking robots.txt for ${url}:`, error.message);
            // Be permissive on error
            return true;
        }
    }

    async waitForRateLimit() {
        await this.rateLimiter.waitForSlot();
        
        // Additional delay from robots.txt if specified
        if (this.robotsRules) {
            try {
                const crawlDelay = this.robotsRules.getCrawlDelay(this.userAgent);
                if (crawlDelay && crawlDelay > 0) {
                    const delayMs = crawlDelay * 1000;
                    logger.debug(`Applying robots.txt crawl delay: ${delayMs}ms`);
                    await this.sleep(delayMs);
                }
            } catch (error) {
                logger.warn('Error applying crawl delay:', error.message);
            }
        }
    }

    async respectfulRequest(url, options = {}) {
        // Check if we can access this URL
        const canAccess = await this.canAccess(url);
        if (!canAccess) {
            throw new Error(`Access to ${url} is disallowed by robots.txt`);
        }

        // Wait for rate limiting
        await this.waitForRateLimit();

        // Add ethical headers
        const ethicalHeaders = {
            'User-Agent': this.userAgent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            ...options.headers
        };

        logger.debug(`Making respectful request to: ${url}`);

        return {
            url,
            headers: ethicalHeaders,
            canProceed: true
        };
    }

    async getSitemaps() {
        if (!this.robotsRules) {
            return [];
        }

        try {
            return this.robotsRules.getSitemaps() || [];
        } catch (error) {
            logger.warn('Error getting sitemaps from robots.txt:', error.message);
            return [];
        }
    }

    async parseSitemap(sitemapUrl) {
        try {
            logger.info(`Parsing sitemap: ${sitemapUrl}`);
            
            const canAccess = await this.canAccess(sitemapUrl);
            if (!canAccess) {
                logger.warn(`Cannot access sitemap due to robots.txt: ${sitemapUrl}`);
                return [];
            }

            await this.waitForRateLimit();

            const response = await axios.get(sitemapUrl, {
                timeout: 30000,
                headers: {
                    'User-Agent': this.userAgent
                }
            });

            // Parse XML sitemap
            const urls = this.extractUrlsFromSitemap(response.data);
            logger.info(`Found ${urls.length} URLs in sitemap: ${sitemapUrl}`);
            
            return urls;

        } catch (error) {
            logger.warn(`Failed to parse sitemap ${sitemapUrl}:`, error.message);
            return [];
        }
    }

    extractUrlsFromSitemap(xmlContent) {
        const urls = [];
        
        try {
            // Simple regex-based XML parsing for sitemap URLs
            const urlMatches = xmlContent.match(/<loc>(.*?)<\/loc>/g);
            
            if (urlMatches) {
                urlMatches.forEach(match => {
                    const url = match.replace(/<\/?loc>/g, '').trim();
                    if (url && this.isValidUrl(url)) {
                        urls.push(url);
                    }
                });
            }

            // Also look for sitemap index files
            const sitemapMatches = xmlContent.match(/<sitemap>[\s\S]*?<\/sitemap>/g);
            
            if (sitemapMatches) {
                sitemapMatches.forEach(sitemapBlock => {
                    const locMatch = sitemapBlock.match(/<loc>(.*?)<\/loc>/);
                    if (locMatch) {
                        const sitemapUrl = locMatch[1].trim();
                        if (sitemapUrl && this.isValidUrl(sitemapUrl)) {
                            urls.push(sitemapUrl);
                        }
                    }
                });
            }

        } catch (error) {
            logger.warn('Error parsing sitemap XML:', error.message);
        }
        
        return urls;
    }

    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getEthicalStats() {
        return {
            robotsLoaded: this.robotsLoaded,
            respectRobotsTxt: this.respectRobotsTxt,
            allowedPaths: this.allowedPaths.size,
            disallowedPaths: this.disallowedPaths.size,
            rateLimiter: this.rateLimiter.getStats(),
            userAgent: this.userAgent
        };
    }

    // Ethical guidelines checker
    checkEthicalGuidelines() {
        const guidelines = {
            hasUserAgent: !!this.userAgent && this.userAgent !== 'default',
            respectsRobotsTxt: this.respectRobotsTxt,
            hasRateLimit: this.rateLimiter.requestsPerMinute <= 60, // Max 1 request per second
            hasDelay: this.rateLimiter.minDelay >= 1000, // At least 1 second delay
            robotsLoaded: this.robotsLoaded
        };

        const violations = [];
        
        if (!guidelines.hasUserAgent) {
            violations.push('Should have a proper User-Agent string');
        }
        
        if (!guidelines.respectsRobotsTxt) {
            violations.push('Should respect robots.txt');
        }
        
        if (!guidelines.hasRateLimit) {
            violations.push('Rate limit should be reasonable (max 60 requests/minute)');
        }
        
        if (!guidelines.hasDelay) {
            violations.push('Should have at least 1 second delay between requests');
        }

        return {
            guidelines,
            violations,
            isEthical: violations.length === 0
        };
    }
}

module.exports = EthicalScraper;
