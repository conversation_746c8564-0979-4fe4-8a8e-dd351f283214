const logger = require('./logger');
const fs = require('fs-extra');
const path = require('path');

class ErrorHandler {
    constructor(options = {}) {
        this.errorLogFile = options.errorLogFile || './logs/errors.json';
        this.maxRetries = options.maxRetries || 3;
        this.retryDelay = options.retryDelay || 1000;
        this.errors = [];
        
        // Ensure error log directory exists
        fs.ensureDirSync(path.dirname(this.errorLogFile));
    }

    async handleError(error, context = {}) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            message: error.message,
            stack: error.stack,
            context,
            type: error.constructor.name
        };

        this.errors.push(errorInfo);
        
        // Log the error
        logger.error('Error handled:', {
            message: error.message,
            context,
            type: error.constructor.name
        });

        // Categorize error and determine recovery strategy
        const errorCategory = this.categorizeError(error);
        const recoveryStrategy = this.getRecoveryStrategy(errorCategory, context);

        return {
            error: errorInfo,
            category: errorCategory,
            recoveryStrategy,
            shouldRetry: recoveryStrategy.retry,
            retryDelay: recoveryStrategy.delay
        };
    }

    categorizeError(error) {
        const message = error.message.toLowerCase();
        const stack = error.stack ? error.stack.toLowerCase() : '';

        // Network-related errors
        if (message.includes('timeout') || message.includes('econnreset') || 
            message.includes('enotfound') || message.includes('network')) {
            return 'network';
        }

        // Cloudflare-related errors
        if (message.includes('cloudflare') || message.includes('blocked') || 
            message.includes('challenge') || message.includes('ray id')) {
            return 'cloudflare';
        }

        // Rate limiting errors
        if (message.includes('rate limit') || message.includes('too many requests') ||
            message.includes('429')) {
            return 'rate_limit';
        }

        // Authentication/Authorization errors
        if (message.includes('unauthorized') || message.includes('forbidden') ||
            message.includes('401') || message.includes('403')) {
            return 'auth';
        }

        // File system errors
        if (message.includes('enoent') || message.includes('eacces') || 
            message.includes('file') || message.includes('directory')) {
            return 'filesystem';
        }

        // Parsing errors
        if (message.includes('json') || message.includes('parse') || 
            message.includes('syntax')) {
            return 'parsing';
        }

        // Browser/Puppeteer errors
        if (message.includes('puppeteer') || message.includes('browser') || 
            message.includes('page') || stack.includes('puppeteer')) {
            return 'browser';
        }

        // Configuration errors
        if (message.includes('config') || message.includes('invalid') || 
            message.includes('missing')) {
            return 'configuration';
        }

        return 'unknown';
    }

    getRecoveryStrategy(errorCategory, context = {}) {
        const strategies = {
            network: {
                retry: true,
                delay: 5000,
                maxRetries: 5,
                exponentialBackoff: true,
                description: 'Network error - retry with exponential backoff'
            },
            cloudflare: {
                retry: true,
                delay: 10000,
                maxRetries: 3,
                exponentialBackoff: true,
                description: 'Cloudflare protection - retry with longer delay'
            },
            rate_limit: {
                retry: true,
                delay: 60000, // 1 minute
                maxRetries: 3,
                exponentialBackoff: false,
                description: 'Rate limited - wait and retry'
            },
            auth: {
                retry: false,
                delay: 0,
                maxRetries: 0,
                exponentialBackoff: false,
                description: 'Authentication error - manual intervention required'
            },
            filesystem: {
                retry: true,
                delay: 1000,
                maxRetries: 2,
                exponentialBackoff: false,
                description: 'File system error - retry briefly'
            },
            parsing: {
                retry: false,
                delay: 0,
                maxRetries: 0,
                exponentialBackoff: false,
                description: 'Parsing error - skip this item'
            },
            browser: {
                retry: true,
                delay: 3000,
                maxRetries: 3,
                exponentialBackoff: true,
                description: 'Browser error - restart browser and retry'
            },
            configuration: {
                retry: false,
                delay: 0,
                maxRetries: 0,
                exponentialBackoff: false,
                description: 'Configuration error - fix configuration'
            },
            unknown: {
                retry: true,
                delay: 2000,
                maxRetries: 2,
                exponentialBackoff: true,
                description: 'Unknown error - retry with caution'
            }
        };

        return strategies[errorCategory] || strategies.unknown;
    }

    async retryWithStrategy(operation, errorCategory, context = {}) {
        const strategy = this.getRecoveryStrategy(errorCategory, context);
        let lastError;
        
        for (let attempt = 1; attempt <= strategy.maxRetries + 1; attempt++) {
            try {
                logger.debug(`Attempting operation (attempt ${attempt}/${strategy.maxRetries + 1})`);
                return await operation();
            } catch (error) {
                lastError = error;
                
                if (attempt <= strategy.maxRetries) {
                    let delay = strategy.delay;
                    
                    if (strategy.exponentialBackoff) {
                        delay = strategy.delay * Math.pow(2, attempt - 1);
                    }
                    
                    logger.warn(`Operation failed (attempt ${attempt}), retrying in ${delay}ms:`, error.message);
                    await this.sleep(delay);
                } else {
                    logger.error(`Operation failed after ${strategy.maxRetries + 1} attempts:`, error.message);
                }
            }
        }
        
        throw lastError;
    }

    async saveErrorReport() {
        try {
            const report = {
                generatedAt: new Date().toISOString(),
                totalErrors: this.errors.length,
                errorsByCategory: this.getErrorsByCategory(),
                errorsByType: this.getErrorsByType(),
                errors: this.errors
            };

            await fs.writeJson(this.errorLogFile, report, { spaces: 2 });
            logger.info(`Error report saved to: ${this.errorLogFile}`);
            
            return report;
        } catch (error) {
            logger.error('Failed to save error report:', error.message);
        }
    }

    getErrorsByCategory() {
        const categories = {};
        
        this.errors.forEach(error => {
            const category = this.categorizeError({ message: error.message, stack: '' });
            categories[category] = (categories[category] || 0) + 1;
        });
        
        return categories;
    }

    getErrorsByType() {
        const types = {};
        
        this.errors.forEach(error => {
            types[error.type] = (types[error.type] || 0) + 1;
        });
        
        return types;
    }

    getErrorStats() {
        return {
            totalErrors: this.errors.length,
            errorsByCategory: this.getErrorsByCategory(),
            errorsByType: this.getErrorsByType(),
            recentErrors: this.errors.slice(-10) // Last 10 errors
        };
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Circuit breaker pattern
    createCircuitBreaker(operation, options = {}) {
        const threshold = options.threshold || 5;
        const timeout = options.timeout || 60000; // 1 minute
        const resetTimeout = options.resetTimeout || 300000; // 5 minutes
        
        let failures = 0;
        let lastFailureTime = 0;
        let state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN

        return async (...args) => {
            const now = Date.now();

            // Reset if enough time has passed
            if (state === 'OPEN' && now - lastFailureTime > resetTimeout) {
                state = 'HALF_OPEN';
                failures = 0;
            }

            // Reject if circuit is open
            if (state === 'OPEN') {
                throw new Error('Circuit breaker is OPEN - operation temporarily disabled');
            }

            try {
                const result = await Promise.race([
                    operation(...args),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('Circuit breaker timeout')), timeout)
                    )
                ]);

                // Success - reset failures if we were in half-open state
                if (state === 'HALF_OPEN') {
                    state = 'CLOSED';
                    failures = 0;
                }

                return result;
            } catch (error) {
                failures++;
                lastFailureTime = now;

                if (failures >= threshold) {
                    state = 'OPEN';
                    logger.warn(`Circuit breaker opened after ${failures} failures`);
                }

                throw error;
            }
        };
    }
}

module.exports = ErrorHandler;
