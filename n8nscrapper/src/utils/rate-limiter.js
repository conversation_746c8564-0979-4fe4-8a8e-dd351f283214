const logger = require('./logger');

class RateLimiter {
    constructor(options = {}) {
        this.requestsPerMinute = options.requestsPerMinute || 30;
        this.burstLimit = options.burstLimit || 5;
        this.minDelay = options.minDelay || 1000; // 1 second minimum
        
        this.requests = [];
        this.lastRequestTime = 0;
    }

    async waitForSlot() {
        const now = Date.now();
        
        // Remove requests older than 1 minute
        this.requests = this.requests.filter(time => now - time < 60000);
        
        // Check if we've hit the per-minute limit
        if (this.requests.length >= this.requestsPerMinute) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = 60000 - (now - oldestRequest);
            
            if (waitTime > 0) {
                logger.info(`Rate limit reached, waiting ${waitTime}ms`);
                await this.sleep(waitTime);
                return this.waitForSlot(); // Recursive call after waiting
            }
        }
        
        // Check burst limit (requests in quick succession)
        const recentRequests = this.requests.filter(time => now - time < 10000); // Last 10 seconds
        if (recentRequests.length >= this.burstLimit) {
            const waitTime = 10000; // Wait 10 seconds
            logger.info(`Burst limit reached, waiting ${waitTime}ms`);
            await this.sleep(waitTime);
            return this.waitForSlot();
        }
        
        // Ensure minimum delay between requests
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.minDelay) {
            const waitTime = this.minDelay - timeSinceLastRequest;
            await this.sleep(waitTime);
        }
        
        // Record this request
        this.requests.push(Date.now());
        this.lastRequestTime = Date.now();
        
        logger.debug(`Rate limiter: ${this.requests.length}/${this.requestsPerMinute} requests in last minute`);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getStats() {
        const now = Date.now();
        const recentRequests = this.requests.filter(time => now - time < 60000);
        
        return {
            requestsInLastMinute: recentRequests.length,
            requestsPerMinuteLimit: this.requestsPerMinute,
            burstLimit: this.burstLimit,
            minDelay: this.minDelay
        };
    }
}

module.exports = RateLimiter;
