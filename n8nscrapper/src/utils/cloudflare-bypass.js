const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const AdblockerPlugin = require('puppeteer-extra-plugin-adblocker');
const UserAgent = require('user-agents');
const logger = require('./logger');

// Add stealth plugin to avoid detection
puppeteer.use(StealthPlugin());
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));

class CloudflareBypass {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== false,
            timeout: options.timeout || 30000,
            userDataDir: options.userDataDir || './browser-data',
            proxy: options.proxy || null,
            ...options
        };
        this.browser = null;
        this.page = null;
    }

    async initialize() {
        try {
            const launchOptions = {
                headless: this.options.headless,
                userDataDir: this.options.userDataDir,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--window-size=1920,1080'
                ]
            };

            if (this.options.proxy) {
                launchOptions.args.push(`--proxy-server=${this.options.proxy}`);
            }

            this.browser = await puppeteer.launch(launchOptions);
            this.page = await this.browser.newPage();

            // Set random user agent
            const userAgent = new UserAgent();
            await this.page.setUserAgent(userAgent.toString());

            // Set viewport
            await this.page.setViewport({ width: 1920, height: 1080 });

            // Set extra headers
            await this.page.setExtraHTTPHeaders({
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            });

            logger.info('Cloudflare bypass initialized successfully');
            return true;
        } catch (error) {
            logger.error('Failed to initialize Cloudflare bypass:', error);
            return false;
        }
    }

    async bypassCloudflare(url, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info(`Attempting to bypass Cloudflare for ${url} (attempt ${attempt}/${maxRetries})`);

                if (!this.page) {
                    await this.initialize();
                }

                // Navigate to the page
                await this.page.goto(url, { 
                    waitUntil: 'networkidle0', 
                    timeout: this.options.timeout 
                });

                // Check if we're blocked by Cloudflare
                const isBlocked = await this.isCloudflareBlocked();
                
                if (isBlocked) {
                    logger.info('Cloudflare challenge detected, waiting for bypass...');
                    await this.waitForCloudflareBypass();
                }

                // Verify we can access the content
                const content = await this.page.content();
                if (content.includes('Cloudflare') && content.includes('blocked')) {
                    throw new Error('Still blocked by Cloudflare');
                }

                logger.info('Successfully bypassed Cloudflare protection');
                return this.page;

            } catch (error) {
                logger.warn(`Attempt ${attempt} failed:`, error.message);
                
                if (attempt === maxRetries) {
                    throw new Error(`Failed to bypass Cloudflare after ${maxRetries} attempts: ${error.message}`);
                }

                // Wait before retry with exponential backoff
                const delay = Math.pow(2, attempt) * 1000;
                logger.info(`Waiting ${delay}ms before retry...`);
                await this.sleep(delay);

                // Refresh the page for next attempt
                if (this.page) {
                    try {
                        await this.page.reload({ waitUntil: 'networkidle0' });
                    } catch (reloadError) {
                        logger.warn('Failed to reload page:', reloadError.message);
                    }
                }
            }
        }
    }

    async isCloudflareBlocked() {
        try {
            const title = await this.page.title();
            const content = await this.page.content();
            
            return (
                title.includes('Attention Required') ||
                title.includes('Cloudflare') ||
                content.includes('cf-browser-verification') ||
                content.includes('cf-challenge-form') ||
                content.includes('Ray ID') ||
                content.includes('blocked')
            );
        } catch (error) {
            logger.warn('Error checking Cloudflare status:', error.message);
            return false;
        }
    }

    async waitForCloudflareBypass(timeout = 30000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                const isBlocked = await this.isCloudflareBlocked();
                
                if (!isBlocked) {
                    logger.info('Cloudflare bypass completed');
                    return true;
                }

                // Wait a bit before checking again
                await this.sleep(1000);
                
            } catch (error) {
                logger.warn('Error during Cloudflare bypass wait:', error.message);
            }
        }

        throw new Error('Cloudflare bypass timeout');
    }

    async getPageContent() {
        if (!this.page) {
            throw new Error('Page not initialized');
        }
        return await this.page.content();
    }

    async evaluateScript(script) {
        if (!this.page) {
            throw new Error('Page not initialized');
        }
        return await this.page.evaluate(script);
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async close() {
        try {
            if (this.page) {
                await this.page.close();
                this.page = null;
            }
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }
            logger.info('Cloudflare bypass closed successfully');
        } catch (error) {
            logger.error('Error closing Cloudflare bypass:', error);
        }
    }
}

module.exports = CloudflareBypass;
