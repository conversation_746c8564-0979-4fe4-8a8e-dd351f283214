require('dotenv').config();

const config = {
    // Target website configuration
    target: {
        url: process.env.TARGET_URL || 'https://n8nworkflows.xyz',
        timeout: parseInt(process.env.TIMEOUT_MS) || 30000,
        maxRetries: parseInt(process.env.MAX_RETRIES) || 3
    },

    // Output configuration
    output: {
        dir: process.env.OUTPUT_DIR || './workflows',
        rawDir: 'raw',
        categoriesDir: 'categories',
        metadataFile: 'metadata.json'
    },

    // Scraping configuration
    scraping: {
        maxConcurrent: parseInt(process.env.MAX_CONCURRENT_REQUESTS) || 3,
        requestDelay: parseInt(process.env.REQUEST_DELAY_MS) || 2000,
        requestsPerMinute: parseInt(process.env.REQUESTS_PER_MINUTE) || 30,
        burstLimit: parseInt(process.env.BURST_LIMIT) || 5
    },

    // Browser configuration
    browser: {
        headless: process.env.HEADLESS !== 'false',
        userDataDir: process.env.USER_DATA_DIR || './browser-data',
        useProxy: !!process.env.PROXY_URL,
        proxy: {
            url: process.env.PROXY_URL || null,
            username: process.env.PROXY_USERNAME || null,
            password: process.env.PROXY_PASSWORD || null
        }
    },

    // Cloudflare bypass configuration
    cloudflare: {
        useStealth: process.env.USE_STEALTH !== 'false',
        waitForBypass: process.env.WAIT_FOR_CLOUDFLARE !== 'false',
        bypassTimeout: parseInt(process.env.CLOUDFLARE_TIMEOUT) || 30000
    },

    // Logging configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || './logs/scraper.log',
        console: process.env.NODE_ENV !== 'production'
    },

    // Features
    features: {
        deduplication: true,
        categorization: true,
        validation: true,
        metadataGeneration: true,
        progressReporting: true
    }
};

// Validation
function validateConfig() {
    const errors = [];

    // Validate required fields
    if (!config.target.url) {
        errors.push('TARGET_URL is required');
    }

    // Validate numeric fields
    if (config.scraping.maxConcurrent < 1 || config.scraping.maxConcurrent > 10) {
        errors.push('MAX_CONCURRENT_REQUESTS must be between 1 and 10');
    }

    if (config.scraping.requestsPerMinute < 1 || config.scraping.requestsPerMinute > 120) {
        errors.push('REQUESTS_PER_MINUTE must be between 1 and 120');
    }

    // Validate proxy configuration
    if (config.browser.useProxy && !config.browser.proxy.url) {
        errors.push('PROXY_URL is required when using proxy');
    }

    if (errors.length > 0) {
        throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
}

// Override config with CLI options
function overrideWithOptions(options) {
    const overrideConfig = { ...config };

    if (options.output) {
        overrideConfig.output.dir = options.output;
    }

    if (options.concurrent) {
        overrideConfig.scraping.maxConcurrent = parseInt(options.concurrent);
    }

    if (options.delay) {
        overrideConfig.scraping.requestDelay = parseInt(options.delay);
    }

    if (options.headless !== undefined) {
        overrideConfig.browser.headless = options.headless;
    }

    if (options.verbose) {
        overrideConfig.logging.level = 'debug';
    }

    if (options.proxy) {
        overrideConfig.browser.useProxy = true;
        overrideConfig.browser.proxy.url = options.proxy;
    }

    if (options.timeout) {
        overrideConfig.target.timeout = parseInt(options.timeout);
    }

    if (options.retries) {
        overrideConfig.target.maxRetries = parseInt(options.retries);
    }

    return overrideConfig;
}

module.exports = {
    config,
    validateConfig,
    overrideWithOptions
};
