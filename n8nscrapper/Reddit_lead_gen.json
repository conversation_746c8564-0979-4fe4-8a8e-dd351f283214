{"name": "Reddit lead gen", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "id": "ee1a396e-7d50-43cf-ae22-15f4d11960ea", "name": "When clicking ‘Test workflow’"}, {"parameters": {"method": "POST", "url": "https://www.reddit.com/api/v1/access_token", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "MyScript/1.0 by {Your username}"}, {"name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "grant_type", "value": "password"}, {"name": "username", "value": "{Your username}"}, {"name": "password", "value": "{Your password}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "aeba9e96-7146-492b-9315-1d31f52219bd", "name": "Reddit Credential", "credentials": {"httpBasicAuth": {"id": "hOtxjzlFyQsfjlwZ", "name": "Unnamed credential"}}}, {"parameters": {"fieldToSplitOut": "data.children", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [660, 0], "id": "16d72df0-6609-465e-b2aa-e86ae6b7949b", "name": "Split Out"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [880, 0], "id": "8aac0780-1a38-4e40-ab7f-a9f6091379a7", "name": "Loop Over Items"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1vTTG5kItyjXv3yCF0zcyNrUo673IKdlgNfjvKjqMVYg", "mode": "list", "cachedResultName": "Reddit lead gen", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vTTG5kItyjXv3yCF0zcyNrUo673IKdlgNfjvKjqMVYg/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1vTTG5kItyjXv3yCF0zcyNrUo673IKdlgNfjvKjqMVYg/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Reddit Author": "={{ $json.author }}", "Thread Body": "={{ $json.post_body }}", "Lead": "={{ $json.is_lead }}", "Lead Type": "={{ $json.lead_type }}", "Priority": "={{ $json.priority }}", "Reasoning": "={{ $json.reasoning }}", "Resource link": "={{ $json.resource_link }}", "Post url": "={{ $json.reddit_url }}", "Post ID": "={{ $('Loop Over Items').item.json.data.id }}"}, "matchingColumns": [], "schema": [{"id": "Reddit Author", "displayName": "Reddit Author", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON> Body", "displayName": "<PERSON><PERSON><PERSON> Body", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Lead", "displayName": "Lead", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Lead Type", "displayName": "Lead Type", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Priority", "displayName": "Priority", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Reasoning", "displayName": "Reasoning", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Resource link", "displayName": "Resource link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Post url", "displayName": "Post url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Post ID", "displayName": "Post ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1700, 0], "id": "ef1ea3af-84e5-447f-b99a-82dddee5d064", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "NL2OcQ6fxWPJs5vu", "name": "Google Sheets account"}}}, {"parameters": {"url": "https://oauth.reddit.com/r/n8n/hot.json?limit=10", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "={{ $json.token_type }} {{ $json.access_token }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "User-Agent", "value": "MyScript/1.0 by {Your username}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "d56a0fc4-e5c7-41c7-b5c9-7ca9c646af03", "name": "New Post"}, {"parameters": {"promptType": "define", "text": "=Target Persona-\n\nI am an AI Automation Specialist focused on building custom automation solutions for businesses and individuals. \n\nMy core expertise includes:\n- Creating custom AI integrations using OpenAI, LangChain, and other LLM platforms\n- Integrating disparate systems via APIs and webhooks\n- Setting up and optimizing automation tools like n8n, Make.com, and Zapier\n- Streamlining CRM workflows in platforms like Go Highlevel, Hubspot, and Airtable\n\nMy ideal leads are:\n- Business owners or teams struggling with repetitive manual processes\n- People seeking to automate data collection, processing, or reporting\n- Users of no-code/low-code platforms looking to extend functionality\n- Professionals wanting to leverage AI for content creation, data analysis, or customer service\n- Startups needing custom workflow solutions without hiring full development teams\n- Existing users of n8n, React, or NextJS needing expert help with complex implementations\n\nI provide the most value for projects involving:\n- Custom AI agents and workflows\n- Process automation that saves 10+ hours of manual work weekly\n- API integrations between multiple platforms\n- Extending functionality of existing SaaS products\n- Building internal tools and dashboards for business intelligence\n\n\nCarefully review the details of the following Reddit post:\n\nPost Title: {{ $json.data.title }}\nPost Body/Selftext: {{ $json.data.selftext }}\nAuthor: {{ $json.data.author }}\nPost URL: {{ $json.data.url }}\nUpvote Ratio: {{ $json.data.upvote_ratio }}(Indicator of community agreement/visibility)\nNumber of Comments: {{ $json.data.num_comments }}(Indicator of discussion level/engagement)\n", "options": {"systemMessage": "=You are an elite Lead Qualification Analyst specializing in identifying high-value automation opportunities from Reddit posts. Your expertise in n8n, AI automation, and integration workflows makes you uniquely qualified to identify potential clients who match our target persona.\n\n## EVALUATION FRAMEWORK\n\n### STEP 1: PERSONA ALIGNMENT ASSESSMENT (40% weight)\nEvaluate how closely the post aligns with our target customer:\n- Author demonstrates need for custom automation solutions\n- Post mentions technologies in our expertise stack (n8n, React, NextJS, APIs, AI)\n- Problem complexity suggests need for specialized freelance assistance\n- Language indicates decision-making authority or purchasing intent\n\n### STEP 2: OPPORTUNITY IDENTIFICATION (35% weight)\nCategorize the specific need or opportunity:\n[A] SUPPORT: Technical help with n8n implementation or troubleshooting\n[B] SOLUTION: Business problem that could be solved with automation\n[C] COMPARISON: Evaluating n8n against alternatives, suggesting consideration phase\n[D] SHOWCASE: Sharing complex workflows, indicating advanced usage\n[E] FEATURE: Requesting capabilities that could be solved with custom development\n[F] INTEGRATION: Connecting systems or APIs requiring specialized knowledge\n\n### STEP 3: ENGAGEMENT POTENTIAL (25% weight)\nAnalyze factors suggesting conversion potential:\n- Urgency indicators in language (\"need ASAP\", \"stuck\", \"deadline\")\n- Complexity level requiring expert intervention\n- Evidence of previous automation attempts\n- Mentions of budget, timeline, or project parameters\n- Quality of problem articulation and technical sophistication\n\n### STEP 4: RESOURCE EXTRACTION\nExtract valuable contextual information:\n- Scan for URLs to: Google Drive, GitHub, YouTube, documentation, blog posts\n- Note technology stack details or specific error messages\n- Identify existing tools/platforms mentioned\n- Extract any timeline or priority indicators\n\n## CLASSIFICATION OUTPUTS\n\nBased on your analysis, provide a structured determination:\n\n1. LEAD CLASSIFICATION: (Lead | Not Lead)\n2. LEAD TYPE: (Support Request | Solution Inquiry | Tool Comparison | Showcase | Feature Request | Integration Need | General Question)\n3. PRIORITY: (High | Medium | Low)\n4. REASONING: Concise explanation (max 3 sentences) justifying your classification and highlighting key opportunity indicators\n5. RESOURCE LINK: Extract the most relevant URL if available (null if none)\n\n## CRITICAL EXCLUSIONS\n\nDO NOT classify as leads:\n- Posts primarily showcasing third-party products unrelated to n8n\n- Simple questions answerable through documentation\n- Posts from users explicitly stating they've already hired help\n- General coding projects without automation/integration focus\n- Questions about unrelated technologies unless specifically wanting to integrate with n8n\n\nYour analysis must be data-driven, comprehensive, and focused on identifying genuine opportunities for an AI automation freelancer specializing in n8n and related technologies.\n\n## Output Requirements:\nYou MUST return ONLY a valid JSON object containing the following fields:\n\n{\n  \"is_lead\": true | false, // Boolean: Does this post qualify as a lead based on the persona and guidelines?\n  \"lead_type\": \"string\", // Classify the lead: e.g., \"Support Request\", \"Use Case Inquiry\", \"Showcase (Built with n8n)\", \"Competitor Comparison\", \"Positive Feedback\", \"Feature Request\", \"General Question\", \"Not a Lead\"\n  \"priority\": \"string\", // Estimate priority: \"High\", \"Medium\", \"Low\", \"None\"\n  \"post_body\": \"{{ $json.data.selftext }}\", // The original selftext\n  \"reasoning\": \"string\", // Concise explanation for classification and priority. Mention if a resource link was found/not found.\n  \"resource_link\": \"string | null\", // EXTRACT the first relevant URL (Google Drive, YouTube, GitHub, Blog, Template etc.) found in the post body. Return null if no relevant link is explicitly shared by the author.\n  \"author\": \"{{ $json.data.author }}\", // The Reddit author's username\n  \"reddit_url\": \"{{ $json.data.url }}\" // The full URL to the Reddit post\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1100, 0], "id": "ff82770f-9cf9-48ed-a525-07c90f215739", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-06-05", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1140, 220], "id": "713f3e95-20c4-44f8-bbfb-5b6d9b35c87c", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "nscaoDHmHaSsUvs0", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"jsCode": "// Get the raw input from the previous node\nconst raw = items[0].json.output;\n\n// Remove ```json\\n and ending ```\nconst cleaned = raw.replace(/^```json\\n/, '').replace(/```$/, '').trim();\n\n// Parse to JSON safely\nlet parsed;\ntry {\n  parsed = JSON.parse(cleaned);\n} catch (error) {\n  throw new Error(\"Invalid JSON format: \" + error.message);\n}\n\n// Return the parsed JSON\nreturn [{ json: parsed }];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1460, 0], "id": "1a0872d3-a8b1-4498-92a5-bb4c65f59625", "name": "Code"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Reddit Credential", "type": "main", "index": 0}]]}, "Reddit Credential": {"main": [[{"node": "New Post", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "New Post": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "2e38da55-bdc2-4360-8b83-de6509d27da9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0f6b06392e6ba9de49771585b7e4cdeacdf7cdc06484fbe6acd8ba3e84e73b87"}, "id": "4a6tRM0WMMDCxF0x", "tags": []}