{"name": "n8n-workflow-scraper", "version": "1.0.0", "description": "A comprehensive scraper for n8n workflows from n8nworkflows.xyz", "main": "src/index.js", "scripts": {"start": "node src/scraper.js", "scrape": "node src/scraper.js", "cli": "node src/cli.js", "test": "node test.js", "install-deps": "./install.sh", "dev": "nodemon src/index.js", "lint": "eslint src/", "analyze": "node src/cli.js analyze"}, "keywords": ["n8n", "workflow", "scraper", "automation", "web-scraping"], "author": "n8n-scraper", "license": "MIT", "dependencies": {"axios": "^1.6.2", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "commander": "^11.1.0", "dotenv": "^16.3.1", "fs-extra": "^11.2.0", "mime-types": "^2.1.35", "p-limit": "^3.1.0", "p-retry": "^4.6.2", "progress": "^2.0.3", "proxy-agent": "^6.3.1", "puppeteer": "^24.16.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-adblocker": "^2.13.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "robots-parser": "^3.0.1", "sanitize-filename": "^1.6.3", "user-agents": "^1.1.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}