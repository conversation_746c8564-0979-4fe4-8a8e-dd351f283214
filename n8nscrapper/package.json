{"name": "n8n-workflow-scraper", "version": "1.0.0", "description": "A comprehensive scraper for n8n workflows from n8nworkflows.xyz", "main": "src/index.js", "scripts": {"start": "node src/scraper.js", "scrape": "node src/scraper.js", "cli": "node src/cli.js", "test": "node test.js", "install-deps": "./install.sh", "dev": "nodemon src/index.js", "lint": "eslint src/", "analyze": "node src/cli.js analyze"}, "keywords": ["n8n", "workflow", "scraper", "automation", "web-scraping"], "author": "n8n-scraper", "license": "MIT", "dependencies": {"puppeteer": "^21.5.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-adblocker": "^2.13.6", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "fs-extra": "^11.2.0", "commander": "^11.1.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "p-limit": "^4.0.0", "p-retry": "^6.2.0", "user-agents": "^1.1.0", "proxy-agent": "^6.3.1", "robots-parser": "^3.0.1", "mime-types": "^2.1.35", "sanitize-filename": "^1.6.3", "progress": "^2.0.3", "chalk": "^4.1.2"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.55.0", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}