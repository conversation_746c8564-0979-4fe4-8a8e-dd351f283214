{"name": "Generate AI Videos with Google Veo3, Save to Google Drive and Upload to YouTube", "nodes": [{"parameters": {}, "id": "f1fcb9ab-37aa-44a3-91fc-8480351aa5cd", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [380, 1540], "typeVersion": 1}, {"parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $('Create Video').item.json.request_id }}/status ", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "id": "58194812-a64d-401e-be5e-2f1ba9210557", "name": "Get status", "type": "n8n-nodes-base.httpRequest", "position": [1440, 1540], "typeVersion": 4.2}, {"parameters": {"amount": 60}, "id": "1e23339c-f829-4426-b4f6-b02add8f8c5e", "name": "Wait 60 sec.", "type": "n8n-nodes-base.wait", "position": [1260, 1540], "webhookId": "490facb8-d0ae-402e-ab59-a87720cf0bdd", "typeVersion": 1.1}, {"parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "id": "98ad8a6d-9ded-4f4e-a4ef-b624f7fe7e39", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [380, 1400], "typeVersion": 1.2}, {"parameters": {"content": "# Generate AI Videos with Google Veo3, Save to Google Drive and Upload to YouTube\n\nThis workflow allows users to **generate AI videos** using **Google Veo3**, save them to **Google Drive**, generate optimized YouTube titles with GPT-4o, and **automatically upload them to YouTube** . The entire process is triggered from a Google Sheet that acts as the central interface for input and output.\n\nIT automates video creation, uploading, and tracking, ensuring seamless integration between Google Sheets, Google Drive, Google Veo3, and YouTube.\n\n\n\n\n", "height": 280, "width": 740, "color": 3}, "id": "262608c6-e905-4d16-bc52-22b67c1bc298", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"content": "## STEP 1 - <PERSON><PERSON><PERSON><PERSON> SHEET\nCreate a [Google Sheet like this](https://docs.google.com/spreadsheets/d/1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ/edit?usp=sharing).\n\nPlease insert:\n- in the \"PROMPT\" column the accurate description of the video you want to create\n- in the \"DURATION\" column the lenght of the video you want to create\n\nLeave the \"VIDEO\" column unfilled. It will be inserted by the system once the video has been created", "height": 200, "width": 740}, "id": "1ea1879e-1e8e-42f9-8b11-7f771cfca2f8", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 560], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "383d112e-2cc6-4dd4-8985-f09ce0bd1781", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED"}]}, "options": {}}, "id": "8c54b723-d0b9-4d89-8150-975306640a96", "name": "Completed?", "type": "n8n-nodes-base.if", "position": [1620, 1540], "typeVersion": 2.2}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "mode": "list", "value": "1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ/edit?usp=drivesdk", "cachedResultName": "Video Google Veo3"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11ebWJvwwXHgvQld9kxywKQUvIoBw6xMa0g0BuIqHDxE/edit#gid=0", "cachedResultName": "Foglio1"}, "columns": {"value": {"VIDEO": "={{ $('Get Url Video').item.json.video.url }}", "row_number": "={{ $('Get new video').item.json.row_number }}"}, "schema": [{"id": "PROMPT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DURATION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DURATION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VIDEO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "VIDEO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "YOUTUBE_URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "YOUTUBE_URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "1135e1f0-b605-4724-a8b3-7f16b9887ef7", "name": "Update result", "type": "n8n-nodes-base.googleSheets", "position": [1460, 1800], "typeVersion": 4.5}, {"parameters": {"assignments": {"assignments": [{"id": "c713d31f-9abd-496a-ac79-e8e2efe60aa0", "name": "prompt", "type": "string", "value": "={{ $json.PROMPT }}\n\nDuration of the video: {{ $json.DURATION }}"}]}, "options": {}}, "id": "8d0ac30d-5a92-487b-aaff-f761bd720293", "name": "Set data", "type": "n8n-nodes-base.set", "position": [820, 1540], "typeVersion": 3.4}, {"parameters": {"content": "## STEP 4 - <PERSON>IN FLOW\nStart the workflow manually or periodically by hooking the \"Schedule Trigger\" node. It is recommended to set it at 5 minute intervals.", "height": 100, "width": 740}, "id": "ae68690d-c154-4ec9-a5b8-ba95137d2cf4", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [380, 1220], "typeVersion": 1}, {"parameters": {"content": "## STEP 2 - GET API KEY (YOURAPIKEY)\nCreate an account [here](https://fal.ai/) and obtain API KEY.\nIn the node \"Create Image\" set \"Header Auth\" and set:\n- Name: \"Authorization\"\n- Value: \"Key YOURAPIKEY\"", "height": 140, "width": 740}, "id": "429ab164-e165-409c-adc7-8a80c25b9f7d", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [380, 800], "typeVersion": 1}, {"parameters": {"content": "Set API Key created in Step 2", "height": 200, "width": 180}, "id": "868339d3-e3f0-473b-9f1f-c7c4fe9c9898", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1000, 1480], "typeVersion": 1}, {"parameters": {"documentId": {"__rl": true, "mode": "list", "value": "1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ/edit?usp=drivesdk", "cachedResultName": "Video Google Veo3"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ/edit#gid=0", "cachedResultName": "Foglio1"}, "filtersUI": {"values": [{"lookupColumn": "VIDEO"}]}, "options": {}}, "id": "44793285-6bb8-471a-a632-e0fe33d2d047", "name": "Get new video", "type": "n8n-nodes-base.googleSheets", "position": [600, 1540], "typeVersion": 4.5}, {"parameters": {"method": "POST", "url": "https://queue.fal.run/fal-ai/veo3", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n     \"prompt\": \"{{$json.prompt}}\"\n}", "options": {}}, "id": "7271ea9a-c4f5-4091-8f0f-ef40bb1d94af", "name": "Create Video", "type": "n8n-nodes-base.httpRequest", "position": [1040, 1540], "typeVersion": 4.2}, {"parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "id": "f0cbd048-011e-47ee-8840-af630adf6f85", "name": "Get Url Video", "type": "n8n-nodes-base.httpRequest", "position": [380, 1920], "typeVersion": 4.2}, {"parameters": {"url": "={{ $('Get Url Video').item.json.video.url }}", "options": {}}, "id": "f48cef5b-0609-417b-b4ec-21614397cb07", "name": "Get File Video", "type": "n8n-nodes-base.httpRequest", "position": [1000, 1920], "typeVersion": 4.2}, {"parameters": {"name": "={{ $now.format('yyyyLLddHHmmss') }}-{{ $('Get Url Video').item.json.video.file_name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "1aHRwLWyrqfzoVC8HoB-YMrBvQ4tLC-NZ", "cachedResultUrl": "https://drive.google.com/drive/folders/1aHRwLWyrqfzoVC8HoB-YMrBvQ4tLC-NZ", "cachedResultName": "Fal.run"}, "options": {}}, "id": "2cde22e1-1b71-4672-80dc-4992c4bdfe5d", "name": "Upload Video", "type": "n8n-nodes-base.googleDrive", "position": [1280, 1800], "typeVersion": 3}, {"parameters": {"method": "POST", "url": "https://api.upload-post.com/api/upload", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Generate title').item.json.message.content }}"}, {"name": "user", "value": "YOUR_USERNAME"}, {"name": "platform[]", "value": "youtube"}, {"parameterType": "formBinaryData", "name": "video", "inputDataFieldName": "data"}]}, "options": {}}, "id": "bf77f692-8a47-4f49-883b-ba045cb39f3b", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [1280, 2020], "typeVersion": 4.2}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Input: {{ $('Get new video').item.json.PROMPT }}"}, {"content": "You are a YouTube SEO expert specialized in creating engaging and optimized titles.\n\nYour task is to generate an effective title for a YouTube video based on the user's video description.\n\nGUIDELINES:\n- Maximum 60 characters to avoid truncation\n- Use relevant keywords for SEO\n- Make the title catchy and clickable\n- Avoid excessive or misleading clickbait\n- Consider the target audience of the content\n- Use numbers, questions, or power words when appropriate\n- IMPORTANT: Generate the title in the same language as the input description\n\nOUTPUT FORMAT:\nProvide only the title, without additional explanations.\n\nEXAMPLE:\nInput: \"Tutorial video on how to cook perfect pasta carbonara\"\nOutput: \"PERFECT Carbonara in 10 Minutes - Chef's Secrets\"", "role": "system"}]}, "options": {}}, "id": "0fab9370-c079-40c8-9e87-e9b319e0bee7", "name": "Generate title", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [620, 1920], "typeVersion": 1.8}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "mode": "list", "value": "1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1pcoY9N_vQp44NtSRR5eskkL5Qd0N0BGq7Jh_4m-7VEQ/edit?usp=drivesdk", "cachedResultName": "Video Google Veo3"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/11ebWJvwwXHgvQld9kxywKQUvIoBw6xMa0g0BuIqHDxE/edit#gid=0", "cachedResultName": "Foglio1"}, "columns": {"value": {"VIDEO": "=", "row_number": "={{ $('Get new video').item.json.row_number }}", "YOUTUBE_URL": "https://youtu.be/{{ $json.results.youtube.video_id }}"}, "schema": [{"id": "PROMPT", "type": "string", "display": true, "removed": false, "required": false, "displayName": "PROMPT", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "DURATION", "type": "string", "display": true, "removed": false, "required": false, "displayName": "DURATION", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "VIDEO", "type": "string", "display": true, "removed": false, "required": false, "displayName": "VIDEO", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "YOUTUBE_URL", "type": "string", "display": true, "removed": false, "required": false, "displayName": "YOUTUBE_URL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "cf9a58f0-0523-4229-ac2e-42f0f71d5306", "name": "Update Youtube URL", "type": "n8n-nodes-base.googleSheets", "position": [1460, 2020], "typeVersion": 4.5}, {"parameters": {"content": "Set YOUR_USERNAME in Step 3", "height": 200, "width": 180}, "id": "2f97346c-78df-41fe-b15d-c2af7e9ee680", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1260, 1960], "typeVersion": 1}, {"parameters": {"content": "## STEP 3 - Upload video on Youtube\n- Find your API key in your [Upload-Post Manage Api Keys](https://app.upload-post.com/) 10 FREE uploads per month\n- Set the the \"Auth Header\":\n-- Name: Authorization\n-- Value: Apikey YOUR_API_KEY_HERE\n- Create profiles to manage your social media accounts. The \"Profile\" you choose will be used in the field YOUR_USRNAME (eg. test1 or test2).  ", "height": 200, "width": 740}, "id": "0d6517cb-d2a3-4afe-b163-25311faed711", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [380, 980], "typeVersion": 1}], "pinData": {}, "connections": {"Set data": {"main": [[{"node": "Create Video", "type": "main", "index": 0}]]}, "Completed?": {"main": [[{"node": "Get Url Video", "type": "main", "index": 0}], [{"node": "Wait 60 sec.", "type": "main", "index": 0}]]}, "Get status": {"main": [[{"node": "Completed?", "type": "main", "index": 0}]]}, "Create Video": {"main": [[{"node": "Wait 60 sec.", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Update Youtube URL", "type": "main", "index": 0}]]}, "Upload Video": {"main": [[{"node": "Update result", "type": "main", "index": 0}]]}, "Wait 60 sec.": {"main": [[{"node": "Get status", "type": "main", "index": 0}]]}, "Get Url Video": {"main": [[{"node": "Generate title", "type": "main", "index": 0}]]}, "Get new video": {"main": [[{"node": "Set data", "type": "main", "index": 0}]]}, "Update result": {"main": [[]]}, "Generate title": {"main": [[{"node": "Get File Video", "type": "main", "index": 0}]]}, "Get File Video": {"main": [[{"node": "Upload Video", "type": "main", "index": 0}, {"node": "HTTP Request", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get new video", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "4846", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}