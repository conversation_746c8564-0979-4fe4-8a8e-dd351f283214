{"name": "Bible Verse Generator Working Copy", "nodes": [{"parameters": {"method": "POST", "url": "https://apibox.erweima.ai/api/v1/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer "}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "A calm and relaxing lo-fi track with soft melodies"}, {"name": "style", "value": "=lo-fi"}, {"name": "title", "value": "Peaceful Piano Meditation"}, {"name": "customMode", "value": true}, {"name": "instrumental", "value": true}, {"name": "model", "value": "V3_5"}, {"name": "negativeTags", "value": "Heavy Metal, Upbeat Drums"}, {"name": "callBackUrl", "value": "https://api.example.com/callback"}]}, "options": {"redirect": {"redirect": {}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-420, 680], "id": "6532411f-c70d-4e80-add9-f3276016f90b", "name": "Generate Music", "disabled": true}, {"parameters": {"url": "=https://apibox.erweima.ai/api/v1/generate/record-info?taskId=ffb17f558ff5cc2b61f7fafc8fb7846a", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer "}]}, "options": {"redirect": {"redirect": {}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-40, 680], "id": "e8b398ef-77cb-41c7-902e-1c0891ef7bc1", "name": "Get Music Details"}, {"parameters": {"resource": "image", "prompt": "=Generate an image of  {{ $json.data.response.sunoData[0].title }} in 16:9 layout to be used for my youtube long form video", "options": {"quality": "hd", "size": "1024x1024", "style": "natural"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [380, 160], "id": "16586ce2-3072-4dfa-aeeb-3d3272822a5c", "name": "Generate Image", "credentials": {"openAiApi": {"id": "Q2DrwmpfAnCe9zKS", "name": "OpenAi account"}}}, {"parameters": {"url": "={{ $('Get Music Details').item.json.data.response.sunoData[0].audioUrl }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "musicfile"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 660], "id": "8b7e019b-3928-4f7d-8f62-78347496e240", "name": "Download Music"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5750aad8-9f81-49e7-a15a-c7a255c25ac8", "leftValue": "={{ $json.data.status }}", "rightValue": "SUCCESS", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [660, 680], "id": "800af777-f519-48ed-abea-13f5e71d53be", "name": "If"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [280, 680], "id": "dabb12e8-9510-403b-842c-3af304cf3371", "name": "Wait1", "webhookId": "7e0b4816-9c73-477b-ae33-96d2d2dcba2f"}, {"parameters": {"content": "Generate Image and write to file", "height": 280, "width": 460}, "type": "n8n-nodes-base.stickyNote", "position": [360, 80], "typeVersion": 1, "id": "d759b13e-840a-4fea-84bc-001ec6b20245", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "Generate Music", "height": 420, "width": 1040, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-480, 560], "typeVersion": 1, "id": "882726a2-a63e-433a-8848-7c84917e0426", "name": "Sticky Note1"}, {"parameters": {"content": "Download Music and write to file", "height": 280, "width": 700}, "type": "n8n-nodes-base.stickyNote", "position": [940, 580], "typeVersion": 1, "id": "748f8e5b-1cbf-49ad-9a06-92611fea2bc7", "name": "Sticky Note2"}, {"parameters": {"content": "Combine audio Files", "height": 320, "width": 1060, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [2020, -280], "typeVersion": 1, "id": "83888847-3afa-4b63-989d-6fc6d8bb9c75", "name": "Sticky Note3"}, {"parameters": {"content": "Combine Music and Image to create video", "height": 400, "width": 540, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [4000, 80], "typeVersion": 1, "id": "2aef32bb-e28f-4783-84b8-f1a0afc85b11", "name": "Sticky Note4"}, {"parameters": {"content": "Transcribe Sppech And Convert To SRT Format", "height": 260, "width": 1500, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [1740, -680], "typeVersion": 1, "id": "9e3476e4-08b4-4ce3-8436-9998a83b978c", "name": "Sticky Note5"}, {"parameters": {"operation": "write", "fileName": "/tmp/musicfile.mp3", "dataPropertyName": "musicfile", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1460, 660], "id": "d804e20c-2be9-48f5-9f00-0131d11ee14c", "name": "Write Files from Disk"}, {"parameters": {"operation": "write", "fileName": "/tmp/image.jpg", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [640, 160], "id": "94ed7ded-f687-4bd5-9204-84b137183903", "name": "Write Files from Disk1"}, {"parameters": {"command": "ffmpeg -y -i /tmp/musicfile.mp3 -ss 0 -t 10 -c copy /tmp/trimmedaudio.mp3\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1780, 660], "id": "3941e22f-1859-405f-8beb-1ba3af9aa236", "name": "Trim Audio File"}, {"parameters": {"command": "=ffmpeg -y -loop 1 -framerate 2 -i /tmp/image.jpg \\\n-i /tmp/merged_audio.mp3 \\\n-vf \"subtitles='/tmp/captions.srt':force_style='Alignment=8'\" \\\n-c:v libx264 -preset veryfast -tune stillimage -pix_fmt yuv420p \\\n-c:a aac -b:a 192k -shortest /tmp/testoutput.mp4"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [4180, 160], "id": "01fa86b9-21eb-43bc-bf2c-159ae1d54dd4", "name": "Create Video File"}, {"parameters": {"fileSelector": "/tmp/testoutput.mp4", "options": {"fileExtension": ".mp4"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [4920, 160], "id": "3c7590bf-437f-4877-8b10-e0348b9d4f96", "name": "Read Output Video"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?uploadType=multipart", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [5580, 160], "id": "444aa409-8d9f-41d9-b9c0-0c323ecceeb0", "name": "Post To Youtube", "credentials": {"youTubeOAuth2Api": {"id": "7mzDEdmvxUkX3gQy", "name": "YouTube account"}}}, {"parameters": {"jsCode": "// Get all input items (rows from Google Sheets)\nconst allRows = $input.all();\n\n// Get the last row\nconst lastRow = allRows[allRows.length - 1];\n\n// (Optional) Add a custom field to the last row\nlastRow.json.myNewField = 1;\n\n// Return only the last row\nreturn [lastRow];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, -20], "id": "3aca7972-da72-42ae-a4d3-b04771be9211", "name": "Code"}, {"parameters": {"promptType": "define", "text": "={{ $json.Topic }}", "options": {"systemMessage": "You are a helpful assistant that generates one bible verse based on this input, no emojis or non-english characters in text. I only wan to see the Bible Verse and the text in the output. No leading sentences, symbols and remove asterisks"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [0, -620], "id": "59f9b697-1ecd-47f6-b54a-aeede7925602", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"responseFormat": "text"}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [20, -320], "id": "46a75b64-fd7d-4028-a6a7-363552ca0d7d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "Q2DrwmpfAnCe9zKS", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.Topic }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [120, -320], "id": "9131d382-bcb2-4fc2-a45a-fac8c1f9af54", "name": "Window Buffer Memory"}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/text-to-speech/JBFqnCBsd6RMkjVDRZzb", "sendQuery": true, "queryParameters": {"parameters": [{"name": "output_format", "value": "mp3_22050_32"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": ""}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.output }}"}, {"name": "model_id", "value": "eleven_multilingual_v2"}]}, "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "SpeechFileNew"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [820, -620], "id": "fc757b83-9583-48ad-895d-032e963768a2", "name": "HTTP Request"}, {"parameters": {"operation": "write", "fileName": "/tmp/SpeechFileNew.mpga", "dataPropertyName": "SpeechFileNew", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1200, -620], "id": "e7222b5e-6c6f-43b4-8a01-6f6755d5e656", "name": "Read/Write Files from Disk"}, {"parameters": {"content": "Speech To Text", "height": 520, "width": 1600, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-140, -700], "typeVersion": 1, "id": "c1622e0f-dc67-405a-9b41-5c755ea8da7b", "name": "Sticky Note6"}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/speech-to-text", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": ""}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "model_id", "value": "scribe_v1"}, {"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}, {"name": "additional_formats", "value": "[{\"format\":\"srt\"}]"}, {"name": "timestamps_granularity", "value": "word"}, {"name": "diarize", "value": "true"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2180, -620], "id": "daf2df2f-0952-4796-8ad9-e00160f8b365", "name": "HTTP Request1"}, {"parameters": {"fileSelector": "/tmp/SpeechFileNew.mpga", "options": {"fileExtension": ".mpga"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1900, -620], "id": "c6a3a2c5-90d4-4616-a07a-8baa140b6b26", "name": "Read/Write Files from Disk1"}, {"parameters": {"assignments": {"assignments": [{"id": "f7cf021e-8a55-4348-969f-6de58eb936ee", "name": "captions", "value": "={{ $json.additional_formats[0] }}", "type": "object"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2460, -620], "id": "66fb6ea9-010c-467c-b76a-3da1b5c4578b", "name": "<PERSON>"}, {"parameters": {"jsCode": "const srtText = $input.first().json.captions.content;\n\nitems[0].binary = {\n  data: {\n    data: Buffer.from(srtText, 'utf8').toString('base64'),\n    mimeType: 'text/srt',\n    fileName: 'captions.srt'\n  }\n};\n\nreturn items;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2700, -620], "id": "266325e9-0565-49f8-b97b-8e81f40d6f7d", "name": "Code2"}, {"parameters": {"operation": "write", "fileName": "/tmp/captions.srt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2940, -620], "id": "30e31067-2f60-4075-b611-178d71b2e3e2", "name": "Read/Write Files from Disk5"}, {"parameters": {"command": "ffmpeg -y -i /tmp/SpeechFileNew.mpga -i /tmp/trimmedaudio.mp3 \\\n-filter_complex \"[0:a][1:a]amix=inputs=2:duration=longest:dropout_transition=2\" \\\n-c:a libmp3lame -b:a 192k /tmp/merged_audio.mp3\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2100, -160], "id": "530a7887-551c-4626-8681-3a06d2913f0e", "name": "Execute Command"}, {"parameters": {"fileSelector": "/tmp/merged_audio.mp3", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2840, -160], "id": "5d778d00-a8e9-44c9-a1af-b491587c2a50", "name": "Read/Write Files from Disk6"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1BzMzCIF-EnkU9Dg55c3EMpd92ALqgdZl3UxXPT61MYk", "mode": "list", "cachedResultName": "Bible Topics", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BzMzCIF-EnkU9Dg55c3EMpd92ALqgdZl3UxXPT61MYk/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BzMzCIF-EnkU9Dg55c3EMpd92ALqgdZl3UxXPT61MYk/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [-1220, 400], "id": "e3b6e778-8728-4d2b-8266-466096d9b891", "name": "Google Sheets Trigger", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "7zDv04e2T1uuaFcs", "name": "Google Sheets Trigger account"}}}, {"parameters": {"content": "Post On Youtube", "height": 500, "width": 520}, "type": "n8n-nodes-base.stickyNote", "position": [5400, -40], "typeVersion": 1, "id": "3416cd5c-d771-4758-9bd5-8a7fc745a2a6", "name": "Sticky Note7"}], "pinData": {}, "connections": {"Generate Music": {"main": [[{"node": "Get Music Details", "type": "main", "index": 0}]]}, "Get Music Details": {"main": [[{"node": "Wait1", "type": "main", "index": 0}, {"node": "Generate Image", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Write Files from Disk1", "type": "main", "index": 0}]]}, "Download Music": {"main": [[{"node": "Write Files from Disk", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Download Music", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Write Files from Disk": {"main": [[{"node": "Trim Audio File", "type": "main", "index": 0}]]}, "Write Files from Disk1": {"main": [[{"node": "Create Video File", "type": "main", "index": 0}]]}, "Trim Audio File": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Create Video File": {"main": [[{"node": "Read Output Video", "type": "main", "index": 0}]]}, "Read Output Video": {"main": [[{"node": "Post To Youtube", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}, {"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Code2", "type": "main", "index": 0}]]}, "Code2": {"main": [[{"node": "Read/Write Files from Disk5", "type": "main", "index": 0}]]}, "Read/Write Files from Disk5": {"main": [[{"node": "Create Video File", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Read/Write Files from Disk6", "type": "main", "index": 0}]]}, "Read/Write Files from Disk6": {"main": [[{"node": "Create Video File", "type": "main", "index": 0}]]}, "Google Sheets Trigger": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Generate Music", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v0", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "a57421dd-b5d4-41b3-b800-e80ffeaca1b2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5e042ebff1f55bf5f6b5313c7ce55787764df5f56387a144249e5b979f6a19cc"}, "id": "cLEi4G9rO4vll4av", "tags": []}