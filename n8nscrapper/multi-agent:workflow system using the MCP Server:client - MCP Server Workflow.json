{"meta": {"instanceId": "d9e1cda642de4f8909345a14409b4a3b496085da88cc8ef314554b8bf411da6d"}, "nodes": [{"parameters": {"path": "MCPServer"}, "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 1.1, "position": [-560, -100], "id": "mcp-trigger", "name": "MCP Server Trigger", "webhookId": "4e0ffdcf-bcfa-4f17-b356-e98364ad3b4e"}, {"parameters": {"jsCode": "console.log('MCP Server received:', JSON.stringify($json, null, 2));\nlet query = '';\nif ($json.arguments && $json.arguments.query) {\n  query = $json.arguments.query;\n} else if ($json.arguments && $json.arguments.input) {\n  query = $json.arguments.input;\n} else if ($json.query) {\n  query = $json.query;\n} else if ($json.input) {\n  query = $json.input;\n} else if (typeof $json === 'string') {\n  query = $json;\n}\nconsole.log('Extracted query:', query);\nreturn [{\n  json: {\n    RAG: query,\n    debug_received: $json,\n    debug_query: query\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, -100], "id": "extract-query", "name": "Extract Query"}, {"parameters": {"description": "Agent RAG pour recherche documentaire", "workflowId": {"__rl": true, "mode": "id", "value": "REPLACE_WITH_RAG_WORKFLOW_ID"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"RAG": "={{$json.RAG}}"}, "schema": [{"id": "RAG", "displayName": "RAG", "required": true, "type": "string"}]}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 2, "position": [-80, -100], "id": "call-rag", "name": "Call RAG Workflow", "onError": "continueErrorOutput"}, {"parameters": {"jsCode": "const response = $json;\nconsole.log('RAG workflow response:', JSON.stringify(response, null, 2));\nlet answer = '';\nif (response.output && typeof response.output === 'string') {\n  try {\n    const parsed = JSON.parse(response.output);\n    answer = parsed.RAG || parsed.Reasoning || parsed.Coding || parsed.Conversation || response.output;\n  } catch (e) {\n    answer = response.output;\n  }\n} else if (response.RAG) {\n  answer = response.RAG;\n} else if (response.result) {\n  answer = response.result;\n} else {\n  answer = JSON.stringify(response);\n}\nreturn [{\n  json: {\n    content: [{\n      type: \"text\",\n      text: answer\n    }]\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [160, -100], "id": "format-response", "name": "Format MCP Response"}], "connections": {"MCP Server Trigger": {"main": [[{"node": "Extract Query", "type": "main", "index": 0}]]}, "Extract Query": {"main": [[{"node": "Call RAG Workflow", "type": "main", "index": 0}]]}, "Call RAG Workflow": {"main": [[{"node": "Format MCP Response", "type": "main", "index": 0}]]}}, "pinData": {}}