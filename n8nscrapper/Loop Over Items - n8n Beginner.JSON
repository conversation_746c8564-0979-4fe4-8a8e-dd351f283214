{"id": "CDVBZRDH5gNUzIml", "meta": {"instanceId": "ab60b809a7fa5daa22fa7edffc14e989a6e59d4063ec838ad62f2655caafd963", "templateCredsSetupCompleted": true}, "name": "N8N for Beginners: Looping over Items", "tags": [], "nodes": [{"id": "500304a1-5164-4907-b0a7-e514e455e3dc", "name": "Result1", "type": "n8n-nodes-base.noOp", "notes": "Look Here to see resulting output", "position": [880, -280], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "32808ad1-1a35-497f-b1d3-c935a924c541", "name": "Result2", "type": "n8n-nodes-base.noOp", "notes": "Look here to see resulting output", "position": [880, 40], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "f3c9fa5d-a4c7-4cf0-beb5-774d8483974f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [800, -340], "parameters": {"color": 3, "width": 320, "height": 240, "content": "\n### Result1 shows that the array of strings is seen as one item by Loop1"}, "typeVersion": 1}, {"id": "0bb96765-60ce-4618-9b39-7cd203a3cb48", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [800, -60], "parameters": {"color": 3, "width": 320, "height": 260, "content": "### Result2 shows that the Loop2 sees 5 items after the array of strings is split into separate objects"}, "typeVersion": 1}, {"id": "b71fcc86-b0cd-4241-b0d6-21abe4c6b68d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-1680, -1100], "parameters": {"width": 1200, "height": 2480, "content": "# N8N for Beginners: Looping Over Items\n\n## Description\n\nThis workflow is designed for **n8n beginners** to understand how n8n handles **looping (iteration)** over multiple items. It highlights two key behaviors:\n\n- **Built-In Looping:** By default, most n8n nodes iterate over each item in an input array.\n- **Explicit Looping:** The **Loop Over Items** node allows controlled iteration, enabling **custom batch processing** and multi-step workflows.\n\nThis workflow demonstrates the difference between processing an **unsplit array of strings (single item)** vs. **a split array (multiple items)**.\n\n---\n\n## Setup\n\n### 1. Input Data\n\nTo begin, **paste the following JSON** into the **Manual Trigger** node:\n\n```json\n{\n  \"urls\": [\n    \"https://www.reddit.com\",\n    \"https://www.n8n.io/\",\n    \"https://n8n.io/\",\n    \"https://supabase.com/\",\n    \"https://duckduckgo.com/\"\n  ]\n}\n```\n\nðŸ“Œ **Steps to Paste Data:**\n- **Double-click** the \"Manual Trigger\" node.\n- Click **\"Edit Output\"** (top-right corner).\n- Paste the JSON and **Save**.\n- The node **turns purple**, indicating that test data is pinned. \n\n---\n\n## Explanation of the n8n Nodes in the Workflow\n\n### Manual Trigger  \nThis node starts the workflow manually and sends test data.  \n**Documentation:** [Manual Trigger Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.manualtrigger/)\n\n### Split Out (Split Array of Strings into Array of Objects)  \nExtracts the `urls` array and splits it so that each URL becomes a separate JSON object.  \n**Documentation:** [Split Out Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitout/)\n\n### Loop Over Items (Loop Over Items 1)  \nDemonstrates how an **unsplit** array is treated as one item, processing it as a single unit.  \n**Documentation:** [Loop Over Items Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches/)\n\n### Loop Over Items (Loop Over Items 2)  \nProcesses the **split array** one item at a time. This demonstrates individual iteration and controlled looping.  \n**Documentation:** [Loop Over Items Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches/)\n\n### Wait Node  \nIntroduces a **1-second delay** per iteration to demonstrate sequential execution. This can be removed for faster performance.  \n**Documentation:** [Wait Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.wait/)\n\n### Code Nodes (Add param1 to outputX)  \nEach Code node adds a constant field (`param1`) to the data, ensuring that every item gets enriched with the same parameter.  \n**Documentation:** [Code Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/)\n\n### NoOp Nodes (Result1, Result2, Result3, Result4, Result5)\nThese nodes display the output at different stages of the workflow for inspection.\n**Documentation:** [NoOp Node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.noop/)\n\n\n\n---\n\n## Execution Details\n\n### 1. How the Workflow Runs\n\n- **Manual Trigger starts execution** with the pasted JSON data.\n- The workflow follows **two paths**:\n\n#### Unsplit Array Path â†’ Loop Over Items 1  \n- The original JSON is processed **as a single item**.  \n- **Result1 & Result5:** Show that the array was **not split** before processing.\n\n#### Split Array Path â†’ Split Out â†’ Loop Over Items 2  \n- The **Split Out** node converts the `urls` array into separate objects.\n- The **Loop Over Items 2** node processes each URL **one by one**.\n- A **Wait node** (1-second delay) demonstrates **controlled execution**.\n- **Code nodes** modify the JSON, adding the field (`param1`).\n- **Result2, Result3, Result4:** Display the final processed output.\n\n### 2. What You Will See\n\n- **Result1 & Result5:** The entire array is processed **as one item** before splitting.\n- **Result2, Result3, Result4:** Each URL is processed **individually** after being split.\n- **Wait Node:** Adds a **1-second delay per item** in **Loop Over Items 2**.\n\n---\n\n## Notes\n\n- Sticky notes are included in the workflow **for easy reference**.\n- The **Wait node** is **optional**â€”remove it for faster execution.\n- This template is structured for **beginners** but serves as a **building block** for more advanced automations.\n\n---\n\n"}, "typeVersion": 1}, {"id": "53e7d23a-2a4f-4ada-aeb5-de128acf8625", "name": "Split Array of Strings into Array of Objects", "type": "n8n-nodes-base.splitOut", "position": [-20, 380], "parameters": {"options": {"destinationFieldName": "url"}, "fieldToSplitOut": "urls"}, "typeVersion": 1}, {"id": "4910fec6-d600-43be-97b2-5050039db416", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [800, 700], "parameters": {"color": 3, "width": 320, "height": 260, "content": "### Result4 shows that we can turn off the looping feature by setting the looping behavior to \"Run Once For All Items\""}, "typeVersion": 1}, {"id": "f0e68b30-5341-4672-8d8e-7a24d894eb03", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [800, 420], "parameters": {"color": 3, "width": 320, "height": 240, "content": "### Result3 shows that looping over items is built in to n8n nodes"}, "typeVersion": 1}, {"id": "70a987b9-6a4a-49c3-a87c-0dd9cc1879ed", "name": "Result3", "type": "n8n-nodes-base.noOp", "notes": "Look here to see resulting output", "position": [900, 500], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "9de5fac9-e987-4fb1-8f7f-b59af2985e5c", "name": "Result4", "type": "n8n-nodes-base.noOp", "notes": "Look here to see resulting output", "position": [900, 780], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "d5fee847-7758-44ac-a270-6334dd81bb97", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [800, -640], "parameters": {"color": 3, "width": 320, "height": 260, "content": "### Result5 shows that the array of strings is seen as one item by Code5. So the behavior is the same as Loop1"}, "typeVersion": 1}, {"id": "9a134620-4067-4c7a-8b0f-30e19c93a66c", "name": "Result5", "type": "n8n-nodes-base.noOp", "notes": "Look here to see resulting output", "position": [880, -540], "parameters": {}, "notesInFlow": true, "typeVersion": 1}, {"id": "c273cd55-708d-4f86-81f2-3bbe991f59f8", "name": "Paste JSON into this node", "type": "n8n-nodes-base.manualTrigger", "position": [-400, -540], "parameters": {}, "typeVersion": 1}, {"id": "8f64b987-05c5-43a9-89c0-201499df0aa6", "name": "Add param1 to output5", "type": "n8n-nodes-base.code", "position": [320, -540], "parameters": {"jsCode": "\n  $json.param1 =  'add_me_to_all_items_and_name_me_param1';\n\n\nreturn $json"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "4920b4e4-b511-4059-b38a-623d14b6df25", "name": "Add param1 to output1", "type": "n8n-nodes-base.code", "position": [500, -180], "parameters": {"mode": "runOnceForEachItem", "jsCode": "\n  $json.param1 = \"add_me_to_all_items_and_name_me_param1\"\n\nreturn $json"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "28ce5239-6437-4b7a-9395-3beb162fded6", "name": "Loop over Items 2", "type": "n8n-nodes-base.splitInBatches", "position": [320, 60], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "628edc5e-1b7f-4f50-9c29-8990bd4a5cc0", "name": "Loop over Items 1", "type": "n8n-nodes-base.splitInBatches", "position": [320, -260], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "ffccda97-066d-4c13-b4eb-a13ff849b90a", "name": "Add param1 to output2", "type": "n8n-nodes-base.code", "position": [680, 220], "parameters": {"mode": "runOnceForEachItem", "jsCode": "\n  $json.param1 =   \"add_me_to_all_items_and_name_me_param1\"\n\n\nreturn $json"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "6fb44840-6365-46db-9c50-eecd420f274f", "name": "Add param1 to output3", "type": "n8n-nodes-base.code", "position": [320, 500], "parameters": {"mode": "runOnceForEachItem", "jsCode": "\n  $json.param1 =   \"add_me_to_all_items_and_name_me_param1\"\n\n\nreturn $json"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "51b8d766-25ed-407f-a956-693b93bda95f", "name": "Add param1 to output4", "type": "n8n-nodes-base.code", "position": [320, 780], "parameters": {"jsCode": "\n  $json.param1 =   \"add_me_to_all_items_and_name_me_param1\"\n\n\nreturn $json"}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "f8edde34-cf47-42e2-bbd3-552df2509451", "name": "Wait one second(just for show)", "type": "n8n-nodes-base.wait", "position": [480, 180], "webhookId": "61aef80b-4e9d-43ae-a083-cd87b4fdec11", "parameters": {"amount": 1}, "typeVersion": 1.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "e1eff37a-1ffd-4691-9560-eada5baab88d", "connections": {"Result1": {"main": [[]]}, "Result2": {"main": [[]]}, "Loop over Items 1": {"main": [[{"node": "Result1", "type": "main", "index": 0}], [{"node": "Add param1 to output1", "type": "main", "index": 0}]]}, "Loop over Items 2": {"main": [[{"node": "Result2", "type": "main", "index": 0}], [{"node": "Wait one second(just for show)", "type": "main", "index": 0}]]}, "Add param1 to output1": {"main": [[{"node": "Loop over Items 1", "type": "main", "index": 0}]]}, "Add param1 to output2": {"main": [[{"node": "Loop over Items 2", "type": "main", "index": 0}]]}, "Add param1 to output3": {"main": [[{"node": "Result3", "type": "main", "index": 0}]]}, "Add param1 to output4": {"main": [[{"node": "Result4", "type": "main", "index": 0}]]}, "Add param1 to output5": {"main": [[{"node": "Result5", "type": "main", "index": 0}]]}, "Paste JSON into this node": {"main": [[{"node": "Split Array of Strings into Array of Objects", "type": "main", "index": 0}, {"node": "Add param1 to output5", "type": "main", "index": 0}, {"node": "Loop over Items 1", "type": "main", "index": 0}]]}, "Wait one second(just for show)": {"main": [[{"node": "Add param1 to output2", "type": "main", "index": 0}]]}, "Split Array of Strings into Array of Objects": {"main": [[{"node": "Loop over Items 2", "type": "main", "index": 0}, {"node": "Add param1 to output4", "type": "main", "index": 0}, {"node": "Add param1 to output3", "type": "main", "index": 0}]]}}}