{"name": "My workflow", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {"q": ""}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-900, 1040], "id": "578e32da-4bd4-48b5-ba74-59ff45290636", "name": "Gmail Trigger1", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-740, 1240], "id": "bac5eb02-d358-4b90-addb-d0226949833b", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIALS_ID", "name": "OpenAi account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [500, 180], "id": "753ce70f-cf4a-4282-b269-71cc0f6d4d8a", "name": "Google Gemini Chat Model3", "credentials": {"googlePalmApi": {"id": "YOUR_GOOGLE_GEMINI_CREDENTIALS_ID", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [520, 900], "id": "703857ef-59c4-4838-bff1-2b589866d7d1", "name": "Google Gemini Chat Model4", "credentials": {"googlePalmApi": {"id": "YOUR_GOOGLE_GEMINI_CREDENTIALS_ID", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [540, 1660], "id": "cca310a8-d151-40f1-8432-9436fc054062", "name": "Google Gemini Chat Model5", "credentials": {"googlePalmApi": {"id": "YOUR_GOOGLE_GEMINI_CREDENTIALS_ID", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"resource": "draft", "subject": "={{ $json.output.properties.subject.description }}", "message": "={{ $json.output.properties.body.description }}", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1060, 1460], "id": "50fca8dc-57cc-4a90-9b5b-c7927d024871", "name": "Gmail4", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"promptType": "define", "text": "=Subject: {{ $('Gmail Trigger1').item.json.headers.subject }}\nEmail body: {{ $('Gmail Trigger1').item.json.text }}", "hasOutputParser": true, "options": {"systemMessage": "You are an intelligent email summarization assistant. Your task is to analyze emails and create concise, well-structured summaries that capture the essential information.\n\nInstructions:\n- Extract the key points, main topics, and important details from the email\n- Identify the sender, primary purpose, and any action items or deadlines\n- Preserve important names, dates, numbers, and specific details\n- Create a clear, readable summary that allows someone to quickly understand the email's content\n- Use bullet points or numbered lists when appropriate for better readability\n- Maintain a professional and neutral tone\n- Focus on facts and avoid speculation or interpretation\n\nFormat your response as a structured summary with relevant headings where appropriate."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [600, -20], "id": "719007e8-68d8-4641-9f1b-d01a948ca4ac", "name": "Summarize Agent"}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "labelIds": ["Label_9106524978846299669"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [320, 700], "id": "e60300bd-1676-436e-9ad3-a790d69274e5", "name": "Assign Personal label", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "labelIds": ["Label_3322761793655401836"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [320, -20], "id": "ef7307ed-7f04-4d1f-bec6-b3e1aa49a49d", "name": "Assign Work label", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"promptType": "define", "text": "=Subject: {{ $('Gmail Trigger1').item.json.headers.subject }}\nEmail body: {{ $('Gmail Trigger1').item.json.text }}", "hasOutputParser": true, "options": {"systemMessage": "You are a friendly and thoughtful email assistant that automatically responds to personal emails on behalf of the user. Your role is to acknowledge personal messages warmly while being helpful and maintaining appropriate boundaries.\n\n## Guidelines:\n\n**Tone & Style:**\n- Use a warm, friendly, and conversational tone\n- Match the formality level of the incoming message\n- Be genuine and personable without being overly casual\n- Keep responses concise but meaningful\n\n**Response Strategy:**\n- Always acknowledge the sender and their message\n- Express genuine appreciation for their communication\n- If it's an invitation, acknowledge it and indicate you'll get back to them soon\n- For personal updates, show interest and engagement\n- For congratulations/birthday wishes, express gratitude warmly\n\n**What to Include:**\n- Thank the sender for reaching out\n- Briefly acknowledge the main topic/purpose of their email\n- Indicate when they can expect a more detailed response if needed\n- Maintain the personal connection\n\n**What to Avoid:**\n- Making commitments or promises without user confirmation\n- Sharing personal information or details\n- Being overly formal with close friends/family\n- Giving definitive answers to questions requiring user input\n\n**Response Examples:**\n- For invitations: \"Thanks for thinking of me! This sounds great - let me check my schedule and get back to you soon.\"\n- For personal updates: \"So good to hear from you! Thanks for the update - I'd love to catch up properly soon.\"\n- For birthday wishes: \"Thank you so much for the birthday wishes! Really appreciate you thinking of me.\"\n\n**Format:**\nKeep responses to 2-3 sentences maximum. Always end on a positive, warm note that maintains the relationship while buying time for a proper personal response later.\n\nRemember: You are a temporary acknowledgment, not a replacement for genuine personal communication. The goal is to maintain relationships while the user prepares a more thoughtful personal response."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [600, 700], "id": "eb4cbf16-23ca-4f73-8586-4d88f14d7e01", "name": "Reply Agent"}, {"parameters": {"chatId": "=YOUR_CHAT_ID", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1020, -20], "id": "d8d16129-705a-462e-bc58-1dac39413984", "name": "Send Summary as Telegram Message", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"telegramApi": {"id": "YOUR_TELEGRAM_CREDENTIALS_ID", "name": "YOUR_TELEGRAM_BOT_NAME"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "message": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1040, 700], "id": "563603b7-211b-4277-8b15-d0d117c555f9", "name": "Reply to the Mail", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "labelIds": ["Label_1195998419257611102"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [320, 1460], "id": "efd64883-c96c-42e1-aa38-f81b3dc9ac5a", "name": "Assign Shopping Label", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"promptType": "define", "text": "=Subject: {{ $('Gmail Trigger1').item.json.headers.subject }}\nEmail body: {{ $('Gmail Trigger1').item.json.text }}", "hasOutputParser": true, "options": {"systemMessage": "You are an intelligent email assistant that automatically analyzes shopping and promotional emails and crafts draft replies requesting relevant additional information. You will receive the email content and autonomously determine what important details are missing or unclear.\n\n## Core Function:\nAnalyze the incoming shopping/promotional email and automatically generate a professional draft reply asking for specific details that would help make an informed purchasing decision.\n\n## Analysis Process:\n\n1. **Content Analysis:**\n   - Identify the main product/service being promoted\n   - Determine what information is already provided\n   - Detect what critical information is missing or vague\n   - Assess the type of offer (sale, new product, service, subscription, etc.)\n\n2. **Automatic Question Generation:**\n   Based on your analysis, automatically ask about missing details such as:\n   - **Pricing Details:** If price isn't clear, ask for specific costs, fees, taxes, shipping\n   - **Product Specs:** If features are vague, request detailed specifications\n   - **Availability:** If stock/timing unclear, ask about availability and delivery\n   - **Terms & Conditions:** If offer details are sparse, request full terms\n   - **Compatibility:** For tech products, ask about system requirements or compatibility\n   - **Support & Warranty:** Request information about customer service and guarantees\n   - **Comparison Options:** Ask about different models, sizes, or service tiers\n\n3. **Email Type Recognition:**\n   - **Flash Sales:** Focus on duration, stock levels, exclusions\n   - **New Product Launches:** Ask about features, availability, early access\n   - **Service Promotions:** Request implementation details, contract terms, trial options\n   - **Subscription Offers:** Inquire about billing cycles, cancellation policy, included features\n   - **Discount Codes:** Ask about expiration, restrictions, minimum purchase requirements\n\n## Response Generation:\n\n**Subject Line:** \"Re: [Original Subject] - Additional Information Request\"\n\n**Draft Structure:**\n\nDear [Sender Name/Team],\nThank you for your email about [product/service]. I'm interested in learning more and would appreciate clarification on a few points:\n[Automatically generated relevant questions based on email analysis]\nI look forward to your response so I can make an informed decision.\nBest regards,\n[Recipient Name]\n\n\n## Smart Question Examples:\n\n**For Vague Product Descriptions:**\n- \"Could you provide detailed specifications for [product]?\"\n- \"What are the exact dimensions and technical requirements?\"\n\n**For Unclear Pricing:**\n- \"What is the total cost including all fees and shipping?\"\n- \"Are there any additional charges or subscription fees?\"\n\n**For Limited Offers:**\n- \"What is the exact expiration date and time for this offer?\"\n- \"Are there quantity limits or geographic restrictions?\"\n\n**For Services:**\n- \"What does the implementation process involve?\"\n- \"What ongoing support is included?\"\n\n## Key Instructions:\n- AUTOMATICALLY determine what questions to ask - don't wait for user guidance\n- Generate 3-5 relevant questions maximum per email\n- Focus on the most important missing information that affects purchasing decisions\n- Adapt question style based on whether it's B2B or B2C communication\n- Be specific rather than generic in your questions\n- Always maintain professional, interested tone\n- If all important details are already provided, focus on next steps or additional options\n\n## Output Format:\nProvide only the complete draft email ready to send, with subject line and body text. Do not explain your reasoning or ask for user input - just generate the appropriate response automatically."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [600, 1460], "id": "a37a6509-311c-49ca-8784-77468083ed9d", "name": "Reply Agent1"}, {"parameters": {"jsonSchemaExample": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"subject\": {\n      \"type\": \"string\",\n      \"description\": \"The subject line for the draft email reply, should start with 'Re:' followed by the original subject and indicate it's a request for additional information\"\n    },\n    \"body\": {\n      \"type\": \"string\",\n      \"description\": \"The complete body text of the draft email reply, including greeting, main content with specific questions, and professional closing\"\n    }\n  },\n  \"required\": [\"subject\", \"body\"],\n  \"additionalProperties\": false\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [840, 1660], "id": "5d9b5e4b-59ed-4599-b0a5-22bc77bccf2e", "name": "Structured Output Parser"}, {"parameters": {"content": "# Gmail AI Agent Setup Instructions\n\nThis workflow transforms your Gmail inbox into an intelligent, automated assistant. It reads incoming emails, categorizes them using AI, and takes different actions based on the category: summarizing work emails, auto-replying to personal messages, drafting replies to promotions, and managing other emails.\n\n## 🚀 Workflow Overview\n\n- **Triggers on new emails** in your Gmail account.\n- **Classifies emails** into: Work, Personal, Shopping, Finance/Important, or Other.\n- **For Work emails**: Summarizes the content and sends the summary to you via Telegram.\n- **For Personal emails**: Sends a warm, auto-generated acknowledgment reply.\n- **For Shopping emails**: Drafts a reply asking for more information about the product/offer.\n- **For Spam emails**: Deletes them (use with caution!).\n- **For Other emails**: Assigns an \"Others\" label.\n\n## ⚙️ Node Configuration Summary\n\n### 🔴 Nodes Requiring Configuration:\nYou must configure the following nodes with your credentials for the workflow to function.\n\n1.  **Gmail Trigger1** & other **Gmail** nodes: Connect your Gmail account.\n2.  **OpenAI Chat Model1**: Add your OpenAI API credentials. Used for initial text classification.\n3.  **Google Gemini Chat Model3, 4, 5**: Add your Google Gemini API credentials. Used for summarization and reply generation.\n4.  **Send Summary as Telegram Message**: Configure with your Telegram Bot token and Chat ID.\n\n## 🧪 Testing the Workflow\n\n1.  **Activate the Workflow**: Toggle it to \"Active\".\n2.  **Send Test Emails**: Send emails with different subjects and content to the connected Gmail account to test each category.\n    - An email about a project update.\n    - A friendly message from a friend.\n    - A promotional email from a store.\n3.  **Monitor Execution**: Check the N8N executions list to see the workflow run.\n4.  **Verify Actions**:\n    - Check for a new message in your Telegram for the work email.\n    - Check the \"Sent\" folder in Gmail for the auto-reply to the personal email.\n    - Check the \"Drafts\" folder for the shopping email reply.\n    - See if other emails are labeled or deleted correctly.\n\n## 🎨 Personalize Your Workflow\n\nThis workflow is a powerful template, but it works best when tailored to **you**! I encourage you to customize it to match your own organizational system in Gmail.\n\n*   **Match Your Labels**: Go into the \"Assign Work label,\" \"Assign Personal label,\" and other label nodes and update them to use your existing Gmail labels. This ensures emails are filed exactly where you want them.\n*   **Tune the AI**: Don't just stop at labels! Open the \"Reply Agent\" or \"Summarize Agent\" nodes and edit the system prompts. You can change the tone of the replies, adjust the summarization style, or even give the AI a completely new personality.\n\nMake this workflow truly your own!\n\n## 🆘 Need Help?\nIf you encounter any issues or need assistance with:\n- Setting up the automation\n- Customizing the workflow for your specific needs\n- Troubleshooting technical problems\n- Adding new features or integrations\n- Building new automation workflows from scratch\n\nMessage me on [X @Victor_explore](https://x.com/Victor_explore) to hire the services of my agency.\n\n## 🙏 If You Found This Helpful:\n- ⭐️ **Give this automation a 5-star rating** and leave a review to help others discover it\n- 📤 **Share it** with colleagues and friends who might benefit\n- 🐦 **Follow me** on [X @Victor_explore](https://x.com/Victor_explore) for more automation workflows\n- 💬 **Join the conversation** - tag me when you share your results!\n\nYour feedback and success stories help improve this workflow for everyone! 🚀", "height": 3000, "width": 900, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2080, -400], "id": "7f788309-f6ef-45f3-b3d7-70a3e96b48b0", "name": "Main Instructions"}, {"parameters": {"content": "## Gmail Trigger & Text Classifier\n\n### What this section does:\n\nThis is the entry point of the workflow.\n\n1.  **Gmail Trigger**: It constantly watches for new, unread emails in the connected Gmail account.\n2.  **Text Classifier**: When a new email arrives, it's passed to this node. It uses an AI model (OpenAI) to analyze the subject and body and classify the email into one of the predefined categories:\n    - Work/Professional\n    - Personal/Family\n    - Shopping/Promotions\n    - Finance/Important\n\nThe output of this classifier determines which path the workflow will take next.\n", "height": 780, "width": 780, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1020, 620], "id": "ff8e70f8-52b6-44a7-91b6-67932ed8f52a", "name": "Trigger and Classifier"}, {"parameters": {"content": "## Work Email Path\n\n### What this section does:\n\nThis path handles emails classified as \"Work/Professional\".\n\n1.  **Assign Work label**: It first applies the \"Work\" label in Gmail for organization.\n2.  **Summarize Agent**: The email content is sent to a Gemini-powered agent that creates a concise summary of the key points.\n3.  **Send Summary as Telegram Message**: The generated summary is sent as a message to a specified Telegram chat, allowing you to get the gist of work emails without opening Gmail.\n", "height": 680, "width": 1680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-100, -320], "id": "4fbbeb2e-1e05-47c2-b823-d6aa630259f2", "name": "Work Flow"}, {"parameters": {"content": "## Personal Email Path\n\n### What this section does:\n\nThis path handles emails classified as \"Personal/Family\".\n\n1.  **Assign Personal label**: It applies the \"Personal\" label in Gmail.\n2.  **Reply Agent**: The email is passed to a Gemini-powered agent instructed to write a warm, friendly, but non-committal acknowledgment. This lets the sender know their message was received.\n3.  **Reply to the Mail**: The generated message is automatically sent as a reply to the original email.\n", "height": 680, "width": 1680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-100, 400], "id": "71357c2f-81d9-4f8e-a0e8-f060b2e6f578", "name": "Personal Flow"}, {"parameters": {"content": "## Shopping Email Path\n\n### What this section does:\n\nThis path handles \"Shopping/Promotions\" emails.\n\n1.  **Assign Shopping Label**: It applies the \"Shopping\" label in Gmail.\n2.  **Reply Agent1**: The email is analyzed by a Gemini-powered agent that identifies the product/promotion and generates intelligent questions to ask for more details (e.g., about pricing, availability, terms).\n3.  **Structured Output Parser**: This ensures the AI's output is in a clean format.\n4.  **Gmail4 (Create Draft)**: A draft email is created in Gmail containing the generated questions, ready for you to review, edit, and send.\n", "height": 680, "width": 1680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-100, 1120], "id": "70691732-65c7-47e5-b760-385d9be277b6", "name": "Shopping Flow"}, {"parameters": {"content": "## Finance & Other Emails\n\n### What this section does:\n\nThis path handles emails that are not work, personal, or shopping-related.\n\n- **Finance/Important**: The current workflow is set to **delete** these emails. Be cautious with this and adjust if you want to keep them.\n- **Other**: For emails that don't fit any other category, it simply applies an \"Others\" label in Gmail for later review.\n", "height": 680, "width": 1680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-100, 1840], "id": "b508cba2-926d-4a8a-a2db-673be0328ba6", "name": "Other Flows"}, {"parameters": {"inputText": "=Subject: {{ $json.subject }}\nEmail body: {{ $json.text }}", "categories": {"categories": [{"category": "Work/Professional", "description": "Work-related emails, business communications, job opportunities, professional networking, work meetings, and official company correspondence"}, {"category": "Personal/Family", "description": "Personal emails from friends and family, social invitations, personal updates, birthday wishes, and informal communications"}, {"category": "Shopping/Promotions", "description": "Promotional emails, newsletters, sales announcements, deals and discounts, product updates from retailers, and marketing communications"}, {"category": "Spams", "description": "Spams"}]}, "options": {"fallback": "other"}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [-660, 1040], "id": "453210ff-8b40-4e4a-ac9a-403af262f88f", "name": "Email Classifier"}, {"parameters": {"operation": "delete", "messageId": "={{ $('Gmail Trigger1').item.json.id }}"}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [660, 2320], "id": "c7747ea2-fa6d-43b0-aec3-9cb9233011d5", "name": "Delete Mails", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger1').item.json.id }}", "labelIds": ["Label_3333709168806354442"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [660, 2120], "id": "d3becf6e-8836-43b7-9609-32a02105077d", "name": "Other Emails", "webhookId": "YOUR_WEBHOOK_ID", "credentials": {"gmailOAuth2": {"id": "YOUR_GMAIL_CREDENTIALS_ID", "name": "<EMAIL>"}}}, {"parameters": {"content": "# Automation\n\n", "height": 3000, "width": 2800, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1140, -400], "id": "3f35ad9b-23c7-416f-9cb8-7e135e165cdd", "name": "Main Instructions1"}], "pinData": {}, "connections": {"Gmail Trigger1": {"main": [[{"node": "Email Classifier", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Email Classifier", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model3": {"ai_languageModel": [[{"node": "Summarize Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model4": {"ai_languageModel": [[{"node": "Reply Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model5": {"ai_languageModel": [[{"node": "Reply Agent1", "type": "ai_languageModel", "index": 0}]]}, "Summarize Agent": {"main": [[{"node": "Send Summary as Telegram Message", "type": "main", "index": 0}]]}, "Assign Personal label": {"main": [[{"node": "Reply Agent", "type": "main", "index": 0}]]}, "Assign Work label": {"main": [[{"node": "Summarize Agent", "type": "main", "index": 0}]]}, "Reply Agent": {"main": [[{"node": "Reply to the Mail", "type": "main", "index": 0}]]}, "Assign Shopping Label": {"main": [[{"node": "Reply Agent1", "type": "main", "index": 0}]]}, "Reply Agent1": {"main": [[{"node": "Gmail4", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Reply Agent1", "type": "ai_outputParser", "index": 0}]]}, "Email Classifier": {"main": [[{"node": "Assign Work label", "type": "main", "index": 0}], [{"node": "Assign Personal label", "type": "main", "index": 0}], [{"node": "Assign Shopping Label", "type": "main", "index": 0}], [{"node": "Delete Mails", "type": "main", "index": 0}], [{"node": "Other Emails", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bcfc7cf4-92d1-4371-a6a3-131762a71172", "meta": {"instanceId": "YOUR_INSTANCE_ID"}, "id": "paaktNW119SeJc1G", "tags": []}