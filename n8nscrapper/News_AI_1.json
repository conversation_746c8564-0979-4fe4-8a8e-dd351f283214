{"name": "News AI", "nodes": [{"parameters": {"model": "gpt-4o", "options": {"frequencyPenalty": 0.2, "temperature": 0.7}}, "id": "597f9450-df30-4540-9d79-c5295822a083", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1000, 380], "typeVersion": 1, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "id": "cf6b40b8-bb45-4625-92ed-076dae74638f", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1100, 380], "typeVersion": 1}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "={{ $('AI Agent').item.json.output.replace(/&/g, \"&amp;\").replace(/>/g, \"&gt;\").replace(/</g, \"&lt;\").replace(/\"/g, \"&quot;\") }}", "additionalFields": {"appendAttribution": false, "parse_mode": "HTML"}}, "id": "d4e3ef2a-0f7d-429c-a22b-2e8220b6b728", "name": "Correct errors", "type": "n8n-nodes-base.telegram", "position": [1600, 260], "typeVersion": 1.1, "webhookId": "8e7f0c3a-7bad-4406-a561-290be7db4811", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "fe438a51-3d5c-45c6-b2a8-d71405f86946", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-80, 240], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "typeVersion": 1, "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "35cc3e2f-8c64-4a1b-bb33-3e9c8532d2f6", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [440, 360], "typeVersion": 1.2, "webhookId": "f95e4ed7-64dd-42e3-ac81-8a684430a437", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}, "options": {}}, "id": "a295146a-5e56-4156-bd21-cb419353069c", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [820, 220], "typeVersion": 3.4}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "={{ $json.output }} \n\nThank you for your{{ $('Combine content and set properties').item.json['Source Type'] }} {{ $('Combine content and set properties').item.json['Message Type '] }} 🤗", "additionalFields": {"appendAttribution": false, "parse_mode": "HTML"}}, "id": "06ea7f37-eaa3-491f-b8b1-f4141285bad1", "name": "Send final reply", "type": "n8n-nodes-base.telegram", "position": [1400, 220], "typeVersion": 1.1, "webhookId": "4a5aa0dd-1f94-4983-bb37-b655ac9c80f0", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}, "onError": "continueErrorOutput"}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "8e66b3a8-f421-418f-a7e3-3e537d028aa9", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [440, 60], "typeVersion": 1.2, "webhookId": "5055aaf6-1bc4-4ef4-8f59-cc3b17c55c36", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "", "temperature": 0.7}}, "id": "0b374690-1bb9-48b5-b2fd-7db0ede0b793", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [620, 360], "typeVersion": 1.5, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "b27a90ad-3fb1-4a64-894d-73050c46f726", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [200, 60], "typeVersion": 1.2, "webhookId": "8c0a02d6-a71f-47da-b31a-e0dd7378c329", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/", "id": "d68f5557-eaf3-430c-b3e0-4c0bbb147c45"}]}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "47a2d644-a117-4d06-9a93-e5344d013b93", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [200, 240], "typeVersion": 3.2}, {"parameters": {"promptType": "define", "text": "=Request: {{ $json.CombinedMessage }}\n\nTry to help solve the request, use the tools to do it if possible, give the user a summary of what has been done.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1020, 220], "id": "976ed4ae-e4cf-4c67-9b56-63f0e5afe739", "name": "AI Agent1"}, {"parameters": {"toolDescription": "Reads data from an RSS Feed (Latest News)", "url": "https://www.thenation.com/subject/politics/feed/", "options": {}}, "type": "n8n-nodes-base.rssFeedReadTool", "typeVersion": 1.1, "position": [1200, 420], "id": "34923fcb-e881-400a-a4fb-55540370c8f8", "name": "RSS Read"}, {"parameters": {"name": "Create_a_Image_Post_with_text", "description": "Create a Image Post with text", "workflowId": {"__rl": true, "value": "Nf5wyQ4pVUqXgA2U", "mode": "list", "cachedResultName": "Auto Image Post Generator Telegram"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"topic": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('topic', ``, 'string') }}", "audience": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('audience', ``, 'string') }}", "language": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('language', ``, 'string') }}", "goalPlatform": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('goalPlatform', ``, 'string') }}", "tone": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('tone', ``, 'string') }}", "visuals": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('visuals', ``, 'string') }}", "context": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('context', ``, 'string') }}", "chatId": "={{ $('Determine content type').item.json.message.chat.id }}"}, "matchingColumns": [], "schema": [{"id": "topic", "displayName": "topic", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "audience", "displayName": "audience", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "language", "displayName": "language", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "goalPlatform", "displayName": "goalPlatform", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "tone", "displayName": "tone", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "visuals", "displayName": "visuals", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "context", "displayName": "context", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "chatId", "displayName": "chatId", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.1, "position": [1280, 380], "id": "2d67cb3a-6f04-4e90-916b-fb48cca3631d", "name": "Call n8n Workflow Tool"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Send final reply": {"main": [[], [{"node": "Correct errors", "type": "main", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Send final reply", "type": "main", "index": 0}]]}, "RSS Read": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "77022666-2bd5-432e-b0c3-6849973b8d4c", "meta": {"instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "lgnhB5RV5JYV1VXn", "tags": []}