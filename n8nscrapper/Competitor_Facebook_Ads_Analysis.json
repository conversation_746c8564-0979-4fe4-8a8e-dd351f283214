{"name": "Competitor Facebook Ads Analysis", "nodes": [{"parameters": {"formTitle": "Facebook Ads Research", "formDescription": "Research Competitors based on keywords", "formFields": {"values": [{"fieldLabel": "Keyword", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-320, -100], "id": "adf65921-a4ad-45a9-af66-cdf28fb9489d", "name": "On form submission", "webhookId": "8a6055af-adc8-48df-95d7-ebcc24c4ba08"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/curious_coder~facebook-ads-library-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "maxItems", "value": "10"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"count\": 10,\n    \"scrapeAdDetails\": false,\n    \"scrapePageAds.activeStatus\": \"all\",\n    \"urls\": [\n        {\n            \"url\": \"https://www.facebook.com/ads/library/?active_status=all&ad_type=all&country=ALL&is_targeted_country=false&media_type=all&q={{ $json.Keyword }}&search_type=keyword_unordered\",\n            \"method\": \"GET\"\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-100, -100], "id": "b1a7ea37-cb62-40d1-8f44-5462526ee15d", "name": "Scrape Ads", "credentials": {"httpQueryAuth": {"id": "iNQkR7FRp8EukQCZ", "name": "Apify"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.snapshot.display_format }}", "rightValue": "VIDEO", "operator": {"type": "string", "operation": "equals"}, "id": "1567d5a1-9f61-43d9-bb32-ae96fae2bee0"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Video"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a8d2b445-68b2-4775-9ff2-f1d63398f6b6", "leftValue": "={{ $json.snapshot.display_format }}", "rightValue": "IMAGE", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image"}]}, "options": {"fallbackOutput": "extra"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [120, -100], "id": "e4afbd32-609f-4cc8-8110-ddba6fae220c", "name": "Switch"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "Describe what's in the image. Emphasize it's design and what's said.", "imageUrls": "={{ $json.snapshot.images[0].original_image_url }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1000, 240], "id": "5b65018d-13bc-49c1-8d90-72c312d435a0", "name": "OpenAI", "credentials": {"openAiApi": {"id": "L6yK8rSOf4Ez4Te2", "name": "OpenAi account"}}}, {"parameters": {"operation": "binaryToPropery", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [700, -440], "id": "30b8d761-b8dd-4fa7-b95d-69fdbaf5f611", "name": "Extract from File"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n      \"contents\": [{\n        \"parts\":[\n            {\n              \"inline_data\": {\n                \"mime_type\":\"video/mp4\",\n                \"data\": \"{{ $json.data }}\"\n              }\n            },\n            {\"text\": \"Describe this video, include what you see, and the transcription of what's been said\"}\n        ]\n      }]\n    }", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1520, -240], "id": "297b137d-efe5-4505-af48-64ee1b74e183", "name": "Gemini Video Decode", "retryOnFail": true, "maxTries": 2, "credentials": {"httpQueryAuth": {"id": "Nu85sck6Rh1cTmDI", "name": "<PERSON>th"}}}, {"parameters": {"url": "={{ $json.snapshot.videos[0].video_sd_url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, -440], "id": "269d4a72-1357-43f6-8f00-04a1ee4a6598", "name": "Download Video"}, {"parameters": {"assignments": {"assignments": [{"id": "a88bf97e-a69d-4789-9e38-0b4abb6f3083", "name": "Media Description", "value": "={{ $json.content }}", "type": "string"}, {"id": "71fda7bf-f86c-465a-b0ab-3108a63c53f6", "name": "Ad URL", "value": "={{ $('Scrape Ads').item.json.url }}", "type": "string"}, {"id": "f886f938-74d5-4543-80de-972fdbe18958", "name": "Copywriting", "value": "={{ $('Scrape Ads').item.json.snapshot.body.text }}", "type": "string"}, {"id": "3ca05b96-ff10-41c2-bcaa-d64bd6f837e9", "name": "Page URL", "value": "={{ $('Scrape Ads').item.json.snapshot.link_url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1240, 240], "id": "85e1b3f4-0756-4417-a671-40fa12dbf7ac", "name": "Final Image Values"}, {"parameters": {"assignments": {"assignments": [{"id": "a88bf97e-a69d-4789-9e38-0b4abb6f3083", "name": "Media Description", "value": "={{ $json.candidates[0].content.parts[0].text }}", "type": "string"}, {"id": "71fda7bf-f86c-465a-b0ab-3108a63c53f6", "name": "Ad URL", "value": "={{ $('Scrape Ads').item.json.url }}", "type": "string"}, {"id": "f886f938-74d5-4543-80de-972fdbe18958", "name": "Copywriting", "value": "={{ $('Scrape Ads').item.json.snapshot.body.text }}", "type": "string"}, {"id": "3ca05b96-ff10-41c2-bcaa-d64bd6f837e9", "name": "Page URL", "value": "={{ $('Scrape Ads').item.json.snapshot.link_url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1820, -460], "id": "32b28e43-318f-4fef-b6ad-0aedbc1208b8", "name": "Final Video Values"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [2100, -100], "id": "54257346-eebb-4d78-b067-6fced3f47f87", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "14_fx9x7JP033oDE_VazWp4oTYVAxc4X6dCoEFnfQCC8", "mode": "list", "cachedResultName": "Facebook Ads Library", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/14_fx9x7JP033oDE_VazWp4oTYVAxc4X6dCoEFnfQCC8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/14_fx9x7JP033oDE_VazWp4oTYVAxc4X6dCoEFnfQCC8/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Ad URL": "={{ $json['Ad URL'] }}", "Page URL": "={{ $json['Page URL'] }}", "Copywriting": "={{ $json.Copywriting }}", "Media Description": "={{ $json['Media Description'] }}"}, "matchingColumns": [], "schema": [{"id": "Ad URL", "displayName": "Ad URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Page URL", "displayName": "Page URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Copywriting", "displayName": "Copywriting", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Media Description", "displayName": "Media Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [2320, -100], "id": "2a0d213e-12b2-4f74-927b-83800f87c669", "name": "Save", "credentials": {"googleSheetsOAuth2Api": {"id": "Rn3BYzXFYtmdsek1", "name": "Google Sheets account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [960, -440], "id": "af5c93a1-4cfd-4872-87bf-9aa14c6c9d3f", "name": "Loop Over Items"}, {"parameters": {"content": "## Gets Facebook Ads\n\n### Setup\n- Apify account - [Signup](https://apify.com/)\n- Apify Actor - [`curious_coder/facebook-ads-library-scraper`](https://console.apify.com/actors/XtaWFhbtfxyzqrFmd)\n\n### How To\n- Apify Setup [Tutorial](https://x.com/search?q=from%3Aarchitjn%20%22n8n%20Basics%22%20%22apify%22&src=typed_query&f=top)", "height": 680, "width": 700}, "type": "n8n-nodes-base.stickyNote", "position": [-380, -420], "typeVersion": 1, "id": "9ffc219a-5fdc-4807-90a8-9cbeb1b96070", "name": "<PERSON><PERSON>"}, {"parameters": {"amount": 30}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1320, -240], "id": "6cd95539-dd5c-4f30-acd5-74c9ef6165dd", "name": "Wait", "webhookId": "32807fcb-8d03-4359-bfbe-34463de097a9"}, {"parameters": {"content": "## Process Video\n\n### Setup \n- Google AI Studio API Key - [Generate here](https://aistudio.google.com/apikey)\n- Make sure to have a wait for rate limiting from Gemini - [Read Here](https://ai.google.dev/gemini-api/docs/rate-limits)\n\n### How To\n- Google Gemini Setup - [Tutorial](https://x.com/search?q=from%3Aarchitjn%20%22n8n%20Basics%22%20%22Gemini%20Video%22&src=typed_query&f=top)", "height": 720, "width": 1640, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [360, -720], "typeVersion": 1, "id": "29e86694-dedd-4c98-b495-a1392fc60c02", "name": "Sticky Note1"}, {"parameters": {"content": "## Analyse with <PERSON>", "height": 320, "width": 560, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [1200, -360], "typeVersion": 1, "id": "96e4fe36-feab-460a-afa1-dc37bfff728f", "name": "Sticky Note2"}, {"parameters": {"content": "## Analyse Image\n\n### Setup\n- OpenAI Account - [Here](https://platform.openai.com/login)\n\n### How To\n- OpenAI API Key Setup - [Tutorial](https://x.com/search?q=from%3Aarchitjn%20%22n8n%20Basics%22%20%22OpenAI%20API%22&src=typed_query&f=top)", "height": 400, "width": 1640, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [360, 40], "typeVersion": 1, "id": "f4776bf8-1cdf-464a-8e84-ee3a3484179c", "name": "Sticky Note3"}, {"parameters": {"content": "## Save Analysis\n\n### How to\n- Setup Google Sheets - [Tutorial](https://x.com/search?q=from%3Aarchitjn%20%22n8n%20Basics%22%20%22Google%20Sheets%22&src=typed_query&f=top)", "height": 620, "width": 500, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [2040, -360], "typeVersion": 1, "id": "704d9e14-42bd-453e-b0a0-66023860f4b8", "name": "Sticky Note4"}], "pinData": {}, "connections": {"On form submission": {"main": [[{"node": "Scrape Ads", "type": "main", "index": 0}]]}, "Scrape Ads": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Download Video", "type": "main", "index": 0}], [{"node": "OpenAI", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Final Image Values", "type": "main", "index": 0}]]}, "Gemini Video Decode": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Final Video Values": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Final Image Values": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Save", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Final Video Values", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Gemini Video Decode", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "efedf1cf-6e33-41ea-889c-e37f73ed8826", "meta": {"templateCredsSetupCompleted": true, "instanceId": "933df83c84196d70ce3ffd0bf5d3e1aca31b7366f2f84e1f3482640096e4a3a9"}, "id": "vHsNo2bT3fT7hUut", "tags": []}