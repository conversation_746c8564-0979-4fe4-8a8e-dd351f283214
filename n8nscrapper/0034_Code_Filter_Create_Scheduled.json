{"meta": {"instanceId": "0c99324b4b0921a9febd4737c606882881f3ca11d9b1d7e22b0dad4784eb24c7"}, "nodes": [{"id": "f418ae01-01ea-4794-8903-d5709a29c735", "name": "Get current date", "type": "n8n-nodes-base.code", "position": [240, 2460], "parameters": {"jsCode": "const monthNames = [\n  'January',\n  'February',\n  'March',\n  'April',\n  'May',\n  'June',\n  'July',\n  'August',\n  'September',\n  'October',\n  'November',\n  'December',\n]\n\nconst date = new Date()\nconst year = date.getFullYear()\nconst month = date.getMonth()\n\nlet currentDate = {\n  month: month,\n  year: year,\n  text: `${monthNames[month]} '${year.toString().slice(-2)}`\n}\n\nitems[0].json.currentDate = currentDate\n\nreturn items\n\n// Month > Number e.g. July = 6, December = 11\n// Year > Text\n// Text > Playlist name\n\n// let currentDate = {\n//   month: 8, \n//   year: '2024',\n//   text: `September '23`\n// }\n\n// items[0].json.currentDate = currentDate\n\n// return items\n\n"}, "typeVersion": 1}, {"id": "855e493a-a232-45ef-8fdd-4a8225065c95", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [460, 2580], "parameters": {"width": 1290.************, "height": 407.*************, "content": "## Check if the song is present in the database"}, "typeVersion": 1}, {"id": "672ef06c-b812-41c8-8501-cde8b61a4aef", "name": "Get last 10 liked tracks", "type": "n8n-nodes-base.spotify", "position": [500, 2680], "parameters": {"limit": 10, "resource": "library"}, "credentials": {"spotifyOAuth2Api": {"id": "zQrMRwwU6DLh4W77", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "da13c571-6af4-49bf-b8ff-2d54245f6d3e", "name": "Check if track is saved", "type": "n8n-nodes-base.nocoDb", "position": [940, 2780], "parameters": {"table": "m0dm2y304t7vmuk", "options": {"where": "=(uri,eq,{{ $json.track.uri }})", "fields": ["uri"]}, "operation": "getAll", "projectId": "pepq760y5lwt5tm", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "9144cda9-f18f-46d9-be2d-9fca4b192dbb", "name": "Is not saved", "type": "n8n-nodes-base.if", "position": [1160, 2780], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "dbb259d9-e2ec-4a7b-b375-601346dc2571", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "66b430e2-f46c-43b2-84e7-35c85d2b4403", "name": "Create song entry", "type": "n8n-nodes-base.nocoDb", "position": [1380, 2700], "parameters": {"table": "m0dm2y304t7vmuk", "fieldsUi": {"fieldValues": [{"fieldName": "uri", "fieldValue": "={{ $('For each tracks in liked song').item.json.track.uri }}"}, {"fieldName": "added_at", "fieldValue": "={{ $('For each tracks in liked song').item.json.added_at }}"}, {"fieldName": "playlistName", "fieldValue": "={{ $('Get current date').item.json.currentDate.text }}"}]}, "operation": "create", "projectId": "pepq760y5lwt5tm", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3}, {"id": "9bd883ea-2e87-45aa-b8a0-b361ba7c5d9f", "name": "Get all user playlist", "type": "n8n-nodes-base.spotify", "position": [500, 2220], "parameters": {"resource": "playlist", "operation": "getUserPlaylists", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "zQrMRwwU6DLh4W77", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "3a0dad98-4571-4fb7-b366-0060d35b65fe", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [460, 2080], "parameters": {"width": 1481.*************, "height": 416.*************, "content": "## Check if the playlist present in the database"}, "typeVersion": 1}, {"id": "e793b97c-cc29-47b0-8aa7-015fa631bc37", "name": "Get monthly playlist", "type": "n8n-nodes-base.filter", "position": [720, 2220], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "********-d774-4cb4-b26f-4dca294dda1d", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.name }}", "rightValue": "={{ $('Get current date').item.json.currentDate.text }}"}]}}, "typeVersion": 2, "alwaysOutputData": true}, {"id": "502ea9e2-7f03-4a8a-860e-90d63e42ee33", "name": "Get playlist in DB", "type": "n8n-nodes-base.nocoDb", "position": [1160, 2120], "parameters": {"table": "mchan0xys9h7h7e", "options": {"where": "=(name,eq,{{ $('Get current date').item.json.currentDate.text }})"}, "operation": "getAll", "projectId": "pepq760y5lwt5tm", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3, "alwaysOutputData": true}, {"id": "3d2bece0-8096-4ee1-a3b9-ae91b83f0957", "name": "Monthly playlist exist in Spotify ?", "type": "n8n-nodes-base.if", "position": [940, 2220], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a2d9e3e0-a906-4ed9-9e23-166f781c86b1", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "d983b940-2f8d-4823-aaaf-d1bfa4428b41", "name": "Playlist exist  in DB ?", "type": "n8n-nodes-base.if", "position": [1380, 2120], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9485c9d4-ecdc-4d0e-a576-c7db5787c069", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "c694ab19-bca7-4dd4-8d10-cf8a1adab341", "name": "Create playlist in Spotify", "type": "n8n-nodes-base.spotify", "position": [1160, 2320], "parameters": {"name": "={{ $('Get current date').item.json.currentDate.text }}", "resource": "playlist", "operation": "create", "additionalFields": {"description": "Monthly playlist"}}, "credentials": {"spotifyOAuth2Api": {"id": "zQrMRwwU6DLh4W77", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "dc9dc3b5-cef7-412b-b3f8-5ec011c2746d", "name": "Create playlist in DB1", "type": "n8n-nodes-base.nocoDb", "position": [1380, 2320], "parameters": {"table": "mchan0xys9h7h7e", "fieldsUi": {"fieldValues": [{"fieldName": "uri", "fieldValue": "={{ $json.uri }}"}, {"fieldName": "name", "fieldValue": "={{ $json.name }}"}, {"fieldName": "description", "fieldValue": "={{ $json.description}}"}]}, "operation": "create", "projectId": "pepq760y5lwt5tm", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3}, {"id": "0356c3a4-dc20-42b0-b069-************", "name": "Create playlist in DB", "type": "n8n-nodes-base.nocoDb", "position": [1600, 2200], "parameters": {"table": "mchan0xys9h7h7e", "fieldsUi": {"fieldValues": [{"fieldName": "uri", "fieldValue": "={{ $('Get monthly playlist').item.json.uri }}"}, {"fieldName": "name", "fieldValue": "={{ $('Get monthly playlist').item.json.name }}"}, {"fieldName": "description", "fieldValue": "={{ $('Get monthly playlist').item.json.description }}"}]}, "operation": "create", "projectId": "pepq760y5lwt5tm", "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3}, {"id": "e2c86f04-725c-4af7-b3c2-9c22e2dc64bf", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2040, 2460], "parameters": {"mode": "chooseBranch", "output": "empty"}, "typeVersion": 2.1}, {"id": "036e0d74-3383-44e9-991d-7e062b982b51", "name": "Clean op", "type": "n8n-nodes-base.noOp", "position": [1820, 2200], "parameters": {}, "typeVersion": 1}, {"id": "323c9746-f713-4a3d-9af5-9579ec767fca", "name": "Clean op2", "type": "n8n-nodes-base.noOp", "position": [1600, 2800], "parameters": {}, "typeVersion": 1}, {"id": "3b0be7ca-c47b-4524-b72a-c37f25c5e4d0", "name": "Get this month playlist in DB", "type": "n8n-nodes-base.nocoDb", "position": [2260, 2460], "parameters": {"table": "mchan0xys9h7h7e", "options": {"where": "=(name,eq,{{ $('Get current date').item.json.currentDate.text }})"}, "operation": "getAll", "projectId": "pepq760y5lwt5tm", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3}, {"id": "733077e4-c474-4c95-ba05-d0b2375475ad", "name": "Get this month tracks in DB", "type": "n8n-nodes-base.nocoDb", "position": [2480, 2460], "parameters": {"table": "m0dm2y304t7vmuk", "options": {"where": "=(playlistN<PERSON>,eq,{{ $('Get current date').item.json.currentDate.text }})"}, "operation": "getAll", "projectId": "pepq760y5lwt5tm", "returnAll": true, "authentication": "nocoDbApiToken"}, "credentials": {"nocoDbApiToken": {"id": "9uSbSrDz8EL2OIL7", "name": "NocoDB Token account"}}, "typeVersion": 3}, {"id": "6c8ef70f-542d-4454-9ae6-8f4e9778beb0", "name": "Add song to the playlist", "type": "n8n-nodes-base.spotify", "position": [3580, 2460], "parameters": {"id": "={{ $('Get this month playlist in DB').item.json.uri }}", "trackID": "={{ $('For each monthly tracks in DB').item.json.uri }}", "resource": "playlist", "additionalFields": {}}, "credentials": {"spotifyOAuth2Api": {"id": "zQrMRwwU6DLh4W77", "name": "Spotify account"}}, "typeVersion": 1}, {"id": "034cd38d-4800-4f9c-9b67-453fdb2afa3c", "name": "For each tracks in liked song", "type": "n8n-nodes-base.splitInBatches", "position": [720, 2680], "parameters": {"options": {"reset": false}}, "typeVersion": 3}, {"id": "90ff5c0b-e842-437f-be85-a5938288c513", "name": "For each monthly tracks in DB", "type": "n8n-nodes-base.splitInBatches", "position": [2700, 2460], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "decf36a4-fb8c-41eb-ae15-7ba36d621ad7", "name": "Get this month tracks in Spotify", "type": "n8n-nodes-base.spotify", "position": [2920, 2560], "parameters": {"id": "={{ $('Get this month playlist in DB').item.json.uri }}", "resource": "playlist", "operation": "getTracks", "returnAll": true}, "credentials": {"spotifyOAuth2Api": {"id": "zQrMRwwU6DLh4W77", "name": "Spotify account"}}, "typeVersion": 1, "alwaysOutputData": true}, {"id": "d322a655-e80b-4277-87d9-93e927b2f372", "name": "Filter1", "type": "n8n-nodes-base.filter", "position": [3140, 2560], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a11640e1-f22a-4ce9-abff-976efc57e1d3", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $('For each monthly tracks in DB').item.json.uri }}", "rightValue": "={{ $json.track.uri }}"}]}}, "executeOnce": false, "typeVersion": 2, "alwaysOutputData": true}, {"id": "5027f98d-b973-405f-81cf-534df794325f", "name": "Song is not present in the playlist ?", "type": "n8n-nodes-base.if", "position": [3360, 2560], "parameters": {"options": {}, "conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1beb843e-53da-48ce-9717-d7797232e4ae", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}}, "typeVersion": 2}, {"id": "cd2e3a28-24c1-47d7-ad30-c836e08ad40f", "name": "Clean op1", "type": "n8n-nodes-base.noOp", "position": [3800, 2560], "parameters": {}, "typeVersion": 1}, {"id": "56bbb0e9-3ee5-48e3-b0bf-48e8d026daa9", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2220, 2400], "parameters": {"width": 1733.785946789966, "height": 351.94195615011336, "content": "## Check if the song is in the Spotify playlist. If not, add it."}, "typeVersion": 1}, {"id": "9834163b-0991-4910-bb4f-cf4557bfa0d5", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [20, 2460], "parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "typeVersion": 1.2}, {"id": "72a3c48f-a759-4e0c-b7bb-9f69a5f4377e", "name": "End", "type": "n8n-nodes-base.noOp", "position": [4100, 2260], "parameters": {}, "typeVersion": 1}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "Get this month playlist in DB", "type": "main", "index": 0}]]}, "Filter1": {"main": [[{"node": "Song is not present in the playlist ?", "type": "main", "index": 0}]]}, "Clean op": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Clean op1": {"main": [[{"node": "For each monthly tracks in DB", "type": "main", "index": 0}]]}, "Clean op2": {"main": [[{"node": "For each tracks in liked song", "type": "main", "index": 0}]]}, "Is not saved": {"main": [[{"node": "Create song entry", "type": "main", "index": 0}], [{"node": "Clean op2", "type": "main", "index": 0}]]}, "Get current date": {"main": [[{"node": "Get all user playlist", "type": "main", "index": 0}, {"node": "Get last 10 liked tracks", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get current date", "type": "main", "index": 0}]]}, "Create song entry": {"main": [[{"node": "Clean op2", "type": "main", "index": 0}]]}, "Get playlist in DB": {"main": [[{"node": "Playlist exist  in DB ?", "type": "main", "index": 0}]]}, "Get monthly playlist": {"main": [[{"node": "Monthly playlist exist in Spotify ?", "type": "main", "index": 0}]]}, "Create playlist in DB": {"main": [[{"node": "Clean op", "type": "main", "index": 0}]]}, "Get all user playlist": {"main": [[{"node": "Get monthly playlist", "type": "main", "index": 0}]]}, "Create playlist in DB1": {"main": [[{"node": "Clean op", "type": "main", "index": 0}]]}, "Check if track is saved": {"main": [[{"node": "Is not saved", "type": "main", "index": 0}]]}, "Playlist exist  in DB ?": {"main": [[{"node": "Clean op", "type": "main", "index": 0}], [{"node": "Create playlist in DB", "type": "main", "index": 0}]]}, "Add song to the playlist": {"main": [[{"node": "Clean op1", "type": "main", "index": 0}]]}, "Get last 10 liked tracks": {"main": [[{"node": "For each tracks in liked song", "type": "main", "index": 0}]]}, "Create playlist in Spotify": {"main": [[{"node": "Create playlist in DB1", "type": "main", "index": 0}]]}, "Get this month tracks in DB": {"main": [[{"node": "For each monthly tracks in DB", "type": "main", "index": 0}]]}, "For each monthly tracks in DB": {"main": [[{"node": "End", "type": "main", "index": 0}], [{"node": "Get this month tracks in Spotify", "type": "main", "index": 0}]]}, "For each tracks in liked song": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}], [{"node": "Check if track is saved", "type": "main", "index": 0}]]}, "Get this month playlist in DB": {"main": [[{"node": "Get this month tracks in DB", "type": "main", "index": 0}]]}, "Get this month tracks in Spotify": {"main": [[{"node": "Filter1", "type": "main", "index": 0}]]}, "Monthly playlist exist in Spotify ?": {"main": [[{"node": "Get playlist in DB", "type": "main", "index": 0}], [{"node": "Create playlist in Spotify", "type": "main", "index": 0}]]}, "Song is not present in the playlist ?": {"main": [[{"node": "Add song to the playlist", "type": "main", "index": 0}], [{"node": "Clean op1", "type": "main", "index": 0}]]}}}