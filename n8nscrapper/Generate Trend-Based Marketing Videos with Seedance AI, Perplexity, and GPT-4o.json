{"name": "Generate product videos", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "0f5b2233-2956-4128-93b0-fd0207f1ef23", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "position": [780, 180], "webhookId": "534ea1e8-0470-41b0-a690-b3f09cc348dd", "typeVersion": 1.2}, {"parameters": {}, "id": "5469a576-76a2-4fe9-9b12-b7c816eddda3", "name": "Trend Research Agent", "type": "n8n-nodes-base.perplexity", "position": [1040, 180], "typeVersion": 1, "credentials": {}}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "chatgpt-4o-latest", "cachedResultName": "CHATGPT-4O-LATEST"}, "messages": {"values": [{"content": "You are a creative video prompt generator for short-form ads (e.g., Instagram, TikTok). Based on:\n\nThe user's original brief (e.g., <PERSON>Create a 5-second IG ad for my perfume brand”), and\n\nThe top marketing trend insight provided by the previous research node,\n\nYour task is to craft a concise, visually descriptive video generation prompt suitable for input into the Seedance API.\n\n🛑 Do not include any extra explanation, formatting, or commentary. Your entire output must be the exact prompt to pass into the video generator.\n\nThe prompt should:\n– Be under 100 words\n– Describe the visual elements, tone, and motion clearly\n– Reflect the product or brand type, and\n– Incorporate the marketing trend as the core concept or hook\n\nExample of your expected output format:\n\n“A close-up slow-motion shot of a glass perfume bottle as morning sunlight filters through mist. The background shows soft-focus wildflowers, matching the theme of natural beauty and calm. Overlay text: ‘Nature’s Elegance. Reinvented.’ Trend-inspired styling based on the ‘quiet luxury’ aesthetic. For Instagram. 5 seconds.”", "role": "system"}, {"content": "={{ $json.choices[0].message.content }}"}]}, "options": {}}, "id": "f0a4835e-dde9-477a-844e-a31b856d73ac", "name": "Video Prompt Engineer", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1260, 180], "typeVersion": 1.8}, {"parameters": {"method": "POST", "url": "https://api.wavespeed.ai/api/v3/bytedance/seedance-v1-pro-i2v-1080p", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "duration", "value": "5"}, {"name": "image"}, {"name": "prompt", "value": "={{ $json.message.content }}"}, {"name": "seed", "value": "-1"}]}, "options": {"redirect": {"redirect": {}}}}, "id": "6d518872-8d51-4009-af52-a317dcbc2b5c", "name": "Post Request - Wavespeed", "type": "n8n-nodes-base.httpRequest", "position": [1660, 180], "typeVersion": 4.2}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.data.outputs[0] }}", "additionalFields": {}}, "id": "cde212e0-11c2-406a-9604-731ab5dba7f5", "name": "Send a text message", "type": "n8n-nodes-base.telegram", "position": [2580, 200], "webhookId": "05add186-9fd7-4219-9b54-3e458fe6b0d6", "typeVersion": 1.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9a0179e2-465e-467a-b750-19c592649d3b", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.data.status }}", "rightValue": "processing"}]}, "options": {}}, "id": "ee6ef4e0-b2bb-4409-b24d-48b86bd5b48e", "name": "If", "type": "n8n-nodes-base.if", "position": [2300, 180], "typeVersion": 2.2}, {"parameters": {"content": "<PERSON>eg<PERSON>", "height": 260, "width": 260, "color": 7}, "id": "0d3f9da3-b45d-4b31-8cad-85a9d2199510", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [700, 100], "typeVersion": 1}, {"parameters": {"content": "Perplexity Research Agent", "height": 220, "color": 7}, "id": "eb2189e9-b2dd-4631-9872-13789e13a163", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [980, 120], "typeVersion": 1}, {"parameters": {"content": "Video Prompt Agent", "height": 280, "width": 300, "color": 7}, "id": "e47019d1-bf68-4f15-85d7-913d3b2ed4b6", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1240, 100], "typeVersion": 1}, {"parameters": {"content": "POST Request to Wavespeed", "height": 280, "width": 300, "color": 7}, "id": "b54040c5-58df-4006-a84f-495add26297d", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1560, 120], "typeVersion": 1}, {"parameters": {"content": "GET Request Loop", "height": 600, "width": 640, "color": 7}, "id": "2feb0fae-2e61-4b6a-89f8-2f9055e9d026", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1880, 80], "typeVersion": 1}, {"parameters": {"content": "Telegram Output", "height": 560, "width": 280, "color": 7}, "id": "b0ef772e-3802-4ce4-a391-4cb3e6f68494", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2540, 80], "typeVersion": 1}, {"parameters": {"content": "\n🎬 Seedance AI Video Agent\n\nTurn any trend-based idea into a short video ad with a single Telegram message.\n\nHow it works:\n\n💬 Send a short prompt on Telegram\n\n📈 Agent finds trending angles using Perplexity AI\n\n🧠 GPT-4<PERSON> writes a video script\n\n🎥 Seedance creates the video via Wavespeed\n\n🔁 Video link is sent back to Telegram when ready\n\nBuilt for: Marketers, founders, and creators who want fast, trend-driven video ads — no editing needed.\n\nTools: Telegram, Perplexity AI, GPT-4o, Seedance API, n8n\n\nUse it to: Auto-generate IG/TikTok ads, scale creative production, or power your marketing chatbot.\n\n\n\n\n\n\n\n\n\n", "height": 480, "width": 680, "color": 7}, "id": "3e9bf3cc-e377-4424-9894-bfdc13ddb3b8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "typeVersion": 1}, {"parameters": {"amount": 30}, "id": "3327ca41-287a-499d-b63a-71f8d51ce0ca", "name": "Wait 30 sec", "type": "n8n-nodes-base.wait", "position": [2340, 460], "webhookId": "2927f5c0-0881-4c48-900b-0194aded5926", "typeVersion": 1.1}, {"parameters": {"amount": 30}, "id": "a7dc7112-e761-4ab6-a1bf-c232577570f8", "name": "Wait 30 Sec", "type": "n8n-nodes-base.wait", "position": [1920, 180], "webhookId": "40dd854a-29ad-41a1-adb1-3cd661dfd562", "typeVersion": 1.1}, {"parameters": {"url": "=https://api.wavespeed.ai/api/v3/predictions/{{ $json.data.id }}/result", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"redirect": {"redirect": {}}}}, "id": "a24bef53-1258-4a46-aeeb-bcfb1dfa2318", "name": "GET Request Wavespeed", "type": "n8n-nodes-base.httpRequest", "position": [2100, 180], "typeVersion": 4.2}], "pinData": {}, "connections": {"If": {"main": [[{"node": "Wait 30 sec", "type": "main", "index": 0}], [{"node": "Send a text message", "type": "main", "index": 0}]]}, "Wait 30 Sec": {"main": [[{"node": "GET Request Wavespeed", "type": "main", "index": 0}]]}, "Wait 30 sec": {"main": [[{"node": "GET Request Wavespeed", "type": "main", "index": 0}]]}, "GET Request Wavespeed": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Video Prompt Engineer": {"main": [[{"node": "Post Request - Wavespeed", "type": "main", "index": 0}]]}, "Post Request - Wavespeed": {"main": [[{"node": "Wait 30 Sec", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "58b81497-0f64-49ce-884d-c07133fbba36", "meta": {"instanceId": "06dc2f69cd870abfe9d3c116af39b865de427bccaf18688c6a8df8ff517cc5b3"}, "id": "xhibKnAef6JH2I1O", "tags": []}