{"nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [450, 300], "webhookId": "45e2593e-f25d-4be5-9b50-4a7c1e566a9e", "parameters": {"path": "45e2593e-f25d-4be5-9b50-4a7c1e566a9e", "options": {}, "httpMethod": "POST", "responseMode": "lastNode"}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [850, 200], "parameters": {"url": "=https://{{$json[\"body\"][\"data\"][\"options\"][0][\"value\"]}}", "options": {}, "responseFormat": "string"}, "typeVersion": 1}, {"name": "Check type", "type": "n8n-nodes-base.if", "position": [650, 300], "parameters": {"conditions": {"number": [{"value1": "={{$json[\"body\"][\"type\"]}}", "value2": 1, "operation": "notEqual"}]}}, "typeVersion": 1}, {"name": "Extract Title", "type": "n8n-nodes-base.htmlExtract", "position": [1050, 200], "parameters": {"options": {}, "extractionValues": {"values": [{"key": "title", "cssSelector": "title"}]}}, "typeVersion": 1}, {"name": "Add Link to Notion", "type": "n8n-nodes-base.notion", "position": [1250, 200], "parameters": {"resource": "databasePage", "databaseId": "8a1638ce-da33-41b7-8fd9-37a4c272ba95", "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{$json[\"title\"]}}"}, {"key": "Link|url", "urlValue": "={{$node[\"Check type\"].json[\"body\"][\"data\"][\"options\"][0][\"value\"]}}"}]}}, "credentials": {"notionApi": "Notion API Credentials"}, "typeVersion": 1}, {"name": "Reply on Discord", "type": "n8n-nodes-base.set", "position": [1450, 200], "parameters": {"values": {"number": [{"name": "type", "value": 4}], "string": [{"name": "data.content", "value": "Added Link to notion"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Register URL", "type": "n8n-nodes-base.set", "position": [850, 410], "parameters": {"values": {"number": [{"name": "type", "value": 1}], "string": []}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}], "connections": {"Webhook": {"main": [[{"node": "Check type", "type": "main", "index": 0}]]}, "Check type": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "Register URL", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Extract Title", "type": "main", "index": 0}]]}, "Extract Title": {"main": [[{"node": "Add Link to Notion", "type": "main", "index": 0}]]}, "Add Link to Notion": {"main": [[{"node": "Reply on Discord", "type": "main", "index": 0}]]}}}