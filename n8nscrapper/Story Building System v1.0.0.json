{"name": "🐬 Story Building System v1.0.0", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "6732a4f9-659b-44b0-954a-db8c4c830e0e", "name": "input", "value": "=⚠️ ADD YOUR STORY REQUEST HERE ⚠️", "type": "string"}, {"id": "86da6d70-4bb6-4f57-b24d-afa2b67b3669", "name": "cards_text", "value": "=Charac<PERSON><PERSON>\nCut to the Chase\nSlogan: “Get to the point, fast!”\nThis card tells you ta drop the fluff and jump right in. It urges you to show action, emotion, or meaning immediately. Keep your story alive and your audience alert.\n\n🚀 Jump into action\n😮 Use strong emotion\n💡 Share a key insight\n❓ Ask an engaging question\n🎯 Be clear and direct\nDrive Stories\nSlogan: “Fuel your tale with drive!”\nShare moments that show what really pushes you forward. Talk about times you felt free, grew your skills, or found true purpose. Use both wins and losses to build real grit in your story.\n\n🕶 Show autonomy moments\n🏋️ Illustrate mastery\n🎯 Highlight purpose\n😃 Share ups and downs\n💪 Prove your inner drive\nHero & Guide\nSlogan: “You guide the hero.”\nTurn your user into the hero of the journey. Show how you step in as the expert guide to help them win. Keep your tone humble, wise, and supportive.\n\n🗺 Map the journey\n🌟 Define the hero’s goal\n👣 Demonstrate guidance\n📖 Share a relatable tale\n🛡 Offer practical help\nTrust Me, I’m an Expert\nSlogan: “Show your know-how.”\nBuild trust by telling a real-life story that backs your claims. Prove that you act on your values even when it’s tough. Let your deeds speak louder than words.\n\n👓 Demonstrate expertise\n💰 Show values over money\n🕵️ Reveal moral choices\n🏆 Highlight a success moment\n💬 Build strong credibility\nConcept\nCircle of Life\nSlogan: “Life goes round and round.”\nMap your tale using life stages—from childhood to the wide world and back home. Show growth and change over time. This makes your story feel real and relatable.\n\n👶 Begin with innocence\n🌍 Explore the wide world\n🏠 Return home changed\n🔄 Embrace life cycles\n💖 Connect with deep emotions\nCurious Tales\nSlogan: “Stoke your natural wonder.”\nShow what grabs your attention and sparks curiosity. Share a “Eureka!” moment or a slow hunch that built up over time. Let your natural wonder drive the narrative.\n\n🔍 Highlight an anomaly\n😲 Capture surprise\n⏳ Describe flow moments\n😏 Note healthy envy\n💔 Mention necessary sacrifice\nGood & Evil\nSlogan: “Set the moral stakes.”\nCreate a clear fight between right and wrong. Use moral conflicts to drive drama in your story. Stand for a cause that feels bigger than you.\n\n⚖ Define care vs. harm\n🤝 Contrast fairness vs. cheating\n🗽 Weigh liberty vs. oppression\n👮 Show authority vs. subversion\n🛡 Highlight loyalty vs. betrayal\nOrder & Chaos\nSlogan: “Balance the known and unknown.”\nDraw a line between what is safe and what is wild. Describe the comfort of order and the thrill of chaos. Let your hero bridge these two worlds for a dynamic tale.\n\n📊 Define the known world\n⚡ Spotlight chaos\n🔄 Show disruption\n🦸 Place your hero as the bridge\n🎢 Build dynamic tension\nRules, Cheats & Rebels\nSlogan: “Break the mold.”\nPick a rule and show what happens when it’s broken. Tell a tale of a rebel or a cheat who challenges norms. Use this to reveal the cost or reward of bending the rules.\n\n📜 Set up the rule\n🚨 Introduce the rule breaker\n⚖ Show the consequences\n🔥 Build moral tension\n🏆 Offer a lesson learned\nSecrets & Puzzles\nSlogan: “Keep them guessing.”\nShare a secret or drop a puzzle that sparks curiosity. Point out a gap in what’s known and tease the answer. Make your audience work for the revelation.\n\n🤫 Reveal a hidden fact\n🧩 Pose a challenging puzzle\n❓ Create an information gap\n🔍 Guide the discovery\n🎁 Reward with insight\nShock of the Old\nSlogan: “Find wisdom in the past.”\nLook at old objects or ways with fresh eyes. Reveal hidden stories in what seems mundane. Use age-old lessons to add depth to your tale.\n\n🏺 Choose an old object\n🔍 Uncover its hidden role\n👥 Connect with personal history\n😮 Surprise with fresh insight\n💡 Offer timeless wisdom\nThe Dragon & the City\nSlogan: “Face the beast, save the city.”\nTurn your project into an epic adventure where the status quo is the City and the threat is the Dragon. Decide whether to escape, defend, or attack. This makes your goal thrilling and clear.\n\n🏙 Define the safe City\n🐉 Identify the dangerous Dragon\n🚪 Show escape routes\n🛡 Build up defenses\n⚔ Plan a bold attack\nThree Great Conflicts\nSlogan: “Show the big fight.”\nFocus on one of three epic battles: nature, society, or yourself. Let the conflict drive your narrative forward. Choose the fight that matters most and resolve it.\n\n🌊 Battle against nature\n👥 Conflict with society\n🤯 Struggle within self\n🚀 Contrast dream and nightmare\n🔄 Resolve the conflict\nUniversal Stories\nSlogan: “Build on what we all know.”\nUse themes that everyone can relate to. Touch on free will, conflict, and rites of passage. Create a common ground that connects with all audiences.\n\n👤 Emphasize free will\n⚔ Show conflict and cooperation\n🤔 Highlight human inconsistencies\n😨 Address fear and bravery\n👪 Tap into universal life stages\nExplore\nAbstractions\nSlogan: “See layers of action.”\nObserve your users to find hidden layers behind their actions. Move from what they do up to how they think. This deep look builds a richer story behind everyday acts.\n\n👀 Watch actions\n🧠 Note mimicked behaviors\n🎭 Identify rituals and plays\n📖 Uncover the underlying narrative\n🔄 Spot rational explanations\nData Detectives\nSlogan: “Solve the mystery with data.”\nTurn raw data into a story. Zoom out to see trends, then zoom in on a vivid example. Let your facts lead you to an “aha!” moment that drives your narrative.\n\n🔍 Find the big trends\n🔬 Focus on a key detail\n🕵️ Follow the clues\n💡 Uncover hidden insights\n🧩 Weave numbers into a tale\nEmotional Dashboard\nSlogan: “Map your feelings.”\nTreat emotions like dashboard lights on your journey. Note every high and low as you work through challenges. Your tale gains power when it taps into raw, real feelings.\n\n😊 List positive vibes\n😢 Record negative moods\n🔄 Track shifts over time\n⏱ Mark key emotional beats\n💬 Connect with raw feelings\nSocial Proof\nSlogan: “Let others back you up.”\nShow that people trust your idea by sharing trends, tests, and testimonials. Let real-life examples support your claims. This builds a safe and believable story that others can join.\n\n📊 Present clear trends\n👥 Share user success\n⭐ Use strong testimonials\n📈 Highlight early adopters\n✅ Validate with real data\nStory Listening\nSlogan: “Hear the lessons in others’ tales.”\nListen closely to the stories around you. Pick up teachable moments and build a simple timeline. Use real-life decisions to shape your narrative. This makes your story grounded and wise.\n\n👂 Listen actively\n📝 Note key moments\n⏳ Build a simple timeline\n🔍 Highlight important choices\n💡 Extract valuable lessons\nThat’s Funny\nSlogan: “Laugh and learn.”\nSpot the funny, odd, or downright bizarre in your work. Let humor reveal hidden truths and break tension. A good laugh makes your story memorable and real.\n\n😂 Identify funny moments\n🤔 Spot odd surprises\n🤷‍♂️ Notice contradictions\n😄 Use humor to connect\n🧠 Spark fresh ideas\nThoughtful Failures\nSlogan: “Fail smart, learn hard.”\nEvery mistake is a chance to grow. Break down what went wrong without playing the blame game. Share the hard lessons to show true progress.\n\n❌ Identify the error\n🧐 Analyze your choices\n💡 Learn from the mistake\n🔄 Ask what could be done differently\n👥 Share to help others\nFunction\nIcebreaker Stories\nSlogan: “Warm up with a tale.”\nUse random images or prompts to spark creative stories. Let your team mix up before, during, and after moments. It breaks the ice and jumpstarts fresh ideas.\n\n🖼 Pick random images\n🔄 Create a before/during/after\n😊 Encourage team sharing\n🤹 Explore different views\n🚀 Kickstart creativity\nPitch Perfect\nSlogan: “Sell your idea in a snap.”\nCraft a short pitch that hits on the problem, solution, and why you’re the one to trust. Build your narrative with ups and downs. This card makes your idea clear and compelling.\n\n🎤 Craft a tight elevator pitch\n❓ Define the problem\n💡 Present your solution\n📈 Build a dramatic arc\n💪 Show why you’re trusted\nSimple Sales Stories\nSlogan: “Sell with a story.”\nKeep it simple by showing that someone like your audience already loves your product. Use real examples and relatable wins. This builds trust and makes the sale feel safe.\n\n👥 Target a similar customer\n👍 Share a success story\n🎤 Use genuine testimonials\n📊 Highlight proven results\n✅ Build relatable trust\nStory-ish Conversations\nSlogan: “Turn chat into a story.”\nFind moments in everyday talk that spark a mini narrative. Ask the who, what, where, and when to dig deeper. Turn casual chats into treasure troves of ideas.\n\n💬 Ask clear, probing questions\n🔍 Dig for the details\n😃 Capture real emotions\n🤝 Involve your audience\n📝 Transform dialogue into a tale\nOrganise\nAudience Profile\nSlogan: “Know who you’re talking to.”\nBuild a clear picture of your crowd by listing basic facts, hopes, and fears. Tailor your story to speak directly to them. When you know your audience, your story lands better.\n\n👤 Collect basic details\n🔍 Identify their problems\n💭 Note hopes and fears\n🗣 Connect through shared traits\n🎯 Tailor your narrative\nBig, Small, Inside, Outside\nSlogan: “Tell the right story to the right crowd.”\nSort your stories by scale and where they belong. Big stories work for the world; small ones fit inside your team. Organize your tales to suit the moment and audience.\n\n📏 Size up your story\n🏢 Define the audience scope\n🔄 Differentiate inside vs. outside\n🗂 Categorize by context\n🎯 Deliver the right tale\nStory Bank\nSlogan: “Keep your best tales safe.”\nCollect and log your stories so you never repeat or misuse one. Track the key details, audience, and freshness of each tale. Your Story Bank keeps your narrative assets sharp and true.\n\n📝 Record each story\n🔑 Note key details\n👥 Track your audience\n⏰ Update regularly\n🤝 Respect ownership rights\nStructure\nDownfall\nSlogan: “Watch the mighty fall.”\nTell a tale of a rise and a sudden crash. Show the hidden flaw, desperate denial, and ultimate disgrace. Use it to warn or learn from failure.\n\n🏰 Show flawed power\n🚫 Reveal denial\n📉 Expose disgrace\n⚠ Offer a cautionary lesson\n🔄 Encourage change\nEpic Fail\nSlogan: “Falling so you can rise.”\nTurn a setback into a story of growth. Own your missteps whether it’s hubris, betrayal, or a system failure. Let your failure fuel future wins.\n\n❌ Identify the failure\n😓 Own your mistake\n🏋️ Show personal growth\n🧐 Analyze the error\n🔄 Share the lesson learned\nFive Ts\nSlogan: “A neat five-step guide.”\nStructure your story with a simple five-beat formula. Cover the timeline, turning points, tensions, temptations, and teachable moments. This gives your narrative a clear flow.\n\n⏳ Set the timeline\n🔄 Mark turning points\n⚡ Highlight tensions\n🍬 Note temptations\n📚 Share a teachable moment\nHappy Ever Afters\nSlogan: “End on a high note.”\nWrap your story with a feel-good ending. Show how your hero grows, finds love, or earns respect. End with hope and a win that feels well earned.\n\n🌱 Show growth\n🏠 Find home or love\n👍 Earn respect\n🤝 Highlight teamwork\n🎉 End with optimism\nInnovation Curve\nSlogan: “From bold to safe.”\nMap your story from the daring early days to a steady mainstream. Tailor your narrative for innovators, pioneers, and cautious adopters. This reduces risk and wins over doubters.\n\n🚀 Begin with innovators\n🧭 Chart the pioneers\n📈 Target early adopters\n🛡 Address cautious users\n🔒 Comfort traditional minds\nMan in a Hole\nSlogan: “Rise from the pit.”\nTell a tale of a fall and a climb back up. Start with comfort, hit a crisis, then show recovery and growth. It proves that setbacks can build strength.\n\n🏠 Define the comfort zone\n🚪 Trigger a fall\n⛏ Highlight the crisis\n🧗 Show the climb out\n🌟 End on a higher note\nNo Easy Way\nSlogan: “Real talk, real struggle.”\nLay out the full rollercoaster of your journey. Present a problem, early success, setback, crisis, and eventual recovery. It’s raw, real, and relatable.\n\n🚩 State the problem\n🌟 Note early wins\n🔻 Show a setback\n💥 Build the crisis\n🏆 End with recovery\nPride & Fall\nSlogan: “Hubris leads to downfall.”\nShare a tale where pride builds up only to crash down hard. Show how overconfidence can blind you and lead to a worse outcome. Use it to warn and teach your audience.\n\n🏰 Set a starting low point\n🎈 Note rising pride\n⚠ Issue a clear warning\n⬇ Show the dramatic fall\n💔 End with a costly loss\nRags to Riches\nSlogan: “Hidden value unveiled.”\nTell a classic story of rising from nothing to something great. Start humble, face a trigger and struggle hard, then win recognition. It inspires and proves that value always shines through.\n\n🌱 Start with hidden potential\n🚀 Introduce the trigger\n⚔ Detail the struggle\n🏆 Achieve recognition\n💖 Inspire hope\nVoyage & Return\nSlogan: “Leave, learn, come back.”\nTake your hero on an adventure away from home. They face challenges, learn crucial lessons, and return transformed. This journey highlights real growth and change.\n\n🏡 Define the safe home\n🚢 Begin the adventure\n⚠ Face new challenges\n🧠 Learn key lessons\n🔄 Return transformed\nStyle\nLeave it Out!\nSlogan: “Cut the clutter.”\nTrim your story to the essentials. Leave out excess details and let gaps spark curiosity. This creates a lean narrative that pulls your audience in.\n\n✂️ Remove unnecessary parts\n🤔 Hint at missing details\n❓ Spark curiosity\n📉 Keep the text tight\n🧩 Leave space for imagination\nMovie Time\nSlogan: “Paint a mental film.”\nSet the scene like a movie with action, emotion, and clear meaning. Let your words create vivid images in your listener’s head. Your tale becomes a mini film that captivates.\n\n🎬 Set the scene clearly\n🏃 Show dynamic action\n😢 Capture raw emotion\n🗣 Explain the meaning\n📽 Create a mental movie\nRolls Royce Moment\nSlogan: “Nail the vivid detail.”\nShare one crisp detail that sums up your story perfectly. Make it sensory and precise, like a luxury ad. This moment tells the big picture in a few words.\n\n🚗 Pinpoint a key detail\n👀 Use sensory language\n📏 Keep it precise\n💎 Reflect quality\n🎯 Sum up your message\nShow & Tell\nSlogan: “Blend visuals with words.”\nPair strong images with a short, punchy script. Don’t overload with text or stray off-topic. Let your visuals and words work together to hold attention.\n\n🖼 Use clear, strong images\n📝 Write a brief script\n🔗 Link visuals to words\n🎨 Balance both elements\n👥 Engage the audience\nStory Hooks\nSlogan: “Grab them from the start.”\nStart your story with a twist that hooks your reader instantly. Ask a big question or drop a surprising fact. This sets the tone and makes them want more.\n\n❓ Pose a big question\n⚡ Use an unexpected twist\n🔄 Offer irony or contrast\n🤝 Make it relatable\n🌟 Use superlatives for impact\nThree is the Magic Number\nSlogan: “Keep it in threes.”\nFocus on three core points to simplify your story. Use three repetitions to drive your message home. This method makes your key points easy to remember.\n\n🔢 Choose three key points\n🎯 Repeat for emphasis\n⚖ Compare two extremes plus a middle\n📝 Keep it simple\n💭 Enhance recall\n", "type": "string"}, {"id": "a1c1bc72-077a-4c10-b7d3-682692c9140e", "name": "google_drive_folder_id", "value": "⚠️ ADD GOOGLE DRIVE FOLDER ID ⚠️", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1020, -360], "id": "c92b4ff9-aee9-43af-9771-a65f82d36b39", "name": "$INPUTS$"}, {"parameters": {"inputText": "=User input:\n\n{{ $('$INPUTS$').first().json.input }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}", "options": {"categories": "Connect, <PERSON><PERSON>ce, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Lead, Motivate, <PERSON>ll", "systemPromptTemplate": "=Your goal is to pick the best recipe for a given story\n\nCONVINCE\nRecipe: Explain Your Expertise\nStories that Convince:\nExplain complex expertise to a skeptical, non-expert audience. Win their trust.\n\nYou’ve got years of experience and deep research. But why aren't people listening? Stop drowning them in numbers. Connect through authentic storytelling. Make your knowledge feel real and personal, not abstract.\n\nCards:\n\n✂️ Three is the Magic Number\nYour audience won't remember 20 points. Choose three razor-sharp ideas, make them stick.\n\n🧪 That’s Funny\nReveal the weird, fascinating insights behind your facts. Humor and genuine excitement are unforgettable.\n\n🕵️ Data Detectives\nPresent data through mystery, investigation, or curiosity-driven storytelling. Let us discover alongside you.\n\n🧠 Trust Me, I’m an Expert\nProve your worth with clear examples—why should we care? Cut the jargon, show results.\n\n🦸 Hero & Guide\nPosition yourself as the guide helping your audience achieve something valuable. It’s their journey, you're their trusted expert.\n\nCONNECT\nRecipe: Get Closer to People\nStories that Connect:\nBuild deep empathy and genuine connections by stepping into another person’s world through story.\n\nYou can’t truly serve others without understanding their lived experiences. Stories help you deeply relate, build trust, and spark meaningful relationships.\n\nCards:\n\n🦻 Story Listening\nStop talking, start truly listening. Capture the powerful insights hidden in other people's stories.\n\n🦻 Abstractions\nObserve actions—not just answers. Stories reveal truth in what people actually do.\n\n💡 Universal Stories\nAnchor your stories in universal struggles and emotions everyone instantly recognizes.\n\n⬡ Story-ish Conversations\nTurn daily interactions into meaningful stories—every moment has narrative potential.\n\n💡 Circle of Life\nUse relatable character journeys everyone instinctively understands. Life is a shared human story; tap into it.\n\nIMPRESS\nRecipe: Nail Your Presentation\nStories that Impress:\nPresent your message powerfully. No jargon, no boredom—grab attention and keep it.\n\nYour audience is restless. Capture their imagination by turning facts and figures into memorable stories that stick. Impress, don't depress.\n\nCards:\n\n✂️ Movie Time\nDeliver an actual narrative, not just slides. Your facts must feel cinematic, engaging, alive.\n\n🔺 Five Ts\nStructure your talk clearly (Trigger, Tension, Turning Point, Transformation, Takeaway). Give your story a rhythm that resonates.\n\n✂️ Story Hooks\nKeep grabbing your audience's attention every few minutes. No flat spots—relentless intrigue.\n\n✂️ Show and Tell\nVisuals should clarify, entertain, or shock—not bore or confuse. Use them sparingly, dramatically, effectively.\n\n💠 Cut to the Chase\nIf your talk stumbles, pivot swiftly. Know how to quickly clarify your key message with ruthless efficiency.\n\nEXPLAIN\nRecipe: Make it Crystal Clear\nStories that Explain:\nTurn complicated ideas into easy-to-follow narratives. Don’t talk at people—draw them in.\n\nNo matter how technical or detailed your content, a story is always clearer than jargon. Respect your audience’s time; give them clarity with story structure.\n\nCards:\n\n✂️ Movie Time\nReplace dense explanations with clear narrative flow. Every idea needs context and progression.\n\n🔺 Five Ts\nStructure your explanation to unfold logically (Trigger, Tension, Turning Point, Transformation, Takeaway).\n\n✂️ Story Hooks\nKeep curiosity alive at each stage. Constantly re-engage attention to make ideas stick.\n\n✂️ Show and Tell\nUse visuals for clarity. If it doesn’t clarify or enhance understanding, drop it immediately.\n\n💠 Cut to the Chase\nAlways have a short, powerful summary ready. Clarity beats complexity every single time.\n\nSELL\nRecipe: Prove Your Value\nStories that Sell:\nConvince your audience you're the real deal. Win trust through authentic, human-centered stories—not bullet points.\n\nFacts alone don't sell. Stories sell confidence, credibility, and trust. Make your promise believable through stories people can identify with.\n\nCards:\n\n🧬 Audience Profile\nKnow exactly who you’re talking to. Clearly address their pain points with precision.\n\n💠 Simple Sales Stories\nShow relatable people you've successfully helped. Make their success feel tangible and realistic.\n\n🧩 Social Proof\nStrengthen your credibility with genuine testimonials or relatable success trends. Proof speaks louder than promises.\n\n🔺 Rags to Riches\nPut your customer’s journey at the heart. Show transformation: from struggle to triumph clearly and emotionally.\n\n💠 Pitch Perfect\nDeliver a sharp, irresistible elevator pitch. Short, clear, believable—no fluff.\n\nLEAD\nRecipe: Show the Way Forward\nStories that Lead:\nUnify your team and strengthen culture through powerful, shared stories.\n\nLeadership isn’t about slogans. Stories create genuine bonds and shared values. Make teamwork, success, and culture come alive through storytelling.\n\nCards:\n\n🧠 Curious Tales\nUse stories to discover what motivates your team—personalize your leadership by truly understanding their drives.\n\n🔺 Man in a Hole\nFrame projects as meaningful journeys with real stakes and clear outcomes. Inspire through collective struggle.\n\n⚙️ Emotional Dashboard\nHighlight emotional highs and lows in real time. Acknowledge struggles openly to build genuine connection.\n\n⚒️ Thoughtful Failures\nCelebrate learning from mistakes. Real leadership comes from openly reflecting on setbacks.\n\n🎵 Story Bank\nCollect impactful stories regularly. Continuously share relatable lessons to reinforce strong culture and shared goals.\n\nMOTIVATE\nRecipe: Drive People to Act\nStories that Motivate:\nMove people beyond agreement—drive them to action with clear vision, realistic struggles, and compelling purpose.\n\nPeople don’t move unless deeply inspired. Your story must ignite their passion, clearly convey risks, and powerfully reveal what's possible.\n\nCards:\n\n🐉 The Dragon & the City\nClearly define a bold, exciting vision that demands action—show clearly what victory looks like.\n\n🏎️ Drive Stories\nShow your personal motivations authentically—make people feel why it matters deeply to you, and therefore to them.\n\n🐮 Three Great Conflicts\nName specific, tough barriers you must conquer. Honesty about obstacles builds real motivation and trust.\n\n🔺 Innovation Curve\nAddress risks clearly—show realistic reasons to believe your idea will succeed despite uncertainty.\n\n🔺 No Easy Way\nBe brutally honest about challenges ahead. Prepare your audience with real-world expectations, not sugar-coated promises.\n--------------------------------\n Only output the JSON.\nReply in English"}}, "type": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "typeVersion": 1, "position": [1260, -180], "id": "32d9f0e9-144d-4e19-a91d-9a72da1fce3d", "name": "Sentiment Analysis", "retryOnFail": true}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Get Closer to People\nStories that Connect:\nBuild deep empathy and genuine connections by stepping into another person’s world through story.\n\nYou can’t truly serve others without understanding their lived experiences. Stories help you deeply relate, build trust, and spark meaningful relationships.\n\nCards:\n\n🦻 Story Listening\nStop talking, start truly listening. Capture the powerful insights hidden in other people's stories.\n\n🦻 Abstractions\nObserve actions—not just answers. Stories reveal truth in what people actually do.\n\n💡 Universal Stories\nAnchor your stories in universal struggles and emotions everyone instantly recognizes.\n\n⬡ Story-ish Conversations\nTurn daily interactions into meaningful stories—every moment has narrative potential.\n\n💡 Circle of Life\nUse relatable character journeys everyone instinctively understands. Life is a shared human story; tap into it.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1800, -340], "id": "cafd5be9-880f-405e-8ffe-4b8c34f937f3", "name": "Connect"}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Explain Your Expertise\nStories that Convince:\nExplain complex expertise to a skeptical, non-expert audience. Win their trust.\n\nYou’ve got years of experience and deep research. But why aren't people listening? Stop drowning them in numbers. Connect through authentic storytelling. Make your knowledge feel real and personal, not abstract.\n\nCards:\n\n✂️ Three is the Magic Number\nYour audience won't remember 20 points. Choose three razor-sharp ideas, make them stick.\n\n🧪 That’s Funny\nReveal the weird, fascinating insights behind your facts. Humor and genuine excitement are unforgettable.\n\n🕵️ Data Detectives\nPresent data through mystery, investigation, or curiosity-driven storytelling. Let us discover alongside you.\n\n🧠 Trust Me, I’m an Expert\nProve your worth with clear examples—why should we care? Cut the jargon, show results.\n\n🦸 Hero & Guide\nPosition yourself as the guide helping your audience achieve something valuable. It’s their journey, you're their trusted expert.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1620, -340], "id": "2ce2d8eb-290e-408b-ba6c-4a1958434e1f", "name": "Convince"}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Make it Crystal Clear\nStories that Explain:\nTurn complicated ideas into easy-to-follow narratives. Don’t talk at people—draw them in.\n\nNo matter how technical or detailed your content, a story is always clearer than jargon. Respect your audience’s time; give them clarity with story structure.\n\nCards:\n\n✂️ Movie Time\nReplace dense explanations with clear narrative flow. Every idea needs context and progression.\n\n🔺 Five Ts\nStructure your explanation to unfold logically (<PERSON><PERSON>, <PERSON><PERSON>, Turning Point, Transformation, Takeaway).\n\n✂️ Story Hooks\nKeep curiosity alive at each stage. Constantly re-engage attention to make ideas stick.\n\n✂️ Show and Tell\nUse visuals for clarity. If it doesn’t clarify or enhance understanding, drop it immediately.\n\n💠 Cut to the Chase\nAlways have a short, powerful summary ready. Clarity beats complexity every single time.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1800, -180], "id": "9f448ab7-9015-450e-96ff-a3b91ba710c8", "name": "Explain"}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Nail Your Presentation\nStories that Impress:\nPresent your message powerfully. No jargon, no boredom—grab attention and keep it.\n\nYour audience is restless. Capture their imagination by turning facts and figures into memorable stories that stick. Impress, don't depress.\n\nCards:\n\n✂️ Movie Time\nDeliver an actual narrative, not just slides. Your facts must feel cinematic, engaging, alive.\n\n🔺 Five Ts\nStructure your talk clearly (<PERSON><PERSON>, <PERSON><PERSON>, Turning Point, Transformation, Takeaway). Give your story a rhythm that resonates.\n\n✂️ Story Hooks\nKeep grabbing your audience's attention every few minutes. No flat spots—relentless intrigue.\n\n✂️ Show and Tell\nVisuals should clarify, entertain, or shock—not bore or confuse. Use them sparingly, dramatically, effectively.\n\n💠 Cut to the Chase\nIf your talk stumbles, pivot swiftly. Know how to quickly clarify your key message with ruthless efficiency.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1620, -180], "id": "07d62485-32e9-4bed-9e1a-7655f3c958e9", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Show the Way Forward\nStories that Lead:\nUnify your team and strengthen culture through powerful, shared stories.\n\nLeadership isn’t about slogans. Stories create genuine bonds and shared values. Make teamwork, success, and culture come alive through storytelling.\n\nCards:\n\n🧠 Curious Tales\nUse stories to discover what motivates your team—personalize your leadership by truly understanding their drives.\n\n🔺 Man in a Hole\nFrame projects as meaningful journeys with real stakes and clear outcomes. Inspire through collective struggle.\n\n⚙️ Emotional Dashboard\nHighlight emotional highs and lows in real time. Acknowledge struggles openly to build genuine connection.\n\n⚒️ Thoughtful Failures\nCelebrate learning from mistakes. Real leadership comes from openly reflecting on setbacks.\n\n🎵 Story Bank\nCollect impactful stories regularly. Continuously share relatable lessons to reinforce strong culture and shared goals.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1620, -20], "id": "becf7b85-24fb-4356-8cb0-36ccd57032c1", "name": "Lead"}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Drive People to Act\nStories that Motivate:\nMove people beyond agreement—drive them to action with clear vision, realistic struggles, and compelling purpose.\n\nPeople don’t move unless deeply inspired. Your story must ignite their passion, clearly convey risks, and powerfully reveal what's possible.\n\nCards:\n\n🐉 The Dragon & the City\nClearly define a bold, exciting vision that demands action—show clearly what victory looks like.\n\n🏎️ Drive Stories\nShow your personal motivations authentically—make people feel why it matters deeply to you, and therefore to them.\n\n🐮 Three Great Conflicts\nName specific, tough barriers you must conquer. Honesty about obstacles builds real motivation and trust.\n\n🔺 Innovation Curve\nAddress risks clearly—show realistic reasons to believe your idea will succeed despite uncertainty.\n\n🔺 No Easy Way\nBe brutally honest about challenges ahead. Prepare your audience with real-world expectations, not sugar-coated promises.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1800, -20], "id": "057d4b7e-6ada-4971-affb-c6c4758da99a", "name": "Motivate"}, {"parameters": {"assignments": {"assignments": [{"id": "93291fc0-609a-4e76-98f7-bb1c02e0b940", "name": "prompt", "value": "=Recipe: Prove Your Value\nStories that Sell:\nConvince your audience you're the real deal. Win trust through authentic, human-centered stories—not bullet points.\n\nFacts alone don't sell. Stories sell confidence, credibility, and trust. Make your promise believable through stories people can identify with.\n\nCards:\n\n🧬 Audience Profile\nKnow exactly who you’re talking to. Clearly address their pain points with precision.\n\n💠 Simple Sales Stories\nShow relatable people you've successfully helped. Make their success feel tangible and realistic.\n\n🧩 Social Proof\nStrengthen your credibility with genuine testimonials or relatable success trends. Proof speaks louder than promises.\n\n🔺 Rags to <PERSON><PERSON>\nPut your customer’s journey at the heart. Show transformation: from struggle to triumph clearly and emotionally.\n\n💠 Pitch Perfect\nDeliver a sharp, irresistible elevator pitch. Short, clear, believable—no fluff.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1980, -180], "id": "63a88fd9-19ac-4e6b-99c6-b92adbd03488", "name": "<PERSON>ll"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"cards\": {\n    \t\t\"category\": {\n    \t\t\t\"type\": \"string\"\n    \t\t},\n    \t\t\"title\": {\n    \t\t\t\"type\": \"string\"\n    \t\t},\n\t\t\t\"card_content\": {\n\t\t\t\t\"type\": \"string\",\n                \"maxLenght\": 1500\n\t\t\t}\n\t\t},\n\t\t\"type\": \"array\"\n\t}\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [960, 220], "id": "45364868-d7ae-4f09-8dbf-c55d5b8b4c18", "name": "Structured Output Parser"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [720, -360], "id": "64c41e54-d300-4f53-8ea6-fcb5ab8f5143", "name": "When clicking ‘Test workflow’"}, {"parameters": {"assignments": {"assignments": [{"id": "7eca80ac-6a9c-4b22-a7b5-b02147006b54", "name": "prompt", "value": "={{ $json.prompt }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2220, -340], "id": "5be4862e-2157-49dd-9776-c8cda5652996", "name": "prompt"}, {"parameters": {"promptType": "define", "text": "=User Input: {{ $('$INPUTS$').item.json.input }}\n\nStory Baseline:\n\n{{ $('story baseline').item.json.output.toJsonString() }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\nCharacters:\n{{ $('characters').item.json.output.toJsonString() }}", "hasOutputParser": true, "options": {"systemMessage": "=Reply in JSON. \n\nReply in the language of the original user input. If language is not English - translate them to the target language.\n\nYour goal is to pick which 7 cards we will be using in this \"{{ $('Sentiment Analysis').item.json.sentimentAnalysis.category }}\" story.\n\nUse this formula:\n{{ $('prompt').item.json.prompt }}\n\nAll cards\n\n{{ $('$INPUTS$').item.json.cards_text }}\n\n\nPick one card for each category: Concept, Explore, Character, Function, Structure, Style\nreply following this schema:\n\n{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"cards\": {\n    \t\t\"category\": {\n    \t\t\t\"type\": \"string\"\n    \t\t},\n    \t\t\"title\": {\n    \t\t\t\"type\": \"string\"\n    \t\t},\n\t\t\t\"card_content\": {\n\t\t\t\t\"type\": \"string\",\n                \"maxLenght\": 1500\n\t\t\t}\n\t\t},\n\t\t\"type\": \"array\"\n\t}\n}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [720, 260], "id": "7e3e5894-e76a-4e5f-839d-f32bf8eed774", "name": "pick cards"}, {"parameters": {"promptType": "define", "text": "={{ $('$INPUTS$').first().json.input }}", "hasOutputParser": true, "options": {"systemMessage": "=Write a baseline for a story. User provided input, we provided the story recipe. Now my goal is to create a baseline for our dtory that would include:\n\nWrite a killer baseline for a powerful story. We've taken user input and our story recipe—now it's time to craft a rock-solid foundation that excites readers from start to finish.\n\nInclude clearly and specifically:\n\nOutput Format\nWhat is the format of this story. is it a simple short story? long story? a screenplay? an ad? long sales letter? email? childrens book with images and pages? instructional pdf? \n\nKEY Theme\nIdentify one badass theme. Make it gutsy, authentic, and meaningful—something sharp enough to slice through everyday bullshit. No vague \"hope\" or \"courage\" nonsense—get real.\n\nStoryteller’s Goal\nClearly define why you're telling this story. What's your angle? To shock people awake? Make them laugh till they choke? Teach a brutal life lesson? No half-assed \"raise awareness\" nonsense—say exactly what you aim to do.\n\nEmotional Targets\nState specific, powerful emotions this story will trigger. We're not aiming for weak-ass \"happy\" or \"sad\"—think envy, fear of loss, regret, raw humor, cringe embarrassment, or gut-punch empathy. Aim deep, hit hard.\n\nHidden Higher Concepts\nClearly list deeper ideas beneath the surface. No surface-level moral lectures. Hide raw truths about adult struggles—money anxieties, messy relationships, health scares—behind relatable everyday stories.\n\nAudience Questions\nClearly define tough, compelling questions we leave readers chewing on long after they're done reading. Make readers wonder about their own lives—no obvious bullshit, real reflection only.\n\nUnique Story Delivery\nSuggest an innovative, unexpected, or clever way of delivering this story. Screw predictable narrators or linear timelines. Consider unusual structures, unreliable narrators, biting satire, ironic twists, fourth-wall breaks, or brutally honest monologues. Surprise your readers with something fresh and memorable.\n\nGive readers something they'll feel in their gut, laugh at, or be haunted by. Cut through the boring, watered-down crap—be random, bold, and relentlessly honest.\n\nRecipe:\n{{ $('prompt').item.json.prompt }}\n\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\nReply in the language of the original user input"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [2380, -340], "id": "08c279c5-c5b1-4f5e-8561-c7f97d56c1e7", "name": "story baseline", "retryOnFail": true}, {"parameters": {"promptType": "define", "text": "=User Input: {{ $('$INPUTS$').item.json.input }}\n\nStory Baseline:\n\n{{ $('story baseline').item.json.output.toJsonString() }}\n\nCharacters:\n\n{{ $('characters').item.json.output.toJsonString() }}\n\nStory Enhancements Ideas\n\n{{ $json.output.toJsonString() }}\n\nStory Cards:\n\n{{ $('pick cards').item.json.output.cards.toJsonString() }}", "hasOutputParser": true, "options": {"systemMessage": "=Reply in JSON\nReply in the language of the original user input\n\nYour goal is to writhe the plot to this this \"{{ $('Sentiment Analysis').item.json.sentimentAnalysis.category }}\" story.\n\nUse this formula:\n{{ $('prompt').item.json.prompt }}\n\nAll available cards:\n\n{{ $('$INPUTS$').item.json.cards_text }}\n\n🔥 Story Plot Generator from Selected Cards\nYour job:\nUse the user-selected story cards provided as key anchors to write a vivid, intense, and original story plot.\n\nInstructions:\n\nPick ONLY the cards provided by the user. Don’t add random ones.\nClearly connect each selected card to build a coherent, gripping plot.\nGround the story deeply in relatable, real-life struggles—not fantasy or fluff.\nNo watered-down bullshit. Make it raw, funny, shocking, emotional—something readers won't forget.\n🃏 Using the Cards:\nEvery chosen card must directly shape or affect plot events.\nClearly explain how each card connects and advances the story.\nHighlight moments that trigger strong emotions—make readers laugh, cringe, or gasp out loud.\n🎯 Your Plot Must Clearly Cover These Points:\nOpening Scene (Set the Hook)\nDescribe exactly how the story opens—set a clear, intriguing situation or event right away. No vague setups, hit readers hard from sentence one.\n\nImmediate Tension: Clearly define what kicks off the tension at the start. Be explicit—no soft intros. Identify specific adult-level stakes (health scares, financial disasters, relationship meltdowns).\n\nPlot Development: Clearly show how each card escalates tension, deepens character trouble, or ups emotional stakes. Cards shouldn't be random—they should build naturally off each other.\n\nHidden Reveal (Late-stage Reveal): Precisely state critical info readers learn only near the end. Make sure it's surprising yet believable—no cheap twists.\n\nMajor Plot Twist: Clearly describe the major twist—either gut-wrenchingly emotional or hilariously relatable. State exactly why it matters deeply to the characters and readers alike.\n\nForeshadowing Clues: Clearly pinpoint subtle early clues or details planted at the start to hint (but not spoil!) the twist. No obvious hints—make readers slap their foreheads later in appreciation.\n\nShock Factor: Rate the shock/comedy factor explicitly. How strong is the punch of the twist? Suggest ways to amplify if it's not intense enough.\n\nAdult Themes Explored: Clearly name authentic themes (health, wealth, relationships) you're subtly embedding in the story. Keep it real—no preachy fluff.\n\nComedic Relief Moments: Point exactly where and how you'll add humor or comedic irony to relieve tension and keep readers hooked. Keep jokes sharp and relatable—adult humor only.\n\nMemorable Ending: Clearly define a strong, impactful closing scene. End with either a lasting emotional punch, an insightful reflection, or a smart, dark joke readers won't shake off easily.\n\nYour Goal:\nCraft a story that hits readers in the gut, makes them laugh out loud, or keeps them thinking. Use the selected cards as anchors. Go big, go bold—leave the watered-down stuff behind.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [2340, 260], "id": "56bfd805-9ee5-4821-8ead-969f047dbaa4", "name": "story plot"}, {"parameters": {"fieldToSplitOut": "output.cards", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1100, 260], "id": "80d6fe40-52f8-4e7c-8f0f-7886ae05bcdf", "name": "Split Out"}, {"parameters": {"promptType": "define", "text": "=Card: {{ $('Loop Over Items').item.json.toJsonString() }}", "hasOutputParser": true, "options": {"systemMessage": "=Your goal is to boost and deepen the story plot using a storytelling card. You have many inputs to work with. Use each one to create four rich and detailed story enhancement ideas. Follow these steps:\n\nRead the Inputs Thoroughly:\n\nUser Input:\n{{ $('$INPUTS$').first().json.input }}\n\nStory Baseline:\n{{ $('story baseline').first().json.output.toJsonString() }}\n\nStory Cards:\n{{ $('pick cards').first().json.output.cards.toJsonString() }}\n\nCharacters:\n{{ $('characters').first().json.output.toJsonString() }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\n\n\nAnalyze the Materials:\nLook for connections and tensions between the user input, the baseline, cards, plot, and characters. Ask yourself:\n\nWhat elements are strong?\nWhat parts need more twist or depth?\nWhich characters or themes can be pushed further?\nDevelop Four Detailed Enhancement Ideas:\nEach idea must be:\n\nSpecific: Clearly explain the twist or enhancement.\nActionable: Provide a path to add this twist into the story.\nCharacter-Driven or Plot-Driven: It can be a twist in character arcs, relationships, or the overall narrative.\nIntegrated: Show how it uses at least two of the provided inputs (e.g., merge a storytelling card with a character trait or the baseline).\nFor Each Idea, Address the Following Points:\n\nIdea Summary: A short title or summary of the enhancement.\nDetailed Description: Explain how this twist changes the narrative or deepens the character arcs.\nConnection to Inputs: Specify which parts of the inputs are being used (User Input, Baseline, Cards, Current Plot, Characters).\nPotential Impact: Describe what new tension, conflict, or insight this idea brings to the story.\nUse Clear, Direct Language:\nWrite short sentences and clear words. Avoid fluff. Each idea should be easy to understand and actionable.\n\nFinal Output:\nYour final answer should list four enhancement ideas. Use numbered bullets (1 to 4) for each idea. Make sure each idea is a standalone enhancement that can be built into the full plot later.\n\nReply in JSON\nReply in the language of the original user input"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1560, 260], "id": "3f2ce91b-b4cc-499b-a844-c0d6b157e3cc", "name": "story enhancement", "retryOnFail": true}, {"parameters": {"promptType": "define", "text": "=Write characters that would fit this story (if none needed - use default narrator/author) \n\nStory Baseline:\n{{ $('story baseline').item.json.output.toJsonString() }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}", "hasOutputParser": true, "options": {"systemMessage": "=You’re a Character Creator for our storytelling system. Your job: Build kick-ass, authentic characters that feel gritty, alive, and real as hell. Characters should stand alone—not just orbit around the main character. Each character lives their own messy life, driven by personal struggles, secret desires, embarrassing habits, relatable hang-ups, and unforgettable quirks.\n\nRules for creating characters:\n\nLimit to 13 data points max per character. Choose only what's most relevant to telling a gripping story.\nSkip clichés and surface-level fluff. Go straight for the juicy, uncomfortable, human stuff.\nWrite in raw, direct language—no empty platitudes, no watered-down feel-good bullshit.\nFor each character, clearly define these 12 critical points:\n\n1. Name: Realistic, believable name (or nickname) matching their personality. \"Unknown\" if they're a mystery—but make it interesting.\n2. Occupation: How they survive, make money, hustle, or pretend they're useful.\n3. Affiliation: Which group, collective is the character a part of.\n4. Demographics: Sex, Age, Race, Physical traits (if fictional character - include detailed description)\n5. Appearance & Style: Brief but memorable—body type, weird scars, tattoos, embarrassing outfits, striking features, or signature item.\n6. Biggest Insecurities: Real, honest anxieties—money stress, sexual hang-ups, fear of mediocrity, ugly envy, or hidden shame.\n7. Core Motivation: What actually gets their ass out of bed every morning? Greed, revenge, lust, pride, pure survival instinct—be blunt.\n8. Public Goal vs Hidden Goal: The bullshit they show people vs. their real secret agenda. Clearly differentiate the two.\n9. Deepest Fear or Desire: Choose either their worst nightmare or secret yearning. Go deep and uncomfortable.\n10. Family & Messy Past: Short snapshot of their screwed-up childhood, broken relationships, or defining family drama. Keep it tight, keep it real.\n11. Quirks & Habits: Embarrassing or memorable mannerisms, tics, catchphrases, addictions—something unique and vivid.\n12. Special Skill or Talent: Something they're undeniably good at—practical or absurd, useful or oddly specific.\n13. Defining Moment: A specific event or trauma that permanently screwed them up, changed their perspective, or defined their personality.\n\nImportant:\n\nDon’t build characters as accessories to your main character. They have their own shit going on, even if the main character isn't around.\nMake them flawed, relatable, memorable. Nobody cares about perfect or preachy characters—real humans are messy, selfish, hilarious, and occasionally brilliant.\nGo create characters readers can't forget—even if they try.\n\n\nUser Input: {{ $('$INPUTS$').item.json.input }}\n\nRecipe:\n{{ $('prompt').item.json.prompt }}\n\nIf no characters are presesnt (perhaps you are describing the natural event, a city life, a police high speed chase, you know what i mean - in that case use only one character - narrator with everything set to NA. Unless you choose to take a concept and turn it into a character (e.g. a business could be considered a separate organism with its own path)\n\nReply in the language of the original user input"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [2200, -40], "id": "eb46ce10-f348-466f-a744-4edc29d0a49f", "name": "characters", "retryOnFail": true}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1340, 260], "id": "68885015-91d6-4654-add1-3e3e452113b4", "name": "Loop Over Items"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "output"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2060, 260], "id": "1783f82f-f6f1-48eb-818e-0cde36a67ea0", "name": "Aggregate"}, {"parameters": {"assignments": {"assignments": [{"id": "c8767d42-b610-4b6d-b4ef-1d3ace83b9d7", "name": "story_frile_url", "value": "=https://docs.google.com/document/d/{{ $json.id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1180, 860], "id": "1efe0358-36ec-4208-b4a3-2581a3bd82b8", "name": "<PERSON>"}, {"parameters": {"promptType": "define", "text": "=User Input: {{ $('$INPUTS$').item.json.input }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\nStory Baseline:\n\n{{ $('story baseline').item.json.output.toJsonString() }}\n\nStory Plot So Far\n\n{{ $json.output }}\n\nStory Cards:\n\n{{ $('pick cards').item.json.output.cards.toJsonString() }} \n\nCharacters:\n\n{{ $('characters').item.json.output.toJsonString() }}", "hasOutputParser": true, "options": {"systemMessage": "=Your goal is to writhe the timeline to this this \"{{ $('Sentiment Analysis').item.json.sentimentAnalysis.category }}\" story.\n\nUse this formula:\n{{ $('prompt').item.json.prompt }}\n\nAll available cards:\n\n{{ $('$INPUTS$').item.json.cards_text }}\n\nPick the cards that user provides, and write a story timeline. \n\nMAIN TIMELINE:\n\nStarting Point: Clearly define the initial setting or event to instantly hook the reader.\n\nCore Tension: Identify tension clearly from the start that persists and intensifies, resolving only at the end.\n\nHidden Reveal: Pinpoint exactly what crucial information readers only discover in the final moments.\n\nPlot Twist: Clearly describe and critique the effectiveness of the unexpected turn. Ensure it surprises or entertains profoundly.\n\nEarly Hints: Recommend subtle details early on to foreshadow and strengthen the twist’s inevitability.\n\nShock Value: Evaluate how genuinely shocking, surprising, or funny the twist is.\n\nAdult Themes: Clearly identify and suggest ways to deepen exploration of relatable adult topics (health, wealth, relationships).\n\nComedic Relief: Identify specific moments ripe for humor to balance the story’s tension and suggest ways to enhance them.\n\nStrong Ending: Recommend how to craft a powerful, reflective ending that leaves readers thinking deeply.\n\nBEFORE THE STORY:\n\nStory Duration: Clearly critique the chosen timeline (minutes, days, lifetime), ensuring it aligns with story goals.\n\nGlobal Impact: Review background global or local events for credibility and relevance.\n\nImmediate Background: Clearly outline and critique recent events influencing the current story's setup.\n\nPre-story Tensions: Analyze realistic and compelling tensions impacting characters prior to the story.\n\nCharacter History: Evaluate events in character’s lives (1 day, 1 month, 1 year prior) for emotional depth and relevance.\n\nDURING THE STORY:\n\nCurrent Setting: Clearly critique where characters find themselves initially and why it matters.\n\nImmediate Hook: Evaluate the first major event for its ability to engage readers instantly.\n\nCharacter Interactions: Suggest improvements for character interactions to ensure depth and realism.\n\nTiming of Plot Twist: Recommend precise timing and context adjustments to maximize emotional or comedic impact.\n\nAFTER THE STORY:\n\nRealistic Outcomes: Clearly propose believable consequences or developments 1 day, 1 month, 1 year, and 10 years after the story ends, even though readers won’t see them.\n\nYour goal: Ensure readers FEEL deeply, laugh authentically, or experience genuine surprise. Provide blunt, actionable, and creatively insightful feedback—no sugar-coating.\n\nReply in JSON\nReply in the language of the original user input\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [720, 560], "id": "69735d14-bc24-4a91-9f8b-2fc9e3d09540", "name": "story timeline", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 100}, {"parameters": {"content": "## Start of Funnel", "height": 240, "width": 220}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, -440], "id": "454f32c0-16c6-4187-873e-642d196d5f4f", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Pick the story type and story recipe\nAI will analyze what type of story you are trying to tell.", "height": 580, "width": 940, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1200, -440], "id": "6d0cf2bb-cf00-4404-8b3b-64b3e04a06fe", "name": "Sticky Note1"}, {"parameters": {"content": "## Set inputs\nYour story goes here", "height": 240, "width": 260, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [920, -440], "id": "668eccc9-a6dc-4b7f-a949-edea1f405138", "name": "Sticky Note2"}, {"parameters": {"content": "## Write a baseline\nSetting the ground philosophies for this story", "height": 280, "width": 520, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2160, -440], "id": "afa731f1-e4e3-4f8c-a2eb-2f075b227f48", "name": "Sticky Note3"}, {"parameters": {"content": "## Define characters\nBuild strong independent and lively characters", "height": 280, "width": 520, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2160, -140], "id": "1a90ea2a-d84f-4f53-b2f5-577be1b0c2d2", "name": "Sticky Note4"}, {"parameters": {"content": "## Send to Editor\nGet critique of the story and improvement suggestions", "height": 280, "width": 420, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1680, 460], "id": "0c533b8a-7b77-4882-af0a-3f0fc2b2cf1f", "name": "Sticky Note5"}, {"parameters": {"content": "## Write the timeline\nHow does the story evolves", "height": 280, "width": 520, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, 460], "id": "690c9516-2fa5-4454-a3c1-42e235ec96f6", "name": "Sticky Note6"}, {"parameters": {"content": "## Write a plot \nMap out basic course of events, twists and more", "height": 280, "width": 400, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2280, 160], "id": "5f495274-d64c-4ce9-ad22-776c34e80c55", "name": "Sticky Note7"}, {"parameters": {"content": "## Enhance the story\nApply the cards to make the story better", "height": 280, "width": 960, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1300, 160], "id": "f14a6431-f6ec-4eef-873d-2a64f07f1811", "name": "Sticky Note8"}, {"parameters": {"content": "## Pick story cards\nWhich concepts will drive the story forward", "height": 280, "width": 600, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, 160], "id": "fc8cb6e2-c55c-4358-abcc-c5ee9dbde3e2", "name": "Sticky Note9"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"output_format\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"key_theme\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"goal\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"emotions\": {\n\t\t\t\"type\": \"array\",\n\t\t\t\"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t},\n\t\t\"higher_concepts\": {\n\t\t\t\"type\": \"array\",\n\t\t\t\"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t},\n\t\t\"questions_for_user\": {\n\t\t\t\"type\": \"array\",\n\t\t\t\"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t},\n\t\t\"unique_story_delivery\": {\n\t\t\t\"type\": \"array\",\n\t\t\t\"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t}\n\t},\n    \"required\": [\"output_format\",\"key_theme\",\"goal\",\"emotions\",\"higher_concepts\",\"questions_for_user\",\"unique_story_delivery\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2540, -400], "id": "5d2abf13-f144-44ac-9704-4cbf77251f2e", "name": "json schema"}, {"parameters": {"promptType": "define", "text": "=User Input: {{ $('$INPUTS$').item.json.input }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\nStory Baseline:\n\n{{ $('story baseline').item.json.output.toJsonString() }}\n\nStory Plot So Far\n\n{{ $json.output.story_timeline.toJsonString() }}\n\nStory Cards:\n\n{{ $('pick cards').item.json.output.cards.toJsonString() }} \n\nCharacters:\n\n{{ $('characters').item.json.output.toJsonString() }}\n\nTimeline\n\n{{ $('story timeline').item.json.output.toJsonString() }}\n\n", "hasOutputParser": true, "options": {"systemMessage": "=Write the story from this configuration. Your whole reaponse should be the whole story start to ffinish. Do not start with sometheing like \"Sure, here is the story...\" or something similar - just write an engaging story full of dialog, journey, conflict, plot twist, intrigue, satisfying resolution that could teach the reader something subliminally and more.\n\nMake sure to write the story in full. Do not cut it off prematurely. Also write a story title. \n\nIf multiple characters are involved - make sure to have dialogs, monologs, interruptions, and so on. Have fun and make the story dynamic.\n\nReply in JSON\nReply in the language of the original user input\n\nWrite it as long as it's needed to be"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1300, 560], "id": "b6de0d85-6488-46ed-a422-662bdbba8927", "name": "story draft", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 100}, {"parameters": {"promptType": "define", "text": "=Story Draft:\n\n{{ $('story draft').item.json.output }}\n", "hasOutputParser": true, "options": {"systemMessage": "=User Input: {{ $('$INPUTS$').item.json.input }}\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\nStory Baseline:\n\n{{ $('story baseline').item.json.output.toJsonString() }}\n\nStory Plot So Far\n\n{{ $json.output.toJsonString() }}\n\nStory Cards:\n\n{{ $('pick cards').item.json.output.cards.toJsonString() }} \n\nCharacters:\n\n{{ $('characters').item.json.output.toJsonString() }}\n\nTimeline:\n\n{{ $('story timeline').item.json.output.toJsonString() }}\n\nAct as a sharp, no-bullshit editor. Your job: Identify clichés, boring scenes, stale situations, weak stakes, flat characters, watered-down dialogue, or uninspiring parts in this story draft.\n\nBe brutally honest. Ground your critique in authentic, everyday human struggles—no forced DEI fluff. If the story is serious, suggest how to make plot twists emotionally gut-wrenching. If humorous, recommend ways to heighten the comedy through relatable adult life experiences.\n\nSubtly weave in realistic themes related to health, wealth, and relationships.\n\nExplicitly address only the story drafts and point out bad writing and how to un-cliche it.\n\nSuggest 5 better story titles.\n\nMost importantly, see if the current plan still matches original user input or if we lose the requirement (mismatching the requested format, misunderstanding the irony in the request, missing key intent, etc of this nature), in which case correct it.\n\nReply in JSON\nReply in the language of the original user input"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1800, 560], "id": "d9b577d2-76a7-4d8e-ab5a-7ac0b14907ab", "name": "edit notes", "retryOnFail": true, "maxTries": 5, "waitBetweenTries": 100}, {"parameters": {"content": "## Write the draft\nFirst attempt at the story", "height": 280, "width": 440, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1220, 460], "id": "3ef023fa-b434-4f4d-9f5a-8fa73fb2e13a", "name": "Sticky Note10"}, {"parameters": {"content": "## Write the story\nWrite the final story version", "height": 280, "width": 560, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2120, 460], "id": "a4ad6eea-cffe-454c-8d5d-ca9083fe7f4a", "name": "Sticky Note11"}, {"parameters": {"content": "## Save results\nCreate a google drive file with the story", "height": 260, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, 760], "id": "aa9cb55e-f07a-4d88-a605-4dcfaf10d4ea", "name": "Sticky Note12"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"characters\": {\n      \"type\": \"array\",\n      \"minItems\": 1,\n      \"maxItems\": 7,\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Realistic, believable name (or nickname), or 'Unknown' if intentionally mysterious.\"\n          },\n          \"occupation\": {\n            \"type\": \"string\",\n            \"description\": \"What they actually do to make money, survive, or hustle.\"\n          },\n          \"affiliations\": {\n            \"type\": \"string\",\n            \"description\": \"Who are they with.\"\n          },\n          \"demographics\": {\n            \"type\": \"string\",\n            \"description\": \"Sex, age, race, type, body traits,\"\n          },\n          \"appearance_and_style\": {\n            \"type\": \"string\",\n            \"description\": \"Memorable body type, scars, tattoos, embarrassing outfits, or signature items.\"\n          },\n          \"biggest_insecurities\": {\n            \"type\": \"string\",\n            \"description\": \"Real anxieties—money stress, envy, shame, or deep regrets.\"\n          },\n          \"core_motivation\": {\n            \"type\": \"string\",\n            \"description\": \"What truly drives them—greed, revenge, pride, survival, etc.\"\n          },\n          \"public_goal\": {\n            \"type\": \"string\",\n            \"description\": \"The goal they openly present to others.\"\n          },\n          \"hidden_goal\": {\n            \"type\": \"string\",\n            \"description\": \"Their true agenda or secret plan, hidden from others.\"\n          },\n          \"deepest_fear_or_desire\": {\n            \"type\": \"string\",\n            \"description\": \"Their absolute worst fear or most hidden yearning.\"\n          },\n          \"family_and_messy_past\": {\n            \"type\": \"string\",\n            \"description\": \"Brief snapshot of their troubled childhood or formative family drama.\"\n          },\n          \"quirks_and_habits\": {\n            \"type\": \"string\",\n            \"description\": \"Unique, vivid tics, catchphrases, addictions, or embarrassing mannerisms.\"\n          },\n          \"special_skill_or_talent\": {\n            \"type\": \"string\",\n            \"description\": \"Something they're undeniably good at—practical or absurd, useful or oddly specific.\"\n          },\n          \"defining_moment\": {\n            \"type\": \"string\",\n            \"description\": \"A specific trauma or event that permanently shaped who they are.\"\n          }\n        },\n        \"required\": [\n          \"name\",\n          \"occupation\",\n          \"affiliations\",\n          \"demographics\",\n          \"appearance_and_style\",\n          \"biggest_insecurities\",\n          \"core_motivation\",\n          \"public_goal\",\n          \"hidden_goal\",\n          \"deepest_fear_or_desire\",\n          \"family_and_messy_past\",\n          \"quirks_and_habits\",\n          \"special_skill_or_talent\",\n          \"defining_moment\"\n        ]\n      }\n    }\n  },\n  \"required\": [\"characters\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2400, -80], "id": "5ef977f9-86c0-4ab0-aba3-f7e37728f5e1", "name": "character json"}, {"parameters": {"content": "## Set ground rules\nSet the format, tone, basic story rules", "height": 320, "width": 500, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, -180], "id": "38f5dffc-d2bb-4c9b-b257-2af6fc8b7d30", "name": "Sticky Note13"}, {"parameters": {"promptType": "define", "text": "={{ $json.input }}", "hasOutputParser": true, "options": {"systemMessage": "Your job: Process the input provided by the user and clearly categorize what kind of story they're trying to create. Be explicit, specific, and direct—no vague terms or jargon.\n\nClearly identify and define:\n\nLanguage:\nWhat language the story will be written in.\n\nStory Type:\nCategorize the story. is it a non-fiction educational story about the actual events? is it a magical short novel. is it a sales email? stories come in different ways, analyze what kind of story format does user expect.\n\nCategory of Story:\nPrecisely label the type of story: thriller, romance, comedy, horror, slice-of-life, sales pitch, gritty realism, satire, etc.\n\nAudience Type:\nExplicitly state who it's meant for—adults, young adults, professionals, etc. No wishy-washy \"everyone\" nonsense.\n\nDominant Emotion:\nClearly name the primary emotion the story targets—envy, humor, regret, anxiety, nostalgia, etc. Make sure it hits deep and real.\n\nCore Theme:\nState one key theme that's authentic and relatable, tied to health, wealth, or relationships. No empty inspirational fluff.\n\nStorytelling Style:\nClearly indicate the storytelling style—dark comedy, emotional drama, thriller, satire, brutally honest realism, etc.\n\nRecommended Delivery:\nSuggest a unique and effective narrative approach—linear, nonlinear, monologue, unreliable narrator, epistolary, etc.\n\nHigh-Level Genre:\nClearly state genre (realistic fiction, dark humor, crime thriller, personal memoir, satire, romance, horror, etc.) without ambiguity.\n\nReply as JSON\n\nReply in the language of the original user input\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [720, -60], "id": "bde362d9-18c7-4e35-bda4-5487a01d2a07", "name": "story rules", "retryOnFail": true}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"language\": {\n      \"type\": \"string\",\n      \"description\": \"The language we will be writing the story in. English by defaullt, but adopt an input language if it's not English.\"\n    },\n    \"story_type\": {\n      \"type\": \"string\",\n      \"description\": \"Clearly defined type of story (e.g. non-fiction, sales letter, haiku, journal entries, cold email, or anything else that fits the task).\"\n    },\n    \"story_category\": {\n      \"type\": \"string\",\n      \"description\": \"Clearly defined category of story—no ambiguity.\"\n    },\n    \"target_audience\": {\n      \"type\": \"string\",\n      \"description\": \"Precisely who the story is intended for.\"\n    },\n    \"dominant_emotion\": {\n      \"type\": \"string\",\n      \"description\": \"One clear, powerful emotion driving the narrative.\"\n    },\n    \"core_theme\": {\n      \"type\": \"string\",\n      \"description\": \"Central relatable theme anchoring the entire story.\"\n    },\n    \"narrative_style\": {\n      \"type\": \"string\",\n      \"description\": \"Clear description of the narrative delivery style.\"\n    },\n    \"unique_storytelling_method\": {\n      \"type\": \"string\",\n      \"description\": \"Specific, innovative storytelling method chosen.\"\n    },\n    \"high_level_genre\": {\n      \"type\": \"string\",\n      \"description\": \"Explicit story genre classification.\"\n    }\n  },\n  \"required\": [\n    \"dominant_emotion\",\n    \"core_theme\",\n    \"story_category\",\n    \"high_level_genre\"\n  ]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [940, -100], "id": "12b70183-36dc-4834-88b9-43560449ace8", "name": "rules json"}, {"parameters": {"model": "o3-mini", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "typeVersion": 1, "position": [2080, 860], "id": "e77b44ef-f80c-47d8-b416-0f652f67a70e", "name": "gpt-4o1", "credentials": {"azureOpenAiApi": {"id": "lZeJOwybJiWyoj0X", "name": "gpt-4o"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"story_plot\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"selected_cards\": {\n          \"type\": \"array\",\n          \"description\": \"List of user-selected cards explicitly used to shape this story plot.\",\n          \"items\": {\n            \"type\": \"string\"\n          },\n          \"minItems\": 1\n        },\n        \"opening_scene\": {\n          \"type\": \"string\",\n          \"description\": \"Sharp, clear opening scene designed to instantly grip readers.\"\n        },\n        \"immediate_tension\": {\n          \"type\": \"string\",\n          \"description\": \"Explicitly defined adult-level tension introduced immediately (e.g., financial crisis, infidelity, health scare).\"\n        },\n        \"plot_development\": {\n          \"type\": \"string\",\n          \"description\": \"Clear description of how each card chosen drives or intensifies tension, deepens conflict, or raises emotional stakes.\"\n        },\n        \"hidden_reveal\": {\n          \"type\": \"string\",\n          \"description\": \"Critical hidden information that readers only discover near the end, making it genuinely surprising yet believable.\"\n        },\n        \"major_plot_twist\": {\n          \"type\": \"string\",\n          \"description\": \"Explicit description of a powerful, unexpected twist—emotional, shocking, or darkly comedic—and why it deeply matters.\"\n        },\n        \"foreshadowing_clues\": {\n          \"type\": \"string\",\n          \"description\": \"Subtle details strategically placed early to hint at the plot twist. Clearly state these clues.\"\n        },\n        \"shock_factor\": {\n          \"type\": \"string\",\n          \"description\": \"Explicit rating of how shocking or hilarious the plot twist is, plus recommendations on amplifying impact.\"\n        },\n        \"adult_themes_explored\": {\n          \"type\": \"string\",\n          \"description\": \"Clearly identify adult themes explored subtly but realistically (money, health, relationships).\"\n        },\n        \"comedic_relief_moments\": {\n          \"type\": \"string\",\n          \"description\": \"Explicitly pinpoint scenes or interactions ideal for adult-level comedic relief.\"\n        },\n        \"memorable_ending\": {\n          \"type\": \"string\",\n          \"description\": \"Clearly define a memorable closing scene—emotional, insightful, or darkly humorous—to stick with readers long after the end.\"\n        }\n      },\n      \"required\": [\n        \"selected_cards\",\n        \"opening_scene\",\n        \"immediate_tension\",\n        \"plot_development\",\n        \"hidden_reveal\",\n        \"major_plot_twist\",\n        \"foreshadowing_clues\",\n        \"shock_factor\",\n        \"adult_themes_explored\",\n        \"comedic_relief_moments\",\n        \"memorable_ending\"\n      ]\n    }\n  },\n  \"required\": [\"story_plot\"]\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2540, 200], "id": "2f98a06a-54ba-41af-8104-89dfc2b188b7", "name": "Structured Output Parser1"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"story\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n        \"story_title\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t},\n    \"required\": [\"story\", \"story_title\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2480, 520], "id": "237eb833-f2e1-4d4f-90b1-4499f5560dbc", "name": "Structured Output Parser2"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"story\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n        \"story_title\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t},\n    \"required\": [\"story\", \"story_title\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1500, 500], "id": "439ea9b6-14f8-452b-a090-f1dd94c2f69f", "name": "Structured Output Parser3"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n   \"type\":\"object\",\n   \"properties\":{\n      \"idea_summary\":{\n         \"type\":\"string\",\n         \"description\":\"A short, sharp title summarizing the enhancement idea.\"\n      },\n      \"detailed_description\":{\n         \"type\":\"string\",\n         \"description\":\"A clear, direct explanation of exactly what happens in this enhancement and how it deepens plot or character arcs.\"\n      },\n      \"connection_to_inputs\":{\n         \"type\":\"string\",\n         \"description\":\"Explicit mention of at least two provided inputs (User Input, Story Baseline, Story Cards, Current Characters) clearly showing how they merge to strengthen the story.\"\n      },\n      \"potential_impact\":{\n         \"type\":\"string\",\n         \"description\":\"Clearly describe how this enhancement increases tension, emotion, humor, or depth in the overall narrative.\"\n      }\n   },\n   \"required\":[\n      \"idea_summary\",\n      \"detailed_description\",\n      \"connection_to_inputs\",\n      \"potential_impact\"\n   ]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1820, 180], "id": "713d1051-1841-4218-9d7e-a8a7405a885e", "name": "story enhancements json"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"editor_notes\": {\n\t\t\t\"type\": \"array\",\n\t\t\t\"items\": {\n\t\t\t\t\"type\": \"string\"\n\t\t\t}\n\t\t}\n\t}\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1980, 500], "id": "a417089c-3b0f-4bb5-bd83-5a7773c2675a", "name": "edit notes json"}, {"parameters": {"promptType": "define", "text": "=Story Draft:\n{{ $('story draft').first().json.output.toJsonString() }}\n\n\nStory Rules\n{{ $('story rules').first().json.output.toJsonString() }}\n\nStory Baseline:\n{{ $('story baseline').first().json.output.toJsonString() }}\n\nEdit instructions:\n\n{{ $('edit notes').first().json.output.toJsonString() }}\n\nMost importantly, see how you can shorten it by condencing long sentences into short ones, making dialog punchier, using simpler shorter words and making the story experience maeaningful and entertaining withouth being way too long.\n", "hasOutputParser": true, "options": {"systemMessage": "=Write the story from this draft. Your whole reaponse should be the whole story start to finish. Do not start with sometheing like \"Sure, here is the story...\" or something similar - just write an engaging story full of dialog, journey, conflict, plot twist, intrigue, satisfying resolution that could teach the reader something subliminally and more. Also write a better story title.\n\nIf multiple characters are interacting with each other - make sure to have dialogs, monologs, interruptions, and so on. Have fun and make the story dynamic.\n\nUser Input: {{ $('$INPUTS$').item.json.input }}\n\nStory Cards:\n\n{{ $('pick cards').item.json.output.cards.toJsonString() }} \n\nCharacters:\n\n{{ $('characters').item.json.output.toJsonString() }}\n\nTimeline\n\n{{ $('story timeline').item.json.output }}\n\nReply in JSON\nReply in the language of the original user input"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [2260, 560], "id": "50b841a3-8386-4c29-a384-a9afd784e690", "name": "story_final", "retryOnFail": true}, {"parameters": {"operation": "createFromText", "content": "={{ $('story_final').item.json.output.story.removeMarkdown() }}", "name": "={{ $now.format('yyyy-MM-dd') }} {{ $('story_final').item.json.output.story_title.removeMarkdown() }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "={{ $('$INPUTS$').item.json.google_drive_folder_id }}", "mode": "id"}, "options": {"convertToGoogleDocument": true}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [720, 860], "id": "298d49f8-9381-43e3-8aef-18929b2d4442", "name": "create_story_file", "credentials": {"googleDriveOAuth2Api": {"id": "HqXZHjVZMt4YwuDM", "name": "lo**<EMAIL>"}}}, {"parameters": {"content": "## End of Funnel", "height": 260, "width": 400}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1020, 760], "id": "1a4ed288-761f-47e9-ba9e-4a0a1c2c05c5", "name": "Sticky Note14"}, {"parameters": {"content": "## Params\nI am getting really good results with GPT-4o but feel free to experiment with other models", "height": 260, "width": 760, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1440, 760], "id": "b77cccd2-987d-4626-8731-5ce985ff4a59", "name": "Sticky Note15"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"story_timeline\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"selected_cards\": {\n          \"type\": \"array\",\n          \"description\": \"Only the story cards selected explicitly by the user.\",\n          \"items\": {\n            \"type\": \"string\"\n          },\n          \"minItems\": 1\n        },\n        \"main_timeline\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"starting_point\": {\n              \"type\": \"string\",\n              \"description\": \"Sharp, vivid opening moment or setting to immediately hook readers—no vague intros.\"\n            },\n            \"core_tension\": {\n              \"type\": \"string\",\n              \"description\": \"Clearly defined tension from the beginning, escalating consistently, resolved only at the end.\"\n            },\n            \"hidden_reveal\": {\n              \"type\": \"string\",\n              \"description\": \"Specific critical information revealed only at the end—must genuinely surprise.\"\n            },\n            \"plot_twist\": {\n              \"type\": \"string\",\n              \"description\": \"Explicit, unexpected, emotional or comedic major twist.\"\n            },\n            \"foreshadowing_clues\": {\n              \"type\": \"string\",\n              \"description\": \"Subtle early hints to make twist believable yet unexpected.\"\n            },\n            \"shock_value\": {\n              \"type\": \"string\",\n              \"description\": \"Clearly evaluate how shocking or hilarious the twist is—suggest ways to amplify impact.\"\n            },\n            \"adult_themes\": {\n              \"type\": \"string\",\n              \"description\": \"Explicit identification of adult themes (money trouble, relationship drama, health struggles) explored realistically.\"\n            },\n            \"comedic_relief\": {\n              \"type\": \"string\",\n              \"description\": \"Clearly pinpoint specific moments to inject relatable adult humor.\"\n            },\n            \"strong_ending\": {\n              \"type\": \"string\",\n              \"description\": \"Explicitly describe an emotionally powerful or humorously insightful closing scene.\"\n            }\n          },\n          \"required\": [\n            \"starting_point\",\n            \"core_tension\",\n            \"hidden_reveal\",\n            \"plot_twist\",\n            \"adult_themes\",\n            \"strong_ending\"\n          ]\n        },\n        \"before_story\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"story_duration\": {\n              \"type\": \"string\",\n              \"description\": \"Clear, specific story duration—realistic and aligned with story’s scope.\"\n            },\n            \"global_context\": {\n              \"type\": \"string\",\n              \"description\": \"Realistic global or societal events briefly summarized.\"\n            },\n            \"immediate_setup\": {\n              \"type\": \"string\",\n              \"description\": \"Directly relevant events immediately preceding story’s start.\"\n            },\n            \"pre_story_tensions\": {\n              \"type\": \"string\",\n              \"description\": \"Realistic tensions or unresolved issues in characters’ lives just before story begins.\"\n            },\n            \"character_history\": {\n              \"type\": \"string\",\n              \"description\": \"Clear summary of relevant events in character lives (1 day, 1 month, 1 year ago).\"\n            }\n          },\n          \"required\": [\n            \"story_duration\",\n            \"immediate_setup\",\n            \"pre_story_tensions\",\n            \"character_history\"\n          ]\n        },\n        \"during_story\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"current_setting\": {\n              \"type\": \"string\",\n              \"description\": \"Exactly where the characters find themselves as story opens—be blunt, specific, vivid.\"\n            },\n            \"immediate_hook\": {\n              \"type\": \"string\",\n              \"description\": \"Immediate event clearly hooking readers from the first lines.\"\n            },\n            \"character_interactions\": {\n              \"type\": \"string\",\n              \"description\": \"Specific interactions building character depth and realism.\"\n            },\n            \"plot_twist_moment\": {\n              \"type\": \"string\",\n              \"description\": \"Explicit timing or scenario when plot twist occurs.\"\n            }\n          },\n          \"required\": [\n            \"current_setting\",\n            \"immediate_hook\",\n            \"plot_twist_moment\"\n          ]\n        },\n        \"after_story\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"after_1_day\": {\n              \"type\": \"string\",\n              \"description\": \"Immediate realistic aftermath one day after story ends.\"\n            },\n            \"after_1_month\": {\n              \"type\": \"string\",\n              \"description\": \"Logical consequences or changes one month after.\"\n            },\n            \"after_1_year\": {\n              \"type\": \"string\",\n              \"description\": \"Clearly outlined realistic life changes after one year.\"\n            },\n            \"after_10_years\": {\n              \"type\": \"string\",\n              \"description\": \"Believable long-term outcomes 10 years later.\"\n            }\n          },\n          \"required\": [\n            \"after_1_year\",\n            \"after_10_years\"\n          ]\n        }\n      },\n      \"required\": [\"selected_cards\", \"main_timeline\", \"before_story\", \"during_story\", \"after_story\"]\n    }\n  },\n  \"required\": [\"story_plot\"]\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [940, 500], "id": "2c7ee24e-e5ef-48cd-981a-379a84a9b751", "name": "timeline json"}, {"parameters": {"content": "## This automation was brought to you by [BundlyAI](https://bundly.ai).\n\nSave thousands of dollars by automating 50% of your business development.\n\n## Get 100 Free credits with code N8N", "height": 360, "width": 300, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2380, -820], "id": "0e05e8b7-4cdb-4c41-883f-c8b12bb47f37", "name": "Sticky Note17"}, {"parameters": {"content": "## For custom builds or if you need help setting up this workflow reach out to <NAME_EMAIL>", "height": 260, "width": 460, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2220, 760], "id": "25265f0f-9c8c-4717-ad43-428f3c758311", "name": "Sticky Note18"}, {"parameters": {"content": "# Welcome to the Storytelling Wizard automation! \n\n", "height": 360, "width": 460, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [680, -820], "id": "f2c434e3-ca7a-4c68-99b8-65f3c05008f0", "name": "Sticky Note16"}, {"parameters": {"content": "## 1. Clearly write your story description and explicitly state the desired format (e.g., children's story, short story, sales email).\n\n## 2. Provide your Google Drive folder ID where the completed stories will be saved automatically.\n\n## 3. For optimal stories, iterate your prompt description around 5-7 times.\n\n## 4.Feel free to customize triggers and outputs as needed.\n\n## Enjoy automated storytelling made easy!", "height": 360, "width": 1200, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1160, -820], "id": "a1f06eb0-7b8d-4610-87bc-f07f0506912b", "name": "Sticky Note19"}], "pinData": {}, "connections": {"$INPUTS$": {"main": [[{"node": "story rules", "type": "main", "index": 0}]]}, "Sentiment Analysis": {"main": [[{"node": "Connect", "type": "main", "index": 0}], [{"node": "Convince", "type": "main", "index": 0}], [{"node": "Explain", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Lead", "type": "main", "index": 0}], [{"node": "Motivate", "type": "main", "index": 0}], [{"node": "<PERSON>ll", "type": "main", "index": 0}]]}, "Connect": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Convince": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Explain": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Impress": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Lead": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Motivate": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Sell": {"main": [[{"node": "prompt", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "pick cards", "type": "ai_outputParser", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "$INPUTS$", "type": "main", "index": 0}]]}, "prompt": {"main": [[{"node": "story baseline", "type": "main", "index": 0}]]}, "pick cards": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "story baseline": {"main": [[{"node": "characters", "type": "main", "index": 0}]]}, "story plot": {"main": [[{"node": "story timeline", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "story enhancement": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "characters": {"main": [[{"node": "pick cards", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}], [{"node": "story enhancement", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "story plot", "type": "main", "index": 0}]]}, "story timeline": {"main": [[{"node": "story draft", "type": "main", "index": 0}]]}, "json schema": {"ai_outputParser": [[{"node": "story baseline", "type": "ai_outputParser", "index": 0}]]}, "story draft": {"main": [[{"node": "edit notes", "type": "main", "index": 0}]]}, "edit notes": {"main": [[{"node": "story_final", "type": "main", "index": 0}]]}, "character json": {"ai_outputParser": [[{"node": "characters", "type": "ai_outputParser", "index": 0}]]}, "story rules": {"main": [[{"node": "Sentiment Analysis", "type": "main", "index": 0}]]}, "rules json": {"ai_outputParser": [[{"node": "story rules", "type": "ai_outputParser", "index": 0}]]}, "gpt-4o1": {"ai_languageModel": [[{"node": "story rules", "type": "ai_languageModel", "index": 0}, {"node": "story plot", "type": "ai_languageModel", "index": 0}, {"node": "pick cards", "type": "ai_languageModel", "index": 0}, {"node": "story enhancement", "type": "ai_languageModel", "index": 0}, {"node": "story baseline", "type": "ai_languageModel", "index": 0}, {"node": "story timeline", "type": "ai_languageModel", "index": 0}, {"node": "edit notes", "type": "ai_languageModel", "index": 0}, {"node": "Sentiment Analysis", "type": "ai_languageModel", "index": 0}, {"node": "characters", "type": "ai_languageModel", "index": 0}, {"node": "story draft", "type": "ai_languageModel", "index": 0}, {"node": "story_final", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "story plot", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "story_final", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser3": {"ai_outputParser": [[{"node": "story draft", "type": "ai_outputParser", "index": 0}]]}, "story enhancements json": {"ai_outputParser": [[{"node": "story enhancement", "type": "ai_outputParser", "index": 0}]]}, "edit notes json": {"ai_outputParser": [[{"node": "edit notes", "type": "ai_outputParser", "index": 0}]]}, "story_final": {"main": [[{"node": "create_story_file", "type": "main", "index": 0}]]}, "create_story_file": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "timeline json": {"ai_outputParser": [[{"node": "story timeline", "type": "ai_outputParser", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "00759c2a-7dc9-495d-8542-f92df6427871", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c468f3e9ec76d6a999f2a3139e7b38fe14df5109932c20629efadf9cb993c0f1"}, "id": "9zpeoQyGvasyzJth", "tags": [{"createdAt": "2025-02-10T17:44:44.231Z", "updatedAt": "2025-02-10T17:44:44.231Z", "id": "ijxUivpA86zSsyeZ", "name": "Public"}]}