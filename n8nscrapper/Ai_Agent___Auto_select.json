{"name": "Ai Agent - Auto select", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1220, -420], "id": "ecff8575-7c2b-4fe6-bf18-ee40583958cb", "name": "When chat message received", "webhookId": "462abc84-2aa9-48b1-8d92-c1fbd598060d"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1020, -180], "id": "7dafc4ca-6929-4463-b919-9301dc5e85e1", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "6dTFypqHZvE8ET3Z", "name": "OpenAi account"}}}, {"parameters": {"options": {"systemMessage": "You are a lightweight model router AI.\n\nYour task is to select the most suitable language model for the given user query. You must choose one model from the list below, and output **only the exact model ID**, with no extra text, explanation, or formatting.\n\nAvailable models:\n\n- anthropic/claude-3.5-sonnet  \n- anthropic/claude-3.5-haiku  \n- anthropic/claude-3.7-sonnet  \n- anthropic/claude-sonnet-4  \n- anthropic/claude-opus-4  \n- deepseek/deepseek-r1  \n- google/gemini-2.5-pro-preview  \n- google/gemini-2.5-flash-preview  \n- mistralai/mistral-large-2411  \n- mistralai/mixtral-8x22b-instruct  \n- mistralai/mistral-small-3.1-24b-instruct  \n- openai/gpt-4o  \n- openai/gpt-4o-mini  \n- openai/gpt-4-turbo  \n- x-ai/grok-3-beta  \n- x-ai/grok-3-mini-beta\n\nGuidelines:\n- Use **OpenAI** models for general reasoning, Q&A, and structured output  \n- Use **Claude** models for long-form content, legal or safer tone responses  \n- Use **<PERSON>** for high-end analysis, and **<PERSON>** for balance  \n- Use **Gemini** if the query involves images, charts, or multimodal inputs  \n- Use **DeepSeek** for tasks involving coding, logic, or mathematics  \n- Use **Mistral/Mixtral** for fast, cost-efficient responses or self-hosting needs  \n- Use **Grok** if ultra-fast response time is important\n\n⚠️ Output must be **only one model ID**, exactly as listed above.  \nNo quotes. No punctuation. No markdown. No comments. No extra lines."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1020, -420], "id": "f378a76c-0341-4c41-accb-b54ccb806794", "name": "AI Model Router"}, {"parameters": {"promptType": "define", "text": "={{ $('When chat message received').item.json.chatInput }}", "hasOutputParser": true, "options": {"systemMessage": "You are a helpful assistant"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-600, -420], "id": "e7b97c4d-c1e9-4ba7-9f20-a41fa66d7f7d", "name": "AI Agent"}, {"parameters": {"model": "={{ $json.output }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-580, -200], "id": "f00ea675-1db9-4e14-8dc7-a55d27b25533", "name": "OpenRouter Chat Model1", "credentials": {"openRouterApi": {"id": "YE4fgGAnFs7oLc5h", "name": "OpenRouter account"}}}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Model Router", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Model Router", "type": "ai_languageModel", "index": 0}]]}, "AI Model Router": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c17ee1e9-7abb-4e42-bd0c-747030957b83", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b20c1a22249a77e3587acdad476e33a7feb1a098de595996887840c1c60c39ba"}, "id": "2l9nXU0Uy1POIktX", "tags": [{"createdAt": "2025-04-14T11:49:08.906Z", "updatedAt": "2025-04-14T11:49:08.906Z", "id": "mRWtuJGQczue1RFe", "name": "YouTube"}]}