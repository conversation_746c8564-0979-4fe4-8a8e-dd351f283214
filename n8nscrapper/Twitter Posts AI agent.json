{"name": "My workflow 6", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1dAazVhOF_J0fM2b188aa7lJkz0jg0ORD9QUFcXAPZzE", "mode": "list", "cachedResultName": "Social media articles", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1dAazVhOF_J0fM2b188aa7lJkz0jg0ORD9QUFcXAPZzE/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1dAazVhOF_J0fM2b188aa7lJkz0jg0ORD9QUFcXAPZzE/edit#gid=0"}, "event": "rowAdded", "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [-1240, 100], "id": "a035472a-4b1f-4bdd-a56f-edb04b5877e8", "name": "Google Sheets Trigger", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "UAXntbZXP7QOpWSu", "name": "Google Sheets Trigger account"}}}, {"parameters": {"jsCode": "const rows = $input.all();\nconst lastRow = rows[rows.length - 1]?.json;\nreturn lastRow;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1020, 100], "id": "c2f49972-7357-4c74-9d16-2f68aa976b16", "name": "Code"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1000, 320], "id": "3fdf77bf-1c7a-4f6d-8cd3-c7bd9e8478f5", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "XUuhIwKDrgAobBOF", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.Links }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-800, 340], "id": "cda64a32-0cf3-4d2f-be26-c18fc3e0ed89", "name": "Simple Memory"}, {"parameters": {"operation": "sendAndWait", "sendTo": "<EMAIL>", "subject": "Approval required for Twitter post", "message": "={{ $('Code').item.json.Links }}{{ $json.output }}", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-440, 100], "id": "bb2a4c6c-0ee9-47cf-ae80-d4e47ea4de63", "name": "Gmail", "webhookId": "ef89aa3a-f12c-409b-975d-af085f782921", "credentials": {"gmailOAuth2": {"id": "REpG6PElIqFoQcQo", "name": "Gmail account 2"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "05c90162-7c65-4e0e-a24a-75821ea4c4ca", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-220, 100], "id": "007ee222-3d68-4adb-9b6c-644be7e08987", "name": "If"}, {"parameters": {"text": "={{ $('AI Agent').item.json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [0, 0], "id": "1a7f3b0e-98b0-4b2b-a803-f539225fbf20", "name": "X"}, {"parameters": {"promptType": "define", "text": "={{ $json.Links }}", "options": {"systemMessage": "An **AI agent** that analyzes links and drafts Twitter (X) posts works like a **smart social media assistant** that automates content creation. Here’s how it works in simple steps:  \\n\\n### **How an AI Agent Drafts Twitter Posts from Links**  \\n\\n#### **Step 1: Receive the Link**  \\n- The AI agent takes a **URL** (e.g., a news article, blog post, or YouTube video).  \\n- It can be manually provided by the user or automatically fetched from a content source.  \\n\\n#### **Step 2: Analyze the Content**  \\n- The AI scans the webpage, extracting **key details** like the headline, main points, and any important quotes.  \\n- It **summarizes** the content to keep it short and engaging for Twitter’s character limit.  \\n\\n#### **Step 3: Generate a Draft Post**  \\n- The AI **writes a tweet** based on the extracted information, making it:  \\n  ✅ Concise (within 280 characters)  \\n  ✅ Engaging (using hooks, hashtags, and emojis)  \\n  ✅ Relevant to the audience  \\n- Example:  \\n  *\\\"🚀 Big news in AI! OpenAI just launched a game-changing tool that automates content creation. Here's why it matters: [link] #AI #TechNews\\\"*  \\n\\n#### **Step 4: Add Enhancements (Optional)**  \\n- The AI **suggests relevant hashtags** to improve reach.  \\n- It can **attach an image, video, or GIF** if available.  \\n- It may also add **a call-to-action (CTA)** like “What are your thoughts?” or “Read more here!”  \\n\\n#### **Step 5: Review & Post**  \\n- The user can **approve, edit, or schedule** the tweet.  \\n- The AI can also **auto-post** at the best engagement time.  \\n\\n### **Bonus Features**  \\n✨ Can personalize posts based on your past tweets  \\n✨ Can reformat for threads or multiple variations  \\n✨ Can schedule multiple tweets for content consistency  \\n\\n💡 **This AI agent is perfect for content creators, marketers, and businesses who want to automate Twitter posting without losing quality!** 🚀  \\n\"\n        }"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-820, 100], "id": "b207254a-4355-4bd0-8534-bb061c2061f6", "name": "AI Agent1"}], "pinData": {}, "connections": {"Google Sheets Trigger": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Gmail": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "X", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ce29dc00-8e83-4ee1-96b3-2da6360ee359", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6c97abda19f53c864e448ccd73924e48de2c9faaca2a268f2ae17c7b52dd49ff"}, "id": "fSB6jCHwb2YqOTyT", "tags": []}