{"name": "🍭🤖 Google Gmail Agent", "nodes": [{"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-160, 400], "id": "ed29c29b-d878-486e-8f9a-3939353fecee", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "", "name": ""}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('TG Agent Trigger').item.json.message.chat.id }}", "contextWindowLength": 8}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-60, 400], "id": "54187bf5-b242-4b59-91a7-71d3d61219ba", "name": "Window Buffer Memory"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [40, 400], "id": "ce530bca-8704-461d-a27b-a784e012e7c6", "name": "Calculator"}, {"parameters": {"assignments": {"assignments": [{"id": "fe7ecc99-e1e8-4a5e-bdd6-6fce9757b234", "name": "text", "value": "={{ $json.message.text }}", "type": "string"}]}, "options": {}}, "id": "260a2e15-1d10-42bc-9cd6-e8c99b18703e", "name": "Set 'Text'", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [60, 160]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8c844924-b2ed-48b0-935c-c66a8fd0c778", "leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "id": "85919d91-0e90-4313-afd5-0375946d6729", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-200, 100]}, {"parameters": {"chatId": "={{ $('TG Agent Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "id": "a34ccdb1-681a-4de6-9d5e-98366e7ec6e2", "name": "Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [660, 100], "webhookId": "0a86a682-1883-4c1e-b973-ceb2c88280eb", "credentials": {"telegramApi": {"id": "", "name": ""}}}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "id": "09911cb3-fd28-4c14-ab2a-9f066c0c0a49", "name": "Download File", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [0, 0], "webhookId": "01b4a4eb-f2f4-4156-af9d-2a92a1a81552", "credentials": {"telegramApi": {"id": "", "name": ""}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "id": "289a5ef6-c725-41e3-94da-72e8c72b711d", "name": "Transcribe", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [120, 0], "credentials": {"openAiApi": {"id": "", "name": ""}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [-260, 400], "id": "3e3f8061-ec76-4eb7-962f-0be2d25685c8", "name": "Think"}, {"parameters": {"options": {"timezone": "America/New_York"}}, "type": "n8n-nodes-base.dateTimeTool", "typeVersion": 2, "position": [140, 400], "id": "60c5e6db-bba0-4ee7-ba96-509bbae4d3fa", "name": "Get Current Date"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-820, 460], "id": "0208479a-bc23-473c-bff2-eb2d4c71a9a0", "name": "When chat message received", "webhookId": "b802b387-91c8-4a6e-a47b-d9f5ba8bfbd8"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-600, 460], "id": "4db387d1-a51f-40bd-a2c1-bac4c8a3d357", "name": "When Executed by Another Workflow"}, {"parameters": {"content": "## Telegram Trigger \nCreate a bot: <PERSON><PERSON> @BotFather → /newbot → name → copy the HTTP API token.\n\nGet your chat ID:\nSend a message to the bot you just created (or add it to a group).\n\nDM @get_id_bot; it instantly replies with the chat ID of the last conversation.\n\nAdd credentials in n8n: Settings → Credentials → Telegram → paste the API token.\n\nTelegram Trigger node: Double-click to open the node, pick your credential, and add the chat ID from step @get_id_bot.\n\nActivate & Test: Toggle this workflow to 'Active', send a Telegram message, confirm the trigger fires. Done!", "height": 440, "width": 440}, "type": "n8n-nodes-base.stickyNote", "position": [-840, -120], "typeVersion": 1, "id": "012f8374-b72a-4a9f-967d-9742caf905d6", "name": "<PERSON><PERSON>"}, {"parameters": {"updates": ["message"], "additionalFields": {"chatIds": ""}}, "id": "cef91c1a-641b-4fd9-a15d-9964feb8cc45", "name": "TG Agent <PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-360, 100], "webhookId": "f40402d1-f537-4cc6-9855-730b05b5c900", "credentials": {"telegramApi": {"id": "", "name": ""}}}, {"parameters": {"content": "## Get Started\nAdd your credentials to all required nodes", "height": 80, "width": 340, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-60, -100], "typeVersion": 1, "id": "704766ec-1777-4344-9bbd-41180eb7cce1", "name": "Sticky Note3"}, {"parameters": {"content": "## Additional Trigger Options\nYou can use alternative trigger options instead of Telegram. Be sure to update the prompt value in the agent to match the output of your chosen trigger and deactivate the Telegram Response node.", "height": 280, "width": 580, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-900, 340], "typeVersion": 1, "id": "683815e3-a365-4a2f-97d9-8a67812466c4", "name": "Sticky Note1"}, {"parameters": {"operation": "getAll", "returnAll": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Return_All', ``, 'boolean') }}", "simple": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Simplify', ``, 'boolean') }}", "filters": {"q": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search', ``, 'string') }}", "receivedAfter": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_After', ``, 'string') }}", "receivedBefore": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_Before', ``, 'string') }}", "sender": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Sender', ``, 'string') }}"}, "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [300, 400], "id": "e04ab9a7-5c93-4d4f-96bd-474b6d2efb34", "name": "Gmail - Get All Emails", "webhookId": "c4c82411-4a61-49c1-b48e-eab4027d4e1d", "credentials": {"gmailOAuth2": {"id": "", "name": ""}}}, {"parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "emailType": "text", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"appendAttribution": false, "bccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('BCC', ``, 'string') }}", "ccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('CC', ``, 'string') }}"}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [780, 400], "id": "2ee8f933-803a-477a-a8f6-1056b076b692", "name": "Gmail - Send Email", "webhookId": "a1dea3f5-b8c7-42df-8e4f-71e1927f1347", "credentials": {"gmailOAuth2": {"id": "", "name": ""}}}, {"parameters": {"operation": "get", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', ``, 'string') }}", "simple": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Simplify', ``, 'boolean') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [460, 400], "id": "9aea1a47-004c-41a7-86aa-5decfa879f5f", "name": "Gmail - Get One Email", "webhookId": "79d52ae8-1415-438b-bb10-499645e405d2", "credentials": {"gmailOAuth2": {"id": "", "name": ""}}}, {"parameters": {"resource": "draft", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"bccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('BCC', ``, 'string') }}", "ccList": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('CC', ``, 'string') }}", "threadId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Thread_ID', ``, 'string') }}", "sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To_Email', ``, 'string') }}"}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [620, 400], "id": "36f6f985-faa0-4e8e-9d7d-f13621ec9292", "name": "Gmail - Create Draft", "webhookId": "c49a77ca-09b6-4444-ada6-3f7e2181fcbf", "credentials": {"gmailOAuth2": {"id": "", "name": ""}}}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "=## Agent Identity\nYou are the **G<PERSON> Agent**, an intelligent email automation specialist within n8n workflows. Execute Gmail operations with precision while maintaining security and deliverability best practices.\n\n## Available Tools\n\n### Tool 1: Gmail - Get All Emails\n**Function**: `getAll_message`\n**Purpose**: Retrieve multiple emails with filtering capabilities\n**Key Parameters**:\n- `query`: Gmail search syntax (e.g., \"from:<EMAIL> is:unread\")\n- `maxResults`: Limit returned emails (default: 10, max: 500)\n- `labelIds`: Filter by specific Gmail labels\n- `includeSpamTrash`: Include/exclude spam and trash\n\n**Execution Notes**:\n- Use Gmail search operators for precise filtering\n- Implement pagination for large result sets\n- Return email metadata + body content\n\n### Tool 2: Gmail - Get One Email\n**Function**: `get_message`\n**Purpose**: Retrieve single email by message ID\n**Key Parameters**:\n- `messageId`: Unique Gmail message identifier\n- `format`: Response format (full, metadata, minimal)\n\n**Use Cases**:\n- Fetch specific email after list operation\n- Get complete email details including attachments\n- Access reply-to and thread information\n\n### Tool 3: Gmail - Create Draft\n**Function**: `create_draft`\n**Purpose**: Create email drafts for review before sending\n**Required Parameters**:\n- `to`: Recipient email address(es)\n- `subject`: Email subject line\n- `body`: Email content (HTML or plain text)\n**Optional Parameters**:\n- `cc`, `bcc`: Additional recipients\n- `attachments`: File attachments array\n- `replyTo`: Custom reply-to address\n\n### Tool 4: Gmail - Send Email\n**Function**: `send_message`\n**Purpose**: Send emails directly or send existing drafts\n**Required Parameters**:\n- `to`: Recipient email address(es)\n- `subject`: Email subject line\n- `body`: Email content\n**Advanced Options**:\n- `threadId`: Reply to existing conversation\n- `importance`: Set email priority\n- `tracking`: Enable read receipts\n\n## Operational Protocols\n\n### Required Sequences\n1. **Email Processing Pipeline**: Get All → Get One → Process → Send/Draft\n2. **Draft Review Workflow**: Create Draft → Review → Send Email\n3. **Thread Management**: Get One (for threadId) → Send Email (with threadId)\n\n### Error Handling\n- **Authentication**: Auto-retry with exponential backoff\n- **Rate Limits**: Queue requests, respect Gmail API quotas (250 quota units/user/second)\n- **Delivery Failures**: Log bounce reasons, implement retry logic for temporary failures\n\n### Security Requirements\n- Validate recipient email formats\n- Sanitize email content to prevent injection attacks\n- Never log sensitive email content in plain text\n- Implement access controls for sensitive operations\n\n### Performance Optimization\n- Batch email retrieval when possible\n- Cache frequently accessed emails\n- Use minimal format for metadata-only operations\n- Implement smart filtering to reduce API calls\n\n## Response Format\n**Success**: Return operation result with metadata (message IDs, thread IDs, delivery status)\n**Failure**: Provide specific error type, actionable guidance, and suggested retry logic\n\nExecute all Gmail operations with enterprise-grade security, optimal performance, and reliable delivery tracking.\n\n## Final Notes\nUse your memory tool to find and keep track of document IDs and sheet IDs, etc.\n\nHere is the current date/time: {{ $now }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [320, 100], "id": "dc7354c1-847e-4fba-8cdd-524849d793b9", "name": "Gmail Agent"}, {"parameters": {"content": "![Agent Skins](https://agent-skins.nyc3.cdn.digitaloceanspaces.com/agent_skins_logo.png)\n## Agent Skins\n[Browse More Agents](https://agentskins.gumroad.com)", "height": 340, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-360, -280], "typeVersion": 1, "id": "94e52eb3-2154-45c0-a165-2b059a6ce585", "name": "Sticky Note2"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Gmail Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Gmail Agent", "type": "ai_memory", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "Switch": {"main": [[{"node": "Download File", "type": "main", "index": 0}], [{"node": "Set 'Text'", "type": "main", "index": 0}]]}, "Set 'Text'": {"main": [[{"node": "Gmail Agent", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Transcribe", "type": "main", "index": 0}]]}, "Transcribe": {"main": [[{"node": "Gmail Agent", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "Get Current Date": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "TG Agent Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[]]}, "Gmail - Get All Emails": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "Gmail - Get One Email": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "Gmail - Create Draft": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "Gmail - Send Email": {"ai_tool": [[{"node": "Gmail Agent", "type": "ai_tool", "index": 0}]]}, "Gmail Agent": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9a04b47b-a845-43ab-8c51-89c870b1078a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "82165f16ec120159b1af47043d75bae62a86c23c2786c49fff5bfbe22bbe4abe"}, "id": "MhCOGYcENnQKFyrC", "tags": [{"createdAt": "2025-06-04T21:20:28.169Z", "updatedAt": "2025-06-04T21:20:28.169Z", "id": "jSklMxQC4xwIi6I5", "name": "gumroad"}, {"createdAt": "2025-05-09T01:28:24.803Z", "updatedAt": "2025-05-09T01:28:24.803Z", "id": "pqNlWhNm70Ouwmsh", "name": "agent"}]}