{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [220, 80], "parameters": {}, "typeVersion": 1}, {"name": "Mock data", "type": "n8n-nodes-base.function", "position": [420, 80], "parameters": {"functionCode": "// Code here will run only once, no matter how many input items there are.\n// More info and help: https://docs.n8n.io/nodes/n8n-nodes-base.function\n\n\nreturn [ { json: { amount_USD: 50 } }, { json: { amount_USD: 20 } }, { json: { amount_USD: 60 } } ];"}, "typeVersion": 1}, {"name": "Summing function", "type": "n8n-nodes-base.function", "position": [660, 80], "parameters": {"functionCode": "// Code here will run only once, no matter how many input items there are.\n// More info and help: https://docs.n8n.io/nodes/n8n-nodes-base.function\n\n//Setup an empty variable to hold the count\nlet total = 0;\n\n//Loop over the incoming items of data\nfor (item of items) {\n  //For each item of data, add the amount_USD to our total counter\n  total += item.json.amount_USD;\n}\n\n//Returns a well formed JSON object with just the total_value . You can either add more data to this returned object OR use the set node afterwards to do it in a no-code way.\nreturn [ { json: { total_value: total } } ];"}, "typeVersion": 1}], "connections": {"Mock data": {"main": [[{"node": "Summing function", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Mock data", "type": "main", "index": 0}]]}}}