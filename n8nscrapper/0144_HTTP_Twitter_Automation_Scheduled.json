{"nodes": [{"name": "Request blablagues", "type": "n8n-nodes-base.httpRequest", "position": [750, 250], "parameters": {"url": "https://api.blablagues.net/?rub=images", "options": {}, "responseFormat": "string"}, "typeVersion": 1}, {"name": "Recup image", "type": "n8n-nodes-base.httpRequest", "position": [1000, 250], "parameters": {"url": "={{$node[\"Request blablagues\"].json[\"data\"][\"data\"][\"content\"][\"media\"]}}", "options": {}, "responseFormat": "file"}, "typeVersion": 1}, {"name": "At 17H image jokes", "type": "n8n-nodes-base.cron", "position": [500, 250], "parameters": {"triggerTimes": {"item": [{"hour": 17}]}}, "typeVersion": 1}, {"name": "Tweet image jokes", "type": "n8n-nodes-base.twitter", "position": [1250, 250], "parameters": {"text": "={{$node[\"Request blablagues\"].json[\"data\"][\"data\"][\"content\"][\"text\"]}}", "additionalFields": {"attachments": "data"}}, "credentials": {"twitterOAuth1Api": {"id": "", "name": ""}}, "typeVersion": 1}], "connections": {"Recup image": {"main": [[{"node": "Tweet image jokes", "type": "main", "index": 0}]]}, "At 17H image jokes": {"main": [[{"node": "Request blablagues", "type": "main", "index": 0}]]}, "Request blablagues": {"main": [[{"node": "Recup image", "type": "main", "index": 0}]]}}}