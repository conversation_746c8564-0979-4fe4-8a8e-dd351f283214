{"name": "DriveToRag", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [2600, 3500], "id": "5f92a20c-72df-48ed-97ee-16cdce0c53d4", "name": "Recursive Character Text Splitter"}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.1, "position": [2540, 3180], "id": "a747e22d-53f2-4927-8913-c9b4860facc5", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"type": "SHA256", "value": "={{ $json.text }}", "dataPropertyName": "hash"}, "type": "n8n-nodes-base.crypto", "typeVersion": 1, "position": [2460, 1440], "id": "d84fa70a-96d7-46c0-82f2-556a96232822", "name": "Generate Hash"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{  $json }}", "rightValue": "", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "id": "89afefe2-051c-42c4-8296-fce4d805fd53"}], "combinator": "and"}, "renameOutput": true, "outputKey": "No record"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0299eadf-a50d-42f5-8652-37c71ab39491", "leftValue": "={{ $json.hash }}", "rightValue": "={{ $('Generate Hash').item.json.hash }}", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Record & Hash match"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "75667908-cffd-4fe5-b50e-ae09459c4b76", "leftValue": "={{ $json.hash }}", "rightValue": "={{ $('Generate Hash').item.json.hash }}", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Hash mismatch"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [2840, 1440], "id": "a5c9edc7-3007-4e87-b55e-38c8a0013f35", "name": "Switch"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2580, 2580], "id": "1adf4c9c-2806-4b85-9247-59315ac37c47", "name": "Aggregate"}, {"parameters": {"assignments": {"assignments": [{"id": "16c056c5-8dc2-4e73-b40f-3a48dad0fba6", "name": "content", "value": "={{ $('Set data for hash calculation').item.json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2320, 3180], "id": "de31c123-0e3d-4e85-8046-e63d4a50cab8", "name": "Set up Text for Embedding"}, {"parameters": {"assignments": {"assignments": [{"id": "8fb078d4-c4fc-4c99-a44a-89edd69b0072", "name": "content", "value": "={{ $json.pages.map(p => p.markdown).join('\\n\\n') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1980, 1940], "id": "3246c749-8310-4a5c-947f-a9e129687afe", "name": "Aggregate Markdown"}, {"parameters": {"content": "## Google Docs\n\n### What does this section do?\nThis section handles the processing of Google Documents by using the Google Docs API to directly extract text content while preserving document structure and formatting, providing clean, structured text extraction without requiring file conversion for optimal content analysis and embedding generation\n\n### How to set it up?\nEnsure that the Google Credentials are set in \"Extract contents of Doc\" node", "height": 420, "width": 1340, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [780, 1140], "typeVersion": 1, "id": "7a8bdae9-2be2-4b8e-bb39-5d6ca492b3f3", "name": "Sticky Note2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a0d58ac5-4bff-474f-a3ab-fe525ed1ad6b", "leftValue": "={{ $json.text.replace(/[\\r\\n\\s]/g, '').length }}", "rightValue": 10, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1100, 1940], "id": "fa2218ce-0c23-40c3-ad24-05f7d114de31", "name": "OCR not needed ?"}, {"parameters": {"content": "## For new files\n\n### What does this section do?\nThis section handles newly discovered files by storing their calculated hash value in the record manager database, creating a baseline for future change detection and ensuring that the file's content signature is tracked for subsequent update comparisons\n\n### How to set it up?\nEnsure that the Supabase credentials are set in \"Store hash\" node", "height": 480, "width": 940, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [2160, 1760], "typeVersion": 1, "id": "314cac3e-7bff-4148-b10e-cacbfb16aa39", "name": "Sticky Note15"}, {"parameters": {"content": "## For old files\n\n### What does this section do?\nThis section handles previously processed files that have been updated by first deleting the outdated RAG vectors from the vector database, then aggregating the deletion results, and finally updating the hash record in the database with the new content hash to maintain accurate change tracking\n\n### How to set it up?\nEnsure that the Supabase credentials are set in \"Delete old RAG vectors\" & \"Update hash based on the new file\" node", "height": 540, "width": 940, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [2160, 2280], "typeVersion": 1, "id": "dbe13980-e874-4833-a749-c38991ec4646", "name": "Sticky Note16"}, {"parameters": {"content": "## Add vectors to Vector Store\n\n### What does this section do?\nThis section converts the processed text content into vector embeddings and stores them in the Supabase vector database by preparing the text for embedding, splitting it into manageable chunks using the Recursive Character Text Splitter, generating embeddings with OpenAI's embedding model, and finally storing the vectorized content in Supabase for retrieval-augmented generation (RAG) queries\n\n### How to set it up?\n1. Ensure that the Supabase credentials are set in \"Supabase Vector Store\" node\n2. Ensure that Open AI credentials are set in \"Embeddings OpenAI\" node", "height": 800, "width": 940, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [2160, 2860], "typeVersion": 1, "id": "8da8b5d9-973d-4762-b60c-128543b5e915", "name": "Sticky Note14"}, {"parameters": {"content": "# Pipeline to handle data deletion from G Drive", "height": 740, "width": 4020, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-840, 4040], "typeVersion": 1, "id": "d1f6c5f8-5f01-4219-9dac-233b504b0ad9", "name": "Sticky Note17"}, {"parameters": {"method": "POST", "url": "https://api.ocr.space/parse/image", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "REDACTED_OCR_SPACE_API_KEY"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1380, 3700], "id": "5323e07e-c067-4468-a6fc-364884cdcca3", "name": "Analyze Image using OCR Space"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "REDACTED_GOOGLE_DRIVE_FOLDER_ID", "mode": "list", "cachedResultName": "REDACTED_GOOGLE_DRIVE_FOLDER_NAME", "cachedResultUrl": "REDACTED_GOOGLE_DRIVE_FOLDER_URL"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [360, 1420], "id": "7ad14b24-a591-4980-b505-4104bad669b6", "name": "When file is added", "credentials": {"googleDriveOAuth2Api": {"id": "REDACTED_GOOGLE_DRIVE_OAUTH_API_ID", "name": "REDACTED_GOOGLE_DRIVE_OAUTH_API_NAME"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "REDACTED_GOOGLE_DRIVE_FOLDER_ID", "mode": "list", "cachedResultName": "REDACTED_GOOGLE_DRIVE_FOLDER_NAME", "cachedResultUrl": "REDACTED_GOOGLE_DRIVE_FOLDER_URL"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [360, 1620], "id": "aa235a9c-6d20-40a6-8917-fbe71834b167", "name": "When file is updated", "credentials": {"googleDriveOAuth2Api": {"id": "REDACTED_GOOGLE_DRIVE_OAUTH_API_ID", "name": "REDACTED_GOOGLE_DRIVE_OAUTH_API_NAME"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [160, 2540], "id": "1bbd9340-f4ff-4591-88f7-71182c133249", "name": "Loop Over file"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [380, 2540], "id": "5e4c94ac-6e5e-4b5a-8a6d-2978884652b0", "name": "Download file", "credentials": {"googleDriveOAuth2Api": {"id": "REDACTED_GOOGLE_DRIVE_OAUTH_API_ID", "name": "REDACTED_GOOGLE_DRIVE_OAUTH_API_NAME"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "9649f517-e92c-4f48-b87f-da9a31485b47"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Docs"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0058e284-bda8-47c7-8c66-ac662eb9de41", "leftValue": "={{ $json.mimeType }}", "rightValue": "=application/pdf", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "PDF"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1aec7c6c-ad23-4a60-86d1-34563b49558f", "leftValue": "={{ $json.mimeType }}", "rightValue": "text/html", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "HTML"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "9cf71251-d6f1-492f-b6a5-6676a676d0cc", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Sheets"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "9cc53b1c-ca64-48e8-803e-ac23efa7d148", "leftValue": "={{ $json.mimeType }}", "rightValue": "=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CSV"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d75054e3-5d48-406a-be11-7605a2e2a6b8", "leftValue": "={{ $json.mimeType }}", "rightValue": "=image/png", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "PNG"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "aae121f4-8ae3-4a0c-8220-12bd084a60a2", "leftValue": "={{ $json.mimeType }}", "rightValue": "={{ $json.mimeType }}", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "JPEG"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [560, 2540], "id": "52d44f35-b831-4111-b285-03541cf274d1", "name": "Handle all type of files"}, {"parameters": {"operation": "get", "documentURL": "={{ $json.id }}"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1340, 1380], "id": "fd3b728b-2754-40ad-8050-84d1bfa1ac2e", "name": "Extract contents of Doc", "credentials": {"googleDocsOAuth2Api": {"id": "REDACTED_GOOGLE_DOCS_OAUTH_API_ID", "name": "REDACTED_GOOGLE_DOCS_OAUTH_API_NAME"}}}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [880, 1940], "id": "95466434-32a3-4ec4-81c9-3238b3859dfb", "name": "Extract contents of PDF"}, {"parameters": {"method": "POST", "url": "https://api.mistral.ai/v1/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}, {"name": "purpose", "value": "ocr"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 1940], "id": "f2d7fa72-3006-4d6c-93f3-2c821dcedb0e", "name": "Upload files to Mistral for OCR", "credentials": {"mistralCloudApi": {"id": "REDACTED_MISTRAL_CLOUD_API_ID", "name": "REDACTED_MISTRAL_CLOUD_API_NAME"}}}, {"parameters": {"url": "=https://api.mistral.ai/v1/files//url {{ $json.id }}/url", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "expiry", "value": "24"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 1940], "id": "374b69de-e98b-46ba-9978-409f7385269c", "name": "Get OCR data from Mistral", "credentials": {"mistralCloudApi": {"id": "REDACTED_MISTRAL_CLOUD_API_ID", "name": "REDACTED_MISTRAL_CLOUD_API_NAME"}}}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1280, 2340], "id": "907eebad-c39c-4661-80f4-c8e078b2193c", "name": "Extract HTML"}, {"parameters": {"html": "={{ $json.data }}", "destinationKey": "content", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [1480, 2340], "id": "21c3f0e8-0c93-4d13-9cc1-4fe650dcbd0e", "name": "Convert HTML to Markdown"}, {"parameters": {"operation": "xlsx", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1180, 2760], "id": "31af7204-6ac1-4f4f-b5c1-fe56aacb0d4e", "name": "Extract data"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "b04a46af-30c1-48d5-a2a1-175bd860ccca", "name": "Aggregate data", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1380, 2760]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "02ae9c5e-2ae4-4775-aac0-f496af9df597", "name": "Summarize data", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1560, 2760]}, {"parameters": {"operation": "xlsx", "options": {}}, "id": "3f7c9dd4-5138-4fa0-ab93-0ec571eda258", "name": "Extract data from excel", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1180, 3160]}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "40a0a148-4a08-4ccd-9f32-601e0cdd8993", "name": "Aggregate data1", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1380, 3160]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "81142579-b6de-4128-9349-85710624f47e", "name": "Summarize data1", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1560, 3160]}, {"parameters": {"assignments": {"assignments": [{"id": "54881e41-8ac6-4dbf-9648-bd660f04ab0e", "name": "text", "value": "={{ $json.content || $json.text || $json.concatenated_data || $json.ParsedResults[0].ParsedText }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2300, 1440], "id": "3bc1cf02-8da3-4574-bc49-dfe3c9279c5d", "name": "Set data for hash calculation"}, {"parameters": {"operation": "getAll", "tableId": "record_manager", "limit": 1, "filters": {"conditions": [{"keyName": "google_drive_file_id", "condition": "eq", "keyValue": "={{ $('Loop Over file').item.json.id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2640, 1440], "id": "afc1e7ee-2dcb-423f-959c-89e60a79156c", "name": "Search if the hash exists", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"content": "## Check if the file is updated by calculating Hash\n\n### What does this section do?\nThis section implements intelligent change detection by generating a SHA256 hash of the extracted file content and comparing it against previously stored hashes to determine if the file has been modified, ensuring that only new or updated content is processed and preventing unnecessary reprocessing of unchanged files\n\n### How to set it up?\n1. Follow this guide to setup Supabase credentials:  [How to setup Supabase credentials](https://www.victorexplore.com/posts/n8n/connect-supabase-to-n8n/)\n2. Set Supabase credentials in \"Search if the hash exists\" node", "height": 580, "width": 940, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [2160, 1140], "typeVersion": 1, "id": "07941f51-e9fb-44a8-abd9-5cba439cfc6b", "name": "Sticky Note12"}, {"parameters": {"tableId": "record_manager", "fieldsUi": {"fieldValues": [{"fieldId": "hash", "fieldValue": "={{ $('Generate Hash').item.json.hash }}"}, {"fieldId": "google_drive_file_id", "fieldValue": "={{ $('Loop Over file').item.json.id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2580, 2040], "id": "e5439464-0827-4a02-8e4d-07dcf5a5ba0b", "name": "Store hash", "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $('Loop Over file').item.json.id }}*"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2360, 2580], "id": "894cf8e3-a686-4bf5-b733-30ec64f4fc48", "name": "Delete old RAG vectors", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"operation": "update", "tableId": "record_manager", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Search if the hash exists').item.json.id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "hash", "fieldValue": "={{ $('Generate Hash').item.json.hash }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2800, 2580], "id": "393f5f26-2e35-420d-8015-dbab7775615a", "name": "Update hash based on the new file", "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"options": {"metadata": {"metadataValues": [{"name": "file_id", "value": "={{ $('Loop Over file').item.json.id }}"}, {"name": "file_name", "value": "={{ $('Loop Over file').item.json.name }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2680, 3380], "id": "56b705bb-0471-44a2-9fb9-6020dd8d56dc", "name": "Data loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2460, 3380], "id": "3a6629b8-a46d-4236-99d0-72eae19746de", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "REDACTED_OPENAI_API_ID", "name": "REDACTED_OPENAI_API_NAME"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "REDACTED_GOOGLE_DRIVE_FOLDER_ID", "mode": "list", "cachedResultName": "REDACTED_GOOGLE_DRIVE_FOLDER_NAME", "cachedResultUrl": "REDACTED_GOOGLE_DRIVE_FOLDER_URL"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-480, 4480], "id": "46826bf5-1b59-4f59-8fb3-c0787553f40d", "name": "When file added to Recycle Bin", "credentials": {"googleDriveOAuth2Api": {"id": "REDACTED_GOOGLE_DRIVE_OAUTH_API_ID", "name": "REDACTED_GOOGLE_DRIVE_OAUTH_API_NAME"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [220, 4480], "id": "df7a1a77-b373-4d00-b773-d3f56e457f15", "name": "For every file"}, {"parameters": {"operation": "getAll", "tableId": "record_manager", "limit": 1, "filters": {"conditions": [{"keyName": "google_drive_file_id", "condition": "eq", "keyValue": "={{ $('For every file').item.json.id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 4480], "id": "91c41401-4c20-4cfc-b4a9-58213918b35e", "name": "Search records for the file", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "3cc69d7d-4af3-408a-935a-8f2a78d38566", "leftValue": "={{ $json.id }}", "rightValue": "", "operator": {"type": "number", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [780, 4480], "id": "dc856eff-07b3-4f30-a132-afc23835864e", "name": "Was file vectorized"}, {"parameters": {"operation": "delete", "tableId": "documents", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $('For every file').item.json.id }}*"}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1280, 4480], "id": "e3b7b9d9-3de9-4f79-b8cc-c8e48d61cc8a", "name": "Delete RAG vectors", "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"operation": "delete", "tableId": "record_manager", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $('Search records for the file').item.json.id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1920, 4480], "id": "268f1b23-c011-481c-9d33-046c90b5a15b", "name": "Delete records from hash table", "credentials": {"supabaseApi": {"id": "REDACTED_SUPABASE_API_ID", "name": "REDACTED_SUPABASE_API_NAME"}}}, {"parameters": {"operation": "deleteFile", "fileId": {"__rl": true, "value": "={{ $('For every file').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2700, 4480], "id": "8106df96-3fa5-4668-9d63-421f2f10e44f", "name": "Delete file from recycle bin", "alwaysOutputData": true, "disabled": true}, {"parameters": {"content": "# DriveToRAG Workflow Setup Instructions\n\nDriveToRAG automatically monitors your Google Drive folders, converts documents into vector embeddings for advanced retrieval-augmented generation (RAG) applications, and keeps your vector store synchronized with your Drive content.\n\n## 🗄️ Database Setup\n\n### 1. Documents Table (with Vector Support)\nRun this SQL code in your Supabase database to create the documents table and functions responsible for storing documents and their embeddings:\n```sql\n-- Enable the pgvector extension to work with embedding vectors\ncreate extension vector;\n\n-- Create a table to store your documents\ncreate table documents (\n  id bigserial primary key,\n  content text, -- corresponds to Document.pageContent\n  metadata jsonb, -- corresponds to Document.metadata\n  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed\n);\n\n-- Create a function to search for documents\ncreate function match_documents (\n  query_embedding vector(1536),\n  match_count int default null,\n  filter jsonb DEFAULT '{}'\n) returns table (\n  id bigint,\n  content text,\n  metadata jsonb,\n  similarity float\n)\nlanguage plpgsql\nas $$\n#variable_conflict use_column\nbegin\n  return query\n  select\n    id,\n    content,\n    metadata,\n    1 - (documents.embedding <=> query_embedding) as similarity\n  from documents\n  where metadata @> filter\n  order by documents.embedding <=> query_embedding\n  limit match_count;\nend;\n$$;\n```\n\n### 2. Record Manager Table\nRun this SQL code in your Supabase database to create the record manager table responsible for storing unique hashes of documents:\n```sql\ncreate table record_manager (\n  id serial primary key,\n  google_drive_file_id text unique not null,\n  hash text not null\n);\n```\n\n## ⚙️ Nodes Requiring Configuration\nBased on the workflow diagram, the nodes are organized by configuration requirements:\n\n**🔴 Red Section - Nodes Requiring Configuration:**\nAll nodes in the red sections of the workflow require setup with your credentials and settings. These include:\n- Google Drive trigger nodes\n- OpenAI/LLM nodes for embeddings\n- Supabase database connection nodes\n- OCR processing nodes\n- Any API authentication nodes\n\nDetailed setup instructions and links to helpful resources are provided directly within each red section alongside the nodes that require configuration.\n\n**🟢 Green Section - Nodes Not Requiring Configuration:**\nNodes in the green sections are pre-configured logic and processing nodes that work automatically once the red nodes are properly set up. These include:\n- Data transformation nodes\n- Flow control nodes\n- Built-in processing functions\n- Conditional logic nodes\n\n## 🧪 Testing the Workflow\n\nAfter configuring all nodes:\n1. Activate the workflow\n2. Upload a file to your monitored Google Drive folder\n3. Monitor the workflow execution in n8n\n4. Verify that vectors are stored in your Supabase database\n\n## 🆘 Need Help?\nIf you encounter any issues or need assistance with:\n- Setting up the automation\n- Customizing the workflow for your specific needs\n- Troubleshooting technical problems\n- Adding new features or integrations\n- Building new automation workflows from scratch\n\nMessage me on [X @Victor_explore](https://x.com/Victor_explore) to hire the services of my agency.\n\n## 🙏 If You Found This Helpful:\n- ⭐️ **Give this automation a 5-star rating** and leave a review to help others discover it\n- 📤 **Share it** with colleagues and friends who might benefit\n- 🐦 **Follow me** on [X @Victor_explore](https://x.com/Victor_explore) for more automation workflows\n- 💬 **Join the conversation** - tag me when you share your results!\n\nYour feedback and success stories help improve this workflow for everyone! 🚀\n", "height": 2920, "width": 860, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-840, 1040], "typeVersion": 1, "id": "6c8f5a34-fa70-4f4f-8889-f75789d99996", "name": "Sticky Note3"}, {"parameters": {"content": "## Handle every file\n\n### What does this section do?\nThis section processes each triggered file individually through a systematic workflow:\n\n1. **Loop Over file** - Uses batch processing to handle files one at a time, ensuring proper resource management and error handling\n2. **Download file** - Downloads the actual file content from Google Drive to make it available for processing\n3. **Handle all type of files** - Routes each file to the appropriate processing path based on its MIME type:\n   - **Docs** - Google Documents\n   - **PDF** - PDF files\n   - **HTML** - HTML files  \n   - **Sheets** - Google Sheets\n   - **CSV** - Excel files\n   - **PNG** - PNG images\n   - **JPEG** - JPEG images\n\nThis routing system ensures that each file type is processed using the most appropriate extraction method for optimal content retrieval\n\n### How to set it up?\nEnsure that the created Google Credentials are set in \"Download file\" Node", "height": 1040, "width": 620, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [120, 1900], "typeVersion": 1, "id": "3e6061a7-232f-4236-828b-7b35f621f1b1", "name": "Sticky Note11"}, {"parameters": {"content": "## PDF\n\n### What does this section do?\nThis section handles PDF processing with intelligent OCR fallback - it first attempts to extract text directly from the PDF, and if the content is insufficient (less than 10 characters), it automatically processes the PDF through Mistral AI's OCR service to extract text from image-based or scanned PDFs, then aggregates the extracted content into markdown format for further processing\n\n### How to set it up?\n1. Follow this guide to get Mistral API key:  [How to get Mistral API Keys](https://www.victorexplore.com/posts/n8n/generate-mistral-api-keys/)\n2. Setup Mistral API keys in following nodes:\n   - Upload files to Mistral for OCR\n   - Get OCR data from Mistral\n   - HTTP Request to Mistral\n", "height": 540, "width": 1340, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [780, 1600], "typeVersion": 1, "id": "545630c9-5d74-466e-9a6d-50085bd049b0", "name": "Sticky Note4"}, {"parameters": {"content": "## HTML\n\n### What does this section do?\nThis section processes HTML files by first extracting the raw HTML content from the file, then converting it to clean markdown format for improved readability and consistent text processing across different file types", "height": 360, "width": 1340, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [780, 2180], "typeVersion": 1, "id": "134a6d8a-ed1a-4f8c-9080-bd910640b79f", "name": "Sticky Note5"}, {"parameters": {"content": "## Google Sheets\n\n### What does this section do?\nThis section processes Google Sheets by extracting data from the spreadsheet format, aggregating all the data from multiple sheets or rows, and then summarizing the content by concatenating it into a single unified text format for embedding generation", "height": 360, "width": 1340, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [780, 2580], "typeVersion": 1, "id": "fffc4ecc-999c-4517-acec-87e69c979174", "name": "Sticky Note6"}, {"parameters": {"content": "## Excel\n\n### What does this section do?\nThis section processes Excel files by extracting data from XLSX format spreadsheets, aggregating all the data from multiple sheets or rows, and then summarizing the content by concatenating it into a single unified text format for embedding generation", "height": 360, "width": 1340, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [780, 2980], "typeVersion": 1, "id": "d702b6d2-49bc-4ee3-94cf-2375e4b4ed5f", "name": "Sticky Note7"}, {"parameters": {"content": "## Images\n\n### What does this section do?\nThis section processes image files (PNG and JPEG) by using OCR Space API to analyze and extract any text content found within the images, converting visual text into machine-readable format for further processing and embedding generation\n\n### How to set it up?\n1. Follow this guide to get OCR Space API key:  [How to get OCR Space API Keys](https://www.victorexplore.com/posts/n8n/generate-ocr-space-api-keys/)\n2. Add credentials in \"Analyze Image using ICR Space\" node.", "height": 520, "width": 1340, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [780, 3380], "typeVersion": 1, "id": "77c93fc6-f42d-47fb-9723-f78d8faadf0e", "name": "Sticky Note8"}, {"parameters": {"content": "## When files are added or updated in Google Drive\n\n### What does this section do?\nThis section handles the main processing pipeline triggered by:\n1. **When file is added** - Monitors for new files in the specified Google Drive folder\n2. **When file is updated** - Monitors for changes to existing files in the same folder\n\n### How to set it up?\nYou can follow this guide to set up these nodes: [How to Connect Google Drive To n8n](https://www.victorexplore.com/posts/n8n/connect-google-drive-to-n8n/)", "height": 720, "width": 620, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [120, 1140], "typeVersion": 1, "id": "b6447b7e-4541-4716-9ae0-52eeb19e44d2", "name": "Sticky Note13"}, {"parameters": {"content": "## Trigger\n\n### What does this section do?\nThis section monitors the Google Drive Recycle Bin folder for files that have been deleted, triggering the cleanup workflow whenever a file is moved to the recycle bin to ensure corresponding vector embeddings and database records are properly removed from the system.\n\n### How to set it up?\n1. Ensure that the Google Credentials are set in \"When file added to Recycle Bin\" Node\n2. Also ensure that you have created a folder to act as recycle bun in your Google Drive. Set this folder in \"When file added to Recycle Bin\" node. Now whenever you move file from the folder being monitored to the recycle bin this sub automation will run", "height": 560, "width": 740, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-780, 4140], "typeVersion": 1, "id": "8448db54-8830-4f35-9499-a6448bda13fb", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Handle every file\n\n### What does this section do?\nThis section processes each deleted file individually by looping through files found in the recycle bin, searching the record manager database to check if the file was previously processed and vectorized, and determining whether cleanup actions are needed based on whether the file exists in the system records.\n\n### How to set it up?\nEnsure that the Supabase credentials are set in \"Search records for the file\" node", "height": 560, "width": 1080, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [20, 4140], "typeVersion": 1, "id": "cf91aab6-3c52-46a8-bb1f-198eb91a082c", "name": "Sticky Note1"}, {"parameters": {"content": "## Delete records\n\n### What does this section do?\nThis section performs comprehensive cleanup for deleted files by removing all associated RAG vectors from the vector database, aggregating the deletion results, and then removing the corresponding records from the hash table to ensure no orphaned data remains in the system.\n\n### How to set it up?\nEnsure that the Supabase credentials are set in \"Delete RAG vectors\" & \"Delete records from hash table\" nodes", "height": 560, "width": 1080, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [1140, 4140], "typeVersion": 1, "id": "3f21f4f0-1fbc-4163-a689-2a0e64e651ab", "name": "Sticky Note9"}, {"parameters": {"content": "## Delete file from recycle bin (optional)\n\n### What does this section do?\nThis section provides an optional final step to permanently delete files from the recycle bin after all associated records and vectors have been cleaned up from the system, ensuring complete removal of the file from Google Drive storage (currently deactivated by default).\n\n### How to set it up?\nEnsure that the created Google Credentials are set in \"Delete file from recycle bin\" Node", "height": 560, "width": 840, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [2260, 4140], "typeVersion": 1, "id": "55e646bf-6be8-4a05-8a3c-af244f267604", "name": "Sticky Note10"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1600, 4480], "id": "b12a40c2-433c-4b77-b4eb-88289f159224", "name": "Aggregates data"}, {"parameters": {"method": "POST", "url": "=https://api.mistral.ai/v1/ocr", "authentication": "predefinedCredentialType", "nodeCredentialType": "mistralCloudApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n\"model\":\"mistral=ocr-latest\",\n\"document\": {\n  \"type\":\"document_url\",\n  \"document_url\": \"{{ $json.url }}\"\n},\n\"include_image_base64\": true\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1780, 1940], "id": "54483272-a111-4502-8d1d-816b8de4cce0", "name": "HTTP Request to Mistral", "credentials": {"mistralCloudApi": {"id": "REDACTED_MISTRAL_CLOUD_API_ID", "name": "REDACTED_MISTRAL_CLOUD_API_NAME"}}}, {"parameters": {"content": "# Pipeline to add Google Drive data to RAG Vector Store", "height": 2920, "width": 3100, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [60, 1040], "typeVersion": 1, "id": "44f6db09-41b8-49f1-a905-b260dcb5e36c", "name": "Sticky Note19"}], "pinData": {}, "connections": {"Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Data loader", "type": "ai_textSplitter", "index": 0}]]}, "Generate Hash": {"main": [[{"node": "Search if the hash exists", "type": "main", "index": 0}]]}, "Supabase Vector Store": {"main": [[{"node": "Loop Over file", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Store hash", "type": "main", "index": 0}], [{"node": "Loop Over file", "type": "main", "index": 0}], [{"node": "Delete old RAG vectors", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Update hash based on the new file", "type": "main", "index": 0}]]}, "Set up Text for Embedding": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Aggregate Markdown": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}]]}, "OCR not needed ?": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}], [{"node": "Upload files to Mistral for OCR", "type": "main", "index": 0}]]}, "Analyze Image using OCR Space": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}]]}, "When file is added": {"main": [[{"node": "Loop Over file", "type": "main", "index": 0}]]}, "When file is updated": {"main": [[{"node": "Loop Over file", "type": "main", "index": 0}]]}, "Loop Over file": {"main": [[], [{"node": "Download file", "type": "main", "index": 0}]]}, "Download file": {"main": [[{"node": "Handle all type of files", "type": "main", "index": 0}]]}, "Handle all type of files": {"main": [[{"node": "Extract contents of Doc", "type": "main", "index": 0}], [{"node": "Extract contents of PDF", "type": "main", "index": 0}], [{"node": "Extract HTML", "type": "main", "index": 0}], [{"node": "Extract data", "type": "main", "index": 0}], [{"node": "Extract data from excel", "type": "main", "index": 0}], [{"node": "Analyze Image using OCR Space", "type": "main", "index": 0}], [{"node": "Analyze Image using OCR Space", "type": "main", "index": 0}]]}, "Extract contents of Doc": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}]]}, "Extract contents of PDF": {"main": [[{"node": "OCR not needed ?", "type": "main", "index": 0}]]}, "Upload files to Mistral for OCR": {"main": [[{"node": "Get OCR data from Mistral", "type": "main", "index": 0}]]}, "Get OCR data from Mistral": {"main": [[{"node": "HTTP Request to Mistral", "type": "main", "index": 0}]]}, "Extract HTML": {"main": [[{"node": "Convert HTML to Markdown", "type": "main", "index": 0}]]}, "Convert HTML to Markdown": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}]]}, "Extract data": {"main": [[{"node": "Aggregate data", "type": "main", "index": 0}]]}, "Aggregate data": {"main": [[{"node": "Summarize data", "type": "main", "index": 0}]]}, "Summarize data": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}]]}, "Extract data from excel": {"main": [[{"node": "Aggregate data1", "type": "main", "index": 0}]]}, "Aggregate data1": {"main": [[{"node": "Summarize data1", "type": "main", "index": 0}]]}, "Summarize data1": {"main": [[{"node": "Set data for hash calculation", "type": "main", "index": 0}]]}, "Set data for hash calculation": {"main": [[{"node": "Generate Hash", "type": "main", "index": 0}]]}, "Search if the hash exists": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Store hash": {"main": [[{"node": "Set up Text for Embedding", "type": "main", "index": 0}]]}, "Delete old RAG vectors": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Update hash based on the new file": {"main": [[{"node": "Set up Text for Embedding", "type": "main", "index": 0}]]}, "Data loader": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "When file added to Recycle Bin": {"main": [[{"node": "For every file", "type": "main", "index": 0}]]}, "For every file": {"main": [[], [{"node": "Search records for the file", "type": "main", "index": 0}]]}, "Search records for the file": {"main": [[{"node": "Was file vectorized", "type": "main", "index": 0}]]}, "Was file vectorized": {"main": [[{"node": "Delete RAG vectors", "type": "main", "index": 0}], [{"node": "Delete file from recycle bin", "type": "main", "index": 0}]]}, "Delete RAG vectors": {"main": [[{"node": "Aggregates data", "type": "main", "index": 0}]]}, "Delete records from hash table": {"main": [[{"node": "Delete file from recycle bin", "type": "main", "index": 0}]]}, "Delete file from recycle bin": {"main": [[{"node": "For every file", "type": "main", "index": 0}]]}, "Aggregates data": {"main": [[{"node": "Delete records from hash table", "type": "main", "index": 0}]]}, "HTTP Request to Mistral": {"main": [[{"node": "Aggregate Markdown", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f2276d68-fdf5-43ef-88aa-8e9c807c89b8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "REDACTED_INSTANCE_ID"}, "id": "REDACTED_WORKFLOW_ID", "tags": []}