{"nodes": [{"parameters": {}, "id": "bde7b7db-45c6-4ab3-a705-358000cefbec", "name": "Merge chapters title and text", "type": "n8n-nodes-base.merge", "position": [2976, 368], "typeVersion": 2.1}, {"parameters": {}, "id": "0079022b-eaa2-481b-8c78-f8623a63645b", "name": "Final article text", "type": "n8n-nodes-base.code", "position": [3232, 368], "typeVersion": 1}, {"parameters": {"path": "f3822072-36a2-4f5a-8fe8-ca4b513f354c", "options": {}}, "id": "2e1c004d-43a5-4b87-9e30-beb2f22d4683", "name": "n8n Form Trigger", "type": "n8n-nodes-base.formTrigger", "position": [992, 368], "webhookId": "f3822072-36a2-4f5a-8fe8-ca4b513f354c", "typeVersion": 2.1}, {"parameters": {"options": {}}, "id": "2246ffe9-868f-4680-9770-3bb5c8e4b2f8", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [1232, 368], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "a883d54a-13fd-49bd-becf-7abdae21cd6c", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3728, 544], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "ef57acff-30d4-4493-bfd1-486314bd0a53", "name": "Ghost", "type": "n8n-nodes-base.ghost", "position": [4256, 368], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "479f474a-1687-4588-8485-d793afc6757d", "name": "Delegate to Writers", "type": "n8n-nodes-base.splitOut", "position": [2256, 368], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "1ba1b58e-4101-4348-b0f0-a0a92b1f4806", "name": "Editor", "type": "@n8n/n8n-nodes-langchain.agent", "position": [3456, 368], "typeVersion": 1.6}, {"parameters": {}, "id": "789c210d-6841-4c50-9057-e5fd114c0d77", "name": "Create title", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3936, 368], "typeVersion": 1.4}, {"parameters": {"options": {}}, "id": "d368ebec-3fdd-45a5-ae47-d252abd3b351", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1440, 544], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "040789b8-cedf-42ed-bce7-175e7ca61caa", "name": "Content Analyst", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1456, 368], "typeVersion": 1.6}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": ""}, "messages": {"values": [{}]}, "options": {}}, "id": "608860bf-f372-415e-a7f0-5708ba22ac2a", "name": "Project Planner", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1856, 368], "typeVersion": 1}, {"parameters": {}, "id": "80b7a065-dc27-4109-b012-be7438ced628", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1216, 240], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "03cc812a-d7eb-498f-8f90-821d55607f26", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2528, 544], "typeVersion": 1}, {"parameters": {}, "id": "ef96ee94-1499-43b1-baee-fb40e41818d9", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1808, 240], "typeVersion": 1}, {"parameters": {}, "id": "adf103db-d4df-47f5-938e-43e4a3a87411", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2480, 240], "typeVersion": 1}, {"parameters": {}, "id": "2e33d24b-128c-47f0-adda-024191091db2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3200, 240], "typeVersion": 1}, {"parameters": {}, "id": "48e313af-df5b-4887-8fa1-92d6de58d596", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3888, 240], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "e43a0a0e-e791-4295-b7b7-d53bc87f04ec", "name": "Writers", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2560, 368], "retryOnFail": true, "typeVersion": 1.6}, {"parameters": {}, "id": "8db1e8bb-4b50-46f3-be08-1abecbf1b2b0", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [368, 240], "typeVersion": 1}], "connections": {"Merge chapters title and text": {"main": [[{"node": "Final article text", "type": "main", "index": 0}]]}, "Final article text": {"main": [[{"node": "Editor", "type": "main", "index": 0}]]}, "n8n Form Trigger": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Content Analyst", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Editor", "type": "ai_languageModel", "index": 0}, {"node": "Create title", "type": "ai_languageModel", "index": 0}]]}, "Delegate to Writers": {"main": [[{"node": "Merge chapters title and text", "type": "main", "index": 1}, {"node": "Writers", "type": "main", "index": 0}]]}, "Editor": {"main": [[{"node": "Create title", "type": "main", "index": 0}]]}, "Create title": {"main": [[{"node": "Ghost", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Content Analyst", "type": "ai_languageModel", "index": 0}]]}, "Content Analyst": {"main": [[{"node": "Project Planner", "type": "main", "index": 0}]]}, "Project Planner": {"main": [[{"node": "Delegate to Writers", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Writers", "type": "ai_languageModel", "index": 0}]]}, "Writers": {"main": [[{"node": "Merge chapters title and text", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "2f281fde0de64f39316b38bf7aeff647de66c777fd2c9178aac0dbc0cd948eca", "templateId": "2187"}}