{"`name`": "💥Automate Product Data Collection & Customer Support via WhatsApp + GPT-4 + Google Sheets - DRFIRAS", "`nodes`": [{"`parameters`": {"`operation`": "send", "`phoneNumberId`": "***************", "`recipientPhoneNumber`": "+************", "`textBody`": "={{ $json.output }}", "`additionalFields`": {}}, "`type`": "n8n-nodes-base.whatsApp", "`typeVersion`": 1, "`position`": [1020, 220], "`id`": "370611ee-2725-4ee4-a73a-68d4eaabfe3a", "`name`": "WhatsApp Business Cloud", "`webhookId`": "ef261fd4-e3b8-42e9-a0e2-2c3c44b6b15e", "`credentials`": {"`whatsAppApi`": {"`id`": "TzeEzaeiZOOqcLOZ", "`name`": "WhatsApp account"}}}, {"`parameters`": {"`content`": "## 🟡 STEP 1 – Incoming WhatsApp Message\nListens for new WhatsApp messages.\nIf the message starts with train:, it triggers the product training flow.\nOtherwise, it goes to the customer support flow.\n**WhatsApp Business Cloud node** : [Here](https://www.notion.so/automatisation/WHATSAPP-WORKFLOW-1c63d6550fd980559679e7535938a68d?pvs=4#1c63d6550fd980f9a2a5e25a3654da82)", "`height`": 440, "`width`": 400}, "`type`": "n8n-nodes-base.stickyNote", "`typeVersion`": 1, "`position`": [-740, -20], "`id`": "30520fe8-a423-4942-85b2-9e8776b45939", "`name`": "<PERSON><PERSON>"}, {"`parameters`": {"`content`": "## 🔵 STEP 3 – Customer Support Flow\nTriggered when the message **does NOT start with `train:`**.\n\n1. **AI Analyzes the Message**  \n   Understands the customer's question or problem.\n\n2. **Fetch Product Data if Needed**  \n   Reads relevant product info from Google Sheets.\n\n3. **Detect Issues Automatically**  \n   Identifies if the user is facing a specific issue.\n\n4. **Suggest Solutions**  \n   Proposes a helpful, clear response to solve the issue.\n\n5. **Log Customer Problems**  \n   Saves the problem, suggested solution, and category.\n\n6. **Respond to the User**  \n   Sends a professional and helpful WhatsApp reply.", "`height`": 420, "`width`": 1180, "`color`": 6}, "`type`": "n8n-nodes-base.stickyNote", "`typeVersion`": 1, "`position`": [-300, 280], "`id`": "d3259e0e-5368-407d-8ac6-993784c939c9", "`name`": "Sticky Note1"}, {"`parameters`": {"`content`": "## 🔴 STEP 2 – Product Data Training (Triggered by train:)\nTriggered when the message starts with `train:`.\n**OpenAI API keys** : [Here](https://platform.openai.com/api-keys)\n\n\n\n\n\n\n\n\n\n\n\n\n1. **Extract URL from Message**  \n   Uses regex to detect and extract the product link.\n\n2. **Fetch HTML Content**  \n   Sends an HTTP request to retrieve the full page content.\n\n3. **Clean HTML Data**  \n   Strips HTML tags, scripts, and formats the text.\n\n4. **Save Raw Product Info**  \n   Appends the cleaned content and URL into Google Sheets.\n\n5. **AI: Enhance Product Details**  \n   - Extracts Product Name  \n   - Detects Price (subscription or one-time)  \n   - Identifies Product Topic  \n   - Generates FAQs\n\n6. **Update Product Sheet**  \n   Enriches the row in Sheets with structured product data.\n", "`height`": 660, "`width`": 1180, "`color`": 3}, "`type`": "n8n-nodes-base.stickyNote", "`typeVersion`": 1, "`position`": [-300, -400], "`id`": "455ce4ab-6d14-4d4e-8ff8-04612cf532b9", "`name`": "Sticky Note2"}, {"`parameters`": {"`content`": "## 🟢 STEP 4 – Client Response\nFinal step of the flow.\n\n- Sends the AI-generated response back to the customer via WhatsApp.\n- Ensures the message is clear, helpful, and personalized.", "`height`": 440, "`width`": 320, "`color`": 4}, "`type`": "n8n-nodes-base.stickyNote", "`typeVersion`": 1, "`position`": [920, -20], "`id`": "15251a04-a139-4a71-9e96-d5ecd9c2e66c", "`name`": "Sticky Note3"}, {"`parameters`": {"`updates`": ["messages"], "`options`": {}}, "`type`": "n8n-nodes-base.whatsAppTrigger", "`typeVersion`": 1, "`position`": [-700, 220], "`id`": "f028f5a3-fed3-4f90-89cc-2af211da7362", "`name`": "Incoming Message Trigger", "`webhookId`": "48ce9bac-c7ea-4cb7-a40a-b010ea5ac743", "`credentials`": {"`whatsAppTriggerApi`": {"`id`": "zYvcHcxGBNdN9Ptf", "`name`": "WhatsApp OAuth account"}}}, {"`parameters`": {"`rules`": {"`values`": [{"`conditions`": {"`options`": {"`caseSensitive`": true, "`leftValue`": "", "`typeValidation`": "strict", "`version`": 2}, "`conditions`": [{"`leftValue`": "={{ $json.messages[0].text.body }}", "`rightValue`": "train:", "`operator`": {"`type`": "string", "`operation`": "startsWith"}, "`id`": "9865cb5b-33da-490c-afc3-186457d5b564"}], "`combinator`": "and"}, "`renameOutput`": true, "`outputKey`": "train"}, {"`conditions`": {"`options`": {"`caseSensitive`": true, "`leftValue`": "", "`typeValidation`": "strict", "`version`": 2}, "`conditions`": [{"`id`": "5a9a1fee-b408-469f-a08c-e8d690fc9792", "`leftValue`": "={{ $json.messages[0].text.body }}", "`rightValue`": "train:", "`operator`": {"`type`": "string", "`operation`": "notStartsWith"}}], "`combinator`": "and"}, "`renameOutput`": true, "`outputKey`": "text"}]}, "`options`": {}}, "`type`": "n8n-nodes-base.switch", "`typeVersion`": 3.2, "`position`": [-520, 220], "`id`": "54edaa0c-005e-4810-a2fa-76fbcc009b2b", "`name`": "Check If Training"}, {"`parameters`": {"`jsCode`": "// Récupérer le texte à analyser depuis l'input\nconst texteExemple = $input.first().json.messages[0].text.body;\n\n// Expression régulière modifiée pour capturer les URL avec ou sans protocole\nconst regex = /((?:https?:\\/\\/)?(?:www\\.)?[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}(?:\\/\\S*)?)/g;\n\n// Extraction des URL à partir du texte\nconst matches = texteExemple.match(regex) || [];\n\n// Si des URL ont été trouvées, on les mappe en objets\nif (matches.length > 0) {\n    const output = matches.map(url => ({ url }));\n    return output;\n} else {\n    // Si aucune URL n'est trouvée, on retourne les items d'entrée pour que le workflow continue\n    return $input.all();\n}\n"}, "`type`": "n8n-nodes-base.code", "`typeVersion`": 2, "`position`": [-260, -320], "`id`": "b4187b28-efd9-46cb-8d1c-23d89d3f6125", "`name`": "Extract URL from Text"}, {"`parameters`": {"`url`": "={{ $json.url }}", "`options`": {"`response`": {"`response`": {"`responseFormat`": "text"}}}}, "`type`": "n8n-nodes-base.httpRequest", "`typeVersion`": 4.2, "`position`": [-100, -320], "`id`": "dbbcc8cf-2544-4de1-bcfd-6292d96a1017", "`name`": "Fetch HTML Page"}, {"`parameters`": {"`jsCode`": "// Exemple : récupération du contenu HTML depuis le premier item\n// Si vous avez un champ nommé \"html\" dans un noeud précédent.\nconst htmlContent = $input.first().json.html;\n\n// Fonction de nettoyage du HTML\nfunction nettoyerHTML(input) {\n  if (typeof input !== 'string') {\n    throw new Error(\"Expected HTML content as a string.\");\n  }\n  let cleanedText = input;\n\n  // 1. Retirer les liens <a> et leur contenu\n  cleanedText = cleanedText.replace(/<a[^>]*>.*?<\\/a>/gs, '');\n\n  // 2. Retirer <script>, <style>, commentaires, etc.\n  cleanedText = cleanedText.replace(/<script[^>]*>.*?<\\/script>/gs, '');\n  cleanedText = cleanedText.replace(/<style[^>]*>.*?<\\/style>/gs, '');\n  cleanedText = cleanedText.replace(/<!--[\\s\\S]*?-->/g, '');\n\n  // 3. Insérer des retours à la ligne pour certaines balises\n  cleanedText = cleanedText.replace(/<h[1-6][^>]*>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<\\/h[1-6]>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<p[^>]*>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<\\/p>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<ul[^>]*>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<\\/ul>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<ol[^>]*>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<\\/ol>/gi, '\\n');\n  cleanedText = cleanedText.replace(/<li[^>]*>/gi, '- ');\n  cleanedText = cleanedText.replace(/<\\/li>/gi, '\\n');\n\n  // 4. Supprimer toutes les balises HTML restantes\n  cleanedText = cleanedText.replace(/<[^>]+>/g, '');\n\n  // 5. Supprimer (ou ajuster) les caractères spéciaux\n  cleanedText = cleanedText.replace(/[^\\w\\sÀ-ÖØ-öø-ÿ]+/g, '');\n\n  // 6. Normaliser les espaces multiples et trim\n  cleanedText = cleanedText.replace(/\\s+/g, ' ').trim();\n\n  return cleanedText;\n}\n\n// Nettoyage\nconst result = nettoyerHTML($input.first().json.data);\n\n// Retour d'un tableau contenant l'objet final\nreturn [\n  {\n    json: {\n      cleanedText: result\n    }\n  }\n];\n"}, "`type`": "n8n-nodes-base.code", "`typeVersion`": 2, "`position`": [60, -320], "`id`": "91e711d0-26c0-41dd-a9c5-28100f0f44f6", "`name`": "Clean HTML Content"}, {"`parameters`": {"`operation`": "append", "`documentId`": {"`__rl`": true, "`value`": "1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ", "`mode`": "list", "`cachedResultName`": "Product Sales - AI Agent WhatsApp", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit?usp=drivesdk"}, "`sheetName`": {"`__rl`": true, "`value`": *********, "`mode`": "list", "`cachedResultName`": "Products", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit#gid=*********"}, "`columns`": {"`mappingMode`": "defineBelow", "`value`": {"`Product Description`": "={{ $json.cleanedText }}", "`Product Link`": "={{ $('Extract URL from Text').item.json.url }}"}, "`matchingColumns`": [], "`schema`": [{"`id`": "Product Link", "`displayName`": "Product Link", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Product Name", "`displayName`": "Product Name", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Product Price", "`displayName`": "Product Price", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Product Description", "`displayName`": "Product Description", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Product Topic", "`displayName`": "Product Topic", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}], "`attemptToConvertTypes`": false, "`convertFieldsToString`": false}, "`options`": {}}, "`type`": "n8n-nodes-base.googleSheets", "`typeVersion`": 4.5, "`position`": [240, -320], "`id`": "7e457450-42ef-417e-b5f9-3248c9c7c2ae", "`name`": "Save Raw Product Info", "`credentials`": {"`googleSheetsOAuth2Api`": {"`id`": "51us92xkOlrvArhV", "`name`": "Google Sheets account"}}}, {"`parameters`": {"`model`": {"`__rl`": true, "`mode`": "list", "`value`": "gpt-4o-mini"}, "`options`": {}}, "`type`": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "`typeVersion`": 1.2, "`position`": [320, -40], "`id`": "d3dfa439-4056-4ce9-b874-100500967de6", "`name`": "OpenAI Model", "`credentials`": {"`openAiApi`": {"`id`": "6h3DfVhNPw9I25nO", "`name`": "OpenAi account"}}}, {"`parameters`": {"`sessionIdType`": "customKey", "`sessionKey`": "={{ $('Incoming Message Trigger').item.json.messages[0].id }}", "`contextWindowLength`": 50}, "`type`": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "`typeVersion`": 1.3, "`position`": [500, -40], "`id`": "********-f6dd-4890-b05d-197d3d2adb79", "`name`": "Short-Term Memory"}, {"`parameters`": {"`operation`": "appendOrUpdate", "`documentId`": {"`__rl`": true, "`value`": "1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ", "`mode`": "list", "`cachedResultName`": "Product Sales - AI Agent WhatsApp", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit?usp=drivesdk"}, "`sheetName`": {"`__rl`": true, "`value`": *********, "`mode`": "list", "`cachedResultName`": "Products", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit#gid=*********"}, "`columns`": {"`mappingMode`": "defineBelow", "`value`": {"`Product Link`": "={{ $fromAI(\"product_url\",\"this is the website link of the product\") }}", "`Product Name`": "={{ $fromAI(\"product_name\",\"this is the name of the product\") }}", "`Product Price`": "={{ $fromAI(\"product_price\",\"this is the price of the product\") }}", "`Product Topic`": "={{ $fromAI(\"product_topic\",\"this is the topic of the product that specifies what it is for, who it is for and what the key benefits are\") }}", "`F&Q`": "={{ $fromAI(\"product_faq\", \"these are the most common questions users might have about the product, including answers if available\") }}\n"}, "`matchingColumns`": ["Product Link"], "`schema`": [{"`id`": "Product Link", "`displayName`": "Product Link", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true, "`removed`": false}, {"`id`": "Product Name", "`displayName`": "Product Name", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Product Price", "`displayName`": "Product Price", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Product Description", "`displayName`": "Product Description", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true, "`removed`": true}, {"`id`": "Product Topic", "`displayName`": "Product Topic", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "F&Q", "`displayName`": "F&Q", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true, "`removed`": false}], "`attemptToConvertTypes`": false, "`convertFieldsToString`": false}, "`options`": {}}, "`type`": "n8n-nodes-base.googleSheetsTool", "`typeVersion`": 4.5, "`position`": [680, -40], "`id`": "58ce0f46-fabc-4b6e-97d7-857dc6f1e568", "`name`": "Update Product Sheet", "`credentials`": {"`googleSheetsOAuth2Api`": {"`id`": "51us92xkOlrvArhV", "`name`": "Google Sheets account"}}}, {"`parameters`": {"`promptType`": "define", "`text`": "={{ $json.messages[0].text.body }}", "`options`": {"`systemMessage`": "You are a helpful and intelligent customer support assistant.\n\nYou have access to a Google Sheets tool that allows you to read any content from a specific sheet to gather necessary information.\nNever mention to the user that you accessed Google Sheets to retrieve any data.\n\nYour main responsibilities:\n\nUnderstand the user’s request or issue.\n\nIf the user is asking about a product, retrieve the relevant product name, price, and details as needed.\n\nDetermine whether the product price is a subscription or a one-time payment, based on the description.\n\nIf you detect that the user is facing a problem:\n\nIdentify and describe the problem clearly.\n\nPropose a practical and helpful solution.\n\nLog this interaction by adding a new row to the Google Sheet with the following columns:\n\nProblem\n\nSuggested Solution\n\nCategory (e.g., payment, login, access, delivery, technical issue, etc.)\n\nBe professional, concise, and empathetic in your responses.\nAlways aim to resolve the issue or provide the next best action."}}, "`type`": "@n8n/n8n-nodes-langchain.agent", "`typeVersion`": 1.7, "`position`": [380, 300], "`id`": "020714ae-5668-4fa5-8ba8-f1e476a8060e", "`name`": "AI Agent -  Customer Support Agent"}, {"`parameters`": {"`promptType`": "define", "`text`": "=My product description :  {{ $json['Product Description'] }}\n\nMy product link : {{ $json['Product Link'] }}", "`options`": {"`systemMessage`": "You are a helpful and intelligent assistant.\n\nYou receive the text content of a product page.\n\nYour tasks are:\n\nExtract the product name.\n\nExtract the product price, and determine whether it is a subscription or a one-time payment.\n\nIdentify the product topic.\n\nExtract the most frequently asked questions (FAQs) related to the product.\n\nYou have access to a Google Sheets tool that allows you to update specific columns and cells.\n\nAlways add the following data to the same row as the product URL in the Google Sheet:\n\nProduct Name\n\nProduct Price (with subscription/one-time label)\n\nProduct Topic\n\nFAQs\n\nBe accurate, structured, and consistent when filling in the sheet.\nDo not mention Google Sheets in your responses."}}, "`type`": "@n8n/n8n-nodes-langchain.agent", "`typeVersion`": 1.8, "`position`": [420, -320], "`id`": "53e49985-e1b9-48fd-9ea8-3352a63484e0", "`name`": "AI Agent - Enhance Product Details"}, {"`parameters`": {"`model`": {"`__rl`": true, "`mode`": "list", "`value`": "gpt-4o-mini"}, "`options`": {}}, "`type`": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "`typeVersion`": 1.2, "`position`": [220, 560], "`id`": "194136b5-872a-492b-882b-d26a2890cbcf", "`name`": "OpenAI Model1", "`credentials`": {"`openAiApi`": {"`id`": "6h3DfVhNPw9I25nO", "`name`": "OpenAi account"}}}, {"`parameters`": {"`sessionIdType`": "customKey", "`sessionKey`": "={{ $json.messages[0].id }}", "`contextWindowLength`": 50}, "`type`": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "`typeVersion`": 1.3, "`position`": [400, 560], "`id`": "efc89a9b-3141-4735-adaf-7b9e2b6879ed", "`name`": "Conversation Memory"}, {"`parameters`": {"`documentId`": {"`__rl`": true, "`value`": "1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ", "`mode`": "list", "`cachedResultName`": "Product Sales - AI Agent WhatsApp", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit?usp=drivesdk"}, "`sheetName`": {"`__rl`": true, "`value`": *********, "`mode`": "list", "`cachedResultName`": "Products", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit#gid=*********"}, "`options`": {"`dataLocationOnSheet`": {"`values`": {"`rangeDefinition`": "detectAutomatically", "`readRowsUntil`": "firstEmptyRow"}}}}, "`type`": "n8n-nodes-base.googleSheetsTool", "`typeVersion`": 4.5, "`position`": [560, 560], "`id`": "d1a21094-0237-4b17-818c-3af7760b405f", "`name`": "Read Product Sheet", "`credentials`": {"`googleSheetsOAuth2Api`": {"`id`": "51us92xkOlrvArhV", "`name`": "Google Sheets account"}}}, {"`parameters`": {"`operation`": "append", "`documentId`": {"`__rl`": true, "`value`": "1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ", "`mode`": "list", "`cachedResultName`": "Product Sales - AI Agent WhatsApp", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit?usp=drivesdk"}, "`sheetName`": {"`__rl`": true, "`value`": **********, "`mode`": "list", "`cachedResultName`": "Customer Issues", "`cachedResultUrl`": "https://docs.google.com/spreadsheets/d/1wa41S888Ya3sqEkBdzYiDq-5FOu_V30BOe-toX_78xQ/edit#gid=**********"}, "`columns`": {"`mappingMode`": "defineBelow", "`value`": {"`Support Problem`": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Support_Problem', ``, 'string') }}", "`Solution`": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Solution', ``, 'string') }}", "`Category`": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Category', ``, 'string') }}"}, "`matchingColumns`": [], "`schema`": [{"`id`": "Support Problem", "`displayName`": "Support Problem", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Solution", "`displayName`": "Solution", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}, {"`id`": "Category", "`displayName`": "Category", "`required`": false, "`defaultMatch`": false, "`display`": true, "`type`": "string", "`canBeUsedToMatch`": true}], "`attemptToConvertTypes`": false, "`convertFieldsToString`": false}, "`options`": {}}, "`type`": "n8n-nodes-base.googleSheetsTool", "`typeVersion`": 4.5, "`position`": [740, 560], "`id`": "2c2dcf9b-5578-4bd4-85b6-27621927a0a0", "`name`": "Log Customer Issues", "`credentials`": {"`googleSheetsOAuth2Api`": {"`id`": "51us92xkOlrvArhV", "`name`": "Google Sheets account"}}}], "`pinData`": {}, "`connections`": {"`WhatsApp Business Cloud`": {"`main`": [[]]}, "`Incoming Message Trigger`": {"`main`": [[{"`node`": "Check If Training", "`type`": "main", "`index`": 0}]]}, "`Check If Training`": {"`main`": [[{"`node`": "Extract URL from Text", "`type`": "main", "`index`": 0}], [{"`node`": "AI Agent -  Customer Support Agent", "`type`": "main", "`index`": 0}]]}, "`Extract URL from Text`": {"`main`": [[{"`node`": "Fetch HTML Page", "`type`": "main", "`index`": 0}]]}, "`Fetch HTML Page`": {"`main`": [[{"`node`": "Clean HTML Content", "`type`": "main", "`index`": 0}]]}, "`Clean HTML Content`": {"`main`": [[{"`node`": "Save Raw Product Info", "`type`": "main", "`index`": 0}]]}, "`Save Raw Product Info`": {"`main`": [[{"`node`": "AI Agent - Enhance Product Details", "`type`": "main", "`index`": 0}]]}, "`OpenAI Model`": {"`ai_languageModel`": [[{"`node`": "AI Agent - Enhance Product Details", "`type`": "ai_languageModel", "`index`": 0}]]}, "`Short-Term Memory`": {"`ai_memory`": [[{"`node`": "AI Agent - Enhance Product Details", "`type`": "ai_memory", "`index`": 0}]]}, "`Update Product Sheet`": {"`ai_tool`": [[{"`node`": "AI Agent - Enhance Product Details", "`type`": "ai_tool", "`index`": 0}]]}, "`AI Agent -  Customer Support Agent`": {"`main`": [[{"`node`": "WhatsApp Business Cloud", "`type`": "main", "`index`": 0}]]}, "`AI Agent - Enhance Product Details`": {"`main`": [[{"`node`": "WhatsApp Business Cloud", "`type`": "main", "`index`": 0}]]}, "`OpenAI Model1`": {"`ai_languageModel`": [[{"`node`": "AI Agent -  Customer Support Agent", "`type`": "ai_languageModel", "`index`": 0}]]}, "`Conversation Memory`": {"`ai_memory`": [[{"`node`": "AI Agent -  Customer Support Agent", "`type`": "ai_memory", "`index`": 0}]]}, "`Read Product Sheet`": {"`ai_tool`": [[{"`node`": "AI Agent -  Customer Support Agent", "`type`": "ai_tool", "`index`": 0}]]}, "`Log Customer Issues`": {"`ai_tool`": [[{"`node`": "AI Agent -  Customer Support Agent", "`type`": "ai_tool", "`index`": 0}]]}}, "`active`": false, "`settings`": {"`executionOrder`": "v1"}, "`versionId`": "1b3a456c-40a2-4b64-8926-53a1625177f6", "`meta`": {"`templateCredsSetupCompleted`": true, "`instanceId`": "a2b23892dd6989fda7c1209b381f5850373a7d2b85609624d7c2b7a092671d44"}, "`id`": "GvlLeSOUPfar0DQS", "`tags`": []}