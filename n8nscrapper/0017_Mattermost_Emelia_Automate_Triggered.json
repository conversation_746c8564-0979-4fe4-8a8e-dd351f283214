{"nodes": [{"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [650, 200], "parameters": {"message": "={{$json[\"contact\"][\"firstName\"]}} from {{$json[\"contact\"][\"company\"]}} has replied back to your campaign.", "channelId": "qx9yo1i9z3bg5qcy5a1oxnh69c", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "Mattermost Credentials"}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.em<PERSON><PERSON>", "position": [450, 200], "webhookId": "f53bc370-a8cb-4748-8f81-be7ae9b94972", "parameters": {"events": ["replied"], "campaignId": "6054d068b374b64365740101"}, "credentials": {"emeliaApi": "Emelia API Credentials"}, "typeVersion": 1}], "connections": {"Emelia Trigger": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}]]}}}