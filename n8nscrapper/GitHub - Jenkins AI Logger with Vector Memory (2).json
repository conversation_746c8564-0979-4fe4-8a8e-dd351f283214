{"name": "GitHub Commit Jenkins", "nodes": [{"parameters": {"content": "Automated workflow: GitHub Commit Jenkins", "height": 530, "width": 1100, "color": 5}, "id": "5e656fa4-c9ef-419c-b8e5-93b4dd837c81", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-480, -240]}, {"parameters": {"httpMethod": "POST", "path": "github-commit-jenkins"}, "id": "faa3187e-7313-474f-b54a-a25790101607", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-300, 0]}, {"parameters": {"chunkSize": 400, "chunkOverlap": 40}, "id": "c5119e77-1c04-4245-9cd2-2f9538fbb8d0", "name": "Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [-130, 0]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "fa72e972-a6f0-4352-a738-37f9167c5294", "name": "Embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [70, 0], "credentials": {"openAiApi": {"id": "OPENAI_API", "name": "OpenAI"}}}, {"parameters": {"mode": "insert", "options": {}, "indexName": "github_commit_jenkins"}, "id": "8cdecff9-3760-4347-b0db-d24928bae5b4", "name": "Supabase Insert", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [270, 0], "credentials": {"supabaseApi": {"id": "SUPABASE_API", "name": "Supabase account"}}}, {"parameters": {"indexName": "github_commit_jenkins"}, "id": "fe3e81f7-097a-4322-84a0-e7d24f7db8e8", "name": "Supabase Query", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [270, -180], "credentials": {"supabaseApi": {"id": "SUPABASE_API", "name": "Supabase account"}}}, {"parameters": {"name": "Supabase", "description": "Vector context"}, "id": "6f4c86c2-e1bd-4ecf-83cb-b2ceb4b73e6a", "name": "Vector Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [450, -180]}, {"parameters": {}, "id": "dace1819-1ddc-4e66-bebe-08fe643516bf", "name": "Window Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [450, -40]}, {"parameters": {"options": {}}, "id": "91e7ee4a-61ff-479a-acc6-57c7a51a9a70", "name": "Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [450, -340], "credentials": {"openAiApi": {"id": "OPENAI_API", "name": "OpenAI"}}}, {"parameters": {"promptType": "define", "text": "Process the following data for task 'GitHub Commit Jenkins':\n\n{{ $json }}", "options": {"systemMessage": "You are an assistant for GitHub Commit Jenkins"}}, "id": "3e35e0ca-1355-462b-8f1e-840b1736fce5", "name": "RAG Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [720, -40]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "SHEET_ID", "mode": "list", "cachedResultName": "GitHub Commit Jenkins"}, "sheetName": {"__rl": true, "value": "Log", "mode": "list", "cachedResultName": "Log"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "={{$json[\"RAG Agent\"].text}}"}, "schema": []}}, "id": "3624b160-adce-4657-853a-bbf8d6948a1b", "name": "Append Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [930, -40], "credentials": {"googleSheetsOAuth2Api": {"id": "SHEETS_API", "name": "Google Sheets account"}}}, {"parameters": {"channel": "#alerts", "text": "GitHub Commit Jenkins error: {$json.error.message}"}, "id": "9a67ce5b-4abd-4f9f-a5da-5113300ef9ff", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [930, 120], "credentials": {"slackApi": {"id": "SLACK_API", "name": "<PERSON><PERSON>ck"}}}], "connections": {"Webhook Trigger": {"main": [[{"node": "Text Splitter", "type": "main", "index": 0}, {"node": "Window Memory", "type": "main", "index": 0}]]}, "Text Splitter": {"main": [[{"node": "Embeddings", "type": "main", "index": 0}]], "ai_textSplitter": [[{"node": "Supabase Insert", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Supabase Insert", "type": "ai_embedding", "index": 0}, {"node": "Supabase Query", "type": "ai_embedding", "index": 0}]]}, "Supabase Insert": {"ai_document": [[]]}, "Supabase Query": {"ai_vectorStore": [[{"node": "Vector Tool", "type": "ai_vectorStore", "index": 0}]]}, "Vector Tool": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "Window Memory": {"ai_memory": [[{"node": "RAG Agent", "type": "ai_memory", "index": 0}]]}, "Chat Model": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "RAG Agent": {"main": [[{"node": "Append Sheet", "type": "main", "index": 0}]], "onError": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "triggerCount": 1}