{"name": "Call-to-Content", "nodes": [{"parameters": {"path": "713cf20c-6087-489d-9744-00439d99221f", "formTitle": "Meeting Content Creator", "formDescription": "Upload your meeting transcript and we'll create social media and blog content for you", "formFields": {"values": [{"fieldLabel": "Meeting Transcript", "fieldType": "file", "requiredField": true}, {"fieldLabel": "Speaker Name", "placeholder": "Enter the main speaker's name", "requiredField": true}, {"fieldLabel": "Topics You Usually Post About", "placeholder": "e.g., AI/ML, leadership, productivity, marketing, tech trends", "requiredField": true}, {"fieldLabel": "Brand Voice/Tone", "requiredField": true}]}, "options": {}}, "id": "e494c482-3d3e-4aca-86a7-9f526940ff47", "name": "Form Submission Trigger", "type": "n8n-nodes-base.formTrigger", "position": [0, 0], "typeVersion": 2.1, "webhookId": "713cf20c-6087-489d-9744-00439d99221f"}, {"parameters": {"operation": "pdf", "binaryPropertyName": "Meeting_Transcript", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [220, 0], "id": "4be8cba2-9ddb-42ba-a107-12c1e53ff950", "name": "Extract from File"}, {"parameters": {"assignments": {"assignments": [{"id": "055d0f6c-5288-4e31-ab8e-c66c02681721", "name": "Speaker Name", "value": "={{ $('Form Submission Trigger').item.json['Speaker Name'] }}", "type": "string"}, {"id": "bb706655-e777-4661-965a-b1ddaf9561ce", "name": "Topics Usually Posted About", "value": "={{ $('Form Submission Trigger').item.json['Topics You Usually Post About'] }}", "type": "string"}, {"id": "fcbe7562-96f0-4c9f-94f3-4072ad5102c6", "name": "Brand Voice / Tone", "value": "={{ $('Form Submission Trigger').item.json['Brand Voice/Tone'] }}", "type": "string"}, {"id": "f414883e-4a79-4e97-af95-c3b1de4d3e26", "name": "Call Transcription", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 0], "id": "0c2cb1dd-9ae9-4ff1-911e-dcd1b4c2bf24", "name": "<PERSON>"}, {"parameters": {"promptType": "define", "text": "=Here’s a transcript of a recent talk or meeting given by {{ $json['Speaker Name'] }}. They usually post about {{ $json['Topics Usually Posted About'] }} and prefer a {{ $json['Brand Voice / Tone'] }} tone of voice.\n\nTranscript:\n\"\"\"\n{{ $json['Call Transcription'] }}\n\"\"\"\n\nPlease extract the strongest, most original ideas and repurpose them into high-quality content in the speaker’s tone of voice.\n\nYour job is to write *for real humans* — smart, busy, discerning ones. Don't summarize. Don't sound like AI. Make it feel like something an expert thought through and drafted.\n\nCreate:\n1. A **blog post** (~300–500 words) — narrative-driven, punchy, and non-obvious\n2. A **LinkedIn post** — bold opinion, short paragraphs, native tone\n3. An **Instagram caption** — idea-led, short and scroll-stopping\n4. One other short-form snippet (e.g., tweet thread or newsletter hook) — conversational and high-impact\n\nThe voice should feel editorial, confident, and intellectually generous — like someone speaking from experience, not theory.\n\nAvoid: filler, cliches, lists of tips, generic takeaways.\nDo: challenge assumptions, show insight, sound like a real expert wrote it.\n\nYour output must be exactly:\n\n{\n  \"blog_post\": \"...\",\n  \"linkedin_post\": \"...\",\n  \"instagram_caption\": \"...\",\n  \"other_snippets\": \"...\"\n}", "hasOutputParser": true, "options": {"systemMessage": "=You are a world-class content strategist and editorial AI. Your job is to help creators repurpose ideas from audio transcripts into compelling content. You are strategic, articulate, and tuned into platform-native content styles. Be opinionated, but stay true to the speaker's voice and tone. Assume the speaker has strong expertise and is sharing thought leadership. Keep the outputs clean, punchy, and ready to publish."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [680, 0], "id": "31ca2a97-250c-452a-b1a4-0772c4b19df4", "name": "AI Agent"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [820, 220], "id": "f3c36abe-def7-403c-b1ad-f14867ad6f4d", "name": "Think"}, {"parameters": {"jsonSchemaExample": "{\n  \"blog_post\": \"string\",\n  \"linkedin_post\": \"string\",\n  \"instagram_caption\": \"string\",\n  \"other_snippets\": \"string\"\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [960, 220], "id": "4e1e80b0-13db-4950-b222-c1fd8dba6804", "name": "Structured Output Parser"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1yTu55nWtEvar373cWhPjL_8KLIYlRVf3QMQ0ZBePjmw", "mode": "list", "cachedResultName": "Content Dash <PERSON>", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yTu55nWtEvar373cWhPjL_8KLIYlRVf3QMQ0ZBePjmw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Feuille 1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yTu55nWtEvar373cWhPjL_8KLIYlRVf3QMQ0ZBePjmw/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Blog Post": "={{ $json.output.blog_post }}", "Instagram Caption": "={{ $json.output.instagram_caption }}", "Other Snippets": "={{ $json.output.other_snippets }}", "Linkedin Post": "={{ $json.output.linkedin_post }}"}, "matchingColumns": [], "schema": [{"id": "Blog Post", "displayName": "Blog Post", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Linkedin Post", "displayName": "Linkedin Post", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instagram Caption", "displayName": "Instagram Caption", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Other Snippets", "displayName": "Other Snippets", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1040, 0], "id": "7cff49ee-e16b-47ac-898e-8165a684b8a6", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "k0jA2DcVZVcflFFD", "name": "Google Sheets account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [580, 220], "id": "f668ed83-8bc5-4049-a3b8-79d9130b04bd", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "8r0MQGKQfcBcHHao", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"Form Submission Trigger": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "da40af25-9446-4d1c-b023-956ce63152c0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "33517288c241266caa5271ec633a2a38484d56e0d3b7f919114782d16dd1979a"}, "id": "CnWU2BgRY5GJwNPi", "tags": []}