{"name": "AI Video Agent", "nodes": [{"parameters": {"options": {"allowFileUploads": true}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-96, 0], "id": "4a7fb2f0-a466-4d47-a23c-75458d2d1a8c", "name": "When chat message received", "webhookId": "9883c7e1-dc69-4775-8fba-81eadc36efae"}, {"parameters": {"method": "POST", "url": "https://ginigen-veo3-free.hf.space/gradio_api/call/generate_video_with_audio", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"data\": [\n    \"{{ $('When chat message received').item.json.chatInput }}\",\n    \"\",\n    1,\n    512,\n    512,\n    2,\n    4,\n    2025,\n    true,\n    true,\n    \"\",\n    10,\n    1\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [464, 0], "id": "6972bd07-a326-48b3-9b17-c40ca1451090", "name": "HTTP Request"}, {"parameters": {"url": "=https://ginigen-veo3-free.hf.space/gradio_api/call/generate_video_with_audio/{{$json.event_id}}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [656, 0], "id": "2857386a-5daf-459c-84b9-feb1d062208a", "name": "HTTP Request1"}, {"parameters": {"options": {"systemMessage": "You are a prompt writer for ai video models. Just prompt no other text or word."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [80, 0], "id": "32b93fdc-42a3-421f-8c8c-6bfc00ac1c34", "name": "AI Agent"}, {"parameters": {"model": "qwen/qwen2.5-vl-32b-instruct:free", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [80, 224], "id": "ff5605b7-b5f0-4c0f-b57e-de869b7858e0", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "lD4knZWYlZFk5A88", "name": "OpenRouter account"}}}, {"parameters": {"jsCode": "// n8n Function node\n// Extracts video URL from string like:\n// event: complete\\ndata: [{...}]\\n\\n\n\nconst input =  $input.first().json;\n\n// If your input is like { data: \"...\" }\nconst rawString = input.data || input;\n\n// Extract everything AFTER 'data: '\nconst dataStart = rawString.indexOf('data: ');\nif (dataStart === -1) {\n  throw new Error('No `data:` found in input string');\n}\n\n// Get the JSON substring\nconst jsonStringPart = rawString.substring(dataStart + 6);\n\n// Remove trailing escaped \\n\\n if present\nconst cleanedJsonString = jsonStringPart.replace(/\\\\n\\\\n$/, '');\n\n// Now parse JSON\nlet parsedArray;\ntry {\n  parsedArray = JSON.parse(cleanedJsonString);\n} catch (err) {\n  throw new Error('Could not parse JSON: ' + err.message);\n}\n\n// Grab the URL\nconst videoUrl = parsedArray[0].video.url;\n\nreturn [\n  {\n    json: {\n      videoUrl: videoUrl,\n    },\n  },\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [864, 0], "id": "ded85139-6487-4127-82cd-85483b7058d6", "name": "Code"}, {"parameters": {"content": "### Using Veo 3 for Free for AI Videos\n##### Note: Make the output shouldn't not include special characters else invalid error will come.", "height": 272, "width": 400}, "type": "n8n-nodes-base.stickyNote", "position": [400, -96], "typeVersion": 1, "id": "d67c104b-2ec0-4b0f-8c49-b1bb85759875", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "### Extract the ai video url from string output of veo 3 model.", "height": 240, "width": 272, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [832, -80], "typeVersion": 1, "id": "2f74108a-3c10-46dd-ad13-117eb4867c0a", "name": "Sticky Note1"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "21eece98-e9d6-4055-8375-2a0b1a713024", "meta": {"templateCredsSetupCompleted": true, "instanceId": "dedc19d9bdfc7243f7ee13c48a4edef60ea52cfb037e9ca16e44a41bcd75a2d3"}, "id": "qvojwl4YkylzqAJI", "tags": []}