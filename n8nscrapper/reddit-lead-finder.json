{
  `meta`: {
    `instanceId`: `reddit-freelance-finder-v1`
  },
  `nodes`: [
    {
      `parameters`: {
        `rule`: {
          `interval`: [2],
          `intervalUnit`: `hours`
        }
      },
      `id`: `cron-trigger`,
      `name`: `Schedule - Every 2 Hours`,
      `type`: `n8n-nodes-base.cron`,
      `typeVersion`: 1,
      `position`: [
        200,
        300
      ]
    },
    {
      `parameters`: {
        `values`: {
          `string`: [
            {
              `name`: `subreddits`,
              `value`: `forhire,n8n,automation,freelance,HireaWriter,slavelabour,remotework,digitalnomad,jobbit,WorkOnline`
            },
            {
              `name`: `job_keywords`,
              `value`: `freelance,job,hiring,looking for,need help,automation,n8n,workflow,zapier,make,integromat,bot,api,scraping,data entry`
            },
            {
              `name`: `reddit_oauth_url`,
              `value`: `https://oauth.reddit.com/r/{subreddit}/new`
            }
          ]
        }
      },
      `id`: `config-node`,
      `name`: `Configuration Settings`,
      `type`: `n8n-nodes-base.set`,
      `typeVersion`: 3.4,
      `position`: [
        400,
        300
      ]
    },
    {
      `parameters`: {
        `fieldToSplitOut`: `subreddits`,
        `options`: {
          `destinationFieldName`: `current_subreddit`
        }
      },
      `id`: `split-subreddits`,
      `name`: `Split Subreddits`,
      `type`: `n8n-nodes-base.itemLists`,
      `typeVersion`: 3,
      `position`: [
        600,
        300
      ]
    },
    {
      `parameters`: {
        `url`: `=https://oauth.reddit.com/r/{{ $json.current_subreddit }}/new.json?limit=25`,
        `authentication`: `predefinedCredentialType`,
        `nodeCredentialType`: `redditOAuth2Api`,
        `options`: {
          `timeout`: 30000,
          `retry`: {
            `enabled`: true,
            `maxTries`: 3,
            `waitBetweenTries`: 1000
          }
        }
      },
      `id`: `fetch-posts`,
      `name`: `Fetch Reddit Posts`,
      `type`: `n8n-nodes-base.httpRequest`,
      `typeVersion`: 4.2,
      `position`: [
        800,
        300
      ]
    },
    {
      `parameters`: {
        `jsCode`: `// Extract posts from Reddit API response
const posts = $input.first().json.data.children;
const extractedPosts = [];

for (let post of posts) {
  const postData = post.data;
  
  // Extract relevant post information
  extractedPosts.push({
    subreddit: postData.subreddit,
    post_id: postData.id,
    title: postData.title,
    selftext: postData.selftext || '',
    author: postData.author,
    created_utc: postData.created_utc,
    score: postData.score,
    num_comments: postData.num_comments,
    permalink: postData.permalink,
    url: \`https://reddit.com\${postData.permalink}\`,
    full_url: \`https://oauth.reddit.com\${postData.permalink}.json\`
  });
}

return extractedPosts.map(post => ({ json: post }));`
      },
      `id`: `extract-posts`,
      `name`: `Extract Post Data`,
      `type`: `n8n-nodes-base.code`,
      `typeVersion`: 2,
      `position`: [
        1000,
        300
      ]
    },
    {
      `parameters`: {
        `conditions`: {
          `options`: {
            `caseSensitive`: false
          },
          `conditions`: [
            {
              `id`: `freelance-keywords`,
              `leftValue`: `={{ $json.title }} {{ $json.selftext }}`,
              `rightValue`: `freelance|job|hiring|looking for|need help|automation|n8n|workflow|zapier|make|integromat|bot|api|scraping|data entry|remote work|virtual assistant`,
              `operator`: {
                `type`: `regex`
              }
            },
            {
              `id`: `exclude-spam`,
              `leftValue`: `={{ $json.title }} {{ $json.selftext }}`,
              `rightValue`: `spam|scam|fake|bot|automated|[removed]|[deleted]`,
              `operator`: {
                `type`: `regex`,
                `operation`: `notRegex`
              }
            },
            {
              `id`: `minimum-length`,
              `leftValue`: `={{ ($json.title + ' ' + $json.selftext).length }}`,
              `rightValue`: 20,
              `operator`: {
                `type`: `number`,
                `operation`: `gte`
              }
            }
          ]
        },
        `combineOperation`: `all`
      },
      `id`: `filter-relevant-posts`,
      `name`: `Filter Relevant Posts`,
      `type`: `n8n-nodes-base.filter`,
      `typeVersion`: 2,
      `position`: [
        1200,
        300
      ]
    },
    {
      `parameters`: {
        `url`: `={{ $json.full_url }}`,
        `authentication`: `predefinedCredentialType`,
        `nodeCredentialType`: `redditOAuth2Api`,
        `options`: {
          `timeout`: 30000,
          `retry`: {
            `enabled`: true,
            `maxTries`: 3,
            `waitBetweenTries`: 1000
          }
        }
      },
      `id`: `fetch-comments`,
      `name`: `Fetch Post Comments`,
      `type`: `n8n-nodes-base.httpRequest`,
      `typeVersion`: 4.2,
      `position`: [
        1400,
        300
      ]
    },
    {
      `parameters`: {
        `jsCode`: `// Extract top-level comments and filter out mod comments
const postData = $input.first().json[0].data.children[0].data;
const comments = $input.first().json[1].data.children;
const processedComments = [];

for (let comment of comments) {
  const commentData = comment.data;
  
  // Skip if it's not a comment or if it's deleted/removed
  if (commentData.kind === 'more' || !commentData.body || 
      commentData.body === '[deleted]' || commentData.body === '[removed]') {
    continue;
  }
  
  // Skip mod comments and stickied comments
  if (commentData.stickied || commentData.distinguished === 'moderator') {
    continue;
  }
  
  // Only process top-level comments (no parent_id that starts with 't1_')
  if (!commentData.parent_id || commentData.parent_id.startsWith('t3_')) {
    processedComments.push({
      subreddit: postData.subreddit,
      post_id: postData.id,
      post_title: postData.title,
      post_url: \`https://reddit.com\${postData.permalink}\`,
      comment_id: commentData.id,
      comment_body: commentData.body,
      comment_author: commentData.author,
      comment_score: commentData.score,
      created_utc: commentData.created_utc,
      timestamp: new Date(commentData.created_utc * 1000).toISOString()
    });
  }
}

// If no comments, return post data without comments
if (processedComments.length === 0) {
  return [{
    json: {
      subreddit: postData.subreddit,
      post_id: postData.id,
      post_title: postData.title,
      post_url: \`https://reddit.com\${postData.permalink}\`,
      comment_id: '',
      comment_body: '',
      comment_author: '',
      comment_score: 0,
      created_utc: postData.created_utc,
      timestamp: new Date(postData.created_utc * 1000).toISOString()
    }
  }];
}

return processedComments.map(comment => ({ json: comment }));`
      },
      `id`: `process-comments`,
      `name`: `Extract Top-Level Comments`,
      `type`: `n8n-nodes-base.code`,
      `typeVersion`: 2,
      `position`: [
        1600,
        300
      ]
    },
    {
      `parameters`: {
        `jsCode`: `// Calculate lead score based on various factors
const data = $input.first().json;

let score = 0;

// Scoring based on keywords in title and comment
const highValueKeywords = ['n8n', 'automation', 'workflow', 'zapier', 'make', 'integromat', 'freelance'];
const mediumValueKeywords = ['api', 'integration', 'bot', 'scraping', 'data'];
const lowValueKeywords = ['help', 'job', 'hiring', 'looking for'];

const text = \`\${data.post_title} \${data.comment_body}\`.toLowerCase();

// High value keywords (10 points each)
highValueKeywords.forEach(keyword => {
  if (text.includes(keyword)) score += 10;
});

// Medium value keywords (5 points each)
mediumValueKeywords.forEach(keyword => {
  if (text.includes(keyword)) score += 5;
});

// Low value keywords (2 points each)
lowValueKeywords.forEach(keyword => {
  if (text.includes(keyword)) score += 2;
});

// Bonus for longer comments (indicates detailed requirements)
if (data.comment_body && data.comment_body.length > 200) score += 5;
if (data.comment_body && data.comment_body.length > 500) score += 5;

// Bonus for recent posts (less than 24 hours old)
const now = Date.now() / 1000;
const postAge = now - data.created_utc;
if (postAge < 86400) score += 5; // 24 hours

// Comment score bonus
if (data.comment_score > 5) score += 3;
if (data.comment_score > 10) score += 5;

// Determine priority
let priority = 'low';
if (score >= 25) priority = 'high';
else if (score >= 15) priority = 'medium';

return [{
  json: {
    ...data,
    lead_score: score,
    priority: priority,
    processed_at: new Date().toISOString()
  }
}];`
      },
      `id`: `score-leads`,
      `name`: `Calculate Lead Score`,
      `type`: `n8n-nodes-base.code`,
      `typeVersion`: 2,
      `position`: [
        1800,
        300
      ]
    },
    {
      `parameters`: {
        `operation`: `get`,
        `documentId`: {
          `__rl`: true,
          `value`: `YOUR_GOOGLE_SHEET_ID`,
          `mode`: `id`
        },
        `sheetName`: {
          `__rl`: true,
          `value`: `gid=0`,
          `mode`: `id`
        },
        `options`: {
          `usePathForAll`: true
        }
      },
      `id`: `check-existing`,
      `name`: `Check for Duplicates`,
      `type`: `n8n-nodes-base.googleSheets`,
      `typeVersion`: 4.4,
      `position`: [
        2000,
        300
      ]
    },
    {
      `parameters`: {
        `jsCode`: `// Check if this lead already exists
const currentLead = $input.first().json;
const existingData = $input.last().json;

// Create unique identifier for deduplication
const uniqueId = \`\${currentLead.subreddit}_\${currentLead.post_id}_\${currentLead.comment_id || 'post'}\`;

// Check if this entry already exists
const isDuplicate = existingData.some(row => 
  row.unique_id === uniqueId || 
  (row.post_id === currentLead.post_id && row.comment_id === currentLead.comment_id)
);

if (isDuplicate) {
  return []; // Return empty array to stop processing
}

return [{
  json: {
    ...currentLead,
    unique_id: uniqueId
  }
}];`
      },
      `id`: `deduplication`,
      `name`: `Remove Duplicates`,
      `type`: `n8n-nodes-base.code`,
      `typeVersion`: 2,
      `position`: [
        2200,
        300
      ]
    },
    {
      `parameters`: {
        `values`: {
          `string`: [
            {
              `name`: `Timestamp`,
              `value`: `={{ $json.processed_at }}`
            },
            {
              `name`: `Subreddit`,
              `value`: `={{ $json.subreddit }}`
            },
            {
              `name`: `Post Title`,
              `value`: `={{ $json.post_title }}`
            },
            {
              `name`: `Post URL`,
              `value`: `={{ $json.post_url }}`
            },
            {
              `name`: `Comment Body`,
              `value`: `={{ $json.comment_body || 'No comments' }}`
            },
            {
              `name`: `Comment Author`,
              `value`: `={{ $json.comment_author || 'N/A' }}`
            },
            {
              `name`: `Lead Score`,
              `value`: `={{ $json.lead_score }}`
            },
            {
              `name`: `Priority`,
              `value`: `={{ $json.priority }}`
            },
            {
              `name`: `Comment Score`,
              `value`: `={{ $json.comment_score || 0 }}`
            },
            {
              `name`: `Unique ID`,
              `value`: `={{ $json.unique_id }}`
            }
          ]
        },
        `options`: {
          `dotNotation`: false
        }
      },
      `id`: `format-data`,
      `name`: `Format for Sheets`,
      `type`: `n8n-nodes-base.set`,
      `typeVersion`: 3.4,
      `position`: [
        2400,
        300
      ]
    },
    {
      `parameters`: {
        `operation`: `appendOrUpdate`,
        `documentId`: {
          `__rl`: true,
          `value`: `YOUR_GOOGLE_SHEET_ID`,
          `mode`: `id`
        },
        `sheetName`: {
          `__rl`: true,
          `value`: `gid=0`,
          `mode`: `id`
        },
        `columns`: {
          `mappingMode`: `defineBelow`,
          `value`: {
            `Timestamp`: `={{ $json["Timestamp"] }}`,
            `Subreddit`: `={{ $json["Subreddit"] }}`,
            `Post Title`: `={{ $json["Post Title"] }}`,
            `Post URL`: `={{ $json["Post URL"] }}`,
            `Comment Body`: `={{ $json["Comment Body"] }}`,
            `Comment Author`: `={{ $json["Comment Author"] }}`,
            `Lead Score`: `={{ $json["Lead Score"] }}`,
            `Priority`: `={{ $json["Priority"] }}`,
            `Comment Score`: `={{ $json["Comment Score"] }}`,
            `Unique ID`: `={{ $json["Unique ID"] }}`
          },
          `matchingColumns`: [
            `Unique ID`
          ]
        },
        `options`: {
          `usePathForAll`: true
        }
      },
      `id`: `append-to-sheets`,
      `name`: `Append to Google Sheets`,
      `type`: `n8n-nodes-base.googleSheets`,
      `typeVersion`: 4.4,
      `position`: [
        2600,
        300
      ]
    },
    {
      `parameters`: {
        `conditions`: {
          `conditions`: [
            {
              `id`: `high-priority`,
              `leftValue`: `={{ $json.Priority }}`,
              `rightValue`: `high`,
              `operator`: {
                `type`: `string`,
                `operation`: `equals`
              }
            }
          ]
        }
      },
      `id`: `filter-high-priority`,
      `name`: `Filter High Priority Leads`,
      `type`: `n8n-nodes-base.filter`,
      `typeVersion`: 2,
      `position`: [
        2800,
        300
      ]
    },
    {
      `parameters`: {
        `fromEmail`: `<EMAIL>`,
        `toEmail`: `<EMAIL>`,
        `subject`: `🚨 High Priority n8n Lead Found!`,
        `emailType`: `html`,
        `message`: `=<h2>High Priority Lead Detected!</h2>
<p><strong>Subreddit:</strong> r/{{ $json.Subreddit }}</p>
<p><strong>Post:</strong> {{ $json["Post Title"] }}</p>
<p><strong>Lead Score:</strong> {{ $json["Lead Score"] }}</p>
<p><strong>Priority:</strong> {{ $json.Priority }}</p>
<p><strong>Comment Author:</strong> {{ $json["Comment Author"] }}</p>
<p><strong>Comment Preview:</strong></p>
<blockquote>{{ $json["Comment Body"].substring(0, 300) }}{{ $json["Comment Body"].length > 300 ? '...' : '' }}</blockquote>
<p><a href="{{ $json["Post URL"] }}" target="_blank">View Full Post on Reddit</a></p>
<hr>
<p><em>Generated by n8n Reddit Lead Finder</em></p>`,
        `options`: {
          `allowUnauthorizedCerts`: false
        }
      },
      `id`: `email-notification`,
      `name`: `Send Email Alert`,
      `type`: `n8n-nodes-base.emailSend`,
      `typeVersion`: 2.1,
      `position`: [
        3000,
        300
      ]
    },
    {
      `parameters`: {
        `assignments`: {
          `assignments`: [
            {
              `id`: `summary`,
              `name`: `summary`,
              `value`: `=Processed {{ $("filter-high-priority")?.length || 0 }} high priority leads from {{ $("split-subreddits")?.length || 0 }} subreddits`,
              `type`: `string`
            },
            {
              `id`: `total-processed`,
              `name`: `total_processed`,
              `value`: `={{ $("append-to-sheets")?.length || 0 }}`,
              `type`: `number`
            },
            {
              `id`: `high-priority-count`,
              `name`: `high_priority_count`,
              `value`: `={{ $("filter-high-priority")?.length || 0 }}`,
              `type`: `number`
            }
          ]
        },
        `options`: {}
      },
      `id`: `final-summary`,
      `name`: `Execution Summary`,
      `type`: `n8n-nodes-base.set`,
      `typeVersion`: 3.4,
      `position`: [
        3200,
        300
      ]
    }
  ],
  `connections`: {
    `cron-trigger`: {
      `main`: [
        [
          {
            `node`: `config-node`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `config-node`: {
      `main`: [
        [
          {
            `node`: `split-subreddits`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `split-subreddits`: {
      `main`: [
        [
          {
            `node`: `fetch-posts`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `fetch-posts`: {
      `main`: [
        [
          {
            `node`: `extract-posts`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `extract-posts`: {
      `main`: [
        [
          {
            `node`: `filter-relevant-posts`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `filter-relevant-posts`: {
      `main`: [
        [
          {
            `node`: `fetch-comments`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `fetch-comments`: {
      `main`: [
        [
          {
            `node`: `process-comments`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `process-comments`: {
      `main`: [
        [
          {
            `node`: `score-leads`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `score-leads`: {
      `main`: [
        [
          {
            `node`: `check-existing`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `check-existing`: {
      `main`: [
        [
          {
            `node`: `deduplication`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `deduplication`: {
      `main`: [
        [
          {
            `node`: `format-data`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `format-data`: {
      `main`: [
        [
          {
            `node`: `append-to-sheets`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `append-to-sheets`: {
      `main`: [
        [
          {
            `node`: `filter-high-priority`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `filter-high-priority`: {
      `main`: [
        [
          {
            `node`: `email-notification`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    },
    `email-notification`: {
      `main`: [
        [
          {
            `node`: `final-summary`,
            `type`: `main`,
            `index`: 0
          }
        ]
      ]
    }
  },
  `pinData`: {},
  `settings`: {
    `executionOrder`: `v1`,
    `saveManualExecutions`: true,
    `callerPolicy`: `workflowsFromSameOwner`,
    `errorWorkflow`: {
      `__rl`: true,
      `mode`: `id`,
      `value`: ``
    }
  },
  `staticData`: {},
  `tags`: [
    {
      `createdAt`: `2025-01-29T12:00:00.000Z`,
      `updatedAt`: `2025-01-29T12:00:00.000Z`,
      `id`: `lead-generation`,
      `name`: `Lead Generation`
    },
    {
      `createdAt`: `2025-01-29T12:00:00.000Z`,
      `updatedAt`: `2025-01-29T12:00:00.000Z`,
      `id`: `reddit-automation`,
      `name`: `Reddit Automation`
    }
  ],
  `triggerCount`: 1,
  `updatedAt`: `2025-01-29T12:00:00.000Z`,
  `versionId`: `reddit-lead-finder-v1`,
  `id`: `reddit-freelance-lead-finder`,
  `name`: `Reddit Freelance Lead Finder`,
  `active`: true,
  `createdAt`: `2025-01-29T12:00:00.000Z`
}