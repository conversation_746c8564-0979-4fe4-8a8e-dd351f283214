{"id": "110", "name": "Get all the stories starting with `release` and publish them", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Storyblok", "type": "n8n-nodes-base.storyblok", "position": [450, 300], "parameters": {"space": 96940, "source": "managementApi", "filters": {"starts_with": "release"}, "operation": "getAll"}, "credentials": {"storyblokManagementApi": "storyb<PERSON><PERSON>-tanay"}, "typeVersion": 1}, {"name": "Storyblok1", "type": "n8n-nodes-base.storyblok", "position": [650, 300], "parameters": {"space": "={{$node[\"Storyblok\"].parameter[\"space\"]}}", "source": "managementApi", "options": {}, "storyId": "={{$node[\"Storyblok\"].json[\"id\"]}}", "operation": "publish"}, "credentials": {"storyblokManagementApi": "storyb<PERSON><PERSON>-tanay"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Storyblok": {"main": [[{"node": "Storyblok1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Storyblok", "type": "main", "index": 0}]]}}}