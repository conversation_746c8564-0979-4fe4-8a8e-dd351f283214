{
  `mode`: `rewrite`,
  `path`: `/tmp/ai_sdr_simple.json`,
  `content`: `{
  \"meta\": {
    \"instanceId\": \"12345\"
  },
  \"nodes\": [
    {
      \"parameters\": {
        \"rule\": {
          \"interval\": [
            {
              \"field\": \"hours\",
              \"value\": 8
            }
          ]
        }
      },
      \"id\": \"trigger1\",
      \"name\": \"Daily Trigger\",
      \"type\": \"n8n-nodes-base.cron\",
      \"typeVersion\": 1,
      \"position\": [
        240,
        300
      ]
    },
    {
      \"parameters\": {
        \"resource\": \"contact\",
        \"operation\": \"getAll\",
        \"returnAll\": true
      },
      \"id\": \"hubspot1\",
      \"name\": \"Get New Leads\",
      \"type\": \"n8n-nodes-base.hubspot\",
      \"typeVersion\": 1,
      \"position\": [
        460,
        300
      ]
    },
    {
      \"parameters\": {
        \"conditions\": {
          \"string\": [
            {
              \"value1\": \"={{$json.properties.email}}\",
              \"operation\": \"isNotEmpty\"
            }
          ]
        }
      },
      \"id\": \"if1\",
      \"name\": \"Filter Valid Leads\",
      \"type\": \"n8n-nodes-base.if\",
      \"typeVersion\": 1,
      \"position\": [
        680,
        300
      ]
    },
    {
      \"parameters\": {
        \"url\": \"https://api.clearbit.com/v2/enrichment/find\",
        \"options\": {
          \"queryAuth\": {
            \"user\": \"={{$credentials.clearbit.apiKey}}\"
          }
        },
        \"sendQuery\": true,
        \"queryParameters\": {
          \"parameters\": [
            {
              \"name\": \"email\",
              \"value\": \"={{$json.properties.email}}\"
            }
          ]
        }
      },
      \"id\": \"http1\",
      \"name\": \"Enrich Lead Data\",
      \"type\": \"n8n-nodes-base.httpRequest\",
      \"typeVersion\": 4,
      \"position\": [
        900,
        300
      ]
    },
    {
      \"parameters\": {
        \"options\": {},
        \"requestBody\": {
          \"messages\": [
            {
              \"role\": \"user\",
              \"content\": \"Analyze this lead and provide a score from 1-10 and personalized email strategy. Lead: {{$json.person.name.fullName}} at {{$json.company.name}}. Return valid JSON with score, subject, and opening fields.\"
            }
          ],
          \"model\": \"gpt-4\"
        }
      },
      \"id\": \"openai1\",
      \"name\": \"AI Lead Analysis\",
      \"type\": \"n8n-nodes-base.httpRequest\",
      \"typeVersion\": 4,
      \"position\": [
        1120,
        300
      ]
    },
    {
      \"parameters\": {
        \"conditions\": {
          \"number\": [
            {
              \"value1\": \"={{JSON.parse($json.choices[0].message.content).score}}\",
              \"operation\": \"larger\",
              \"value2\": 6
            }
          ]
        }
      },
      \"id\": \"if2\",
      \"name\": \"Check Score\",
      \"type\": \"n8n-nodes-base.if\",
      \"typeVersion\": 1,
      \"position\": [
        1340,
        300
      ]
    },
    {
      \"parameters\": {
        \"resource\": \"contact\",
        \"operation\": \"update\",
        \"contactId\": \"={{$('Get New Leads').item.json.id}}\",
        \"updateFields\": {
          \"properties\": [
            {
              \"name\": \"lead_score\",
              \"value\": \"={{JSON.parse($('AI Lead Analysis').item.json.choices[0].message.content).score}}\"
            }
          ]
        }
      },
      \"id\": \"hubspot2\",
      \"name\": \"Update Lead Score\",
      \"type\": \"n8n-nodes-base.hubspot\",
      \"typeVersion\": 1,
      \"position\": [
        1560,
        200
      ]
    },
    {
      \"parameters\": {
        \"options\": {},
        \"requestBody\": {
          \"messages\": [
            {
              \"role\": \"user\",
              \"content\": \"Generate a personalized cold email for {{$('Get New Leads').item.json.properties.firstname}} at {{$('Get New Leads').item.json.properties.company}}. Make it about AI SDR solutions. Return JSON with subject and body fields.\"
            }
          ],
          \"model\": \"gpt-4\"
        }
      },
      \"id\": \"openai2\",
      \"name\": \"Generate Email\",
      \"type\": \"n8n-nodes-base.httpRequest\",
      \"typeVersion\": 4,
      \"position\": [
        1780,
        200
      ]
    },
    {
      \"parameters\": {
        \"fromEmail\": \"<EMAIL>\",
        \"toEmail\": \"={{$('Get New Leads').item.json.properties.email}}\",
        \"subject\": \"={{JSON.parse($json.choices[0].message.content).subject}}\",
        \"text\": \"={{JSON.parse($json.choices[0].message.content).body}}\"
      },
      \"id\": \"email1\",
      \"name\": \"Send Email\",
      \"type\": \"n8n-nodes-base.emailSend\",
      \"typeVersion\": 2,
      \"position\": [
        2000,
        200
      ]
    },
    {
      \"parameters\": {
        \"amount\": 2,
        \"unit\": \"days\"
      },
      \"id\": \"wait1\",
      \"name\": \"Wait 2 Days\",
      \"type\": \"n8n-nodes-base.wait\",
      \"typeVersion\": 1,
      \"position\": [
        2220,
        200
      ]
    },
    {
      \"parameters\": {
        \"resource\": \"contact\",
        \"operation\": \"get\",
        \"contactId\": \"={{$('Get New Leads').item.json.id}}\"
      },
      \"id\": \"hubspot3\",
      \"name\": \"Check Response\",
      \"type\": \"n8n-nodes-base.hubspot\",
      \"typeVersion\": 1,
      \"position\": [
        2440,
        200
      ]
    },
    {
      \"parameters\": {
        \"conditions\": {
          \"string\": [
            {
              \"value1\": \"={{$json.properties.notes_last_updated}}\",
              \"operation\": \"isEmpty\"
            }
          ]
        }
      },
      \"id\": \"if3\",
      \"name\": \"No Response\",
      \"type\": \"n8n-nodes-base.if\",
      \"typeVersion\": 1,
      \"position\": [
        2660,
        200
      ]
    },
    {
      \"parameters\": {
        \"options\": {},
        \"requestBody\": {
          \"messages\": [
            {
              \"role\": \"user\",
              \"content\": \"Generate a follow-up email for {{$('Get New Leads').item.json.properties.firstname}}. Different angle, value-focused. Return JSON with subject and body.\"
            }
          ],
          \"model\": \"gpt-4\"
        }
      },
      \"id\": \"openai3\",
      \"name\": \"Generate Follow-up\",
      \"type\": \"n8n-nodes-base.httpRequest\",
      \"typeVersion\": 4,
      \"position\": [
        2880,
        120
      ]
    },
    {
      \"parameters\": {
        \"fromEmail\": \"<EMAIL>\",
        \"toEmail\": \"={{$('Get New Leads').item.json.properties.email}}\",
        \"subject\": \"={{JSON.parse($json.choices[0].message.content).subject}}\",
        \"text\": \"={{JSON.parse($json.choices[0].message.content).body}}\"
      },
      \"id\": \"email2\",
      \"name\": \"Send Follow-up\",
      \"type\": \"n8n-nodes-base.emailSend\",
      \"typeVersion\": 2,
      \"position\": [
        3100,
        120
      ]
    },
    {
      \"parameters\": {
        \"resource\": \"contact\",
        \"operation\": \"update\",
        \"contactId\": \"={{$('Get New Leads').item.json.id}}\",
        \"updateFields\": {
          \"properties\": [
            {
              \"name\": \"lifecycle_stage\",
              \"value\": \"marketing_qualified_lead\"
            }
          ]
        }
      },
      \"id\": \"hubspot4\",
      \"name\": \"Mark as MQL\",
      \"type\": \"n8n-nodes-base.hubspot\",
      \"typeVersion\": 1,
      \"position\": [
        2880,
        280
      ]
    },
    {
      \"parameters\": {
        \"resource\": \"contact\",
        \"operation\": \"update\",
        \"contactId\": \"={{$('Get New Leads').item.json.id}}\",
        \"updateFields\": {
          \"properties\": [
            {
              \"name\": \"lifecycle_stage\",
              \"value\": \"unqualified\"
            }
          ]
        }
      },
      \"id\": \"hubspot5\",
      \"name\": \"Mark Unqualified\",
      \"type\": \"n8n-nodes-base.hubspot\",
      \"typeVersion\": 1,
      \"position\": [
        1560,
        400
      ]
    },
    {
      \"parameters\": {
        \"jsCode\": \"const items = $input.all();\
const stats = {\
  total: items.length,\
  qualified: items.filter(i => i.json.properties && i.json.properties.lead_score > 6).length,\
  emails: items.filter(i => i.json.email_sent).length\
};\
return [{json: stats}];\"
      },
      \"id\": \"code1\",
      \"name\": \"Calculate Stats\",
      \"type\": \"n8n-nodes-base.code\",
      \"typeVersion\": 2,
      \"position\": [
        3320,
        200
      ]
    },
    {
      \"parameters\": {
        \"url\": \"https://hooks.slack.com/services/YOUR/WEBHOOK/URL\",
        \"sendBody\": true,
        \"specifyBody\": \"json\",
        \"jsonBody\": \"={\\\"text\\\": \\\"Daily SDR Stats: {{$json.total}} leads, {{$json.qualified}} qualified, {{$json.emails}} emails sent\\\"}\"
      },
      \"id\": \"slack1\",
      \"name\": \"Send Report\",
      \"type\": \"n8n-nodes-base.httpRequest\",
      \"typeVersion\": 4,
      \"position\": [
        3540,
        200
      ]
    }
  ],
  \"connections\": {
    \"Daily Trigger\": {
      \"main\": [
        [
          {
            \"node\": \"Get New Leads\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Get New Leads\": {
      \"main\": [
        [
          {
            \"node\": \"Filter Valid Leads\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Filter Valid Leads\": {
      \"main\": [
        [
          {
            \"node\": \"Enrich Lead Data\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Enrich Lead Data\": {
      \"main\": [
        [
          {
            \"node\": \"AI Lead Analysis\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"AI Lead Analysis\": {
      \"main\": [
        [
          {
            \"node\": \"Check Score\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Check Score\": {
      \"main\": [
        [
          {
            \"node\": \"Update Lead Score\",
            \"type\": \"main\",
            \"index\": 0
          }
        ],
        [
          {
            \"node\": \"Mark Unqualified\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Update Lead Score\": {
      \"main\": [
        [
          {
            \"node\": \"Generate Email\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Generate Email\": {
      \"main\": [
        [
          {
            \"node\": \"Send Email\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Send Email\": {
      \"main\": [
        [
          {
            \"node\": \"Wait 2 Days\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Wait 2 Days\": {
      \"main\": [
        [
          {
            \"node\": \"Check Response\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Check Response\": {
      \"main\": [
        [
          {
            \"node\": \"No Response\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"No Response\": {
      \"main\": [
        [
          {
            \"node\": \"Generate Follow-up\",
            \"type\": \"main\",
            \"index\": 0
          }
        ],
        [
          {
            \"node\": \"Mark as MQL\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Generate Follow-up\": {
      \"main\": [
        [
          {
            \"node\": \"Send Follow-up\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Send Follow-up\": {
      \"main\": [
        [
          {
            \"node\": \"Calculate Stats\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Mark as MQL\": {
      \"main\": [
        [
          {
            \"node\": \"Calculate Stats\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Mark Unqualified\": {
      \"main\": [
        [
          {
            \"node\": \"Calculate Stats\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    },
    \"Calculate Stats\": {
      \"main\": [
        [
          {
            \"node\": \"Send Report\",
            \"type\": \"main\",
            \"index\": 0
          }
        ]
      ]
    }
  }
}`
}