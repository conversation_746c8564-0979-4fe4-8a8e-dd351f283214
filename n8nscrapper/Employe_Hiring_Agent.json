{"name": "Hiring Agent", "nodes": [{"parameters": {"formTitle": "Apply For Executive Assistant at SAMSUNG", "formDescription": "Give your Info carefully", "formFields": {"values": [{"fieldLabel": "Name", "placeholder": "Full Name", "requiredField": true}, {"fieldLabel": "Email Address", "placeholder": "<EMAIL>", "requiredField": true}, {"fieldLabel": "Job", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Executive Assistant"}, {"option": "IT Specialist"}, {"option": "Manager"}]}, "requiredField": true}, {"fieldLabel": "Resume", "fieldType": "file", "multipleFiles": false, "acceptFileTypes": ".pdf", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-280, -40], "id": "adb94918-cb94-4b31-bbe7-beb43085216c", "name": "On form submission", "webhookId": "78e59dcf-52a0-45c9-99d8-4325d7b7cb2e"}, {"parameters": {"operation": "pdf", "binaryPropertyName": "Resume", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-80, -40], "id": "9c9d94f9-4c7a-4607-8f4b-5484e4e57c5b", "name": "Extract from File"}, {"parameters": {"promptType": "define", "text": "=Name: {{ $('On form submission').item.json.Name }}\nJob Name: {{ $('On form submission').item.json.Job }}\nResume: {{ $json.text }}", "options": {"systemMessage": "=You are a hiring agent for SAMSUNG. You will evaluate a candidate based on three inputs: the person's name, their job title, and their resume.\n\nYour task:\n1. Rate the resume out of 10 in terms of how suitable it is for the job title.\n2. Give a status: \"Accepted\" if score >= 7, else \"Rejected\".\n3. Generate an email:\n   - If \"Accepted\", write a short congratulatory email with the subject \"Congratulations on Joining Samsung!\" and mention the start date as 1st October 2026.\n   - If \"Rejected\", write a polite rejection email with the subject \"Regarding Your Application at Samsung\" in a soft and respectful tone.\n\nExmaple Output:\n\nScore: 8\nStatus: Accepted\nEmail: \n    Subject: Congratulations on Joining Samsung!\n    Body:\nDear <PERSON><PERSON>,\nWe are pleased to inform you that you have been selected for the position of Video Editor at Samsung. Your start date will be 1st October 2026.\nWelcome to the team!\n\nBest regards,\nSamsung HR Team"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [160, -140], "id": "89bd6a3f-ccc3-42d4-a6c0-bb22ab9f39b9", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [340, 140], "id": "3cb99231-c937-4d16-8327-d6875270c8f0", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"text": "={{ $json.output }}", "attributes": {"attributes": [{"name": "Score", "type": "number", "description": "the score", "required": true}, {"name": "Status", "description": "Accepted or Rejected", "required": true}, {"name": "Mail Subject", "description": "only the subject of the mail", "required": true}, {"name": "Mail Body", "description": "the body of the mail without the subject"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [500, -140], "id": "ace82bba-a974-4151-a3d5-2b15880966d4", "name": "Information Extractor"}, {"parameters": {"sendTo": "={{ $('On form submission').item.json['Email Address'] }}", "subject": "={{ $json.output['Mail Subject'] }}", "emailType": "text", "message": "={{ $json.output['Mail Body'] }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [880, -140], "id": "1434fd15-df76-4940-a786-4f28eaecd08d", "name": "Gmail", "webhookId": "9ce2f294-f38c-4c4c-88c8-7828088511c5", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1YBesKlPbUuWihzOjWx3wdtNZdo1VFKt6zyM6O4A8yWc", "mode": "list", "cachedResultName": "Candidate Details", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1YBesKlPbUuWihzOjWx3wdtNZdo1VFKt6zyM6O4A8yWc/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": ********, "mode": "list", "cachedResultName": "Form responses 1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1YBesKlPbUuWihzOjWx3wdtNZdo1VFKt6zyM6O4A8yWc/edit#gid=********"}, "columns": {"mappingMode": "defineBelow", "value": {"Name": "={{ $('On form submission').item.json.Name }}", "Job": "={{ $('On form submission').item.json.Job }}", "Score": "={{ $('Information Extractor').item.json.output.Score }}", "Status": "={{ $('Information Extractor').item.json.output.Status }}", "Email": "={{ $('On form submission').item.json['Email Address'] }}", "Email Status": "SENT ✅"}, "matchingColumns": [], "schema": [{"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Job", "displayName": "Job", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Score", "displayName": "Score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email Status", "displayName": "Email Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1100, -140], "id": "********-7b08-4872-b029-3212f12dec96", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "tw8iJbHLeqfMESfX", "name": "Google Sheets account"}}}], "pinData": {}, "connections": {"On form submission": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Gmail": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "32e73413-8467-4c91-bda8-21b6<PERSON><PERSON><PERSON>", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5c2f5b5b4cf20114a6c53aaed9430bfdabad5c4604b5a73e1363b96c75e842ec"}, "id": "Ux3SHMxxA3HKE3tW", "tags": []}