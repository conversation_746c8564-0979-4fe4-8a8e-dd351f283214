{"name": "AIS n8n best hacks", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-3740, -1200], "id": "286e959c-20b6-4a03-9156-3e3bcd452d09", "name": "When clicking ‘Test workflow’"}, {"parameters": {"content": "# 1 Multiple Triggers\n", "height": 560, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, -1860], "id": "d0725099-c786-4706-95f1-bc7fed40a183", "name": "<PERSON><PERSON>"}, {"parameters": {"updates": ["messages"], "options": {}}, "type": "n8n-nodes-base.whatsAppTrigger", "typeVersion": 1, "position": [-3500, -1740], "id": "a3a343cb-8d92-4239-9621-edb49e0846d8", "name": "<PERSON><PERSON><PERSON><PERSON>", "webhookId": "32573bc5-2c54-4078-af60-e47425d58b78", "credentials": {"whatsAppTriggerApi": {"id": "mdxV6hzetFkSSAkP", "name": "AIS WhatsApp on message"}}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-3500, -1540], "id": "ec98fc54-abbd-495d-a56c-9f96fda00431", "name": "<PERSON>eg<PERSON>", "webhookId": "5a2bce82-cfd8-4c14-b306-2b8865ca856d", "credentials": {"telegramApi": {"id": "bTH5mUmx8kqE7Iqf", "name": "AIS Telegram account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "d1265719-f518-4f7d-a8d1-19eabfc827d1", "name": "trigger1", "value": "=triggered by : {{ $json.messages[0].text.body }}", "type": "string"}, {"id": "63d2304e-aaf0-4d8a-a7af-363f20d14546", "name": "trigger2", "value": "=triggered by : {{ $json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-3240, -1660], "id": "********-c2a4-403e-bf53-920c065a4b6b", "name": "Common fields"}, {"parameters": {"jsCode": "return items.map(item => {\n  const email = item.json.email || \"<EMAIL>\";\n  const domain = email.split('@')[1];\n  item.json.domain = domain;\n  return item;\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3280, -1200], "id": "63c34907-1ef8-4d02-ba9c-f52bdc6163dc", "name": "Code"}, {"parameters": {"content": "# 2 Write Code", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, -1260], "id": "aa30e6a6-0c18-4ddd-9742-c74d4be85111", "name": "Sticky Note1"}, {"parameters": {"content": "# 3 Pin data", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, -900], "id": "a03a7749-36c3-450b-a24e-842f96a07dbb", "name": "Sticky Note2"}, {"parameters": {"content": "# 4 Webhooks method", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, -540], "id": "8e86a2c5-b2ce-4801-b30c-9b128baf9016", "name": "Sticky Note3"}, {"parameters": {"content": "# 5 Version history", "height": 80, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, -100], "id": "130d4aef-98a7-4a9d-b42b-e8d8f0ae1725", "name": "Sticky Note4"}, {"parameters": {"content": "# 6 Save Credentials", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, 160], "id": "e8277891-0666-4a46-9adc-f65e214529f8", "name": "Sticky Note5"}, {"parameters": {"content": "# 7 Continue on error", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, 500], "id": "cae19ee4-4d6b-4937-94f4-2814e3b1e8ae", "name": "Sticky Note6"}, {"parameters": {"jsCode": "return items.map(item => {\n  const email = item.json.email || \"<EMAIL>\";\n  const domain = email.split('@')[1];\n  item.json.domain = domain;\n  return item;\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3120, 580], "id": "b7006fae-7318-4482-a5e7-54e153841493", "name": "extract domain"}, {"parameters": {"url": "http://fakeurl.com", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3380, 580], "id": "c36fe191-75be-45a1-8492-a79f6a2330e0", "name": "HTTP Request", "onError": "continueRegularOutput"}, {"parameters": {"content": "# 8 Error on output", "height": 340, "width": 680}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, 840], "id": "e95a3231-4719-4bb1-9b30-f05c29629814", "name": "Sticky Note7"}, {"parameters": {"jsCode": "return items.map(item => {\n  const email = item.json.email || \"<EMAIL>\";\n  const domain = email.split('@')[1];\n  item.json.domain = domain;\n  return item;\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3120, 860], "id": "f6965101-70ba-4fc4-a165-502b28f61eda", "name": "extract domain 1"}, {"parameters": {"url": "http://fakeurl.com", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3380, 920], "id": "5dde537f-1920-4e54-b61a-a42400fd381b", "name": "HTTP Request 1", "onError": "continueErrorOutput"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Hello World", "message": "Hello World", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-3120, 1020], "id": "d3246929-8a33-4caa-af58-b3893d6b44f7", "name": "Gmail", "webhookId": "04d0bc4c-6de8-4382-9be2-b3d7a5af4a85", "credentials": {"gmailOAuth2": {"id": "qmEGVPxG059QdFxW", "name": "AIS Gmail account"}}}, {"parameters": {"content": "# 9 Other Node settings", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, 1220], "id": "86527ff4-c869-4cc2-bff1-77da4219aaca", "name": "Sticky Note8"}, {"parameters": {"url": "http://fakeurl.com", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3380, 1320], "id": "0f4a690d-08a1-423f-9169-26d73bdd83d3", "name": "HTTP Request 2"}, {"parameters": {"content": "# 10 Moving around nodes", "height": 300, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3560, 1580], "id": "8fc19ef2-8ea5-4d6f-9167-2953eda83cca", "name": "Sticky Note9"}, {"parameters": {"formTitle": "Baby Podcast Creation", "formDescription": "Baby Podcast Creation", "formFields": {"values": [{"fieldLabel": "Ethnicity", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "American"}, {"option": "European"}]}, "requiredField": true}, {"fieldLabel": "Baby Hair", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Curly"}, {"option": "Straight"}, {"option": "Bald"}]}, "requiredField": true}, {"fieldLabel": "Podcast Topic", "placeholder": "sports,movies,heatlh & fitness", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-3400, -800], "id": "c8f981ad-26a7-4f09-b72d-46fc4e796e50", "name": "On form submission", "webhookId": "5c4835f6-a251-494a-96c9-64ce72663bf5"}, {"parameters": {"httpMethod": "POST", "path": "5142a33a-147f-493e-b2a6-d9d8c991f8e6", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-3500, -440], "id": "6c752baa-a2e8-4256-ad91-d5f57dc68ffc", "name": "Webhook", "webhookId": "5142a33a-147f-493e-b2a6-d9d8c991f8e6"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <open ai apikey>"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.message.content.prompt }}"}, {"name": "size", "value": "1024x1536"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3460, 260], "id": "000a6f7d-64b0-4900-a31a-ac9157315253", "name": "openai exposed api"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <open ai apikey>"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.message.content.prompt }}"}, {"name": "size", "value": "1024x1536"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3140, 260], "id": "d7d6ce6d-6a95-46ec-a229-91ceea470622", "name": "openai saved credential", "credentials": {"httpHeaderAuth": {"id": "eWA1EKlCLqgks7pI", "name": "AIS open<PERSON> <PERSON><PERSON> "}}}, {"parameters": {"sendTo": "=", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1720, -1540], "id": "8156fcdf-b5c9-43a0-8c6a-e522e89a2b8d", "name": "Googlemail", "webhookId": "23d1d194-5e59-4175-80fe-1cf9e4610730", "credentials": {"gmailOAuth2": {"id": "qmEGVPxG059QdFxW", "name": "AIS Gmail account"}}}, {"parameters": {"additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2240, -920], "id": "6c0c8d05-7201-4c43-a5f0-7dda3db93217", "name": "Telegram", "webhookId": "b14da873-c4b3-44b0-b36e-baa78e2211dc", "credentials": {"telegramApi": {"id": "AwvhjnQJWoRvM99e", "name": "Telegram account"}}}, {"parameters": {"authentication": "oAuth2", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [-1780, -920], "id": "628f5305-cf98-46d2-8487-15eef7cf7b02", "name": "<PERSON><PERSON>ck", "webhookId": "8bf14c6f-54b7-4995-9f54-fa746add8a04", "credentials": {"slackOAuth2Api": {"id": "RxysvE1chZxHuOCi", "name": "Slack oauth22"}}}, {"parameters": {"sendTo": "=", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-2000, -920], "id": "0a389c67-a70e-45ab-a5ee-f29d9e3a2bd1", "name": "Googlemail1", "webhookId": "23d1d194-5e59-4175-80fe-1cf9e4610730", "credentials": {"gmailOAuth2": {"id": "qmEGVPxG059QdFxW", "name": "AIS Gmail account"}}}, {"parameters": {"promptType": "define", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-1780, 100], "id": "cbf47f76-3bfa-4888-8e54-e6aa9221a713", "name": "AI Agent"}, {"parameters": {"authentication": "oAuth2", "operation": "sendAndWait", "user": {"__rl": true, "mode": "list", "value": ""}, "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [-2260, 440], "id": "318d9828-41d4-4d88-97f8-ea5ff36c4245", "name": "Slack1", "webhookId": "e5409c96-1d8e-4103-b74d-9d12f186018c", "credentials": {"slackOAuth2Api": {"id": "RxysvE1chZxHuOCi", "name": "Slack oauth22"}}}, {"parameters": {"operation": "sendAndWait", "options": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2020, 440], "id": "4ccfa75a-0047-4ca1-a4a3-a1899846f4db", "name": "Telegram1", "webhookId": "45fbd53f-a5e7-48a8-9ac9-b39e9c736c31", "credentials": {"telegramApi": {"id": "AwvhjnQJWoRvM99e", "name": "Telegram account"}}}, {"parameters": {"operation": "sendAndWait", "options": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [-1800, 440], "id": "759982d8-d188-4f8e-91ec-935edd28f7f3", "name": "WhatsApp Business Cloud", "webhookId": "dffb019b-bc2f-4cf6-a477-a8163edb2aad", "credentials": {"whatsAppApi": {"id": "lmIEloQEj7as2bpT", "name": "WhatsApp account"}}}, {"parameters": {"operation": "sendAndWait", "options": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [-1700, 780], "id": "95e5edb2-bde0-44b1-9638-4b08c00697e1", "name": "WhatsApp Business Cloud1", "webhookId": "dffb019b-bc2f-4cf6-a477-a8163edb2aad", "credentials": {"whatsAppApi": {"id": "lmIEloQEj7as2bpT", "name": "WhatsApp account"}}}, {"parameters": {"formTitle": "Baby Podcast Creation", "formDescription": "Baby Podcast Creation", "formFields": {"values": [{"fieldLabel": "Ethnicity", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "American"}, {"option": "European"}]}, "requiredField": true}, {"fieldLabel": "Baby Hair", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Curly"}, {"option": "Straight"}, {"option": "Bald"}]}, "requiredField": true}, {"fieldLabel": "Podcast Topic", "placeholder": "sports,movies,heatlh & fitness", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-2200, -1760], "id": "ce40b5e8-3587-4be5-897f-cacb43565fa3", "name": "On form submission1", "webhookId": "9c33c354-a5a7-4f3c-a0d2-e8c5108b4196"}, {"parameters": {"content": "# 1. Pin/Unpin data (covered in Part 1 already)\n", "height": 220, "width": 760}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2400, -1840], "id": "82334d0f-6fa8-4a15-b39c-f1f59bb50080", "name": "Sticky Note10"}, {"parameters": {"content": "# 2. Organizing Workflows\n## -- use sticky notes \n## -- change colors\n## -- rename node\n", "height": 220, "width": 440, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2280, -1580], "id": "86d485c8-a359-4fb4-9c61-71bd111d2eb3", "name": "Sticky Note11"}, {"parameters": {"content": "# 3. Workflow History (covered in Part 1 already)\n## -- workflow definition changes\n## -- restore a previous version\n## -- clone workflow\n## -- download workflow\n", "height": 220, "width": 780}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2400, -1300], "id": "53241d9d-135a-4c15-944c-d5deb3c42f06", "name": "Sticky Note12"}, {"parameters": {"content": "# 4. n8n attribution\n## -- telegram node\n## -- gmail node\n## -- slack node\n\n", "height": 380, "width": 900}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2480, -1000], "id": "6f07f795-98f8-4095-bda4-ddf7dba4761c", "name": "Sticky Note13"}, {"parameters": {"content": "# 5. Add node quickly\n## -- hit tab key\n\n\n", "height": 220, "width": 440}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2280, -540], "id": "33da3952-3643-4d60-8063-7bfa13827f51", "name": "Sticky Note14"}, {"parameters": {"content": "# 6. Workflow variables\n## --workflow.id\n## --workflow.name\n\n\n\n", "height": 220, "width": 440}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2280, -240], "id": "00ac550e-83ab-4474-b21f-8db04381f852", "name": "Sticky Note15"}, {"parameters": {"content": "# 7. Fixed to Expression\n## --Press = Key\n\n\n\n\n", "height": 220, "width": 440}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2280, 60], "id": "b2b78eaa-268a-46bc-91a6-4ffa631cc8a9", "name": "Sticky Note16"}, {"parameters": {"content": "# 8. Human In The Loop\n## -- slack node\n\n## -- telegram node\n\n## -- What<PERSON><PERSON> node\n\n", "height": 300, "width": 900}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2500, 340], "id": "686bf9e8-8b2c-416d-a0dd-868bcd4b8b33", "name": "Sticky Note17"}, {"parameters": {"content": "# 9. Activate/Deactivate\n## --Click node and key \"d\"\n\n\n\n\n", "height": 220, "width": 440}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2280, 740], "id": "278b8e07-d4a7-434b-9d62-8374ea1d96f9", "name": "Sticky Note18"}, {"parameters": {"content": "# 10. Move between nodes\n## --arrow keys\n\n\n\n\n", "height": 220, "width": 440}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2280, 1040], "id": "b49567e7-5a72-4d66-8f2a-e5f95008547f", "name": "Sticky Note19"}, {"parameters": {"content": "# Part 1\n", "height": 4060, "width": 1080, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3800, -2020], "id": "f6fe4608-7217-49da-a850-4b4ffc552980", "name": "Sticky Note20"}, {"parameters": {"content": "# Part 2\n", "height": 4060, "width": 1120, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2520, -2020], "id": "a15932a1-c5e7-43e1-9725-32744c060cc0", "name": "Sticky Note21"}], "pinData": {"On form submission": [{"json": {"Ethnicity": "American", "Baby Hair": "Curly", "Podcast Topic": "fitness", "submittedAt": "2025-06-19T23:23:20.744-05:00", "formMode": "test"}}]}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "Common fields", "type": "main", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Common fields", "type": "main", "index": 0}]]}, "extract domain": {"main": [[]]}, "HTTP Request": {"main": [[{"node": "extract domain", "type": "main", "index": 0}]]}, "HTTP Request 1": {"main": [[{"node": "extract domain 1", "type": "main", "index": 0}], [{"node": "Gmail", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "timezone": "America/Chicago", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "67e616b6-88d6-42f9-8ba4-2660d17409b8", "meta": {"templateCredsSetupCompleted": true, "instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "id": "9qiYfdM4GLgIqPUG", "tags": []}