{"name": "Google Maps Scraping", "nodes": [{"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"search\": \"landscaping\",\n  \"location\": \"New York\",\n  \"state\": \"NY\",\n  \"country\": \"us\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "ca2d3552-5624-46f3-89a4-84e69891dddf", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/2Mdma1N6Fd0y3QEjR/runs?waitForFinish=300", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <YOUR_API_KEY>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"city\": \"{{ $json.location }}\",\n    \"countryCode\": \"{{ $json.country }}\",\n    \"language\": \"en\",\n    \"locationQuery\": \"{{ $json.location }}\",\n    \"maxCrawledPlacesPerSearch\": 2,\n    \"searchStringsArray\": [\n        \"{{ $json.search }}\"\n    ],\n    \"skipClosedPlaces\": false,\n    \"state\": \"{{ $json.state }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "f25d8729-871c-436d-9c1c-fcd56dc4b598", "name": "HTTP Request"}, {"parameters": {"url": "=https://api.apify.com/v2/datasets/{{ $json.data.defaultDatasetId }}/items?format=json", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_API_KEY>"}, {"name": "="}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "b13d3f8b-a616-4d5e-b797-66fbb07c152e", "name": "HTTP Request1"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [880, 0], "id": "3cb86a4b-6924-4452-9134-bba953c41ed3", "name": "Aggregate"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4", "mode": "list", "cachedResultName": "AI Agent Web Scraping Results", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"ID": "={{ $json.url }}", "City": "=", "URL": "={{ $json.website }}", "Name": "={{ $json.title }}", "Phone": "={{ $json.phone }}", "Service": "={{ $json.searchString }}", "Platform": "Google Maps"}, "matchingColumns": ["ID"], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Service", "displayName": "Service", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "City", "displayName": "City", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Platform", "displayName": "Platform", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [660, 0], "id": "61869c62-2238-4064-b652-c1e77607fd66", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "oHG2DldApzJrCoBY", "name": "Google Sheets account 2"}}}], "pinData": {"When Executed by Another Workflow": [{"json": {"search": "landscaping", "location": "New York", "state": "NY", "country": "us"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a7bf9b17-f86c-4248-8bd7-************", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d2017cc6d1e4b956d269a8123bffa72fb7aaa41ad37a73b7c0fb64c7d0e2edae"}, "id": "12AKoHjuuvaPIHb6", "tags": []}