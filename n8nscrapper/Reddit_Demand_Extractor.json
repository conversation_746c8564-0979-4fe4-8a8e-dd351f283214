{"name": "<PERSON><PERSON> Extractor", "nodes": [{"parameters": {}, "id": "e8ae6cdf-ac64-4283-b3d0-5814ae80ec5c", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-2040, 840], "typeVersion": 1}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "id": "c9e26c9e-3d75-4a0d-8996-9ea06f9999dd", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-840, 860], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "7RJfRoyoPvBnrEwy", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "id": "fd71575d-9e5c-4d1e-9fe2-2681e6f1b803", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 420], "typeVersion": 1.2, "credentials": {"openAiApi": {"id": "7RJfRoyoPvBnrEwy", "name": "OpenAi account"}}}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Based on the following Reddit post, suggest a the authors core pain point, their current stage of awareness how to solve it, and what they actually need to do to solve this problem that I could write about to help this problem for this business and other with similar needs.\n\nReddit post:  \"{{ $json.postcontent }}\"\n\nProvide a concise description of a core desire, paint points and expressions the authors names that would adress this issue effectively so I can create targeted content speaking to their core needs and desires. Output in consice list format, no bold.\n"}]}, "options": {}}, "id": "b3880236-300e-4816-9952-b55dcde1c040", "name": "Find Proper Solutions", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [380, 440], "typeVersion": 1.8, "credentials": {"openAiApi": {"id": "7RJfRoyoPvBnrEwy", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "id": "2f829237-783d-43a9-ae51-24abb1199f1a", "name": "Post Summarization", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [260, 220], "typeVersion": 2}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "id": "c71b266b-f6d2-4b9f-9e70-34fdff945dad", "name": "Merge <PERSON>", "type": "n8n-nodes-base.merge", "position": [-540, 940], "typeVersion": 3}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1QurQVZbWGdueGw-88Qd4yCJGbJnN_Cc9M_Lbv0oHnM8", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Reddit Email Marketing", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1QurQVZbWGdueGw-88Qd4yCJGbJnN_Cc9M_Lbv0oHnM8/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Problem": "={{ $json.response.text }}", "Solution": "={{ $json.message.content }}", "Source URL": "={{ $json.url }}"}, "matchingColumns": ["Result"], "schema": [{"id": "Problem", "displayName": "Problem", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Solution", "displayName": "Solution", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Source URL", "displayName": "Source URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "42f83d2b-c7ea-4e89-9abc-79b04fd71085", "name": "Output The Results", "type": "n8n-nodes-base.googleSheets", "position": [1180, 140], "typeVersion": 4.5, "credentials": {"googleSheetsOAuth2Api": {"id": "LaeetbMpjdSWgH0Q", "name": "Google Sheets account 2"}}}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {}}, "id": "77d218c4-bd4d-4f05-9c52-a42015ad901f", "name": "Merge 3 Inputs", "type": "n8n-nodes-base.merge", "position": [860, 460], "typeVersion": 3}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0823d10a-ad54-4d82-bcea-9dd236e97857", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.ups }}", "rightValue": 2}, {"id": "bb8187aa-f0f1-4999-8d4b-bdc9abba0618", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.selftext }}", "rightValue": ""}, {"id": "539f0f5c-025a-4f82-9b3a-2ef1ad3a2d96", "operator": {"type": "dateTime", "operation": "after"}, "leftValue": "={{ DateTime.fromSeconds($json.created).toISO() }}", "rightValue": "={{ $today.minus(180,'days').toISO() }}"}]}, "options": {}}, "id": "0d98f581-68a2-4c93-afcc-4425c0e191bd", "name": "Filter Posts By Features", "type": "n8n-nodes-base.if", "position": [-1620, 840], "typeVersion": 2.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d5d38c01-3a88-4767-b488-d9c04145bb8f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "yes"}]}, "options": {}}, "id": "9b54e1a6-eb01-4fb7-a542-464fce9a49d8", "name": "Filter Posts By Content", "type": "n8n-nodes-base.if", "position": [-340, 940], "typeVersion": 2.2}, {"parameters": {"assignments": {"assignments": [{"id": "e5082ecc-3add-474e-bdb5-b8ad64729930", "name": "upvotes", "type": "string", "value": "={{ $json.ups }}"}, {"id": "a92b5859-fbcc-40c2-95e0-452b12530d98", "name": "subreddit_subscribers", "type": "number", "value": "={{ $json.subreddit_subscribers }}"}, {"id": "a846e21c-6cff-4521-9e0c-a32fa1305376", "name": "postcontent", "type": "string", "value": "={{ $json.selftext }}"}, {"id": "b8045389-684d-4872-9e32-9a6b5511eb2b", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "f182fedc-1b09-40fe-aeb5-2473263da442", "name": "date", "type": "string", "value": "={{ DateTime.fromSeconds($json.created).toISO() }}"}]}, "options": {}}, "id": "1bf00ffc-e579-4ecd-893c-dd8b90be6e46", "name": "Select Key Fields", "type": "n8n-nodes-base.set", "position": [-1380, 820], "typeVersion": 3.4}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Decide whether this reddit post is describing a business-related problem or a need for a solution. The post should mention a specific challenge or requirement that a business is trying to address.\nReddit post:  {{ $json.postcontent }}\nIs this post about a business problem or need for a solution ? Output only yes or no", "options": {}}, "id": "ecbc6045-00e9-4a6c-ae61-8e736de96042", "name": "Analysis Content  By AI", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-920, 660], "typeVersion": 1.7}, {"parameters": {"operation": "search", "subreddit": "=Emailmarketing", "keyword": "the best", "limit": 20, "additionalFields": {"sort": "hot"}}, "id": "********-95d6-4003-a83a-268d290111ee", "name": "Get Posts", "type": "n8n-nodes-base.reddit", "position": [-1820, 840], "typeVersion": 1, "credentials": {"redditOAuth2Api": {"id": "nUOZOFNm7CSvJILw", "name": "Reddit account"}}}, {"parameters": {"content": "# Data Collection\n## Retrieves recent popular posts from specified Reddit communities\n## Filters content by engagement metrics and keywords", "height": 440, "width": 920}, "id": "c671f4de-343c-41f9-8f0e-4778417a70b0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-2140, 600], "typeVersion": 1}, {"parameters": {"content": "# Analysis Content\n## Core problem, desire analysis, market needs\n## Underserved customer demands and opportunity\n", "height": 680, "width": 820, "color": 4}, "id": "9726b3c6-4d3d-42cc-8698-34f72e4c6bd8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-960, 480], "typeVersion": 1}, {"parameters": {"content": "# Insight Generation And Output \n## Generates executive summaries of key opportunities\n## Consolidates findings in Google Sheets", "height": 640, "width": 1220, "color": 6}, "id": "77792e46-ce3b-48d4-aee2-9c14b8ee8741", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [40, 20], "typeVersion": 1}], "pinData": {}, "connections": {"Get Posts": {"main": [[{"node": "Filter Posts By Features", "type": "main", "index": 0}]]}, "Merge Input": {"main": [[{"node": "Filter Posts By Content", "type": "main", "index": 0}]]}, "Merge 3 Inputs": {"main": [[{"node": "Output The Results", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Analysis Content  By AI", "type": "ai_languageModel", "index": 0}]]}, "Select Key Fields": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 1}, {"node": "Analysis Content  By AI", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Post Summarization", "type": "ai_languageModel", "index": 0}]]}, "Post Summarization": {"main": [[{"node": "Merge 3 Inputs", "type": "main", "index": 0}]]}, "Find Proper Solutions": {"main": [[{"node": "Merge 3 Inputs", "type": "main", "index": 1}]]}, "Analysis Content  By AI": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 0}]]}, "Filter Posts By Content": {"main": [[{"node": "Post Summarization", "type": "main", "index": 0}, {"node": "Find Proper Solutions", "type": "main", "index": 0}, {"node": "Merge 3 Inputs", "type": "main", "index": 2}]]}, "Filter Posts By Features": {"main": [[{"node": "Select Key Fields", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Posts", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a1264dbd-f006-4250-bf46-2b26ad08cd4c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d1cbe5e4d484094864949e27278cd8f17ab1670f83a5efa855a5715a5670720d"}, "id": "cWBOEYBURRLs1GSs", "tags": [{"name": "Social Reading", "id": "7UzKa0MVR6FUiJfx", "createdAt": "2025-07-01T12:29:21.500Z", "updatedAt": "2025-07-01T12:29:21.500Z"}, {"name": "Reddit", "id": "IIi1IfWnhIwVz7vL", "createdAt": "2025-07-01T12:29:02.052Z", "updatedAt": "2025-07-01T12:29:02.052Z"}]}