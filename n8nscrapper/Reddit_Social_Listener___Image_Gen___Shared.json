{"name": "Reddit Social Listener + Image Gen - Shared", "nodes": [{"parameters": {"operation": "search", "location": "allReddit", "keyword": "={{ $json.message.content }}", "limit": 10, "additionalFields": {}}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [-900, 0], "id": "a633a98e-3d02-4465-919b-74c02d2423e1", "name": "Reddit", "credentials": {"redditOAuth2Api": {"id": "dmC9CiVpdOFg99r8", "name": "Reddit Integration oAuth"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1f62a8d7-2859-41ce-b87d-d34777bfc19b", "leftValue": "={{ $json.ups }}", "rightValue": 2, "operator": {"type": "number", "operation": "gte"}}, {"id": "1b3eb507-03e0-4cd3-8ebd-54bdfde787d5", "leftValue": "={{ $json.selftext }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {"ignoreCase": false}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-500, 0], "id": "1bc79e89-d362-4666-a8a9-241b2bca56e9", "name": "Filter Upvotes + Text"}, {"parameters": {"assignments": {"assignments": [{"id": "b2311fdd-e2bc-413f-a948-2fe4886d78e1", "name": "upvotes", "value": "={{ $('Reddit').item.json.ups }}", "type": "string"}, {"id": "991a6ac8-0c9d-4975-8612-b6df33c7f4e7", "name": "subreddit", "value": "={{ $('Reddit').item.json.subreddit }}", "type": "string"}, {"id": "c219f718-c595-4156-ae87-b672c50df142", "name": "originalPost", "value": "={{ $('Reddit').item.json.selftext }}", "type": "string"}, {"id": "86566583-b073-4615-b3c4-114c9f1de518", "name": "title", "value": "={{ $('Reddit').item.json.title }}", "type": "string"}, {"id": "faa2a938-05af-4644-9d37-a55fe748656c", "name": "url", "value": "={{ $('Reddit').item.json.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [400, -80], "id": "0d63bbe5-6e23-4ac9-a342-9f4693e5b079", "name": "<PERSON>"}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "=You are a copywriter and strategist helping a DTC brand turn Reddit pain points into powerful marketing messages.\n\nHere are Reddit posts expressing a problems or frustration:\n\n{{ JSON.stringify($json.data) }}\n\n\nThe brand's product is described as: {{ $('On form submission').item.json['Product Explanation'] }}\n\nYour job is to create 10 unique, punchy variations of how this product solves the pain in the Reddit posts.\n\nEach variation should be:\n\n- 1–2 sentences\n- Emotionally resonant\n- Framed as a \"hero\" moment (where the product enters to solve the issue)\n\nThink of these like ad angles or mini comic scripts. Output the list clearly as:\n\n1. ...\n2. ...\n3. ...\n...\n10. ...\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [940, -80], "id": "f4495f17-94d4-4ad1-b56e-de629a64d5b5", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [940, 60], "id": "bfbfa81b-292e-4b30-951c-5cbc782a2f3f", "name": "Anthropic <PERSON>"}, {"parameters": {"formTitle": "Add Details Here ", "formFields": {"values": [{"fieldLabel": "Brand Name", "requiredField": true}, {"fieldLabel": "Website", "requiredField": true}, {"fieldLabel": "Product Explanation", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-1600, 0], "id": "6a1f717d-0041-4e82-b11c-418dd480865e", "name": "On form submission", "webhookId": "d970aa7f-e00b-4d02-8712-1e4660441228"}, {"parameters": {"modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "messages": {"values": [{"content": "=You are a marketing strategist and Reddit researcher. \nGiven the following product description, extract a single keyword or phrase (1 to 3 words max) \nthat people on Reddit might use when discussing pain points related to this product or service.\n\nReturn ONLY the keyword or phrase, no extra text.\n\n\nProduct Description:{{ $json['Product Explanation'] }}\n"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1400, 0], "id": "880ac57b-75b0-4f17-b041-801a08193a2d", "name": "OpenAI", "credentials": {"openAiApi": {"id": "oh0Z4G6r5neaRiER", "name": "<PERSON>"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [1120, 60], "id": "b5730848-8c3f-4d22-9012-45fc54419930", "name": "Think"}, {"parameters": {"modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "messages": {"values": [{"content": "=You are a marketing strategist helping evaluate potential ad angles for a DTC product.\n\nThe product is described as: {{ $('On form submission').item.json['Product Explanation'] }}\n\nBelow is a list of short ad variations generated from Reddit-sourced user pain points.\n\nPlease do the following:\n1. Review all variations.\n2. Select the top 10 that are most relevant and persuasive for this product.\n3. Re-rank them from best to least among the 10.\n4. Return them clearly as a numbered list (1 to 10), with no commentary.\n5. Only select the 10 most emotionally persuasive and product-relevant ad angles. Ignore redundant or vague lines.\n\n\nVariations: {{ $json.output }}"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1400, -80], "id": "22d1970f-d2fc-4ee1-ab85-654c10df4c93", "name": "OpenAI1"}, {"parameters": {"promptType": "define", "text": "=You are a creative director making 4-panel comic ads for a product.\n\nThe product is: {{ $('On form submission').item.json['Product Explanation'] }}\n\nHere’s the ad message: {{ $json.message.content }}\n\nWrite a prompt for an AI image generator that describes a 4-panel comic showing this message as a visual story.\n\nFormat the output like:\n\n**Prompt**:  \nCreate a single image showing a 4-panel comic in clean, minimal style.  \nPanel 1: [setup/pain point]  \nPanel 2: [escalation]  \nPanel 3: [product enters heroically]  \nPanel 4: [positive outcome]  \n", "hasOutputParser": true, "options": {"systemMessage": "Please return **only** a JSON array of 10 objects, each with a single property `\"prompt\"` containing the image prompt. Example output structure:\n\n[\n  {\n    \"prompt\": \"Sun-drenched poolside shot of the product on a marble ledge at golden hour, with soft shadows and warm tones. Aspect ratio 1:1.\"\n  },\n  {\n    \"prompt\": \"Cool lavender-tinted sunset beach backdrop behind the product, highlighting reflective metallic accents. Aspect ratio 4:5.\"\n  },\n  {\n    \"prompt\": \"...\"\n  }\n]"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1900, -80], "id": "ad75a8df-d012-471c-a4c9-aca8c6236e58", "name": "AI Agent1"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "claude-3-7-sonnet-20250219", "cachedResultName": "Claude 3.7 Sonnet"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [1840, 80], "id": "62a7148d-b4ac-494e-8f2f-ba94b8253d2f", "name": "Anthropic Chat Model1"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [1960, 80], "id": "9119f246-e7b3-486f-929c-88ca6d68bc6a", "name": "Think1"}, {"parameters": {"fieldToSplitOut": "output", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2340, -80], "id": "301b01ab-5c70-44a3-9553-30eec170f698", "name": "Split Out"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.prompt }}"}, {"name": "size", "value": "1024x1024"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2860, -80], "id": "a5f7f155-c645-4e90-a909-17a2ee490ca8", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "hVhd4thoDKMcb8wd", "name": "Perplexity"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [3060, -80], "id": "a052452b-a588-4e45-b168-105faff13cc0", "name": "Convert to File"}, {"parameters": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1R5bTxrKmi9NDMFJIh3aQgbNuZwmCybLV", "mode": "list", "cachedResultName": "n8n Testing", "cachedResultUrl": "https://drive.google.com/drive/folders/1R5bTxrKmi9NDMFJIh3aQgbNuZwmCybLV"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [3280, -80], "id": "00ddb9e2-e6a3-44d0-8fb5-c84066c0a7b2", "name": "Google Drive"}, {"parameters": {"content": "## Generate Reddit Keyword ", "height": 340, "width": 580, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1660, -120], "id": "e814f43e-6eec-4a27-8fad-02ad7e8c509c", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## <PERSON><PERSON>e Reddit", "height": 340, "width": 340, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1020, -120], "id": "b5e055df-3e09-4433-9ff4-3b2d5ed527d4", "name": "Sticky Note1"}, {"parameters": {"content": "## Filter Posts By Popularity", "height": 340, "width": 340, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-620, -120], "id": "6ae382c3-aaa2-49c8-a393-6619d4dbae3d", "name": "Sticky Note2"}, {"parameters": {"content": "## Turn Painpoints Into Messaging", "height": 400, "width": 400, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [860, -180], "id": "e2eb5465-262a-4fb6-9677-7d8598bbdf59", "name": "Sticky Note3"}, {"parameters": {"content": "## Rank Top 10 Messaging", "height": 400, "width": 400, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1320, -180], "id": "3b561b91-09de-4740-8da6-a56f810b1631", "name": "Sticky Note4"}, {"parameters": {"content": "## Create Image Prompt", "height": 400, "width": 460, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1780, -180], "id": "d4151e6e-5989-4d88-aff1-01cce7a51728", "name": "Sticky Note5"}, {"parameters": {"content": "## Create Images", "height": 400, "width": 900, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2560, -180], "id": "799dec1d-ca9f-46bb-9aee-d03668914829", "name": "Sticky Note6"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [640, -80], "id": "d6170743-4022-492d-b2a3-999a5bf277e8", "name": "Aggregate"}, {"parameters": {"content": "## Post Relevance Classifier", "height": 340, "width": 500, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, -120], "id": "d54fa8db-8970-40ae-9ac0-798fc4c5f1b5", "name": "Sticky Note7"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-160, 80], "id": "6f3eb86d-a0f2-4423-aae9-291953966786", "name": "OpenAI Chat Model"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [0, 80], "id": "e34b49b8-c7d4-47e6-91ed-e1167c1e1ef3", "name": "Think2"}, {"parameters": {"promptType": "define", "text": "=Important: always use the think2 tool\n\nYou are a content relevance classifier.\n\nGiven the following DTC product description:\n{{ $('On form submission').item.json['Product Explanation'] }}\n\nAnd the details of a single Reddit post:\n• **Title**: {{ $('Reddit').item.json.title }}\n• **Content**: {{ $('Reddit').item.json.selftext }}\n• **Subreddit**: {{ $('Reddit').item.json.subreddit }}\n• **Upvotes**: {{ $('Reddit').item.json.ups }}\n• **Upvote Ratio**: {{ $('Reddit').item.json.upvote_ratio }}\n• **URL**: {{ $('Reddit').item.json.url }}\n\nYour task is to determine whether this post is relevant for marketing our product.\nReturn **only** a JSON array with one object containing two fields:\n- `isRelevant`: `true` if the post is relevant, otherwise `false`\n- `reason`: a short explanation of why you judged it relevant or not\n\nExample output:\n[\n  {\n    \"isRelevant\": true,\n    \"reason\": \"The user describes the exact pain point our product solves.\"\n  }\n]\n", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-100, -60], "id": "58fc930d-aa7b-44ab-9104-a9f82a4c44e1", "name": "Post Relevance Classifier"}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"isRelevant\": true,\n    \"reason\": \"The user describes the exact pain point our product solves.\"\n  }\n]\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [140, 80], "id": "24a8b537-252a-4ffc-b0e5-bc119747ee4f", "name": "Structured Output Parser"}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"prompt\": \"Sun-drenched poolside shot of the product on a marble ledge at golden hour, with soft shadows and warm tones. Aspect ratio 1:1.\"\n  },\n  {\n    \"prompt\": \"Cool lavender-tinted sunset beach backdrop behind the product, highlighting reflective metallic accents. Aspect ratio 4:5.\"\n  },\n  {\n    \"prompt\": \"...\"\n  }\n]"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2100, 80], "id": "705ec9c0-dcd5-4f71-a4e9-9ca45fce6222", "name": "Structured Output Parser1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2640, -80], "id": "6d54a631-dd94-4753-82e0-6b5f9b655cda", "name": "Loop Over Items"}], "pinData": {"On form submission": [{"json": {"Brand Name": "Try B<PERSON>", "Website": "drinkbrez.com", "Product Explanation": "Try Brez is a microdosed social tonic—sparkling and non-alcoholic—blending hemp-derived THC, CBD, and Lion’s Mane for a light, euphoric buzz without the hangover.", "submittedAt": "2025-05-15T10:50:59.968+02:00", "formMode": "test"}}]}, "connections": {"Reddit": {"main": [[{"node": "Filter Upvotes + Text", "type": "main", "index": 0}]]}, "Filter Upvotes + Text": {"main": [[{"node": "Post Relevance Classifier", "type": "main", "index": 0}], []]}, "Edit Fields": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Reddit", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Anthropic Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Think1": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Post Relevance Classifier", "type": "ai_languageModel", "index": 0}]]}, "Think2": {"ai_tool": [[{"node": "Post Relevance Classifier", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Post Relevance Classifier", "type": "ai_outputParser", "index": 0}]]}, "Post Relevance Classifier": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "AI Agent1", "type": "ai_outputParser", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3ca38996-4f79-455a-b138-281b5d186b8b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e0f3afdfa8cdf759f3628c3983ef08ff7c153a90839a4c34ddd866854a12653a"}, "id": "st864QZtIu7dS5wB", "tags": []}