{"id": "102", "name": "Send updates about the position of the ISS every minute to a topic in ActiveMQ", "nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [510, 300], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [710, 300], "parameters": {"url": "https://api.wheretheiss.at/v1/satellites/25544/positions", "options": {}, "queryParametersUi": {"parameter": [{"name": "timestamps", "value": "={{Date.now();}}"}]}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [910, 300], "parameters": {"values": {"number": [{"name": "Latitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"latitude\"]}}"}, {"name": "Longitude", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"longitude\"]}}"}, {"name": "Timestamp", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"timestamp\"]}}"}], "string": [{"name": "Name", "value": "={{$node[\"HTTP Request\"].json[\"0\"][\"name\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "AMQP Sender", "type": "n8n-nodes-base.amqp", "position": [1110, 300], "parameters": {"sink": "iss-postition", "options": {}}, "credentials": {"amqp": "ampq"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Set": {"main": [[{"node": "AMQP Sender", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}