{"name": "AI Avatar Video Bot IG", "flow": [{"id": null, "module": "placeholder:Placeholder", "metadata": {"designer": {"x": 0, "y": 0}}}, {"id": 4, "module": "util:SetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "Select Topic", "scope": "roundtrip", "value": "{{switch((floor(random * (15 - 1 + 1)) + 1); 1; \"leveraging A.I. in business\"; 2; \"no-code\"; 3; \"marketing automation\"; 4; \"neuro-marketing tactics\"; 5; \"lessons from old-school copywriting masters\"; 6; \"persuasion tactics\"; 7; \"consumer psychology\"; 8; \"modern copywriting tips\"; 9; \"conversion optimization\"; 10; \"sales writing tactics\"; 11; \"persuasion techniques in copywriting\"; 12; \"consumer neuroscience\"; 13; \"low-code development\"; 14; \"SaaS\"; 15; \"marketing for small businesses\")}}"}, "metadata": {"designer": {"x": 300, "y": 0, "name": "Topic Randomizer"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "Select Topic", "type": "any", "label": "Select Topic"}]}}, {"id": 5, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 579692}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You are a content creator avatar that creates commentary on a number of topics that are used as scripts for video content on social media."}, {"role": "user", "content": "Prompt: Create a video script on the provided topic that will be used in an Instagram Reel (video) between 20 to 30 seconds long. Do not include words like \"Action\" or \"Cut.\" You are not a director, just writing the words for the teleprompter. Do not use emojis since this will be read as a script.\nTopic: {{4.`Select Topic`}}\nConstraints: Do not include pretext or context, only return the video script. Be sure the script will take at least 20 seconds to read, but not take longer than 30 seconds to read. Remember, no \"Action\" or \"Cut\" director words, and no emojis. Only return the words to be read on screen."}], "max_tokens": "1000", "temperature": "1.1"}, "metadata": {"designer": {"x": 600, "y": 0, "name": "GPT <PERSON> Writer"}, "restore": {"expect": {"echo": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}]}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "anthonylee991 OpenAI"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["prompt", "chat"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature"}, {"name": "top_p", "type": "number", "label": "Top p"}, {"name": "n_completions", "type": "number", "label": "N"}, {"name": "echo", "type": "boolean", "label": "Echo"}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content", "required": true}], "type": "array", "label": "Messages", "required": true}], "advanced": true}}, {"id": 12, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{trim(replace(5.choices[].message.content; \"\"\"\"; space))}}"}, "metadata": {"designer": {"x": 900, "y": 0}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 1, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": false, "useNewZLibDeCompress": true}, "mapper": {"url": "https://api.d-id.com/talks", "serializeUrl": false, "method": "post", "headers": [{"name": "Authorization", "value": "Basic YOUR API KEY"}, {"name": "Accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}], "qs": [], "bodyType": "raw", "parseResponse": true, "authUser": "", "authPass": "", "timeout": "", "shareCookies": false, "ca": "", "rejectUnauthorized": true, "followRedirect": true, "useQuerystring": false, "gzip": true, "useMtls": false, "contentType": "application/json", "data": "{     \"script\": {          \"type\": \"text\",          \"provider\": {               \"type\": \"microsoft\",               \"voice_id\": \"<PERSON>\"          },          \"ssml\": \"false\",          \"input\": {{12.json}}     },     \"config\": {          \"fluent\": \"false\",          \"pad_audio\": \"0.0\"     },     \"source_url\": \"URL TO YOUR IMAGE\"}", "followAllRedirects": false}, "metadata": {"designer": {"x": 1200, "y": 0, "name": "Create Video"}, "restore": {"expect": {"method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null, null, null]}, "qs": {"mode": "chose"}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "qs", "type": "array", "label": "Query String", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 3, "module": "util:FunctionSleep", "version": 1, "parameters": {}, "mapper": {"duration": "60"}, "metadata": {"designer": {"x": 1500, "y": 0}, "restore": {}, "expect": [{"name": "duration", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Delay", "required": true, "validate": {"max": 300, "min": 1}}]}}, {"id": 2, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": false, "useNewZLibDeCompress": true}, "mapper": {"url": "https://api.d-id.com/talks/{{1.data.id}}", "serializeUrl": false, "method": "get", "headers": [{"name": "Authorization", "value": "Basic YOUR API KEY"}, {"name": "Accept", "value": "application/json"}], "qs": [], "bodyType": "raw", "parseResponse": true, "authUser": "", "authPass": "", "timeout": "", "shareCookies": false, "ca": "", "rejectUnauthorized": true, "followRedirect": true, "useQuerystring": false, "gzip": true, "useMtls": false, "contentType": "", "data": "", "followAllRedirects": false}, "metadata": {"designer": {"x": 1800, "y": 0, "name": "Fetch Video"}, "restore": {"expect": {"method": {"mode": "chose", "label": "GET"}, "headers": {"mode": "chose", "items": [null, null]}, "qs": {"mode": "chose"}, "bodyType": {"label": "Raw"}, "contentType": {"label": "Empty"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "qs", "type": "array", "label": "Query String", "spec": [{"name": "name", "label": "Name", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text"}]}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 9, "module": "cloudconvert:ConvertFile", "version": 2, "parameters": {"__IMTCONN__": 916716}, "mapper": {"export": {"downloadFile": false}, "import": {"url": "{{2.data.result_url}}", "importType": "url"}, "options": {"opts": [{"name": "resolution", "value": "1280x720 (720p HD)"}], "filename": "aivideo.mp4", "input_format": "mp4", "useInputType": "yes", "output_format": "mp4"}}, "metadata": {"designer": {"x": 2100, "y": 0, "name": "Compress Video"}, "restore": {"expect": {"export": {"nested": {"downloadFile": {"mode": "chose"}}}, "import": {"nested": {"headers": {"mode": "chose"}, "importType": {"label": "import a file from URL"}}}, "options": {"nested": {"opts": {"mode": "chose", "items": [null]}, "input_format": {"mode": "chose", "label": "mp4"}, "useInputType": {"mode": "chose", "label": "I want to select the input format"}, "output_format": {"mode": "chose", "label": "mp4"}}}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "cloudconvert3"}, "label": "CloudConvert (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:cloudconvert3,cloudconvert2", "label": "Connection", "required": true}], "expect": [{"name": "import", "spec": [{"name": "importType", "type": "select", "label": "Input file", "required": true, "validate": {"enum": ["upload", "url"]}}, {"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value", "required": true}], "type": "array", "label": "Headers"}], "type": "collection", "label": "Import options", "required": true}, {"name": "options", "spec": [{"name": "useInputType", "type": "select", "label": "Format", "required": true, "validate": {"enum": ["yes", "no"]}}, {"name": "input_format", "type": "select", "label": "Input format", "required": true}, {"name": "output_format", "type": "select", "label": "Output format", "required": true}, {"name": "filename", "type": "filename", "label": "File name", "required": true}, {"name": "opts", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Conversion and engine specific options"}], "type": "collection", "label": "Convert options", "required": true}, {"name": "export", "spec": [{"name": "downloadFile", "type": "boolean", "label": "Download a file", "required": true}], "type": "collection", "label": "Export options", "required": true}]}}, {"id": 6, "module": "instagram-business:CreateAReelPost", "version": 1, "parameters": {"__IMTCONN__": 772091}, "mapper": {"caption": "{{trim(replace(5.choices[].message.content; \"\"\"\"; space))}}", "accountId": "*****************", "video_url": "{{9.url}}", "share_to_feed": true}, "metadata": {"designer": {"x": 2400, "y": 0, "name": "Post As Reel"}, "restore": {"expect": {"accountId": {"mode": "chose", "label": "<PERSON><PERSON><PERSON><PERSON> (@aimarketingbot1)"}, "share_to_feed": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "facebook"}, "label": "AIMarketingBot1 (<PERSON>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:facebook", "label": "Connection", "required": true}], "expect": [{"name": "accountId", "type": "select", "label": "Page", "required": true}, {"name": "video_url", "type": "url", "label": "Video URL", "required": true}, {"name": "caption", "type": "text", "label": "Caption"}, {"name": "thumb_offset", "type": "number", "label": "Cover Frame"}, {"name": "location_id", "type": "text", "label": "Location ID"}, {"name": "share_to_feed", "type": "boolean", "label": "Share to Feed", "required": true}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": [], "notes": [{"id": 6, "text": "Post as <PERSON><PERSON>:\nThis posts the video as a reel to IG with the script as the caption.", "filter": false}, {"id": 9, "text": "Compress Video:\nThe video file is often too large for Instagram, so this compresses it.\nYou will need a Cloud Convert account. They have a free tier and you can perform up to 25 compressions a day for free.", "filter": false}, {"id": 2, "text": "Fetch Video:\nAfter the video renders this API call will get the video URL.", "filter": false}, {"id": 3, "text": "Sleep:\nThe video needs a moment to render before it can be fetched.", "filter": false}, {"id": 1, "text": "Create Video:\nThis API call is for D-ID to create a video using an image of your choice (the A.I. avatar) and the script <PERSON><PERSON> just wrote.\n\nYou must input your API key.\nYou must input a URL to an image (the image that will be animated to speak).", "filter": false}, {"id": 12, "text": "Transform to JSON:\nThe script has to be in JSON format when it gets pushed to the video creation api.\nThat is what this module does.", "filter": false}, {"id": 5, "text": "GPT Script Writer:\nThis is the A.I. module that has <PERSON><PERSON> write the video script for your A.I. avatar.", "filter": false}, {"id": 4, "text": "Topic Randomizer:\nThis randomly chooses a topic from a list. Be sure to replace the placeholder topics with your own!", "filter": false}]}, "zone": "us1.make.com"}}