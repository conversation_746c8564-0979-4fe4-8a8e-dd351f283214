{"id": "133", "name": "Analyze the sentiment of feedback and send a message on Mattermost", "nodes": [{"name": "Typeform Trigger", "type": "n8n-nodes-base.typeformTrigger", "position": [510, 260], "webhookId": "ad8a87ef-d293-4e48-8d36-838d69ebce0f", "parameters": {"formId": ""}, "credentials": {"typeformApi": "typeform"}, "typeVersion": 1}, {"name": "Google Cloud Natural Language", "type": "n8n-nodes-base.googleCloudNaturalLanguage", "position": [710, 260], "parameters": {"content": "={{$node[\"Typeform Trigger\"].json[\"What did you think about the event?\"]}}", "options": {}}, "credentials": {"googleCloudNaturalLanguageOAuth2Api": "cloud"}, "typeVersion": 1}, {"name": "IF", "type": "n8n-nodes-base.if", "position": [910, 260], "parameters": {"conditions": {"number": [{"value1": "={{$node[\"Google Cloud Natural Language\"].json[\"documentSentiment\"][\"score\"]}}"}]}}, "typeVersion": 1}, {"name": "Mattermost", "type": "n8n-nodes-base.mattermost", "position": [1110, 160], "parameters": {"message": "=You got a new feedback with a score of {{$node[\"Google Cloud Natural Language\"].json[\"documentSentiment\"][\"score\"]}}. Here is what it says:{{$node[\"Typeform Trigger\"].json[\"What did you think about the event?\"]}}", "channelId": "4h1bz64cyifwxnzojkzh8hxh4a", "attachments": [], "otherOptions": {}}, "credentials": {"mattermostApi": "mattermost"}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1110, 360], "parameters": {}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"IF": {"main": [[{"node": "Mattermost", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}, "Typeform Trigger": {"main": [[{"node": "Google Cloud Natural Language", "type": "main", "index": 0}]]}, "Google Cloud Natural Language": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}}}