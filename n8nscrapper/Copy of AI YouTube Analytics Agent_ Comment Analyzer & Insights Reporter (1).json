{"name": "AI YouTube Analytics Agent: Comment Analyzer & Insights Reporter", "nodes": [{"parameters": {"content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\n", "height": 740, "width": 480, "color": 4}, "id": "e0eae618-d330-4b74-b5a4-528a28ec91c6", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"content": "# 📊 YouTube Comment Analyzer Workflow\n\n**Purpose:** Automate YouTube comment collection, sentiment analysis, and email reporting\n\n**Schedule:** Triggers every minute for new/updated rows\n\n**Required Setup:**\n1. Google Sheet with video IDs\n2. YouTube API credentials\n3. OpenAI API key\n4. Gmail account\n\n## Workflow Steps:\n1. **Trigger** - Monitor Google Sheet\n2. **Filter** - Process only 'Pending' videos\n3. **Fetch** - Get video details & comments\n4. **Analyze** - AI sentiment analysis\n5. **Report** - Send email summary\n6. **Update** - Mark as processed", "height": 820, "width": 350, "color": 2}, "id": "e73f0186-b1a7-4ab9-a23e-94a38d51f8bb", "name": "Workflow Overview", "type": "n8n-nodes-base.stickyNote", "position": [1000, 440], "typeVersion": 1}, {"parameters": {"content": "## 🚀 Trigger Section\n\n**Google Sheets Trigger**\nMonitors the spreadsheet every minute for:\n- New video IDs added\n- Status changes to 'Pending'\n\n**Required Sheet Structure:**\n| ID | Video Title | YouTube Video ID | Status |\n\n⚠️ Set Status to 'Pending' to trigger analysis", "height": 340, "width": 380, "color": 3}, "id": "606f8cb5-54a5-4a90-be41-089d7ccae72f", "name": "Trigger Documentation", "type": "n8n-nodes-base.stickyNote", "position": [1380, 340], "typeVersion": 1}, {"parameters": {"content": "## 📹 YouTube Data Collection\n\n**This section fetches:**\n- Video metadata (title, channel)\n- Top 100 comments\n- Comment statistics\n\n**API Limits:**\n- Max 100 comments per request\n- Ordered by relevance\n- Top-level comments only", "height": 200, "width": 280}, "id": "f225b68e-272e-4414-82c2-2f79cfbcc381", "name": "YouTube API Section", "type": "n8n-nodes-base.stickyNote", "position": [2200, 460], "typeVersion": 1}, {"parameters": {"content": "## 🤖 AI Analysis Section\n\n**GPT-4o analyzes comments for:**\n1. Sentiment breakdown (positive/negative/neutral)\n2. Main themes & topics\n3. Common viewer questions\n4. Key feedback points\n5. Actionable insights\n\n**Output Format:** Structured JSON with underscore keys\n\n💡 Customize the prompt in AI Agent node for different analysis needs", "height": 250, "width": 320, "color": 5}, "id": "628d65ff-007c-4b7a-862e-64158fa80e7c", "name": "AI Analysis Documentation", "type": "n8n-nodes-base.stickyNote", "position": [2940, 440], "typeVersion": 1}, {"parameters": {"content": "## 📧 Email & Update Section\n\n**Email Report includes:**\n- Video statistics\n- Sentiment analysis\n- Key insights\n- Formatted HTML\n\n**Final Step:**\nUpdates Google Sheet status to 'Mail Sent' to prevent duplicate processing\n\n⚠️ Update recipient email in Gmail node", "height": 220, "width": 300, "color": 4}, "id": "85b58261-88e1-499a-a082-548713da293e", "name": "Email & Status Update", "type": "n8n-nodes-base.stickyNote", "position": [3460, 480], "typeVersion": 1}, {"parameters": {"content": "⚠️ **Data Preparation**\n\nThis node:\n- Extracts comment texts\n- Calculates statistics\n- Performs basic sentiment analysis\n- Prepares data for AI\n\nLimited to 50 comments for AI analysis to manage token usage", "height": 180, "width": 250, "color": 6}, "id": "e0ba6c37-8b04-406b-9158-aef610274135", "name": "Data Processing Note", "type": "n8n-nodes-base.stickyNote", "position": [2700, 920], "typeVersion": 1}, {"parameters": {}, "id": "d7edbd68-9213-426b-ae6b-71a13bb23690", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [1860, 720], "typeVersion": 1, "notes": "Limits processing to 1 item at a time to prevent API overload"}, {"parameters": {"promptType": "define", "text": "=Analyze these comments from the video \"{{ $json.videoTitle }}\":\n\nTotal Comments: {{ $json.totalComments }}\nAverage Likes: {{ $json.avgLikes }}\n\nComments to analyze:\n{{ $json.commentTexts }}", "options": {"systemMessage": "You are a YouTube comment analyst. Analyze the provided comments and return a structured analysis with:\n1. Overall sentiment breakdown (positive/negative/neutral percentages)\n2. Top 3-5 main themes or topics discussed\n3. Common questions from viewers\n4. Key feedback points and suggestions\n5. Actionable insights for content improvement\n\nFormat your response in clear sections with headers in json key value format and use underscore in the key name. Always use following keys for output json: \n1. overall_sentiment_breakdown\n2. main_themes\n3. common_questions\n4. key_feedback_points\n5. actionable_insights", "maxIterations": 100}}, "id": "42f0f581-57a0-438d-b1a1-29a89e984554", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2960, 720], "typeVersion": 1.9, "notes": "Uses OpenAI GPT-4o to analyze comments and generate insights. Customize the prompt for different analysis needs."}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {"responseFormat": "json_object"}}, "id": "35abbd58-e69a-4d86-963e-0a758aaff7b2", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3060, 940], "typeVersion": 1.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "5f1eae10-5270-4e99-8627-597e3451c82c", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.Status }}", "rightValue": "Pending"}]}, "options": {}}, "id": "747fb13b-ee39-4dd7-8338-8db07e3b568e", "name": "If", "type": "n8n-nodes-base.if", "position": [1640, 720], "typeVersion": 2.2, "notes": "Filters rows to process only videos with 'Pending' status"}, {"parameters": {"assignments": {"assignments": [{"id": "219795ef-daa4-4444-9865-c5d3856be63b", "name": "videoId", "type": "string", "value": "={{ $json['Youtube video id'] }}"}, {"id": "cd4f519d-4c84-496c-8974-29ef69c890fc", "name": "maxComments ", "type": "number", "value": 100}]}, "options": {}}, "id": "6895d6b4-4ae4-4092-9302-33b1c69167d6", "name": "Set Video Details", "type": "n8n-nodes-base.set", "position": [2080, 720], "typeVersion": 3.4, "notes": "Prepares video ID and sets max comments limit (100)"}, {"parameters": {"resource": "video", "operation": "get", "videoId": "={{ $json.videoId }}", "options": {}}, "id": "df200d75-d932-4bb4-9e57-16a940963db3", "name": "Get Youtube Video Details", "type": "n8n-nodes-base.youTube", "position": [2300, 720], "typeVersion": 1, "notes": "Fetches video metadata including title, channel name, and other details"}, {"parameters": {"url": "https://www.googleapis.com/youtube/v3/commentThreads", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "videoId", "value": "={{ $('Set Video Details').item.json.videoId }}"}, {"name": "maxResults", "value": "100"}, {"name": "order", "value": "relevance"}]}, "options": {}}, "id": "5346229d-9386-497b-99c7-192e75896bf2", "name": "Get Youtube Video Comments", "type": "n8n-nodes-base.httpRequest", "position": [2520, 720], "typeVersion": 4.2, "notes": "Retrieves top 100 comments ordered by relevance using YouTube API"}, {"parameters": {"jsCode": "// Get comments from HTTP Request node\nconst comments = $input.first().json.items\n//const comments = response.items || [];\n\n// Get video title from the YouTube node (step 5)\n//const videoData = ;\nconst videoTitle = $('Get Youtube Video Details').first().json.snippet.title;\n\n// Extract comment data\nconst processedComments = comments.map(item => {\n  const comment = item.snippet.topLevelComment.snippet;\n  return {\n    text: comment.textDisplay,\n    author: comment.authorDisplayName,\n    likes: comment.likeCount || 0,\n    publishedAt: comment.publishedAt,\n    replyCount: item.snippet.totalReplyCount || 0\n  };\n});\n\n// Calculate statistics\nconst totalComments = processedComments.length;\nconst totalLikes = processedComments.reduce((sum, c) => sum + c.likes, 0);\nconst avgLikes = totalComments > 0 ? (totalLikes / totalComments).toFixed(2) : 0;\nconst totalReplies = processedComments.reduce((sum, c) => sum + c.replyCount, 0);\n\n// Get top comments by likes\nconst topComments = processedComments\n  .sort((a, b) => b.likes - a.likes)\n  .slice(0, 5);\n\n// Prepare comment texts for AI analysis\nconst commentTexts = processedComments\n  .slice(0, 50) // Limit to 50 comments for AI analysis\n  .map(c => c.text)\n  .join('\\n---\\n');\n\n// Basic sentiment analysis (count positive/negative keywords)\nconst positiveWords = ['love', 'great', 'awesome', 'amazing', 'excellent', 'good', 'fantastic', 'helpful', 'thanks'];\nconst negativeWords = ['hate', 'terrible', 'awful', 'bad', 'worst', 'horrible', 'useless', 'waste'];\n\nlet positiveCount = 0;\nlet negativeCount = 0;\n\nprocessedComments.forEach(comment => {\n  const lowerText = comment.text.toLowerCase();\n  positiveWords.forEach(word => {\n    if (lowerText.includes(word)) positiveCount++;\n  });\n  negativeWords.forEach(word => {\n    if (lowerText.includes(word)) negativeCount++;\n  });\n});\n\nreturn [{\n  json: {\n    videoTitle,\n    totalComments,\n    avgLikes,\n    totalReplies,\n    topComments,\n    commentTexts,\n    processedComments,\n    sentimentCounts: {\n      positive: positiveCount,\n      negative: negativeCount,\n      neutral: totalComments - positiveCount - negativeCount\n    }\n  }\n}];\n\n"}, "id": "ffee99ec-ad6d-4ac9-b0ad-e466a18ad7de", "name": "Prepare Comments Data", "type": "n8n-nodes-base.code", "position": [2740, 720], "typeVersion": 2, "notes": "Processes raw comments: extracts text, calculates stats, performs basic sentiment analysis, limits to 50 comments for AI"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "const output = JSON.parse($json.output)\n //return output; \n//return output.overall_sentiment_breakdown.Positive;\n\nlet common_question = \"\";\n  \noutput.common_questions.map(rec => {\n  return common_question += \"<li>\"+rec.trim().replace(new RegExp(\".*,$\"), \"\")+\"</li>\"\n})\n\nlet key_feedback_points_and_suggestions = \"\";\n\noutput.key_feedback_points.map(rec => {\n  return key_feedback_points_and_suggestions += \"<li>\"+rec.trim().replace(new RegExp(\".*,$\"), \"\")+\"</li>\"\n})\n\n\nlet actionable_insights_for_content_improvement = \"\"\n\noutput.actionable_insights.map(rec => {\n  return actionable_insights_for_content_improvement += \"<li>\"+rec.trim().replace(new RegExp(\".*,$\"), \"\")+\"</li>\"\n})\n\n\n// return {common_question};\n\n\n     \n\n//return output; \n// Generate the email HTML\nconst emailHTML = `\n<!DOCTYPE html>\n<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n    .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }\n    .section { margin: 20px 0; }\n    .stats { background: #e9ecef; padding: 15px; border-radius: 5px; }\n    .analysis { background: #f8f9fa; padding: 20px; border-radius: 5px; }\n    h2 { color: #2c3e50; }\n    h3 { color: #34495e; }\n    h4 { color: #7f8c8d; }\n    .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 0.9em; }\n  </style>\n</head>\n<body>\n  <div class=\"header\">\n    <h2>YouTube Comments Analysis Report</h2>\n    <h3>Video: ${$(\"Prepare Comments Data\").item.json.videoTitle}</h3>\n  </div>\n\n  <div class=\"section stats\">\n    <h3> Quick Statistics</h3>\n    <ul>\n      <li><strong>Total Comments Analyzed:</strong> ${$('Prepare Comments Data').item.json.totalComments}</li>\n      <li><strong>Average Likes per Comment:</strong> ${$('Prepare Comments Data').item.json.avgLikes}</li>\n      <li><strong>Total Replies:</strong> ${$('Prepare Comments Data').item.json.totalReplies}</li>\n      <li><strong>Sentiment Summary:</strong> \n        Positive: ${output.overall_sentiment_breakdown.positive}, \n        Negative: ${output.overall_sentiment_breakdown.negative}, \n        Neutral: ${output.overall_sentiment_breakdown.neutral}\n      </li>\n    </ul>\n  </div>\n\n  <div class=\"section stats\">\n    <h3>Common Questions</h3>\n    <ul>\n      ${common_question}\n    </ul>\n  </div>\n\n    <div class=\"section stats\">\n    <h3>Key feedback points and suggestions</h3>\n    <ul>\n      ${key_feedback_points_and_suggestions}\n    </ul>\n  </div>\n\n\n  <div class=\"section stats\">\n    <h3>Actionable insights for content improvement</h3>\n    <ul>\n      ${actionable_insights_for_content_improvement}\n    </ul>\n  </div>\n\n\n\n\n  <div class=\"footer\">\n    <p><em>Report generated on ${new Date().toLocaleString()}</em></p>\n    \n  </div>\n</body>\n</html>`;\n\n\n// Return the formatted data\nreturn  {\n  json: {\n    emailHTML,\n    subject: `YouTube Analysis: ${$('Prepare Comments Data').item.json.videoTitle} - ${$('Prepare Comments Data').item.json.totalComments} comments analyzed`,\n    videoTitle: $('Prepare Comments Data').item.json.videoTitle\n  }\n};"}, "id": "7eab55e1-e9e2-4750-a07d-f02941c7a8c6", "name": "Prepare HTML for Email", "type": "n8n-nodes-base.code", "position": [3340, 720], "typeVersion": 2, "notes": "Converts AI analysis into formatted HTML email with statistics, insights, and professional styling"}, {"parameters": {"sendTo": "SENDER_EMAIL_ADDRESS", "subject": "={{ $json.subject }}", "message": "={{ $json.emailHTML }}", "options": {}}, "id": "f789c4e0-813b-4f6d-9584-86a274fba969", "name": "Gmail Account Configuration", "type": "n8n-nodes-base.gmail", "position": [3560, 720], "webhookId": "90b8f9ad-4226-42a0-971b-7fa9f5f190bd", "typeVersion": 2.1, "notes": "Sends formatted analysis report via Gmail. Update SENDER_EMAIL_ADDRESS with actual recipient"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "mode": "list", "value": "1qe6Ovp_pfmwZA2k5c7LmmZx-OKqv3hnu8Dg80Pw4KoY", "cachedResultUrl": "GOOGLE_DRIVE_URL", "cachedResultName": "Youtube_Video"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "GOOGLE_DRIVE_URL", "cachedResultName": "Youtube_Video "}, "columns": {"value": {"Id": "={{ $('Pick Video Ids from Google sheet').item.json.Id }}", "Status": "Mail Sent"}, "schema": [{"id": "Id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Youtube video id", "type": "string", "display": true, "required": false, "displayName": "Youtube video id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Status", "type": "string", "display": true, "required": false, "displayName": "Status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "309d2e7d-2da0-439b-b2ac-87196d9f5733", "name": "Update Status on Google Sheet", "type": "n8n-nodes-base.googleSheets", "position": [3780, 720], "typeVersion": 4.5, "notes": "Updates video status to 'Mail Sent' to prevent duplicate processing"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "mode": "list", "value": "1qe6Ovp_pfmwZA2k5c7LmmZx-OKqv3hnu8Dg80Pw4KoY", "cachedResultUrl": "GOOGLE_DRIVE_URL", "cachedResultName": "Youtube_Video"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "GOOGLE_DRIVE_URL", "cachedResultName": "Youtube_Video "}, "options": {}}, "id": "b62a4e3a-c843-4ed2-9ee4-cbfb1a218259", "name": "Pick Video Ids from Google sheet", "type": "n8n-nodes-base.googleSheetsTrigger", "position": [1420, 720], "typeVersion": 1, "notes": "Triggers on new YouTube videos added to spreadsheet. Polls every minute for changes"}], "pinData": {}, "connections": {"If": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Set Video Details", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Prepare HTML for Email", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Set Video Details": {"main": [[{"node": "Get Youtube Video Details", "type": "main", "index": 0}]]}, "Prepare Comments Data": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Prepare HTML for Email": {"main": [[{"node": "Gmail Account Configuration", "type": "main", "index": 0}]]}, "Get Youtube Video Details": {"main": [[{"node": "Get Youtube Video Comments", "type": "main", "index": 0}]]}, "Get Youtube Video Comments": {"main": [[{"node": "Prepare Comments Data", "type": "main", "index": 0}]]}, "Gmail Account Configuration": {"main": [[{"node": "Update Status on Google Sheet", "type": "main", "index": 0}]]}, "Pick Video Ids from Google sheet": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "4459", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}