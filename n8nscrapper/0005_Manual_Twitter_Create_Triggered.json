{"id": 1003, "name": "New tweets", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 260], "parameters": {}, "typeVersion": 1}, {"name": "Twitter", "type": "n8n-nodes-base.twitter", "position": [520, 160], "parameters": {"limit": 100, "operation": "search", "searchText": "verstap<PERSON>", "additionalFields": {"resultType": "mixed"}}, "typeVersion": 1}, {"name": "Set_AT_list", "type": "n8n-nodes-base.set", "position": [780, 360], "parameters": {"values": {"number": [{"name": "<PERSON>s", "value": "={{$node[\"Twitter\"].json[\"favorite_count\"] ? $node[\"Twitter\"].json[\"favorite_count\"] : 0 }}"}], "string": [{"name": "Tweet", "value": "={{$node[\"get airtable list\"].json[\"fields\"][\"Tweet\"]}}"}, {"name": "Tweet_id", "value": "={{$node[\"get airtable list\"].json[\"fields\"][\"Tweet_id\"]}}"}, {"name": "Tweet URL", "value": "={{$node[\"get airtable list\"].json[\"fields\"][\"Tweet URL\"]}}"}, {"name": "Author", "value": "={{$node[\"get airtable list\"].json[\"fields\"][\"Author\"]}}"}, {"name": "Time", "value": "={{$node[\"get airtable list\"].json[\"fields\"][\"Time\"]}}"}]}, "options": {"dotNotation": false}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "get airtable list", "type": "n8n-nodes-base.airtable", "position": [520, 360], "parameters": {"table": "tbl6rexxFBodzKVoC", "operation": "list", "application": "app36P08S3Jzki6qJ", "additionalOptions": {}}, "credentials": {"airtableApi": {"id": "2", "name": "airtable_api"}}, "typeVersion": 1}, {"name": "set twitter data", "type": "n8n-nodes-base.set", "position": [780, 160], "parameters": {"values": {"number": [{"name": "<PERSON>s", "value": "={{$node[\"Twitter\"].json[\"favorite_count\"]}}"}], "string": [{"name": "Tweet", "value": "={{$node[\"Twitter\"].json[\"text\"]}}"}, {"name": "Tweet_id", "value": "={{$node[\"Twitter\"].json[\"id\"]}}"}, {"name": "Tweet URL", "value": "=https://twitter.com/{{$node[\"Twitter\"].json[\"user\"][\"screen_name\"]}}/status/{{$node[\"Twitter\"].json[\"id_str\"]}}"}, {"name": "Author", "value": "={{$node[\"Twitter\"].json[\"in_reply_to_screen_name\"]}}"}, {"name": "Time", "value": "={{$node[\"Twitter\"].json[\"created_at\"]}}"}]}, "options": {"dotNotation": false}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Leave only new tweets", "type": "n8n-nodes-base.merge", "position": [1060, 260], "parameters": {"mode": "removeKeyMatches", "propertyName1": "Tweet_id", "propertyName2": "Tweet_id"}, "typeVersion": 1}, {"name": "Append new tweets to airtable", "type": "n8n-nodes-base.airtable", "position": [1300, 260], "parameters": {"table": "tbl6rexxFBodzKVoC", "options": {}, "operation": "append", "application": "app36P08S3Jzki6qJ", "addAllFields": "={{true}}"}, "credentials": {"airtableApi": {"id": "2", "name": "airtable_api"}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Twitter": {"main": [[{"node": "set twitter data", "type": "main", "index": 0}]]}, "Set_AT_list": {"main": [[{"node": "Leave only new tweets", "type": "main", "index": 1}]]}, "set twitter data": {"main": [[{"node": "Leave only new tweets", "type": "main", "index": 0}]]}, "get airtable list": {"main": [[{"node": "Set_AT_list", "type": "main", "index": 0}]]}, "Leave only new tweets": {"main": [[{"node": "Append new tweets to airtable", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Twitter", "type": "main", "index": 0}, {"node": "get airtable list", "type": "main", "index": 0}]]}}}