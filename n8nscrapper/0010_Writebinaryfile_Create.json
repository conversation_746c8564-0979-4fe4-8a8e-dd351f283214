{"nodes": [{"name": "Write Binary File", "type": "n8n-nodes-base.writeBinaryFile", "position": [800, 350], "parameters": {"fileName": "test.json"}, "typeVersion": 1}, {"name": "Make Binary", "type": "n8n-nodes-base.function", "position": [600, 350], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"name": "Create Example Data", "type": "n8n-nodes-base.function", "position": [390, 350], "parameters": {"functionCode": "items[0].json = {\n  \"text\": \"asdf\",\n  \"number\": 1\n};\nreturn items;"}, "typeVersion": 1}], "connections": {"Make Binary": {"main": [[{"node": "Write Binary File", "type": "main", "index": 0}]]}, "Create Example Data": {"main": [[{"node": "Make Binary", "type": "main", "index": 0}]]}}}