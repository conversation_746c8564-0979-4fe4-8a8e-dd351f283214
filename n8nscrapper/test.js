#!/usr/bin/env node

/**
 * Simple test script to verify the scraper components
 */

const fs = require('fs-extra');
const path = require('path');

async function runTests() {
    console.log('🧪 Running N8N Workflow Scraper Tests...\n');
    
    const tests = [
        testDirectoryStructure,
        testConfigurationLoading,
        testLoggerInitialization,
        testRateLimiter,
        testWorkflowParser,
        testStorageManager
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            await test();
            console.log(`✅ ${test.name} - PASSED`);
            passed++;
        } catch (error) {
            console.log(`❌ ${test.name} - FAILED: ${error.message}`);
            failed++;
        }
    }
    
    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
        console.log('🎉 All tests passed! The scraper is ready to use.');
    } else {
        console.log('⚠️  Some tests failed. Please check the installation.');
        process.exit(1);
    }
}

async function testDirectoryStructure() {
    const requiredDirs = ['src', 'src/utils', 'src/scrapers', 'src/parsers', 'src/config'];
    const requiredFiles = [
        'package.json',
        'src/index.js',
        'src/cli.js',
        'src/scraper.js',
        'src/config/config.js',
        'src/utils/logger.js',
        'src/utils/rate-limiter.js',
        'src/utils/cloudflare-bypass.js',
        'src/utils/ethical-scraper.js',
        'src/utils/storage-manager.js',
        'src/utils/error-handler.js',
        'src/scrapers/workflow-scraper.js',
        'src/parsers/workflow-parser.js'
    ];
    
    for (const dir of requiredDirs) {
        if (!(await fs.pathExists(dir))) {
            throw new Error(`Missing directory: ${dir}`);
        }
    }
    
    for (const file of requiredFiles) {
        if (!(await fs.pathExists(file))) {
            throw new Error(`Missing file: ${file}`);
        }
    }
}

async function testConfigurationLoading() {
    const { config, validateConfig } = require('./src/config/config');
    
    if (!config) {
        throw new Error('Configuration not loaded');
    }
    
    if (!config.target || !config.target.url) {
        throw new Error('Target URL not configured');
    }
    
    // Test validation (should not throw)
    validateConfig();
}

async function testLoggerInitialization() {
    const logger = require('./src/utils/logger');
    
    if (!logger) {
        throw new Error('Logger not initialized');
    }
    
    // Test logging methods
    logger.info('Test log message');
    logger.scraper('Test scraper log');
    logger.parser('Test parser log');
}

async function testRateLimiter() {
    const RateLimiter = require('./src/utils/rate-limiter');
    
    const rateLimiter = new RateLimiter({
        requestsPerMinute: 60,
        burstLimit: 5,
        minDelay: 100
    });
    
    const stats = rateLimiter.getStats();
    if (!stats || typeof stats.requestsPerMinuteLimit !== 'number') {
        throw new Error('Rate limiter stats not working');
    }
}

async function testWorkflowParser() {
    const WorkflowParser = require('./src/parsers/workflow-parser');
    
    const parser = new WorkflowParser({
        outputDir: './test-output'
    });
    
    // Test with a sample workflow
    const sampleWorkflow = {
        name: 'Test Workflow',
        nodes: [
            { type: 'webhook', name: 'Webhook' },
            { type: 'openai', name: 'OpenAI' }
        ],
        connections: {}
    };
    
    const analysis = parser.analyzeWorkflow(sampleWorkflow);
    
    if (!analysis || !analysis.hasOwnProperty('isValid')) {
        throw new Error('Workflow analysis not working');
    }
    
    if (!analysis.hasAI) {
        throw new Error('AI detection not working');
    }
}

async function testStorageManager() {
    const StorageManager = require('./src/utils/storage-manager');
    
    const storage = new StorageManager({
        outputDir: './test-output'
    });
    
    const stats = storage.getStats();
    if (!stats || typeof stats.totalStored !== 'number') {
        throw new Error('Storage manager stats not working');
    }
    
    // Clean up test directory
    await fs.remove('./test-output');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(error => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = runTests;
