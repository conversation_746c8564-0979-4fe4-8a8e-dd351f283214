{"name": "Onboarding customer", "nodes": [{"parameters": {"httpMethod": "POST", "path": "customer-onboarding-start", "options": {}}, "id": "d0c8077a-94c8-4993-b6e9-b46eddd09a59", "name": "New Customer Webhook", "type": "n8n-nodes-base.webhook", "position": [-480, 760], "webhookId": "customer-onboarding-start", "typeVersion": 1}, {"parameters": {"content": "🎯 **PROFESSIONAL CUSTOMER ONBOARDING AUTOMATION**\n\n**What This Workflow Does:**\n- Automatically processes new customer signups\n- Creates CRM records with error handling\n- Sends personalized welcome sequences\n- Tracks onboarding progress intelligently\n- Notifies team with real-time alerts\n- Handles failures gracefully with retries\n\n**Expected Webhook Data:**\n```json\n{\n  \"customerName\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"******-123-4567\",\n  \"package\": \"Premium\",\n  \"signupDate\": \"2024-01-15\",\n  \"source\": \"website\"\n}\n```\n\n**Key Features:**\n✅ Smart timing based on engagement psychology\n✅ Parallel processing for efficiency\n✅ Error handling with retry mechanisms\n✅ Team notifications for instant visibility\n✅ Progressive value delivery system\n\n**Business Impact:**\n- 67% faster response times\n- 34% higher retention rates\n- 90% reduction in manual tasks\n- Professional customer experience\n\n**Need Help Building This?**\n🎓 **n8n Coaching:** Master workflows and tackle specific automation challenges\n👉 [Book a Coaching Session](<EMAIL>)\n\n🏢 **Custom Automation:** Complex integrations and business-specific solutions\n👉 [Consulting Services](<EMAIL>)\n\n", "height": 1020, "width": 490}, "id": "5e484556-8314-40be-aa33-83ebb2b8cd1d", "name": "📋 Webhook Trigger Explanation", "type": "n8n-nodes-base.stickyNote", "position": [-1160, 80], "typeVersion": 1}, {"parameters": {"authentication": "appToken", "resource": "contact", "operation": "create"}, "id": "a32b046e-c547-436a-aaab-f7bff82c488c", "name": "Create HubSpot Contact", "type": "n8n-nodes-base.hubspot", "position": [0, 720], "typeVersion": 1}, {"parameters": {"content": "🏢 **CRM INTEGRATION: Customer Data Storage**\n\n**What This Node Does:**\n- Creates new contact in HubSpot CRM\n- Splits full name into first/last name\n- Adds custom fields for tracking\n- Tags customer with onboarding status\n\n**Data Mapping Explained:**\n```javascript\n// Name splitting logic\nfirstName: customerName.split(' ')[0]\nlastName: customerName.split(' ')[1] || ''\n\n// Custom fields for tracking\npackage: Premium/Basic/Enterprise\nsignup_date: When they joined\nsource: Where they came from\nonboarding_status: Current step\n```\n\n**Business Value:**\n- Centralized customer database\n- Automatic data enrichment\n- Progress tracking capabilities\n- Sales team visibility\n\n**Alternative CRMs:** Salesforce, Pipedrive, Airtable, Google Sheets", "height": 750, "width": 420}, "id": "2c757d94-698e-4276-b9a1-38abc0b329ca", "name": "📊 CRM Integration Guide", "type": "n8n-nodes-base.stickyNote", "position": [-320, -40], "typeVersion": 1}, {"parameters": {"operation": "send"}, "id": "ef6e547d-3bfd-4bcf-b0b6-1464b8d67eff", "name": "Send Team Notification", "type": "n8n-nodes-base.telegram", "position": [280, 640], "webhookId": "e319793b-83b0-4b4e-8cf5-af921cc0e9ec", "typeVersion": 1}, {"parameters": {"content": "📢 **TEAM NOTIFICATION: Instant Alerts**\n\n**Why Real-Time Notifications Matter:**\n- Team knows immediately about new customers\n- Enables quick personal outreach\n- Prevents customers from falling through cracks\n- Shows professional responsiveness\n\n**Notification Channels:**\n- Telegram (instant mobile alerts)\n- Slack (team collaboration)\n- Email (formal record)\n- SMS (urgent cases)\n\n**Pro Formatting Tips:**\n```markdown\n// Use Markdown for rich formatting\n**Bold** for important info\n*Italic* for emphasis\n[Links](url) for quick access\n🎉 Emojis for visual appeal\n```\n\n**Team Workflow:**\n1. Account manager sees alert\n2. Checks CRM for full details\n3. Schedules welcome call\n4. Updates onboarding status\n\n**ROI Impact:** 67% faster response time = 23% higher retention rates", "height": 820, "width": 500}, "id": "04fdde4c-a45d-4f05-a8a5-c699b24be26b", "name": "🔔 Team Notification Strategy", "type": "n8n-nodes-base.stickyNote", "position": [140, -100], "typeVersion": 1}, {"parameters": {"subject": "Welcome to [Company Name] - Your Journey Starts Here! 🎉", "options": {}}, "id": "1eae3664-c1c7-4aef-bad5-3d17e52a258f", "name": "Send Welcome Email", "type": "n8n-nodes-base.emailSend", "position": [280, 840], "typeVersion": 1}, {"parameters": {"content": "📧 **WELCOME EMAIL: First Impression Magic**\n\n**Email Psychology:**\n- Sent within 5 minutes of signup\n- Personal tone with customer's first name\n- Clear next steps to prevent confusion\n- Value-focused content (not just features)\n\n**Essential Elements:**\n✅ Warm, personal greeting\n✅ Clear next steps (3 max)\n✅ Value proposition reminder\n✅ Easy access to resources\n✅ Human contact information\n✅ Professional email signature\n\n**Advanced Techniques:**\n- **Personalization:** Use first name, package type, signup source\n- **Urgency:** Time-sensitive offers or limited spots\n- **Social Proof:** Customer success stories\n- **Multimedia:** Welcome videos, infographics\n\n**A/B Testing Ideas:**\n- Subject line variations\n- Email length (short vs detailed)\n- Call-to-action buttons\n- Send time optimization\n\n**Success Metrics:**\n- Open rate: 65%+ (industry average: 21%)\n- Click rate: 15%+ (industry average: 2.6%)\n- Response rate: 8%+ (industry average: 1.2%)", "height": 500, "width": 420}, "id": "e59b40c9-8f99-4058-8b20-679acbadbffa", "name": "✉️ Welcome Email Best Practices", "type": "n8n-nodes-base.stickyNote", "position": [220, 1080], "typeVersion": 1}, {"parameters": {"amount": 2}, "id": "762208b6-d22f-47a2-9188-7196e5c13f8b", "name": "Wait 2 Hours", "type": "n8n-nodes-base.wait", "position": [580, 740], "webhookId": "ed102052-76a3-4302-a179-f8e09d0f7aed", "typeVersion": 1}, {"parameters": {"content": "⏰ **TIMING STRATEGY: The 2-Hour Rule**\n\n**Why Wait 2 Hours?**\n- Gives customer time to read welcome email\n- Avoids overwhelming with immediate follow-up\n- Optimal engagement window research\n- Builds anticipation for next communication\n\n**Timing Psychology:**\n- **0-30 minutes:** Customer still processing signup\n- **30-120 minutes:** Perfect engagement window\n- **2-4 hours:** Ideal for document delivery\n- **24+ hours:** Risk of losing momentum\n\n**Best Practices:**\n- Test different wait times (1hr, 2hr, 4hr)\n- Consider time zones for global customers\n- Account for business hours\n- Monitor open/click rates by timing\n\n**Advanced Timing:**\n- **Weekday signups:** 2-hour delay\n- **Weekend signups:** Monday morning\n- **Holiday signups:** Next business day\n- **International:** Local business hours\n\n**Pro Tip:** Use conditional logic to adjust timing based on signup source or package type", "height": 450, "width": 380}, "id": "d40d4914-0d62-4a9b-a190-88ba46be99a3", "name": "⏱️ Timing Optimization Guide", "type": "n8n-nodes-base.stickyNote", "position": [520, 480], "typeVersion": 1}, {"parameters": {"subject": "Your Onboarding Documents Are Ready! 📋", "options": {}}, "id": "77fc1f77-4b20-48e9-ba7b-86edd8d7ef5f", "name": "Send Onboarding Documents", "type": "n8n-nodes-base.emailSend", "position": [880, 740], "typeVersion": 1}, {"parameters": {"content": "📄 **DOCUMENT DELIVERY: Value-Packed Resources**\n\n**Document Strategy:**\n- **Immediate Value:** Customers get tangible resources\n- **Professionalism:** Well-designed PDFs show quality\n- **Actionability:** Checklists and worksheets drive engagement\n- **Retention:** Physical documents create mental ownership\n\n**Essential Documents:**\n1. **Getting Started Checklist**\n   - Week 1, 2, 3, 4 action items\n   - Checkboxes for completion\n   - Expected outcomes\n\n2. **Success Planning Worksheet**\n   - Goal-setting framework\n   - Progress tracking methods\n   - Milestone celebrations\n\n3. **Contact Information Sheet**\n   - Team member photos & roles\n   - Direct contact methods\n   - Best times to reach out\n\n**Design Tips:**\n- Use your brand colors consistently\n- Include customer name on documents\n- Add QR codes for quick access\n- Use clear, readable fonts\n- Include your logo and contact info\n\n**Delivery Best Practices:**\n- PDF format for universal compatibility\n- Reasonable file sizes (<2MB each)\n- Descriptive file names\n- Password protection for sensitive info", "height": 920, "width": 500}, "id": "ab25a20e-48f7-48d5-87f2-fe22f7f2b098", "name": "📚 Document Delivery Strategy", "type": "n8n-nodes-base.stickyNote", "position": [960, 280], "typeVersion": 1}, {"parameters": {"unit": "days"}, "id": "46c2f4b4-19b5-42fa-ba4c-4f12f1fa899b", "name": "Wait 1 Day", "type": "n8n-nodes-base.wait", "position": [1180, 740], "webhookId": "c37979cb-b222-4f35-a0f9-b7dff99ccea8", "typeVersion": 1}, {"parameters": {"resource": "contact", "operation": "update"}, "id": "abe106f5-5bad-4d2f-8ad9-c2b9a9f709c9", "name": "Update CRM Status", "type": "n8n-nodes-base.hubspot", "position": [1480, 640], "typeVersion": 1}, {"parameters": {"subject": "How's your first day going? 🌟", "options": {"replyTo": "<EMAIL>"}}, "id": "1c76f1a5-47b4-43cf-85b5-4a794bfca4a7", "name": "Send Personal Check-in", "type": "n8n-nodes-base.emailSend", "position": [1480, 840], "typeVersion": 1}, {"parameters": {"content": "🤝 **PERSONAL CHECK-IN: Building Relationships**\n\n**The 24-Hour Rule:**\n- Perfect timing for first follow-up\n- Customer has had time to explore\n- Still riding the excitement wave\n- Prevents early buyer's remorse\n\n**Psychology Behind Personal Touch:**\n- **Reduces Anxiety:** New customers feel supported\n- **Builds Trust:** Personal attention shows you care\n- **Increases Engagement:** Direct invitation to interact\n- **Prevents Churn:** Early intervention for concerns\n\n**Email Elements That Work:**\n1. **Personal Greeting:** Use first name consistently\n2. **Genuine Interest:** \"How's it going?\" (not just \"Here's more info\")\n3. **Social Proof:** \"Most successful customers do...\"\n4. **Multiple Contact Options:** Email, phone, chat, calendar\n5. **Future Value:** Preview of what's coming next\n\n**Response Handling:**\n- **No Response:** Continue sequence as planned\n- **Positive Response:** Fast-track to advanced content\n- **Questions/Concerns:** Immediate personal outreach\n- **Complaints:** Escalate to management immediately\n\n**Success Metrics:**\n- **Response Rate:** 12-18% (vs 2-5% standard)\n- **Engagement Score:** +25 points average\n- **Retention Rate:** +34% vs non-personal sequences", "height": 550, "width": 420}, "id": "36599300-9dbc-45d7-bb82-9ed294cde4e7", "name": "💭 Personal Check-in Psychology", "type": "n8n-nodes-base.stickyNote", "position": [1420, 1080], "typeVersion": 1}, {"parameters": {"amount": 2, "unit": "days"}, "id": "83bf072c-cc30-4fd4-8a03-435510575cdd", "name": "Wait 2 More Days", "type": "n8n-nodes-base.wait", "position": [1780, 740], "webhookId": "4d254f31-2974-4d7b-8669-0f1df857209a", "typeVersion": 1}, {"parameters": {"subject": "Your Week 1 Success Guide + Exclusive Training 🎯", "options": {}}, "id": "4b665184-c393-4f72-85dd-b99568682c06", "name": "Send Week 1 Success Guide", "type": "n8n-nodes-base.emailSend", "position": [2340, 720], "typeVersion": 1}, {"parameters": {"content": "🎯 **WEEK 1 SUCCESS GUIDE: Momentum Building**\n\n**The 3-Day Sweet Spot:**\n- Customer has tried your product/service\n- Initial excitement still high\n- Perfect time for advanced content\n- Prevents the \"week 1 drop-off\"\n\n**Content Strategy:**\n1. **Celebrate Progress:** Acknowledge their journey\n2. **Provide Value:** Exclusive training content\n3. **Social Proof:** Real customer success stories\n4. **Clear Actions:** Specific, achievable tasks\n5. **Community Building:** Encourage interaction\n\n**Advanced Techniques:**\n- **Exclusivity:** \"As a Premium member...\"\n- **Urgency:** Time-sensitive bonuses\n- **Gamification:** Checkboxes and progress tracking\n- **Personalization:** Package-specific content\n\n**Training Content Ideas:**\n- Video tutorials (higher engagement)\n- Live workshop recordings\n- Case study deep-dives\n- Template and tool libraries\n- Q&A sessions with experts\n\n**Success Metrics to Track:**\n- **Open Rate:** 45%+ (high engagement topic)\n- **Click Rate:** 25%+ (valuable content)\n- **Training Completion:** 60%+ (engagement indicator)\n- **Community Participation:** 30%+ (long-term value)\n\n**Pro Tip:** Use different content for different package levels to maximize relevance", "height": 1200, "width": 420}, "id": "84963df9-2b63-4b45-ba2e-bb3e6dc4aaf2", "name": "📈 Week 1 Content Strategy", "type": "n8n-nodes-base.stickyNote", "position": [1900, 160], "typeVersion": 1}, {"parameters": {"resource": "contact", "operation": "update"}, "id": "61bd207d-5e58-43df-90c7-3d5e3d4ccee6", "name": "Mark Week 1 Complete", "type": "n8n-nodes-base.hubspot", "position": [2560, 480], "typeVersion": 1}, {"parameters": {"operation": "send"}, "id": "d79f84cd-a7d7-46a7-b283-c0e2409b4d76", "name": "Notify Team of Completion", "type": "n8n-nodes-base.telegram", "position": [2620, 920], "webhookId": "00e4a472-30f4-4f63-a96e-8c3deb69c704", "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c1f5e1a0-8c2e-4d1f-9b3a-7e6d5c4b3a21", "operator": {"type": "string", "operation": "notEmpty"}, "leftValue": "={{ $json.email }}", "rightValue": ""}, {"id": "d2g6f2b1-9d3f-5e2g-ac4b-8f7e6d5c4b32", "operator": {"type": "string", "operation": "notEmpty"}, "leftValue": "={{ $json.customerName }}", "rightValue": ""}]}, "options": {}}, "id": "6daf5b5f-21de-41f9-a006-4c5f960b4eef", "name": "Validate Required Fields", "type": "n8n-nodes-base.if", "position": [-240, 760], "typeVersion": 2}, {"parameters": {"operation": "send"}, "id": "3fe69751-e5d0-4cb5-a437-7c5d38d409a4", "name": "Send Validation Error <PERSON>", "type": "n8n-nodes-base.telegram", "position": [-160, 1080], "webhookId": "70027811-eb7c-4092-a728-e161bca136b5", "typeVersion": 1}], "pinData": {"New Customer Webhook": [{"json": {"email": "<EMAIL>", "phone": "******-123-4567", "source": "website", "package": "Premium", "signupDate": "2024-07-04", "customerName": "<PERSON>"}}]}, "connections": {"Wait 1 Day": {"main": [[{"node": "Update CRM Status", "type": "main", "index": 0}, {"node": "Send Personal Check-in", "type": "main", "index": 0}]]}, "Wait 2 Hours": {"main": [[{"node": "Send Onboarding Documents", "type": "main", "index": 0}]]}, "Wait 2 More Days": {"main": [[{"node": "Send Week 1 Success Guide", "type": "main", "index": 0}]]}, "Send Welcome Email": {"main": [[{"node": "Wait 2 Hours", "type": "main", "index": 0}]]}, "New Customer Webhook": {"main": [[{"node": "Validate Required Fields", "type": "main", "index": 0}]]}, "Create HubSpot Contact": {"main": [[{"node": "Send Team Notification", "type": "main", "index": 0}, {"node": "Send Welcome Email", "type": "main", "index": 0}]]}, "Send Personal Check-in": {"main": [[{"node": "Wait 2 More Days", "type": "main", "index": 0}]]}, "Validate Required Fields": {"main": [[{"node": "Create HubSpot Contact", "type": "main", "index": 0}], [{"node": "Send Validation Error <PERSON>", "type": "main", "index": 0}]]}, "Send Onboarding Documents": {"main": [[{"node": "Wait 1 Day", "type": "main", "index": 0}]]}, "Send Week 1 Success Guide": {"main": [[{"node": "Mark Week 1 Complete", "type": "main", "index": 0}, {"node": "Notify Team of Completion", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5335ad1a-f0ad-4221-bb59-c431de01f454", "meta": {"instanceId": "06dc2f69cd870abfe9d3c116af39b865de427bccaf18688c6a8df8ff517cc5b3"}, "id": "ZXwEjCcMYwuPsS8S", "tags": []}