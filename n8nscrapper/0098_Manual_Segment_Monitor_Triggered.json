{"id": "122", "name": "Track an event in Segment", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Segment", "type": "n8n-nodes-base.segment", "position": [450, 300], "parameters": {"event": "", "resource": "track"}, "credentials": {"segmentApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"On clicking 'execute'": {"main": [[{"node": "Segment", "type": "main", "index": 0}]]}}}