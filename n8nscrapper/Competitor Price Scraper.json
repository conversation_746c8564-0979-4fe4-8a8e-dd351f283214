{"name": "Competitor <PERSON>", "nodes": [{"parameters": {"content": "Automated workflow: Competitor Price Scraper", "height": 530, "width": 1100, "color": 5}, "id": "3658f18f-6ed3-4c14-b12d-9ec7fadc03f9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-480, -240]}, {"parameters": {"httpMethod": "POST", "path": "competitor-price-scraper"}, "id": "9b5a4057-4b5f-44f7-aea9-847d23a8f821", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-300, 0]}, {"parameters": {"chunkSize": 400, "chunkOverlap": 40}, "id": "3b015342-843e-40e8-8164-734611f2c7aa", "name": "Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [-130, 0]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "3b77afdb-828a-48bf-ab39-5600c4ac8512", "name": "Embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [70, 0], "credentials": {"openAiApi": {"id": "OPENAI_API", "name": "OpenAI"}}}, {"parameters": {"mode": "insert", "options": {}, "indexName": "competitor_price_scraper"}, "id": "217480ed-2ad6-407d-80fa-0d5284905815", "name": "Supabase Insert", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [270, 0], "credentials": {"supabaseApi": {"id": "SUPABASE_API", "name": "Supabase account"}}}, {"parameters": {"indexName": "competitor_price_scraper"}, "id": "90ed9d12-3239-48b0-b5a5-23fb2a0172ea", "name": "Supabase Query", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [270, -180], "credentials": {"supabaseApi": {"id": "SUPABASE_API", "name": "Supabase account"}}}, {"parameters": {"name": "Supabase", "description": "Vector context"}, "id": "68bc4d4a-293e-4252-8a88-486aa3e3cb5f", "name": "Vector Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [450, -180]}, {"parameters": {}, "id": "3d31e91e-ad51-4967-903f-7ae2c598a870", "name": "Window Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [450, -40]}, {"parameters": {"options": {}}, "id": "af6568a4-96a6-4b41-8be0-730fb7c0b328", "name": "Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1, "position": [450, -340], "credentials": {"anthropicApi": {"id": "ANTHROPIC_API", "name": "Anthropic"}}}, {"parameters": {"promptType": "define", "text": "Process the following data for task 'Competitor Price Scraper':\n\n{{ $json }}", "options": {"systemMessage": "You are an assistant for Competitor <PERSON> Scraper"}}, "id": "c0a57592-08d8-4b83-80bf-73b63a29966f", "name": "RAG Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [720, -40]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "SHEET_ID", "mode": "list", "cachedResultName": "Competitor <PERSON>"}, "sheetName": {"__rl": true, "value": "Log", "mode": "list", "cachedResultName": "Log"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "={{$json[\"RAG Agent\"].text}}"}, "schema": []}}, "id": "ff81c3bb-35e8-428d-9ad9-491707d7ee15", "name": "Append Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [930, -40], "credentials": {"googleSheetsOAuth2Api": {"id": "SHEETS_API", "name": "Google Sheets account"}}}, {"parameters": {"channel": "#alerts", "text": "Competitor Price Scraper error: {$json.error.message}"}, "id": "9f49421e-8263-4127-ad94-e063318685fb", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [930, 120], "credentials": {"slackApi": {"id": "SLACK_API", "name": "<PERSON><PERSON>ck"}}}], "connections": {"Webhook Trigger": {"main": [[{"node": "Text Splitter", "type": "main", "index": 0}, {"node": "Window Memory", "type": "main", "index": 0}]]}, "Text Splitter": {"main": [[{"node": "Embeddings", "type": "main", "index": 0}]], "ai_textSplitter": [[{"node": "Supabase Insert", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Supabase Insert", "type": "ai_embedding", "index": 0}, {"node": "Supabase Query", "type": "ai_embedding", "index": 0}]]}, "Supabase Insert": {"ai_document": [[]]}, "Supabase Query": {"ai_vectorStore": [[{"node": "Vector Tool", "type": "ai_vectorStore", "index": 0}]]}, "Vector Tool": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "Window Memory": {"ai_memory": [[{"node": "RAG Agent", "type": "ai_memory", "index": 0}]]}, "Chat Model": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "RAG Agent": {"main": [[{"node": "Append Sheet", "type": "main", "index": 0}]], "onError": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "triggerCount": 1}