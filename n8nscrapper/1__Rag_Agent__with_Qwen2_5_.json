{"name": "1. <PERSON><PERSON> (with <PERSON><PERSON>2.5)", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.message.text }}", "contextWindowLength": 100}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [60, 260], "id": "********-c059-4ca8-afab-9ab2fa2dd7c6", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "n4bjsaGMuYUj05YU", "name": "Postgres account"}}}, {"parameters": {"tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {}}, "id": "9f3ae930-43dd-4744-ad8f-b9a16271bf15", "name": "Supabase Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [220, 240], "credentials": {"supabaseApi": {"id": "5zkfZLWcoQNG5AKm", "name": "Supabase account"}}}, {"parameters": {"name": "user_documents", "description": "Contains all the user's documents that you can check for context to answer user questions."}, "id": "d5946ae9-ba4f-4c43-9423-1ac68b76fe0c", "name": "Retrieve Documents", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [320, 60]}, {"parameters": {"model": "qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [580, 220], "id": "fa616883-1ee6-4ebc-85c2-ca5260551a2e", "name": "Qwen 2.5 Instruct", "credentials": {"openRouterApi": {"id": "uBQTiXd5eRsdJrn8", "name": "OpenRouter account"}}}, {"parameters": {"model": "=qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-120, 180], "id": "330714b8-9360-4b93-a300-8b7e1eefe1c4", "name": "Qwen 2.5 Instruct1", "credentials": {"openRouterApi": {"id": "uBQTiXd5eRsdJrn8", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "options": {"systemMessage": "=## Agent Overview\nThis agent retrieves relevant information from stored documents based on user queries using the \"Retrieve Document\" tool in n8n.\n\n## Context\n- The agent is designed to assist users by fetching specific information from documents.\n- It will analyze the user query and match it against the stored document database.\n- The retrieved information will be returned as a response.\n\n## Primary Capabilities\n1. **User Query Processing**\n   - Accepts a query from the user.\n   - Identifies key terms and context.\n2. **Document Retrieval**\n   - Uses the \"Retrieve Document\" tool to search for relevant information.\n   - Fetches the most relevant passages.\n3. **Response Generation**\n   - Returns the retrieved information in a structured response.\n   - Provides source details if available.\n\n## Instructions\n### 1. Input Analysis\n- Accept user input as a query.\n- Ensure the query is clear; if vague, request clarification.\n\n### 2. Retrieve Information\n- Pass the query to the \"Retrieve Document\" tool.\n- Fetch relevant data from stored documents.\n\n### 3. Output Formatting\n- Present the retrieved information in a readable format.\n- If multiple matches are found, summarize key points.\n- Include document references when possible.\n\n### 4. Error Handling\n- If no relevant information is found, return a message indicating no matches.\n- If the query is unclear, ask the user for more details.\n\n## Standard Operating Procedure (SOP)\n1. Receive user query.\n2. Process the query for key terms.\n3. Use the \"Retrieve Document\" tool to fetch relevant data.\n4. Format and return the retrieved information.\n5. Handle errors by requesting clarification or notifying the user if no match is found.\n\n## Notes\n- Ensure fast and accurate retrieval by optimizing document indexing.\n- Allow users to refine their queries for better results.\n- Today date and time is {{$now}}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [40, -100], "id": "272aa282-8e17-4ee3-8b54-b6ebbb295769", "name": "RAG AI Agent"}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "9c48a356-155f-42a8-8860-225f655689ce", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [220, 400], "credentials": {"openAiApi": {"id": "Eq5hz9TcNAkYHxaL", "name": "OpenAi account 3"}}}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-160, -80], "id": "631e8338-9a83-4982-b5d3-4229c9164af1", "name": "<PERSON>eg<PERSON>", "webhookId": "23c0ef55-5228-402e-a3b0-2d0fd4d236a7", "credentials": {"telegramApi": {"id": "lWZEaGqzr8Ce98wN", "name": "Rag <PERSON>"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [440, -160], "id": "27cec8b7-dd95-4721-8b38-957bcfdfb158", "name": "Telegram", "webhookId": "2041258b-6ab8-4cde-b0a5-6b2e3323d517", "credentials": {"telegramApi": {"id": "lWZEaGqzr8Ce98wN", "name": "Rag <PERSON>"}}}], "pinData": {}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Agent", "type": "ai_memory", "index": 0}]]}, "Supabase Vector Store1": {"ai_vectorStore": [[{"node": "Retrieve Documents", "type": "ai_vectorStore", "index": 0}]]}, "Retrieve Documents": {"ai_tool": [[{"node": "RAG AI Agent", "type": "ai_tool", "index": 0}]]}, "Qwen 2.5 Instruct": {"ai_languageModel": [[{"node": "Retrieve Documents", "type": "ai_languageModel", "index": 0}]]}, "Qwen 2.5 Instruct1": {"ai_languageModel": [[{"node": "RAG AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Telegram Trigger": {"main": [[{"node": "RAG AI Agent", "type": "main", "index": 0}]]}, "RAG AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f7139246-5715-4630-896a-e898cf281dd4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e9750110772e39d19e520d5411ea772dd5055cd081f0298a554cb86fb57bf55d"}, "id": "46VI31Uoa4cuSEyc", "tags": [{"createdAt": "2025-02-11T07:19:10.054Z", "updatedAt": "2025-02-11T07:49:34.353Z", "id": "NPbbeoP4jOohrmIG", "name": "05 Rag Supabase"}]}