{"name": "Grow youtube", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-440, 0], "id": "fe37d33c-120b-4f8d-8a9c-ede81588331d", "name": "When clicking ‘Test workflow’"}, {"parameters": {"operation": "get", "channelId": ""}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [-160, 0], "id": "d9c81302-39f9-4f7b-90ee-b208bf428f6d", "name": "YouTube", "credentials": {"youTubeOAuth2Api": {"id": "I4z3Y6tHhJwvOgJ6", "name": "YouTube account 2"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.Captions }}", "options": {"systemMessage": "=You are a helpful assistant that formats YouTube video summaries into clean, Reddit-friendly, **step-by-step instructional posts**.\n\nPlease format the content as a clear guide that the reader can follow step-by-step.\n\nUse the following formatting rules:\n\n- This should be the title: {{ $('Set fields').item.json.title }}\n- Please ensure that each paragraph is separated by a blank line, so that it renders as separate paragraphs on Reddit.\n- Do **NOT** include any emojis.\n- Do **NOT** include YouTube-style intro phrases like \"Hey friends!\", \"Welcome back to the channel\", \"Let's get started\", etc.\n- The introduction must start with the exact phrase: **\"In this step-by-step tutorial,\"** — no additional phrases before this.\n- Use **bold** for the main title and key headings.\n- Use `### Step 1:`, `### Step 2:`, etc. style for the step headings.\n- For lists inside steps, use bullet points.\n- Do not include escaped newlines (no `\\n\\n`), just use normal paragraphs.\n- Keep the tone friendly, informative, and easy to read.\n- Write in an informative style suitable for Reddit audiences (not YouTube-style language).\n- At the end, include a short **Wrap-up** section to close the post.\n- The final output should be at least **300-500 characters long** (do not produce a too-short post).\n\nHere is the raw summary content to format:\n\n{{ $json.Captions }}\n\nPlease output the formatted Reddit post text only and add  {{ $('Set fields').item.json.link }} at the end — no extra comments.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1420, 0], "id": "1d7fe57d-59e6-4142-8ab7-6b54d887d03d", "name": "AI Agent"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Set fields').item.json.title }}", "rightValue": "n8n", "operator": {"type": "string", "operation": "contains"}, "id": "a11c1f6a-7221-4ce8-8c99-5faaee77979d"}], "combinator": "and"}, "renameOutput": true, "outputKey": "n8n"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "3bc58f1c-648b-41bb-b395-553bffbfb859", "leftValue": "={{ $('Set fields').item.json.title }}", "rightValue": "AI Tools", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "AI Tools"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8b1e7e77-a980-4615-8965-c4b576ed0752", "leftValue": "={{ $('Set fields').item.json.title }}", "rightValue": "AI Avatar", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "AI Avatar"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [2220, 0], "id": "83a3c819-b62b-4ecc-8709-6cad2298f428", "name": "Switch"}, {"parameters": {"subreddit": "n8n", "title": "={{ $('Get latest video').item.json.snippet.title }}", "text": "={{ $('Set fields').item.json.description }}"}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [2900, -240], "id": "caeaa7be-2c2a-4171-8237-b1bfb1873ba3", "name": "n8n", "credentials": {"redditOAuth2Api": {"id": "", "name": "Reddit account"}}}, {"parameters": {"subreddit": "generativeAI", "title": "={{ $('Get latest video').item.json.snippet.title }}", "text": "={{ $('Set fields').item.json.description }}"}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [2920, 20], "id": "d1e8affc-9d7c-4742-9eac-fd20a1588863", "name": "generativeAI", "credentials": {"redditOAuth2Api": {"id": "", "name": "Reddit account"}}}, {"parameters": {"subreddit": "artificial", "title": "={{ $('Get latest video').item.json.snippet.title }}", "text": "={{ $('Set fields').item.json.description }}"}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [2940, 280], "id": "27f0f0fe-eca2-4afb-b2d1-b7e32742018c", "name": "artificial", "credentials": {"redditOAuth2Api": {"id": "", "name": "Reddit account"}}}, {"parameters": {"jsCode": "let gptText = $json[\"output\"] || '';\n\n// Normalize Windows newlines\ngptText = gptText.replace(/\\r\\n/g, '\\n');\n\n// Add blank line after title (bold title)\ngptText = gptText.replace(/\\*\\*(.*?)\\*\\*\\n\\n?/, '**$1**\\n\\n');\n\n// Add blank line after intro paragraph (first paragraph after title)\ngptText = gptText.replace(/(\\*\\*.*\\*\\*\\n\\n[^\\n]+)\\n(### )/, '$1\\n\\n$2');\n\n// Add blank line after each heading\ngptText = gptText.replace(/(### [^\\n]+)\\n(?!\\n)/g, '$1\\n\\n');\n\n// Collapse triple newlines to double\ngptText = gptText.replace(/\\n{3,}/g, '\\n\\n');\n\n// Trim trailing spaces on line breaks\ngptText = gptText.replace(/[ ]+\\n/g, '\\n');\n\nconst formattedText = gptText.trim();\n\nreturn {\n    json: {\n        formatted: formattedText\n    }\n};\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1880, 0], "id": "678f674d-5bec-4923-b82d-4dfed074df6c", "name": "Code"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Generate captions').item.json.items[0].id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1540, 280], "id": "56be38e4-fd71-46de-8347-3f11764b4e53", "name": "Simple Memory"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1420, 260], "id": "a272b427-b507-4bb1-96ca-d019d2ab032b", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "MMw5WkceTrbujzcn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"resource": "video", "limit": 1, "filters": {}, "options": {"order": "date"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [60, 0], "id": "b537cfbf-f8c6-438a-bb09-6d32e7bbdb98", "name": "Get latest video", "credentials": {"youTubeOAuth2Api": {"id": "I4z3Y6tHhJwvOgJ6", "name": "YouTube account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a2f71a68-6b01-46cf-8e88-7d15286cd914", "name": "title", "value": "={{ $json.snippet.title }}", "type": "string"}, {"id": "15cac6a8-236d-4312-82d3-2d298865b2b3", "name": "description", "value": "={{ $json.snippet.description }}", "type": "string"}, {"id": "b2c47c8f-9203-42c9-ad71-4baa53786658", "name": "link", "value": "=https://www.youtube.com/watch?v={{ $json.id.videoId }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [280, 0], "id": "c76004f3-01a3-41f6-9fdf-47ac4aa81102", "name": "Set fields"}, {"parameters": {"url": "https://youtube.googleapis.com/youtube/v3/captions", "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "videoId", "value": "NNShrqPjm5I"}, {"name": "key", "value": ""}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 0], "id": "8e854a1c-a262-4efa-b771-ce653407c4e5", "name": "Get captions"}, {"parameters": {"url": "=https://youtube.googleapis.com/youtube/v3/captions/ {{ $json.items[0].id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "tfmt", "value": "srt"}, {"name": "key", "value": ""}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 0], "id": "96d49fc8-09c6-437b-8a70-7b3a2da6e9e0", "name": "Generate captions", "credentials": {"youTubeOAuth2Api": {"id": "", "name": "YouTube account 2"}}}, {"parameters": {"operation": "text", "destinationKey": "Captions", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1120, 0], "id": "e60cd72a-ddce-4d5c-b74a-6b48e5673cef", "name": "Converts SRT to text"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "YouTube": {"main": [[{"node": "Get latest video", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "n8n", "type": "main", "index": 0}], [{"node": "generativeAI", "type": "main", "index": 0}, {"node": "artificial", "type": "main", "index": 0}], [{"node": "artificial", "type": "main", "index": 0}, {"node": "generativeAI", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Get latest video": {"main": [[{"node": "Set fields", "type": "main", "index": 0}]]}, "Set fields": {"main": [[{"node": "Get captions", "type": "main", "index": 0}]]}, "Get captions": {"main": [[{"node": "Generate captions", "type": "main", "index": 0}]]}, "Generate captions": {"main": [[{"node": "Converts SRT to text", "type": "main", "index": 0}]]}, "Converts SRT to text": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4197d6bc-ed4c-48d6-bd2a-8db46954b11f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f21f53385ae396fcb0c1b69950e1ec16f2dcc4ddca34a170466835249ec1c42c"}, "id": "gSMnXTmsU1Aq1OUh", "tags": []}