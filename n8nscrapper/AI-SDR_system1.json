{"name": "AI SDR - Complete Outbound Sales Automation", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "value": 8}]}}, "id": "f5d4e3c2-b1a0-4b5c-8d7e-1a2b3c4d5e6f", "name": "Daily Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"resource": "contact", "operation": "getAll", "returnAll": true, "filters": {"stage": "lead"}}, "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "Get New Leads", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.properties.email }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.properties.company }}", "operation": "isNotEmpty"}]}}, "id": "b2c3d4e5-f6a7-8901-bcde-f23456789012", "name": "Filter Qualified Leads", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "https://api.clearbit.com/v2/enrichment/find", "authentication": "predefinedCredentialType", "nodeCredentialType": "clearbitApi", "options": {}, "parametersUi": {"parameter": [{"name": "email", "value": "={{ $json.properties.email }}"}]}}, "id": "c3d4e5f6-a7b8-9012-cdef-345678901234", "name": "<PERSON>rich Lead Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"model": "gpt-4", "options": {}, "prompt": "Analyze this lead profile and generate a personalized outreach strategy:Lead Info:- Name: {{ $('Get New Leads').item.json.properties.firstname }} {{ $('Get New Leads').item.json.properties.lastname }}- Company: {{ $('Get New Leads').item.json.properties.company }}- Title: {{ $('Get New Leads').item.json.properties.jobtitle }}- Industry: {{ $json.company.category.industry }}- Company Size: {{ $json.company.metrics.employees }}- Technologies: {{ $json.company.tech }}Generate:1. Lead qualification score (1-10)2. Personalized email subject line3. 3-sentence personalized opening for email4. Key value proposition to highlight5. Recommended outreach timingReturn as JSON format."}, "id": "d4e5f6a7-b8c9-0123-def4-56789012345a", "name": "AI Lead Analysis", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"conditions": {"number": [{"value1": "={{ JSON.parse($json.choices[0].message.content).score }}", "operation": "largerEqual", "value2": 7}]}}, "id": "e5f6a7b8-c9d0-1234-ef56-789012345bcd", "name": "Check Lead Score", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{ $('Get New Leads').item.json.id }}", "updateFields": {"properties": [{"name": "lead_score", "value": "={{ JSON.parse($('AI Lead Analysis').item.json.choices[0].message.content).score }}"}, {"name": "ai_analysis", "value": "={{ JSON.parse($('AI Lead Analysis').item.json.choices[0].message.content) }}"}, {"name": "lifecycle_stage", "value": "qualified_lead"}]}}, "id": "f6a7b8c9-d0e1-2345-f678-90123456cdef", "name": "Update Lead in HubSpot", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"model": "gpt-4", "options": {}, "prompt": "Generate a personalized cold outreach email based on this analysis:{{ JSON.parse($('AI Lead Analysis').item.json.choices[0].message.content) }}Lead Details:- Name: {{ $('Get New Leads').item.json.properties.firstname }}- Company: {{ $('Get New Leads').item.json.properties.company }}- Title: {{ $('Get New Leads').item.json.properties.jobtitle }}Generate a professional, personalized email that:1. Uses the suggested subject line2. Includes the personalized opening3. Highlights our AI SDR solution benefits4. Includes a clear call-to-action for a 15-minute demo5. Keeps it under 150 wordsReturn the email in this JSON format:{ \"subject\": \"...\", \"body\": \"...\"}"}, "id": "a7b8c9d0-e1f2-3456-789a-bcdef0123456", "name": "Generate Personalized Email", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"sendTo": "={{ $('Get New Leads').item.json.properties.email }}", "subject": "={{ JSON.parse($json.choices[0].message.content).subject }}", "message": "={{ JSON.parse($json.choices[0].message.content).body }}", "options": {"senderName": "Sales Team"}}, "id": "b8c9d0e1-f2a3-4567-89ab-cdef01234567", "name": "Send Initial Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2000, 200]}, {"parameters": {"amount": 2, "unit": "days"}, "id": "c9d0e1f2-a3b4-5678-9abc-def012345678", "name": "Wait 2 Days", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [2220, 200]}, {"parameters": {"resource": "contact", "operation": "get", "contactId": "={{ $('Get New Leads').item.json.id }}"}, "id": "d0e1f2a3-b4c5-6789-abcd-ef0123456789", "name": "Check Engagement", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [2440, 200]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.properties.last_engagement_date }}", "operation": "isEmpty"}]}}, "id": "e1f2a3b4-c5d6-789a-bcde-f01234567890", "name": "Check No Response", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2660, 200]}, {"parameters": {"model": "gpt-4", "options": {}, "prompt": "Generate a follow-up email for this lead who hasn't responded to our initial outreach:Original Analysis: {{ $('Get New Leads').item.json.properties.ai_analysis }}Lead Details:- Name: {{ $('Get New Leads').item.json.properties.firstname }}- Company: {{ $('Get New Leads').item.json.properties.company }}Generate a different angle follow-up email that:1. References a recent industry trend or news2. Offers a valuable resource (case study, whitepaper, etc.)3. Creates urgency without being pushy4. Includes social proof5. Keeps it under 100 wordsReturn as JSON format with subject and body."}, "id": "f2a3b4c5-d6e7-89ab-cdef-012345678901", "name": "Generate Follow-up Email", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [2880, 120]}, {"parameters": {"sendTo": "={{ $('Get New Leads').item.json.properties.email }}", "subject": "={{ JSON.parse($json.choices[0].message.content).subject }}", "message": "={{ JSON.parse($json.choices[0].message.content).body }}", "options": {"senderName": "Sales Team"}}, "id": "a3b4c5d6-e7f8-9abc-def0-123456789012", "name": "Send Follow-up Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [3100, 120]}, {"parameters": {"amount": 3, "unit": "days"}, "id": "b4c5d6e7-f8a9-abcd-ef01-234567890123", "name": "Wait 3 Days", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [3320, 120]}, {"parameters": {"resource": "contact", "operation": "get", "contactId": "={{ $('Get New Leads').item.json.id }}"}, "id": "c5d6e7f8-a9ba-bcde-f012-345678901234", "name": "Final Engagement Check", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [3540, 120]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.properties.last_engagement_date }}", "operation": "isEmpty"}]}}, "id": "d6e7f8a9-bacb-cdef-0123-456789012345", "name": "Final No Response Check", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [3760, 120]}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{ $('Get New Leads').item.json.id }}", "updateFields": {"properties": [{"name": "lifecycle_stage", "value": "unqualified"}, {"name": "lead_status", "value": "no_response"}]}}, "id": "e7f8a9ba-cbdc-def0-1234-567890123456", "name": "<PERSON> as Unqualified", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [3980, 80]}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{ $('Get New Leads').item.json.id }}", "updateFields": {"properties": [{"name": "lifecycle_stage", "value": "engaged_lead"}, {"name": "lead_status", "value": "responded"}]}}, "id": "f8a9bacb-dced-ef01-2345-************", "name": "<PERSON> as Engaged", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [3980, 200]}, {"parameters": {"url": "https://api.calendly.com/scheduling_links", "authentication": "predefinedCredentialType", "nodeCredentialType": "calen<PERSON><PERSON><PERSON>", "sendBody": true, "bodyParameters": {"parameters": [{"name": "max_event_count", "value": "1"}, {"name": "owner", "value": "https://api.calendly.com/users/YOUR_USER_UUID"}]}}, "id": "a9bacbdc-def0-1234-5678-90123456789a", "name": "Create Meeting Link", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [4200, 200]}, {"parameters": {"model": "gpt-4", "options": {}, "prompt": "Generate a meeting booking email for this engaged lead:Lead: {{ $('Get New Leads').item.json.properties.firstname }} from {{ $('Get New Leads').item.json.properties.company }}Generate an email that:1. Thanks them for their interest2. Suggests a 15-minute discovery call3. Includes the Calendly link: {{ $json.resource.booking_url }}4. Sets clear expectations for the call5. Professional but friendly toneReturn as JSON with subject and body."}, "id": "bacbdcde-f012-3456-789a-bcdef0123456", "name": "Generate Meeting Email", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [4420, 200]}, {"parameters": {"sendTo": "={{ $('Get New Leads').item.json.properties.email }}", "subject": "={{ JSON.parse($json.choices[0].message.content).subject }}", "message": "={{ JSON.parse($json.choices[0].message.content).body }}", "options": {"senderName": "Sales Team"}}, "id": "cbdcdef0-1234-5678-9abc-def012345678", "name": "Send Meeting Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [4640, 200]}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{ $('Get New Leads').item.json.id }}", "updateFields": {"properties": [{"name": "lifecycle_stage", "value": "marketing_qualified_lead"}, {"name": "lead_status", "value": "meeting_scheduled"}]}}, "id": "dcdef012-3456-789a-bcde-f01234567890", "name": "Update to MQL", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [4860, 200]}, {"parameters": {"resource": "contact", "operation": "update", "contactId": "={{ $('Get New Leads').item.json.id }}", "updateFields": {"properties": [{"name": "lifecycle_stage", "value": "unqualified"}, {"name": "lead_status", "value": "low_score"}]}}, "id": "def01234-5678-9abc-def0-123456789012", "name": "<PERSON> Score Lead", "type": "n8n-nodes-base.hubspot", "typeVersion": 1, "position": [1560, 400]}, {"parameters": {"functionCode": "const items = $input.all();const analytics = { totalLeads: items.length, qualifiedLeads: items.filter(item => item.json.properties && item.json.properties.lead_score >= 7 ).length, emailsSent: items.filter(item => item.json.email_sent === true ).length, engagements: items.filter(item => item.json.properties && item.json.properties.last_engagement_date ).length, meetingsScheduled: items.filter(item => item.json.properties && item.json.properties.lifecycle_stage === 'marketing_qualified_lead' ).length, conversionRate: 0};analytics.conversionRate = analytics.totalLeads > 0 ? (analytics.meetingsScheduled / analytics.totalLeads * 100).toFixed(2) : 0;return [{ json: analytics }];"}, "id": "f0123456-789a-bcde-f012-3456789abcde", "name": "Calculate Analytics", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [5080, 300]}, {"parameters": {"url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "ð Daily AI SDR Reportâ¢ Total Leads Processed: {{ $json.totalLeads }}â¢ Qualified Leads: {{ $json.qualifiedLeads }}â¢ Emails Sent: {{ $json.emailsSent }}â¢ Engagements: {{ $json.engagements }}â¢ Meetings Scheduled: {{ $json.meetingsScheduled }}â¢ Conversion Rate: {{ $json.conversionRate }}%"}]}}, "id": "0123456789-abcd-ef01-2345-6789abcdef01", "name": "Send Slack Report", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [5300, 300]}], "connections": {"Daily Trigger": {"main": [[{"node": "Get New Leads", "type": "main", "index": 0}]]}, "Get New Leads": {"main": [[{"node": "Filter Qualified Leads", "type": "main", "index": 0}]]}, "Filter Qualified Leads": {"main": [[{"node": "<PERSON>rich Lead Data", "type": "main", "index": 0}], []]}, "Enrich Lead Data": {"main": [[{"node": "AI Lead Analysis", "type": "main", "index": 0}]]}, "AI Lead Analysis": {"main": [[{"node": "Check Lead Score", "type": "main", "index": 0}]]}, "Check Lead Score": {"main": [[{"node": "Update Lead in HubSpot", "type": "main", "index": 0}], [{"node": "<PERSON> Score Lead", "type": "main", "index": 0}]]}, "Update Lead in HubSpot": {"main": [[{"node": "Generate Personalized Email", "type": "main", "index": 0}]]}, "Generate Personalized Email": {"main": [[{"node": "Send Initial Email", "type": "main", "index": 0}]]}, "Send Initial Email": {"main": [[{"node": "Wait 2 Days", "type": "main", "index": 0}]]}, "Wait 2 Days": {"main": [[{"node": "Check Engagement", "type": "main", "index": 0}]]}, "Check Engagement": {"main": [[{"node": "Check No Response", "type": "main", "index": 0}]]}, "Check No Response": {"main": [[{"node": "Generate Follow-up Email", "type": "main", "index": 0}], [{"node": "<PERSON> as Engaged", "type": "main", "index": 0}]]}, "Generate Follow-up Email": {"main": [[{"node": "Send Follow-up Email", "type": "main", "index": 0}]]}, "Send Follow-up Email": {"main": [[{"node": "Wait 3 Days", "type": "main", "index": 0}]]}, "Wait 3 Days": {"main": [[{"node": "Final Engagement Check", "type": "main", "index": 0}]]}, "Final Engagement Check": {"main": [[{"node": "Final No Response Check", "type": "main", "index": 0}]]}, "Final No Response Check": {"main": [[{"node": "<PERSON> as Unqualified", "type": "main", "index": 0}], [{"node": "<PERSON> as Engaged", "type": "main", "index": 0}]]}, "Mark as Engaged": {"main": [[{"node": "Create Meeting Link", "type": "main", "index": 0}]]}, "Create Meeting Link": {"main": [[{"node": "Generate Meeting Email", "type": "main", "index": 0}]]}, "Generate Meeting Email": {"main": [[{"node": "Send Meeting Email", "type": "main", "index": 0}]]}, "Send Meeting Email": {"main": [[{"node": "Update to MQL", "type": "main", "index": 0}]]}, "Update to MQL": {"main": [[{"node": "Calculate Analytics", "type": "main", "index": 0}]]}, "Mark Low Score Lead": {"main": [[{"node": "Calculate Analytics", "type": "main", "index": 0}]]}, "Mark as Unqualified": {"main": [[{"node": "Calculate Analytics", "type": "main", "index": 0}]]}, "Calculate Analytics": {"main": [[{"node": "Send Slack Report", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 1, "updatedAt": "2025-06-18T12:00:00.000Z", "versionId": "1"}