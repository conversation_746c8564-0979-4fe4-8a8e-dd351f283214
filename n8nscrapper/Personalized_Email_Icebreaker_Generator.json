{"name": "Personalized Email Icebreaker Generator", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-520, 400], "id": "7564787a-fea5-4635-ad66-08009ff5775d", "name": "When clicking ‘Test workflow’"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "links", "cssSelector": "a", "returnValue": "attribute", "attribute": "href", "returnArray": true}]}, "options": {"trimValues": true, "cleanUpText": true}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [20, 180], "id": "f6a58e19-8ed7-4560-acc7-8cce886e8081", "name": "HTML"}, {"parameters": {"fieldToSplitOut": "links", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-300, 620], "id": "33e9a62d-a342-47e7-80b3-55a83f6a3f9e", "name": "Split Out"}, {"parameters": {"url": "={{ $json.links }}", "options": {}}, "id": "a7234b59-6f6f-4b6d-b90d-782ac443e961", "name": "Request web page for URL", "type": "n8n-nodes-base.httpRequest", "position": [340, 380], "typeVersion": 4.2, "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"html": "={{ $json.data ? $json.data : \"<div>empty</div>\" }}", "options": {}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [500, 380], "id": "14d5913a-1d53-4ab0-9853-ae9b82d6945e", "name": "<PERSON><PERSON>"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent website scraping assistant.", "role": "system"}, {"content": "You're provided a Markdown scrape of a website page. Your task is to provide a two-paragraph abstract of what this page is about.\n\nReturn in this JSON format:\n\n{\"abstract\":\"your abstract goes here\"}\n\nRules:\n- Your extract should be comprehensive—similar level of detail as an abstract to a published paper.\n- Use a straightforward, spartan tone of voice.\n- If it's empty, just say \"no content\"."}, {"content": "={{ $json.data }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [800, 260], "id": "277e3440-68b4-4d10-bfb6-56e96cefc424", "name": "Summarize Website Page"}, {"parameters": {"maxItems": 3}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [20, 620], "id": "bd24e74b-27ee-4b6b-a51c-bb6ab0404d5e", "name": "Limit"}, {"parameters": {"url": "={{ $json['organization_website_url'] }}", "options": {"allowUnauthorizedCerts": false, "redirect": {"redirect": {}}}}, "id": "c2ebb111-6899-490f-8bd5-c0e63adf8252", "name": "Scrape Home", "type": "n8n-nodes-base.httpRequest", "position": [-140, 180], "executeOnce": false, "typeVersion": 4.2, "alwaysOutputData": false, "onError": "continueErrorOutput"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "message.content.abstract"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1120, 260], "id": "02807521-5597-4e17-8a14-0ba06d2a8a89", "name": "Aggregate"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent sales assistant.", "role": "system"}, {"content": "=We just scraped a series of web pages for a business. Your task is to take their summaries and turn them into catchy, personalized openers for a cold email campaign to imply that the rest of the campaign is personalized.\n\nYou'll return your icebreakers in the following JSON format:\n\n{\"icebreaker\":\"Hey {name}. I noticed your recent work on {thing} and thought it was brilliant. I’m also involved in {otherThing} and wanted to share something with you.\\n\\nI spent some time exploring your site and saw how much emphasis you place on {anotherThing}. A few months ago I built a tool that could make that even easier.\"}\n\nWe’ve been working with web agencies like LeenWeb, GreatNash and Galax media on something simple but highly effective:\n\nA system that finds businesses from local business directories.\nAudits their site (design, SEO, speed, accessibility)\nThen sends them a cold email that feels like it came from a real person\n\nLast 2 months: $70K+ generated through this approach\n\nNo fluff. No hand-holding. You get leads with prewritten emails and audits — ready to send.\n\nTo make a long story short, it's an outreach system that uses AI automation to handle outreach for web agencies. Costs just a few cents to run, very high converting, and I think it's in line with {someImpliedBeliefTheyHave}\"}\n\nIf you're curious, I’d love to show you a short video of it in action, or we can hop on a 10-min call if that’s easier. Just let me know what works for you.\n\nRules:\n- Write in a spartan/laconic tone of voice.\n- Make sure to use the above format when constructing your icebreakers. We wrote it this way on purpose.\n- Shorten the company name wherever possible (say, \"XYZ\" instead of \"XYZ Agency\"). More examples: \"Love AMS\" instead of \"Love AMS Professional Services\", \"Love Mayo\" instead of \"Love Mayo Inc.\", etc.\n- Do the same with locations. \"San Fran\" instead of \"San Francisco\", \"BC\" instead of \"British Columbia\", etc.\n- For your variables, focus on small, non-obvious things to paraphrase. The idea is to make people think we *really* dove deep into their website, so don't use something obvious. Do not say cookie-cutter stuff like \"Love your website!\" or \"Love your take on marketing!\".\n-Do not use hipens and Emojies"}, {"content": "=Profile: <PERSON><PERSON>, CEO founder - Maki Agency / Ti'bouffe, Maki agency, outsourcing/offshoring, Antananarivo, Madagascar\n\nWebsite: \n\nThis webpage presents Maki Agency, a professional digital outsourcing company based in Madagascar that specializes in tailored web development, integration, design, SEO, content creation, community management, and more. The agency offers a range of white-label and dedicated resource solutions, targeting businesses that wish to outsource various digital projects. Maki Agency emphasizes its team's versatile technical expertise across major web technologies, including CSS, HTML, JQuery, WordPress, PHP, WooCommerce, Laravel, and Odoo. The company positions itself as an ideal partner for comprehensive digital support, ensuring that client's digital and branding needs are met through experienced personnel and rigorous project management.\\n\\nThe website details the specific services provided, such as graphic and web design (logos, banners, retouching), web integration (landing pages, newsletters, site layouts), development (showcase sites, e-commerce, intranets, bespoke applications, maintenance), content writing (SEO-optimized texts, articles, product sheets, social media posts), search engine optimization (audits, optimizations, submissions), and social media management. The agency highlights its strengths in quality of work, experience, and discretion, especially in white-label arrangements. Visitors are encouraged to contact Maki Agency for new or existing projects, and convenient contact options (phone, QR codes, social media, chat) are provided for initiating discussions.\n\nThis page presents Maki Agency, a Madagascar-based digital agency specializing in web outsourcing and subcontracting services. The agency emphasizes its experience and dedicated team capable of handling diverse digital tasks such as web development, design, SEO, content writing, integration, community management, and maintenance. Maki Agency offers both white-label and dedicated resource solutions for clients seeking to externalize parts of their workflow to a specialized offshore partner. The descriptions highlight the agency's proficiency in popular web technologies, frameworks, and CMS platforms (such as HTML, CSS, PHP, WordPress, Laravel, WooCommerce, and Odoo), as well as its ability to execute projects ranging from landing pages, e-commerce platforms, and intranets to detailed graphic design and content creation.\\n\\nThe site underscores Maki Agency's core values and competitive advantages, such as meticulous attention to detail, experienced professionals, creativity, discretion, and a client-focused approach. It provides detailed breakdowns of service offerings, ranging from graphic materials (logos, flyers, banners), technical integration, app and website development, staff outsourcing (developers, designers, writers), SEO strategies, community management, and digital content production. Contact details and multiple avenues for communication (phone, WhatsApp, Skype, QR codes) are prominently featured, along with encouragements for clients to reach out for consultations or ongoing projects requiring outsourcing. The agency also highlights its longevity and adaptability in the digital sector, supporting clients across various industries and digital competencies"}, {"content": "{\"icebreaker\":\"Hey <PERSON><PERSON>,\\n\\nLove what you're doing at <PERSON><PERSON>. Also doing some outsourcing right now, wanted to run something by you.\\n\\nSo I hope you'll forgive me, but I creeped you/<PERSON><PERSON> quite a bit. I know that discretion is important to you guys (or at least I'm assuming this given the part on your website about white-labelling your services) and I put something together a few months ago that I think could help. To make a long story short, it's an outreach system that uses AI to find people hiring website devs. Then pitches them with templates (actually makes them a white-labelled demo website). Costs just a few cents to run, very high converting, and I think it's in line with <PERSON><PERSON>'s emphasis on scalability.\"}", "role": "assistant"}, {"content": "=Profile: {{ $node[\"Get Search URL\"].json.first_name }} {{ $node[\"Get Search URL\"].json.last_name }} {{ $node[\"Get Search URL\"].json.headline }}\n\nWebsite: {{ $json.abstract.join(\"/n\") }}"}]}, "jsonOutput": true, "options": {"temperature": 0.5}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [800, 480], "id": "96647bf7-4646-4284-bc34-48ed5aafedb6", "name": "Generate Multiline Icebreaker"}, {"parameters": {"documentId": {"__rl": true, "value": "1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY", "mode": "list", "cachedResultName": "Marketing Companies (AUS)(Fully verified)", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 1034888960, "mode": "list", "cachedResultName": "Marketing Companies (AUS)(Fully verified)", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY/edit#gid=1034888960"}, "filtersUI": {"values": [{"lookupColumn": "Status", "lookupValue": "="}]}, "options": {"returnFirstMatch": true}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-300, 180], "id": "e40ac764-830e-4345-8616-526f99692894", "name": "Get Search URL", "executeOnce": false, "alwaysOutputData": true}, {"parameters": {"assignments": {"assignments": [{"id": "40fd7130-c65d-4826-a713-ecca24d23b07", "name": "links", "value": "={{ $json.links }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [20, 400], "id": "74988d5f-aff9-466a-99c7-feef330e3946", "name": "<PERSON>"}, {"parameters": {"jsCode": "const items = $input.all();\n\n// Get base URL from previous node (make sure it's executed!)\nconst BASE_URL_RAW = $('Get Search URL').first().json.organization_website_url?.trim();\nif (!BASE_URL_RAW) {\n  throw new Error(\"Missing organization_website_url from 'Code1' node.\");\n}\n\nconst BASE_URL = BASE_URL_RAW.replace(/\\/$/, \"\"); // remove trailing slash\n\nconst getHostname = (url) => {\n  return url\n    .replace(/^https?:\\/\\//, \"\")\n    .replace(/^www\\./, \"\")\n    .split(\"/\")[0];\n};\n\nconst baseHostname = getHostname(BASE_URL);\n\n// Initialize result\nconst seen = new Set();\nconst updatedItems = [\n  { json: { links: `${BASE_URL}/` } }\n];\nseen.add(`${BASE_URL}/`);\n\nitems.forEach((item) => {\n  const link = item?.json?.links;\n  if (typeof link !== \"string\") return;\n\n  const cleanLink = link.trim().toLowerCase();\n\n  const isAnchor = cleanLink.startsWith(\"#\");\n  const isMailOrPhone = cleanLink.startsWith(\"mailto:\") || cleanLink.startsWith(\"tel:\");\n  const isEmpty = cleanLink === \"\" || cleanLink === \"#\" || cleanLink === \"#.\";\n  const isSocial = [\n    \"facebook.com\", \"instagram.com\", \"linkedin.com\", \"twitter.com\",\n    \"youtube.com\", \"tiktok.com\", \"pinterest.com\", \"snapchat.com\"\n  ].some(domain => cleanLink.includes(domain));\n\n  if (isAnchor || isMailOrPhone || isEmpty || isSocial) return;\n\n  let fullUrl = null;\n\n  if (cleanLink.startsWith(\"http://\") || cleanLink.startsWith(\"https://\")) {\n    const linkHostname = getHostname(cleanLink);\n    if (linkHostname !== baseHostname) return;\n\n    // Extract path manually\n    const pathParts = cleanLink.split(linkHostname);\n    const path = pathParts[1]?.split(/[?#]/)[0] || \"/\";\n    const cleanPath = path.endsWith(\"/\") && path !== \"/\" ? path.slice(0, -1) : path;\n\n    fullUrl = `${BASE_URL}${cleanPath}`;\n  } else if (cleanLink.startsWith(\"/\")) {\n    const cleanPath = cleanLink.endsWith(\"/\") && cleanLink !== \"/\" ? cleanLink.slice(0, -1) : cleanLink;\n    fullUrl = `${BASE_URL}${cleanPath}`;\n  }\n\n  if (fullUrl && !seen.has(fullUrl)) {\n    seen.add(fullUrl);\n    updatedItems.push({ json: { links: fullUrl } });\n  }\n});\n\nreturn updatedItems;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-140, 620], "id": "08acbf58-bb7f-4653-b058-5ffddc02eec7", "name": "Code"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY", "mode": "list", "cachedResultName": "Marketing Companies (AUS)(Fully verified)", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 1034888960, "mode": "list", "cachedResultName": "Marketing Companies (AUS)(Fully verified)", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY/edit#gid=1034888960"}, "columns": {"mappingMode": "defineBelow", "value": {"IceBreaker": "={{ $json.message.content.icebreaker }}", "Status": "Done", "linkedin_url": "={{ $node[\"Get Search URL\"].json.linkedin_url}}"}, "matchingColumns": ["linkedin_url"], "schema": [{"id": "first_name", "displayName": "first_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "last_name", "displayName": "last_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "personal_email", "displayName": "personal_email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "linkedin_url", "displayName": "linkedin_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Company Name", "displayName": "Company Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "organization_website_url", "displayName": "organization_website_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "IceBreaker", "displayName": "IceBreaker", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "headline", "displayName": "headline", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company LinkedIn URL", "displayName": "Company LinkedIn URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "organization_phone", "displayName": "organization_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_postal_code", "displayName": "organization_postal_code", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_primary_domain", "displayName": "organization_primary_domain", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_raw_address", "displayName": "organization_raw_address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "photo_url", "displayName": "photo_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "seniority", "displayName": "seniority", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "state", "displayName": "state", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "twitter_url", "displayName": "twitter_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "username", "displayName": "username", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "domain", "displayName": "domain", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "overall_score", "displayName": "overall_score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1120, 480], "id": "6d88bd53-4a6c-4e84-b221-0dbf3390390d", "name": "Google Sheets"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY", "mode": "list", "cachedResultName": "Marketing Companies (AUS)(Fully verified)", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 1034888960, "mode": "list", "cachedResultName": "Marketing Companies (AUS)(Fully verified)", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1z4_JWLSsO7V_2U9duotxaCXdhcDACwFLWtKkX9KRxOY/edit#gid=1034888960"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "Error", "linkedin_url": "={{ $('Get Search URL').item.json.linkedin_url }}"}, "matchingColumns": ["linkedin_url"], "schema": [{"id": "first_name", "displayName": "first_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "last_name", "displayName": "last_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "name", "displayName": "name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "personal_email", "displayName": "personal_email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "linkedin_url", "displayName": "linkedin_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Company Name", "displayName": "Company Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_website_url", "displayName": "organization_website_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "IceBreaker", "displayName": "IceBreaker", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "headline", "displayName": "headline", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company LinkedIn URL", "displayName": "Company LinkedIn URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_phone", "displayName": "organization_phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_postal_code", "displayName": "organization_postal_code", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_primary_domain", "displayName": "organization_primary_domain", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "organization_raw_address", "displayName": "organization_raw_address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "photo_url", "displayName": "photo_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "seniority", "displayName": "seniority", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "state", "displayName": "state", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "twitter_url", "displayName": "twitter_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "username", "displayName": "username", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "domain", "displayName": "domain", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "overall_score", "displayName": "overall_score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-140, 400], "id": "6751b3ab-9385-4b9b-b8b0-843e7aef1d00", "name": "Google Sheets1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0f0accc3-f769-43ee-9d8c-350748f3edfb", "leftValue": "={{ $json.links }}", "rightValue": "", "operator": {"type": "array", "operation": "empty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-300, 400], "id": "aa4b728c-d0be-4d6e-bf90-b766972a0de1", "name": "If"}, {"parameters": {"content": "# Step-1\n## Search website & find all pages on their website", "height": 840, "width": 600, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-400, 40], "typeVersion": 1, "id": "232b8a91-1866-47cb-87c9-8cbb1044451e", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Step-2 \n## Visit each page & convert html to text", "height": 840, "width": 420, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [260, 40], "typeVersion": 1, "id": "75c69f4d-cbb6-402c-b7b4-e72a73dcef76", "name": "Sticky Note1"}, {"parameters": {"content": "# Step-3\n## Summarize each page & generate icebreaker", "height": 840, "width": 580, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [720, 40], "typeVersion": 1, "id": "88cdbb08-98e1-407b-a20b-57596398930f", "name": "Sticky Note2"}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Get Search URL", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Request web page for URL": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Markdown": {"main": [[{"node": "Summarize Website Page", "type": "main", "index": 0}]]}, "Summarize Website Page": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Request web page for URL", "type": "main", "index": 0}]]}, "Scrape Home": {"main": [[{"node": "HTML", "type": "main", "index": 0}], [{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Generate Multiline Icebreaker", "type": "main", "index": 0}]]}, "Generate Multiline Icebreaker": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Get Search URL": {"main": [[{"node": "Scrape Home", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Get Search URL", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Get Search URL", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "7be730e9-2013-41a4-a6bb-91a560e6258f", "meta": {"instanceId": "2915fcf85c51117af269d163a79f6919851d1f2b3a14b3ee1e6fc84ddeb7094c"}, "id": "onXz48KA7e89s9PI", "tags": []}