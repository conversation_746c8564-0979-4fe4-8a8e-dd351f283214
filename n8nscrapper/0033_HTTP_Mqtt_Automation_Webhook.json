{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [490, 360], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}, {"name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [690, 360], "parameters": {"url": "https://api.wheretheiss.at/v1/satellites/25544/positions", "options": {}, "queryParametersUi": {"parameter": [{"name": "timestamps", "value": "={{Date.now()}}"}]}}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [890, 360], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$json[\"0\"][\"name\"]}}"}, {"name": "Latitude", "value": "={{$json[\"0\"][\"latitude\"]}}"}, {"name": "Longitude", "value": "={{$json[\"0\"][\"longitude\"]}}"}, {"name": "Timestamp", "value": "={{$json[\"0\"][\"timestamp\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "MQTT", "type": "n8n-nodes-base.mqtt", "position": [1090, 360], "parameters": {"topic": "iss-position", "options": {}}, "credentials": {"mqtt": "mqtt"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "MQTT", "type": "main", "index": 0}]]}, "Cron": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}