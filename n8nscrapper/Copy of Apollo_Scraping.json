{"name": "Apollo Scraping", "nodes": [{"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"search\": \"landscaping\",\n  \"location\": \"New York, US\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "fb27e4cc-d2cf-4dfd-9b45-7275831c7be9", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/gKvEpKx3i3zlRj2G9/runs?waitForFinish=300", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <YOUR_API_KEY>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"getPersonalEmails\": true,\n    \"getWorkEmails\": true,\n    \"totalRecords\": 2,\n    \"url\": \"https://app.apollo.io/#/people?page=1&contactEmailExcludeCatchAll=true&sortAscending=false&sortByField=%5Bnone%5D&personLocations[]={{ $json.location }}&qOrganizationKeywordTags[]={{ $json.industry_tag }}&includedOrganizationKeywordFields[]=tags&includedOrganizationKeywordFields[]=name\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "1c69222c-4080-4a74-9708-74db66d67e9a", "name": "HTTP Request"}, {"parameters": {"url": "=https://api.apify.com/v2/datasets/{{ $json.data.defaultDatasetId }}/items?format=json", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_API_KEY>"}, {"name": "="}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [660, 0], "id": "9cb5062c-59e5-4e08-be3d-93605f4f4489", "name": "HTTP Request1"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1100, 0], "id": "b25ae559-d76e-409d-b6b5-9f2339d47866", "name": "Aggregate"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4", "mode": "list", "cachedResultName": "AI Agent Web Scraping Results", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"ID": "={{ $json.id }}", "City": "={{ $json.city }}", "URL": "={{ $json.organization.website_url }}", "Name": "={{ $json.employment_history[0].organization_name }}", "Phone": "=Phone: {{ $json.organization.primary_phone.number }}", "Service": "=", "Platform": "Apollo", "LinkedIn": "={{ $json.linkedin_url }}", "Last Name": "={{ $json.last_name }}", "First Name": "={{ $json.first_name }}", "Email": "={{ $json.email }}"}, "matchingColumns": ["ID"], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Service", "displayName": "Service", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "City", "displayName": "City", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Platform", "displayName": "Platform", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "LinkedIn", "displayName": "LinkedIn", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [880, 0], "id": "28d2e980-855b-4d08-a7e9-d80f6dd5776d", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "oHG2DldApzJrCoBY", "name": "Google Sheets account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "e389d681-3bc9-45a5-93a3-693e9e1f2baf", "name": "location", "value": "={{ encodeURIComponent($json.location) }}", "type": "string"}, {"id": "82bd17ab-9f52-47cc-b262-26eeb052cb09", "name": "industry_tag", "value": "={{ $json.search }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 0], "id": "76ad76f1-95fa-45c3-b613-3f27da5591df", "name": "<PERSON>"}], "pinData": {"When Executed by Another Workflow": [{"json": {"search": "landscaping", "location": "New York, US"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bb6592ce-7787-4c70-898c-7220481bc7b6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d2017cc6d1e4b956d269a8123bffa72fb7aaa41ad37a73b7c0fb64c7d0e2edae"}, "id": "vg7ioHzFge25fuAy", "tags": []}