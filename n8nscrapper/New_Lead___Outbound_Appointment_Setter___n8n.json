{"name": "New Lead - Outbound Appointment Setter - n8n", "nodes": [{"parameters": {"content": "## New Lead", "height": 320, "width": 820, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1100, -2100], "id": "d3391e8e-a1cf-4ded-8766-c305b8b93522", "name": "Sticky Note3"}, {"parameters": {"httpMethod": "POST", "path": "19ea7a1a-1d6e-4f7d-8e8f-46f96d009604", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1000, -2000], "id": "f37076ee-9975-443f-8ce7-9a7ca792c7f6", "name": "Webhook2", "webhookId": "19ea7a1a-1d6e-4f7d-8e8f-46f96d009604"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "columns": {"mappingMode": "defineBelow", "value": {"Email": "={{ $json.body['We Appreciate Your Interest! 😊'].email }}", "First Name": "={{ $json.body['We Appreciate Your Interest! 😊'].first_name }}", "Last Name": "={{ $json.body['We Appreciate Your Interest! 😊'].last_name }}", "Phone Number": "={{ $json.body['We Appreciate Your Interest! 😊'].phone_number }}", "Lead Status": "New", "Budget (from form)": "={{ $json.body['What is your budget for this project or service? 👇'] }}", "Interest (from form)": "={{ $json.body['What are you interested in? 👇'] }}", "Client Info (from form)": "={{ $json.body['Tell us briefly about your goals or what you\\'re looking to achieve: 👇'] }}", "Call Status": "Call"}, "matchingColumns": [], "schema": [{"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Lead Status", "displayName": "Lead Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "New", "value": "New"}, {"name": "Active", "value": "Active"}, {"name": "No-show", "value": "No-show"}, {"name": "Ghosted", "value": "Ghosted"}, {"name": "Declined", "value": "Declined"}], "readOnly": false, "removed": false}, {"id": "Budget (from form)", "displayName": "Budget (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Interest (from form)", "displayName": "Interest (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Client Info (from form)", "displayName": "Client Info (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Deal Value", "displayName": "Deal Value", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Call Status", "displayName": "Call Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Call", "value": "Call"}, {"name": "Called", "value": "Called"}], "readOnly": false, "removed": false}, {"id": "Call Transcription", "displayName": "Call Transcription", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Pipeline Status", "displayName": "Pipeline Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Called Booked", "value": "Called Booked"}, {"name": "Follow Up", "value": "Follow Up"}, {"name": "Won", "value": "Won"}, {"name": "Closed", "value": "Closed"}], "readOnly": false, "removed": false}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "AUTO_ID", "displayName": "AUTO_ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Products", "displayName": "Products", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Deal Value (from Products)", "displayName": "Deal Value (from Products)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Knowledge", "displayName": "Knowledge", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Goals (from Knowledge)", "displayName": "Goals (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Dream Outcome  (from Knowledge)", "displayName": "Dream Outcome  (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Blockers (from Knowledge)", "displayName": "Blockers (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Questions (from Knowledge)", "displayName": "Questions (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Objections (from Knowledge)", "displayName": "Objections (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-780, -2000], "id": "92f5a421-60b8-4e8a-80eb-4b6a57856e24", "name": "Add Lead", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"chatId": "1378743444", "text": "=🔥 New Lead 🔥\n\nName: {{ $json.fields['First Name'] }} {{ $json.fields['Last Name'] }}\nEmail: {{ $json.fields.Email }}\nPhone: {{ $json.fields['Phone Number'] }}\n\nInterest: {{ $json.fields['Interest (from form)'] }}\nClient info: {{ $json.fields['Client Info (from form)'] }}\nBudget: {{ $json.fields['Budget (from form)'] }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-560, -2000], "id": "2416da1f-ed51-4755-a525-48a9648c55f0", "name": "Notify (New Lead)", "webhookId": "3a86b766-8c22-4ed8-b5ee-8d6971145dbf", "credentials": {"telegramApi": {"id": "qVlRpUiAXyh5cvNs", "name": "marcusm_assistant_bot"}}}, {"parameters": {"content": "## Auto Call", "height": 280, "width": 820}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1100, -1720], "id": "501eabe7-217e-4aaa-ac20-21337f0dbb9e", "name": "Sticky Note4"}, {"parameters": {"path": "2781bffc-c4bb-4d26-ba10-4c9985b1fa2c", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1000, -1640], "id": "c6a37e1e-b274-4360-a4d0-52ffddb6b5a6", "name": "Webhook3", "webhookId": "2781bffc-c4bb-4d26-ba10-4c9985b1fa2c"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "options": {"view": {"__rl": true, "value": "viwxGbyMZNCfpu64q", "mode": "list", "cachedResultName": "👷 Auto Call - Vapi", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz/viwxGbyMZNCfpu64q"}}}, "id": "9a73e467-b0ea-4a25-94c6-fdb08e6662de", "name": "List Records", "type": "n8n-nodes-base.airtable", "typeVersion": 2, "position": [-780, -1640], "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"method": "POST", "url": "https://api.vapi.ai/call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer c6e7d617-1158-4cb6-be4d-c1ebf344d612"}, {"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"phoneNumberId\": \"11e873f6-340c-4afe-bf2a-38c57ebf6e1b\",\n  \"assistantId\": \"c4741ba6-8337-46e2-9975-3b2a3e85d4af\",\n  \"customer\": {\n    \"number\": \"{{ $json['Phone Number'] }}\"\n  },\n  \"assistantOverrides\": {\n    \"variableValues\": {\n      \"name\": \"{{ $json['First Name'] }}\",\n      \"email\": \"{{ $json.Email }}\",\n      \"interest\": \"{{ $json['Interest (from form)'] }}\",\n      \"notes\": \"{{ $json['Client Info (from form)'] }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-540, -1640], "id": "6a617fbc-a4dd-4e1b-9aa6-107362fb8f96", "name": "HTTP Request"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Find Record').item.json.id }}", "Transcript Summary": "={{ $('Webhook5').item.json.body.message.analysis.summary }}", "Call Status": "Unavailable"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Lead Status", "displayName": "Lead Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "New", "value": "New"}, {"name": "Active", "value": "Active"}, {"name": "No-show", "value": "No-show"}, {"name": "Ghosted", "value": "Ghosted"}, {"name": "Declined", "value": "Declined"}], "readOnly": false, "removed": true}, {"id": "Budget (from form)", "displayName": "Budget (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Interest (from form)", "displayName": "Interest (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Client Info (from form)", "displayName": "Client Info (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Call Status", "displayName": "Call Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Call", "value": "Call"}, {"name": "Booked", "value": "Booked"}, {"name": "Unavailable", "value": "Unavailable"}, {"name": "Follow-Up", "value": "Follow-Up"}, {"name": "Not Interested", "value": "Not Interested"}], "readOnly": false, "removed": false}, {"id": "Transcript Summary", "displayName": "Transcript Summary", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Deal Value", "displayName": "Deal Value", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Pipeline Status", "displayName": "Pipeline Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Called Booked", "value": "Called Booked"}, {"name": "Follow Up", "value": "Follow Up"}, {"name": "Won", "value": "Won"}, {"name": "Closed", "value": "Closed"}], "readOnly": false, "removed": true}, {"id": "AUTO_ID", "displayName": "AUTO_ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Products", "displayName": "Products", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Deal Value (from Products)", "displayName": "Deal Value (from Products)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Knowledge", "displayName": "Knowledge", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Goals (from Knowledge)", "displayName": "Goals (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Dream Outcome  (from Knowledge)", "displayName": "Dream Outcome  (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Blockers (from Knowledge)", "displayName": "Blockers (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Questions (from Knowledge)", "displayName": "Questions (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Objections (from Knowledge)", "displayName": "Objections (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [340, -140], "id": "dc2303c6-bd82-4d7a-9c2f-4eff4ce7b982", "name": "Unavailable", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.content.outcome }}", "rightValue": "Booked", "operator": {"type": "string", "operation": "equals"}, "id": "d81a7656-c33e-4e6c-965e-0f460c8084d0"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Booked"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9a98d19-7211-4a0f-a97c-d6537942dc9a", "leftValue": "={{ $json.message.content.outcome }}", "rightValue": "Unavailable", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "unavailable"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "19781905-a472-4d86-a031-7e43b48f6b76", "leftValue": "={{ $json.message.content.outcome }}", "rightValue": "Not Interested", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "not interested"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f0be3c23-5d74-4375-a7e1-597af66a9e12", "leftValue": "={{ $json.message.content.outcome }}", "rightValue": "Follow-Up", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "follow-up"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [120, -60], "id": "2172f3e1-986e-40e9-9438-1fbe49c2d3f9", "name": "Router2"}, {"parameters": {"httpMethod": "POST", "path": "4a62e8d4-b9d3-464a-8ae0-7550e087d32c", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1000, -940], "id": "31cecb33-54bc-4bb6-8e0c-3d549cac5e5f", "name": "Webhook4", "webhookId": "4a62e8d4-b9d3-464a-8ae0-7550e087d32c"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "filterByFormula": "={Email} = \"{{ $json.body.message.assistant.variableValues.email }}\"", "returnAll": false, "limit": 1, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-760, -40], "id": "b08922bd-0fb4-4afc-9409-c7469c4f58e5", "name": "Find Record", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"assignments": {"assignments": [{"id": "149ea05f-0f62-464c-aacb-f9e377b4d5f9", "name": "username", "value": "marcus-mewett-giu7ew", "type": "string"}, {"id": "55762796-e02a-4fac-913b-3710838abfca", "name": "eventTypeSlug", "value": "consultation", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-780, -940], "id": "b62ceb48-fbf5-4d24-b021-ab1e05bcccb2", "name": "<PERSON>"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Webhook4').item.json.body.message.toolCalls[0].function.name }}", "rightValue": "getOpenSlots", "operator": {"type": "string", "operation": "equals"}, "id": "49d089ba-d540-4574-aff4-3aaa4967f5b8"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Get Open Slots"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c3398cee-3389-4a0b-a0e1-f5ce7b574bd8", "leftValue": "={{ $('Webhook4').item.json.body.message.toolCalls[0].function.name }}", "rightValue": "bookAppointment", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Book Appointment"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-560, -940], "id": "36268387-4222-49bf-ae30-8c7df0c142af", "name": "Switch"}, {"parameters": {"assignments": {"assignments": [{"id": "3b95f60f-7585-41da-9908-a8e17b0c8677", "name": "start", "value": "={{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.requestedAppointment }}", "type": "string"}, {"id": "f8ba04e5-55c3-4004-b2a2-fbdef3a2e5c3", "name": "end", "value": "={{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.requestedAppointment.toDateTime().plus(1, 'days') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-340, -1140], "id": "2b149e24-4ba8-4db0-9a4b-5b3ce8d35417", "name": "Start and End Time"}, {"parameters": {"url": "https://api.cal.com/v2/slots", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "username", "value": "={{ $('Switch').item.json.username }}"}, {"name": "eventTypeSlug", "value": "={{ $('Switch').item.json.eventTypeSlug }}"}, {"name": "start", "value": "={{ $json.start }}"}, {"name": "end", "value": "={{ $json.end }}"}, {"name": "timeZone", "value": "={{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.callerTimezone }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "cal-api-version", "value": "2024-09-04"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-120, -1140], "id": "46a0d52a-3762-4a57-8167-16188c9ce897", "name": "Get Open Slots", "credentials": {"httpHeaderAuth": {"id": "UxZMA2EutkDPJOg4", "name": "Cal.com (marcus/prod)"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0a4e29bb-9a4c-48ae-a341-f562df2e275f", "leftValue": "={{ $json.data }}", "rightValue": "", "operator": {"type": "object", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [120, -1140], "id": "dcb1ffe2-6192-454a-a378-502e317e0136", "name": "Are Slots Available"}, {"parameters": {"respondWith": "json", "responseBody": "={\n    \"results\": [\n        {\n            \"toolCallId\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].id }}\",\n            \"result\": {{ $('Get Open Slots').item.json.data.toJsonString() }}\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [340, -1240], "id": "c0901e0d-c712-4209-9db2-a6491e0c7976", "name": "Slots Available"}, {"parameters": {"respondWith": "json", "responseBody": "={\n    \"results\": [\n        {\n            \"toolCallId\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].id }}\",\n            \"result\": \"There were no open slots, try selecting another day/time.\"\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [340, -1040], "id": "9ae78298-f7c4-42a9-b773-eb278bca5730", "name": "Slots Not Available"}, {"parameters": {"respondWith": "json", "responseBody": "={\n    \"results\": [\n        {\n            \"toolCallId\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].id }}\",\n            \"result\": \"Booking successful.\"\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-120, -940], "id": "cb8a088e-b427-446c-831a-b58a5cdb2f2b", "name": "Booking Success"}, {"parameters": {"respondWith": "json", "responseBody": "={\n    \"results\": [\n        {\n            \"toolCallId\": \"{{ $('Webhook5').item.json.body.message.toolCalls[0].id }}\",\n            \"result\": \"Booking not successful. Try another day or time.\"\n        }\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-120, -740], "id": "e0885ca3-6e7f-40a4-9024-3c53fa19807d", "name": "Booking Not Successful"}, {"parameters": {"method": "POST", "url": "https://api.cal.com/v2/bookings", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "cal-api-version", "value": "2024-08-13"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"attendee\": {\n    \"language\": \"en\",\n    \"name\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.name }}\",\n    \"timeZone\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.callerTimezone }}\",\n    \"email\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.email }}\"\n  },\n  \"start\": \"{{ $('Webhook4').item.json.body.message.toolCalls[0].function.arguments.requestedAppointment }}\",\n  \"eventTypeSlug\": \"{{ $('Edit Fields').item.json.eventTypeSlug }}\",\n  \"username\": \"{{ $('Edit Fields').item.json.username }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-340, -840], "id": "ee7197c2-6fa0-49b8-a0b7-7b36375439a4", "name": "Book Appointment", "credentials": {"httpHeaderAuth": {"id": "UxZMA2EutkDPJOg4", "name": "Cal.com (marcus/prod)"}}, "onError": "continueErrorOutput"}, {"parameters": {"content": "## Vapi > AirTable", "height": 960, "width": 1920, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1100, -440], "id": "6e8a7dcd-72b8-4d79-b202-102d6cdf645c", "name": "Sticky Note5"}, {"parameters": {"content": "## Appointment Setter", "height": 880, "width": 1680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1100, -1380], "id": "addfd560-f061-4552-a59a-19a00bde75ba", "name": "Sticky Note6"}, {"parameters": {"httpMethod": "POST", "path": "a495a81d-f601-47e9-ab0e-9c83260f179d", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-980, -40], "id": "edd66f5b-5009-42a8-a311-9573a0eaefd7", "name": "Webhook5", "webhookId": "a495a81d-f601-47e9-ab0e-9c83260f179d"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "columns": {"mappingMode": "defineBelow", "value": {"Pipeline Status": "Called Booked", "id": "={{ $json.id }}", "Lead Status": "Active"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Lead Status", "displayName": "Lead Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "New", "value": "New"}, {"name": "Active", "value": "Active"}, {"name": "No-show", "value": "No-show"}, {"name": "Ghosted", "value": "Ghosted"}, {"name": "Declined", "value": "Declined"}], "readOnly": false, "removed": false}, {"id": "Budget (from form)", "displayName": "Budget (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Interest (from form)", "displayName": "Interest (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Client Info (from form)", "displayName": "Client Info (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "AI Call Status", "displayName": "AI Call Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Call", "value": "Call"}, {"name": "Booked", "value": "Booked"}, {"name": "Unavailable", "value": "Unavailable"}, {"name": "Follow-Up", "value": "Follow-Up"}, {"name": "Not Interested", "value": "Not Interested"}], "readOnly": false, "removed": true}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Deal Value", "displayName": "Deal Value", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Pipeline Status", "displayName": "Pipeline Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Called Booked", "value": "Called Booked"}, {"name": "Follow Up", "value": "Follow Up"}, {"name": "Won", "value": "Won"}, {"name": "Closed", "value": "Closed"}], "readOnly": false, "removed": false}, {"id": "AUTO_ID", "displayName": "AUTO_ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Products", "displayName": "Products", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Deal Value (from Products)", "displayName": "Deal Value (from Products)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Knowledge", "displayName": "Knowledge", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Goals (from Knowledge)", "displayName": "Goals (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Dream Outcome  (from Knowledge)", "displayName": "Dream Outcome  (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Blockers (from Knowledge)", "displayName": "Blockers (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Questions (from Knowledge)", "displayName": "Questions (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Objections (from Knowledge)", "displayName": "Objections (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Call Transcription", "displayName": "Call Transcription", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "AI Call Transcript Summary", "displayName": "AI Call Transcript Summary", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [560, -340], "id": "2043ee5e-1fcd-4c59-8036-6e8ae8cd1f1a", "name": "Active Lead", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "4c100b23-943c-48a7-ac14-c49951d2ca48", "leftValue": "={{ $('Webhook5').item.json.body.message.analysis.successEvaluation }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-540, -40], "id": "68ca69bf-31b9-4ba5-b78c-205128c5768f", "name": "Filter"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Find Record').item.json.id }}", "Transcript Summary": "={{ $('Webhook5').item.json.body.message.analysis.summary }}", "Call Status": "Not Interested"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Lead Status", "displayName": "Lead Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "New", "value": "New"}, {"name": "Active", "value": "Active"}, {"name": "No-show", "value": "No-show"}, {"name": "Ghosted", "value": "Ghosted"}, {"name": "Declined", "value": "Declined"}], "readOnly": false, "removed": true}, {"id": "Budget (from form)", "displayName": "Budget (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Interest (from form)", "displayName": "Interest (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Client Info (from form)", "displayName": "Client Info (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Call Status", "displayName": "Call Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Call", "value": "Call"}, {"name": "Booked", "value": "Booked"}, {"name": "Unavailable", "value": "Unavailable"}, {"name": "Follow-Up", "value": "Follow-Up"}, {"name": "Not Interested", "value": "Not Interested"}], "readOnly": false, "removed": false}, {"id": "Transcript Summary", "displayName": "Transcript Summary", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Deal Value", "displayName": "Deal Value", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Pipeline Status", "displayName": "Pipeline Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Called Booked", "value": "Called Booked"}, {"name": "Follow Up", "value": "Follow Up"}, {"name": "Won", "value": "Won"}, {"name": "Closed", "value": "Closed"}], "readOnly": false, "removed": true}, {"id": "AUTO_ID", "displayName": "AUTO_ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Products", "displayName": "Products", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Deal Value (from Products)", "displayName": "Deal Value (from Products)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Knowledge", "displayName": "Knowledge", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Goals (from Knowledge)", "displayName": "Goals (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Dream Outcome  (from Knowledge)", "displayName": "Dream Outcome  (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Blockers (from Knowledge)", "displayName": "Blockers (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Questions (from Knowledge)", "displayName": "Questions (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Objections (from Knowledge)", "displayName": "Objections (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [340, 60], "id": "edc7c5b6-a3a8-4a8c-928e-6661ac4330f6", "name": "Not Interested", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Find Record').item.json.id }}", "Transcript Summary": "={{ $('Webhook5').item.json.body.message.analysis.summary }}", "Call Status": "Follow-Up"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Lead Status", "displayName": "Lead Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "New", "value": "New"}, {"name": "Active", "value": "Active"}, {"name": "No-show", "value": "No-show"}, {"name": "Ghosted", "value": "Ghosted"}, {"name": "Declined", "value": "Declined"}], "readOnly": false, "removed": true}, {"id": "Budget (from form)", "displayName": "Budget (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Interest (from form)", "displayName": "Interest (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Client Info (from form)", "displayName": "Client Info (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Call Status", "displayName": "Call Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Call", "value": "Call"}, {"name": "Booked", "value": "Booked"}, {"name": "Unavailable", "value": "Unavailable"}, {"name": "Follow-Up", "value": "Follow-Up"}, {"name": "Not Interested", "value": "Not Interested"}], "readOnly": false, "removed": false}, {"id": "Transcript Summary", "displayName": "Transcript Summary", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Deal Value", "displayName": "Deal Value", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Pipeline Status", "displayName": "Pipeline Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Called Booked", "value": "Called Booked"}, {"name": "Follow Up", "value": "Follow Up"}, {"name": "Won", "value": "Won"}, {"name": "Closed", "value": "Closed"}], "readOnly": false, "removed": true}, {"id": "AUTO_ID", "displayName": "AUTO_ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Products", "displayName": "Products", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Deal Value (from Products)", "displayName": "Deal Value (from Products)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Knowledge", "displayName": "Knowledge", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Goals (from Knowledge)", "displayName": "Goals (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Dream Outcome  (from Knowledge)", "displayName": "Dream Outcome  (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Blockers (from Knowledge)", "displayName": "Blockers (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Questions (from Knowledge)", "displayName": "Questions (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Objections (from Knowledge)", "displayName": "Objections (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [340, 260], "id": "ab5d7a4a-54a2-463b-bc9a-5282f1dc2f8c", "name": "Follow-Up", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"chatId": "1378743444", "text": "=📲 Follow-Up 📲\n\nName: \nEmail: \nPhone:\n\nInterest: \nClient info: \nBudget: ", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [560, 260], "id": "c32c7ea4-8f36-451c-916a-7c370368c77c", "name": "Notify (Follow-Up)", "webhookId": "3a86b766-8c22-4ed8-b5ee-8d6971145dbf", "credentials": {"telegramApi": {"id": "qVlRpUiAXyh5cvNs", "name": "marcusm_assistant_bot"}}, "disabled": true}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appmZe7DVdEwpk1SR", "mode": "list", "cachedResultName": "CRM v1", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR"}, "table": {"__rl": true, "value": "tblbr3IxUema201rz", "mode": "list", "cachedResultName": "Leads", "cachedResultUrl": "https://airtable.com/appmZe7DVdEwpk1SR/tblbr3IxUema201rz"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Find Record').item.json.id }}", "Transcript Summary": "={{ $('Webhook5').item.json.body.message.analysis.summary }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Lead Status", "displayName": "Lead Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "New", "value": "New"}, {"name": "Active", "value": "Active"}, {"name": "No-show", "value": "No-show"}, {"name": "Ghosted", "value": "Ghosted"}, {"name": "Declined", "value": "Declined"}], "readOnly": false, "removed": true}, {"id": "Budget (from form)", "displayName": "Budget (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Interest (from form)", "displayName": "Interest (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Client Info (from form)", "displayName": "Client Info (from form)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Call Status", "displayName": "Call Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Call", "value": "Call"}, {"name": "Booked", "value": "Booked"}, {"name": "Unavailable", "value": "Unavailable"}, {"name": "Follow-Up", "value": "Follow-Up"}, {"name": "Not Interested", "value": "Not Interested"}], "readOnly": false, "removed": false}, {"id": "Transcript Summary", "displayName": "Transcript Summary", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Deal Value", "displayName": "Deal Value", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Pipeline Status", "displayName": "Pipeline Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Called Booked", "value": "Called Booked"}, {"name": "Follow Up", "value": "Follow Up"}, {"name": "Won", "value": "Won"}, {"name": "Closed", "value": "Closed"}], "readOnly": false, "removed": true}, {"id": "AUTO_ID", "displayName": "AUTO_ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Products", "displayName": "Products", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Deal Value (from Products)", "displayName": "Deal Value (from Products)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Knowledge", "displayName": "Knowledge", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Goals (from Knowledge)", "displayName": "Goals (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Dream Outcome  (from Knowledge)", "displayName": "Dream Outcome  (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Blockers (from Knowledge)", "displayName": "Blockers (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Questions (from Knowledge)", "displayName": "Questions (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Objections (from Knowledge)", "displayName": "Objections (from Knowledge)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [340, -340], "id": "eb8bd8f1-5300-4d44-a9ce-4f77545e8247", "name": "Booked", "credentials": {"airtableTokenApi": {"id": "0XdVTKus5ynQbHY0", "name": "Airtable - <EMAIL>"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "=You are an expert call analysis assistant. Your task is to read a call summary and return one of the following outcomes based on the context of the conversation:\n\nBooked – An appointment has been booked.\n\nUnavailable – The lead said is currently unavailable.\n\nNot Interested – The lead clearly states they are not interested in the service or offering.\n\nFollow-Up – The lead expressed interest but needs more information, wants to think about it, or agreed to a future follow-up call.\n\nReturn only one of the following words as your answer:\nBooked, Unavailable, Not Interested, or Follow-Up.", "role": "system"}, {"content": "=Read the call summary below and determine the outcome:\n\n{{ $('Webhook5').item.json.body.message.analysis.summary }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-300, -40], "id": "0c3f51a4-0e49-4687-9611-ef7f60c269b2", "name": "OpenAI", "credentials": {"openAiApi": {"id": "yFBpQzkbIvosJRgs", "name": "OpenAI - mmmar<PERSON><EMAIL>"}}}], "pinData": {}, "connections": {"Webhook2": {"main": [[{"node": "Add Lead", "type": "main", "index": 0}]]}, "Add Lead": {"main": [[{"node": "Notify (New Lead)", "type": "main", "index": 0}]]}, "Webhook3": {"main": [[{"node": "List Records", "type": "main", "index": 0}]]}, "List Records": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Router2": {"main": [[{"node": "Booked", "type": "main", "index": 0}], [{"node": "Unavailable", "type": "main", "index": 0}], [{"node": "Not Interested", "type": "main", "index": 0}], [{"node": "Follow-Up", "type": "main", "index": 0}]]}, "Webhook4": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Find Record": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Start and End Time", "type": "main", "index": 0}], [{"node": "Book Appointment", "type": "main", "index": 0}]]}, "Start and End Time": {"main": [[{"node": "Get Open Slots", "type": "main", "index": 0}]]}, "Get Open Slots": {"main": [[{"node": "Are Slots Available", "type": "main", "index": 0}]]}, "Are Slots Available": {"main": [[{"node": "Slots Available", "type": "main", "index": 0}], [{"node": "Slots Not Available", "type": "main", "index": 0}]]}, "Book Appointment": {"main": [[{"node": "Booking Success", "type": "main", "index": 0}], [{"node": "Booking Not Successful", "type": "main", "index": 0}]]}, "Webhook5": {"main": [[{"node": "Find Record", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Follow-Up": {"main": [[{"node": "Notify (Follow-Up)", "type": "main", "index": 0}]]}, "Booked": {"main": [[{"node": "Active Lead", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Router2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "544af88e-1249-402c-a307-67a3ccbd57fb", "meta": {"instanceId": "069cc30b71ab087be24d51218b9d3ef9b2a96a675589ff764e6e5a5b9b9cf572"}, "id": "uTwfc3hgB5P3t3si", "tags": []}