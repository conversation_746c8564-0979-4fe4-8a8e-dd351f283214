{"name": "book app", "nodes": [{"parameters": {"httpMethod": "POST", "path": "45a19d56-3c9d-4baf-9a9e-b2fd418af848", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [720, 0], "id": "f2b8f727-6f3b-46a0-811a-2dc848b16259", "name": "Webhook", "webhookId": "45a19d56-3c9d-4baf-9a9e-b2fd418af848"}, {"parameters": {"promptType": "define", "text": "=Your Job is to book google calendar events. You will be given a time to book and you must use that time to create a event. Use the 'calender tool to book the appoitment with the customers deatails. The appointment duration will be 1 hour long. ensure that you add 1 hour to the start tiem to get the end date.\n\nCustomer deatils: \n\nBooking time: {{ $json.body.message.toolCalls[0].function.arguments.Time }}\n\nOut put results: \n\nSucessful booking message e.g \"The meeting at june 26th from 10am to 11am has been schedualedl, thank you for booking\" \n\nensure your output is conversational & natrual & not robotic. \n\n\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1100, -40], "id": "b168b0eb-6ff8-4cda-8a4d-f825e5a13061", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [900, 200], "id": "061bb0b2-c1ff-4a84-a086-211d468f1779", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "sYGWHRTm0aBGym7M", "name": "OpenAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"results\": [\n    {\n      \"toolCallId\": \"{{ $('Webhook').item.json.body.message.toolCalls[0].id }}\",\n      \"result\": \"{{ $json.output }}\"\n    }\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.3, "position": [1480, 0], "id": "61c2e5ec-10c7-4fbb-92d4-8b3871613627", "name": "Respond to Webhook"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"attendees": ["={{ $('Webhook').item.json.body.message.toolCalls[0].function.arguments.email }}"], "description": "=Details:\nName: {{ $json.body.message.toolCalls[0].function.arguments.full_name }} \nTime: {{ $('Webhook').item.json.body.message.toolCalls[0].function.arguments.Time }}\nEmail: {{ $json.body.message.toolCalls[0].function.arguments.email }} \nProperty Adress: {{ $('Webhook').item.json.body.message.toolCalls[0].function.arguments.property_address }}", "summary": "= New Property Appointment for {{ $json.body.message.toolCalls[0].function.arguments.full_name }} at {{ $('Webhook').item.json.body.message.toolCalls[0].function.arguments.Time }},{{ $('Webhook').item.json.body.message.toolCalls[0].function.arguments.property_address }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [1180, 200], "id": "259b9a50-fffa-4dac-828a-4ade052a0779", "name": "Create an event in Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "nGN7h9SIZXxERx6E", "name": "Google Calendar account"}}}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Create an event in Google Calendar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "33599aa2-3cab-448c-a12b-088922b3fe24", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5e83950c16f424931a11129d242bc57414727cd7495fa9f2a8ca68a3f953658d"}, "id": "Kufdhosawih3rGAD", "tags": []}