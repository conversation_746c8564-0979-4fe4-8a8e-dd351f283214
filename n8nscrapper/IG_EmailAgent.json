{"name": "IG_EmailAgent", "nodes": [{"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "61f05603-8920-461c-9f9d-ba41e142e895", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-1900, 0], "webhookId": "e010f207-11f9-4cdd-9155-7ec300f39372", "typeVersion": 1, "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "290c1f86-3df2-459c-aa7a-e23a1208f7b4", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [-1420, 80], "typeVersion": 1.2, "webhookId": "c3ab6823-3c09-4918-91d3-714528a68c1b", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}, "options": {}}, "id": "********-2941-435d-883c-cd11b4df0ba1", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [-1040, -60], "typeVersion": 3.4}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "00a6121a-7c9e-4ab6-8850-f341cee89087", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [-1420, -220], "typeVersion": 1.2, "webhookId": "73cd5a0e-649a-498e-b45f-a69ecc7e83e9", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "", "temperature": 0.7}}, "id": "7561c40c-f7d7-49a1-9497-9d8634b079f6", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [-1240, 80], "typeVersion": 1.5, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "1911da09-ed0c-493a-b55b-56a8e421dff2", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [-1660, -220], "typeVersion": 1.2, "webhookId": "3e16821f-e31c-4ac3-b00a-3269d7b3a4d9", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/", "id": "d68f5557-eaf3-430c-b3e0-4c0bbb147c45"}]}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "2716f152-9107-4063-a54d-904cffd4f3df", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [-1620, 0], "typeVersion": 3.2}, {"parameters": {"promptType": "define", "text": "={{ $json.CombinedMessage }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-800, -60], "id": "e996ff54-9ae5-46f5-8842-64fa8d7503a3", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-800, 100], "id": "0975b520-80ba-48f1-9f87-dd5d1d0a16e0", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "7gFuLBYAdUiSiCO5", "name": "OpenRouter account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Listen for incoming events').item.json.message.chat.id }}", "contextWindowLength": 25}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-700, 100], "id": "eea5ae71-3d74-4182-9d60-c6f43d18a8d3", "name": "Simple Memory"}, {"parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-720, 260], "id": "e4f42d68-2a81-4b4e-8d12-6bfbd693af17", "name": "Gmail", "webhookId": "c7941136-3e61-4d37-8b3c-1ef68a336dde", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', `message id`, 'string') }}", "labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', `label name or ids`, 'string') }}"}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-620, 260], "id": "eeafa5a3-1432-4bcf-b40b-82495bd2bdf5", "name": "Gmail1", "webhookId": "c7941136-3e61-4d37-8b3c-1ef68a336dde", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"operation": "get", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', `message id`, 'string') }}"}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-520, 260], "id": "0edfd6f9-7fd5-4711-ad14-cf017e907e2f", "name": "Gmail2", "webhookId": "c7941136-3e61-4d37-8b3c-1ef68a336dde", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"operation": "getAll", "filters": {"includeSpamTrash": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Include_Spam_and_Trash', ``, 'boolean') }}", "labelIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Label_Names_or_IDs', ``, 'string') }}", "q": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Search', ``, 'string') }}", "receivedAfter": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_After', ``, 'string') }}", "receivedBefore": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Received_Before', ``, 'string') }}", "sender": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Sender', ``, 'string') }}"}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-720, 380], "id": "********-f636-48ed-aa80-828224002d56", "name": "Gmail3", "webhookId": "c7941136-3e61-4d37-8b3c-1ef68a336dde", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"operation": "reply", "messageId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_ID', `message id`, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', `content`, 'string') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-620, 380], "id": "f84d5019-b52f-40ac-a4ee-065d21e38dab", "name": "Gmail4", "webhookId": "c7941136-3e61-4d37-8b3c-1ef68a336dde", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"resource": "label", "returnAll": true}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [-520, 380], "id": "d9b1e3e6-b635-40b6-8256-ad3826877bd6", "name": "Gmail5", "webhookId": "c7941136-3e61-4d37-8b3c-1ef68a336dde", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"chatId": "={{ $('Listen for incoming events').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-400, -60], "id": "8b4baf0c-b84b-474e-b405-bc6ac6689eb9", "name": "Telegram1", "webhookId": "af3d2e5c-6e31-4881-b715-dc2f3c9db5d2", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}], "pinData": {}, "connections": {"Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Gmail": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Gmail1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Gmail2": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Gmail3": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Gmail4": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Gmail5": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f9b3a0f2-fa36-4384-b455-e9b6fdbffd3c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "KsLKdlt5k5yHcy1S", "tags": []}