{"name": "MCP Client", "nodes": [{"parameters": {"options": {"allowFileUploads": true}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1640, -300], "id": "0f7d301a-e3de-40ca-8f1f-f0c3bbd871fe", "name": "When chat message received", "webhookId": "33f0fd30-3693-4038-a163-d365f730f806"}, {"parameters": {"sseEndpoint": "", "include": "=all"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [-960, -20], "id": "0e2207ec-f56e-4080-a99e-652dda2a6392", "name": "SendtoMCPServer1"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-1140, -300], "id": "c430b91e-302e-4251-b9ba-a09222b480e3", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1240, -20], "id": "887e74e3-3044-46b6-bdec-2b921d7594a0", "name": "AI for client", "credentials": {"openAiApi": {"id": "AxcwYLkUt9aueZ9i", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-1100, -20], "id": "3c21e793-43be-4c5d-85d9-590a8ed6eb95", "name": "Simple Memory"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "SendtoMCPServer1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI for client": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "2575ad1b-5834-4194-af8d-6d7d1714922d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f21f53385ae396fcb0c1b69950e1ec16f2dcc4ddca34a170466835249ec1c42c"}, "id": "nTTpJNb5mZymTuiP", "tags": []}