{"name": "Web Audit & Hyper-personalised Outreach", "nodes": [{"parameters": {"amount": 25}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [960, -960], "id": "cdb37cef-c0da-4e12-8458-380def161a89", "name": "Wait1", "webhookId": "55c3dc88-2797-40a4-9370-d69adcd1eede"}, {"parameters": {"documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "filtersUI": {"values": [{"lookupColumn": "Status"}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-500, -740], "id": "e2c3fc6a-0f01-4190-ad21-b4119177d887", "name": "Get New Rows", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"jsCode": "return [items[0]];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-500, -1160], "id": "be9e4fa7-917a-4b65-9a1a-8e1db4849823", "name": "FirstRow"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "88f0524b-2dd2-4a50-8776-9d305498776e", "leftValue": "={{ $json.Email }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-500, -880], "id": "e11c7859-127f-4577-87f2-7f754fe921b1", "name": "If Email is not Empty"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "Status": "Email doesn't exist"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-500, -600], "id": "a8a79ba6-4222-4be9-be1d-44e709362fc2", "name": "Update Sheet (email doesn't exist)", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "463f92c5-e786-4bdc-bc8c-21781e683325", "leftValue": "={{ $json.Email }}", "rightValue": "<EMAIL>", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-280, -740], "id": "61de41bb-4a47-4e97-8929-ab0b6e2002d6", "name": "If Email is not BokaDirect's support email"}, {"parameters": {"url": "={{ $json['BokaDirect Profile'] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-60, -1160], "id": "********-3c3b-45aa-85f3-0faffc30f40c", "name": "BokaDirect Profile URL Request"}, {"parameters": {"jsCode": "const html = $('BokaDirect Profile URL Request').first().json.data; // Replace with your actual input\n\n// Regex to check for <div class=\"w-full\"> with any content inside, non-greedy\nconst regex = /<div\\s+class=[\"']w-full[\"'][^>]*>[\\s\\S]*?<\\/div>/i;\n\nconst found = regex.test(html);\n\nreturn {\n  hasWFullDiv: found\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, -880], "id": "e90d99f2-3ce9-4036-9dae-551cca1bafbc", "name": "ProfilePage or Redirected"}, {"parameters": {"jsCode": "const html = $('BokaDirect Profile URL Request').first().json.data; // Replace with actual HTML input\n\n// Regex to find an email address (basic pattern)\nconst emailRegex = /[\\w.+-]+@[\\w-]+\\.[\\w.-]+/g;\n\nconst match = html.match(emailRegex);\n\nreturn {\n  emailFound: !!match,\n  email: match ? match[0] : null\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, -1160], "id": "4c956598-f129-45ee-bafe-db314f49caaa", "name": "Get Email"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "Status": "URL Redirected"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-60, -320], "id": "578505fd-520b-4024-a3dc-3d6c07b936ba", "name": "Update Sheet URL redirected", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "You're a helpful, intelligent web scraping assistant.", "role": "assistant"}, {"content": "Tell me about DecisionMakerName using the StaffList and email scrape below. Use the following JSON format.\n\n{\"DecisionMakerName\":\"\"}\n\nNOTE: Donot add variable name as json for given output. Only give output in curly brackets {given format}. For DecisionMakerName, check the name and role in given data below in this format: name1, role1; name2, role2; ....\nIf 1 or more than 1 staff members, check who's the decision maker from their roles and return their name only. \n\nAlway prioritize this:\nCheck if given email has name from the given staff list, then consider that staff a decision maker."}, {"content": "=StaffList:  {{ $json.staff_list }}\nEmail: {{ $('FirstRow').item.json.Email }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-60, -1000], "id": "2d1d36ff-c166-4f78-8728-8f2caa49205c", "name": "DecisionMaker Name", "credentials": {"openAiApi": {"id": "sSh5op9qG8sgh726", "name": "OpenAi account ZZ"}}}, {"parameters": {"jsCode": "// Get HTML content from previous node\nconst htmlContent = $('BokaDirect Profile URL Request').first().json.data;\n\n// Basic HTML entity decoder\nfunction decodeHtmlEntities(str) {\n  const entities = {\n    '&amp;': '&',\n    '&lt;': '<',\n    '&gt;': '>',\n    '&quot;': '\"',\n    '&#39;': \"'\",\n    '&apos;': \"'\",\n    '&nbsp;': ' ',\n  };\n  return str.replace(/&[a-zA-Z0-9#]+;/g, (entity) => entities[entity] || entity);\n}\n\n// Base64 decoding function\nfunction decodeBase64(str) {\n  return decodeURIComponent(\n    Array.prototype.map.call(atob(str), c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join('')\n  );\n}\n\n// Regex patterns\nconst nameRegex = /<h1.*?>(.*?)<\\/h1>/i;\nconst descriptionRegex = /<div id=\"welcomeText\".*?>(.*?)<\\/div>/is;\nconst staffNameRegex = /<span class=\" block font-semibold\">\\s*(?:<div[^>]*>.*?<\\/div>\\s*)*([^<]+)\\s*<\\/span>/g;\nconst staffRoleRegex = /<span class=\"text-black-600 text-sm\">([^<]+)<\\/span>/g;\nconst urlRegex = /<li[^>]*class=\"[^\"]*border-black-100[^\"]*\"[^>]*>[\\s\\S]*?<a[^>]*href=\"([^\"]+)\"[^>]*>/i;\nconst phoneRegex = /<meta data-react-helmet=\"true\" name=\"business:contact_data:phone_number\" content=\"(.*?)\"\\s*\\/>/i;\nconst emailImageRegex = /<img[^>]+src=\"data:image\\/png;base64,([^\"]+)\"[^>]*>/i;\n\n// Extract business name\nconst nameMatch = htmlContent.match(nameRegex);\nconst businessName = nameMatch ? decodeHtmlEntities(nameMatch[1].trim()) : null;\n\n// Extract business description\nconst descriptionMatch = htmlContent.match(descriptionRegex);\nconst businessDescription = descriptionMatch ? decodeHtmlEntities(descriptionMatch[1].trim()) : null;\n\n// Extract staff names\nconst names = [];\nlet nameMatchLoop;\nwhile ((nameMatchLoop = staffNameRegex.exec(htmlContent)) !== null) {\n  names.push(decodeHtmlEntities(nameMatchLoop[1].trim()));\n}\n\n// Extract staff roles\nconst roles = [];\nlet roleMatchLoop;\nwhile ((roleMatchLoop = staffRoleRegex.exec(htmlContent)) !== null) {\n  roles.push(decodeHtmlEntities(roleMatchLoop[1].trim()));\n}\n\n// Combine staff names and roles\nconst staffList = names.map((name, i) => {\n  const role = roles[i] || 'Unknown';\n  return `${name}, ${role}`;\n}).join('; ');\n\n// Extract URL\nconst urlMatch = htmlContent.match(urlRegex);\nconst url = urlMatch ? decodeHtmlEntities(urlMatch[1].trim()) : null;\n\n// Extract phone number\nconst phoneMatch = htmlContent.match(phoneRegex);\nconst phone = phoneMatch ? phoneMatch[1].trim() : null;\n\n// Extract email image (Base64)\nconst emailImageMatch = htmlContent.match(emailImageRegex);\nlet email = null;\n\nif (emailImageMatch) {\n  // Decode Base64 string and extract the email content\n  const base64String = emailImageMatch[1].trim();\n  const decodedEmailContent = decodeBase64(base64String);\n\n  // Assuming decoded content might contain email-like text (this can be further refined as needed)\n  // You can try a simple regex for extracting an email\n  const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/i;\n  const emailMatch = decodedEmailContent.match(emailRegex);\n\n  if (emailMatch) {\n    email = emailMatch[1];\n  } else {\n    email = \"Decoded content doesn't contain a valid email\";\n  }\n} else {\n  email = \"No Base64 image email found\";\n}\n\nconst pBlockRegex = /<p class=\"cursor-pointer\">([\\s\\S]*?)<\\/p>/;\nconst spanRegex = /<span>(.*?)<\\/span>/g;\n\nconst pMatch = htmlContent.match(pBlockRegex);\nlet address = '';\n\nif (pMatch) {\n  const spanContent = [...pMatch[1].matchAll(spanRegex)].map(m => m[1]);\n  address = spanContent.join(', ');\n}\n\n// Decode HTML entities (like &amp; to &)\nconst txt = decodeHtmlEntities(address)\n\n// Return structured result\nreturn [\n  {\n    json: {\n      business_name: businessName,\n      business_description: businessDescription,\n      staff_list: staffList,\n      url: url,\n      phone: phone,\n      email: email,\n      location: txt\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, -880], "id": "ade305e5-40df-4a53-8e72-e092d796b13e", "name": "Business Details"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "991cacdd-b64e-4e72-8d36-70d1aeee738b", "leftValue": "={{ $('ProfilePage or Redirected').item.json.hasWFullDiv }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-60, -740], "id": "f56c3098-b052-4443-9385-ea363b6986bc", "name": "If ProfilePage or not"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "22599d7c-3e5d-4e6b-980e-5c81edd3edec", "leftValue": "={{ $('Business Details').item.json.url }}", "rightValue": "/", "operator": {"type": "string", "operation": "notStartsWith"}}, {"id": "0f8bc517-b7a4-41f6-9f81-46c99d327f16", "leftValue": "={{ $('Business Details').item.json.url }}", "rightValue": "none", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "eaef4953-4c8b-4402-92c1-a7d07ae508c1", "leftValue": "={{ $('Business Details').item.json.url }}", "rightValue": "instagram", "operator": {"type": "string", "operation": "notContains"}}, {"id": "8f2fbedf-23fb-4638-8e99-f3432fe2fe54", "leftValue": "={{ $('Business Details').item.json.url }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-60, -600], "id": "1ddc94ac-3b87-4e37-a19e-888f27bfed9a", "name": "If website exist"}, {"parameters": {"jsCode": "// List of keywords that indicate personal/free email providers\nconst freeEmailKeywords = [\n  \"gmail\", \"yahoo\", \"hotmail\", \"outlook\", \"aol\",\n  \"icloud\", \"live\", \"protonmail\", \"msn\", \"mail\", \n  \"zoho\", \"yandex\", \"gmx\"\n];\n\nreturn items.map(item => {\n  const email = $('FirstRow').first().json.Email || \"\";\n  const domain = email.split(\"@\")[1]?.toLowerCase() || \"\";\n\n  const isPersonal = freeEmailKeywords.some(keyword => domain.includes(keyword));\n  const isProfessional = !isPersonal;\n\n  // Extract domain only if it's a professional email\n  const result = {\n    ...item.json,\n    isProfessional,\n    emailType: isProfessional ? \"professional\" : \"personal\"\n  };\n\n  if (isProfessional) {\n    result.domain = domain;\n  }\n\n  return { json: result };\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, -740], "id": "1065d310-0680-432f-a3d4-3297e1894e0e", "name": "Professional Email or Personal"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "96bf5921-7ef0-41a8-ac42-37b50fbb1e57", "leftValue": "={{ $json.emailType }}", "rightValue": "professional", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [80, -600], "id": "1ee90a61-2740-470e-89b6-a365389bb9ef", "name": "Professional or Not"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "URL": "={{ $('Business Details').item.json.url }}", "Business Name": "={{ $('Business Details').item.json.business_name }}", "StaffList": "={{ $('Business Details').item.json.staff_list }}", "Description": "={{ $('Business Details').item.json.business_description }}", "Phone": "={{ $('Business Details').item.json.phone }}", "Location": "={{ $('Business Details').item.json.location }}", "Status": "Website not found", "Decision maker name": "={{ $('DecisionMaker Name').item.json.message.content.DecisionMakerName }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [-60, -160], "id": "d1fdebea-3f59-4a41-b43c-bad06967014e", "name": "Update Website doesn't exist", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "65d5a8d6-e8d8-4500-abff-bd5517f728ae", "name": "URL", "value": "=https://www.{{ $json.domain }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, -460], "id": "c3a729e4-b1b7-44af-abf4-d2068f4e3d66", "name": "Set URL from Professional Email"}, {"parameters": {"assignments": {"assignments": [{"id": "e0dd80bd-1a37-4c11-b808-139b529eca41", "name": "URL", "value": "={{ $('Business Details').item.json.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-60, -460], "id": "bbecdab2-c630-4c47-b6cf-0db6f5267246", "name": "Set URL"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "URL": "={{ $json.URL }}", "Business Name": "={{ $('Business Details').item.json.business_name }}", "Description": "={{ $('Business Details').item.json.business_description }}", "StaffList": "={{ $('Business Details').item.json.staff_list }}", "Phone": "={{ $('Business Details').item.json.phone }}", "Decision maker name": "={{ $('DecisionMaker Name').item.json.message.content.DecisionMakerName }}", "Location": "={{ $('Business Details').item.json.location }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [80, -320], "id": "a45cece2-fd05-45c8-b5ba-1fe273f7541d", "name": "Update Scraped Business Details", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"url": "={{ $json.URL }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [320, -740], "id": "908c613a-1c27-460f-9001-e0f5ab234579", "name": "Business Website Request", "onError": "continueErrorOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=We scraped a business website but the site returned an error.\nWe collected:\nThe error type (e.g., 404 Not Found, 500 Server Error, SSL Error, timeout)\n\n\nBasic context (business name, location, what they do)\n\n\nYour task:\n Generate a personalized icebreaker for a cold outreach email, pitching web repair, redesign, and performance/SEO improvements. Make it feel researched and human-written. Always output as a JSON below:\n{\n  \"content\": \"Hey [Team companyShort or FirstName], I tried visiting your site but it looks like it's returning a [error_type] error right now. That kind of issue can quietly block new leads, affect your SEO, or just make the business feel offline to potential customers.\\n\\nFigured you’d want to know, since it’s likely costing traffic and trust. I’ve worked on 500+ web projects over the last 8 years—fixing things like this fast, and often turning them into a chance to improve performance, ranking, and UX.\\n\\nAlong with development, I also bring SEO and AI expertise to help businesses strengthen their online presence and drive more qualified traffic. If you're open to it, happy to jump on a quick call and walk through what I’d recommend for getting your site up and performing at its best.\\n\\nNo pressure—just reaching out since I already ran the check. Let me know if you want to see some sites I’ve helped turn around.\"\n}\n\nRules Recap (for this error-based version):\n-Clearly mention the error type (e.g., 404, timeout, SSL error)\n-Highlight the cost of downtime: missed leads, SEO loss, trust issues\n-Keep tone helpful, not critical\n-Include your track record (1000+ web projects, 8 years)\n-Mention your SEO and AI expertise\n-Offer web repair + performance/redesign improvements\n-Include a soft, benefit-driven CTA (e.g., “happy to jump on a quick call…”)\n\n\nBasic Business Details: \nName: {{ $('Business Details').item.json.business_name }}\nLocation: {{ $('Business Details').item.json.location }}\nBusiness Description: {{ $('Business Details').item.json.business_description }}\nDecision Maker Name: {{ $('DecisionMaker Name').item.json.message.content.DecisionMakerName }}\n\nError Details: \nError Name: {{ $('Business Website Request').item.json.error.name }}\nError Code: {{ $('Business Website Request').item.json.error.code}}\nError Status: {{ $('Business Website Request').item.json.error.status }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [580, -360], "id": "cda0ccf5-8d14-46d1-b6ab-7267d29c6322", "name": "Write message for error in Website", "credentials": {"openAiApi": {"id": "sSh5op9qG8sgh726", "name": "OpenAi account ZZ"}}}, {"parameters": {"url": "=https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url={{ $('Update Scraped Business Details').item.json.URL }}&strategy=mobile&category=performance&category=accessibility&category=seo&category=best-practices&key=AIzaSyBQaQXRouRmNK_2GavBSuu5cNjTEa0VrG8", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [540, -1120], "id": "f99c7024-2ff9-4ae7-9023-4625351e9ec2", "name": "Lighthouse Stats Request", "onError": "continueErrorOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "You are a native Swedish copywriter. Your job is to translate English into fluent Swedish using everyday language, slang, and a natural, relaxed tone. The result should sound like it was written by a Swedish local, not robotic or overly formal. Use casual 'du' form unless clearly inappropriate.\nAlways return output as JSON:\n{\n \"translation\": \"\"\n}\n\nRules:\n- Use casual, friendly Swedish (avoid overly formal structures)\n- Prefer real spoken language over textbook grammar when possible\n- Incorporate common slang, fillers, or conversational phrasing naturally\n- Avoid sounding like Google Translate or a corporate bot\n- Use contractions or shortenings common in everyday Swedish (e.g., \"tänkte bara\", \"har du hunnit\")\n- Maintain the intention and tone of the original message (friendly, light follow-up)\n- And just translate the given text don’t add text extra text from yourself\n- Remove Hey firstname, part from given text and translate remaining.", "role": "assistant"}, {"content": "=Here's text to translate:\n{{ $json.message.content.content }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [580, -240], "id": "885ccebc-d33f-4083-a950-7918ca8258be", "name": "Translate into Swedish", "credentials": {"openAiApi": {"id": "sSh5op9qG8sgh726", "name": "OpenAi account ZZ"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "Status": "Error with Stats"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [540, -820], "id": "d217f242-6f21-4d16-a905-5b65a8f058ef", "name": "Update Error in Stats", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"jsCode": "const categories = $input.first().json.lighthouseResult?.categories || {};\n\nfunction extractScore(cat) {\n  return categories[cat]?.score !== undefined\n    ? Math.round(categories[cat].score * 100)\n    : \"none\";\n}\n\nreturn [{\n  performance: extractScore(\"performance\"),\n  accessibility: extractScore(\"accessibility\"),\n  bestPractices: extractScore(\"best-practices\"),\n  seo: extractScore(\"seo\"),\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [540, -960], "id": "9512c413-5c26-468d-9f05-c4c40abf1702", "name": "Filter Stats"}, {"parameters": {"url": "=https://image.thum.io/get/fullpage/{{ $('Update Scraped Business Details').item.json.URL }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [740, -1120], "id": "4a024a83-4cf1-446c-adab-857d6e25a46e", "name": "Screenshot of Website Request", "onError": "continueErrorOutput"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "text": "=We scraped a business website and collected the following data:\n- A screenshot of the homepage  \n- A Lighthouse audit with scores for Performance (e.g. 34), Accessibility (e.g. 55), SEO (e.g. 61), and Best Practices  \n- Basic context (business name, location, what they do)  \n\nYour task:  \nGenerate a personalized email copy for a cold outreach email, pitching web redesign, performance optimization, and AI features depending on what you found. Always output the icebreaker value only from the following JSON. \n\n{\n \"icebreaker\": \"Hey [FirstName/companyShort], I came across [companyShort] and thought the way you [smallDetailYouAdmired] was a smart detail. Also noticed [anotherSpecificThingYouNoticed]—subtle but thoughtful.\\n\\nI checked your site and ran a Lighthouse audit—showed [performance]% performance, [seo]% SEO, [accessibility]% accessibility, and [best_practices]% best practices. I also saw a few things that could be holding it back—[Mention specifics that you noticed in the UI analysis]. I’ve worked on 500+ web projects over the last 8 years helping teams clean this stuff up, and the impact is real. I also bring in SEO and AI expertise to help businesses boost their online presence, drive more qualified traffic, and stay competitive.\\n\\nIf you're open to it, you can book a quick call—happy to walk you through a few ideas to make your site faster, smoother, and more findable.\\n\\nNo pressure—just figured I’d reach out since I already did the review. If you wanna see some relevant websites that I’ve worked on just let me know, I’ll send you the links.\" \n}\n\nRules:  \n- Write in a spartan/laconic tone of voice.  \n- Reference small, specific non-obvious UI or UX details from the homepage  \n- Mention Lighthouse scores using placeholders (e.g., [performance])  \n- Tie issues to business impact (e.g., slow load = fewer leads)  \n- Briefly mention your track record (e.g., 1000+ projects in 8 years)  \n- Include a clear, soft CTA (e.g., “Book a quick call to discuss how to improve speed, UX, and ranking”)  \n- Point out what the potential cost is (lost traffic, trust, SEO, new leads)  \n- Keep the tone helpful, not critical  \n- Offer to help fix and modernize the site  \n- The output should be just text don't mentioned any breackets, and words Icebreaker\n- Don't write anything above and below the output Like here's the email or Let me know if you need any changes etc\n- Use firsname if decisionmaker name is given else use companyShort in the beginning. \n\nLighthouse audit (Performance, SEO, Accessibility, Best Practices):\nPerformance: {{ $('Filter Stats').item.json.performance }}\nSEO: {{ $('Filter Stats').item.json.seo }}\nAccessibility: {{ $('Filter Stats').item.json.accessibility }}\nBest Practices: {{ $('Filter Stats').item.json.bestPractices }}\n\nBasic Context: \nBusinessName: {{ $('Business Details').item.json.business_name }}\nDecisionMakerName: \nBusinessDescription: {{ $('Business Details').item.json.business_description }}\nLocation: {{ $('Business Details').item.json.location }}\nWebsite URL: {{ $('Update Scraped Business Details').item.json.URL }}\nEmail: {{ $('Update Scraped Business Details').item.json.email }}", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [920, -1120], "id": "7e533b36-7782-423a-b4ce-1964fa489a4e", "name": "Analyse UI/UX design, Lighthouse stats & Business details", "credentials": {"openAiApi": {"id": "sSh5op9qG8sgh726", "name": "OpenAi account ZZ"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "You are a native Swedish copywriter. Your job is to translate English into fluent Swedish using everyday language, slang, and a natural, relaxed tone. The result should sound like it was written by a Swedish local, not robotic or overly formal. Use casual 'du' form unless clearly inappropriate.\nAlways return output as JSON:\n{\n \"translation\": \"\"\n}\n\nRules:\n- Use casual, friendly Swedish (avoid overly formal structures)\n- Prefer real spoken language over textbook grammar when possible\n- Incorporate common slang, fillers, or conversational phrasing naturally\n- Avoid sounding like Google Translate or a corporate bot\n- Use contractions or shortenings common in everyday Swedish (e.g., \"tänkte bara\", \"har du hunnit\")\n- Maintain the intention and tone of the original message (friendly, light follow-up)\n- And just translate the given text don’t add text extra text from yourself\n- Remove Hey firstname, part from given text and translate remaining.", "role": "assistant"}, {"content": "=Here's text to translate:\n{{ $json.content }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [680, -960], "id": "9c557490-a52c-4932-bbd6-2a9dcdd6c0c3", "name": "Translate Hyper-personalized message into Swedish", "credentials": {"openAiApi": {"id": "sSh5op9qG8sgh726", "name": "OpenAi account ZZ"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "Status": "Successfully Executed", "Message SE": "={{ $json.message.content.translation }}", "Message EN": "={{ $('Write message for error in Website').item.json.message.content.content }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [920, -360], "id": "3c7938a9-bf4c-4f9e-8f5d-1383d89a1792", "name": "Update Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8", "mode": "list", "cachedResultName": "BokaDirect Scrape Data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Sheet6", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Zf1xbYuqpjG_drKmNuCFXvX1Fr1x1c3xfWU9rYbW3h8/edit#gid=*********"}, "columns": {"mappingMode": "defineBelow", "value": {"row_number": "={{ $('FirstRow').item.json.row_number }}", "Status": "Successfully Executed", "Message SE": "={{ $json.message.content.translation }}", "Message EN": "={{ $('Analyse UI/UX design, Lighthouse stats & Business details').item.json.content }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "BokaDirect Profile", "displayName": "BokaDirect Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Message SE", "displayName": "Message SE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Message EN", "displayName": "Message EN", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Business Name", "displayName": "Business Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Decision maker name", "displayName": "Decision maker name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "StaffList", "displayName": "StaffList", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Make Status", "displayName": "Make Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Instantly Status", "displayName": "Instantly Status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [960, -820], "id": "a7b5e581-d71d-4c04-9b6c-227a087c1c2e", "name": "Update Sheet with messages and Status", "credentials": {"googleSheetsOAuth2Api": {"id": "Z03wfu5aZDxPPOdw", "name": "Google Sheets account"}}, "onError": "continueErrorOutput"}, {"parameters": {"path": "0f267a89-60f6-46c5-a97e-39c4e1452579", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-720, -740], "id": "3a83c8d9-0b8c-4a37-8252-3515d462389d", "name": "Webhook", "webhookId": "0f267a89-60f6-46c5-a97e-39c4e1452579"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-500, -1020], "id": "2db2c12e-beea-442f-b928-b27071318cbc", "name": "Wait", "webhookId": "a1be5555-4dc5-45da-b325-acef36739068"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-500, -440], "id": "aff4659e-87a2-4bac-a14a-c8ebcb911b07", "name": "Wait2", "webhookId": "cf54f6db-ead7-4dcb-83c9-08226efea440"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [80, -160], "id": "c2130eb8-45b9-4903-8bdb-45455c48d8d9", "name": "Wait3", "webhookId": "e3174a31-fbc3-4c51-95b5-6bc4cbdddb09"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-60, -20], "id": "25a0b300-f39a-45a5-bfc3-729f7e78d6c8", "name": "Wait4", "webhookId": "573bfa47-7c05-4bb4-a97d-8435a2b6d662"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [80, -20], "id": "7a7a18f6-dfbc-4c22-8745-2f05c14bb490", "name": "Wait5", "webhookId": "4396638e-aba6-45e5-84e9-d3a03ebb1939"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [920, -220], "id": "9f105d63-bbb6-4e99-8bfb-30ee0f8bad48", "name": "Wait6", "webhookId": "6eb88867-d8b2-41d9-b995-48cad54e0dd4"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [680, -820], "id": "571e47c0-3837-4749-a498-a29ff29160d9", "name": "Wait7", "webhookId": "179cac21-c996-4e0a-bdeb-531d95bf020b"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [820, -820], "id": "1137b206-ed6c-4d76-83ce-8b13b0fb5df0", "name": "Wait8", "webhookId": "67e35c1e-1785-465d-998d-8bb49ce3b260"}, {"parameters": {"content": "# Step 1.\n## Trigger & CRM Input", "height": 1100, "width": 260, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-580, -1300], "id": "50adafe2-cab5-427a-ba9f-102ad959fb35", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Step 2.\n## Scraping Business Data", "height": 1520, "width": 420, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-140, -1300], "id": "60181054-7b03-46b8-99a0-1e72f139a020", "name": "Sticky Note1"}, {"parameters": {"content": "# Step 3.\n## Analyzing Lighthouse Stats + Website UI/UX Design & Generating Hyper-Personalised Outreach Email", "height": 680, "width": 720, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [460, -1300], "id": "c557e65d-e962-44f4-8141-591d5c8939d1", "name": "Sticky Note2"}, {"parameters": {"content": "# Step 4.\n## Analyzing Website Error & Generating Hyper-Personalised Outreach Email", "height": 540, "width": 660, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, -560], "id": "99622d56-711a-4cc6-8327-1d0f65af0ec8", "name": "Sticky Note3"}], "pinData": {}, "connections": {"Wait1": {"main": [[{"node": "Screenshot of Website Request", "type": "main", "index": 0}]]}, "Get New Rows": {"main": [[{"node": "FirstRow", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "FirstRow": {"main": [[{"node": "If Email is not Empty", "type": "main", "index": 0}]]}, "If Email is not Empty": {"main": [[{"node": "If Email is not BokaDirect's support email", "type": "main", "index": 0}], [{"node": "Update Sheet (email doesn't exist)", "type": "main", "index": 0}]]}, "Update Sheet (email doesn't exist)": {"main": [[{"node": "Get New Rows", "type": "main", "index": 0}], [{"node": "Wait2", "type": "main", "index": 0}]]}, "If Email is not BokaDirect's support email": {"main": [[{"node": "BokaDirect Profile URL Request", "type": "main", "index": 0}], [{"node": "Update Sheet URL redirected", "type": "main", "index": 0}]]}, "BokaDirect Profile URL Request": {"main": [[{"node": "ProfilePage or Redirected", "type": "main", "index": 0}]]}, "ProfilePage or Redirected": {"main": [[{"node": "Get Email", "type": "main", "index": 0}]]}, "Get Email": {"main": [[{"node": "If ProfilePage or not", "type": "main", "index": 0}]]}, "Update Sheet URL redirected": {"main": [[], [{"node": "Wait3", "type": "main", "index": 0}]]}, "DecisionMaker Name": {"main": [[{"node": "If website exist", "type": "main", "index": 0}]]}, "Business Details": {"main": [[{"node": "DecisionMaker Name", "type": "main", "index": 0}]]}, "If ProfilePage or not": {"main": [[{"node": "Business Details", "type": "main", "index": 0}], [{"node": "Update Sheet URL redirected", "type": "main", "index": 0}]]}, "If website exist": {"main": [[{"node": "Set URL", "type": "main", "index": 0}], [{"node": "Professional Email or Personal", "type": "main", "index": 0}]]}, "Professional Email or Personal": {"main": [[{"node": "Professional or Not", "type": "main", "index": 0}]]}, "Professional or Not": {"main": [[{"node": "Set URL from Professional Email", "type": "main", "index": 0}], [{"node": "Update Website doesn't exist", "type": "main", "index": 0}]]}, "Update Website doesn't exist": {"main": [[], [{"node": "Wait4", "type": "main", "index": 0}]]}, "Set URL from Professional Email": {"main": [[{"node": "Update Scraped Business Details", "type": "main", "index": 0}]]}, "Set URL": {"main": [[{"node": "Update Scraped Business Details", "type": "main", "index": 0}]]}, "Update Scraped Business Details": {"main": [[{"node": "Business Website Request", "type": "main", "index": 0}], [{"node": "Wait5", "type": "main", "index": 0}]]}, "Business Website Request": {"main": [[{"node": "Lighthouse Stats Request", "type": "main", "index": 0}], [{"node": "Write message for error in Website", "type": "main", "index": 0}]]}, "Write message for error in Website": {"main": [[{"node": "Translate into Swedish", "type": "main", "index": 0}]]}, "Lighthouse Stats Request": {"main": [[{"node": "Filter Stats", "type": "main", "index": 0}], [{"node": "Update Error in Stats", "type": "main", "index": 0}]]}, "Translate into Swedish": {"main": [[{"node": "Update Sheet", "type": "main", "index": 0}]]}, "Update Error in Stats": {"main": [[], [{"node": "Wait7", "type": "main", "index": 0}]]}, "Filter Stats": {"main": [[{"node": "Screenshot of Website Request", "type": "main", "index": 0}]]}, "Screenshot of Website Request": {"main": [[{"node": "Analyse UI/UX design, Lighthouse stats & Business details", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}]]}, "Analyse UI/UX design, Lighthouse stats & Business details": {"main": [[{"node": "Translate Hyper-personalized message into Swedish", "type": "main", "index": 0}]]}, "Translate Hyper-personalized message into Swedish": {"main": [[{"node": "Update Sheet with messages and Status", "type": "main", "index": 0}]]}, "Update Sheet": {"main": [[], [{"node": "Wait6", "type": "main", "index": 0}]]}, "Update Sheet with messages and Status": {"main": [[], [{"node": "Wait8", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Get New Rows", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get New Rows", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Update Sheet (email doesn't exist)", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Update Sheet URL redirected", "type": "main", "index": 0}]]}, "Wait4": {"main": [[{"node": "Update Website doesn't exist", "type": "main", "index": 0}]]}, "Wait5": {"main": [[{"node": "Update Scraped Business Details", "type": "main", "index": 0}]]}, "Wait6": {"main": [[{"node": "Update Sheet", "type": "main", "index": 0}]]}, "Wait7": {"main": [[{"node": "Update Error in Stats", "type": "main", "index": 0}]]}, "Wait8": {"main": [[{"node": "Update Sheet with messages and Status", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "72f0011a-a9d7-4781-b1fe-5e008a49eabd", "meta": {"templateCredsSetupCompleted": true, "instanceId": "70ec592e3e2ce9d04b830769fee3a6938b0f44ab688fd09111c9c3f19fe6b0ea"}, "id": "9DGwFeTa6J3svG18", "tags": []}