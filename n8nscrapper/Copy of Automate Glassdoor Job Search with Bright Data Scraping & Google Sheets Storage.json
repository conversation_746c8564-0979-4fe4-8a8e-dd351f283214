{"name": "Automate Glassdoor Job Search with Bright Data Scraping & Google Sheets Storage", "nodes": [{"parameters": {"formTitle": "Search Job Listings Using Keywords", "formFields": {"values": [{"fieldLabel": "Location"}, {"fieldLabel": "Search Job type"}, {"fieldLabel": "country"}]}, "options": {}}, "id": "ce3a7e97-41b6-49fc-ae23-cbf65044a974", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [460, 340], "webhookId": "9b16a680-d396-41ee-8304-44a430954ad7", "typeVersion": 2.2}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_BRIGHTDATA_API_TOKEN"}]}, "options": {}}, "id": "10cbb574-3967-490a-9c57-c1140a04af00", "name": "Check Delivery Status of Snap ID", "type": "n8n-nodes-base.httpRequest", "position": [980, 340], "typeVersion": 4.2, "alwaysOutputData": true}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "35ed620d-b5d5-4e97-bcc5-52b283d85616", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "ready"}]}, "options": {}}, "id": "c1090f01-84ab-4586-8c0e-4a2f3a5fc389", "name": "Check Final Status", "type": "n8n-nodes-base.if", "position": [1480, 340], "typeVersion": 2.2}, {"parameters": {"amount": 1, "unit": "minutes"}, "id": "1cc8376f-393f-48cc-8bb3-93cfafa28d58", "name": "Wait 1 minute", "type": "n8n-nodes-base.wait", "position": [1240, 340], "webhookId": "10b45556-5e91-4387-abd0-f9078b220286", "typeVersion": 1.1}, {"parameters": {"method": "POST", "url": "https://api.brightdata.com/datasets/v3/trigger", "sendQuery": true, "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_lpfbbndm1xnopbrcr0"}, {"name": "include_errors", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "keyword"}, {"name": "limit_per_input", "value": "5"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_BRIGHTDATA_API_TOKEN"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"input\": [\n    {\n      \"keyword\": \"{{ $json['Search Job type'] }}\",\n      \"location\": \"{{ $json['Location'] }}\",\n      \"country\": \"{{ $json['country'] }}\"\n    }\n  ],\n  \"custom_output_fields\": [\n    \"url\",\n    \"company_name\",\n    \"company_rating\",\n    \"job_title\",\n    \"job_location\",\n    \"company_website\"\n  ]\n}", "options": {}}, "id": "c9157c5c-bcab-462c-9ace-6f5e456e186c", "name": "Scrap data", "type": "n8n-nodes-base.httpRequest", "position": [680, 340], "typeVersion": 4.2}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_BRIGHTDATA_API_TOKEN"}]}, "options": {}}, "id": "f1a0e9ed-ced0-4eb6-8eeb-6c7537c7822f", "name": "Getting Job Lists", "type": "n8n-nodes-base.httpRequest", "position": [1740, 320], "typeVersion": 4.2}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "mode": "list", "value": "YOUR_GOOGLE_SHEET_ID", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_GOOGLE_SHEET_ID/edit?usp=drivesdk", "cachedResultName": "Data Scraper <PERSON>, <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>"}, "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_GOOGLE_SHEET_ID/edit#gid=*********", "cachedResultName": "Glassdoor Scraper by keyword"}, "columns": {"value": {"Rating": "={{ $json.company_rating }}", "Job Link": "={{ $json.company_website }}", "Location": "={{ $json.job_location }}", "Job Title": "={{ $json.job_title }}", "Company Name": "={{ $json.company_name }}"}, "schema": [{"id": "Job Title", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Job Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Company Name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Company Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Location", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Rating", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Salary Estimate", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Salary Estimate", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job Posted Date", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Job Posted Date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job Link", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Job Link", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["Job Link"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "558543dc-af03-4033-990a-d850614150fe", "name": "Update List", "type": "n8n-nodes-base.googleSheets", "position": [2080, 320], "typeVersion": 4.6}, {"parameters": {"content": "Triggers the workflow when the user submits the job type, location, and country via form.", "height": 260}, "id": "2ad5e448-ae48-4ae8-a8e7-243101810613", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"content": "Calls Bright Data API to initiate scraping Glassdoor job listings based on the form input (keyword, location, country).", "height": 260, "width": 260, "color": 2}, "id": "02a1a60f-b11f-4981-81c3-c4e86f2e95d2", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [640, 240], "typeVersion": 1}, {"parameters": {"content": "Pauses the workflow for 1 minute before rechecking status (to allow data generation).", "height": 260, "width": 220, "color": 3}, "id": "32d05b44-e78e-4f09-9626-c25bbc1b8c6b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1200, 240], "typeVersion": 1}, {"parameters": {"content": "Checks the scraping task's status using Bright Data snapshot ID to monitor progress.", "height": 260, "width": 260, "color": 5}, "id": "b7085f2b-c2ae-4f68-926d-ca6658ea9215", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [920, 240], "typeVersion": 1}, {"parameters": {"content": "Conditional node that checks if Bright Data has marked the scraping job as \"ready\". Loops back to check if not ready.", "height": 280, "width": 280, "color": 7}, "id": "eee21db7-8bad-4c59-9dcd-ab95eb5501c9", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1680, 240], "typeVersion": 1}, {"parameters": {"content": "Appends or updates job data (title, company, rating, location, link) in Google Sheets under the \"Glassdoor Scraper by keyword\" tab.", "height": 340, "width": 280, "color": 4}, "id": "9b59c3c5-8e07-4082-a8b9-6657055d4e5f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1980, 220], "typeVersion": 1}], "pinData": {}, "connections": {"Scrap data": {"main": [[{"node": "Check Delivery Status of Snap ID", "type": "main", "index": 0}]]}, "Wait 1 minute": {"main": [[{"node": "Check Final Status", "type": "main", "index": 0}]]}, "Getting Job Lists": {"main": [[{"node": "Update List", "type": "main", "index": 0}]]}, "Check Final Status": {"main": [[{"node": "Getting Job Lists", "type": "main", "index": 0}], [{"node": "Check Delivery Status of Snap ID", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Scrap data", "type": "main", "index": 0}]]}, "Check Delivery Status of Snap ID": {"main": [[{"node": "Wait 1 minute", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "4843", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}