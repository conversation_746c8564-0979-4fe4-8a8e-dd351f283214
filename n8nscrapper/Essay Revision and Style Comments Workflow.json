{"name": "Essay Revision and Style Comments Workflow", "nodes": [{"parameters": {"path": "6fbBHVWvuckjSot6", "options": {"noResponseBody": false}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "webhookId": "essay-revision-webhook"}, {"parameters": {"url": "={{ 'https://docs.googleapis.com/v1/documents/' + $json.documentId }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "get-document", "name": "Get Document", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [200, 0], "credentials": {"httpHeaderAuth": {"id": "google_docs_credential_id", "name": "Google Docs (Pipedream)"}}}, {"parameters": {"jsCode": "// Extract text content from Google Docs structure\nconst docData = $input.first().json;\nconst content = docData.body?.content || [];\n\nlet fullText = '';\nlet textElements = [];\n\n// Extract text from document structure\nfunction extractTextFromContent(contentArray) {\n  for (const element of contentArray) {\n    if (element.paragraph) {\n      const paragraph = element.paragraph;\n      if (paragraph.elements) {\n        for (const elem of paragraph.elements) {\n          if (elem.textRun && elem.textRun.content) {\n            const text = elem.textRun.content;\n            fullText += text;\n            textElements.push({\n              text: text,\n              startIndex: elem.startIndex,\n              endIndex: elem.endIndex\n            });\n          }\n        }\n      }\n    }\n  }\n}\n\nextractTextFromContent(content);\n\nreturn [{\n  json: {\n    documentId: docData.documentId,\n    title: docData.title,\n    fullText: fullText,\n    textElements: textElements,\n    originalDocData: docData\n  }\n}];"}, "id": "extract-content", "name": "Extract Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 0]}, {"parameters": {"promptType": "define", "text": "=You are an expert writing coach specializing in The Elements of Style by <PERSON><PERSON> and <PERSON>. Analyze the following essay text and provide detailed revision suggestions.\n\nEssay Text:\n{{ $json.fullText }}\n\nFor each issue you identify, provide:\n1. The specific text that needs revision\n2. The Elements of Style principle it violates (e.g., \"Omit needless words\", \"Use active voice\", \"Be specific\")\n3. A suggested revision\n4. Brief explanation of why the change improves the writing\n\nFocus on common issues like:\n- Wordiness and redundancy\n- Passive voice\n- Weak verbs\n- Unclear pronoun references\n- Poor sentence structure\n- Lack of parallel structure\n- Overuse of adverbs\n- Vague language\n\nProvide your response as a structured list of revision suggestions.", "options": {"maxIterations": 8}}, "id": "ai-revision-agent", "name": "AI Revision Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [560, 0]}, {"parameters": {"model": {"__rl": true, "value": "claude-sonnet-4-20250514", "mode": "list", "cachedResultName": "<PERSON> 4"}, "options": {}}, "id": "anthropic-chat-model", "name": "Anthropic <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [600, 200], "credentials": {"anthropicApi": {"id": "6zFJfpg1oNMqvxlJ", "name": "Claude <PERSON> (System)"}}}, {"parameters": {"jsCode": "// Parse AI response and format for Google Docs comments\nconst aiResponse = $input.first().json.output;\nconst originalData = $input.first().json;\n\n// Extract revision suggestions from AI response\nconst suggestions = [];\nconst lines = aiResponse.split('\\n');\n\nlet currentSuggestion = null;\nfor (const line of lines) {\n  const trimmed = line.trim();\n  \n  // Look for numbered suggestions or bullet points\n  if (trimmed.match(/^\\d+\\.|^[-*]\\s/)) {\n    if (currentSuggestion) {\n      suggestions.push(currentSuggestion);\n    }\n    currentSuggestion = {\n      originalText: '',\n      principle: '',\n      revision: '',\n      explanation: '',\n      fullText: trimmed\n    };\n  } else if (currentSuggestion && trimmed) {\n    currentSuggestion.fullText += ' ' + trimmed;\n  }\n}\n\nif (currentSuggestion) {\n  suggestions.push(currentSuggestion);\n}\n\n// Process suggestions to extract components\nfor (const suggestion of suggestions) {\n  const text = suggestion.fullText;\n  \n  // Extract original text (look for quotes or \"text that needs revision\")\n  const originalMatch = text.match(/[\"'](.*?)[\"']|original text[:\\s]+(.*?)(?=\\n|$)/i);\n  if (originalMatch) {\n    suggestion.originalText = originalMatch[1] || originalMatch[2] || '';\n  }\n  \n  // Extract principle\n  const principleMatch = text.match(/principle[:\\s]+(.*?)(?=\\n|suggested|revision|$)/i);\n  if (principleMatch) {\n    suggestion.principle = principleMatch[1].trim();\n  }\n  \n  // Extract revision\n  const revisionMatch = text.match(/revision[:\\s]+(.*?)(?=\\n|explanation|$)/i);\n  if (revisionMatch) {\n    suggestion.revision = revisionMatch[1].trim();\n  }\n  \n  // Extract explanation\n  const explanationMatch = text.match(/explanation[:\\s]+(.*?)$/i);\n  if (explanationMatch) {\n    suggestion.explanation = explanationMatch[1].trim();\n  }\n}\n\n// Create comment requests for Google Docs API\nconst commentRequests = [];\nconst fullText = originalData.fullText || '';\n\nfor (let i = 0; i < Math.min(suggestions.length, 10); i++) {\n  const suggestion = suggestions[i];\n  \n  // Find position in text for comment\n  let startIndex = 1; // Default to beginning\n  if (suggestion.originalText) {\n    const foundIndex = fullText.indexOf(suggestion.originalText);\n    if (foundIndex !== -1) {\n      startIndex = foundIndex + 1; // Google Docs uses 1-based indexing\n    }\n  }\n  \n  const commentText = `**${suggestion.principle || 'Style Suggestion'}**\\n\\n${suggestion.revision || 'Consider revising this text.'}\\n\\n${suggestion.explanation || 'This change will improve clarity and style.'}`;\n  \n  commentRequests.push({\n    insertText: {\n      location: {\n        index: startIndex\n      },\n      text: `[Comment ${i + 1}: ${commentText}]`\n    }\n  });\n}\n\nreturn [{\n  json: {\n    documentId: originalData.documentId,\n    suggestions: suggestions,\n    commentRequests: commentRequests,\n    aiResponse: aiResponse,\n    totalSuggestions: suggestions.length\n  }\n}];"}, "id": "format-comments", "name": "Format Comments", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 0]}, {"parameters": {"method": "POST", "url": "={{ 'https://docs.googleapis.com/v1/documents/' + $json.documentId + ':batchUpdate' }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"requests\": {{ JSON.stringify($json.commentRequests) }}\n}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "id": "add-comments", "name": "Add Comments", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1000, 0], "credentials": {"httpHeaderAuth": {"id": "google_docs_credential_id", "name": "Google Docs (Pipedream)"}}}, {"parameters": {"assignments": {"assignments": [{"id": "status_field", "name": "status", "type": "string", "value": "success"}, {"id": "message_field", "name": "message", "type": "string", "value": "Essay revision completed successfully"}, {"id": "document_id_field", "name": "documentId", "type": "string", "value": "={{ $json.documentId }}"}, {"id": "suggestions_count_field", "name": "suggestionsAdded", "type": "number", "value": "={{ $json.totalSuggestions }}"}]}, "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, 0]}], "pinData": {}, "connections": {"Webhook Trigger": {"main": [[{"node": "Get Document", "type": "main", "index": 0}]]}, "Get Document": {"main": [[{"node": "Extract Content", "type": "main", "index": 0}]]}, "Extract Content": {"main": [[{"node": "AI Revision Agent", "type": "main", "index": 0}]]}, "AI Revision Agent": {"main": [[{"node": "Format Comments", "type": "main", "index": 0}]]}, "Format Comments": {"main": [[{"node": "Add Comments", "type": "main", "index": 0}]]}, "Add Comments": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "AI Revision Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ad10cc2e-8f33-41f4-9dca-0fc01ee2a80d", "meta": {"instanceId": "017a7fc3d74edc30de2a0f52f2562bd6175764dc2df5355a276ce0ae3f651006"}, "id": "6fbBHVWvuckjSot6", "tags": []}