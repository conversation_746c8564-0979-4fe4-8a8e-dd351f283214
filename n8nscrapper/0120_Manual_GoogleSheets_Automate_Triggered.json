{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [250, 300], "parameters": {}, "typeVersion": 1}, {"name": "Dropcontact", "type": "n8n-nodes-base.dropcontact", "position": [650, 300], "parameters": {"email": "={{$json[\"email\"]}}", "options": {"siren": true, "language": "fr"}, "additionalFields": {"company": "={{$json[\"companyName\"]}}", "website": "={{$json[\"website\"]}}", "linkedin": "={{$json[\"LinkedIn\"]}}", "full_name": "={{$json[\"fullName\"]}}", "last_name": "={{$json[\"lastName\"]}}", "first_name": "={{$json[\"firstName\"]}}"}}, "credentials": {"dropcontactApi": {"id": "6", "name": ""}}, "typeVersion": 1}, {"name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [450, 300], "parameters": {"range": "A:K", "options": {"continue": false}, "sheetId": "", "authentication": "oAuth2"}, "credentials": {"googleSheetsOAuth2Api": {"id": "7", "name": "Google Sheets account"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.lemlist", "position": [850, 300], "parameters": {"email": "={{$node[\"Dropcontact\"].json[\"email\"][0][\"email\"]}}", "resource": "lead", "campaignId": "", "additionalFields": {"lastName": "={{$node[\"Dropcontact\"].json[\"last_name\"]}}", "firstName": "={{$node[\"Dropcontact\"].json[\"first_name\"]}}", "companyName": "={{$node[\"Dropcontact\"].json[\"company\"]}}"}}, "credentials": {"lemlistApi": {"id": "9", "name": "Lemlist account"}}, "typeVersion": 1}], "connections": {"Dropcontact": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Dropcontact", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}}