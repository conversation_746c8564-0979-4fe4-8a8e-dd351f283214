{"name": "Document Agent", "nodes": [{"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"file_id\": \"\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "b236fc31-7f66-4435-b0f6-af9645169693", "name": "When Executed by Another Workflow"}, {"parameters": {"resource": "file", "fileId": "={{ $json.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [220, 0], "id": "5b309fd0-6be8-44a7-a09f-91be7710c750", "name": "Telegram", "webhookId": "a584bb04-8f07-447f-9272-381f5b70ffe4", "credentials": {"telegramApi": {"id": "OGKm4F9KSKep4HTN", "name": "Telegram account 4"}}}, {"parameters": {"name": "={{ $('When Executed by Another Workflow').item.json.message.document.file_name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "12dVXMe-em5GnCBmOzP1w8ENx6TaCpWNo", "mode": "list", "cachedResultName": "Test Folder", "cachedResultUrl": "https://drive.google.com/drive/folders/12dVXMe-em5GnCBmOzP1w8ENx6TaCpWNo"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [440, 0], "id": "9422b356-10d1-4e18-8afc-114052cbc617", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "ky2JdHaL16Kd4ddI", "name": "Google Drive account 3"}}}, {"parameters": {"operation": "pdf", "binaryPropertyName": "=data", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [880, 0], "id": "e9f7b043-1128-48d9-80cf-ccd4562b41c5", "name": "Extract from File"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [660, 0], "id": "740ba453-8414-414d-8746-9934f42afb15", "name": "Google Drive1", "credentials": {"googleDriveOAuth2Api": {"id": "ky2JdHaL16Kd4ddI", "name": "Google Drive account 3"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "You're an intelligent bot at extracting data from documents.", "role": "system"}, {"content": "=Please analyze the text below and extract the following items for me: Invoice #, Quantity, Price, Tax, Subtotal, Total, Date, Description.\n\nPlease make sure the output is JSON Data.\n\nHere's the text:\n {{ $json.text }}"}, {"content": "Please structure the data in JSON format exactly like this:\n[{\n  \"invoice_number\": \"INVOICE_NUMBER_PLACEHOLDER\",\n  \"date\": \"DATE_PLACEHOLDER\",\n  \"items\": [\n    {\n      \"description\": \"DESCRIPTION_PLACEHOLDER\",\n      \"quantity\": \"QUANTITY_PLACEHOLDER\",\n      \"price\": \"PRICE_PLACEHOLDER\",\n      \"tax\": \"TAX_PLACEHOLDER\",\n      \"subtotal\": \"SUBTOTAL_PLACEHOLDER\"\n    }\n  ],\n  \"total\": \"TOTAL_PLACEHOLDER\"\n}\n]", "role": "assistant"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1100, 0], "id": "014fdb0b-77ca-404a-b6db-4ad2ca783f8a", "name": "OpenAI", "credentials": {"openAiApi": {"id": "ijZWPGwLXdAOVFfI", "name": "OpenAi account 2"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1Gjyfz4L_LOvDQsNTBIlF2SsoUNz9PvQjmikIe1zcRIQ", "mode": "list", "cachedResultName": "n8n Personal Assistant", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Gjyfz4L_LOvDQsNTBIlF2SsoUNz9PvQjmikIe1zcRIQ/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Gjyfz4L_LOvDQsNTBIlF2SsoUNz9PvQjmikIe1zcRIQ/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Invoice #": "={{ $('OpenAI').item.json.message.content.invoice_number }}", "Description": "={{ $json.description }}", "Qty": "={{ $json.quantity }}", "Price": "={{ $json.price }}", "Tax": "={{ $json.tax }}", "Subtotal": "={{ $json.subtotal }}", "Total": "={{ $('OpenAI').item.json.message.content.invoice.total }}", "Date": "={{ $now }}"}, "matchingColumns": [], "schema": [{"id": "Invoice #", "displayName": "Invoice #", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Qty", "displayName": "Qty", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Price", "displayName": "Price", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Tax", "displayName": "Tax", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Subtotal", "displayName": "Subtotal", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Total", "displayName": "Total", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Date", "displayName": "Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1680, 0], "id": "65ab8e01-8705-4686-a719-b64efdecba7a", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "oHG2DldApzJrCoBY", "name": "Google Sheets account 2"}}}, {"parameters": {"fieldToSplitOut": "message.content.items", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1460, 0], "id": "0125f8a1-807a-423e-b127-b240f6e9c6c4", "name": "Split Out"}], "pinData": {"When Executed by Another Workflow": [{"json": {"file_id": "BQACAgEAAxkBAAMoZ6-T0mSeT6IDz1TN0ZZli5OWSYUAAlMEAALwWnhFKSdut4sAAbOeNgQ"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Google Drive1", "type": "main", "index": 0}]]}, "Google Drive1": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8593a6a3-7cb6-4907-a9d0-24db788f22d4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d2017cc6d1e4b956d269a8123bffa72fb7aaa41ad37a73b7c0fb64c7d0e2edae"}, "id": "W4Z2oMtQxhDcPHa6", "tags": []}