{"generatedAt": "2025-08-13T11:30:23.668Z", "totalWorkflows": 528, "validWorkflows": 500, "invalidWorkflows": 28, "categories": {"automation": 495, "ai": 371, "productivity": 279, "data-processing": 194, "communication": 122, "social-media": 44, "file-management": 82, "integration": 121, "monitoring": 3, "ecommerce": 3, "general": 3}, "nodeTypes": {"n8n-nodes-base.manualTrigger": 136, "n8n-nodes-base.markdown": 16, "n8n-nodes-base.gmail": 48, "@n8n/n8n-nodes-langchain.chainLlm": 47, "@n8n/n8n-nodes-langchain.lmChatGoogleGemini": 35, "n8n-nodes-base.aggregate": 48, "n8n-nodes-base.stickyNote": 219, "n8n-nodes-base.merge": 84, "n8n-nodes-base.scheduleTrigger": 65, "n8n-nodes-base.telegram": 69, "n8n-nodes-nostrobots.nostrobotsread": 1, "n8n-nodes-base.totp": 1, "n8n-nodes-base.bitwarden": 1, "n8n-nodes-base.typeformTrigger": 4, "n8n-nodes-base.if": 146, "n8n-nodes-base.googleSheets": 114, "n8n-nodes-base.set": 210, "n8n-nodes-base.twitter": 14, "n8n-nodes-base.airtable": 49, "n8n-nodes-base.plivo": 1, "n8n-nodes-base.openWeatherMap": 3, "n8n-nodes-base.cron": 25, "n8n-nodes-base.todoist": 1, "n8n-nodes-base.function": 35, "n8n-nodes-base.writeBinaryFile": 5, "n8n-nodes-base.copper": 2, "n8n-nodes-base.noOp": 29, "n8n-nodes-base.coda": 1, "n8n-nodes-base.httpRequest": 244, "n8n-nodes-base.amqp": 1, "n8n-nodes-base.googleSlides": 2, "n8n-nodes-base.mattermost": 3, "n8n-nodes-base.emeliaTrigger": 1, "n8n-nodes-base.functionItem": 8, "n8n-nodes-base.uproc": 4, "n8n-nodes-base.awsSqs": 1, "n8n-nodes-base.googleBigQuery": 3, "n8n-nodes-base.clearbit": 2, "n8n-nodes-base.mailcheck": 1, "n8n-nodes-base.clickUp": 2, "n8n-nodes-base.awsSes": 1, "n8n-nodes-base.dropbox": 3, "n8n-nodes-base.filemaker": 1, "n8n-nodes-base.mqtt": 1, "n8n-nodes-base.code": 117, "n8n-nodes-base.spotify": 1, "n8n-nodes-base.nocoDb": 3, "n8n-nodes-base.filter": 29, "n8n-nodes-base.splitInBatches": 69, "n8n-nodes-base.webhook": 78, "n8n-nodes-base.googleDrive": 69, "n8n-nodes-base.googleBooks": 1, "n8n-nodes-base.iCal": 1, "n8n-nodes-base.emailSend": 15, "n8n-nodes-base.crypto": 4, "n8n-nodes-base.storyblok": 1, "n8n-nodes-base.clickUpTrigger": 1, "n8n-nodes-base.htmlExtract": 6, "n8n-nodes-base.notion": 9, "n8n-nodes-base.awsTranscribe": 1, "n8n-nodes-base.awsS3": 3, "n8n-nodes-base.uptimeRobot": 1, "n8n-nodes-base.microsoftToDo": 1, "n8n-nodes-base.git": 1, "n8n-nodes-base.googleCalendar": 8, "n8n-nodes-base.trello": 2, "n8n-nodes-base.moveBinaryData": 2, "n8n-nodes-base.activeCampaignTrigger": 1, "n8n-nodes-base.githubTrigger": 1, "n8n-nodes-base.travisCi": 1, "n8n-nodes-base.pipedrive": 4, "n8n-nodes-base.line": 1, "n8n-nodes-base.getResponse": 1, "n8n-nodes-base.trelloTrigger": 1, "n8n-nodes-base.switch": 70, "n8n-nodes-base.googleCalendarTrigger": 1, "n8n-nodes-base.slack": 29, "n8n-nodes-base.xml": 6, "n8n-nodes-base.respondToWebhook": 36, "n8n-nodes-base.spreadsheetFile": 3, "n8n-nodes-base.interval": 2, "n8n-nodes-base.shopifyTrigger": 2, "n8n-nodes-base.zohoCrm": 1, "n8n-nodes-base.mailchimp": 1, "n8n-nodes-base.harvest": 1, "n8n-nodes-base.lemlist": 2, "n8n-nodes-base.hubspot": 10, "n8n-nodes-base.phantombuster": 1, "n8n-nodes-base.dropcontact": 3, "n8n-nodes-base.wait": 74, "n8n-nodes-base.debugHelper": 2, "n8n-nodes-base.splitOut": 73, "n8n-nodes-base.executeWorkflowTrigger": 66, "n8n-nodes-base.executeWorkflow": 17, "n8n-nodes-base.calendlyTrigger": 2, "n8n-nodes-base.dateTime": 6, "n8n-nodes-base.hubspotTrigger": 2, "n8n-nodes-base.executeCommand": 7, "n8n-nodes-base.mailgun": 1, "n8n-nodes-base.segment": 1, "n8n-nodes-base.zendesk": 1, "n8n-nodes-base.itemLists": 5, "n8n-nodes-base.netlifyTrigger": 1, "n8n-nodes-base.drift": 1, "n8n-nodes-base.humanticAi": 1, "n8n-nodes-base.vero": 1, "n8n-nodes-base.awsTextract": 2, "n8n-nodes-base.graphql": 2, "n8n-nodes-base.discord": 2, "n8n-nodes-base.onfleet": 2, "n8n-nodes-base.readBinaryFile": 4, "n8n-nodes-base.flow": 1, "n8n-nodes-base.n8nTrainingCustomerDatastore": 1, "n8n-nodes-base.googleCloudNaturalLanguage": 2, "n8n-nodes-base.github": 5, "n8n-nodes-base.gitlab": 1, "n8n-nodes-base.googleFirebaseRealtimeDatabase": 1, "n8n-nodes-base.amqpTrigger": 1, "n8n-nodes-base.togglTrigger": 1, "n8n-nodes-base.telegramTrigger": 43, "n8n-nodes-base.awsRekognition": 2, "n8n-nodes-base.profitWell": 1, "n8n-nodes-base.openAi": 7, "n8n-nodes-base.html": 12, "n8n-nodes-base.readBinaryFiles": 2, "n8n-nodes-base.sendy": 1, "n8n-nodes-base.mongoDb": 2, "n8n-nodes-base.n8n": 4, "n8n-nodes-base.strapi": 1, "n8n-nodes-base.onfleetTrigger": 2, "n8n-nodes-base.shopify": 1, "n8n-nodes-base.quickbooks": 1, "n8n-nodes-base.googleDriveTrigger": 11, "n8n-nodes-base.quickbase": 1, "n8n-nodes-base.pagerDuty": 1, "n8n-nodes-base.theHive": 1, "@n8n/n8n-nodes-langchain.agent": 216, "n8n-nodes-base.gmailTool": 14, "@n8n/n8n-nodes-langchain.lmChatOpenRouter": 51, "n8n-nodes-base.googleCalendarTool": 15, "n8n-nodes-base.airtableTool": 19, "@n8n/n8n-nodes-langchain.toolThink": 18, "@n8n/n8n-nodes-langchain.outputParserStructured": 71, "@n8n/n8n-nodes-langchain.lmChatOpenAi": 145, "@n8n/n8n-nodes-langchain.chatTrigger": 73, "@n8n/n8n-nodes-langchain.toolWorkflow": 26, "@n8n/n8n-nodes-langchain.memoryBufferWindow": 80, "n8n-nodes-base.youTube": 23, "n8n-nodes-base.formTrigger": 38, "@n8n/n8n-nodes-langchain.openAi": 80, "n8n-nodes-base.limit": 16, "@n8n/n8n-nodes-langchain.memoryPostgresChat": 5, "@n8n/n8n-nodes-langchain.vectorStoreSupabase": 13, "@n8n/n8n-nodes-langchain.toolVectorStore": 16, "@n8n/n8n-nodes-langchain.embeddingsOpenAi": 24, "@n8n/n8n-nodes-langchain.documentDefaultDataLoader": 17, "n8n-nodes-base.extractFromFile": 22, "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter": 13, "n8n-nodes-base.supabase": 4, "n8n-nodes-base.twilio": 4, "n8n-nodes-base.schedule": 1, "n8n-nodes-base.postgres": 6, "n8n-nodes-base.clerk": 1, "n8n-nodes-base.form": 4, "n8n-nodes-base.convertToFile": 37, "@n8n/n8n-nodes-langchain.toolSerpApi": 5, "n8n-nodes-base.facebookGraphApi": 16, "n8n-nodes-base.linkedIn": 10, "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter": 6, "@n8n/n8n-nodes-langchain.embeddingsHuggingFace": 1, "@n8n/n8n-nodes-langchain.lmChatAnthropic": 21, "n8n-nodes-base.whatsAppTrigger": 5, "n8n-nodes-base.whatsApp": 10, "@n8n/n8n-nodes-langchain.toolHttpRequest": 14, "@n8n/n8n-nodes-langchain.outputParserAutofixing": 9, "n8n-nodes-base.gmailTrigger": 11, "n8n-nodes-base.readWriteFile": 6, "@n8n/n8n-nodes-langchain.toolCalculator": 8, "n8n-nodes-base.dateTimeTool": 4, "n8n-nodes-base.baserowTool": 2, "n8n-nodes-base.googleSheetsTrigger": 10, "@n8n/n8n-nodes-langchain.informationExtractor": 10, "n8n-nodes-base.httpRequestTool": 5, "@n8n/n8n-nodes-langchain.lmChatXAiGrok": 2, "n8n-nodes-base.telegramTool": 3, "n8n-nodes-base.googleDocsTool": 3, "@n8n/n8n-nodes-langchain.mcpTrigger": 4, "@n8n/n8n-nodes-langchain.mcpClientTool": 8, "n8n-nodes-base.redis": 1, "n8n-nodes-base.errorTrigger": 5, "n8n-nodes-wassenger.wassengerTrigger": 1, "n8n-nodes-wassenger.wassenger": 1, "@n8n/n8n-nodes-langchain.vectorStoreInMemory": 2, "n8n-nodes-base.perplexity": 3, "n8n-nodes-base.googleContacts": 2, "n8n-nodes-base.googleDocs": 8, "n8n-nodes-base.removeDuplicates": 7, "n8n-nodes-base.wordpress": 2, "n8n-nodes-base.summarize": 4, "@n8n/n8n-nodes-langchain.vectorStorePinecone": 11, "n8n-nodes-base.readPDF": 1, "n8n-nodes-base.reddit": 8, "@n8n/n8n-nodes-langchain.toolCode": 1, "@n8n/n8n-nodes-langchain.textClassifier": 7, "n8n-nodes-mcp.mcpClient": 3, "n8n-nodes-base.perplexityTool": 3, "n8n-nodes-base.s3": 1, "n8n-nodes-base.notionTool": 1, "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini": 3, "n8n-nodes-scrapeless.scrapeless": 2, "n8n-nodes-base.googleSheetsTool": 3, "n8n-nodes-craftmypdf.craftMyPdf": 1, "n8n-nodes-base.rssFeedRead": 1, "n8n-nodes-base.sort": 5, "n8n-nodes-mcp.mcpClientTool": 23, "@n8n/n8n-nodes-langchain.agentTool": 1, "n8n-nodes-base.ghost": 1, "n8n-nodes-base.rssFeedReadTool": 1, "n8n-nodes-base.salesforceTrigger": 1, "n8n-nodes-base.salesforce": 1, "@n8n/n8n-nodes-langchain.lmChatGoogleVertex": 1, "n8n-nodes-base.sendInBlue": 1, "n8n-nodes-base.baserow": 1, "@n8n/n8n-nodes-langchain.manualChatTrigger": 3, "@n8n/n8n-nodes-langchain.chainSummarization": 3, "n8n-nodes-base.compression": 2, "n8n-nodes-base.slackTrigger": 1, "n8n-nodes-base.medium": 1, "@n8n/n8n-nodes-langchain.lmChatGroq": 1, "@n8n/n8n-nodes-langchain.sentimentAnalysis": 2, "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi": 1, "n8n-nodes-base.openWeatherMapTool": 1, "n8n-nodes-base.nocoDbTool": 1, "n8n-nodes-tesseractjs.tesseractNode": 1, "n8n-nodes-base.aiTransform": 2, "n8n-nodes-base.googleCloudStorage": 1, "n8n-nodes-base.twilioTrigger": 3, "n8n-nodes-base.microsoftOutlook": 3, "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter": 4, "n8n-nodes-base.erpNext": 1, "n8n-nodes-base.emailReadImap": 1, "@n8n/n8n-nodes-langchain.vectorStoreQdrant": 4, "n8n-nodes-youtube-transcription.youtubeTranscripter": 2, "n8n-nodes-base.structuredOutputParser": 1, "n8n-nodes-base.postgresTool": 1, "@n8n/n8n-nodes-langchain.lmOpenAi": 1, "n8n-nodes-base.youtube": 1, "@n8n/n8n-nodes-langchain.toolWikipedia": 2, "@n8n/n8n-nodes-langchain.lmChatOllama": 3, "@n8n/n8n-nodes-langchain.embeddingsOllama": 1, "@n8n/n8n-nodes-langchain.lmOllama": 1, "n8n-nodes-base.microsoftOneDrive": 2, "n8n-nodes-base.hubspotTool": 1, "n8n-nodes-base.slackTool": 1, "n8n-nodes-base.extractHtml": 1, "n8n-nodes-base.openRouter": 2, "n8n-nodes-base.stopAndError": 2, "n8n-nodes-base.zoom": 1, "n8n-nodes-base.microsoftOutlookTool": 1, "n8n-nodes-base.microsoftOneDriveTrigger": 1, "n8n-nodes-base.microsoftExcel": 1}, "complexity": {"simple": 6, "medium": 90, "complex": 404}, "features": {"withAI": 362, "withWebhooks": 81, "withSchedule": 91}, "workflows": [{"filename": "0001_Telegram_Schedule_Automation_Scheduled.json", "name": "#️⃣Nostr #damus AI Powered Reporting + Gmail + Telegram", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "communication"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0002_Manual_Totp_Automation_Triggered.json", "name": "Complete Guide to Setting Up and Generating TOTP Codes in n8n 🔐", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0003_Bitwarden_Automate.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0004_GoogleSheets_Typeform_Automate_Triggered.json", "name": "typeform feedback workflow", "description": "", "categories": ["automation", "productivity"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0005_Manual_Twitter_Create_Triggered.json", "name": "New tweets", "description": "", "categories": ["automation", "social-media", "ai", "productivity", "data-processing"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0006_Openweathermap_Cron_Automate_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0007_Manual_Todoist_Create_Triggered.json", "name": "Create a new task in Todoist", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0009_Process.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "automation"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0010_Writebinaryfile_Create.json", "name": "Unnamed Workflow", "description": "", "categories": ["file-management", "automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0011_Manual_Copper_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0012_Manual_Copper_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0013_Manual_Noop_Import_Triggered.json", "name": "Loading data into a spreadsheet", "description": "", "categories": ["automation", "data-processing", "productivity"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0014_Manual_Coda_Create_Triggered.json", "name": "Insert data into a new row for a table in Coda", "description": "", "categories": ["automation", "data-processing"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0015_HTTP_Cron_Update_Webhook.json", "name": "Send updates about the position of the ISS every minute to a topic in ActiveMQ", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0016_Manual_Googleslides_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0017_<PERSON><PERSON>_<PERSON><PERSON>a_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0019_Manual_Uproc_Send_Triggered.json", "name": "verify email", "description": "", "categories": ["automation", "ai", "communication"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0021_HTTP_Awssqs_Automation_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0023_HTTP_Googlebigquery_Automation_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0024_Manual_Clearbit_Send_Triggered.json", "name": "Look up a person using their email in Clearbit", "description": "", "categories": ["automation", "ai", "communication"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0026_Mailcheck_Airtable_Monitor.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "productivity", "automation"], "nodeCount": 4, "complexity": "medium", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0030_Manual_Clickup_Create_Triggered.json", "name": "Create a task in ClickUp", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0031_Functionitem_Dropbox_Automation_Webhook.json", "name": "screenshot", "description": "", "categories": ["automation", "data-processing"], "nodeCount": 10, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0032_Manual_Filemaker_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["file-management", "automation"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0033_HTTP_Mqtt_Automation_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0034_Code_Filter_Create_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "automation"], "nodeCount": 30, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0035_GoogleSheets_Webhook_Automate_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "automation", "integration"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0036_Gmail_GoogleDrive_Import.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "productivity", "automation"], "nodeCount": 3, "complexity": "medium", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0037_Manual_Googlebooks_Create_Triggered.json", "name": "Get a volume and add it to your bookshelf", "description": "", "categories": ["automation", "productivity"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0038_Manual_Ical_Send_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "communication"], "nodeCount": 3, "complexity": "medium", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0042_Crypto_Airtable_Update_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "integration"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0045_Manual_Telegram_Import_Triggered.json", "name": "Get SSL Certificate", "description": "", "categories": ["automation", "communication"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0046_Manual_Storyblok_Import_Triggered.json", "name": "Get all the stories starting with `release` and publish them", "description": "", "categories": ["automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0047_Clickup_Update_Triggered.json", "name": "Receive updates for events in ClickUp", "description": "", "categories": ["automation"], "nodeCount": 1, "complexity": "simple", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0048_HTTP_Htmlextract_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "integration"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0049_Manual_Awss3_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "file-management"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0050_Uptimerobot_Automate.json", "name": "Unnamed Workflow", "description": "", "categories": ["monitoring", "automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0051_Manual_Microsofttodo_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0052_Manual_Git_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0053_<PERSON><PERSON><PERSON>_GoogleCalendar_Create_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "data-processing", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0054_Manual_Writebinaryfile_Automate_Triggered.json", "name": "Standup Bot - Initialize", "description": "", "categories": ["automation", "file-management", "data-processing"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0057_Activecampaign_Create_Triggered.json", "name": "Receive updates when a new account is added by an admin in ActiveCampaign", "description": "", "categories": ["ai", "automation"], "nodeCount": 1, "complexity": "medium", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0059_Manual_Twitter_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "social-media"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0060_<PERSON><PERSON>_GitHub_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0062_Manual_Pipedrive_Create_Triggered.json", "name": "Create an deal in Pipedrive", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0065_Openweathermap_Line_Update_Scheduled.json", "name": "Send daily weather updates via a message in Line", "description": "", "categories": ["automation", "ai"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0070_Splitinbatches_Notion_Export_Scheduled.json", "name": "Archive empty pages in Notion Database", "description": "", "categories": ["productivity", "data-processing", "automation", "integration"], "nodeCount": 10, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0074_Manual_HTTP_Monitor_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 8, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0075_Manual_Noop_Update_Triggered.json", "name": "Get all the contacts from GetResponse and update them", "description": "", "categories": ["automation"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0076_Trello_Update_Triggered.json", "name": "Receive updates for changes in the specified list in Trello", "description": "", "categories": ["automation"], "nodeCount": 1, "complexity": "simple", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0077_HTTP_Noop_Sync_Webhook.json", "name": "<PERSON><PERSON><PERSON> to OpsGenie", "description": "", "categories": ["automation", "monitoring", "integration"], "nodeCount": 7, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0078_Manual_Slack_Monitor_Webhook.json", "name": "Google Calendar to Slack Status & Philips Hue", "description": "", "categories": ["automation", "productivity", "communication", "monitoring"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0081_Xml_Respondtowebhook_Automate_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "data-processing", "automation", "integration"], "nodeCount": 4, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0082_GoogleSheets_Interval_Process_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "file-management", "automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0083_Noop_HTTP_Automation_Webhook.json", "name": "Plex Automatic Throttler", "description": "", "categories": ["automation", "integration"], "nodeCount": 21, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0085_Shopify_Twitter_Create_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["social-media", "communication", "automation", "ecommerce"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0086_<PERSON><PERSON><PERSON><PERSON>_Trello_Create_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "ecommerce"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0090_Wait_Lemlist_Create_Scheduled.json", "name": "Create Email Campaign From LinkedIn Post Interactions", "description": "", "categories": ["automation", "ai", "productivity", "communication", "social-media"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0091_Wait_Splitout_Process_Webhook.json", "name": "↔️ Airtable Batch Processing", "description": "", "categories": ["automation", "data-processing", "ai", "productivity"], "nodeCount": 35, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0092_Wait_Datetime_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "communication", "ai"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0094_<PERSON><PERSON>_Gmail_Create_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0095_Googleslides_Slack_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["communication", "ai", "productivity", "automation"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0097_Executecommand_Mailgun_Automation_Webhook.json", "name": "Steam + CF Report", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0098_Manual_Segment_Monitor_Triggered.json", "name": "Track an event in Segment", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0100_Manual_Zendesk_Create_Triggered.json", "name": "Create a ticket in Zendesk", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0102_Manual_HTTP_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "file-management", "communication"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0103_Netlify_Airtable_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 3, "complexity": "medium", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0106_Manual_Drift_Create_Triggered.json", "name": "Create a contact in Drift", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0109_<PERSON><PERSON><PERSON>_Cron_Automate_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "data-processing", "communication", "automation"], "nodeCount": 12, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0110_Manual_Humanticai_Create_Webhook.json", "name": "Create, update, and get a profile in Humantic AI", "description": "", "categories": ["automation", "ai", "file-management"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0111_Manual_Vero_Create_Triggered.json", "name": "Create a user profile in Vero", "description": "", "categories": ["automation", "file-management"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0112_Manual_Awstextract_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "file-management"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0115_HubSpot_Clearbit_Update_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0116_Graphql_Discord_Automate_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "integration", "ai", "communication"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0118_Readbinaryfile_Onfleet_Create.json", "name": "Create Onfleet tasks from Spreadsheets", "description": "", "categories": ["file-management", "productivity"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0120_Manual_GoogleSheets_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 4, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0121_Respondtowebhook_Webhook_Automate_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "integration"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0122_Manual_Flow_Import_Triggered.json", "name": "Get all the tasks in Flow", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0124_Slack_Typeform_Create_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "communication", "ai", "productivity"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0128_Manual_N8Ntrainingcustomerdatastore_Automation_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "data-processing"], "nodeCount": 4, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0129_HubSpot_Cron_Update_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing"], "nodeCount": 7, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0130_HubSpot_Cron_Automate_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "automation"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0132_Mattermost_Googlecloudnaturallanguage_Send_Triggered.json", "name": "Analyze the sentiment of feedback and send a message on Mattermost", "description": "", "categories": ["automation", "productivity"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0135_GitHub_Cron_Create_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing"], "nodeCount": 6, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0136_HTTP_Googlefirebaserealtimedatabase_Update_Webhook.json", "name": "Receive updates for the position of the ISS every minute and push it to a database", "description": "", "categories": ["automation", "integration", "data-processing", "productivity"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0138_Amqp_Send_Triggered.json", "name": "Receive messages for an ActiveMQ queue via AMQP Trigger", "description": "", "categories": ["automation"], "nodeCount": 1, "complexity": "simple", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0141_Notion_Webhook_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity", "data-processing", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0142_Notion_Webhook_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "productivity", "automation", "integration"], "nodeCount": 23, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0144_HTTP_Twitter_Automation_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "social-media"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0145_Manual_Send_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0146_Functionitem_Telegram_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "communication", "integration"], "nodeCount": 8, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0147_Toggl_Create_Triggered.json", "name": "Get new time entries from Toggl", "description": "", "categories": ["automation"], "nodeCount": 1, "complexity": "simple", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0148_Awstextract_Telegram_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "communication", "ai", "productivity", "file-management"], "nodeCount": 4, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0150_Awsrekognition_GoogleSheets_Automation_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "automation"], "nodeCount": 6, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0153_HTTP_Dropbox_Update_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "data-processing", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0156_HTTP_Awsrekognition_Automation_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0157_Manual_Import_Triggered.json", "name": "Get today's date and day using the Function node", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0158_Telegram_Functionitem_Create_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "file-management", "communication", "data-processing"], "nodeCount": 11, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0159_Datetime_Functionitem_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0160_Manual_Automation_Triggered.json", "name": "Assign values to variables using the Set node", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0164_Crypto_Webhook_Automate_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "integration"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0165_Webhook_Respondtowebhook_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "integration"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0169_<PERSON><PERSON>_Profitwell_Send_Scheduled.json", "name": "Send financial metrics monthly to Mattermost", "description": "", "categories": ["automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0171_Readbinaryfiles_Code_Automation_Webhook.json", "name": "OpenAI-model-examples", "description": "", "categories": ["automation", "ai", "file-management"], "nodeCount": 27, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0173_Manual_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0175_Manual_Sendy_Create_Triggered.json", "name": "Add a subscriber to a list and create and send a campaign", "description": "", "categories": ["automation", "ai"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0178_Functionitem_Executecommand_Automation_Webhook.json", "name": "extract_swifts", "description": "", "categories": ["automation", "ai", "data-processing", "file-management"], "nodeCount": 23, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0179_Manual_Automate_Triggered.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0182_Code_GitHub_Create_Scheduled.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing", "communication"], "nodeCount": 26, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "0183_St<PERSON>i_Webhook_Automation_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["integration", "social-media", "automation", "productivity", "data-processing"], "nodeCount": 14, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0185_Shopify_Onfleet_Automation_Triggered.json", "name": "Updating Shopify tags on Onfleet events", "description": "", "categories": ["automation", "ecommerce"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0186_Quickbooks_Onfleet_Create_Triggered.json", "name": "Create a QuickBooks invoice on a new Onfleet Task creation", "description": "", "categories": ["automation"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0187_Onfleet_GoogleDrive_Create_Triggered.json", "name": "Create an Onfleet task when a file in Google Drive is updated", "description": "", "categories": ["automation", "productivity", "file-management"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0189_Manual_Quickbase_Create_Triggered.json", "name": "Create, update and get records in Quick Base", "description": "", "categories": ["automation"], "nodeCount": 6, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0191_Manual_Slack_Automation_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "communication"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0193_Nocodb_Telegram_Create_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "communication", "automation"], "nodeCount": 18, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0194_Respondtowebhook_Webhook_Import_Webhook.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "integration"], "nodeCount": 5, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0195_Manual_Pagerduty_Create_Triggered.json", "name": "Create, update, and get an incident on PagerDuty", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "0196_Openweathermap_Webhook_Automation_Webhook.json", "name": "Receive the weather information of any city", "description": "", "categories": ["automation", "integration"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "0198_Manual_Thehive_Create_Triggered.json", "name": "Create, update and get a case in TheHive", "description": "", "categories": ["automation"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "02_emailAgent.json", "name": "02 emailAgent", "description": "", "categories": ["automation", "ai", "communication"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "03_calendarAgent.json", "name": "03 calendarAgent", "description": "", "categories": ["ai", "productivity", "automation"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "04_contactagent.json", "name": "04 contactagent", "description": "", "categories": ["ai", "productivity", "automation"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "1 CLICK Viral YETI TikTok Workflow ! (Veo 3 & n8n PROMPTS included).json", "name": "Veo 3 FAL video generation", "description": "", "categories": ["automation", "ai"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "1. AI Youtube Trend Finder Based On Niche (1) (1).json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "1. AI Youtube Trend Finder Based On Niche (1).json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "1. Idea Generator Agent.json", "name": "1. POV Idea Agent", "description": "", "categories": ["ai", "productivity", "communication", "automation", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "1. LinkedIn DM Outreach System.json", "name": "1. LI Connection Request System: Trigger PhantomBuster Agent", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "1__Rag_Agent__with_Qwen2_5_-1.json", "name": "1. <PERSON><PERSON> (with <PERSON><PERSON>2.5)", "description": "", "categories": ["ai", "automation", "communication"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "1__Rag_Agent__with_Qwen2_5_.json", "name": "1. <PERSON><PERSON> (with <PERSON><PERSON>2.5)", "description": "", "categories": ["ai", "automation", "communication"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "2. Image Generator Agent.json", "name": "3. POV Image Agent", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "28_enrich_leads_n8n.json", "name": "28_enrich leads n8n", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "2__Adding_Files_From_Drive_To_Database-1.json", "name": "2. Adding Files From Drive To Database", "description": "", "categories": ["ai", "data-processing", "productivity", "automation", "file-management", "integration"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "2__Adding_Files_From_Drive_To_Database.json", "name": "2. Adding Files From Drive To Database", "description": "", "categories": ["ai", "data-processing", "productivity", "automation", "file-management", "integration"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "3. Scenes Generator Agent.json", "name": "2. POV Scenes Agent", "description": "", "categories": ["ai", "data-processing", "productivity", "automation"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "4. SFX Generator Agent.json", "name": "5. POV SFX Agent", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "5. Video Generator Agent.json", "name": "4. POV Video Agent", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI Call Center Automation.json", "name": "AI_Call_Center_Automation_v1", "description": "", "categories": ["automation", "data-processing", "ai", "communication", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": true}, {"filename": "AI Call Center Automation2.json", "name": "AI_Call_Center_Automation_v1", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "AI Career Coach.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "AI Logo - Visual Style Generator with Imagen 3.0 (n8n).json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "automation", "file-management", "ai"], "nodeCount": 21, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI Task List Generator – n8n-Daily_Tasks__List___Template.json", "name": "Daily Tasks  List - Template", "description": "", "categories": ["automation", "ai", "communication", "productivity"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "AI Video Agent.json", "name": "AI Video Agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI Video Agent2.json", "name": "AI Video Agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI Youtube Trend Finder Based On Niche (1) (2).json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI agent chat.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "integration"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI-Powered Automated Job Search & Application _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "data-processing", "automation", "productivity", "file-management", "integration"], "nodeCount": 21, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "AI-Powered Social Media Content Generator & Publisher _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "integration", "social-media", "data-processing"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI-Powered Social Media Content Generator & Publisher.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "integration", "social-media", "data-processing"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI-Powered Social Media Post Automation (n8n + Google Trends + Perplexity AI).json", "name": "Social_media_post _automation_from_google_trends_and _perplexity copy", "description": "", "categories": ["social-media", "integration", "productivity", "data-processing", "automation", "ai"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "AI-Powered Weather Impact Reporter.json", "name": "Weather Impact Report", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "integration"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "AI-SDR_system.json", "name": "AI SDR - Complete Outbound Sales Automation", "description": "", "categories": ["automation", "ai", "communication"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "AI-SDR_system1.json", "name": "AI SDR - Complete Outbound Sales Automation", "description": "", "categories": ["automation", "ai", "communication"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "AIS_calendar_assistant.json", "name": "AIS calendar assistant", "description": "", "categories": ["automation", "ai", "productivity", "communication"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "AIS_n8n_best_hacks.json", "name": "AIS n8n best hacks", "description": "", "categories": ["automation", "communication", "ai", "integration"], "nodeCount": 47, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "AI_CONTENT_RESEARCH_AND_CREATE.json", "name": "Auto Image Post Generator", "description": "", "categories": ["automation", "ai", "file-management"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI_Cold_Outreach_Scraper.json", "name": "AI Cold Outreach Scraper", "description": "", "categories": ["automation", "productivity", "ai", "integration"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "AI_Email_Agent.json", "name": "AI Email Agent", "description": "", "categories": ["ai", "automation", "communication"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI_Facebook_Ad_Spy_Tool.json", "name": "AI Facebook Ad Spy Tool", "description": "", "categories": ["automation", "data-processing", "ai", "productivity", "social-media"], "nodeCount": 25, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "AI_LOFI_Generator.json", "name": "AI LOFI Generator", "description": "", "categories": ["automation", "ai", "productivity", "file-management", "data-processing"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Ad Agency in a Box.json", "name": "Ad Agency in a box", "description": "", "categories": ["automation", "data-processing", "ai", "productivity", "integration", "social-media"], "nodeCount": 54, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": true}, {"filename": "Ad_Builder.json", "name": "Ad Builder", "description": "", "categories": ["automation", "ai", "productivity", "file-management", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Ai_Agent___Auto_select.json", "name": "Ai Agent - Auto select", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Airtable_AI_Agent_Basic.json", "name": "Airtable AI Agent", "description": "", "categories": ["ai", "automation", "productivity", "integration"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "<PERSON>, Personal AI Assistant with Telegram Voice and Text.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "ai", "automation", "communication"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Apify Youtube Scrapers.json", "name": "Youtube Scraper", "description": "", "categories": ["general"], "nodeCount": 6, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Automated Agentic News Event Monitoring with perplexity.ai _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Baby Podcast Agent (1).json", "name": "My workflow 14", "description": "", "categories": ["automation", "productivity", "ai", "file-management", "integration", "social-media", "data-processing"], "nodeCount": 59, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Baby Podcast Agent.json", "name": "My workflow 14", "description": "", "categories": ["automation", "productivity", "ai", "file-management", "integration", "social-media", "data-processing"], "nodeCount": 59, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Baby Pods.json", "name": "Baby Pods", "description": "", "categories": ["file-management", "data-processing", "ai", "productivity", "automation"], "nodeCount": 36, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Bible_Verse_Generator_Working_Copy.json", "name": "Bible Verse Generator Working Copy", "description": "", "categories": ["ai", "file-management", "automation", "productivity"], "nodeCount": 34, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Brand Voice AI Blog Generator-Blog_automation_Oendies (2).json", "name": "Blog automation Oendies", "description": "", "categories": ["ai", "data-processing", "productivity", "automation", "integration"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Brand Voice AI Blog Generator-scrape_youtube_form.json", "name": "scrape youtube form", "description": "", "categories": ["ai", "productivity", "communication", "automation"], "nodeCount": 14, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Build AN AI Agent Using GROK 4 In Minutes Using n8n (Free Template).json", "name": "sms Grok Agent", "description": "", "categories": ["ai", "automation", "communication", "productivity", "integration"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Build an MCP Server with Google Calendar and Custom Functions.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "productivity", "automation"], "nodeCount": 33, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Build your own N8N Workflows MCP Server.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing", "ai"], "nodeCount": 46, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Calendly_to_Meeting_Prep__No_Creds_.json", "name": "Calendly to Meeting Prep (No Creds)", "description": "", "categories": ["automation", "ai", "data-processing"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Call_to_Content_lead_magnet_2.json", "name": "Call-to-Content", "description": "", "categories": ["automation", "file-management", "ai", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ChatGPT_Image_Variation_Flow___Shared.json", "name": "ChatGPT Image Variation Flow - Shared", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "file-management"], "nodeCount": 23, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Chiropractor Ai Workflow.json", "name": "Chiropractor Voice AI Scheduler with AI Agent & Error Handling", "description": "", "categories": ["automation", "ai", "communication", "productivity", "integration"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Competitor Price Scraper.json", "name": "Competitor <PERSON>", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "communication", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Competitor_Facebook_Ads_Analysis.json", "name": "Competitor Facebook Ads Analysis", "description": "", "categories": ["automation", "ai", "file-management", "data-processing", "productivity", "social-media"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Complete WhatsApp AI Assistant with n8n.json", "name": "WhatsApp Agent Pro", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 20, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Content_Publishing_System_2_0_Template.json", "name": "Content Publishing System 2.0 Template", "description": "", "categories": ["integration", "social-media", "ai", "productivity", "automation", "data-processing"], "nodeCount": 28, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Copy of AI Facebook Ad Spy Tool with Apify, OpenAI, Gemini & Google Sheets.json", "name": "AI Facebook Ad Spy Tool with Apify, OpenAI, Gemini & Google Sheets", "description": "", "categories": ["automation", "data-processing", "ai", "productivity", "integration", "social-media"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of AI YouTube Analytics Agent_ Comment Analyzer & Insights Reporter (1).json", "name": "AI YouTube Analytics Agent: Comment Analyzer & Insights Reporter", "description": "", "categories": ["ai", "productivity", "automation"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of AI_Agent_Web_Scraping.json", "name": "AI Agent Web Scraping", "description": "", "categories": ["ai", "automation", "integration"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Airtable_Agent (1).json", "name": "Airtable Agent", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Apollo_Scraping.json", "name": "Apollo Scraping", "description": "", "categories": ["automation", "productivity", "integration"], "nodeCount": 6, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Automate Glassdoor Job Search with Bright Data Scraping & Google Sheets Storage.json", "name": "Automate Glassdoor Job Search with Bright Data Scraping & Google Sheets Storage", "description": "", "categories": ["automation", "ai", "productivity", "integration", "data-processing", "file-management"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Automate Sales Meeting Prep with AI &  APIFY Sent To WhatsApp.json", "name": "Automate Sales Meeting Prep with AI &  APIFY Sent To WhatsApp", "description": "", "categories": ["ai", "productivity", "automation", "data-processing", "integration"], "nodeCount": 61, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Copy of Building Your First WhatsApp Chatbot.json", "name": "Building Your First WhatsApp Chatbot", "description": "", "categories": ["automation", "ai", "data-processing", "file-management"], "nodeCount": 28, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Convert Image Files (JPG, PNG) to URLs and Reduce File Size for FREE.json", "name": "Convert Image Files (JPG, PNG) to URLs and Reduce File Size for FREE", "description": "", "categories": ["ai", "file-management"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Create an RSS feed based on a website_s content.json", "name": "Create an RSS feed based on a website's content", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Copy of Daily AI News Digest with Perplexity Pro, GPT Format & Gmail Delivery.json", "name": "Daily AI News Digest with Perplexity Pro, GPT Format & Gmail Delivery", "description": "", "categories": ["automation", "ai"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Copy of Document_Agent (1).json", "name": "Document Agent", "description": "", "categories": ["automation", "communication", "productivity", "file-management", "ai", "data-processing"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Find the Cheapest Flights Automatically with Bright Data & n8n.json", "name": "Find the Cheapest Flights Automatically with Bright Data & n8n", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Generate & Auto-post AI Videos to Social Media with Veo3 and Blotato.json", "name": "Generate & Auto-post AI Videos to Social Media with Veo3 and Blotato", "description": "", "categories": ["productivity", "automation", "ai", "social-media"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Copy of Generate AI Videos with Google Veo3, Save to Google Drive and Upload to YouTube.json", "name": "Generate AI Videos with Google Veo3, Save to Google Drive and Upload to YouTube", "description": "", "categories": ["automation", "ai", "productivity", "file-management"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Copy of Gmail_Agent (1).json", "name": "Gmail Agent", "description": "", "categories": ["ai", "automation", "productivity", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Google Maps Scraper.json", "name": "Google Maps Scraper", "description": "", "categories": ["automation", "productivity", "ai", "data-processing"], "nodeCount": 20, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Copy of Google_Calendar_Agent (1).json", "name": "Google Calendar Agent", "description": "", "categories": ["ai", "productivity", "automation", "data-processing"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Google_Maps_Scraping.json", "name": "Google Maps Scraping", "description": "", "categories": ["automation", "productivity", "integration"], "nodeCount": 5, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Instagram_Scraping.json", "name": "Instagram Scraping", "description": "", "categories": ["automation", "productivity", "integration", "social-media"], "nodeCount": 6, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Manager_Agent (1).json", "name": "Manager Agent", "description": "", "categories": ["ai", "automation", "communication", "data-processing"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Market Research & Business Case Generator with GPT-4o, Perplexity & Claude.json", "name": "Market Research & Business Case Generator with GPT-4o, Perplexity & Claude", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of OpenAI GPT-3_ Company Enrichment from website content.json", "name": "OpenAI GPT-3: Company Enrichment from website content", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Parse PDF with LlamaParse and save to Airtable.json", "name": "Parse PDF with LlamaParse and save to Airtable", "description": "", "categories": ["productivity", "automation", "ai", "integration"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Copy of Parse and Extract Data from Documents_Images with Mistral OCR.json", "name": "Parse and Extract Data from Documents/Images with Mistral OCR", "description": "", "categories": ["automation", "productivity", "data-processing"], "nodeCount": 21, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Scrape business emails from Google Maps without the use of any third party APIs.json", "name": "Scrape business emails from Google Maps without the use of any third party APIs", "description": "", "categories": ["data-processing", "productivity", "automation", "ai", "integration", "communication"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Social_Media_Agent (1).json", "name": "Social Media Agent", "description": "", "categories": ["automation", "ai", "social-media", "data-processing"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of TikTok Post Scraper via Keywords _ Bright Data + Sheets Integration.json", "name": "TikTok Post Scraper via Keywords | Bright Data + Sheets Integration", "description": "", "categories": ["productivity", "ai", "automation", "integration", "data-processing"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Voice_Agent (1) (1).json", "name": "Voice Agent", "description": "", "categories": ["automation", "ai"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Copy of Website Content Scraper & SEO Keyword Extractor with GPT-4o-mini and Airtable.json", "name": "Website Content Scraper & SEO Keyword Extractor with GPT-4o-mini and Airtable", "description": "", "categories": ["ai", "automation", "data-processing", "productivity"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Course_Creator_Agent_Team.json", "name": "Course Creator Agent Team", "description": "", "categories": ["communication", "ai", "automation", "productivity", "data-processing"], "nodeCount": 51, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Daily_Tasks__List___Template-AI Task List Generator – n8n.json", "name": "Daily Tasks  List - Template", "description": "", "categories": ["automation", "ai", "communication", "productivity"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Deep Multiline Icebreaker System-Cold emails!!.json", "name": "Deep Multiline Icebreaker System", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Deep_Company_Research_Linkedin_Workflow.json", "name": "Deep Company Research/Linkedin Workflow", "description": "", "categories": ["ai", "data-processing", "productivity", "automation", "social-media"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "DriveToRag.json", "name": "DriveToRag", "description": "", "categories": ["ai", "data-processing", "automation", "productivity", "file-management"], "nodeCount": 61, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "E_Mail_Workflow_Human_in_the_Loop (2).json", "name": "E-Mail Workflow Human in the Loop", "description": "", "categories": ["ai", "automation", "communication", "productivity", "data-processing"], "nodeCount": 25, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Effortless Job Hunting_ Let this Automation Find Your Next Role _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity", "data-processing", "ai"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Employe_Hiring_Agent.json", "name": "Hiring Agent", "description": "", "categories": ["automation", "file-management", "ai", "productivity"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Error Logger.json", "name": "<PERSON><PERSON><PERSON>", "description": "", "categories": ["automation", "productivity", "communication"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Essay Revision and Style Comments Workflow.json", "name": "Essay Revision and Style Comments Workflow", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Extract Trends, Auto-Generate Social Content with AI, Reddit and Google Trends _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "data-processing", "productivity", "social-media", "integration", "automation"], "nodeCount": 39, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Facebook_Ads_Analyzer___Creator___Shared.json", "name": "Facebook Ads Analyzer & Creator - Shared", "description": "", "categories": ["automation", "integration", "social-media", "data-processing", "ai", "productivity", "communication"], "nodeCount": 83, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": true}, {"filename": "Faceless_Shorts_Production_System.json", "name": "Faceless Shorts Production System", "description": "", "categories": ["ai", "data-processing", "automation", "productivity", "integration"], "nodeCount": 54, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Faceless_YouTube_Generator.json", "name": "Faceless YouTube Generator", "description": "", "categories": ["productivity", "ai", "data-processing", "file-management", "automation", "integration"], "nodeCount": 49, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "From news headline to Instagram post in 2 minutes, all automated! (CODE….json", "name": "News poster (ENGLISH)", "description": "", "categories": ["data-processing", "ai", "integration", "social-media", "automation"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "GMAIL_AGENT.json", "name": "GMAIL AGENT", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "G_mail_inbox_assistant.json", "name": "G-mail inbox assistant", "description": "", "categories": ["ai", "automation", "data-processing", "productivity"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Generate Trend-Based Marketing Videos with Seedance AI, Perplexity, and GPT-4o.json", "name": "Generate product videos", "description": "", "categories": ["automation", "communication", "ai"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Generate_Unlimited_Images_For_Free.json", "name": "Generate Unlimited Images For Free", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Generating Keywords using Google Autosuggest.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "automation", "ai", "integration"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Gestor_de_Erro.json", "name": "Gest<PERSON>_<PERSON>_<PERSON><PERSON>", "description": "", "categories": ["automation", "communication", "ai"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "GitHub - <PERSON> AI Logger with Vector Memory (1).json", "name": "GitHub Commit Jenkins", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "communication", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "GitHub - <PERSON> AI Logger with Vector Memory (2).json", "name": "GitHub Commit Jenkins", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "communication", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "GitHub - <PERSON> AI <PERSON>gger with Vector Memory.json", "name": "GitHub Commit Jenkins", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "communication", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "GitHub API RAG Chatbot (n8n).json", "name": "Chat with GitHub OpenAPI Specification using RAG (Pinecone and OpenAI)", "description": "", "categories": ["automation", "ai", "data-processing", "integration"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "GitLab Auto Review with ChatGPT.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "ai", "automation", "integration"], "nodeCount": 14, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Github_Backups.json", "name": "Github_Backups", "description": "", "categories": ["automation", "data-processing"], "nodeCount": 24, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Glassdoor Job Scrapper.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Gmail AI Agent.json", "name": "My workflow", "description": "", "categories": ["ai", "automation", "productivity", "communication"], "nodeCount": 25, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Gmail AI Auto-Responder (n8n + OpenAI).json", "name": "Gmail AI auto-responder: create draft replies to incoming emails", "description": "", "categories": ["ai", "automation", "communication"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Gmail_AI_Auto_Sorter_n8n_Workflow.json", "name": "My workflow", "description": "", "categories": ["ai", "automation", "data-processing"], "nodeCount": 37, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Google Maps Business Scraper with Gemini.json", "name": "Google Maps Business Scraper & Lead Enricher with Bright Data & Google Gemini", "description": "", "categories": ["automation", "ai", "productivity", "file-management", "data-processing"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Google_Gmail_Agent (1).json", "name": "🍭🤖 Google Gmail Agent", "description": "", "categories": ["ai", "communication", "automation", "productivity"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Google_Gmail_Agent.json", "name": "🍭🤖 Google Gmail Agent", "description": "", "categories": ["ai", "communication", "automation", "productivity"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Google_Maps-n8n + Apify Scraper.json", "name": "Google Maps", "description": "", "categories": ["automation", "data-processing", "productivity"], "nodeCount": 7, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Google_Maps_Scraper.json", "name": "Google Maps Scraper", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Google_Veo_Video_Generator_Main_Agent.json", "name": "Google Veo Video Generator Main Agent", "description": "", "categories": ["automation", "communication", "ai", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "GraphRAG Telegram Chatbot-n8n.json", "name": "Telegram AI Chatbot Agent with InfraNodus GraphRAG Knowledge Base", "description": "", "categories": ["ai", "automation", "communication"], "nodeCount": 21, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Grow_youtube.json", "name": "Grow youtube", "description": "", "categories": ["automation", "ai", "productivity", "file-management"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Grow_youtube2.json", "name": "Grow youtube", "description": "", "categories": ["automation", "ai", "productivity", "file-management"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "HR_Agent.json", "name": "HR Agent", "description": "", "categories": ["automation", "ai", "productivity", "file-management", "communication"], "nodeCount": 36, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "How To Call 1,000s of Leads with a Voice AI Agent (Vapi + n8n Tutorial)-check_availability-book_app.json", "name": "book app", "description": "", "categories": ["automation", "ai", "productivity", "integration"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "How To Call 1,000s of Leads with a Voice AI Agent (Vapi + n8n Tutorial)-check_availability.json", "name": "check availability", "description": "", "categories": ["automation", "ai", "productivity", "integration"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "How to Scrape Websites Without Paid APIs Using n8n .json", "name": "My workflow", "description": "", "categories": ["ai", "automation", "data-processing", "productivity"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "How to Scrape Websites Without Paid APIs Using n8n.json", "name": "My workflow", "description": "", "categories": ["ai", "automation", "data-processing", "productivity"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "I built a workflow to scrape (virtually) any news content into LLM-ready markdown (firecrawl + rss.app) _ r_n8n.json", "name": "AI Scraping Pipeline", "description": "", "categories": ["automation", "data-processing", "productivity", "file-management", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "I built an AI automation that can reverse engineer any viral AI video on TikTok_IG and will generate a prompt to re-create it with Veo 3 _ r_n8n_on_server.json", "name": "The Recap AI - Reverse Engineer Short Form AI Video", "description": "", "categories": ["automation", "file-management", "communication", "ai"], "nodeCount": 22, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "I built an AI automation that writes SEO-optimized articles using Deep Research reports (and grew my website to 200k sessions this past year) _ r_n8n.json", "name": "Content - Write Best Tools In Category Article", "description": "", "categories": ["automation", "communication", "file-management", "ai", "data-processing", "productivity"], "nodeCount": 41, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "I built an AI voice agent that replaced my entire marketing team (creates newsletter w_ 10k subs, repurposes content, generates short form videos) _ r_n8n_on_server.json", "name": "The Recap AI - Marketing Team Agent", "description": "", "categories": ["ai", "productivity", "automation", "integration"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "I built this AI Automation to write viral TikTok_IG video scripts (got over 1.8 million views on Instagram) _ r_n8n.json", "name": "The Recap AI - Short Form News Script Generator", "description": "", "categories": ["data-processing", "file-management", "ai", "automation", "communication"], "nodeCount": 42, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "IG_AIPodcastElevelabsCustom.json", "name": "IG_AIPodcastElevelabsCustom", "description": "", "categories": ["communication", "automation", "ai", "data-processing"], "nodeCount": 21, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "IG_ChatWithNotion.json", "name": "IG_ChatWithNotion", "description": "", "categories": ["ai", "automation", "communication", "productivity"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "IG_EmailAgent.json", "name": "IG_EmailAgent", "description": "", "categories": ["automation", "communication", "ai"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "IG_VoiceChangerElevelabs.json", "name": "IG_VoiceChangerElevelabs", "description": "", "categories": ["automation", "communication"], "nodeCount": 7, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "IG_VoiceToMapsScraper.json", "name": "IG_VoiceToMapsScraper", "description": "", "categories": ["automation", "communication", "ai", "data-processing", "productivity"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Indeed Job Sc<PERSON>per.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Infinite SEO blogs in minutes ( Free template ) _ r_n8n.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "file-management"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Instantly_Auto_Replier.json", "name": "Instantly Auto Replier", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Invoice & Receipt n8n Automation That WORKS FOR YOU (Beginner Friendly).json", "name": "Invoice Manager", "description": "", "categories": ["automation", "communication", "ai", "productivity", "file-management", "data-processing"], "nodeCount": 38, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Invoice from JessAutomates-AI Agent Invoice Automation (n8n Template).json", "name": "🤖Invoice Agent", "description": "", "categories": ["automation", "communication", "productivity", "ai"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Invoice from JessAutomates.json", "name": "🤖Invoice Agent", "description": "", "categories": ["automation", "communication", "productivity", "ai"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Invoice_Agent_by_RJ.json", "name": "Invoice Agent", "description": "", "categories": ["automation", "productivity", "data-processing", "ai"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Jobs_In_Your_Inbox.json", "name": "Jobs-In-Your-Inbox", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 46, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Keyword Rank Tracker Google Sheets.json", "name": "keyword_rank_tracker_google_sheets", "description": "", "categories": ["automation", "productivity", "data-processing"], "nodeCount": 30, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Keyword Rank Tracker PostGreSQL.json", "name": "Rank Tracker <PERSON><PERSON><PERSON> Template", "description": "", "categories": ["productivity", "data-processing", "automation"], "nodeCount": 28, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Lead_Generation_Agent.json", "name": "Lead Generation Agent", "description": "", "categories": ["automation", "productivity"], "nodeCount": 11, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Lead_gen.json", "name": "Lead Generation Workflow", "description": "", "categories": ["automation", "productivity", "communication"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Lead_gen_maina_fixed.json", "name": "Lead Generation Workflow", "description": "", "categories": ["automation", "productivity", "communication"], "nodeCount": 4, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Lead_gen_subworkflow.json", "name": "Enrich and Save Lead", "description": "", "categories": ["general"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Lead_gen_subworkflow_fixed.json", "name": "Enrich and Save Lead", "description": "", "categories": ["general"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "LinkedIn DM Outreach System.json", "name": "1. LI Connection Request System: Trigger PhantomBuster Agent", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "LinkedIn Jobs to Google Sheets.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "LinkedIn Lead Generation_Outreach_PROD__Lead_Gen_from_Keywords.json", "name": "[PROD] Lead Gen from Keywords", "description": "", "categories": ["ai", "productivity", "automation", "data-processing"], "nodeCount": 70, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "LinkedIn Lead Generation_Outreach_PROD__LinkedIn_FollowUps.json", "name": "[PROD] LinkedIn FollowUps", "description": "", "categories": ["ai", "productivity", "data-processing", "automation", "social-media"], "nodeCount": 39, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "LinkedIn_Post_Generator (1).json", "name": "LinkedIn Post Generator", "description": "", "categories": ["automation", "data-processing", "productivity", "ai", "social-media"], "nodeCount": 32, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "LinkedIn_Post_Generator.json", "name": "LinkedIn Post Generator", "description": "", "categories": ["automation", "data-processing", "productivity", "ai", "social-media"], "nodeCount": 32, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Linkedin_Carousel.json", "name": "<PERSON><PERSON><PERSON>l", "description": "", "categories": ["automation", "communication", "ai", "data-processing", "file-management", "social-media"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Lo-fi channel automation.json", "name": "My workflow 2", "description": "", "categories": ["automation", "ai", "file-management"], "nodeCount": 14, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "MCP n8n Agents-How I Let an AI Agent Handle My Clients’ Airbnb Inquiries (n8n Template).json", "name": "MCP Airbnb Agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "MCP n8n Agents.json", "name": "MCP Airbnb Agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "MCP_Client_Workflow.json", "name": "MCP Client", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "MCP_Server.json", "name": "MCP Server", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Meeting_Booking_Agent_by_R<PERSON>.json", "name": "My workflow 13", "description": "", "categories": ["automation", "communication", "ai", "productivity"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Miniature_shorts.json", "name": "Miniature shorts", "description": "", "categories": ["automation", "ai", "file-management", "productivity"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Model Output Analyzer.json", "name": "1. Model Output Analyzer", "description": "", "categories": ["automation", "data-processing", "ai", "productivity"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Multi Ai Agent Node.json", "name": "Multi Ai Agent Node", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 20, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Multi-Agent PDF-to-Blog Content Generation _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["data-processing", "automation", "file-management", "ai"], "nodeCount": 20, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "My workflow 2.json", "name": "My workflow 2", "description": "", "categories": ["automation"], "nodeCount": 1, "complexity": "simple", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "My workflow3.json", "name": "My workflow3", "description": "", "categories": ["automation", "data-processing", "file-management", "ai"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "My_workflow_18-Build N8N Gmail Agent.json", "name": "My workflow 18", "description": "", "categories": ["ai", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "My_workflow_18.json", "name": "My workflow 18", "description": "", "categories": ["ai", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "My_workflow_fully_cleaned.json", "name": "My workflow", "description": "", "categories": ["ai", "automation", "data-processing", "productivity"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "N8N - <PERSON><PERSON>r <PERSON>.json", "name": "<PERSON><PERSON><PERSON>", "description": "", "categories": ["automation", "productivity", "communication"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "New Lead Workflow.json", "name": "My workflow 94", "description": "", "categories": ["automation", "productivity", "ai", "data-processing"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "New_Lead___Outbound_Appointment_Setter___n8n.json", "name": "New Lead - Outbound Appointment Setter - n8n", "description": "", "categories": ["automation", "ai", "productivity", "communication", "data-processing", "integration"], "nodeCount": 32, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "News_AI_1.json", "name": "News AI", "description": "", "categories": ["ai", "communication", "automation"], "nodeCount": 14, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "News_AI_2.json", "name": "News AI 2", "description": "", "categories": ["ai", "file-management", "communication", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Onboarding_customer-Customer Onboarding Automation with HubSpot.json", "name": "Onboarding customer", "description": "", "categories": ["automation", "communication", "ai", "integration"], "nodeCount": 23, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Onboarding_customer.json", "name": "Onboarding customer", "description": "", "categories": ["automation", "communication", "ai", "integration"], "nodeCount": 23, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "OpenAI Image Generator.json", "name": "Test", "description": "", "categories": ["automation", "file-management", "productivity", "data-processing"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Orquestrador Github.json", "name": "Orquestrado<PERSON>", "description": "", "categories": ["automation", "data-processing", "communication"], "nodeCount": 24, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "POV_Video_Super_Team_Agent.json", "name": "POV Video Super Team Agent", "description": "", "categories": ["automation", "ai", "productivity", "communication", "data-processing", "integration", "social-media"], "nodeCount": 113, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": true}, {"filename": "PPC_Thievery (1).json", "name": "PPC Thievery", "description": "", "categories": ["automation", "productivity", "data-processing", "ai", "file-management"], "nodeCount": 28, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Personalized Hotel Reward Emails for High-Spenders with Salesforce, Gemini AI & Brevo.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Personalized_Email_Icebreaker_Generator.json", "name": "Personalized Email Icebreaker Generator", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "communication"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Product_video_.json", "name": "Product_video_", "description": "", "categories": ["automation", "productivity", "ai", "file-management"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Prompt Agent.json", "name": "Prompt Agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Publish_Post_to_Socials.json", "name": "Publish Post to Socials (Community)", "description": "", "categories": ["automation", "integration", "social-media"], "nodeCount": 33, "complexity": "complex", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "RAG Agent.json", "name": "RAG Agent", "description": "", "categories": ["automation", "productivity", "data-processing", "ai"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Real-Time AI Research Assistant (Serp API + GPT-4) – Powered by LangChain & n8n.json", "name": "Serp AI Agent", "description": "", "categories": ["ai", "automation", "integration"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Real_Estate_Vapi_Assistant.json", "name": "Real Estate Vapi Assistant", "description": "", "categories": ["ai", "productivity", "automation", "integration"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Reddit Marketing Insights.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "automation"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Reddit_Content_System.json", "name": "Reddit Content Ideation System", "description": "", "categories": ["automation", "data-processing", "ai", "productivity"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Reddit_Demand_Extractor.json", "name": "<PERSON><PERSON> Extractor", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Reddit_Social_Listener___Image_Gen___Shared.json", "name": "Reddit Social Listener + Image Gen - Shared", "description": "", "categories": ["ai", "automation", "data-processing", "file-management", "productivity", "social-media"], "nodeCount": 31, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Reddit_lead_gen.json", "name": "Reddit lead gen", "description": "", "categories": ["automation", "data-processing", "productivity", "ai"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Relatorio_Consolidado_Manual.json", "name": "Relatorio_Consolidado_Manual", "description": "", "categories": ["automation", "data-processing", "ai"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Relatorio_Consolidado_Por_Botao_SEGA.json", "name": "Relatorio_Consolidado_Por_Botao_SEGA", "description": "", "categories": ["automation", "ai", "communication", "integration"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Request_Publication_Response.json", "name": "Request Publication Response (Community)", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "SOP_agent.json", "name": "SOP agent", "description": "", "categories": ["ai", "data-processing", "automation", "productivity", "communication"], "nodeCount": 21, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Self_Learning_AI_Agent_Template.json", "name": "Self Learning AI Agent Template", "description": "", "categories": ["automation", "communication", "ai", "productivity", "data-processing"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Serp_AI_Agent.json", "name": "Serp AI Agent", "description": "", "categories": ["ai", "automation", "integration"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Smart_AI_Blog_Writing_System_for_n8n.json", "name": "Smart AI Blog Writing System for Gumroad Download 041225", "description": "", "categories": ["automation", "ai", "productivity", "file-management", "integration"], "nodeCount": 59, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Smart_Invoice_Followup_System.json", "name": "Smart Invoice Followup System", "description": "", "categories": ["automation", "productivity", "data-processing", "ai"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Social media content generator.json", "name": "My workflow 2", "description": "", "categories": ["ai", "productivity", "automation", "integration"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Social_Media_Automation.json", "name": "Social Media Automation", "description": "", "categories": ["social-media", "ai", "communication", "automation", "productivity", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Social_Media_Publishing_System.json", "name": "Social Media Publishing System", "description": "", "categories": ["automation", "ai", "integration", "social-media", "productivity"], "nodeCount": 20, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "SpeechTo3DModel.json", "name": "IG_SpeechTo3DModel", "description": "", "categories": ["ai", "automation", "communication", "file-management"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Stock_Analysis_AI_Agent.json", "name": "Stock Analyzer copy", "description": "", "categories": ["ai", "productivity", "automation", "communication"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Stock_Analyzer_copy.json", "name": "Stock Analyzer copy", "description": "", "categories": ["ai", "productivity", "automation", "communication"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Story Building System v1.0.0.json", "name": "🐬 Story Building System v1.0.0", "description": "", "categories": ["ai", "automation", "data-processing", "productivity"], "nodeCount": 57, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Super_Siri_Power_Up_.json", "name": "Super Siri Power Up!", "description": "", "categories": ["ai", "automation", "productivity", "integration"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "TEMPLATE__Faceless_Videos_v2_using_Leonardo (2) (1).json", "name": "TEMPLATE: Faceless Videos v2 using Leonardo", "description": "", "categories": ["productivity", "data-processing", "ai", "automation"], "nodeCount": 35, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "TEMPLATE__Faceless_Videos_v2_using_Leonardo.json", "name": "TEMPLATE: Faceless Videos v2 using Leonardo", "description": "", "categories": ["productivity", "data-processing", "ai", "automation"], "nodeCount": 35, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Telegram Voice Agent n8n Template.json", "name": "Basic Agent Prebuild - With Telegram", "description": "", "categories": ["ai", "productivity", "automation", "communication"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Telegram_Daily_Trivia.json", "name": "Telegram Daily Trivia", "description": "", "categories": ["automation", "communication", "data-processing", "ai"], "nodeCount": 41, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Template_5.3___How_I_Built_an_AI_Agent_for_Lead_Generation_Using_Google_Maps__No_Code_.json", "name": "Template 8 - How I Built an AI Agent for Lead Generation Using Google Maps (No-Code)", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "The AI-Content Virality OS.json", "name": "Instig8.AI Viral OS", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "integration"], "nodeCount": 122, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "This AI Agent Builds Prompts for Your Other AI Agents (n8n Tutorial).json", "name": "prompt-builder", "description": "", "categories": ["ai", "automation"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "This AI Extracts Text from PDFs and Images in n8n (free template).json", "name": "document-agent", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "TikTok:Instagram Shorts Generator.json", "name": "AI Facebook Ad Spy Tool", "description": "", "categories": ["automation", "data-processing", "ai", "productivity", "social-media"], "nodeCount": 25, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "TikTok_Automation_System.json", "name": "TikTok Automation System", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "TikTok_Marketing_Insights___Shared.json", "name": "TikTok Marketing Insights - Shared", "description": "", "categories": ["ai", "automation", "productivity", "data-processing"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Top_10_Video_AI_System.json", "name": "Top 10 Video AI System", "description": "", "categories": ["automation", "ai", "productivity", "communication", "data-processing"], "nodeCount": 91, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Track_X_mentions.json", "name": "Track X mentions", "description": "", "categories": ["automation", "productivity"], "nodeCount": 10, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Translate_anything.json", "name": "Translate anything", "description": "", "categories": ["automation", "productivity", "file-management"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Travel_Agent.json", "name": "Travel Agent", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Tutorial_6_1___How_I_Built_an_AI_Agent_with_DeepSeek_AI_to_Create_Faceless_YouTube_Videos__No_Code_.json", "name": "Tutorial 6.1 - How I Built an AI Agent with DeepSeek AI to Create Faceless YouTube Videos (No-Code)", "description": "", "categories": ["automation", "file-management", "data-processing", "productivity", "ai"], "nodeCount": 33, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Twitter Posts AI agent.json", "name": "My workflow 6", "description": "", "categories": ["automation", "productivity", "ai", "social-media"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Twitter Viral tweet scraping-1.json", "name": "Twitter Viral tweet scraping", "description": "", "categories": ["data-processing", "productivity", "ai", "automation", "integration", "social-media"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Twitter Viral tweet scraping.json", "name": "Twitter Viral tweet scraping", "description": "", "categories": ["data-processing", "productivity", "ai", "automation", "integration", "social-media"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Upwork Job Scrapper.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing", "communication"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "VEO_3_Posting_Agent.json", "name": "VEO 3 Posting Agent", "description": "", "categories": ["integration", "social-media", "ai", "productivity", "data-processing", "automation", "communication"], "nodeCount": 25, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "VEO_3_Video_Generator_Agent.json", "name": "VEO 3 Video Generator Agent", "description": "", "categories": ["automation", "ai", "productivity", "communication"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "V<PERSON>_<PERSON><PERSON><PERSON>___Writer___Shared.json", "name": "VSL Scraper + Writer - Shared", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Vapi_Voice_Calling.json", "name": "Vapi Voice Calling", "description": "", "categories": ["automation", "productivity", "integration"], "nodeCount": 2, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Veo 3 automation template - Fal AI workflow By hossamudin.json", "name": "Veo 3 automation", "description": "", "categories": ["automation", "ai"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Veo3_AI.json", "name": "Veo3 AI", "description": "", "categories": ["ai", "automation", "communication"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Veo3_video_s (2).json", "name": "Veo3 video's", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Veo_3_FAL_video_generation.json", "name": "Veo 3 FAL video generation", "description": "", "categories": ["automation", "ai"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Viral Tiktok Agent-Video_Automation_Agent.json", "name": "Video Automation Agent", "description": "", "categories": ["ai", "automation", "data-processing", "productivity", "integration"], "nodeCount": 52, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Virgin AI – Smart Appointment Scheduler & AI Follow-Up Bot (n8n Template) .json", "name": "Virgin ai", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 36, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Virgin AI – Smart Appointment Scheduler & AI Follow-Up Bot.json", "name": "Virgin ai", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 36, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Voice_clone.json", "name": "Voice clone", "description": "", "categories": ["automation", "productivity"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.1 Product Marketing Agent-1.json", "name": "My workflow 14", "description": "", "categories": ["communication", "ai", "automation", "productivity"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.1 Product Marketing Agent.json", "name": "My workflow 14", "description": "", "categories": ["communication", "ai", "automation", "productivity"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.2 Product Creator Agent-1.json", "name": "My workflow 14", "description": "", "categories": ["ai", "automation", "file-management", "productivity", "communication"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.2 Product Creator Agent.json", "name": "My workflow 14", "description": "", "categories": ["ai", "automation", "file-management", "productivity", "communication"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.3 Ads Agent-1.json", "name": "My workflow 14", "description": "", "categories": ["ai", "productivity", "file-management", "automation", "communication"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.3 Ads Agent.json", "name": "My workflow 14", "description": "", "categories": ["ai", "productivity", "file-management", "automation", "communication"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.4 Blog Post Agent-1.json", "name": "My workflow 14", "description": "", "categories": ["ai", "productivity", "file-management", "data-processing", "automation", "communication"], "nodeCount": 33, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "W15.4 Blog Post Agent.json", "name": "My workflow 14", "description": "", "categories": ["ai", "productivity", "file-management", "data-processing", "automation", "communication"], "nodeCount": 33, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "Web Audit & Hyper-personalised Outreach.json", "name": "Web Audit & Hyper-personalised Outreach", "description": "", "categories": ["ai", "productivity", "automation", "integration"], "nodeCount": 44, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Website_Chatbot.json", "name": "Website Chatbot", "description": "", "categories": ["ai", "automation", "productivity", "integration"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "Website_which_scrapes_Latest_Trending_AI_Products.json", "name": "Website which scrapes Latest Trending AI Products", "description": "", "categories": ["ai", "data-processing", "automation", "integration", "productivity"], "nodeCount": 38, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "WhatsApp_Social_Poster.json", "name": "WhatsApp Social Poster (Community)", "description": "", "categories": ["ai", "automation", "data-processing", "social-media"], "nodeCount": 33, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "WordPress - AI Chatbot to enhance user experience - with Supabase and OpenAI.json", "name": "RAG & GenAI App With WordPress Content", "description": "", "categories": ["automation", "ai", "data-processing", "integration"], "nodeCount": 53, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": true}, {"filename": "Workday_Journaling.json", "name": "Workday Journaling", "description": "", "categories": ["productivity", "ai", "data-processing", "automation"], "nodeCount": 31, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "<PERSON>_<PERSON><PERSON>___Shared.json", "name": "X Scaper - Shared", "description": "", "categories": ["productivity", "automation"], "nodeCount": 15, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "YouTube Video to Blog Post .json", "name": "YouTube Transcript to Blog", "description": "", "categories": ["automation", "ai", "data-processing", "productivity", "communication", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "YouTube_Content_Automation_(Faceless)_.json", "name": "YouTube Content Automation (Faceless)", "description": "", "categories": ["ai", "productivity", "automation", "data-processing"], "nodeCount": 31, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "Youtube_Posts.json", "name": "Youtube Posts", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ableton_copilot.json", "name": "ableton-copilot", "description": "", "categories": ["ai", "automation"], "nodeCount": 35, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai-agent-web-scraping-capabilities.json", "name": "Agent with custom HTTP Request", "description": "", "categories": ["ai", "automation"], "nodeCount": 20, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai-customer-feedback-sentiment-analysis.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "data-processing", "ai", "automation"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai-powered-candidate-shortlisting-automation-erpnext.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "productivity", "automation", "file-management", "data-processing", "integration"], "nodeCount": 39, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "ai-powered-email-automation-business-summarize-respond-rag.json", "name": "Email AI Auto-responder. Summerize and send email", "description": "", "categories": ["ai", "communication", "automation", "productivity", "data-processing"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai-powered-rag-workflow-stock-earnings-report-analysis.json", "name": "RAG Workflow For Stock Earnings Report Analysis", "description": "", "categories": ["ai", "productivity", "data-processing", "automation"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai-powered-social-media-content-automation.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "ai", "social-media", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai-powered-youtube-video-summarization-analysis.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing", "communication", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "ai-voice-chatbot-elevenlabs-openai-customer-service.json", "name": "Voice RAG Chatbot with ElevenLabs and OpenAI", "description": "", "categories": ["ai", "automation", "productivity", "data-processing", "integration"], "nodeCount": 23, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "ai-web-researcher-sales-prospecting.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "integration", "data-processing", "productivity"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "ai-youtube-trend-finder-based-on-niche.json", "name": "Complete Youtube", "description": "", "categories": ["ai", "automation", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai_image_studio_redacted (1).json", "name": "ai-image-studio", "description": "", "categories": ["file-management", "automation", "ai", "data-processing"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ai_scraping_pipeline.json", "name": "AI Scraping Pipeline", "description": "", "categories": ["automation", "data-processing", "productivity", "file-management", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "ai_video_workflow.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "communication"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "analyse-papers-hugging-face-ai-notion.json", "name": "Hugging Face to Notion", "description": "", "categories": ["automation", "data-processing", "ai", "productivity"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "article_by_keyword-Keyword Research Automation with Google Sheets, Suggest API, and Custom Search.json", "name": "article by keyword", "description": "", "categories": ["automation", "productivity"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "author-publish-blog-posts-google-sheets.json", "name": "Blog Automation TEMPLATE", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 35, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "automate-competitor-research-exaai-notion-ai-agents.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 39, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "automate-linkedin-posts-notion-openai.json", "name": "Automate LinkedIn Posts with AI", "description": "", "categories": ["automation", "productivity", "social-media", "data-processing", "ai"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "blinko_agent.json", "name": "blinko-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "bookstack_agent.json", "name": "bookstack-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "business-whatsapp-ai-rag-chatbot-openai.json", "name": "Business WhatsApp AI RAG Chatbot", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "integration"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "can't believe i built a $70k ai creative team in 66mins using only N8N.json", "name": "PPC Thievery", "description": "", "categories": ["automation", "productivity", "data-processing", "ai", "file-management"], "nodeCount": 28, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "chat-with-postgresql-database.json", "name": "Chat with Postgresql Database", "description": "", "categories": ["ai", "automation", "integration", "data-processing"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "cli_server_agent.json", "name": "cli-server-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "clone-viral-tiktok-ai-autopost.json", "name": "💥Clone a viral TikTok and auto-post it to 9 platforms using Perplexity & Blotato vide", "description": "", "categories": ["automation", "communication", "ai", "productivity"], "nodeCount": 41, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "customer-support-agent.json", "name": "Customer Support Agent Template", "description": "", "categories": ["ai", "automation"], "nodeCount": 11, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "dentist voice agent making $24K_yr using ElevenLabs. Handles after-hours appointment booking _ r_n8n.json", "name": "The Recap AI - Dentist Voice Agent", "description": "", "categories": ["automation", "productivity", "ai", "integration"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "end-to-end YouTube video automation workflow with HeyGen and GPT-4 avatar videos.json", "name": "End-to-End YouTube Video Automation with HeyGen & GPT-4", "description": "", "categories": ["automation", "ai", "productivity", "communication"], "nodeCount": 19, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "fetch_agent.json", "name": "fetch-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ffmpeg.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "file-management", "data-processing"], "nodeCount": 20, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "flowise_agent.json", "name": "flowise-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "forgejo_agent.json", "name": "forgejo-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "gemini_marketing.json", "name": "gemini marketing", "description": "", "categories": ["ai", "automation", "productivity", "file-management", "integration"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "generate-instagram-content-top-trends-ai-image-generation.json", "name": "Generate Instagram Content from Top Trends with AI Image Generation", "description": "", "categories": ["communication", "data-processing", "automation", "ai", "integration", "social-media"], "nodeCount": 44, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "generate-seo-seed-keywords-using-ai.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "data-processing", "automation"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "gitea_agent.json", "name": "gitea-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 39, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "handling-appointment-leads-follow-up-twilio-calcom-ai.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity"], "nodeCount": 36, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "home_assisstant_agent.json", "name": "home-assisstant-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 16, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "job scraping using LinkedIn, Indeed, Bright Data, Google Sheets _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 33, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "karakeep_agent.json", "name": "<PERSON><PERSON><PERSON>-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "langfuse_agent.json", "name": "langfuse-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "marketing_team_agent.json", "name": "The Recap AI - Marketing Team Agent", "description": "", "categories": ["ai", "productivity", "automation", "integration"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "memos_agent.json", "name": "memos-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "multi-agent:workflow system using the MCP Server:client - 3. RAG Agent Workflow.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "multi-agent:workflow system using the MCP Server:client - MCP Server Workflow.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation"], "nodeCount": 4, "complexity": "medium", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "multi-agent:workflow system using the MCP Server:client -Main Workflow (Client Side).json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "integration"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "n8n AI Agent For Customer Service- Tutorial + Free Template_Customer_Service_Agent.json", "name": "Customer Service Agent", "description": "", "categories": ["ai", "data-processing", "productivity", "communication", "automation"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "n8n ChatGPT.json", "name": "My workflow 43", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "integration"], "nodeCount": 18, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "n8n Time & Date Toolkit.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 9, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "n8n-creators-leaderboard-find-popular-workflows.json", "name": "🔥📈🤖 AI Agent  for n8n Creators Leaderboard - Find Popular Workflows", "description": "", "categories": ["data-processing", "ai", "automation", "file-management"], "nodeCount": 43, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "n8nStockAnalysis.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "n8n_ai_agent_workflow.json", "name": "AI Agent Army - Multi-Agent Orchestration System", "description": "", "categories": ["automation", "ai", "productivity", "integration"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "notion slack message.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity", "communication"], "nodeCount": 6, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "obs_agent.json", "name": "obs-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 129, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "onlyoffice_agent.json", "name": "onlyoffice-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 28, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "open-deep-research-ai-powered-autonomous-research-workflow.json", "name": "Open Deep Research - AI-Powered Autonomous Research Workflow", "description": "", "categories": ["ai", "automation", "data-processing"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "outline_agent.json", "name": "outline-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "painpointreddit.json", "name": "My workflow", "description": "", "categories": ["automation", "ai", "productivity", "data-processing"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "paperless_agent.json", "name": "paperless-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 17, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "prometheus_agent.json", "name": "prometheus-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "prompt_builder_redacted.json", "name": "prompt-builder", "description": "", "categories": ["ai", "automation"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "puppeteer_agent.json", "name": "puppeteer-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "ragflow_agent.json", "name": "ragflow-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "real_estate_assiatnt (1).json", "name": "real estate assiatnt", "description": "", "categories": ["ai", "automation", "productivity", "communication", "integration"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "reaper_agent.json", "name": "reaper-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "reaper_qa_agent.json", "name": "reaper-qa-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "redacted-linkedin-scraper (1).json", "name": "linkedin-scraper", "description": "", "categories": ["ai", "automation", "productivity", "social-media"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "redacted-linkedin-scraper.json", "name": "linkedin-scraper", "description": "", "categories": ["ai", "automation", "productivity", "social-media"], "nodeCount": 9, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "reddit_business_opportunities_identifier.json", "name": "Analyze Reddit Posts with AI to Identify Business Opportunities", "description": "", "categories": ["automation", "ai", "data-processing", "productivity"], "nodeCount": 22, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "resume-reviser.json", "name": "My workflow 2", "description": "", "categories": ["automation", "ai"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "router_agent.json", "name": "router-agent", "description": "", "categories": ["ai", "automation", "integration"], "nodeCount": 58, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "scrape (virtually) any news content into LLM-ready markdown (firecrawl + rss.app) .json", "name": "AI Scraping Pipeline", "description": "", "categories": ["automation", "data-processing", "productivity", "file-management", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "sdr1.json", "name": "sdr1", "description": "", "categories": ["automation", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "sdr2.json", "name": "sdr2", "description": "", "categories": ["automation", "productivity"], "nodeCount": 8, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "sdr3.json", "name": "sdr3", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "sdr4.json", "name": "sdr4", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "seo-writer.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "file-management"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "siyuan_agent.json", "name": "<PERSON>yuan-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "sms Grok Agent.json", "name": "sms Grok Agent", "description": "", "categories": ["ai", "automation", "communication", "productivity", "integration"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "stock identifier.json", "name": "Stock Identifier (Get Ticker Name)", "description": "", "categories": ["automation", "ai"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "stock trader.json", "name": "Stock Market Agent", "description": "", "categories": ["automation", "communication"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "system_search_agent.json", "name": "system-search-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 6, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "transcribe-audio-summarize-store-notion.json", "name": "Whisper Transkription copy", "description": "", "categories": ["automation", "productivity", "ai"], "nodeCount": 8, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "triliumnext_agent.json", "name": "triliumnext-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 10, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "twitter-virtual-ai-influencer.json", "name": "Unnamed Workflow", "description": "", "categories": ["social-media", "automation", "ai"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "twitter_account_info.json", "name": "My workflow 2", "description": "", "categories": ["automation", "integration"], "nodeCount": 3, "complexity": "medium", "hasAI": false, "hasWebhooks": true, "hasSchedule": false}, {"filename": "ui_automation_workflow.json", "name": "UI Data Automation Workflow", "description": "", "categories": ["automation", "communication", "data-processing"], "nodeCount": 15, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "ui_collector.json", "name": "UI Data Collector", "description": "", "categories": ["automation", "communication", "file-management", "data-processing"], "nodeCount": 13, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "untitled text 7.json", "name": "Course Creator Agent Team", "description": "", "categories": ["communication", "ai", "automation", "productivity", "data-processing"], "nodeCount": 51, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}, {"filename": "untitled text.json", "name": "Unnamed Workflow", "description": "", "categories": ["productivity", "ai", "automation", "communication"], "nodeCount": 15, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "upload-instagram-tiktok-google-drive.json", "name": "template in store", "description": "", "categories": ["automation", "productivity", "communication", "ai", "file-management"], "nodeCount": 13, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "upload-post.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation"], "nodeCount": 8, "complexity": "medium", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "vAssistant.json", "name": "vAssistant", "description": "", "categories": ["ai", "productivity", "automation", "integration"], "nodeCount": 26, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "whats_app_chatbot.json", "name": "whats app chatbot", "description": "", "categories": ["automation", "ai", "data-processing", "file-management"], "nodeCount": 28, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "workflow_2.json", "name": "workflow_2", "description": "", "categories": ["automation"], "nodeCount": 1, "complexity": "simple", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "write_seo_optimized_listicle_article.json", "name": "Content - Write Best Tools In Category Article", "description": "", "categories": ["automation", "communication", "file-management", "ai", "data-processing", "productivity"], "nodeCount": 41, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "youtube_agent.json", "name": "youtube-agent", "description": "", "categories": ["ai", "automation"], "nodeCount": 5, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "youtube_automation_workflow.json", "name": "YouTube Shorts Viral Automation", "description": "", "categories": ["automation", "productivity"], "nodeCount": 9, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": true}, {"filename": "zoom-ai-meeting-assistant-mail-summary-clickup-tasks-followup-call.json", "name": "Zoom AI Meeting Assistant", "description": "", "categories": ["ai", "automation", "data-processing", "file-management", "communication", "productivity"], "nodeCount": 24, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "⚡AI-Powered YouTube Video Summarization & Analysis.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "data-processing", "communication", "ai", "integration"], "nodeCount": 12, "complexity": "complex", "hasAI": true, "hasWebhooks": true, "hasSchedule": false}, {"filename": "✨ Vision-Based AI Agent Scraper - with Google Sheets, ScrapingBee, and Gemini.json", "name": "Vision-Based AI Agent Scraper - with Google Sheets, ScrapingBee, and Gemini", "description": "", "categories": ["automation", "ai", "productivity", "data-processing", "integration"], "nodeCount": 29, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": false}, {"filename": "📂 Automated OneDrive-to-Excel Finance Data Sync _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["automation", "productivity", "file-management"], "nodeCount": 14, "complexity": "complex", "hasAI": false, "hasWebhooks": false, "hasSchedule": false}, {"filename": "📈 Receive Daily Market News from FT.com to your Microsoft outlook inbox _ N8N Workflows.json", "name": "Unnamed Workflow", "description": "", "categories": ["ai", "automation", "productivity"], "nodeCount": 7, "complexity": "complex", "hasAI": true, "hasWebhooks": false, "hasSchedule": true}]}