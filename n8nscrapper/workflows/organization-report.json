{"timestamp": "2025-08-13T11:34:12.281Z", "summary": {"totalFilesFound": 528, "jsonFilesProcessed": 528, "validWorkflows": 502, "invalidWorkflows": 26, "duplicatesFound": 48, "successfullyOrganized": 454, "errors": 0}, "categories": {"automation": 497, "ai": 373, "productivity": 280, "data-processing": 196, "communication": 122, "social-media": 44, "file-management": 82, "integration": 122, "monitoring": 3, "ecommerce": 3, "general": 3}, "features": {"withAI": 364, "withWebhooks": 82, "withSchedule": 91}, "complexity": {"simple": 6, "medium": 90, "complex": 406}, "topNodeTypes": {"n8n-nodes-base.httpRequest": 244, "n8n-nodes-base.stickyNote": 221, "@n8n/n8n-nodes-langchain.agent": 217, "n8n-nodes-base.set": 210, "n8n-nodes-base.if": 146, "@n8n/n8n-nodes-langchain.lmChatOpenAi": 145, "n8n-nodes-base.manualTrigger": 137, "n8n-nodes-base.code": 118, "n8n-nodes-base.googleSheets": 115, "n8n-nodes-base.merge": 84}, "errors": []}