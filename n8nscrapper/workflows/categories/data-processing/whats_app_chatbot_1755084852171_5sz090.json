{"name": "whats app chatbot", "nodes": [{"parameters": {"updates": ["messages"], "options": {}}, "id": "c901c22b-4b8e-418c-a202-d7479187562b", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.whatsAppTrigger", "position": [-480, 420], "webhookId": "aaa71f03-f7af-4d18-8d9a-0afb86f1b554", "typeVersion": 1, "credentials": {"whatsAppTriggerApi": {"id": "oFlBXOjQU8q6n1bt", "name": "WhatsApp OAuth account"}}}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "4102b2d6-3c81-47af-85b5-a4d62ffbe6b4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [120, 500], "typeVersion": 1, "credentials": {"openAiApi": {"id": "yDug1IfnPUUdTKN6", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "=whatsapp-75-{{ $json.messages[0].from }}"}, "id": "f247c9c7-5383-4d41-8aa9-a09ea929789b", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [240, 500], "typeVersion": 1.2}, {"parameters": {"name": "query_product_brochure", "description": "Call this tool to query the product brochure. Valid for the year 2024."}, "id": "64d16d20-85b0-437a-9dda-b996361b20e9", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [360, 500], "typeVersion": 1}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "8922b698-a4c8-40dd-8b88-6c902307eb33", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [360, 780], "typeVersion": 1, "credentials": {"openAiApi": {"id": "yDug1IfnPUUdTKN6", "name": "OpenAi account"}}}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "e59512d1-4bc2-48ff-9c5f-fb4f425fbc72", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [600, 640], "typeVersion": 1, "credentials": {"openAiApi": {"id": "yDug1IfnPUUdTKN6", "name": "OpenAi account"}}}, {"parameters": {}, "id": "514d6850-010b-41b5-8f36-2c356cbf3a70", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-1840, 460], "typeVersion": 1}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "53ce6885-63b3-4ace-8683-debce948f049", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-1220, 680], "typeVersion": 1, "credentials": {"openAiApi": {"id": "yDug1IfnPUUdTKN6", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Extract from File').item.json.text }}", "options": {}}, "id": "5eb2e12b-4562-43f1-9795-eaa5d604af85", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [-980, 620], "typeVersion": 1}, {"parameters": {"chunkSize": 2000, "chunkOverlap": {}, "options": {}}, "id": "64dc1e5b-3fe7-4236-9c2c-d8530d65d910", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [-860, 760], "typeVersion": 1}, {"parameters": {"operation": "pdf", "options": {}}, "id": "09cbc38e-edc3-48f7-a126-ff331cb67f0c", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [-1480, 460], "typeVersion": 1}, {"parameters": {"url": "https://usa.yamaha.com/files/download/brochure/1/1474881/Yamaha-Powered-Loudspeakers-brochure-2024-en-web.pdf", "options": {}}, "id": "cdae66fd-2adf-4026-b258-b86b0b5aa736", "name": "get Product Brochure", "type": "n8n-nodes-base.httpRequest", "position": [-1660, 460], "typeVersion": 4.2}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "={{ $json.output }}", "additionalFields": {"previewUrl": false}}, "id": "02abf685-509b-4f09-ba3b-e4f5aff56eec", "name": "Reply To User", "type": "n8n-nodes-base.whatsApp", "position": [980, 420], "typeVersion": 1, "webhookId": "6e8bb40d-cb69-4a23-b0e6-2848df06e86f", "credentials": {"whatsAppApi": {"id": "nAlekav2ib1QsMhC", "name": "WhatsApp account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "=I'm unable to process non-text messages. Please send only text messages. Thanks!", "additionalFields": {"previewUrl": false}}, "id": "69499fbf-439d-4370-a66b-ea4d1751d5da", "name": "Reply To User1", "type": "n8n-nodes-base.whatsApp", "position": [-240, 780], "typeVersion": 1, "webhookId": "c7bc433b-df27-463a-9c8c-bb133c1bc11f", "credentials": {"whatsAppApi": {"id": "nAlekav2ib1QsMhC", "name": "WhatsApp account"}}}, {"parameters": {"memoryKey": "whatsapp-75"}, "id": "3538a7cb-57e4-4eca-87f4-378f25377927", "name": "Product Catalogue", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [360, 640], "typeVersion": 1}, {"parameters": {"content": "## 1. Download Product Brochure PDF\n[Read more about the HTTP Request Tool](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest)\n\nImport your marketing PDF document to build your vector store. This will be used as the knowledgebase by the Sales AI Agent.\n\nFor this demonstration, we'll use the HTTP request node to import the YAMAHA POWERED LOUDSPEAKERS 2024 brochure ([Source](https://usa.yamaha.com/files/download/brochure/1/1474881/Yamaha-Powered-Loudspeakers-brochure-2024-en-web.pdf)) and an Extract from File node to extract the text contents. ", "height": 434.6875, "width": 640.4375, "color": 7}, "id": "7968c092-42ac-4f4e-8554-b583dbe3821b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1920, 220], "typeVersion": 1}, {"parameters": {"content": "## 2. Create Product Brochure Vector Store\n[Read more about the In-Memory Vector Store](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreinmemory/)\n\nVector stores are powerful databases which serve the purpose of matching a user's questions to relevant parts of a document. By creating a vector store of our product catalog, we'll allow users to query using natural language.\n\nTo keep things simple, we'll use the **In-memory Vector Store** which comes built-in to n8n and doesn't require a separate service. For production deployments, I'd recommend replacing the in-memory vector store with either [Qdrant](https://qdrant.tech) or [Pinecone](https://pinecone.io).", "height": 731.1875, "width": 614.6875, "color": 7}, "id": "f490b5dc-4482-454a-860a-4927643c73db", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-1260, 220], "typeVersion": 1}, {"parameters": {"mode": "insert", "memoryKey": "whatsapp-75", "clearStore": true}, "id": "f7d6a6b6-76dc-40f4-b0e8-bfe1b1f98304", "name": "Create Product Catalogue", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [-1060, 480], "typeVersion": 1}, {"parameters": {"content": "## 3. Use the WhatsApp Trigger\n[Learn more about the WhatsApp Trigger](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.whatsapptrigger/)\n\nThe WhatsApp Trigger allows you to receive incoming WhatsApp messages from customers. It requires a bit of setup so remember to follow the documentation carefully! Once ready however, it's quite easy to build powerful workflows which are easily accessible to users.\n\nNote that WhatsApp can send many message types such as audio and video so in this demonstration, we'll filter them out and just accept the text messages.", "height": 484.1875, "width": 546.6875, "color": 7}, "id": "ac3dfb16-072a-429a-bf2c-bdc9b47779df", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-580, 140], "typeVersion": 1}, {"parameters": {"content": "### Want to handle all message types?\nSend me a dm on linkedin, or check out this link: https://www.skool.com/ai-mastermind-4906/about?ref=8cd17393744f4f9ab7e27b976dd7ca52", "height": 92, "width": 338}, "id": "6ca6ebdc-75d6-4347-a5cd-6fa5acd4dd42", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-320, 1000], "typeVersion": 1}, {"parameters": {"content": "### 3a. Handle Unsupported Message Types\nFor non-text messages, we'll just reply with a simple message to inform the sender.", "height": 311.1875, "width": 337.6875, "color": 7}, "id": "40a6ca5a-51d1-47b9-8304-94b3e379dba1", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-360, 640], "typeVersion": 1}, {"parameters": {"content": "## 4. Sales AI Agent Responds To Customers\n[Learn more about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nn8n's AI agents are powerful nodes which make it incredibly easy to use state-of-the-art AI in your workflows. Not only do they have the ability to remember conversations per individual customer but also tap into resources such as our product catalogue vector store to pull factual information and data for every question.\n\nIn this demonstration, we use an AI agent which is directed to help the user navigate the product brochure. A Chat memory subnode is attached to identify and keep track of the customer session. A Vector store tool is added to allow the Agent to tap into the product catalogue knowledgebase we built earlier.", "height": 909, "width": 747, "color": 7}, "id": "c8c6e08d-b279-45f1-b9db-db49bb53e39c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "typeVersion": 1}, {"parameters": {"content": "## 5. Repond to WhatsApp User\n[Learn more about the WhatsApp Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.whatsapp/)\n\nThe WhatsApp node is the go-to if you want to interact with WhatsApp users. With this node, you can send text, images, audio and video messages as well as use your WhatsApp message templates.\n\nHere, we'll keep it simple by replying with a text message which is the output of the AI agent.", "height": 484.1875, "width": 495.4375, "color": 5}, "id": "1f57bb44-f8f4-46b9-ba2c-998801fb6d2a", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [780, 140], "typeVersion": 1}, {"parameters": {"content": "## Test it out!\n\n### This n8n template builds a simple WhatsApp chabot acting as a Sales Agent. The Agent is backed by a product catalog vector store to better answer user's questions.\n\n* This template is in 2 parts: creating the product catalog vector store and building the WhatsApp AI chatbot.\n* A product brochure is imported via HTTP request node and its text contents extracted.\n* The text contents are then uploaded to the in-memory vector store to build a knowledgebase for the chatbot.\n* A WhatsApp trigger is used to capture messages from customers where non-text messages are filtered out.\n* The customer's message is sent to the AI Agent which queries the product catalogue using the vector store tool.\n* The Agent's response is sent back to the user via the WhatsApp node.\n\n50+ n8n templates: https://www.skool.com/ai-mastermind-4906/about?ref=8cd17393744f4f9ab7e27b976dd7ca52\n", "height": 582.6283033962263, "width": 401.25}, "id": "5f9e2907-33c2-4962-a6d1-2432874116fe", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-2360, 80], "typeVersion": 1}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "text"}]}, "renameOutput": true, "outputKey": "Supported"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "89971d8c-a386-4e77-8f6c-f491a8e84cb6", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "text"}]}, "renameOutput": true, "outputKey": "Not Supported"}]}, "options": {}}, "id": "b94dd783-1e3c-4aea-aa6b-e8a093c3d604", "name": "Handle Message Types", "type": "n8n-nodes-base.switch", "position": [-280, 420], "typeVersion": 3.2}, {"parameters": {"content": "### You only have to run this part once!\nRun this step to populate our product catalogue vector. Run again if you want to update the vector store with a new version.", "height": 114.53583720930231, "width": 345.10906976744184, "color": 5}, "id": "4d1250ba-6cc5-4e53-a4b5-1fba1db720f2", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1920, 680], "typeVersion": 1}, {"parameters": {"content": "### Activate your workflow to use!\nTo start using the WhatsApp chatbot, you'll need to activate the workflow. If you are self-hosting ensure WhatsApp is able to connect to your server.", "height": 107.02804651162779, "width": 364.6293255813954, "color": 5}, "id": "e510af69-ae5f-4c6f-a5e5-e28258915c9d", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1000, 100], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "={{ $json.messages[0].text.body }}", "options": {"systemMessage": "You are an assistant working for a company who sells Yamaha Powered Loudspeakers and helping the user navigate the product catalog for the year 2024. Your goal is not to facilitate a sale but if the user enquires, direct them to the appropriate website, url or contact information.\n\nDo your best to answer any questions factually. If you don't know the answer or unable to obtain the information from the datastore, then tell the user so."}}, "id": "d91aac33-70da-4ab6-958d-ca9a9905a650", "name": "AI Sales Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [120, 300], "typeVersion": 1.6}], "pinData": {}, "connections": {"AI Sales Agent": {"main": [[{"node": "Reply To User", "type": "main", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Handle Message Types", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Product Catalogue", "type": "ai_embedding", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Create Product Catalogue", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Sales Agent", "type": "ai_languageModel", "index": 0}]]}, "Product Catalogue": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Sales Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Create Product Catalogue", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Create Product Catalogue", "type": "ai_document", "index": 0}]]}, "Handle Message Types": {"main": [[{"node": "AI Sales Agent", "type": "main", "index": 0}], [{"node": "Reply To User1", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Sales Agent", "type": "ai_memory", "index": 0}]]}, "get Product Brochure": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "get Product Brochure", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9806295c-837d-4c26-acc2-1b304baf29d6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c8dfd55f67b4d6b2b5132f61909c9632ab06de1d6ae43197a9a9463f3887029f"}, "id": "dQwntFsCoi9UVfzy", "tags": []}