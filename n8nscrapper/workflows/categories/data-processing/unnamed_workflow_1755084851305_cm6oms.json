{"nodes": [{"id": "9320d08a-4868-4103-abdf-3f8f54a7a0a0", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [0, 0], "webhookId": "9024e29e-9080-4cf5-9a6b-0d918468f195", "parameters": {"path": "ytube", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "a5cc8922-8124-4269-9cfd-e891b29cc2b7", "name": "YouTube Transcript", "type": "n8n-nodes-youtube-transcription.youtubeTranscripter", "position": [800, 0], "parameters": {}, "typeVersion": 1}, {"id": "ff3c0fd1-36d8-4d64-b405-0600efd4d93b", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [200, 260], "parameters": {"options": {}, "fieldToSplitOut": "transcript"}, "typeVersion": 1}, {"id": "423276e0-81bf-487a-bbdd-26e9b84fa755", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1200, 140], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "27344649-8029-48ae-867b-7363d904fc59", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1200, 380], "parameters": {"text": "={{ $json.title }}\n{{ $json.youtubeUrl }}", "additionalFields": {"parse_mode": "HTML", "appendAttribution": false}}, "typeVersion": 1.2}, {"id": "230c0325-d22a-4070-9460-748a6fef48d5", "name": "Get YouTube URL", "type": "n8n-nodes-base.set", "position": [200, 0], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3ee42e4c-3cee-4934-97e7-64c96b5691ed", "name": "youtubeUrl", "type": "string", "value": "={{ $json.body.youtubeUrl }}"}]}}, "typeVersion": 3.4}, {"id": "420e90c3-9dfa-4f41-825a-9874b5ebe43a", "name": "YouTube Video ID", "type": "n8n-nodes-base.code", "position": [400, 0], "parameters": {"jsCode": "const extractYoutubeId = (url) => {\n // Regex pattern that matches both youtu.be and youtube.com URLs\n const pattern = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n const match = url.match(pattern);\n return match ? match[1] : null;\n};\n\n// Input URL from previous node\nconst youtubeUrl = items[0].json.youtubeUrl; // Adjust this based on your workflow\n\n// Process the URL and return the video ID\nreturn [{\n json: {\n videoId: extractYoutubeId(youtubeUrl)\n }\n}];\n"}, "typeVersion": 2}, {"id": "a4171c3e-1ff2-40de-af7f-b3971a1ebe79", "name": "Get YouTube Video", "type": "n8n-nodes-base.youTube", "position": [600, 0], "parameters": {"options": {}, "videoId": "={{ $json.videoId }}", "resource": "video", "operation": "get"}, "typeVersion": 1}, {"id": "73e6bfc5-8b62-4880-acd4-292f2f692540", "name": "gpt-4o-mini", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [620, 440], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "ea14e296-b30c-46f7-b283-746822ae1af4", "name": "Summarize & Analyze Transcript", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [600, 260], "parameters": {"text": "=Please analyze the given text and create a structured summary following these guidelines:\n\n1. Break down the content into main topics using Level 2 headers (##)\n2. Under each header:\n - List only the most essential concepts and key points\n - Use bullet points for clarity\n - Keep explanations concise\n - Preserve technical accuracy\n - Highlight key terms in bold\n3. Organize the information in this sequence:\n - Definition/Background\n - Main characteristics\n - Implementation details\n - Advantages/Disadvantages\n4. Format requirements:\n - Use markdown formatting\n - Keep bullet points simple (no nesting)\n - Bold important terms using **term**\n - Use tables for comparisons\n - Include relevant technical details\n\nPlease provide a clear, structured summary that captures the core concepts while maintaining technical accuracy.\n\nHere is the text: {{ $json.concatenated_text\n }}", "promptType": "define"}, "typeVersion": 1.4}, {"id": "90e3488f-f854-483e-9106-a5760d0c0457", "name": "Concatenate", "type": "n8n-nodes-base.summarize", "position": [400, 260], "parameters": {"options": {}, "fieldsToSummarize": {"values": [{"field": "text", "separateBy": " ", "aggregation": "concatenate"}]}}, "typeVersion": 1}, {"id": "9c5c249c-5eeb-4433-ba93-ace4611f4858", "name": "Response Object", "type": "n8n-nodes-base.set", "position": [960, 260], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "bf132004-6636-411f-9d85-0c696fda84c4", "name": "summary", "type": "string", "value": "={{ $json.text }}"}, {"id": "63c8d0e3-685c-488a-9b45-363cf52479ea", "name": "topics", "type": "array", "value": "=[]"}, {"id": "171f30cf-34e9-42f3-8735-814024bfde0b", "name": "title", "type": "string", "value": "={{ $('Get YouTube Video').item.json.snippet.title }}"}, {"id": "7f26f5a3-e695-49d1-b1e8-9260c31f1b3d", "name": "description", "type": "string", "value": "={{ $('Get YouTube Video').item.json.snippet.description }}"}, {"id": "d0594232-cb39-453c-b015-3b039c098e1f", "name": "id", "type": "string", "value": "={{ $('Get YouTube Video').item.json.id }}"}, {"id": "17b6ca08-ce89-4467-bd25-0d2d182f7a8b", "name": "youtubeUrl", "type": "string", "value": "={{ $('Webhook').item.json.body.youtubeUrl }}"}]}}, "typeVersion": 3.4}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Get YouTube URL", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Concatenate", "type": "main", "index": 0}]]}, "Concatenate": {"main": [[{"node": "Summarize & Analyze Transcript", "type": "main", "index": 0}]]}, "gpt-4o-mini": {"ai_languageModel": [[{"node": "Summarize & Analyze Transcript", "type": "ai_languageModel", "index": 0}]]}, "Get YouTube URL": {"main": [[{"node": "YouTube Video ID", "type": "main", "index": 0}]]}, "Response Object": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}, {"node": "Telegram", "type": "main", "index": 0}]]}, "YouTube Video ID": {"main": [[{"node": "Get YouTube Video", "type": "main", "index": 0}]]}, "Summarize & Analyze Transcript": {"main": [[{"node": "Response Object", "type": "main", "index": 0}]]}}}