{"name": "Instig8.AI Viral OS", "nodes": [{"parameters": {"path": "8e5a980f-3470-4557-8121-222ab7b51650", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2360, 1075], "id": "1a721b8d-d1cd-40d4-ba68-66268cd50bec", "name": "Webhook", "webhookId": "8e5a980f-3470-4557-8121-222ab7b51650"}, {"parameters": {"base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl1PTB9xGIlHywy4", "mode": "list", "cachedResultName": "🤖 Automations", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl1PTB9xGIlHywy4"}, "id": "={{ $json.query.recordId }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-2140, 1075], "id": "15fd782a-cec5-4ea0-bfde-f2c56001ce23", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Airtable').item.json['Type of Automation'] }}", "rightValue": "🔨 Run Scraper", "operator": {"type": "string", "operation": "equals"}, "id": "ea35f04c-9862-484c-a3f6-bb3f9313c62c"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "13f57bd8-45a1-4c7f-9356-407b966cb804", "leftValue": "={{ $('Airtable').item.json['Type of Automation'] }}", "rightValue": "📄 Analyze Engagement Signals", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "40d56791-f56b-4f71-8371-286675ac226a", "leftValue": "={{ $('Airtable').item.json['Type of Automation'] }}", "rightValue": "🏆 Generate Viral Playbook", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1480, 1075], "id": "e4b160d2-502a-4975-893b-ebbe47078084", "name": "Switch1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "053bad4e-0ada-46dd-a33c-49f36986e93c", "leftValue": "={{ $('Airtable').item.json['Social Channel'] }}", "rightValue": "TikTok", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1260, -25], "id": "97efb465-13dc-4acf-91c1-7f9891ef6c8b", "name": "TikTok"}, {"parameters": {"assignments": {"assignments": [{"id": "ce739599-4f38-4855-86b7-dde281bfec11", "name": "numPosts", "value": "={{ $('Airtable').item.json[\"Quantity to Scrape\"] }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1700, 1075], "id": "80040210-8d3f-4ce8-a19d-18d43444656b", "name": "<PERSON>", "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Initialize an empty array to hold all the post URLs\nconst postURLs = [];\n\n// Loop through all items\nfor (const item of items) {\n  // Get the web video URL from each item\n  const url = item.json[\"webVideoUrl\"];\n  \n  // If there is a URL, add it to our array\n  if (url) {\n    postURLs.push(url);\n  }\n}\n\n// Return a single item with all URLs in an array\nreturn [\n  {\n    json: {\n      postURLs\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4, -100], "id": "ba6b073a-a5ca-4f6e-8270-891edc5db4c9", "name": "Code1"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "videoWebUrl", "field2": "webVideoUrl"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [734, -25], "id": "44f110bb-2633-4be3-9860-3ad9a71b71cd", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// Initialize an empty array to hold all the post URLs\nconst postURLs = [];\n\n// Loop through all items\nfor (const item of items) {\n  // Get the web video URL from each item\n  const url = item.json[\"url\"];\n  \n  // If there is a URL, add it to our array\n  if (url) {\n    postURLs.push(url);\n  }\n}\n\n// Return a single item with all URLs in an array\nreturn [\n  {\n    json: {\n      postURLs\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [216, 225], "id": "90f28ace-6917-4a5c-8d02-386533cee1e4", "name": "Code3"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "postUrl", "field2": "url"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1110, 325], "id": "e30e8f7e-11c2-4f13-b98d-d682f54bcb0e", "name": "Merge1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "053bad4e-0ada-46dd-a33c-49f36986e93c", "leftValue": "={{ $('Airtable').item.json['Social Channel'] }}", "rightValue": "Instagram", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1260, 325], "id": "f04f6a6c-53dd-4a4d-b866-dd19ef1ff718", "name": "Instagram"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-224, 325], "id": "d2e393b1-97cc-4879-8182-a8e1fddd4259", "name": "Merge2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "1ca93f09-641e-4f11-b7e5-fe06cf774c6d", "leftValue": "={{ $json.url }}", "rightValue": "=^https:\\/\\/www\\.instagram\\.com\\/(p|reel)\\/[^\\/]+\\/?$", "operator": {"type": "string", "operation": "regex"}}], "combinator": "and"}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-4, 325], "id": "afaf7d19-f299-4d36-bda2-33e0e98af14b", "name": "Filter for False Posts"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tblaAgdwupzCJCCwq"}, "filterByFormula": "AND({Channel}='YouTube',{Active}='Active')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, 725], "id": "********-4f5e-49a0-8091-a20684589c79", "name": "Search YouTube Inputs", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tblaAgdwupzCJCCwq"}, "filterByFormula": "AND({Channel}='Instagram',{Active}='Active')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, 325], "id": "892b08c8-a380-416e-a424-5cd297c8bcc2", "name": "Search Instagram Inputs", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tblaAgdwupzCJCCwq"}, "filterByFormula": "AND({Channel}='TikTok',{Active}='Active')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, -25], "id": "f40afaa4-151e-42fe-8df2-ea2a838e1c0d", "name": "Search TikTok Inputs", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"jsCode": "// Initialize an empty array to hold all the startUrl objects\nconst startUrls = [];\n\n// Loop through all items\nfor (const item of items) {\n  // Get the web video URL from each item\n  const url = item.json[\"url\"];\n  \n  // If there is a URL, add it as an object to our array\n  if (url) {\n    startUrls.push({\n      url: url,\n      method: \"GET\"\n    });\n  }\n}\n\n// Return a single item with the properly structured startUrls array\nreturn [\n  {\n    json: {\n      startUrls\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-220, 620], "id": "dac9d955-32d9-48f2-938a-f6e20d754fe3", "name": "Create JSON"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "pageUrl", "field2": "url"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [436, 725], "id": "978bd4d7-bcb9-4103-a4b9-0b55072261bf", "name": "Merge3"}, {"parameters": {"jsCode": "const hashtags = [];\nconst profiles = [];\n\nconst numPosts = ($items(\"Twitter\")[0]?.json?.numPosts) ?? null;\n\nfor (const item of items) {\n  const type = item.json[\"Type\"] || \"\";\n  const value = item.json[\"Hashtag/ Username\"];\n\n  if (!value) continue;\n\n  if (type.includes(\"Profile\")) {\n    profiles.push(value);\n  } else if (type.includes(\"Hashtag\")) {\n    hashtags.push(value);\n  }\n}\n\n// 👇 Clean one-item output\nreturn [\n  {\n    json: {\n      hashtags,\n      profiles,\n      numPosts\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, 1075], "id": "bd5d91cd-fb86-4682-930a-1d8b6ec9ee7f", "name": "Code5", "onError": "continueRegularOutput"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "replyText", "separateBy": "\n"}]}, "fieldsToSplitBy": "postUrl", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [216, 1000], "id": "f10b60ab-c4cc-43e5-be7a-e1598d8d2277", "name": "Summarize3"}, {"parameters": {"jsCode": "// Initialize an empty array to hold all the post URLs\nconst postURLs = [];\n\n// Loop through all items\nfor (const item of items) {\n  // Get the web video URL from each item\n  const url = item.json[\"url\"];\n  \n  // If there is a URL, add it to our array\n  if (url) {\n    postURLs.push(url);\n  }\n}\n\n// Return a single item with all URLs in an array\nreturn [\n  {\n    json: {\n      postURLs\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-224, 1000], "id": "50427a7e-7081-4e2a-8d7d-d0e723517716", "name": "Create JSON1"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "postUrl", "field2": "url"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [436, 1075], "id": "18137748-4feb-47bc-b15d-fe7c1bdbd380", "name": "Merge4"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Likes": "={{ $('Merge4').item.json.likeCount }}", "Saves": "={{ $('Merge4').item.json.bookmarkCount }}", "Comments": "={{ $('Merge4').item.json.replyCount }}", "ID": "={{ $('Merge4').item.json.id }}", "Vid URL": "={{ $('Merge4').item.json.url }}", "Views": "={{ $('Merge4').item.json.viewCount }}", "Video Created Time": "={{ $('Merge4').item.json.createdAt }}", "Caption": "={{ $('Merge4').item.json.fullText }}", "Key Insights": "={{ $('Performs Sentimental Analysis4').item.json.message.content.key_insights }}", "Common Questions": "={{ $('Performs Sentimental Analysis4').item.json.message.content.common_questions.join('\\n') }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis4').item.json.message.content.overall_sentiment.toString() }}", "Channel": "Twitter", "Cover": "=[{\n  \"url\": \"{{ $('Merge4').item.json.media[0] }}\"\n}]", "Users": "={{ $('Merge4').item.json.author.userName }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis4').item.json.message.content.tool_usefulness.toString() }}", "Hashtags": "=", "Post Type": "={{ $('Merge4').item.json.type }}", "Title": "=", "Language": "={{ $('Merge4').item.json.lang }}", "Retweets": "={{ $('Merge4').item.json.retweetCount }}", "Quote Count": "={{ $('Merge4').item.json.quoteCount }}", "Quotes": 0, "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": false}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1486, 1075], "id": "708bfa3c-d6c5-4793-942f-d29bd980b9c1", "name": "Airtable8", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tblaAgdwupzCJCCwq"}, "filterByFormula": "AND({Channel}='Twitter',{Active}='Active')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, 1075], "id": "c34c9a28-708f-459b-86d8-c70eed72c2e6", "name": "Search Twitter Inputs", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "053bad4e-0ada-46dd-a33c-49f36986e93c", "leftValue": "={{ $('Airtable').item.json['Social Channel'] }}", "rightValue": "YouTube", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1260, 725], "id": "32e8e865-20de-4a60-9c3b-3424adf33974", "name": "YouTube"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "053bad4e-0ada-46dd-a33c-49f36986e93c", "leftValue": "={{ $('Airtable').item.json['Social Channel'] }}", "rightValue": "Twitter", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1260, 1075], "id": "10edbc3a-d6c7-4be1-8a73-b9cabed74a65", "name": "Twitter"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Channel}='LinkedIn',{Active}='Active')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, 1550], "id": "de9cc809-317b-4fd3-a8fe-cba24afec899", "name": "Search LinkedIn Inputs1", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "053bad4e-0ada-46dd-a33c-49f36986e93c", "leftValue": "={{ $('Airtable').item.json['Social Channel'] }}", "rightValue": "Facebook", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1260, 2200], "id": "5228cf1b-96ad-45b6-bf9c-166c5e7affc7", "name": "Facebook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "053bad4e-0ada-46dd-a33c-49f36986e93c", "leftValue": "={{ $('Airtable').item.json['Social Channel'] }}", "rightValue": "LinkedIn", "operator": {"type": "array", "operation": "contains", "rightType": "any"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1260, 1550], "id": "93ae8859-a143-4627-83c4-7bf400faee50", "name": "LinkedIn"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Channel}='Facebook',{Active}='Active')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, 2200], "id": "bec05ca3-74c6-4828-9fb5-2a611e9bd444", "name": "Search Facebook Inputs", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"jsCode": "// input: `items` (array of Make bundles)\nconst hashtags = [];\nconst profiles = [];\n\nconst numPosts = ($items(\"Facebook\")[0]?.json?.numPosts) ?? null;\n\n// build profiles + hashtags list\nfor (const { json } of items) {\n  const type   = (json.Type || \"\").toLowerCase();\n  const handle = (json[\"Hashtag/ Username\"] || \"\").trim();\n  if (!handle) continue;\n\n  if (type.includes(\"profile\")) {\n    const url = /^https?:\\/\\//i.test(handle)\n      ? handle\n      : `https://www.facebook.com/${handle.replace(/^@?\\/?/, \"\")}`;\n    profiles.push({ url, method: \"GET\" });\n  } else if (type.includes(\"hashtag\")) {\n    hashtags.push(handle);\n  }\n}\n\nreturn [\n  { json: { hashtags, profiles, numPosts } }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, 2200], "id": "a9c54104-60ae-4d70-9f1f-b5168bea70c5", "name": "Code7"}, {"parameters": {"jsCode": "const hashtags = [];\nconst profiles = [];\n\n// get numPosts straight from the LinkedIn If node (first item)\nconst numPosts = ($items(\"LinkedIn\")[0]?.json?.numPosts) ?? null;\n\n// loop through Airtable search results\nfor (const { json } of $items(\"Search LinkedIn Inputs1\")) {\n  const type   = (json.Type || \"\").toLowerCase();\n  const handle = json[\"Hashtag/ Username\"];\n  if (!handle) continue;\n\n  if (type.includes(\"profile\"))\n    profiles.push(`https://linkedin.com/in/${handle}`);\n  else if (type.includes(\"company\"))\n    profiles.push(`https://www.linkedin.com/company/${handle}`);\n  else if (type.includes(\"hashtag\"))\n    hashtags.push(handle);\n}\n\nreturn [{\n  json: { hashtags, profiles, numPosts }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, 1550], "id": "a38aded7-639a-45b6-993d-e46e1004f08a", "name": "Code6"}, {"parameters": {"fieldToSplitOut": "hashtags", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-224, 1700], "id": "646a4d07-68c2-4505-8b93-2122ad927ea3", "name": "Split Out"}, {"parameters": {"jsCode": "// Build a unified list of post links\nconst postURLs = [];\n\nfor (const { json } of items) {\n  const link = json.url || json.post_url; // either field may hold the link\n  if (link) postURLs.push(link);\n}\n\n// Return Make‑style single‑bundle output\nreturn [\n  {\n    json: { postURLs }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4, 1325], "id": "b728c19d-14d5-4d2f-9461-904850875c74", "name": "Create <PERSON><PERSON>"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "text", "separateBy": "\n"}]}, "fieldsToSplitBy": "post_input", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [734, 1325], "id": "f602f9c7-7192-4ac6-a2d5-a01bb5d7e90b", "name": "Summarize4"}, {"parameters": {"fieldToSplitOut": "postURLs", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [216, 1325], "id": "243095a7-36b5-405d-bb8e-af0c3c4b1c01", "name": "Split Out1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bccf8cba-f73f-4fef-89ce-d980f8763b13", "leftValue": "={{ $json.profiles }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-522, 1400], "id": "371bac96-e436-4222-bf8b-46be78c60033", "name": "If"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f9850383-b8de-4526-acbf-ab65c120949c", "leftValue": "={{ $json.hashtags }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-522, 1700], "id": "58bf3131-159d-4ea4-8946-7360316e938e", "name": "If1"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "post_input", "field2": "post_url"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1110, 1700], "id": "547e4e05-b583-4abc-b474-d84df6f70e29", "name": "Merge8"}, {"parameters": {"jsCode": "// Build a unified list of post links\nconst postURLs = [];\n\nfor (const { json } of items) {\n  const link = json.url || json.post_url; // either field may hold the link\n  if (link) postURLs.push(link);\n}\n\n// Return Make‑style single‑bundle output\nreturn [\n  {\n    json: { postURLs }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [216, 1625], "id": "c0fd51fa-6222-4703-9e7d-869ee2619111", "name": "Create Json1"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "text", "separateBy": "\n"}]}, "fieldsToSplitBy": "post_input", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [734, 1625], "id": "7b79add2-1713-47fb-9ec0-f2f721c450f2", "name": "Summarize5"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "post_input", "field2": "url"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1110, 1400], "id": "e45d32a6-6cd5-4283-9a86-12442f48a107", "name": "Merge9"}, {"parameters": {"jsCode": "const urls = [];\n\nfor (const { json } of items) {\n  const { url, postId, error } = json;\n\n  // keep if it's a post (postId exists) and there's no scrape error\n  if (url && postId && !error && !urls.includes(url)) urls.push(url);\n}\n\nreturn [\n  {\n    json: { urls }   // [{ urls: [post‑links…] }]\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4, 1950], "id": "8e2467df-e830-4c5e-86ea-d2b8e9c5d2f8", "name": "Create Json2"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "commentText", "separateBy": "\n"}]}, "fieldsToSplitBy": "postUrl", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [436, 1950], "id": "a0554c18-543c-4086-a8d2-bb5583d187f7", "name": "Summarize6"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "postUrl", "field2": "url"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [734, 2025], "id": "74a0d65b-4675-449d-8965-908a21f752a8", "name": "Merge5"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a3bfe65c-5d52-490c-adae-1c33b31fe902", "leftValue": "={{ $json.profiles }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-522, 2025], "id": "bc448a72-b9cd-4ab5-b160-60dfd3e9e3e5", "name": "If2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a3bfe65c-5d52-490c-adae-1c33b31fe902", "leftValue": "={{ $json.hashtags }}", "rightValue": "", "operator": {"type": "array", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-522, 2350], "id": "345bd91a-cc9a-4d3e-a849-40fd46e7ce1e", "name": "If3"}, {"parameters": {"fieldToSplitOut": "hashtags", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-224, 2350], "id": "32e8b203-bbd8-4913-9274-ca3d927f2113", "name": "Split Out2"}, {"parameters": {"jsCode": "const permalinks = [];\n\nfor (const { json } of items) {\n  const { permalink } = json;\n  if (permalink && !permalinks.includes(permalink)) permalinks.push(permalink);\n}\n\n// Make.com expects an **object** under `json`\nreturn [\n  {\n    json: { permalinks }   // 👉 { permalinks: [link1, link2…] }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [216, 2275], "id": "46f8f46e-46fe-4d46-8d30-d3242d9c9a3c", "name": "Create Json3"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "commentText", "separateBy": "\n"}]}, "fieldsToSplitBy": "postUrl", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [734, 2275], "id": "ab0a0e8f-b938-4473-918c-347e967e7c7e", "name": "Summarize7"}, {"parameters": {"mode": "combine", "advanced": true, "mergeByFields": {"values": [{"field1": "postUrl", "field2": "permalink"}]}, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1110, 2350], "id": "78ac9d24-1288-4b8a-bc02-e77119429272", "name": "Merge6"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl1PTB9xGIlHywy4", "mode": "list", "cachedResultName": "🤖 Automations", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl1PTB9xGIlHywy4"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "✅ Completed", "id": "={{ $('Airtable').item.json.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type of Automation", "displayName": "Type of Automation", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🔨 Run Scraper", "value": "🔨 Run Scraper"}, {"name": "📄 Analyze Engagement Signals", "value": "📄 Analyze Engagement Signals"}, {"name": "🏆 Generate Viral Playbook", "value": "🏆 Generate Viral Playbook"}], "readOnly": false, "removed": true}, {"id": "Run Automation", "displayName": "Run Automation", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "boolean", "readOnly": false, "removed": true}, {"id": "Social Channel", "displayName": "Social Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "options": [{"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "YouTube", "value": "YouTube"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": true}, {"id": "Quantity to <PERSON><PERSON><PERSON>", "displayName": "Quantity to <PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "💥 Sent!", "value": "💥 Sent!"}, {"name": "⚙️ Running", "value": "⚙️ Running"}, {"name": "✅ Completed", "value": "✅ Completed"}], "readOnly": false, "removed": false}, {"id": "Last Run", "displayName": "Last Run", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-522, 2550], "id": "79f4080e-2cab-42ac-8fe0-90c86cec6653", "name": "Airtable17", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"jsCode": "const boundary = 'divider';\nconst docName = $input.first().json.document_name;\nconst folderId = $input.first().json.drive_folder_id;\nconst htmlContent = $input.first().json.html_content;\n\nconst metadata = JSON.stringify({\n  name: docN<PERSON>,\n  mimeType: \"application/vnd.google-apps.document\",\n  parents: [folderId]\n});\n\nconst htmlWithStyles = `\n<!DOCTYPE html>\n<html>\n<head>\n  <meta charset=\"UTF-8\">\n  <style>\n    /* Add bottom margin to block elements for spacing */\n    p,\n    ul,\n    ol,\n    table,\n    h1,\n    h2,\n    h3,\n    h4,\n    h5,\n    h6 {\n      margin-bottom: 10pt;\n    }\n\n    h2 {\n      margin-top: 20pt;\n    }\n\n    /* Prevent margin collapse issues or excessive space inside lists */\n    li {\n       margin-bottom: 2pt; /* Optional: small space between list items */\n    }\n\n    /* Remove margin from the last child within common containers if needed */\n    /* This might be overly aggressive, test without it first */\n    /*\n    body > *:last-child,\n    li > *:last-child {\n       margin-bottom: 0;\n    }\n    */\n  </style>\n</head>\n<body>\n  ${htmlContent}\n</body>\n</html>\n`;\n\n// Construct the body with literal \\r\\n ONLY\nlet body = `--${boundary}\\r\\n`;\nbody += `Content-Type: application/json; charset=UTF-8\\r\\n`;\nbody += `\\r\\n`; // Blank line\nbody += `${metadata}\\r\\n`;\nbody += `--${boundary}\\r\\n`;\nbody += `Content-Type: text/html\\r\\n`;\nbody += `\\r\\n`; // Blank line\nbody += `${htmlWithStyles}\\r\\n`; // Add the HTML content\nbody += `--${boundary}--\\r\\n`; // Final boundary\n\nreturn {\n  rawData: body \n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [216, 2750], "id": "bb422f87-a6e4-4ed6-84a6-f003546b98b3", "name": "Prepare_Request"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-820, 2750], "id": "594adf15-c563-4adc-b3fe-b782d4d1f765", "name": "Loop Over Items"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl1PTB9xGIlHywy4", "mode": "list", "cachedResultName": "🤖 Automations", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tbl1PTB9xGIlHywy4"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Webhook').item.json.query.recordId }}", "Status": "⚙️ Running"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type of Automation", "displayName": "Type of Automation", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🔨 Run Scraper", "value": "🔨 Run Scraper"}, {"name": "📄 Analyze Engagement Signals", "value": "📄 Analyze Engagement Signals"}, {"name": "🏆 Generate Viral Playbook", "value": "🏆 Generate Viral Playbook"}], "readOnly": false, "removed": true}, {"id": "Run Automation", "displayName": "Run Automation", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "boolean", "readOnly": false, "removed": true}, {"id": "Social Channel", "displayName": "Social Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "options": [{"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "YouTube", "value": "YouTube"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": true}, {"id": "Quantity to <PERSON><PERSON><PERSON>", "displayName": "Quantity to <PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "💥 Sent!", "value": "💥 Sent!"}, {"name": "⚙️ Running", "value": "⚙️ Running"}, {"name": "✅ Completed", "value": "✅ Completed"}], "readOnly": false, "removed": false}, {"id": "Last Run", "displayName": "Last Run", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1920, 1075], "id": "76369de2-e3dd-4374-9fb1-ac82b6df15cb", "name": "Updates Automation Status", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"jsCode": "const hashtags = [];\nconst profiles = [];\n\nfor (const item of items) {\n  const type = item.json[\"Type\"] || \"\";\n  const value = item.json[\"Hashtag/ Username\"];\n\n  if (!value) continue;\n\n  if (type.includes(\"Profile\")) {\n    profiles.push(value);\n  } else if (type.includes(\"Hashtag\")) {\n    hashtags.push(value);\n  }\n}\n\n// 👇 Clean one-item output\nreturn [\n  {\n    json: {\n      hashtags,\n      profiles\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, -25], "id": "145c8f24-ba8a-4331-be2a-4e200d443151", "name": "Formats Inputs Items into Array of Hashes and Profiles"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/clockworks~free-tiktok-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"excludePinnedPosts\": true,\n    \"hashtags\": {{ $json.hashtags.toJsonString() }},\n    \"profiles\": \n    {{ $json.profiles.toJsonString() }}\n    ,\n    \"resultsPerPage\": {{ $('TikTok').first().json.numPosts }},\n    \"shouldDownloadSlideshowImages\": false,\n    \"profileSorting\": \"latest\",\n    \"shouldDownloadCovers\": true,\n    \"shouldDownloadSubtitles\": true,\n    \"shouldDownloadVideos\": true\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-522, -25], "id": "12d46444-034b-4e96-9ba3-8a9f5ad59d22", "name": "Running Tik<PERSON><PERSON> Scraper", "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "cb7d41e9-f986-4990-a327-d2c0fc0de9c1", "leftValue": "={{ $json.videoMeta }}", "rightValue": "", "operator": {"type": "object", "operation": "exists", "singleValue": true}}, {"id": "194db668-8c67-430a-ba6b-711f91508c5a", "leftValue": "={{ $json.playCount }}", "rightValue": 200000, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-224, -25], "id": "1fdd24ad-8f52-4010-aaef-eed591db3e10", "name": "Filters Posts based on Metrics"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "text", "separateBy": "\n"}]}, "fieldsToSplitBy": "videoWebUrl", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [436, -100], "id": "67db8ef5-72ce-49da-a086-5c1aa385d009", "name": "Concatenates Comments based on vid_url"}, {"parameters": {"assignments": {"assignments": [{"id": "6215b813-0508-4714-8d76-d19774158ebe", "name": "subtitleLink", "value": "={{ $json.videoMeta.subtitleLinks[0].downloadLink || '' }}", "type": "string"}]}, "options": {"ignoreConversionErrors": true}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1110, -25], "id": "1b8bc10c-5057-41f9-adc0-8531be6743ff", "name": "Field For Subtitle Link"}, {"parameters": {"url": "={{ $json.subtitleLink }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1480, -20], "id": "17e82a7e-4654-42c8-8ae6-79f7cf7745f5", "name": "HTTP Request for Subtitle Fetch", "onError": "continueRegularOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment analysis assistant for async communication and productivity-related TikTok comments.\n", "role": "system"}, {"content": "=Your goal is to produce four fields:\n\noverall_sentiment (1 to 5 scale)\ntool_usefulness (1 to 5 scale)\ncommon_questions (bullet list)\nkey_insights (short summary)\n\nIn your analysis, consider:\n\nIn your analysis, consider:\n- Do commenters find the content, tools, or tips helpful?\n- Would this be usable in their day-to-day workflow?\n- Are people tagging others or using positive emojis (❤️🔥😮‍💨…) or negative ones (😑💀👎)?\n- Are they praising the clarity, usefulness, or relatability?\n- Are they mentioning confusion, frustration, or alternatives?\n- Do they indicate it’s saved/bookmarked for later?\n- Does it seem to address common pain points (e.g. meetings, client calls)?\n- Any mention of free vs. paid tools or features?\n- Summarize recurring feedback patterns in Key Insights — without attributing anything to a specific product unless named directly in the comment.\n\n\nRespond in valid JSON only, with the structure:\njson{\n  \"overall_sentiment\": <number 1-5>,\n  \"tool_usefulness\": <number 1-5>,\n  \"common_questions\": [\"...\",\"...\"],\n  \"key_insights\": \"...\"\n}\nNotes\n\nThe sentiment score & tool usefulness must be an integer between 1 to 5. No decimals.\n\nHere are the aggregated comments for a single TikTok video:\n{{ $json.data }}"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2160, -20], "id": "3d1535fd-74d2-4020-be4f-b0ec5567dbe3", "name": "Performs Sentimental Analysis", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"jsCode": "const hashtags = [];\nconst profiles = [];\n\nfor (const item of items) {\n  const type = item.json[\"Type\"] || \"\";\n  const value = item.json[\"Hashtag/ Username\"];\n\n  if (!value) continue;\n\n  if (type.includes(\"Profile\")) {\n    profiles.push(value);\n  } else if (type.includes(\"Hashtag\")) {\n    hashtags.push(value);\n  }\n}\n\n// 👇 Clean one-item output\nreturn [\n  {\n    json: {\n      hashtags,\n      profiles\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, 320], "id": "389f53d4-d8ea-4d89-b160-a9c3e1a4a242", "name": "Formats Inputs Items into Array of Hashes and Profiles1"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apify~instagram-hashtag-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"hashtags\": {{ $json.hashtags.toJsonString() }},\n    \"resultsType\": \"posts\",\n    \"resultsLimit\": {{ $('Instagram').first().json.numPosts }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-520, 220], "id": "24daf52b-d45c-4472-a3de-c78f23c4888a", "name": "Running Instagram Hashtag Scraper", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apify~instagram-post-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"resultsLimit\": {{ $('Instagram').first().json.numPosts }},\n    \"skipPinnedPosts\": true,\n    \"username\": {{ $json.profiles.toJsonString() }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-522, 425], "id": "6befe1e7-441f-4917-b5a5-e79d366eb68c", "name": "Running Instagram Profile Post Scraper", "onError": "continueRegularOutput"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "text", "includeEmpty": true, "separateBy": "\n"}]}, "fieldsToSplitBy": "postUrl", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [734, 225], "id": "b658a949-d53e-42f8-9f35-185facb05948", "name": "Concatenates Comments based on vid url"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Likes": "={{ $('Merge').item.json.diggCount }}", "Shares": "={{ $('Merge').item.json.shareCount }}", "Saves": "={{ $('Merge').item.json.collectCount }}", "Comments": "={{ $('Merge').item.json.commentCount }}", "Duration": "={{ $('Merge').item.json.videoMeta.duration }}", "Height": "={{ $('Merge').item.json.videoMeta.height }}", "Width": "={{ $('Merge').item.json.videoMeta.width }}", "ID": "={{ $('Merge').item.json.id }}", "Transcript": "={{ $('HTTP Request for Subtitle Fetch').item.json.data }}", "Music Name": "={{ $('Merge').item.json.musicMeta.musicName }}", "Music Author": "={{ $('Merge').item.json.musicMeta.musicAuthor }}", "Vid URL": "={{ $('Merge').item.json.webVideoUrl }}", "Views": "={{ $('Merge').item.json.playCount }}", "Video Created Time": "={{ $('Merge').item.json.createTimeISO }}", "Caption": "={{ $('Merge').item.json.text }}", "Key Insights": "={{ $('Performs Sentimental Analysis').item.json.message.content.key_insights }}", "Common Questions": "={{ $('Performs Sentimental Analysis').item.json.message.content.common_questions.join('\\n') }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis').item.json.message.content.overall_sentiment.toString() }}", "Channel": "TikTok", "Cover": "=[{\n  \"url\": \"{{ $('Merge').item.json.videoMeta.coverUrl }}\"\n}]", "Media": "=[{   \"url\": \"{{ $('Merge').item.json.mediaUrls[0] }}\" }]", "Users": "={{ $('Merge').item.json.authorMeta.name }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis').item.json.message.content.tool_usefulness.toString() }}", "Music": "=[{\n\"url\": \"{{ $('Merge').item.json.musicMeta.playUrl }}\"\n}]", "Language": "={{ $('Merge').item.json.videoMeta.subtitleLinks[0].language }}", "User": "=[ \"{{ $json.id }}\" ]", "Hashtags": "={{'#'+  $('Merge').item.json.hashtags.map(hashtags => hashtags.name).join('\\n#')}}", "Sentiment Status": "Complete", "Quote Count": 0, "Retweets": 0, "Quotes": 0}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": false}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2760, -20], "id": "a6811c4e-bce8-4b2f-8f53-a14329b316ae", "name": "Creates New Record in Audience Intelligence Database", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Likes": "={{ $('Merge1').item.json.likesCount }}", "Shares": "=", "Saves": "=", "Comments": "={{ $('Merge1').item.json.commentsCount }}", "Duration": "={{ $('Merge1').item.json.videoDuration }}", "Height": "={{ $('Merge1').item.json.dimensionsHeight }}", "Width": "={{ $('Merge1').item.json.dimensionsWidth }}", "ID": "={{ $('Merge1').item.json.id }}", "Transcript": "=", "Music Name": "={{ $('Merge1').item.json.musicInfo.music_info.music_asset_info.title }}", "Music Author": "={{ $('Merge1').item.json.musicInfo.music_info.music_asset_info.display_artist }}", "Vid URL": "={{ $('Merge1').item.json.postUrl }}", "Views": "={{ $('Merge1').item.json.videoViewCount }}", "Video Created Time": "={{ $('Merge1').item.json.timestamp }}", "Caption": "={{ $('Merge1').item.json.caption }}", "Key Insights": "={{ $('Performs Sentimental Analysis1').item.json.message.content.key_insights }}", "Common Questions": "={{ $('Performs Sentimental Analysis1').item.json.message.content.common_questions.join('\\n') }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis1').item.json.message.content.overall_sentiment.toString() }}", "Channel": "Instagram", "Cover": "=[{\n  \"url\": \"{{ $('Merge1').item.json.displayUrl }}\"\n}]", "Media": "=[{   \"url\": \"{{ $('Merge1').item.json.videoUrl }}\" }]", "Users": "={{ $('Merge1').item.json.latestComments[0].owner.username }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis1').item.json.message.content.tool_usefulness.toString() }}", "Music": "=[{\n\"url\": \"{{ $('Merge1').item.json.musicInfo.music_info.music_asset_info.progressive_download_url }}\"\n}]", "Language": "=", "Hashtags": "={{'#'+ $('Merge1').item.json.hashtags.join('\\n') }}", "Post Type": "={{ $('Merge1').item.json.type }}", "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": false}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2280, 320], "id": "c836f534-c025-4c07-b23a-7d6c73483d41", "name": "Creates New Record in Audience Intelligence Database1", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Initialize an empty array to hold all the post URLs\nconst searchQueries = [];\n\n// Loop through all items\nfor (const item of items) {\n  const url = item.json[\"Hashtag/ Username\"];\n  \n  // If there is a URL, add it to our array\n  if (url) {\n    searchQueries.push(url);\n  }\n}\n\n// Return a single item with all URLs in an array\nreturn [\n  {\n    json: {\n      searchQueries\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-820, 725], "id": "a395b9f0-ed76-4150-9757-960386f865b0", "name": "Formats Inputs Items into Array of Search Hashes", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/streamers~youtube-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"downloadSubtitles\": true,\n    \"hasCC\": false,\n    \"hasLocation\": false,\n    \"hasSubtitles\": false,\n    \"is360\": false,\n    \"is3D\": false,\n    \"is4K\": false,\n    \"isBought\": false,\n    \"isHD\": false,\n    \"isHDR\": false,\n    \"isLive\": false,\n    \"isVR180\": false,\n    \"maxResultStreams\": 0,\n    \"maxResults\": {{ $('Airtable').item.json['Quantity to Scrape'] }},\n    \"maxResultsShorts\": 0,\n    \"preferAutoGeneratedSubtitles\": false,\n    \"saveSubsToKVS\": false,\n    \"searchQueries\": {{ $json.searchQueries.toJsonString() }},\n    \"sortVideosBy\": \"POPULAR\",\n    \"sortingOrder\": \"views\",\n    \"subtitlesFormat\": \"plaintext\",\n    \"subtitlesLanguage\": \"en\",\n    \"videoType\": \"video\",\n\"oldestPostDate\": \"{{ $('Search YouTube Inputs').first().json['Oldest Post Date'] }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-522, 725], "id": "f77ae524-9df1-433d-9c35-4bde67cda60f", "name": "Running Youtube Scraper", "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2d989258-ae1e-4f75-a268-25fb16d9e580", "leftValue": "={{ $json.subtitles[0].plaintext.length }}", "rightValue": 5000, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [734, 725], "id": "fb756c04-4302-41ea-8e98-ccf1ac07a34c", "name": "Routes Based on Transcript Length"}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "comment", "separateBy": "\n"}]}, "fieldsToSplitBy": "pageUrl", "options": {}}, "type": "n8n-nodes-base.summarize", "typeVersion": 1.1, "position": [220, 620], "id": "44b380c9-2438-473c-b105-0e86fff7fc65", "name": "Concatenates Comments based on vid url1"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge').item.json.input }}',{Channel}='TikTok')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2540, -20], "id": "775b15b9-1fed-44d3-ace2-a73b7b8e1535", "name": "Searches for Input Hashtags and Profile Info from Inputs", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge3').item.json.input }}',{Channel}='YouTube')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1860, 620], "id": "b4ab5ef8-13d9-42e2-a9ae-8391d7ed91df", "name": "Searches for Input Hashtags and Profile Info from Inputs1", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge3').item.json.input }}',{Channel}='YouTube')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2238, 825], "id": "3a08c680-82d2-4d72-b404-1cb1b5f6a49c", "name": "Searches for Input Hashtags and Profile Info from Inputs2", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge9').item.json.author.publicId }}',{Channel}='LinkedIn')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2240, 1400], "id": "ce716632-ce56-42e5-a35c-86d6ec9ec4e7", "name": "Searches for Input Hashtags and Profile Info from Inputs3", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge8').item.json.search_input }}',{Channel}='LinkedIn')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2238, 1700], "id": "789cf659-031e-4e8f-ba01-dc4d30891d43", "name": "Searches for Input Hashtags and Profile Info from Inputs4", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge5').item.json.inputUrl.split(\"/\")[3] }}',{Channel}='Facebook')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1862, 2025], "id": "5334f092-db0b-4a8f-a884-2b7284e107b1", "name": "Searches for Input Hashtags and Profile Info from Inputs5", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tblaAgdwupzCJCCwq", "mode": "list", "cachedResultName": "📃 Inputs", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tblaAgdwupzCJCCwq"}, "filterByFormula": "=AND({Hashtag/ Username}='{{ $('Merge6').item.json.accountID }}',{Channel}='Facebook')", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2238, 2350], "id": "a03e5d7b-272a-4be4-b5c3-0e5c9b12f234", "name": "Searches for Input Hashtags and Profile Info from Inputs6", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Likes": "={{ $('Merge3').item.json.likes }}", "Shares": "=", "Saves": "=", "Comments": "={{ $('Merge3').item.json.commentsCount }}", "Duration": "={{ \n  (() => {\n    const durationStr = $('Merge3').item.json.duration || \"00:00:00\";\n    const parts = durationStr.split(':').map(Number);\n    return (parts[0] * 3600) + (parts[1] * 60) + (parts[2]);\n  })()\n}}", "Height": "=", "Width": "=", "ID": "={{ $('Merge3').item.json.id }}", "Transcript": "={{ $('Merge3').item.json.subtitles[0].plaintext }}", "Vid URL": "={{ $('Merge3').item.json.url }}", "Views": "={{ $('Merge3').item.json.viewCount }}", "Video Created Time": "={{ $('Merge3').item.json.date }}", "Caption": "={{ $('Merge3').item.json.text }}", "Key Insights": "={{ $('Performs Sentimental Analysis2').item.json.message.content.key_insights }}", "Common Questions": "={{ $('Performs Sentimental Analysis2').item.json.message.content.common_questions.join('\\n') }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis2').item.json.message.content.overall_sentiment.toString() }}", "Channel": "YouTube", "Cover": "=[{\n  \"url\": \"{{ $('Merge3').item.json.thumbnailUrl }}\"\n}]", "Users": "={{ $('Merge3').item.json.channelName }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis2').item.json.message.content.tool_usefulness.toString() }}", "User": "=[ \"{{ $json.id }}\" ]", "Hashtags": "={{'#'+  $('Merge3').item.json.hashtags.map(hashtags => hashtags).join('\\n#')}}", "Post Type": "={{ $('Merge3').item.json.type }}", "Title": "={{ $('Merge3').item.json.title }}", "Language": "={{ $('Merge3').item.json.subtitles[0].language }}", "Quote Count": 0, "Retweets": 0, "Quotes": 0, "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": false}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2238, 625], "id": "ada5a048-3489-458d-bdcb-e45af5c07681", "name": "Creates New Record in Audience Intelligence Database2", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Likes": "={{ $('Merge3').item.json.likes }}", "Shares": "=", "Saves": "=", "Comments": "={{ $('Merge3').item.json.commentsCount }}", "Duration": "={{ \n  (() => {\n    const durationStr = $('Merge3').item.json.duration || \"00:00:00\";\n    const parts = durationStr.split(':').map(Number);\n    return (parts[0] * 3600) + (parts[1] * 60) + (parts[2]);\n  })()\n}}", "Height": "=", "Width": "=", "ID": "={{ $('Merge3').item.json.id }}", "Transcript": "={{ $('Summarising Long Transcripts').item.json.message.content }}", "Vid URL": "={{ $('Merge3').item.json.url }}", "Views": "={{ $('Merge3').item.json.viewCount }}", "Video Created Time": "={{ $('Merge3').item.json.date }}", "Caption": "={{ $('Merge3').item.json.text }}", "Key Insights": "={{ $('Performs Sentimental Analysis3').item.json.message.content.key_insights }}", "Common Questions": "={{ $('Performs Sentimental Analysis3').item.json.message.content.common_questions.join('\\n') }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis3').item.json.message.content.overall_sentiment.toString() }}", "Channel": "YouTube", "Cover": "=[{\n  \"url\": \"{{ $('Merge3').item.json.thumbnailUrl }}\"\n}]", "Users": "={{ $('Merge3').item.json.channelName }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis3').item.json.message.content.tool_usefulness.toString() }}", "User": "=[ \"{{ $json.id }}\" ]", "Hashtags": "={{'#'+  $('Merge3').item.json.hashtags.map(hashtags => hashtags).join('\\n#')}}", "Post Type": "={{ $('Merge3').item.json.type }}", "Title": "={{ $('Merge3').item.json.title }}", "Language": "={{ $('Merge3').item.json.subtitles[0].language }}", "Quote Count": 0, "Retweets": 0, "Quotes": 0, "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": false}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2536, 825], "id": "c92892b5-dacd-4571-aa70-cfca139a88dc", "name": "Creates New Record in Audience Intelligence Database3", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Channel": "LinkedIn", "ID": "={{ $('Merge9').item.json.urn.split(\":\")[3] }}", "Shares": "={{ $('Merge9').item.json.numShares }}", "Comments": "={{ $('Merge9').item.json.numComments }}", "Likes": "={{ $('Merge9').item.json.numLikes }}", "Profile Type": "={{ $('Merge9').item.json.authorType }}", "LinkedIn Post Url": "={{ $('Merge9').item.json.url }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis5').item.json.message.content.overall_sentiment.toString() }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis5').item.json.message.content.tool_usefulness.toString() }}", "Common Questions": "={{ $('Performs Sentimental Analysis5').item.json.message.content.common_questions }}", "Key Insights": "={{ $('Performs Sentimental Analysis5').item.json.message.content.key_insights }}", "Post Type": "={{ $('Merge9').item.json.type }}", "LinkedIn Post Text": "={{ $('Merge9').item.json.text }}", "Post Date": "={{ $('Merge9').item.json.postedAtISO.toDateTime().format(\"d/M/yyyy h:mma\").toLowerCase() }}", "User": "=[ \"{{ $json.id }}\" ]", "Quote Count": 0, "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": true}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2536, 1400], "id": "1fb76fed-826b-4152-9e3b-64238be26179", "name": "Creates New Record in Audience Intelligence Database4", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Channel": "LinkedIn", "Likes": "={{ $('Merge8').item.json.stats.reactions[0].count }}", "Shares": "={{ $('Merge8').item.json.stats.shares }}", "Saves": 0, "Comments": "={{ $('Merge8').item.json.stats.comments }}", "ID": "={{ $('Merge8').item.json.activity_id }}", "LinkedIn Post Text": "={{ $('Merge8').item.json.text }}", "Post Date": "={{ $('Merge8').item.json.posted_at.date.toDateTime().format(\"d/M/yyyy h:mma\").toLowerCase() }}", "LinkedIn Post Url": "={{ $('Merge8').item.json.post_url }}", "Common Questions": "={{ $('Performs Sentimental Analysis6').item.json.message.content.common_questions.toJsonString() }}", "Key Insights": "={{ $('Performs Sentimental Analysis6').item.json.message.content.key_insights }}", "Overall Sentiment": "={{ $('Performs Sentimental Analysis6').item.json.message.content.overall_sentiment.toString() }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis6').item.json.message.content.tool_usefulness.toString() }}", "User": "=[ \"{{ $json.id }}\" ]", "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": true}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2540, 1700], "id": "bcf53c6b-c400-4d62-a88a-ed9e2cd953d8", "name": "Creates New Record in Audience Intelligence Database5", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Channel": "Facebook", "Overall Sentiment": "={{ $('Performs Sentimental Analysis7').item.json.message.content.overall_sentiment.toString() }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis7').item.json.message.content.tool_usefulness.toString() }}", "Common Questions": "={{ $('Performs Sentimental Analysis7').item.json.message.content.common_questions }}", "Key Insights": "={{ $('Performs Sentimental Analysis7').item.json.message.content.key_insights }}", "ID": "={{ $('Merge5').item.json.postId }}", "Likes": "={{ $('Merge5').item.json.likes }}", "Comments": "={{ $('Merge5').item.json.comments || 0 }}", "Shares": "={{ $('Merge5').item.json.shares }}", "Width": "={{ $('Merge5').item.json.media[0].photo_image.width }}", "Views": 0, "Saves": 0, "Duration": 0, "Cover": "=[{   \"url\": \"{{ $('Merge5').item.json.media[0].thumbnail }}\" }]", "Post Date": "={{ $('Merge5').item.json.time.toDateTime().format('yyyy-MM-dd HH:mm') }}", "Vid URL": "=", "Post URL": "={{ $('Merge5').item.json.postUrl }}", "Post Text": "={{ $('Merge5').item.json.text || $('Merge5').item.json.sharedPost.text }}", "Post Type": "={{ $('Merge5').item.json.media[0].__isMedia }}", "User": "=[ \"{{ $json.id }}\" ]", "Caption": "=", "Height": "={{ $('Merge5').item.json.media[0].photo_image.height }}", "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": false}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2238, 2025], "id": "dd1140b4-57f5-4cfd-bcca-1496fad4061f", "name": "Creates New Record in Audience Intelligence Database6", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"Channel": "Facebook", "Overall Sentiment": "={{ $('Performs Sentimental Analysis8').item.json.message.content.overall_sentiment.toString() }}", "Tool Helpfulness": "={{ $('Performs Sentimental Analysis8').item.json.message.content.tool_usefulness.toString() }}", "Common Questions": "={{ $('Performs Sentimental Analysis8').item.json.message.content.common_questions }}", "Key Insights": "={{ $('Performs Sentimental Analysis8').item.json.message.content.key_insights }}", "ID": "={{ $('Merge6').item.json.postUrl.split(\"/\")[5] }}", "Likes": "={{ $('Merge6').item.json.like_count }}", "Comments": "={{ $('Merge6').item.json.comment_count }}", "Shares": "={{ $('Merge6').item.json.share_count }}", "Post Type": "=[{{ $('Merge6').item.json.media_type }}]", "Post Date": "={{ $('Merge6').item.json.date }}", "Post URL": "={{ $('Merge6').item.json.postUrl }}", "Post Text": "={{ $('Merge6').item.json.content }}", "Views": "={{ $('Merge6').item.json.total_engagement }}", "Hashtags": "={{ $('Merge6').item.json.accountID }}", "User": "=[ \"{{ $json.id }}\" ]", "Sentiment Status": "Complete"}, "matchingColumns": [], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": false}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": true}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": false}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": false}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": false}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"typecast": true}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2540, 2360], "id": "b3f069d9-99e8-4921-be4e-4b3823ebe24a", "name": "Creates New Record in Audience Intelligence Database7", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}, "onError": "continueRegularOutput"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge').item.json.id }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1862, -25], "id": "5c1bc84a-8a4f-4bf1-8edb-9f5d32c751e0", "name": "Checks Duplicates based on prev executions"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1486, 325], "id": "97fb7861-9bf3-450f-afe3-85c21e441a00", "name": "Checks Duplicates based on prev executions1"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge3').item.json.id }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1486, 625], "id": "d0cbbfef-c901-420a-a5ca-a703e90f1e96", "name": "Checks Duplicates based on prev executions2"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge3').item.json.id }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1860, 820], "id": "0c213576-3a27-4607-8691-a34165394544", "name": "Checks Duplicates based on prev executions3"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge9').item.json.urn }}", "options": {"scope": "node"}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1860, 1400], "id": "fab0fdf3-e10e-45a4-bcf5-bf437e540605", "name": "Checks Duplicates based on prev executions4"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge8').item.json.activity_id }}", "options": {"scope": "node"}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1862, 1700], "id": "842ab3d5-45d5-4dbb-b095-b9b516c8feb9", "name": "Checks Duplicates based on prev executions5"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge5').item.json.postId }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1480, 2020], "id": "2b5489b4-bca9-4836-b8a6-f2c058516a71", "name": "Checks Duplicates based on prev executions6"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge6').item.json.postUrl }}", "options": {"scope": "node"}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1860, 2360], "id": "890541ec-2036-49d2-b3b8-055c57b1a344", "name": "Checks Duplicates based on prev executions7"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment analysis assistant for async communication and productivity-related Instagram comments.\n", "role": "system"}, {"content": "=Your goal is to produce four fields:\n\noverall_sentiment (1 to 5 scale)\ntool_usefulness (1 to 5 scale)\ncommon_questions (bullet list)\nkey_insights (short summary)\n\nIn your analysis, consider:\n\nIn your analysis, consider:\n- Do commenters find the content, tools, or tips helpful?\n- Would this be usable in their day-to-day workflow?\n- Are people tagging others or using positive emojis (❤️🔥😮‍💨…) or negative ones (😑💀👎)?\n- Are they praising the clarity, usefulness, or relatability?\n- Are they mentioning confusion, frustration, or alternatives?\n- Do they indicate it’s saved/bookmarked for later?\n- Does it seem to address common pain points (e.g. meetings, client calls)?\n- Any mention of free vs. paid tools or features?\n- Summarize recurring feedback patterns in Key Insights — without attributing anything to a specific product unless named directly in the comment.\n\n\nRespond in valid JSON only, with the structure:\njson{\n  \"overall_sentiment\": <number 1-5>,\n  \"tool_usefulness\": <number 1-5>,\n  \"common_questions\": [\"...\",\"...\"],\n  \"key_insights\": \"...\"\n}\nNotes\n\nThe sentiment score & tool usefulness must be an integer between 1 to 5. No decimals.\n\nHere are the aggregated comments for a single Instagram video:\n{{ $json.concatenated_text }}\nThe Caption of the Instagram Video for Context: {{ $json.caption }}\n\nIf enough aggregated comments have not been returned, do not make up common questions. Simply return that not enough comments have been returned to gauge common questions. "}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1784, 325], "id": "815bd8a5-7564-4d81-ab6c-d352ecf4fe66", "name": "Performs Sentimental Analysis1", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment analysis assistant for async communication and productivity-related YouTube comments.", "role": "system"}, {"content": "=Your goal is to produce four fields:\n\noverall_sentiment (1 to 5 scale)\ntool_usefulness (1 to 5 scale)\ncommon_questions (bullet list)\nkey_insights (short summary)\n\nIn your analysis, consider:\n\nIn your analysis, consider:\n- Do commenters find the content, tools, or tips helpful?\n- Would this be usable in their day-to-day workflow?\n- Are people tagging others or using positive emojis (❤️🔥😮‍💨…) or negative ones (😑💀👎)?\n- Are they praising the clarity, usefulness, or relatability?\n- Are they mentioning confusion, frustration, or alternatives?\n- Do they indicate it’s saved/bookmarked for later?\n- Does it seem to address common pain points (e.g. meetings, client calls)?\n- Any mention of free vs. paid tools or features?\n- Summarize recurring feedback patterns in Key Insights — without attributing anything to a specific product unless named directly in the comment.\n\n\nRespond in valid JSON only, with the structure:\njson{\n  \"overall_sentiment\": <number 1-5>,\n  \"tool_usefulness\": <number 1-5>,\n  \"common_questions\": [\"...\",\"...\"],\n  \"key_insights\": \"...\"\n}\nNotes\n\nThe sentiment score & tool usefulness must be an integer between 1 to 5. No decimals.\n\nHere is the Youtube Video Title:{{ $json.title }}\nHere are the aggregated comments for a single YouTube video:\n{{ $json.concatenated_comment }}"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1032, 625], "id": "4803630e-705b-42c3-ae3c-203771653f98", "name": "Performs Sentimental Analysis2", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment analysis assistant for async communication and productivity-related YouTube comments.", "role": "system"}, {"content": "=Your goal is to produce four fields:\n\noverall_sentiment (1 to 5 scale)\ntool_usefulness (1 to 5 scale)\ncommon_questions (bullet list)\nkey_insights (short summary)\n\nIn your analysis, consider:\n\nIn your analysis, consider:\n- Do commenters find the content, tools, or tips helpful?\n- Would this be usable in their day-to-day workflow?\n- Are people tagging others or using positive emojis (❤️🔥😮‍💨…) or negative ones (😑💀👎)?\n- Are they praising the clarity, usefulness, or relatability?\n- Are they mentioning confusion, frustration, or alternatives?\n- Do they indicate it’s saved/bookmarked for later?\n- Does it seem to address common pain points (e.g. meetings, client calls)?\n- Any mention of free vs. paid tools or features?\n- Summarize recurring feedback patterns in Key Insights — without attributing anything to a specific product unless named directly in the comment.\n\n\nRespond in valid JSON only, with the structure:\njson{\n  \"overall_sentiment\": <number 1-5>,\n  \"tool_usefulness\": <number 1-5>,\n  \"common_questions\": [\"...\",\"...\"],\n  \"key_insights\": \"...\"\n}\nNotes\n\nThe sentiment score & tool usefulness must be an integer between 1 to 5. No decimals.\n\nHere is the Youtube Video Title: {{ $('Routes Based on Transcript Length').item.title }}\nHere are the aggregated comments for a single YouTube video:\n{{ $('Routes Based on Transcript Length').item.concatenated_comment }}"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1408, 825], "id": "cc99d39e-67b8-470c-b879-04e0b5223685", "name": "Performs Sentimental Analysis3", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "=You are a world-class content summarization expert with 15+ years of experience in linguistics, knowledge extraction, and AI-driven summarization. Your task is to deeply analyze a **YouTube video transcript** and convert it into a structured, insight-rich summary using **smart brevity**.  \n\nBelow is the input:\n\n---\n**Video Title**: {{ $('Merge3').item.json.title }}\n**Transcript**: {{ $('Merge3').item.json.subtitles[0].plaintext }}\n**Captions**: {{ $json.text }}\n**Hashtags**: {{ $json.input }} , {{ $json.hashtags }}\n\n"}, {"content": "=You will be perform the following:\n\n1. **Concise Summary**: Give a high-level overview in 4–6 bullet points.\n2. **Key Takeaways**: List the most important actionable or insightful points.\n3. **Core Sections & Structure**: Break down the transcript into logical sections with headers and short explanations.\n4. **Major Highlights**: Mention any shocking, surprising, or emotional moments.\n5. **New Concepts or Learning Points**: Extract and explain any new or unique knowledge introduced.\n6. **Speaker Insights**: If speakers are distinct, mention their unique contributions (if detectable).\n7. **Smart Brevity Output**: All content must follow a punchy, compressed format — no fluff, all signal.\n8. **Prep for Next Step**: Format the output to be usable for the next automation node. Provide each section under labeled markdown-style headers (e.g., `## Summary`, `## Takeaways`).\n\nDo not repeat the title or transcript in the output. Only return the cleaned, structured, and categorized insights.\n\nNote :\nIf you don't have transcript, try to use other things like captions and other information about the post to summarise it.\n\n", "role": "system"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1032, 825], "id": "691a5860-7e10-4b6b-93ce-be694a9e53dd", "name": "Summarising Long Transcripts", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment analysis assistant for async communication and productivity-related Twitter comments.", "role": "system"}, {"content": "=Your goal is to produce four fields:\n\noverall_sentiment (1 to 5 scale)\ntool_usefulness (1 to 5 scale)\ncommon_questions (bullet list)\nkey_insights (short summary)\n\nIn your analysis, consider:\n\nIn your analysis, consider:\n- Do commenters find the content, tools, or tips helpful?\n- Would this be usable in their day-to-day workflow?\n- Are people tagging others or using positive emojis (❤️🔥😮‍💨…) or negative ones (😑💀👎)?\n- Are they praising the clarity, usefulness, or relatability?\n- Are they mentioning confusion, frustration, or alternatives?\n- Do they indicate it’s saved/bookmarked for later?\n- Does it seem to address common pain points (e.g. meetings, client calls)?\n- Any mention of free vs. paid tools or features?\n- Summarize recurring feedback patterns in Key Insights — without attributing anything to a specific product unless named directly in the comment.\n\n\nRespond in valid JSON only, with the structure:\njson{\n  \"overall_sentiment\": <number 1-5>,\n  \"tool_usefulness\": <number 1-5>,\n  \"common_questions\": [\"...\",\"...\"],\n  \"key_insights\": \"...\"\n}\nNotes\n\nThe sentiment score & tool usefulness must be an integer between 1 to 5. No decimals.\n\nHere is the original Twitter Tweet: {{ $json.fullText }}\nHere are the aggregated comments for the twitter post:\n{{ $json.concatenated_replyText }}"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [656, 1075], "id": "725de58a-2ce3-44e2-a2f8-8a5b023dfadd", "name": "Performs Sentimental Analysis4", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment‑analysis assistant for LinkedIn posts and comments about async video, productivity, and free workflow tools. Focus on professional context, B2B pain points, and adoption signals.", "role": "system"}, {"content": "=Your goal is to output four fields:\n\n\noverall_sentiment (1–5 integer)\ntool_usefulness (1–5 integer)\ncommon_questions  (string)\nkey_insights      (short summary)\n\nIn your analysis, weigh factors unique to LinkedIn:\n\n- Do commenters signal business value, ROI, or workflow fit?\n- Are they tagging colleagues or decision‑makers?\n- Which reactions dominate? 👍 Like, ❤️ Love, 👏 Celebrate, 💡 Insightful, 🤝 Support, 🔄 Repost\n- Praise for clarity, practicality, or cost savings?\n- Mentions of confusion, friction, or competing tools?\n- Are people saving or sharing for team adoption?\n- References to meeting overload, async culture, or free‑vs‑paid tiers?\n\nRespond **only** with valid JSON:\njson{\n  \"overall_sentiment\": <1‑5>,\n  \"tool_usefulness\": <1‑5>,\n  \"common_questions\": \"Q1,Q2,Q3\",\n  \"key_insights\": \"...\"\n}\n\nHere is the original LinkedIn post:\n{{ $json.text }}\n\nHere are the aggregated comments for the post:\n{{ $json.concatenated_text }}\n\n\nHere are the reactions for the post:\n\nNumber of likes: {{ $json.numLikes }}\nNumber of Shares: {{ $json.numShares }}\nNo of comments : {{ $json.numComments }}\n\nType : {{ $json.type }}"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1408, 1400], "id": "868f29dd-8e12-40f9-a372-253bd74149b5", "name": "Performs Sentimental Analysis5", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment‑analysis assistant for LinkedIn posts and comments about async video, productivity, and free workflow tools. Focus on professional context, B2B pain points, and adoption signals.", "role": "system"}, {"content": "=Your goal is to output four fields:\n\n\noverall_sentiment (1–5 integer)\ntool_usefulness (1–5 integer)\ncommon_questions  (array of strings)\nkey_insights      (short summary)\n\nIn your analysis, weigh factors unique to LinkedIn:\n\n- Do commenters signal business value, ROI, or workflow fit?\n- Are they tagging colleagues or decision‑makers?\n- Which reactions dominate? 👍 Like, ❤️ Love, 👏 Celebrate, 💡 Insightful, 🤝 Support, 🔄 Repost\n- Praise for clarity, practicality, or cost savings?\n- Mentions of confusion, friction, or competing tools?\n- Are people saving or sharing for team adoption?\n- References to meeting overload, async culture, or free‑vs‑paid tiers?\n\nRespond **only** with valid JSON:\njson{\n  \"overall_sentiment\": <1‑5>,\n  \"tool_usefulness\": <1‑5>,\n  \"common_questions\": \"...\",\n  \"key_insights\": \"...\"\n}\n\nHere is the original LinkedIn post:\n{{ $json.text }}\n\nHere are the aggregated comments for the post:\n{{ $json.concatenated_text }}\n\n\nHere are the reactions for the post:\nTotal Reactions : {{ $json.stats.total_reactions }}\nNumber of likes: {{ $json.stats.reactions[0].count }}\nNumber of Shares: {{ $json.stats.shares }}\nNo of comments : {{ $json.stats.comments }}\n\nHashTags : {{ $json.hashtags }}"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1408, 1700], "id": "37d045dc-db51-4a65-98b2-7ffad287471f", "name": "Performs Sentimental Analysis6", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment‑analysis assistant for Facebook posts, comments, and page discussions about async video, productivity, and free workflow tools. Focus on professional context, B2B pain points, and adoption signals.", "role": "system"}, {"content": "=Your goal is to output four fields:\n\noverall_sentiment (1–5 integer)\ntool_usefulness (1–5 integer)\ncommon_questions  (string of questions)\nkey_insights      (short summary)\n\nIn your analysis, weigh factors unique to Facebook:\n\n- Do commenters signal business value, ROI, or workflow fit?\n- Are they tagging teammates, founders, or decision‑makers?\n- Which reactions dominate? 👍 Like, ❤️ Love, 😂 Haha, 😮 Wow, 😢 Sad, 😠 Angry, 🔄 Share, 🏷️ Save\n- Praise for clarity, practicality, or cost savings?\n- Mentions of confusion, friction, or competing tools?\n- Are people saving or sharing for team adoption?\n- References to meeting overload, async culture, or free‑vs‑paid tiers?\n\nRespond **only** with valid JSON:\njson{\n  \"overall_sentiment\": <1‑5>,\n  \"tool_usefulness\": <1‑5>,\n  \"common_questions\": \"Q1,Q2,Q3\",\n  \"key_insights\": \"...\"\n}\n\nHere is the original Facebook post:\n{{ $json.text }}\n\nHere are the aggregated comments:\n{{ $json.concatenated_commentText }}\n\nHere are the stats for the post:\nLikes: {{ $json.likes }}\nShares: {{ $json.shares }}\nComments: {{ $json.comments }}\n\nPage: {{ $json.pageName }}\n"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1032, 2025], "id": "fa3e9b20-7115-4501-837d-70939a69e693", "name": "Performs Sentimental Analysis7", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a specialized sentiment‑analysis assistant for Facebook posts, comments, and page discussions about async video, productivity, and free workflow tools. Focus on professional context, B2B pain points, and adoption signals.", "role": "system"}, {"content": "=Your goal is to output four fields:\n\noverall_sentiment (1–5 integer)\ntool_usefulness (1–5 integer)\ncommon_questions  (string of questions)\nkey_insights      (short summary)\n\nIn your analysis, weigh factors unique to Facebook:\n\n- Do commenters signal business value, ROI, or workflow fit?\n- Are they tagging teammates, founders, or decision‑makers?\n- Which reactions dominate? 👍 Like, ❤️ Love, 😂 Haha, 😮 Wow, 😢 Sad, 😠 Angry, 🔄 Share, 🏷️ Save\n- Praise for clarity, practicality, or cost savings?\n- Mentions of confusion, friction, or competing tools?\n- Are people saving or sharing for team adoption?\n- References to meeting overload, async culture, or free‑vs‑paid tiers?\n\nRespond **only** with valid JSON:\njson{\n  \"overall_sentiment\": <1‑5>,\n  \"tool_usefulness\": <1‑5>,\n  \"common_questions\": \"Q1,Q2,Q3\",\n  \"key_insights\": \"...\"\n}\n\nHere is the original Facebook post:\n{{ $json.content }}\n\nHere are the aggregated comments:\n{{ $json.concatenated_commentText }}\n\nHere are the stats for the post:\nLikes: {{ $json.like_count }}\nShares: {{ $json.share_count }}\nComments: {{ $json.comment_count }}\n\nPage: {{ $json.accountID }}\n"}]}, "jsonOutput": true, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1408, 2350], "id": "5a581a22-2dd7-486f-8280-9aabe8297fe1", "name": "Performs Sentimental Analysis8", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apidojo~twitter-scraper-lite/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"maxItems\": {{ $json.numPosts }},\n    \"searchTerms\": {{ $json.hashtags.toJsonString() }},\n    \"sort\": \"Top\",\n    \"twitterHandles\": {{ $json.profiles.toJsonString() }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-522, 1075], "id": "c87bea69-1ecf-4963-9db6-f46174cc203f", "name": "Scrape Twitter Posts", "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/supreme_coder~linkedin-post/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"deepScrape\": true,\n    \"limitPerSource\": {{ $json.numPosts }},\n    \"rawData\": false,\n    \"urls\": {{ $json.profiles.toJsonString() }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-224, 1400], "id": "4013f0f4-c9aa-49a9-9a17-8a6d878ccc45", "name": "Scrapes LinkedIn Profiles and Companies"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apimaestro~linkedin-posts-search-scraper-no-cookies/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"keyword\": \"{{ $json.hashtags }}\",\n  \"limit\": {{ $('If1').item.json.numPosts }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4, 1700], "id": "beb8570b-10e7-4076-937a-251cde600fc7", "name": "Scrapes LinkedIn Posts based on HashTags", "alwaysOutputData": false, "retryOnFail": true, "maxTries": 2}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apify~facebook-posts-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"resultsLimit\": {{ $json.numPosts }},\n    \"startUrls\": {{ $json.profiles.toJsonString() }}\n    \n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-224, 2025], "id": "5b9d1c31-e20e-4b03-9599-a68177acef3d", "name": "Scrape Posts Based on Facebook Profiles", "alwaysOutputData": false, "retryOnFail": true, "maxTries": 2, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/scraping_solutions~facebook-hashtag-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"hashtag\": \"{{ $json.hashtags }}\",\n    \"iterations\": {{ $('If3').item.json.numPosts }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4, 2350], "id": "e025b4c9-8e36-479b-a92a-e0c14f48ca2c", "name": "Scrape Facebook Posts Based on Hashtags", "alwaysOutputData": false, "retryOnFail": true, "maxTries": 2, "onError": "continueRegularOutput"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tbl74w570Z8MgVZcn"}, "filterByFormula": "OR(   {Top Performer} > 1000,   AND(     {Channel} = \"Facebook\",     {Top Performer} > 1000   ) )", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1040, 2760], "id": "00b6c06d-c561-4068-b8bf-eb76781e4380", "name": "Searches For Top Performing Posts", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl1PTB9xGIlHywy4", "mode": "list", "cachedResultName": "🤖 Automations", "cachedResultUrl": "https://airtable.com/appytKAn1EsGQYzvt/tbl1PTB9xGIlHywy4"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "✅ Completed", "id": "={{ $('Airtable').item.json.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type of Automation", "displayName": "Type of Automation", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🔨 Run Scraper", "value": "🔨 Run Scraper"}, {"name": "📄 Analyze Engagement Signals", "value": "📄 Analyze Engagement Signals"}, {"name": "🏆 Generate Viral Playbook", "value": "🏆 Generate Viral Playbook"}], "readOnly": false, "removed": true}, {"id": "Run Automation", "displayName": "Run Automation", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "boolean", "readOnly": false, "removed": true}, {"id": "Social Channel", "displayName": "Social Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "options": [{"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "YouTube", "value": "YouTube"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": true}, {"id": "Quantity to <PERSON><PERSON><PERSON>", "displayName": "Quantity to <PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "💥 Sent!", "value": "💥 Sent!"}, {"name": "⚙️ Running", "value": "⚙️ Running"}, {"name": "✅ Completed", "value": "✅ Completed"}], "readOnly": false, "removed": false}, {"id": "Last Run", "displayName": "Last Run", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [-1260, 2750], "id": "1ebf0598-8080-4f10-966e-8d7a8727e5c6", "name": "Updating Automation Status", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are an elite content strategist.\n\nYour goal is to create viral-ready content blueprints in <strong>pure, clean HTML format</strong> — never Markdown, never escaped characters.\n\n<strong>Instructions:</strong><br>\n- Always return output in HTML with proper tags: <code>&lt;h1&gt;, &lt;h2&gt;, &lt;strong&gt;, &lt;br&gt;, &lt;hr&gt;</code><br>\n- Never use Markdown or encoded line breaks.<br>\n- Use <strong>real line breaks</strong> and section dividers using <code>&lt;hr&gt;</code>.<br>\n- Use <code>&lt;strong&gt;</code> for every label or heading description.<br>\n- Use <code>&lt;h1&gt;</code> for title, <code>&lt;h2&gt;</code> for sections.<br>\n- After every section, insert <code>&lt;hr&gt;</code>.<br>\n- Keep tone punchy, emoji-rich, and optimized for conversion.<br>\n- Never explain or include these instructions in output.\n\n<strong>Final Output Template:</strong>\n\n<pre>\n&lt;h1&gt;Post Title&lt;/h1&gt;\n&lt;strong&gt;Product Summary:&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;Viral Song:&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;Content Type:&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;Production Difficulty:&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;Emotional Triggers:&lt;/strong&gt; … &lt;br&gt;\n\n&lt;hr&gt;\n\n&lt;h2&gt;Viral Hook&lt;/h2&gt;\n…\n\n&lt;hr&gt;\n\n&lt;h2&gt;Polished Video Script&lt;/h2&gt;\n…\n\n&lt;hr&gt;\n\n&lt;h2&gt;Call‑to‑Action&lt;/h2&gt;\n…\n\n&lt;hr&gt;\n\n&lt;h2&gt;Audience Avatar&lt;/h2&gt;\n&lt;strong&gt;• Age Range:&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;• Likely Interests:&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;• Audience Mood:&lt;/strong&gt; … &lt;br&gt;\n\n&lt;hr&gt;\n\n&lt;h2&gt;Audience Insights&lt;/h2&gt;\n&lt;strong&gt;1.&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;2.&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;3.&lt;/strong&gt; … &lt;br&gt;\n\n&lt;hr&gt;\n\n&lt;h2&gt;Repurpose Angle&lt;/h2&gt;\n…\n\n&lt;hr&gt;\n\n&lt;h2&gt;TikTok &amp; Instagram Captions&lt;/h2&gt;\n&lt;strong&gt;1.&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;2.&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;3.&lt;/strong&gt; … &lt;br&gt;\n&lt;strong&gt;4.&lt;/strong&gt; … &lt;br&gt;\n\n&lt;hr&gt;\n\n&lt;h2&gt;SEO Intro&lt;/h2&gt;\n…\n\n&lt;hr&gt;\n\n&lt;h2&gt;Pro Tip&lt;/h2&gt;\n…\n</pre>\n", "role": "system"}, {"content": "=Here is the scraped post data — generate a Viral Content Playbook following the system rules.\n\n\n  \"id\": \"{{ $json.id }}\",\n  \"createdTime\": \"{{ $json.createdTime }}\",\n  \"ID\": \"{{ $json.ID }}\",\n  \"Users\": \"{{ ($json.Users && $json.Users[0]) || '' }}\",\n  \"Video Created Time\": \"{{ $json['Video Created Time'] }}\",\n  \"Caption\": \"{{ $json.Caption }}\",\n  \"Likes\": {{ $json.Likes }},\n  \"Shares\": {{ $json.Shares }},\n  \"Views\": {{ $json.Views }},\n  \"Saves\": {{ $json.Saves }},\n  \"Duration\": {{ $json.Duration }},\n  \"Comments\": {{ $json.Comments }},\n  \"Transcript\": \"{{ $json.Transcript }}\",\n  \"Vid URL\": \"{{ $json['Vid URL'] }}\",\n  \"Channel\": \"{{ $json.Channel }}\",\n  \"Top Performer\": {{ $json['Top Performer'] }},\n  \"Music Name\": \"{{ $json['Music Name'] }}\",\n  \"Music Author\": \"{{ $json['Music Author'] }}\",\n  \"Type\": \"{{ ($json.Type && $json.Type[0]) || '' }}\",\n  \"Language\": \"{{ $json.Language }}\",\n  \"Cover\": \n    \"url\": \"{{ $json.Cover && $json.Cover[0] && $json.Cover[0].url }}\"\n  ,\n  \"Media\": \n    \"url\": \"{{ $json.Media && $json.Media[0] && $json.Media[0].url }}\"\n"}, {"content": "You are a content strategist generating HTML content ONLY.\n\n- Return valid HTML using <h1>, <h2>, <strong>, <br>, <hr>\n- Never include \\n or \\t — real HTML only\n- Never use markdown\n- Don’t wrap output in code blocks\n- Don't include any explanations\n\n\n\n\nOutput Format :\n<h1>Unpopular Productivity Hacks</h1>\n<strong>Product Summary:</strong> A collection of hacks... <br>\n<strong>Viral Song:</strong> Sound by liz 🎵 <br>\n<hr>\n<h2>Viral Hook</h2>\nYou're not lazy, just unoptimized. Try these!\n<hr>\n\nwithout any \\n in middle", "role": "system"}]}, "simplify": false, "options": {"temperature": 0.8, "topP": 0.8}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-600, 2750], "id": "0c49609a-4a05-4723-a148-cf4413da4a62", "name": "Creating Viral AI Playbook HTML Content", "credentials": {"openAiApi": {"id": "sKh6glXnpiTVlKQt", "name": "<PERSON> Account"}}}, {"parameters": {"jsCode": "return [\n  {\n    json: {\n      html: $json.choices[0].message.content.replace(/\\n/g, '')\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-224, 2750], "id": "00ca9f6a-97ba-4be9-a49f-f012520a6c1e", "name": "Removing unwanted \\n characters"}, {"parameters": {"assignments": {"assignments": [{"id": "e7ee03ac-13e3-4fca-a7bc-57c8fc56dc42", "name": "document_name", "value": "=Viral AI Playbook", "type": "string"}, {"id": "48a07ef2-ae46-4bfc-aa7e-d92a74ef46d6", "name": "html_content", "value": "={{ $json.html }}", "type": "string"}, {"id": "22b02fba-ba72-423a-b92f-1191a183a554", "name": "drive_folder_id", "value": "1qFasd7qnWYMG5DuH9_QL6z47RJszAJRX", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-4, 2750], "id": "c1cc57c0-0278-4c62-b92f-35a3f3b607c6", "name": "Preparing Google Docs Inputs", "notesInFlow": true}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "uploadType", "value": "multipart"}, {"name": "supportsAllDrives", "value": "true"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "multipart/related; boundary=divider", "body": "={{ $json.rawData }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [436, 2750], "id": "84e26162-28b3-4f67-8bb0-08feb388e429", "name": "Viral Playbook Google Docs API Request", "notesInFlow": true, "credentials": {"googleDriveOAuth2Api": {"id": "T1O9F0bDSBdnmWxQ", "name": "Google Drive account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appB9jivsD6TraCjD", "mode": "list", "cachedResultName": "INSTIG8 Viral OS", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD"}, "table": {"__rl": true, "value": "tbl74w570Z8MgVZcn", "mode": "list", "cachedResultName": "📚 Audience Intelligence", "cachedResultUrl": "https://airtable.com/appB9jivsD6TraCjD/tbl74w570Z8MgVZcn"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Loop Over Items').item.json.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Top Performer", "displayName": "Top Performer", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Cover", "displayName": "Cover", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Media", "displayName": "Media", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Open URL", "displayName": "Open URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Language", "displayName": "Language", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Transcript", "displayName": "Transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Shares", "displayName": "Shares", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Saves", "displayName": "Saves", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Comments", "displayName": "Comments", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Duration", "displayName": "Duration", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Video Created Time", "displayName": "Video Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "dateTime", "readOnly": false, "removed": true}, {"id": "Last Modified", "displayName": "Last Modified", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Height", "displayName": "Height", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Overall Sentiment", "displayName": "Overall Sentiment", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": true}, {"id": "Tool Helpfulness", "displayName": "Tool Helpfulness", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "1", "value": "1"}, {"name": "2", "value": "2"}, {"name": "3", "value": "3"}, {"name": "4", "value": "4"}, {"name": "5", "value": "5"}], "readOnly": false, "removed": true}, {"id": "Common Questions", "displayName": "Common Questions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Key Insights", "displayName": "Key Insights", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music", "displayName": "Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Music Name", "displayName": "Music Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Music Author", "displayName": "Music Author", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "🔊 Video Type", "displayName": "🔊 Video Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "🎙️ Spoken Content", "value": "🎙️ Spoken Content"}, {"name": "🎵 Visual-Only", "value": "🎵 Visual-Only"}], "readOnly": false, "removed": true}, {"id": "Vid URL", "displayName": "Vid URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Hashtags", "displayName": "Hashtags", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Channel", "displayName": "Channel", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "YouTube", "value": "YouTube"}, {"name": "TikTok", "value": "TikTok"}, {"name": "Facebook", "value": "Facebook"}, {"name": "Instagram", "value": "Instagram"}, {"name": "LinkedIn", "value": "LinkedIn"}, {"name": "Twitter", "value": "Twitter"}], "readOnly": false, "removed": true}, {"id": "Sentiment Status", "displayName": "Sentiment Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Complete", "value": "Complete"}], "readOnly": false, "removed": true}, {"id": "Post Type", "displayName": "Post Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Profile Type", "displayName": "Profile Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Company", "value": "Company"}, {"name": "Person", "value": "Person"}, {"name": "", "value": ""}], "readOnly": false, "removed": true}, {"id": "LinkedIn Post Url", "displayName": "LinkedIn Post Url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open <PERSON><PERSON>", "displayName": "Open <PERSON><PERSON>", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "LinkedIn Post Text", "displayName": "LinkedIn Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post Date", "displayName": "Post Date", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Post URL", "displayName": "Post URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Open URL FB", "displayName": "Open URL FB", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Top Performance", "displayName": "Top Performance", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Users", "displayName": "Users", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Quote Count", "displayName": "Quote Count", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Viral Playbook", "displayName": "Viral Playbook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Viral Playbook Button", "displayName": "Viral Playbook Button", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [734, 2825], "id": "2cb6b599-b0f1-40a2-a630-d5dc7c84d518", "name": "Updating Top Performers with Viral Playbooks", "credentials": {"airtableTokenApi": {"id": "iQTVHc2jAIwK4nbL", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $('Merge4').item.json.id }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1110, 1075], "id": "fd63d9eb-02b6-4f6f-9451-55f7237fb688", "name": "Checks Duplicates based on prev executions8"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/clockworks~tik<PERSON>-comments-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"commentsPerPost\": 15,\n  \"excludePinnedPosts\": false,\n  \"maxRepliesPerComment\": 0,\n  \"postURLs\": {{ $json.postURLs.toJsonString() }},\n  \"resultsPerPage\": 1\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, -100], "id": "d66f4077-4138-444f-9337-d64c8bf499cd", "name": "Scraping Comments for Sentimental Analysis"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apify~instagram-comment-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"directUrls\": {{ $json.postURLs.toJsonString() }},\n    \"includeNestedComments\": false,\n    \"isNewestComments\": false,\n    \"resultsLimit\": 15\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [436, 225], "id": "4e7283f8-4dda-47f1-a6d1-a0e57db4be3a", "name": "Scraping Comments for Sentimental Analysis1"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/streamers~youtube-comments-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"commentsSortBy\": \"0\",\n    \"maxComments\": 15,\n    \"startUrls\": {{ JSON.stringify($json.startUrls) }}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 620], "id": "faba8a8f-31ad-472b-b5de-ca7f3c0a5e2a", "name": "Scraping Youtube Comments for Sentimental Analysis"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/scraper_one~x-post-replies-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"postUrls\": {{ $json.postURLs.toJsonString() }},\n    \"resultsLimit\": 15\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-4, 1000], "id": "cad824ec-c505-45ba-a080-b64e964a37b9", "name": "Scraping Twitter Posts for Comments for Sentimental Analysis"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apimaestro~linkedin-post-comments-replies-engagements-scraper-no-cookies/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"limit\": 15,\n    \"postIds\": \n{{ $json.postURLs.toJsonString() }}\n\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [436, 1625], "id": "52dd7e2b-c394-40b5-9657-b99e32f1af78", "name": "Scraping LinkedIn Posts for Comments for Sentimental Analysis", "retryOnFail": true, "maxTries": 2, "waitBetweenTries": 2000, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apimaestro~linkedin-post-comments-replies-engagements-scraper-no-cookies/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"limit\": 20,\n    \"postIds\": [\n{{ $json.postURLs.toJsonString() }}\n]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 1320], "id": "fa9cefad-9810-448e-ae4b-fafe0519d4c9", "name": "Scraping LinkedIn Posts for Comments for Sentimental Analysis1", "retryOnFail": true, "maxTries": 2, "waitBetweenTries": 2000, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/scraper_one~facebook-comments-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"postUrls\": {{ $json.urls.toJsonString() }},\n  \"resultsLimit\": 5\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [216, 1950], "id": "e6be80bb-6f62-486f-92a5-577e01cc6e61", "name": "Scraping Facebook Posts for Comments for Sentimental Analysis", "alwaysOutputData": false, "retryOnFail": true, "maxTries": 2}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/scraper_one~facebook-comments-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"postUrls\": {{ $json.permalinks.toJsonString() }},\n  \"resultsLimit\": 30\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [436, 2275], "id": "bfadee69-e1d5-4c5f-9eb2-0a89d6403192", "name": "Scraping Facebook Posts for Comments for Sentimental Analysis1", "alwaysOutputData": false, "retryOnFail": true, "maxTries": 2}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "Updates Automation Status", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "TikTok", "type": "main", "index": 0}, {"node": "Instagram", "type": "main", "index": 0}, {"node": "Twitter", "type": "main", "index": 0}, {"node": "YouTube", "type": "main", "index": 0}, {"node": "LinkedIn", "type": "main", "index": 0}, {"node": "Facebook", "type": "main", "index": 0}, {"node": "Updating Automation Status", "type": "main", "index": 0}], [], []]}, "TikTok": {"main": [[{"node": "Search TikTok Inputs", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Scraping Comments for Sentimental Analysis", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Field For Subtitle Link", "type": "main", "index": 0}]]}, "Code3": {"main": [[{"node": "Scraping Comments for Sentimental Analysis1", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Checks Duplicates based on prev executions1", "type": "main", "index": 0}]]}, "Instagram": {"main": [[{"node": "Search Instagram Inputs", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Filter for False Posts", "type": "main", "index": 0}]]}, "Filter for False Posts": {"main": [[{"node": "Code3", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 1}]]}, "Search YouTube Inputs": {"main": [[{"node": "Formats Inputs Items into Array of Search Hashes", "type": "main", "index": 0}]]}, "Search Instagram Inputs": {"main": [[{"node": "Formats Inputs Items into Array of Hashes and Profiles1", "type": "main", "index": 0}]]}, "Search TikTok Inputs": {"main": [[{"node": "Formats Inputs Items into Array of Hashes and Profiles", "type": "main", "index": 0}]]}, "Create JSON": {"main": [[{"node": "Scraping Youtube Comments for Sentimental Analysis", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "Routes Based on Transcript Length", "type": "main", "index": 0}]]}, "Code5": {"main": [[{"node": "Scrape Twitter Posts", "type": "main", "index": 0}]]}, "Summarize3": {"main": [[{"node": "Merge4", "type": "main", "index": 0}]]}, "Create JSON1": {"main": [[{"node": "Scraping Twitter Posts for Comments for Sentimental Analysis", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "Performs Sentimental Analysis4", "type": "main", "index": 0}]]}, "Search Twitter Inputs": {"main": [[{"node": "Code5", "type": "main", "index": 0}]]}, "YouTube": {"main": [[{"node": "Search YouTube Inputs", "type": "main", "index": 0}]]}, "Twitter": {"main": [[{"node": "Search Twitter Inputs", "type": "main", "index": 0}]]}, "LinkedIn": {"main": [[{"node": "Search LinkedIn Inputs1", "type": "main", "index": 0}]]}, "Facebook": {"main": [[{"node": "Search Facebook Inputs", "type": "main", "index": 0}]]}, "Search Facebook Inputs": {"main": [[{"node": "Code7", "type": "main", "index": 0}]]}, "Search LinkedIn Inputs1": {"main": [[{"node": "Code6", "type": "main", "index": 0}]]}, "Code6": {"main": [[{"node": "If", "type": "main", "index": 0}, {"node": "If1", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Scrapes LinkedIn Posts based on HashTags", "type": "main", "index": 0}]]}, "Create Json": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Summarize4": {"main": [[{"node": "Merge9", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Scraping LinkedIn Posts for Comments for Sentimental Analysis1", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Scrapes LinkedIn Profiles and Companies", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Merge8": {"main": [[{"node": "Performs Sentimental Analysis6", "type": "main", "index": 0}]]}, "Create Json1": {"main": [[{"node": "Scraping LinkedIn Posts for Comments for Sentimental Analysis", "type": "main", "index": 0}]]}, "Summarize5": {"main": [[{"node": "Merge8", "type": "main", "index": 0}]]}, "Merge9": {"main": [[{"node": "Performs Sentimental Analysis5", "type": "main", "index": 0}]]}, "Code7": {"main": [[{"node": "If2", "type": "main", "index": 0}, {"node": "If3", "type": "main", "index": 0}]]}, "Create Json2": {"main": [[{"node": "Scraping Facebook Posts for Comments for Sentimental Analysis", "type": "main", "index": 0}]]}, "Summarize6": {"main": [[{"node": "Merge5", "type": "main", "index": 0}]]}, "Merge5": {"main": [[{"node": "Performs Sentimental Analysis7", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Scrape Posts Based on Facebook Profiles", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Split Out2": {"main": [[{"node": "Scrape Facebook Posts Based on Hashtags", "type": "main", "index": 0}]]}, "Create Json3": {"main": [[{"node": "Scraping Facebook Posts for Comments for Sentimental Analysis1", "type": "main", "index": 0}]]}, "Summarize7": {"main": [[{"node": "Merge6", "type": "main", "index": 0}]]}, "Merge6": {"main": [[{"node": "Performs Sentimental Analysis8", "type": "main", "index": 0}]]}, "Prepare_Request": {"main": [[{"node": "Viral Playbook Google Docs API Request", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Airtable17", "type": "main", "index": 0}], [{"node": "Creating Viral AI Playbook HTML Content", "type": "main", "index": 0}]]}, "Updates Automation Status": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Formats Inputs Items into Array of Hashes and Profiles": {"main": [[{"node": "Running Tik<PERSON><PERSON> Scraper", "type": "main", "index": 0}]]}, "Running TikTok Scraper": {"main": [[{"node": "Filters Posts based on Metrics", "type": "main", "index": 0}]]}, "Filters Posts based on Metrics": {"main": [[{"node": "Code1", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Concatenates Comments based on vid_url": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Field For Subtitle Link": {"main": [[{"node": "HTTP Request for Subtitle Fetch", "type": "main", "index": 0}]]}, "HTTP Request for Subtitle Fetch": {"main": [[{"node": "Checks Duplicates based on prev executions", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs", "type": "main", "index": 0}]]}, "Formats Inputs Items into Array of Hashes and Profiles1": {"main": [[{"node": "Running Instagram Hashtag Scraper", "type": "main", "index": 0}, {"node": "Running Instagram Profile Post Scraper", "type": "main", "index": 0}]]}, "Running Instagram Hashtag Scraper": {"main": [[{"node": "Merge2", "type": "main", "index": 0}]]}, "Running Instagram Profile Post Scraper": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "Concatenates Comments based on vid url": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Creates New Record in Audience Intelligence Database1": {"main": [[]]}, "Formats Inputs Items into Array of Search Hashes": {"main": [[{"node": "Running Youtube Scraper", "type": "main", "index": 0}]]}, "Running Youtube Scraper": {"main": [[{"node": "Merge3", "type": "main", "index": 1}, {"node": "Create JSON", "type": "main", "index": 0}]]}, "Routes Based on Transcript Length": {"main": [[{"node": "Performs Sentimental Analysis2", "type": "main", "index": 0}], [{"node": "Summarising Long Transcripts", "type": "main", "index": 0}]]}, "Concatenates Comments based on vid url1": {"main": [[{"node": "Merge3", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs": {"main": [[{"node": "Creates New Record in Audience Intelligence Database", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs1": {"main": [[{"node": "Creates New Record in Audience Intelligence Database2", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs2": {"main": [[{"node": "Creates New Record in Audience Intelligence Database3", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs3": {"main": [[{"node": "Creates New Record in Audience Intelligence Database4", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs4": {"main": [[{"node": "Creates New Record in Audience Intelligence Database5", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs5": {"main": [[{"node": "Creates New Record in Audience Intelligence Database6", "type": "main", "index": 0}]]}, "Searches for Input Hashtags and Profile Info from Inputs6": {"main": [[{"node": "Creates New Record in Audience Intelligence Database7", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions": {"main": [[{"node": "Performs Sentimental Analysis", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions1": {"main": [[{"node": "Performs Sentimental Analysis1", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions2": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs1", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions3": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs2", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions4": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs3", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions5": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs4", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions6": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs5", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions7": {"main": [[{"node": "Searches for Input Hashtags and Profile Info from Inputs6", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis1": {"main": [[{"node": "Creates New Record in Audience Intelligence Database1", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis2": {"main": [[{"node": "Checks Duplicates based on prev executions2", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis3": {"main": [[{"node": "Checks Duplicates based on prev executions3", "type": "main", "index": 0}]]}, "Summarising Long Transcripts": {"main": [[{"node": "Performs Sentimental Analysis3", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis4": {"main": [[{"node": "Checks Duplicates based on prev executions8", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis5": {"main": [[{"node": "Checks Duplicates based on prev executions4", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis6": {"main": [[{"node": "Checks Duplicates based on prev executions5", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis7": {"main": [[{"node": "Checks Duplicates based on prev executions6", "type": "main", "index": 0}]]}, "Performs Sentimental Analysis8": {"main": [[{"node": "Checks Duplicates based on prev executions7", "type": "main", "index": 0}]]}, "Scrape Twitter Posts": {"main": [[{"node": "Create JSON1", "type": "main", "index": 0}, {"node": "Merge4", "type": "main", "index": 1}]]}, "Scrapes LinkedIn Profiles and Companies": {"main": [[{"node": "Create <PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Merge9", "type": "main", "index": 1}]]}, "Scrapes LinkedIn Posts based on HashTags": {"main": [[{"node": "Create Json1", "type": "main", "index": 0}, {"node": "Merge8", "type": "main", "index": 1}]]}, "Scrape Posts Based on Facebook Profiles": {"main": [[{"node": "Create Json2", "type": "main", "index": 0}, {"node": "Merge5", "type": "main", "index": 1}]]}, "Scrape Facebook Posts Based on Hashtags": {"main": [[{"node": "Create Json3", "type": "main", "index": 0}, {"node": "Merge6", "type": "main", "index": 1}]]}, "Searches For Top Performing Posts": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Updating Automation Status": {"main": [[{"node": "Searches For Top Performing Posts", "type": "main", "index": 0}]]}, "Creating Viral AI Playbook HTML Content": {"main": [[{"node": "Removing unwanted \\n characters", "type": "main", "index": 0}]]}, "Removing unwanted \\n characters": {"main": [[{"node": "Preparing Google Docs Inputs", "type": "main", "index": 0}]]}, "Preparing Google Docs Inputs": {"main": [[{"node": "Prepare_Request", "type": "main", "index": 0}]]}, "Viral Playbook Google Docs API Request": {"main": [[{"node": "Updating Top Performers with Viral Playbooks", "type": "main", "index": 0}]]}, "Updating Top Performers with Viral Playbooks": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Checks Duplicates based on prev executions8": {"main": [[{"node": "Airtable8", "type": "main", "index": 0}]]}, "Scraping Comments for Sentimental Analysis": {"main": [[{"node": "Concatenates Comments based on vid_url", "type": "main", "index": 0}]]}, "Scraping Comments for Sentimental Analysis1": {"main": [[{"node": "Concatenates Comments based on vid url", "type": "main", "index": 0}]]}, "Scraping Youtube Comments for Sentimental Analysis": {"main": [[{"node": "Concatenates Comments based on vid url1", "type": "main", "index": 0}]]}, "Scraping Twitter Posts for Comments for Sentimental Analysis": {"main": [[{"node": "Summarize3", "type": "main", "index": 0}]]}, "Scraping LinkedIn Posts for Comments for Sentimental Analysis": {"main": [[{"node": "Summarize5", "type": "main", "index": 0}]]}, "Scraping LinkedIn Posts for Comments for Sentimental Analysis1": {"main": [[{"node": "Summarize4", "type": "main", "index": 0}]]}, "Scraping Facebook Posts for Comments for Sentimental Analysis": {"main": [[{"node": "Summarize6", "type": "main", "index": 0}]]}, "Scraping Facebook Posts for Comments for Sentimental Analysis1": {"main": [[{"node": "Summarize7", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "b4bfa16b-6eb0-4397-ab1f-df40422dbd43", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ff3823aeb1dd592aa71eb8af96615f01af25e12826f45d2c3e771d17c05cebb4"}, "id": "1oE6CgYw98C5fHeY", "tags": []}