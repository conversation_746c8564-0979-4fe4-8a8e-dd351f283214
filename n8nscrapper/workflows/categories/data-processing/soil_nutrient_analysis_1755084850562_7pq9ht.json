{"name": "Soil Nutrient Analysis", "nodes": [{"parameters": {"content": "## Soil Nutrient Analysis", "height": 520, "width": 1100}, "id": "238b2e3b-86db-47f4-a94c-deb533b0df67", "name": "<PERSON>y", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-500, -250]}, {"parameters": {"httpMethod": "POST", "path": "soil_nutrient_analysis"}, "id": "dbc9164d-e065-472b-b194-d749c92cb1b6", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-300, 0]}, {"parameters": {"chunkSize": 400, "chunkOverlap": 40}, "id": "b1e50af6-66c9-4357-b518-ca1eccb1eb61", "name": "Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [-100, 0]}, {"parameters": {"model": "default"}, "id": "eabc7a23-b1c1-49d2-910f-ded6288e3a35", "name": "Embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsHuggingFace", "typeVersion": 1, "position": [100, 0], "credentials": {"huggingFaceApi": {"id": "HF_API", "name": "HuggingFace"}}}, {"parameters": {"mode": "insert", "indexName": "soil_nutrient_analysis"}, "id": "9856bc50-88d2-4c09-a56d-dea2e17e04d9", "name": "Insert", "type": "@n8n/n8n-nodes-langchain.vectorStoreWeaviate", "typeVersion": 1, "position": [300, 0], "credentials": {"weaviateApi": {"id": "WEAVIATE_API", "name": "Weaviate account"}}}, {"parameters": {"indexName": "soil_nutrient_analysis"}, "id": "6fe461aa-020e-46fe-8f01-09419d9cb389", "name": "Query", "type": "@n8n/n8n-nodes-langchain.vectorStoreWeaviate", "typeVersion": 1, "position": [300, -180], "credentials": {"weaviateApi": {"id": "WEAVIATE_API", "name": "Weaviate account"}}}, {"parameters": {"name": "Weaviate"}, "id": "0efa7417-f995-4363-9854-b10e19685d81", "name": "Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [480, -180]}, {"parameters": {}, "id": "d1e84586-1ce4-4fb2-8fe9-936f69325b35", "name": "Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [480, -40]}, {"parameters": {}, "id": "17fb76c5-29db-4d96-bb2b-d5e9a424e249", "name": "Cha<PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatHf", "typeVersion": 1, "position": [480, -340], "credentials": {"huggingFaceApi": {"id": "HF_API", "name": "HuggingFace"}}}, {"parameters": {"promptType": "define", "text": "={{ $json }}"}, "id": "ec9091e0-c299-41e3-a3ae-0449c19c5009", "name": "Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [760, -40]}, {"parameters": {"operation": "append", "documentId": "SHEET_ID", "sheetName": "Log"}, "id": "291bccce-04b0-482e-b8aa-887b34f8d347", "name": "Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [960, -40], "credentials": {"googleSheetsOAuth2Api": {"id": "SHEETS_API", "name": "Sheets"}}}], "connections": {"Webhook": {"main": [[{"node": "Splitter", "type": "main", "index": 0}, {"node": "Memory", "type": "main", "index": 0}]]}, "Splitter": {"main": [[{"node": "Embeddings", "type": "main", "index": 0}]], "ai_textSplitter": [[{"node": "Insert", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Insert", "type": "ai_embedding", "index": 0}, {"node": "Query", "type": "ai_embedding", "index": 0}]]}, "Insert": {"ai_document": [[]]}, "Query": {"ai_vectorStore": [[{"node": "Tool", "type": "ai_vectorStore", "index": 0}]]}, "Tool": {"ai_tool": [[{"node": "Agent", "type": "ai_tool", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "Agent", "type": "ai_memory", "index": 0}]]}, "Chat": {"ai_languageModel": [[{"node": "Agent", "type": "ai_languageModel", "index": 0}]]}, "Agent": {"main": [[{"node": "Sheet", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}