{"meta": {"instanceId": "d9e1cda642de4f8909345a14409b4a3b496085da88cc8ef314554b8bf411da6d"}, "nodes": [{"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1120, 460], "id": "respond-webhook", "name": "Respond to Webhook"}, {"parameters": {"jsCode": "const output = $json.output || \"\";\nlet cleanOutput = output;\nif (output.includes(\"</think>\")) {\n  cleanOutput = output.split(\"</think>\")[1].trim();\n}\ntry {\n  const jsonMatch = cleanOutput.match(/\\{[\\s\\S]*?\\}/);\n  if (jsonMatch) {\n    const parsed = JSON.parse(jsonMatch[0]);\n    const result = parsed.Coding || parsed.Reasoning || parsed.RAG || parsed.Conversation || cleanOutput;\n    return [{ json: { result: result } }];\n  }\n} catch (e) {}\nreturn [{ json: { result: cleanOutput } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 460], "id": "prepare-response", "name": "Prepare Response"}, {"parameters": {"httpMethod": "POST", "path": "invoke_n8n_agent", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [160, 240], "id": "webhook-trigger", "name": "Webhook Trigger", "webhookId": "6a15720b-4402-434a-bfe7-575206a2a749"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [180, 400], "id": "chat-trigger", "name": "<PERSON><PERSON>", "webhookId": "91e01a05-367d-4867-a053-68626e1f90a9"}, {"parameters": {"options": {"systemMessage": "Tu es un assistant IA qui doit utiliser l'outil RAG Tool pour répondre aux questions de recherche documentaire.\n\nTa mission:\n1. Analyser la question de l'utilisateur\n2. Si c'est une question nécessitant une recherche documentaire, utiliser l'outil RAG Tool\n3. Fournir une réponse basée sur les résultats obtenus\n\nFormat de réponse JSON:\n{\n  \"Coding\": null,\n  \"Reasoning\": null,\n  \"RAG\": \"ta réponse ici pour les questions de recherche\",\n  \"Conversation\": \"ta réponse ici pour les questions conversationnelles\"\n}\n\nUne seule clé doit contenir du contenu, les autres doivent être à null."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [480, 400], "id": "ai-agent", "name": "AI Agent"}, {"parameters": {"model": "qwen3:8b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [420, 620], "id": "ollama-chat", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "uRmESWlaceJnOjmU", "name": "Ollama account"}}}, {"parameters": {"name": "RAG Tool", "description": "Tool for searching company documents and knowledge base. Use this when users ask questions that require document search or factual information lookup.", "sseEndpoint": "http://localhost:5678/mcp/MCPServer/sse"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1, "position": [700, 640], "id": "rag-tool", "name": "RAG Tool"}], "connections": {"Prepare Response": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Prepare Response", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "RAG Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "pinData": {}}