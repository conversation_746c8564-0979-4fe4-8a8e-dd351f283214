{"name": "YouTube Transcript to Blog", "nodes": [{"parameters": {"content": "Automated workflow: YouTube Transcript to Blog", "height": 530, "width": 1100, "color": 5}, "id": "bd0e0586-b4e8-4da9-8d93-57602a5219de", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-480, -240]}, {"parameters": {"httpMethod": "POST", "path": "youtube-transcript-to-blog"}, "id": "fe1b58d8-2bdc-4ce8-bcf8-95c9b0b6477d", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-300, 0]}, {"parameters": {"chunkSize": 400, "chunkOverlap": 40}, "id": "ceba2716-6fe1-48de-9bad-512e27bac9c1", "name": "Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [-130, 0]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "20c8a22e-bf48-44b4-ac45-74b80231e773", "name": "Embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [70, 0], "credentials": {"openAiApi": {"id": "OPENAI_API", "name": "OpenAI"}}}, {"parameters": {"mode": "insert", "options": {}, "pineconeIndex": {"__rl": true, "value": "youtube_transcript_to_blog", "mode": "list", "cachedResultName": "youtube_transcript_to_blog"}}, "id": "d5bd2477-053c-4954-b46a-607df0a23935", "name": "Pinecone Insert", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [270, 0], "credentials": {"pineconeApi": {"id": "PINECONE_API", "name": "Pinecone account"}}}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "youtube_transcript_to_blog", "mode": "list", "cachedResultName": "youtube_transcript_to_blog"}}, "id": "d5b8f51f-8904-4039-ab7f-d4cffd717b14", "name": "Pinecone Query", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [270, -180], "credentials": {"pineconeApi": {"id": "PINECONE_API", "name": "Pinecone account"}}}, {"parameters": {"name": "Pinecone", "description": "Vector context"}, "id": "42485b1f-2381-4e86-91af-ddbb3b687689", "name": "Vector Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [450, -180]}, {"parameters": {}, "id": "5fccb006-c66d-407a-b010-a9f7ca49c0d7", "name": "Window Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [450, -40]}, {"parameters": {"options": {}}, "id": "1454b8d0-0703-45e1-b51c-2f9396b4e4d1", "name": "Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1, "position": [450, -340], "credentials": {"anthropicApi": {"id": "ANTHROPIC_API", "name": "Anthropic"}}}, {"parameters": {"promptType": "define", "text": "Process the following data for task 'YouTube Transcript to Blog':\n\n{{ $json }}", "options": {"systemMessage": "You are an assistant for YouTube Transcript to Blog"}}, "id": "a8c23beb-eeb0-45bb-bee8-7d841bf39330", "name": "RAG Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [720, -40]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "SHEET_ID", "mode": "list", "cachedResultName": "YouTube Transcript to Blog"}, "sheetName": {"__rl": true, "value": "Log", "mode": "list", "cachedResultName": "Log"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "={{$json[\"RAG Agent\"].text}}"}, "schema": []}}, "id": "333c30d3-f292-497e-af3d-fffa5575fd75", "name": "Append Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [930, -40], "credentials": {"googleSheetsOAuth2Api": {"id": "SHEETS_API", "name": "Google Sheets account"}}}, {"parameters": {"channel": "#alerts", "text": "YouTube Transcript to Blog error: {$json.error.message}"}, "id": "f84bd864-69d2-4c78-a3df-e0d4bdaf36c0", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [930, 120], "credentials": {"slackApi": {"id": "SLACK_API", "name": "<PERSON><PERSON>ck"}}}], "connections": {"Webhook Trigger": {"main": [[{"node": "Text Splitter", "type": "main", "index": 0}, {"node": "Window Memory", "type": "main", "index": 0}]]}, "Text Splitter": {"main": [[{"node": "Embeddings", "type": "main", "index": 0}]], "ai_textSplitter": [[{"node": "Pinecone Insert", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Pinecone Insert", "type": "ai_embedding", "index": 0}, {"node": "Pinecone Query", "type": "ai_embedding", "index": 0}]]}, "Pinecone Insert": {"ai_document": [[]]}, "Pinecone Query": {"ai_vectorStore": [[{"node": "Vector Tool", "type": "ai_vectorStore", "index": 0}]]}, "Vector Tool": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "Window Memory": {"ai_memory": [[{"node": "RAG Agent", "type": "ai_memory", "index": 0}]]}, "Chat Model": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "RAG Agent": {"main": [[{"node": "Append Sheet", "type": "main", "index": 0}]], "onError": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "triggerCount": 1}