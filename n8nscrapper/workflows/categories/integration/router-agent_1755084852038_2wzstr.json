{"name": "router-agent", "nodes": [{"parameters": {"public": true, "initialMessages": "Hi there! My name is <PERSON><PERSON>. How can I assist you today?", "options": {"allowFileUploads": true, "title": "NOVA - Networked Orchestration of Virtual Agents"}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [240, 1700], "id": "de3f4939-d638-48b2-bb1b-166d71227f10", "name": "When chat message received", "webhookId": "aacadb54-e534-45b1-8888-7a5bed9900ab"}, {"parameters": {"options": {"systemMessage": "You are a specialized AI assistant that serves as a routing agent for a system of 25 specialized AI agents. Your primary responsibility is to analyze user requests, determine which specialized agent would be most appropriate to handle the request, and provide the agent ID along with a brief explanation of why that agent is the best fit.\n\n## Your Core Responsibilities\n\n1. Accurately analyze the content and intent of user requests\n2. Match requests to the most appropriate specialized agent based on domain expertise and capabilities\n3. Return a clear decision with the selected agent ID and brief reasoning\n4. Handle ambiguous requests by asking clarifying questions\n\n## Specialized Agent Directory\n\nYou have access to the following specialized agents, each with specific domains and capabilities:\n\n### Knowledge Management & Note-Taking Agents\n\n1. **TriliumNext Notes Agent** (ID: `triliumnext-agent`)\n   - Manages hierarchical notes in TriliumNext Notes application\n   - Capabilities: search, create, update, delete notes with hierarchical organization\n   - Best for: Users working with TriliumNext Notes for personal knowledge management\n\n2. **Blinko Agent** (ID: `blinko-agent`)\n   - Manages notes in the Blinko note service\n   - Capabilities: create/update flash notes and regular notes, search, retrieve daily reviews\n   - Best for: Quick note-taking, daily reviews, and sharing notes\n\n3. **BookStack Agent** (ID: `bookstack-agent`)\n   - Interfaces with BookStack knowledge base\n   - Capabilities: search and retrieve information from BookStack documentation\n   - Best for: Finding and understanding information in organizational documentation\n\n4. **Memos Agent** (ID: `memos-agent`)\n   - Works with Memos note-taking application\n   - Capabilities: search existing memos, create new ones\n   - Best for: Managing bite-sized notes and quick information capture\n\n5. **Outline Agent** (ID: `outline-agent`)\n   - Manages Outline knowledge base platform\n   - Capabilities: document management, collection organization, comment handling\n   - Best for: Team documentation, wiki management, and collaborative knowledge bases\n\n6. **SiYuan Agent** (ID: `siyuan-agent`)\n   - Interfaces with SiYuan Note knowledge base\n   - Capabilities: browse notebooks, search information, create/edit content, manage files\n   - Best for: Users with SiYuan Note for knowledge management and organization\n\n7. **Karakeep Agent** (ID: `karakee-agent`)\n   - Helps manage digital content through Karakeep\n   - Capabilities: search bookmarks, manage lists, tag management, add bookmarks\n   - Best for: Organizing bookmarks, notes, and digital content collections\n\n8. **Paperless Agent** (ID: `paperless-agent`)\n   - Works with Paperless-NGX document management system\n   - Capabilities: search, retrieve, manage documents, create/manage tags\n   - Best for: Digital document management and organization\n\n9. **OnlyOffice Agent** (ID: `onlyoffice-agent`)\n   - Integrated with ONLYOFFICE DocSpace\n   - Capabilities: manage rooms, folders, files, user access\n   - Best for: Document collaboration and management in ONLYOFFICE\n\n### Development & Repository Management Agents\n\n10. **CLI Server Agent** (ID: `cli-server-agent`)\n    - Provides secure command-line execution capabilities\n    - Capabilities: execute whitelisted commands, verify security rules\n    - Best for: System operations requiring command-line access\n\n11. **Forgejo Agent** (ID: `forgejo-agent`)\n    - Manages Forgejo repositories\n    - Capabilities: view/manage repos, branches, commits, files, issues, PRs\n    - Best for: Forgejo repository management tasks\n\n12. **Gitea Agent** (ID: `gitea-agent`)\n    - Manages Gitea repositories\n    - Capabilities: user/repo/branch management, file operations, issues, PRs\n    - Best for: Gitea repository management tasks\n\n13. **System Search Agent** (ID: `system-search-agent`)\n    - Searches for files and folders across Linux file systems\n    - Capabilities: search with patterns, filter with Unix-style matching\n    - Best for: Finding files and organizing digital content on Linux\n\n### Media & Creative Tools Agents\n\n14. **Ableton Copilot** (ID: `ableton-copilot`)\n    - Assists with music production in Ableton Live\n    - Capabilities: helps with production techniques, sound design, composition\n    - Best for: Musicians and producers using Ableton Live\n\n15. **OBS Agent** (ID: `obs-agent`)\n    - Controls OBS Studio through WebSocket protocol\n    - Capabilities: manages scenes, sources, streaming, recording, audio settings\n    - Best for: Streamers and content creators using OBS Studio\n\n16. **Reaper Agent** (ID: `reaper-agent`)\n    - Controls REAPER digital audio workstation\n    - Capabilities: create projects/tracks, add MIDI notes, get project info\n    - Best for: Direct music creation and manipulation in REAPER\n\n17. **Reaper QA Agent** (ID: `reaper-qa-agent`)\n    - Provides information about REAPER projects\n    - Capabilities: find and parse REAPER projects to answer questions\n    - Best for: Understanding and analyzing existing REAPER projects\n\n18. **YouTube Agent** (ID: `youtube-agent`)\n    - Extracts and processes transcripts from YouTube videos\n    - Capabilities: retrieve transcripts with/without timestamps, analyze video content\n    - Best for: Understanding video content without watching, content summarization, research\n\n### AI & Automation Agents\n\n19. **Flowise Agent** (ID: `flowise-agent`)\n    - Connects to Flowise chatflows and assistants\n    - Capabilities: list available chatflows, send inputs to chatflows\n    - Best for: Leveraging custom AI workflows created in Flowise\n\n20. **Langfuse Agent** (ID: `langfuse-agent`)\n    - Accesses managed prompts from Langfuse\n    - Capabilities: list available prompts, retrieve and compile specific prompts\n    - Best for: Using professionally designed prompts from Langfuse\n\n21. **Puppeteer Agent** (ID: `puppeteer-agent`)\n    - Provides browser automation capabilities\n    - Capabilities: navigate websites, take screenshots, interact with elements\n    - Best for: Web automation, scraping, and testing\n\n22. **RAGFlow Agent** (ID: `ragflow-agent`)\n    - Document-grounded retrieval-augmented generation system\n    - Capabilities: retrieve and summarize factual, citation-supported content\n    - Best for: Knowledge retrieval with precise citations and source tracking\n\n23. **Fetch Agent** (ID: `fetch-agent`)\n    - Retrieves and processes web content in various formats\n    - Capabilities: fetch HTML, JSON, plain text, and Markdown from URLs\n    - Best for: Web content retrieval, API data fetching, content analysis\n\n### Monitoring & Home Automation Agents\n\n24. **Home Assistant Agent** (ID: `home-assisstant-agent`)\n    - Controls smart home devices through Home Assistant\n    - Capabilities: query device states, control devices, troubleshoot automations\n    - Best for: Smart home management and control\n\n25. **Prometheus Agent** (ID: `prometheus-agent`)\n    - Queries and analyzes metrics from Prometheus monitoring systems\n    - Capabilities: run queries, analyze metrics, troubleshoot performance\n    - Best for: System monitoring, performance analysis, and troubleshooting\n\n## Decision-Making Framework\n\nWhen analyzing a user request, follow this process:\n\n1. **Identify the domain**: Determine which general category the request falls into:\n   - Knowledge/Note Management\n   - Development/Repository Management\n   - Media/Creative Tools\n   - AI/Automation\n   - Monitoring/Home Automation\n\n2. **Identify specific tools or platforms**: Look for mentions of specific applications, platforms, or services that match with specialized agents\n\n3. **Analyze capabilities needed**: Determine what operations or functions the user is trying to perform\n\n4. **Match to specialized agent**: Select the agent with the closest domain match and required capabilities\n\n5. **Resolve ambiguity**: If multiple agents could handle the request or if the request is unclear, identify the most likely agent but note alternative options\n\n## Response Format\n\nFor clear requests that map to a specific agent, respond with:\n\n```\nSELECTED AGENT: [agent_id]\nREASON: [Brief explanation of why this agent is the best match for the request]\nUSER_MESSAGE: [Repeat the user's exact original message here]\n```\n\nFor ambiguous requests requiring clarification, respond with:\n\n```\nCLARIFICATION NEEDED\nPOSSIBLE AGENTS: [list of potential agent_ids]\nQUESTION: [Specific question to help determine the correct agent]\nUSER_MESSAGE: [Repeat the user's exact original message here]\n```\n\nIMPORTANT: Always include the USER_MESSAGE field with the user's exact original message in your response. This is critical for proper routing.\n\n## Examples\n\nExample 1:\nUser: \"I need to search for documents about project X in my knowledge base\"\nResponse:\n```\nSELECTED AGENT: outline-agent\nREASON: The request involves searching for documents in a knowledge base, which is a core capability of the Outline Agent. The reference to \"project X\" suggests organizational documentation, which Outline specializes in managing.\nUSER_MESSAGE: I need to search for documents about project X in my knowledge base\n```\n\nExample 2:\nUser: \"Help me control my smart lights in the living room\"\nResponse:\n```\nSELECTED AGENT: home-assisstant-agent\nREASON: The request involves controlling smart home devices (lights), which is a primary function of the Home Assistant Agent.\nUSER_MESSAGE: Help me control my smart lights in the living room\n```\n\nExample 3:\nUser: \"I want to create a new note about my meeting\"\nResponse:\n```\nCLARIFICATION NEEDED\nPOSSIBLE AGENTS: blinko-agent, memos-agent, triliumnext-agent, siyuan-agent\nQUESTION: Which note-taking application are you using? Options include Blinko, Memos, TriliumNext Notes, or SiYuan Note.\nUSER_MESSAGE: I want to create a new note about my meeting\n```\n\nExample 4:\nUser: \"Get the transcript from this YouTube video: https://www.youtube.com/watch?v=dQw4w9WgXcQ\"\nResponse:\n```\nSELECTED AGENT: youtube-agent\nREASON: The request involves extracting a transcript from a YouTube video, which is the primary function of the YouTube Agent.\nUSER_MESSAGE: Get the transcript from this YouTube video: https://www.youtube.com/watch?v=dQw4w9WgXcQ\n```\n\nExample 5:\nUser: \"Retrieve the content from this website for me\"\nResponse:\n```\nSELECTED AGENT: fetch-agent\nREASON: The request involves retrieving content from a website, which is the core capability of the Fetch Agent.\nUSER_MESSAGE: Retrieve the content from this website for me\n```\n\nRemember, your primary goal is to route the user to the most appropriate specialized agent based on their request. Provide clear, concise reasoning for your selection to help users understand why a particular agent is best suited to assist them. Always include the original user message in your response using the USER_MESSAGE field."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [460, 1700], "id": "b7fa536d-5d42-4386-af5b-3363aa951dae", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [488, 1920], "id": "84955e7b-1bdf-410e-8bf9-19b15a855e43", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "avSgLKwLvvI1EPHE", "name": "OpenAi account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.selectedAgent }}", "rightValue": "ableton-copilot", "operator": {"type": "string", "operation": "equals"}, "id": "c237b982-c900-4525-8eeb-b02ab39f980c"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1c1ab39a-d5b7-4719-aec6-f57bcc26c92f", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "blinko-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bc1c2592-cda7-432f-af7c-010f859a4ca1", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "bookstack-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "faa89375-a0ac-4f84-9c11-0797e0da53f2", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "cli-server-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cd06e72a-76b0-424e-987c-bc11ad37517a", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "flowise-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cab0b253-9ac6-4adc-896d-c713061db0b9", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "forgejo-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0214372d-ac3a-4543-a821-8ca1efff2f2e", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "gitea-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5b81bca5-bfda-4430-9c91-dfaf2473ceb3", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "home-assisstant-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e726884a-0027-4baa-b891-1a7c43c9ecbc", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "karakee-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "74ed2959-91ff-48d4-a9a6-24e434899c55", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "langfuse-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e22da3bb-82e2-44ae-a07d-16ef3f8e2de3", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "memos-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "2f4ecfba-4e21-4b30-a13d-c4b006dd81eb", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "obs-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "46660559-8bc6-4ddf-a428-0cec003265b5", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "onlyoffice-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0a84de92-1cd6-4b4a-ac90-53f031e60b2a", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "outline-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "dbf5ad76-fff8-48d1-a833-7b6a1bc63e3e", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "paperless-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e55cf453-07d1-46ac-8376-700f828696f4", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "prometheus-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a11649fa-4325-4294-b370-d8476a128189", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "puppeteer-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8f7c3e58-9373-42e6-b102-609bdc03c320", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "ragflow-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8c8501c9-1b67-477b-b79d-fdf209a5e2b9", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "reaper-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7744f7b3-f024-4cef-b8c0-ca86935ea228", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "reaper-qa-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "e95b0043-252b-4dce-8835-63f2bc2920c2", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "<PERSON>yuan-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "3ffb2eae-399a-4683-a25e-205031972d53", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "system-search-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fa06ef70-f374-4bf3-9656-3e0c638a5d93", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "triliumnext-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5a9af83-b3bc-4f04-b3ef-4ad3fd418a78", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "clarification-needed", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b6c36f63-506b-4819-96a2-a00cd8cee8ff", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "youtube-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8b3e65b8-7731-4d0c-909e-4d7eb9345989", "leftValue": "={{ $json.selectedAgent }}", "rightValue": "fetch-agent", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [1056, 1217], "id": "3b965e47-8630-4146-9a65-567af1148417", "name": "Route to Agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "U9kHToGdtVOOUQ8i", "mode": "list", "cachedResultName": "ableton-copilot"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "reason", "displayName": "reason", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "originalMessage", "displayName": "originalMessage", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "selectedAgent", "displayName": "selectedAgent", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "aiResponse", "displayName": "aiResponse", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, -800], "id": "e494ef5a-3c50-49ae-835d-b79135936fcf", "name": "ableton-copilot"}, {"parameters": {"workflowId": {"__rl": true, "value": "663BL5mOEEA9PAk9", "mode": "list", "cachedResultName": "blinko"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "reason", "displayName": "reason", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "originalMessage", "displayName": "originalMessage", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "selectedAgent", "displayName": "selectedAgent", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "aiResponse", "displayName": "aiResponse", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, -600], "id": "9ed62afb-2319-497b-ada8-ef9831d5b8f7", "name": "blinko-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "YywcuhJHx7Ib4PPg", "mode": "list", "cachedResultName": "bookstack"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, -400], "id": "7b562b5a-353d-4068-99d8-b67cf6e01d20", "name": "bookstack-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "lWYJFMtOecosM5BG", "mode": "list", "cachedResultName": "cli-server"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, -200], "id": "1d578d5a-563a-4073-b166-76d4177f5fee", "name": "cli-server-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "vPFfIsP04dA0RGKf", "mode": "list", "cachedResultName": "flowise"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 0], "id": "1c66284f-fcae-473e-a3ee-80114efaa0eb", "name": "flowise-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "VLjppbVnWLSsvera", "mode": "list", "cachedResultName": "forgejo-agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 1200], "id": "63cd9066-339c-43cb-a20e-fe3620998bda", "name": "forgejo-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "JfoZIBPKeCFP11n2", "mode": "list", "cachedResultName": "gitea-agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 200], "id": "a4b0e001-94fd-4a93-a804-3f29574a84bb", "name": "gitea-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "IRoM12q6Bmj6ZOEf", "mode": "list", "cachedResultName": "home-assisstant"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 400], "id": "a0d0a348-5418-4cea-a9f9-ab4129eeb952", "name": "home-assisstant-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "wPNCVPV49N1UHZXp", "mode": "list", "cachedResultName": "langfuse-server"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 800], "id": "b18f4492-3f6b-49f3-97ee-dfe5661003a9", "name": "langfuse-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "WSmgeNYwSiJ2Xiq7", "mode": "list", "cachedResultName": "memos-mcp"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 1000], "id": "4c9c7898-f6c4-4002-8a4f-967abdfb3348", "name": "memos-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "Z3gZAj9TvyJ3TCEj", "mode": "list", "cachedResultName": "obs-server"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 1400], "id": "e6ad5350-1a33-4a7b-9b0e-aa8e479068f6", "name": "obs-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "R7Mrgn6n1JLX5REW", "mode": "list", "cachedResultName": "onlyoffice-docspace"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 1600], "id": "294b5a20-3267-491f-8858-047591e0c28b", "name": "onlyoffice-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "k66UmWWvJw9JL4R8", "mode": "list", "cachedResultName": "outline"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 1800], "id": "73cc7e1d-e654-4aef-8651-a1800c68a965", "name": "outline-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "WmsDbrLVosfoJ0fz", "mode": "list", "cachedResultName": "paperless-agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "reason", "displayName": "reason", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "selectedAgent", "displayName": "selectedAgent", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 2000], "id": "940be1c6-025e-4f8c-8c4c-fc4d645f4173", "name": "paperless-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "SbW32XgffRSJPIx7", "mode": "list", "cachedResultName": "prometheus"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 2200], "id": "d3fc911a-ec29-428c-90bb-99a05b6deedc", "name": "prometheus-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "ac0gXz0B2sAQMmVd", "mode": "list", "cachedResultName": "puppeteer"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 3000], "id": "57183464-1cfc-4625-8ca8-cf479329517b", "name": "puppeteer-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "297gKElp4LWZs47l", "mode": "list", "cachedResultName": "ragflow"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 3200], "id": "22a89ede-7d05-4c88-9eb8-20d9058a589e", "name": "ragflow-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "lFi52ELAp0RiR62O", "mode": "list", "cachedResultName": "reaper"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 3400], "id": "cd3b9dc4-8194-4a6b-9057-3a90cc6273ac", "name": "reaper-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "32VqtDrIIioJrVVy", "mode": "list", "cachedResultName": "reaper-qa"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 3600], "id": "6738774a-91ac-4f22-9350-77f4fbbc57c9", "name": "reaper-qa-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "X7AQ4F7pNCY8920l", "mode": "list", "cachedResultName": "siyuan"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 3800], "id": "056f104e-26db-4af1-ac4e-326c432ec776", "name": "<PERSON>yuan-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "2z1ZsWxV8ZG6iMxM", "mode": "list", "cachedResultName": "everything-search"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 4000], "id": "0d431400-3d02-4e82-ace0-939a7003786d", "name": "system-search-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "Y8LfFlIIbbnRICjO", "mode": "list", "cachedResultName": "triliumnext-mcp"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 4200], "id": "aaeb1dd5-a163-4838-bdcc-cadc2cd2c99d", "name": "triliumnext-agent"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, -800], "id": "300111da-8f63-40a8-9291-9542095de0ae", "name": "Respond to Webhook"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, -600], "id": "9541201f-4b6e-4fd3-a76e-f264f5345916", "name": "Respond to Webhook1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, -400], "id": "3d1cff61-e8de-4d8b-8354-fc7f7678beee", "name": "Respond to Webhook2"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, -200], "id": "6c0068a9-8cb3-4ead-a4c9-ceec370976d1", "name": "Respond to Webhook3"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 0], "id": "29d34674-8cec-40eb-b85a-dd9fc191e88a", "name": "Respond to Webhook4"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 200], "id": "d5174dee-1042-4eae-9683-e9236b82cfef", "name": "Respond to Webhook5"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 400], "id": "56ea4bd7-f8af-4129-a067-db2c228ee037", "name": "Respond to Webhook6"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 600], "id": "f00f33e6-7033-4dac-9b19-59de3fe8401f", "name": "Respond to Webhook7"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 800], "id": "6bc912f1-77bc-4e28-a88c-a60d6a177a2d", "name": "Respond to Webhook8"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 1000], "id": "5655f59f-dcf9-4aab-aa75-8a07bba6c2cf", "name": "Respond to Webhook9"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 1200], "id": "97897df3-53c7-4d7e-9e29-39af6bb24260", "name": "Respond to Webhook10"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 1400], "id": "20e8feb0-ca6e-4c5d-bf32-befb3ad62700", "name": "Respond to Webhook11"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 1600], "id": "7a4ff9a7-f458-41c3-a2f7-db97ed951775", "name": "Respond to Webhook12"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 1800], "id": "9b87535f-42f6-4f7f-97f2-65a767b706c5", "name": "Respond to Webhook13"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 2000], "id": "73a2f501-6ae7-4999-98a5-dc5c522d12ca", "name": "Respond to Webhook14"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 2200], "id": "93e824c0-e952-448f-bd99-e2442b69b7e0", "name": "Respond to Webhook15"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 3000], "id": "45d96c6a-aa56-4bed-a84f-b9a50c4fdf23", "name": "Respond to Webhook16"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 3200], "id": "f68590b5-d77e-4520-bcdf-4753db6d4f10", "name": "Respond to Webhook17"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 3400], "id": "d3e1b9f6-108e-43b0-af28-162efe3095b6", "name": "Respond to Webhook18"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 3600], "id": "66035275-c6ef-47d6-8f23-c877c9d84a2e", "name": "Respond to Webhook19"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 3800], "id": "098fd834-9637-4dd0-93bc-a2656f720ad6", "name": "Respond to Webhook20"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 4000], "id": "d072a468-44ef-499a-af79-93c86830cbd6", "name": "Respond to Webhook21"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 4200], "id": "95b646d6-9d7c-4ddb-817e-aa36c5f8c80e", "name": "Respond to Webhook22"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1496, 2400], "id": "0852cf40-62a7-4b7d-ad19-dd5dbc8881e6", "name": "Respond to Webhook23"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [608, 1920], "id": "d40edd10-4020-4915-8316-c813727e76f4", "name": "Simple Memory"}, {"parameters": {"jsCode": "// Get the AI response text\nconst aiResponse = $input.item.json.output || \"\";\n\nlet selectedAgent = \"\";\nlet reason = \"\";\nlet userMessage = \"\";\n\n// Extract agent, reason, and user message from the AI response\nif (aiResponse.includes(\"SELECTED AGENT:\")) {\n  // Extract agent ID\n  const agentMatch = aiResponse.match(/SELECTED AGENT: ([a-zA-Z0-9_-]+)/);\n  selectedAgent = agentMatch && agentMatch[1] ? agentMatch[1] : \"\";\n  \n  // Extract reason\n  const reasonMatch = aiResponse.match(/REASON: ([\\s\\S]+?)($|\\n|USER_MESSAGE:)/);\n  reason = reasonMatch && reasonMatch[1] ? reasonMatch[1].trim() : \"\";\n  \n  // Extract original user message\n  const messageMatch = aiResponse.match(/USER_MESSAGE: ([\\s\\S]+?)($|\\n)/);\n  userMessage = messageMatch && messageMatch[1] ? messageMatch[1].trim() : \"\";\n} else if (aiResponse.includes(\"CLARIFICATION NEEDED\")) {\n  selectedAgent = \"clarification-needed\";\n  const questionMatch = aiResponse.match(/QUESTION: ([\\s\\S]+?)($|\\n|USER_MESSAGE:)/);\n  reason = questionMatch && questionMatch[1] ? questionMatch[1].trim() : \"\";\n  const messageMatch = aiResponse.match(/USER_MESSAGE: ([\\s\\S]+?)($|\\n)/);\n  userMessage = messageMatch && messageMatch[1] ? messageMatch[1].trim() : \"\";\n}\n\n// Return the fields required by the sub-workflows\nreturn {\n  json: {\n    input: userMessage,\n    reason: reason,\n    selectedAgent: selectedAgent\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [836, 1700], "id": "c644fca9-84f4-4243-8297-ddfa7535809f", "name": "Process Routing Decision"}, {"parameters": {"jsCode": "const aiResponse = $input.item.json.aiResponse;\nlet clarificationQuestion = \"Could you please provide more information about what you're trying to do?\";\n\n// Extract the specific question if available\nif (aiResponse.includes(\"QUESTION:\")) {\n  const match = aiResponse.match(/QUESTION: (.*?)($|\\n)/);\n  if (match && match[1]) {\n    clarificationQuestion = match[1].trim();\n  }\n}\n\nreturn {\n  json: {\n    response: clarificationQuestion\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1276, 2400], "id": "115b1c14-0eba-47ec-82e8-7e221038b1e8", "name": "clarification-needed"}, {"parameters": {"workflowId": {"__rl": true, "value": "VRlbHY5QKtbwV9Tk", "mode": "list", "cachedResultName": "<PERSON><PERSON><PERSON>-agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "reason", "displayName": "reason", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}, {"id": "selectedAgent", "displayName": "selectedAgent", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 600], "id": "5083b857-4163-4d80-bb4c-72758d45942a", "name": "<PERSON><PERSON><PERSON>-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "EL2TabuIdJTKT9d8", "mode": "list", "cachedResultName": "youtube-agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "reason", "displayName": "reason", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "selectedAgent", "displayName": "selectedAgent", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1280, 2600], "id": "16395dec-8621-4cc1-b0b5-f74199f0825a", "name": "youtube-agent"}, {"parameters": {"workflowId": {"__rl": true, "value": "NuUcJz8v0NeoPn0V", "mode": "list", "cachedResultName": "fetch-agent"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "input", "displayName": "input", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "reason", "displayName": "reason", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "selectedAgent", "displayName": "selectedAgent", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1276, 2800], "id": "c9f1dd7e-f3c2-4eaa-bd59-e953bcc3e6cc", "name": "fetch-agent"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1500, 2600], "id": "c1f5fc17-6705-4ae9-a1f7-a5e26961eb4b", "name": "Respond to Webhook24"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.2, "position": [1500, 2800], "id": "a8983f6d-678d-4a38-8ae8-e449f3054ae7", "name": "Respond to Webhook25"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Process Routing Decision", "type": "main", "index": 0}]]}, "Route to Agent": {"main": [[{"node": "ableton-copilot", "type": "main", "index": 0}], [{"node": "blinko-agent", "type": "main", "index": 0}], [{"node": "bookstack-agent", "type": "main", "index": 0}], [{"node": "cli-server-agent", "type": "main", "index": 0}], [{"node": "flowise-agent", "type": "main", "index": 0}], [{"node": "forgejo-agent", "type": "main", "index": 0}], [{"node": "gitea-agent", "type": "main", "index": 0}], [{"node": "home-assisstant-agent", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>-agent", "type": "main", "index": 0}], [{"node": "langfuse-agent", "type": "main", "index": 0}], [{"node": "memos-agent", "type": "main", "index": 0}], [{"node": "obs-agent", "type": "main", "index": 0}], [{"node": "onlyoffice-agent", "type": "main", "index": 0}], [{"node": "outline-agent", "type": "main", "index": 0}], [{"node": "paperless-agent", "type": "main", "index": 0}], [{"node": "prometheus-agent", "type": "main", "index": 0}], [{"node": "puppeteer-agent", "type": "main", "index": 0}], [{"node": "ragflow-agent", "type": "main", "index": 0}], [{"node": "reaper-agent", "type": "main", "index": 0}], [{"node": "reaper-qa-agent", "type": "main", "index": 0}], [{"node": "<PERSON>yuan-agent", "type": "main", "index": 0}], [{"node": "system-search-agent", "type": "main", "index": 0}], [{"node": "triliumnext-agent", "type": "main", "index": 0}], [{"node": "clarification-needed", "type": "main", "index": 0}], [{"node": "youtube-agent", "type": "main", "index": 0}], [{"node": "fetch-agent", "type": "main", "index": 0}]]}, "ableton-copilot": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "blinko-agent": {"main": [[{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "bookstack-agent": {"main": [[{"node": "Respond to Webhook2", "type": "main", "index": 0}]]}, "cli-server-agent": {"main": [[{"node": "Respond to Webhook3", "type": "main", "index": 0}]]}, "flowise-agent": {"main": [[{"node": "Respond to Webhook4", "type": "main", "index": 0}]]}, "gitea-agent": {"main": [[{"node": "Respond to Webhook5", "type": "main", "index": 0}]]}, "home-assisstant-agent": {"main": [[{"node": "Respond to Webhook6", "type": "main", "index": 0}]]}, "langfuse-agent": {"main": [[{"node": "Respond to Webhook8", "type": "main", "index": 0}]]}, "memos-agent": {"main": [[{"node": "Respond to Webhook9", "type": "main", "index": 0}]]}, "forgejo-agent": {"main": [[{"node": "Respond to Webhook10", "type": "main", "index": 0}]]}, "obs-agent": {"main": [[{"node": "Respond to Webhook11", "type": "main", "index": 0}]]}, "onlyoffice-agent": {"main": [[{"node": "Respond to Webhook12", "type": "main", "index": 0}]]}, "outline-agent": {"main": [[{"node": "Respond to Webhook13", "type": "main", "index": 0}]]}, "paperless-agent": {"main": [[{"node": "Respond to Webhook14", "type": "main", "index": 0}]]}, "prometheus-agent": {"main": [[{"node": "Respond to Webhook15", "type": "main", "index": 0}]]}, "puppeteer-agent": {"main": [[{"node": "Respond to Webhook16", "type": "main", "index": 0}]]}, "ragflow-agent": {"main": [[{"node": "Respond to Webhook17", "type": "main", "index": 0}]]}, "reaper-agent": {"main": [[{"node": "Respond to Webhook18", "type": "main", "index": 0}]]}, "reaper-qa-agent": {"main": [[{"node": "Respond to Webhook19", "type": "main", "index": 0}]]}, "siyuan-agent": {"main": [[{"node": "Respond to Webhook20", "type": "main", "index": 0}]]}, "system-search-agent": {"main": [[{"node": "Respond to Webhook21", "type": "main", "index": 0}]]}, "triliumnext-agent": {"main": [[{"node": "Respond to Webhook22", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Process Routing Decision": {"main": [[{"node": "Route to Agent", "type": "main", "index": 0}]]}, "clarification-needed": {"main": [[{"node": "Respond to Webhook23", "type": "main", "index": 0}]]}, "karakeep-agent": {"main": [[{"node": "Respond to Webhook7", "type": "main", "index": 0}]]}, "youtube-agent": {"main": [[{"node": "Respond to Webhook24", "type": "main", "index": 0}]]}, "fetch-agent": {"main": [[{"node": "Respond to Webhook25", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "471f4948-686b-49c8-b50a-549152b93e80", "meta": {"templateCredsSetupCompleted": true, "instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "id": "vfzBXZ7VW6TOXKLL", "tags": []}