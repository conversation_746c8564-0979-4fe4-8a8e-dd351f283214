{"name": "real estate assiatnt", "nodes": [{"parameters": {"promptType": "define", "text": "={{ $json.body }}\nname : {{ $json.body.message.toolWithToolCallList[0].toolCall.function.arguments.name }}\ndate : {{ $json.body.message.toolWithToolCallList[0].toolCall.function.arguments.date }}\ntime : {{ $json.body.message.toolWithToolCallList[0].toolCall.function.arguments.time }}\n{{$now.format('DD')}}", "options": {"systemMessage": "=You are a customer support AI Agent designed to handle various tasks efficiently. Your primary role is to check for available times for appointments, book appointments, and help retrieve information that is requested for the user. You have access to multiple tools to help you fulfill requests\n\n\nInstructions:\n\n- use find_properties tool to retrieve informations \n- use book_appointment tool to create a calendar appointment when you receive date\n- use slack tool to send information when an appointment is booked\n- use email tool to send email to this email : <EMAIL> when a succesful appointment done\n- if you had to use multiple tools use them one by one properly\n\n- Here is the current time/date: {{$now.format('DD')}} and the date coming is old so use today time/date to set the right date for appointment\n\n\n"}}, "id": "cb19aad5-45d0-4ed6-93f4-eac238cd1711", "name": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "@n8n/n8n-nodes-langchain.agent", "position": [492, 460], "typeVersion": 1.9}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [400, 680], "id": "a02d147f-78bb-441d-990f-7afdc8edb932", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "GjwsNEdNscInJAPl", "name": "OpenAi account"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [520, 680], "id": "2a8400d7-4d96-4250-8f5a-6a9a341252b9", "name": "email", "webhookId": "9ef278d5-3ddd-4eef-bdee-fbdd7f85001c", "credentials": {"gmailOAuth2": {"id": "YuYPyVUrHOF5hfDz", "name": "Gmail account"}}}, {"parameters": {"httpMethod": "POST", "path": "assistant", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [180, 460], "id": "ed516eda-543a-479e-8130-68f79c256ee6", "name": "Webhook", "webhookId": "619301d8-50a3-42a0-aa09-f8fa7a004531"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "location": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Location', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [640, 680], "id": "ee75913f-3146-4875-9b03-3336daa62c94", "name": "book_appointment", "credentials": {"googleCalendarOAuth2Api": {"id": "YsTqG7d2DJ98Yf8j", "name": "Google Calendar account 3"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"appointment booked\"\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.3, "position": [960, 460], "id": "e6d30349-8132-47e7-9d2e-ab4741926f78", "name": "Respond to Webhook"}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C07RH2MSU87", "mode": "list", "cachedResultName": "social"}, "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message_Text', ``, 'string') }}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slackTool", "typeVersion": 2.3, "position": [760, 680], "id": "5619a69e-b723-41be-91ca-0c2df2d78cda", "name": "Slack2", "webhookId": "cfd8686f-e620-4658-bab2-8764602ff264", "credentials": {"slackOAuth2Api": {"id": "ty1ToQTqhObO61gk", "name": "Slack account 2"}}}], "pinData": {}, "connections": {"AI Calendar Assistant (LangChain)\t": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_languageModel", "index": 0}]]}, "email": {"ai_tool": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_tool", "index": 0}]]}, "Webhook": {"main": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "main", "index": 0}]]}, "book_appointment": {"ai_tool": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_tool", "index": 0}]]}, "Slack2": {"ai_tool": [[{"node": "AI Calendar Assistant (Lang<PERSON><PERSON><PERSON>)\t", "type": "ai_tool", "index": 0}]]}, "Respond to Webhook": {"main": [[]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4a4168ff-2f8f-4d2f-b3c7-a90db674de6e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ddbd87a6d86e4c8f0a1ce62d7808de5ad7a3017486cfd93fdc032fe6315d3fc5"}, "id": "tfEBb2lm8ZB3AHCC", "tags": []}