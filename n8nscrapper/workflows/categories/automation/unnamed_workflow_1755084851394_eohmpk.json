{"meta": {"instanceId": "26ba763460b97c249b82942b23b6384876dfeb9327513332e743c5f6219c2b8e"}, "nodes": [{"id": "d26b0190-c683-45fc-ac5b-0654af78f080", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1000, -620], "parameters": {"width": 377.7154173079816, "height": 511.2813260861502, "content": "## Try It Out!\n\n### This workflow builds a competitor research agent using Exa.ai as a starting point. The HTTP Request tool is used to demonstrate how you can build powerful agents with minimal effort.\n\n* Using Exa's findSimilar search, we ask it to look for similar companies ie. competitors, to our source company.\n* This list of competitors is sent to 3 agents to scour the internet to find company overview, product offering and customer reviews.\n* A report is then compiled from the output of all 3 agents into a notion table.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!"}, "typeVersion": 1}, {"id": "747d2f04-1e9c-45bb-b2ad-68da81524f4f", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-520, -420], "parameters": {}, "typeVersion": 1}, {"id": "5cb5f5a1-bc2d-4557-aff4-1993d8dcb99b", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1020, 20], "parameters": {"model": "gpt-4o-mini", "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "eafe20ab-0385-42e6-abbf-e15126bbb6fa", "name": "Search Crunchbase", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1320, 20], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to read the contents of a crunchbase profile.", "optimizeResponse": true}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "71729e21-a820-41a3-9cde-a52a63d1366d", "name": "Search WellFound", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1180, 180], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to read the contents of a wellfound profile.", "optimizeResponse": true}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "ad5be9e0-14dc-40b2-b080-b079fb4c1d4b", "name": "Search LinkedIn", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1320, 180], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to read the contents of a linkedin company profile. You must pass in the the linkedin.com url.", "optimizeResponse": true}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "405fa211-436d-4601-bc3e-ad6e6d99886d", "name": "Structured Output Parser1", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1600, 20], "parameters": {"jsonSchemaExample": "{\n \"company_name\": \"\",\n \"company_website\": \"\",\n \"year_founded\": \"\",\n \"founders\": [{ \"name\": \"\", \"linkedIn\": \"\" }],\n \"ceo\": [{ \"name\": \"\", \"linkedIn\": \"\", \"twitter\": \"\" }],\n \"key_people\": [{ \"name\": \"\", \"role\": \"\", \"linkedIn\": \"\", \"twitter\": \"\" }],\n \"employees\": [{ \"name\": \"\", \"role\": \"\", \"linkedIn\": \"\", \"twitter\": \"\" }],\n \"open_jobs\": [{ \"role\": \"\", \"description\": \"\", \"published\": \"\" }],\n \"offices\": [{ \"address\": \"\", \"city\": \"\" }],\n \"money_raised\": \"\",\n \"funding_status\": \"\",\n \"investors\": [{ \"name\": \"\", \"description\": \"\", \"linkedIn\": \"\" }],\n \"customers\": [{ \"name\": \"\", \"url\": \"\" }],\n \"yoy_customer_growth\": \"\",\n \"annual_revenue\": \"\",\n \"yoy_revenue_growth\": \"\",\n \"latest_articles\": [{ \"title\": \"\", \"snippet\": \"\", \"url\": \"\", \"published_date\": \"\" }]\n}"}, "typeVersion": 1.2}, {"id": "e4955f40-6e8c-42d9-bb1e-d134485717f2", "name": "Webscraper Tool1", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1460, 180], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to fetch any additional webpage and its contents which may be helpful in gathering information for the data points.", "optimizeResponse": true}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "4ddf8829-e11d-4002-ad96-1b3fcddebef7", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "position": [320, -380], "parameters": {"compare": "<PERSON><PERSON><PERSON>s", "options": {}, "fieldsToCompare": "url"}, "typeVersion": 1.1}, {"id": "06d7e6fb-9fe8-4c31-9042-fa375b63dd63", "name": "Extract Domain", "type": "n8n-nodes-base.set", "position": [140, -240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d82bab07-3434-4db3-ba89-d722279e3c40", "name": "title", "type": "string", "value": "={{ $json.title }}"}, {"id": "8a774c1d-c4b1-427a-aa4d-cda0071656ce", "name": "url", "type": "string", "value": "=https://{{ $json.url.extractDomain() }}"}]}}, "typeVersion": 3.4}, {"id": "991fbb7f-9ba5-4672-8573-6a28e77ed5fc", "name": "Results to List", "type": "n8n-nodes-base.splitOut", "position": [140, -380], "parameters": {"options": {}, "fieldToSplitOut": "results"}, "typeVersion": 1}, {"id": "f09112bc-65b5-4b6d-b568-eef95d064d45", "name": "Check Company Profiles Exist", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1180, 20], "parameters": {"url": "https://serpapi.com/search", "fields": "position,title,link,snippet,source", "dataField": "organic_results", "sendQuery": true, "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "parametersQuery": {"values": [{"name": "q"}]}, "toolDescription": "Call this tool to check if a company profile exists in either crunchbase, wellfound or linkedin.\n* To check if a company has a crunchbase profile, use the query \"site: https://crunchbase.com/organizations (company)\"\n* To check if a company has a wellfound profile, use the query \"site: https://wellfound.com/company (company)\"\n* To check if a company has a linked company profile, use the query \"site: https://linkedin.com/company (company)\"", "optimizeResponse": true, "nodeCredentialType": "serp<PERSON><PERSON>"}, "credentials": {"serpApi": {"id": "aJCKjxx6U3K7ydDe", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "5ac6eb04-7c94-443f-bdd3-52e5fc1f72ff", "name": "Webscraper Tool", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2180, -40], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to fetch webpage contents. Pass in the url to fetch.", "optimizeResponse": true}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "082b8e76-30b8-48f2-a581-a04a6f05c20d", "name": "Search Company Website", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2040, -40], "parameters": {"url": "https://serpapi.com/search", "fields": "position,title,link,snippet,source", "dataField": "organic_results", "sendQuery": true, "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "parametersQuery": {"values": [{"name": "q"}]}, "toolDescription": "Call this tool to query the company's profile website.\nExamples could include \"(company) pricing\", \"(company) plans\", \"(company) features\" etc", "optimizeResponse": true, "nodeCredentialType": "serp<PERSON><PERSON>"}, "credentials": {"serpApi": {"id": "aJCKjxx6U3K7ydDe", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "ca3140c5-b4ff-41d5-b0a1-b2595e1fc789", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2320, -40], "parameters": {"jsonSchemaExample": "{\n \"features\": [{ \"name\": \"\", \"description\": \"\" }],\n \"pricing_plans\": [{ \"name\": \"\", \"description\": \"\", \"tier\": \"\", \"price\": \"\", \"monthly_or_annually\": \"\" }],\n \"factors_that_impact_price\": [{ \"factor\": \"\", \"description\": \"\" }],\n \"discounts_promotions\": [{ \"offer\": \"\", \"start\": \"\", \"end\": \"\", \"description\": \"\" }],\n \"custom_plans\": { \"is_available\": false, \"applicable_for\": \"\", \"price\": \"\", \"duration\": \"\", \"description\": \"\" },\n \"free_trial\": { \"is_available\": false, \"applicable_for\": \"\", \"price\": \"\", \"duration\": \"\", \"description\": \"\" },\n \"freemium_version\": { \"is_available\": false, \"applicable_for\": \"\", \"price\": \"\", \"duration\": \"\", \"description\": \"\" },\n \"complementary_tools\": [{ \"name\": \"\", \"description\": \"\", \"price\": \"\" }],\n \"techonology used\": [{ \"name\": \"\", \"description\": \"\", \"purpose\": \"\" }]\n}"}, "typeVersion": 1.2}, {"id": "3c5493eb-6ca9-4909-997d-ddf3f3c88e2d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1900, -40], "parameters": {"model": "gpt-4o-mini", "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "299920bb-194b-4a95-8822-c0f6d559dd15", "name": "Search Product Review Sites", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2757, 20], "parameters": {"url": "https://serpapi.com/search", "fields": "position,title,link,snippet,source", "dataField": "organic_results", "sendQuery": true, "authentication": "predefinedCredentialType", "fieldsToInclude": "selected", "parametersQuery": {"values": [{"name": "q", "value": "{company_or_product} reviews (site:trustpilot.com OR site:producthunt.com)", "valueProvider": "fieldValue"}, {"name": "num", "value": "3", "valueProvider": "fieldValue"}]}, "toolDescription": "Call this tool to search for customer reviews for the desired company or their product/service.", "optimizeResponse": true, "nodeCredentialType": "serp<PERSON><PERSON>", "placeholderDefinitions": {"values": [{"name": "company_or_product", "description": "the name of the company or their product to search for reviews for"}]}}, "credentials": {"serpApi": {"id": "aJCKjxx6U3K7ydDe", "name": "SerpAPI account"}}, "typeVersion": 1}, {"id": "216bc875-365c-4536-b3b4-90de29265cb5", "name": "Webscraper Tool2", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [2897, 20], "parameters": {"url": "https://api.firecrawl.dev/v0/scrape", "fields": "markdown", "method": "POST", "sendBody": true, "dataField": "data", "authentication": "genericCredentialType", "parametersBody": {"values": [{"name": "url", "value": "{url_or_link}", "valueProvider": "fieldValue"}, {"name": "pageOptions", "value": "={{ {\n onlyMainContent: true,\n replaceAllPathsWithAbsolutePaths: true,\n removeTags: 'img,svg,video,audio'\n} }}", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "genericAuthType": "httpHeaderAuth", "toolDescription": "Call this tool to fetch webpage contents. Pass in the url to fetch.", "optimizeResponse": true, "placeholderDefinitions": {"values": [{"name": "url_or_link", "description": "the url or lik to the review site webpage."}]}}, "credentials": {"httpHeaderAuth": {"id": "OUOnyTkL9vHZNorB", "name": "Firecrawl API"}}, "typeVersion": 1}, {"id": "c13f23fd-77b9-4f4c-bdc6-50120ed84cbd", "name": "Structured Output Parser2", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [3037, 20], "parameters": {"jsonSchemaExample": "{\n \"number_of_reviews\": 0,\n \"positive_mentions_%\": \"\",\n \"negative_mentions_%\": \"\",\n \"top_pros\": [\"\"],\n \"top_cons\": [\"\"],\n \"top_countries\": [\"\"],\n \"top_social_media_platforms\": [\"\"]\n}"}, "typeVersion": 1.2}, {"id": "784202a0-4022-4941-8fcc-f1c05c9820a6", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2617, 20], "parameters": {"model": "gpt-4o", "options": {"temperature": 0}}, "credentials": {"openAiApi": {"id": "8gccIjcuf3gvaoEr", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "fbc2e1c1-7851-4fbb-b46c-9fa35eacd810", "name": "Insert Into Notion", "type": "n8n-nodes-base.notion", "position": [3520, -240], "parameters": {"title": "={{ $json.output.company_name }}", "blockUi": {"blockValues": [{"type": "heading_1", "textContent": "={{ $json.output.company_name }}"}, {"textContent": "=Report generated on {{ $now.format('dd MMM yyyy') }}"}, {"type": "heading_2", "textContent": "Company Overview"}, {"textContent": "=Offices:\n{{ $json.output.offices.map(item => `${item.address}, ${item.city}`).join('\\n') }}\n\nYear Founded:\n{{ $json.output.year_founded }}\n\nFounders:\n{{ $json.output.founders.map(item => `${item.name} (${item.linkedIn})`).join('\\n') }}\n\nCEO:\n{{ $json.output.ceo.map(item => `${item.name} (${item.linkedIn})`).join('\\n') }}\n\nEmployees:\n{{ $json.output.employees.map(item => `${item.name} - ${item.role}, (${item.linkedIn})`).join('\\n') }}\n\nOpen Roles:\n{{ $json.output.open_jobs.map(item => `${item.role} (${item.published}), ${item.description}`).join('\\n') }}"}, {"type": "heading_2", "textContent": "Company Funding"}, {"textContent": "=Money Raised:\n{{ $json.output.money_raised || 'unknown' }}\n\nFunding Status:\n{{ $json.output.funding_status }}\n\nYoY Customer Growth:\n{{ $json.output.yoy_customer_growth || 'unknown' }}\n\nAnnual Revenue:\n{{ $json.output.annual_revenue || 'unknown' }}\n\nYoY Revenue Growth:\n{{ $json.output.yoy_revenue_growth || 'unknown' }}\n\nInvestors:\n{{ $json.output.investors.map(item => `${item.name}, ${item.description} (${item.linkedIn})`).join('\\n') }}\n\nCustomers:\n{{ $json.output.customers.map(item => `${item.name} (${item.url})`).join('\\n') }}"}, {"type": "heading_2", "textContent": "Company News"}, {"textContent": "={{ $json.output.latest_articles.length ? $json.output.latest_articles.map(item =>\n`**${item.title}**\n${item.url}\n${item.published_date} | ${item.snippet}\n`).join('\\n') : 'None Found' }}"}, {"type": "heading_2", "textContent": "Product Offering"}, {"textContent": "=Features:\n{{ $json.output.features.map(item => `${item.name} - ${item.description.split('.')[0]}.`).join('\\n') }}\n"}, {"textContent": "=Pricing Plans:\n{{ $json.output.pricing_plans.map(item =>\n`${item.name} - ${item.price} (${item.tier})\n* ${item.description}`\n).join('\\n\\n') }}\n\nFactors that Impact Price:\n{{ $json.output.factors_that_impact_price.map(item => `${item.factor} - ${item.description}`).join('\\n') }}\n\nCurrent Discounts and/or Promotions:\n{{ $json.output.discounts_promotions.length ? $json.output.discounts_promotions.map(item =>\n `${item.offer} (${item.start} - ${item.end})\n* ${item.description}`\n).join('\\n\\n') : '* None Found' }}\n\nCustom Plans:\n{{ $json.output.custom_plans.is_available ? (\n `${$json.output.custom_plans.applicable_for} - ${$json.output.custom_plans.price}\n* ${$json.output.custom_plans.description}`\n) : 'Not applicable' }}\n\nFree Trials:\n{{ $json.output.free_trial.is_available ? (\n `${$json.output.free_trial.applicable_for} - ${$json.output.free_trial.price}\n* ${$json.output.free_trial.description}`\n) : 'Not applicable' }}\n\nFreemium Version:\n{{ $json.output.freemium_version.is_available ? (\n `${$json.output.freemium_version.applicable_for} - ${$json.output.freemium_version.price}\n* ${$json.output.freemium_version.description}`\n) : 'Not applicable' }}\n\nComplimentary Tools:\n{{ $json.output.complementary_tools.map(item =>\n `${item.name} - ${item.price}\n* ${item.description}`\n).join('\\n\\n') }}"}, {"type": "heading_2", "textContent": "=Product Reviews"}, {"textContent": "=Number of Reviews: {{ $json.output.number_of_reviews }}\nPositive Mentions (%): {{ $json.output['positive_mentions_%'] }} \nNegative Mentions (%): {{ $json.output['negative_mentions_%'] }} \n\nTop Pros:\n{{ $json.output.top_pros.length ? $json.output.top_pros.map(item => `* ${item}`).join('\\n'): '* None Found' }}\n\nTop Cons:\n{{ $json.output.top_cons.length ? $json.output.top_cons.map(item => `* ${item}`).join('\\n') : '* None Found' }} \n\nTop Countries:\n{{ $json.output.top_countries.length ? $json.output.top_countries.map(item => `* ${item}`).join('\\n') : '* None Found' }}\n\nTop Social Media Platforms:\n{{ $json.output.top_social_media_platforms.length ? $json.output.top_social_media_platforms.map(item => `* ${item}`).join('\\n') : '* None Found' }}"}]}, "options": {}, "resource": "databasePage", "databaseId": {"__rl": true, "mode": "list", "value": "2d1c3c72-6e8e-42f3-aece-c6338fd24333", "cachedResultUrl": "https://www.notion.so/2d1c3c726e8e42f3aecec6338fd24333", "cachedResultName": "n8n Competitor Analysis"}, "propertiesUi": {"propertyValues": [{"key": "Founded|rich_text", "textContent": "={{ $json.output.year_founded }}"}, {"key": "Funding Status|rich_text", "textContent": "={{ $json.output.funding_status }}"}, {"key": "Money Raised|rich_text", "textContent": "={{ $json.output.money_raised || ''}}"}, {"key": "Positive Reviews (%)|rich_text", "textContent": "={{ $json.output['positive_mentions_%'] }}%"}, {"key": "Pros|rich_text", "textContent": "={{ $json.output.top_pros.join(', ') }}"}, {"key": "Cons|rich_text", "textContent": "={{ $json.output.top_cons.join(', ') }}"}]}}, "credentials": {"notionApi": {"id": "iHBHe7ypzz4mZExM", "name": "Notion account"}}, "typeVersion": 2.2}, {"id": "ec6b578d-4808-4613-881b-67dbcf30f641", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [320, -240], "parameters": {"maxItems": 10}, "typeVersion": 1}, {"id": "2f25cf2e-86c6-4d23-a1ae-cc35134f0d8a", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [680, -280], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "360000e3-bc07-4be9-91cf-169b85ed7ad5", "name": "Competitor Search via Exa.ai", "type": "n8n-nodes-base.httpRequest", "position": [-80, -420], "parameters": {"url": "https://api.exa.ai/findSimilar", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "url", "value": "={{ $json.company_url }}"}, {"name": "type", "value": "neural"}, {"name": "useAutoprompt", "value": "true"}, {"name": "contents", "value": "={{ { \"text\": false } }}"}, {"name": "excludeDomains", "value": "={{ [$json.company_url, \"github.com\", \"linkedIn.com\"] }}"}]}, "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "BWhPl6CgBKn3UJca", "name": "Exa.ai"}}, "typeVersion": 4.2}, {"id": "dae53670-bafb-4dff-95e7-cc94adf5f344", "name": "Get Company News", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1460, 20], "parameters": {"url": "https://serpapi.com/search", "fields": "position,title,link,snippet,source", "sendBody": true, "dataField": "organic_results", "authentication": "predefinedCredentialType", "parametersBody": {"values": [{"name": "q"}, {"name": "engine", "value": "google_news", "valueProvider": "fieldValue"}]}, "fieldsToInclude": "selected", "toolDescription": "Call this tool to search for the latest news articles of a company.", "optimizeResponse": true, "nodeCredentialType": "serp<PERSON><PERSON>"}, "credentials": {"serpApi": {"id": "aJCKjxx6U3K7ydDe", "name": "SerpAPI account"}, "httpHeaderAuth": {"id": "BWhPl6CgBKn3UJca", "name": "Exa.ai"}}, "typeVersion": 1}, {"id": "********-06c8-41d9-8269-571cac716d17", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-589.*************, -613.*************], "parameters": {"color": 7, "width": 1128.************, "height": 582.*************, "content": "## Step 1. Get Competitors\n[Read more about using the HTTP Request node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest)\n\nExa.ai is a relatively new AI search engine startup with a specialised API for finding similar companies.\nThis is perfect for marketing research as we can easily find competitors to compare against."}, "typeVersion": 1}, {"id": "d43e8b2a-53dd-41db-aacd-f3fdd29d8fe9", "name": "Set Source Company", "type": "n8n-nodes-base.set", "position": [-300, -420], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3910089f-065d-4f05-a3b7-a5b848b91eb9", "name": "company_url", "type": "string", "value": "https://notion.so"}]}}, "typeVersion": 3.4}, {"id": "1c28b108-dc18-4304-aed9-275e719c4edd", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-340, -440], "parameters": {"width": 181.85939799093455, "height": 308.12010511833364, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨Required!\nRemember to set your company here."}, "typeVersion": 1}, {"id": "5cb54393-795b-4738-aac2-cc9395456420", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [560, -525.6986144265638], "parameters": {"color": 7, "width": 332.87733508600377, "height": 492.4668447935363, "content": "## Step 2. Feed into Agent Pipeline\n[Learn more about loops](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches)\n\nA loop is used to ensure competitors are processed one at a time. This is ideal when we don't want errors to fail the entire pipeline."}, "typeVersion": 1}, {"id": "4cdddfd1-8631-4d88-a65d-10a53daeaf78", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1800, -420], "parameters": {"color": 7, "width": 687.9856526661888, "height": 600.1548730999224, "content": "## Step 4. Research Competitor Product Offering\n[Learn more about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThis agent uses SERPAPI to discover the competitor's product pages. Once found, it will use the webscraping tool to fetch the page's contents to extract the necessary data points."}, "typeVersion": 1}, {"id": "3a374ce6-0ae0-4f8e-ad6a-35ab2c8da211", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [920, -400], "parameters": {"color": 7, "width": 849.3810544357925, "height": 775.191233831828, "content": "## Step 3. Discover Competitor Company and Funding Overview\n[Learn more about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThis agent searches crunchbase.com, wellfound.com and linkedin.com for the competitor's company details, people data, funding activity and latest news."}, "typeVersion": 1}, {"id": "fa580430-d6c6-4d41-b0f9-c86ad89dc6ab", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2520, -400], "parameters": {"color": 7, "width": 683.8444841203574, "height": 633.9023021841829, "content": "## Step 5. Capture Competitor Product Reviews\n[Learn more about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nThis agent uses SERPAPI to discover product reviews for the competitor's product or service and then summarises the pros and cons."}, "typeVersion": 1}, {"id": "b050b11f-9b4f-4ed1-94b9-3ec69a1ceba7", "name": "Collect Results", "type": "n8n-nodes-base.set", "position": [3340, -240], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3c58a9dd-28c5-4758-a362-d5b29f6a8204", "name": "output", "type": "object", "value": "={{\n {\n ...$('Company Overview Agent').item.json.output,\n ...$('Company Product Offering Agent').item.json.output,\n ...$('Company Product Reviews Agent').item.json.output,\n }\n}}"}]}}, "typeVersion": 3.4}, {"id": "1fc6468e-8a89-4a37-a8d6-1fe94c274b3a", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [3220, -481.22655186925294], "parameters": {"color": 7, "width": 529.1065295866968, "height": 572.5257167828777, "content": "## Step 6. Collect Results and Send to Notion\n[Read more about using Notion](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.notion)\n\nFinally, once the agent's have completed their tasks successfully, we compiled a competitor report and insert it as a new row in our Notion table.\nYou can check out a copy of this table here: https://jimleuk.notion.site/2d1c3c726e8e42f3aecec6338fd24333?v=de020fa196f34cdeb676daaeae44e110&pvs=4"}, "typeVersion": 1}, {"id": "863c40c7-e56d-464f-8105-0cb151654715", "name": "2sec", "type": "n8n-nodes-base.wait", "position": [3680, 280], "webhookId": "94b5b09f-0599-4585-b83b-f669726bc2ef", "parameters": {"amount": 2}, "typeVersion": 1.1}, {"id": "c1a4b720-f56b-4c3a-aeca-a89f473132f4", "name": "Company Overview Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1020, -200], "parameters": {"text": "={{ $('Loop Over Items').item.json.url }}", "options": {"systemMessage": "Your role is a company researcher agent. Your goal is to research and discover the following information about a company:\n* Year founded\n* Founder(s)\n* CEO\n* Key people\n* Employees\n* Open jobs\n* Offices\n* Money raised\n* Funding status\n* Investors\n* Customers\n* YoY customer growth\n* Annual revenue\n* YoY revenue growth\n* Latest articles\n\n## Steps\n1. check if the company's crunchbase profile exists and if it does read the profile page to gather the required information. If you are able to satisfies all data points from the profile, then do not return your response.\n2. repeat step 1 for wellfound if there are missing data points on the crunchbase profile.\n3. repeat step 1 for linkedin if there are missing data points on the wellfound profile.\n4. If there are still missing datapoints after checking cruchbase, wellfound and linkedin then just give up and return your response.\n\nIf a data point is not found after completing all the above steps, do not use null values in your final response. Use either an empty array, object or string depending on the required schema for the data point.\nDo not retry any link that returns a 400,401,403 or 500 error code."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "1070c7f0-544a-478b-bd97-df8f2c0d79fa", "name": "Company Product Offering Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1900, -240], "parameters": {"text": "={{ $('Loop Over Items').item.json.url }}", "options": {"systemMessage": "Your role is company product/service researcher. Your goal is to search and collect the following information:\n* features sets\n* number of pricing plans\n* Factors that impact price\n* Lowest-tier, Mid-tier and Highest-tier price\n* Custom plans if available\n* Discounts & promotions offered currently\n* whether a Free trial is offered\n* description of freemium version if available\n* Complementary tools offered\n* technology used\n\n# steps\n1. Search for the relevant webpage on the company's website. This search should return a url address.\n2. Use this url address with the webscraper tool to fetch the contents of the webpage.\n3. Use the contents of th webpage to populate the data points.\n\nIf a data point is not found after completing all the above steps, do not use null values in your final response. Use either an empty array, object or string depending on the required schema for the data point.\nDo not retry any link that returns a 400,401,403 or 500 error code."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "5dec7033-5057-483f-930d-e950b6eabe05", "name": "Company Product Reviews Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2617, -200], "parameters": {"text": "={{ $('Loop Over Items').item.json.url }}", "options": {"systemMessage": "Your role is customer reviews agent. Your goal is to gather and collect online customer reviews for a company or their product or service.\n* number of reviews\n* Positive mentions, %\n* Negative mentions, %\n* Top pros\n* Top cons\n* Top countries\n* Top social media platforms\n\n## steps\n1. search for review sites that may have reviews for the company or product in question. retrieve the links or urls of the serch results where the reviews are found.\n2. Identify relevant items in the search result and and extract the urls from the search results.\n2. using the extracted urls from the search results, fetch the webpage of the review sites containing reviews for the company or product.\n3. extract the reviews from the fetched review sites to populate the required data points.\n\nIf a data point is not found after completing all the above steps, do not use null values in your final response. Use either an empty array, object or string depending on the required schema for the data point.\nDo not retry any link that returns a 400,401,403 or 500 error code."}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.6}, {"id": "787bb405-1744-43b7-8c47-1a2c23331e05", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [3480, -260], "parameters": {"width": 181.85939799093455, "height": 308.12010511833364, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨Required!\nRemember to set your Notion Database here."}, "typeVersion": 1}], "pinData": {}, "connections": {"2sec": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Extract Domain": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Collect Results": {"main": [[{"node": "Insert Into Notion", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [null, [{"node": "Company Overview Agent", "type": "main", "index": 0}]]}, "Results to List": {"main": [[{"node": "Extract Domain", "type": "main", "index": 0}]]}, "Search LinkedIn": {"ai_tool": [[{"node": "Company Overview Agent", "type": "ai_tool", "index": 0}]]}, "Webscraper Tool": {"ai_tool": [[{"node": "Company Product Offering Agent", "type": "ai_tool", "index": 0}]]}, "Get Company News": {"ai_tool": [[{"node": "Company Overview Agent", "type": "ai_tool", "index": 0}]]}, "Search WellFound": {"ai_tool": [[{"node": "Company Overview Agent", "type": "ai_tool", "index": 0}]]}, "Webscraper Tool1": {"ai_tool": [[{"node": "Company Overview Agent", "type": "ai_tool", "index": 0}]]}, "Webscraper Tool2": {"ai_tool": [[{"node": "Company Product Reviews Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Company Product Offering Agent", "type": "ai_languageModel", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Search Crunchbase": {"ai_tool": [[{"node": "Company Overview Agent", "type": "ai_tool", "index": 0}]]}, "Insert Into Notion": {"main": [[{"node": "2sec", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Company Overview Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Company Product Reviews Agent", "type": "ai_languageModel", "index": 0}]]}, "Set Source Company": {"main": [[{"node": "Competitor Search via Exa.ai", "type": "main", "index": 0}]]}, "Company Overview Agent": {"main": [[{"node": "Company Product Offering Agent", "type": "main", "index": 0}]]}, "Search Company Website": {"ai_tool": [[{"node": "Company Product Offering Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Company Product Offering Agent", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Company Overview Agent", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "Company Product Reviews Agent", "type": "ai_outputParser", "index": 0}]]}, "Search Product Review Sites": {"ai_tool": [[{"node": "Company Product Reviews Agent", "type": "ai_tool", "index": 0}]]}, "Check Company Profiles Exist": {"ai_tool": [[{"node": "Company Overview Agent", "type": "ai_tool", "index": 0}]]}, "Competitor Search via Exa.ai": {"main": [[{"node": "Results to List", "type": "main", "index": 0}]]}, "Company Product Reviews Agent": {"main": [[{"node": "Collect Results", "type": "main", "index": 0}]]}, "Company Product Offering Agent": {"main": [[{"node": "Company Product Reviews Agent", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set Source Company", "type": "main", "index": 0}]]}}}