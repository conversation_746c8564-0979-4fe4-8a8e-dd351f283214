{"id": "dsKnCFwysROIA4MT", "meta": {"instanceId": "03524270bab2c2dfd5b82778cd1355e56cdda3cf098bf2dfd865e18164c00485"}, "name": "Agent with custom HTTP Request", "tags": [], "nodes": [{"id": "e7374976-f3c1-4f60-ae57-9eec65444216", "name": "On new manual Chat Message", "type": "@n8n/n8n-nodes-langchain.manualChatTrigger", "position": [763, 676], "parameters": {}, "typeVersion": 1}, {"id": "97e84a23-9536-43cd-94e9-b8166be8ed32", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [983, 896], "parameters": {"model": "gpt-4-1106-preview", "options": {"timeout": 300000, "temperature": 0.7, "frequencyPenalty": 0.3}}, "credentials": {"openAiApi": {"id": "wPFAzp4ZHdLLwvkK", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "63d98361-8978-4042-84e7-53a0e226f946", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "onError": "continueRegularOutput", "position": [1360, 1200], "parameters": {"url": "={{ encodeURI($json.query.url) }}", "options": {"response": {"response": {"neverError": true}}, "allowUnauthorizedCerts": true}}, "typeVersion": 4.1, "alwaysOutputData": false}, {"id": "17d4b5ae-f5d3-4793-8419-d3c879f7f50d", "name": "Exctract HTML Body", "type": "n8n-nodes-base.set", "position": [1780, 1480], "parameters": {"fields": {"values": [{"name": "HTML", "stringValue": "={{ $json?.data.match(/<body[^>]*>([\\s\\S]*?)<\\/body>/i)[1] }}"}]}, "include": "selected", "options": {}, "includeFields": "HTML"}, "typeVersion": 3.2}, {"id": "36c38ee4-724c-4ba2-a59a-ac0bbc912e94", "name": "Is error?", "type": "n8n-nodes-base.if", "position": [1560, 1200], "parameters": {"conditions": {"boolean": [{"value1": "={{ $json.hasOwnProperty('error') }}", "value2": true}]}}, "typeVersion": 1}, {"id": "4e4d97ce-14a9-4f4f-aa75-f218784d9ed9", "name": "Stringify error message", "type": "n8n-nodes-base.set", "position": [1780, 980], "parameters": {"fields": {"values": [{"name": "page_content", "stringValue": "={{ $('QUERY_PARAMS').first()?.json?.query?.url == null ? \"INVALID action_input. This should be an HTTP query string like this: \\\"?url=VALIDURL&method=SELECTEDMETHOD\\\". Only a simple string value is accepted. JSON object as an action_input is NOT supported!\" : JSON.stringify($json.error) }}"}]}, "include": "selected", "options": {}, "includeFields": "HTML"}, "typeVersion": 3.2}, {"id": "8452e5c4-aa29-4a02-9579-8d9da3727bcb", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [760, 1200], "parameters": {}, "typeVersion": 1}, {"id": "063220c2-fa4d-4d5e-9549-7712aaa72921", "name": "Remove extra tags", "type": "n8n-nodes-base.set", "position": [1980, 1480], "parameters": {"fields": {"values": [{"name": "HTML", "stringValue": "={{ ($json.HTML || \"HTML BODY CONTENT FOR THIS SEARCH RESULT IS NOT AVAILABLE\").replace(/<script[^>]*>([\\s\\S]*?)<\\/script>|<style[^>]*>([\\s\\S]*?)<\\/style>|<noscript[^>]*>([\\s\\S]*?)<\\/noscript>|<!--[\\s\\S]*?-->|<iframe[^>]*>([\\s\\S]*?)<\\/iframe>|<object[^>]*>([\\s\\S]*?)<\\/object>|<embed[^>]*>([\\s\\S]*?)<\\/embed>|<video[^>]*>([\\s\\S]*?)<\\/video>|<audio[^>]*>([\\s\\S]*?)<\\/audio>|<svg[^>]*>([\\s\\S]*?)<\\/svg>/ig, '')}}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "036511d7-a4be-4bbf-b4bc-47ddfabfe76f", "name": "Simplify output", "type": "n8n-nodes-base.set", "notes": "remove links and image URLs", "position": [2360, 1380], "parameters": {"fields": {"values": [{"name": "HTML", "stringValue": "={{ $json.HTML.replace(/href\\s*=\\s*\"(.+?)\"/gi, 'href=\"NOURL\"').replace(/src\\s*=\\s*\"(.+?)\"/gi, 'src=\"NOIMG\"')}}"}]}, "options": {}}, "notesInFlow": true, "typeVersion": 3.2}, {"id": "5e2b5383-adcf-4de0-a406-4f5d631b5e8a", "name": "Simplify?", "type": "n8n-nodes-base.if", "position": [2180, 1480], "parameters": {"conditions": {"string": [{"value1": "={{ $('CONFIG').first()?.json?.query?.method }}", "value2": "simplif", "operation": "contains"}]}}, "typeVersion": 1}, {"id": "a0fc004a-ab0f-4b31-94df-50f5eee69c86", "name": "QUERY_PARAMS", "type": "n8n-nodes-base.set", "position": [960, 1200], "parameters": {"fields": {"values": [{"name": "query", "type": "objectValue", "objectValue": "={{ $json.query.substring($json.query.indexOf('?') + 1).split('&').reduce((result, item) => (result[item.split('=')[0]] = decodeURIComponent(item.split('=')[1]), result), {}) }}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "3b6599d6-ce9a-4861-9b52-07156eb52539", "name": "CONFIG", "type": "n8n-nodes-base.set", "position": [1160, 1200], "parameters": {"fields": {"values": [{"name": "query.maxlimit", "type": "numberValue", "numberValue": "={{ $json?.query?.maxlimit == null ? 70000 : Number($json?.query?.maxlimit) }}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "14f683be-76f6-4034-9a0e-d785738b135f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [721, 1134], "parameters": {"width": 556.25, "height": 235.79999999999995, "content": "### Convert the query string into JSON, apply the limit for a page length"}, "typeVersion": 1}, {"id": "6deabcb7-a984-48ec-af2a-8c70b3a4e4bf", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1720, 840], "parameters": {"width": 491, "height": 285.7, "content": "## Send an error message:\n1. If query param was incorrect, return the instruction. AI Agent should pick up on this and adapt the query on the next iteration.\n2. If the query is OK and an error was during the HTTP Request, then send back the original error message."}, "typeVersion": 1}, {"id": "df1e8d00-0e18-44fa-8f94-8a53c27f7c88", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1720, 1160], "parameters": {"width": 1200, "height": 472.5, "content": "## Post-processing of the HTML page:\n1. Keep only <BODY> content\n2. Remove inline <SCRIPT> tag entirely, as well as: NOSCRIPT, IFRAME, OBJECT, EMBED, VIDEO, AUDIO, SVG, and HTML comments.\n3. In case query parameter method=simplified, replace all page URLs (a href) and IMG (src) with NOURL / NOIMG - this may save up to 20% of the page length\n4. Convert the remaining HTML to Markdown. This step further reduces the length of the page: long HTML tags and styles are eliminated, but the markdown syntax keeps some page structure. This gives much better results compared to just a blank text.\n5. Finally, check the page length. If it's too long, send an \"ERROR: PAGE CONTENT TOO LONG\" instead of the actual page. Of course, you could split the page content in chunks, but sometimes long pages just don't have a needed content, so it makes little sense to burn tokens on them."}, "typeVersion": 1}, {"id": "6afe96a0-0fba-4ae1-ab8f-f7da56d420b1", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [720, 540], "parameters": {"width": 616.8597285067872, "height": 483.0226244343891, "content": "## Example ReAct AI Agent\n1. Agent Prompt is default\n2. Check the description of the HTTP_Request_Tool, it guides the agent to provide a query string with several parameters instead of a JSON object"}, "typeVersion": 1}, {"id": "d5ff2114-1e74-43cf-9f3c-744c241988db", "name": "ReAct AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [983, 676], "parameters": {"agent": "reActAgent", "options": {"prefix": "Answer the following questions as best you can. You have access to the following tools:", "suffix": "Begin!\n\n\tQuestion: {input}\n\tThought:{agent_scratchpad}", "suffixChat": "Begin! Reminder to always use the exact characters `Final Answer` when responding.", "humanMessageTemplate": "{input}\n\n{agent_scratchpad}"}}, "typeVersion": 1}, {"id": "cc7aef4a-a1fb-4a69-a670-1f200f9e9541", "name": "Convert to <PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [2540, 1480], "parameters": {"html": "={{ $json.HTML }}", "options": {}, "destinationKey": "page_content"}, "typeVersion": 1}, {"id": "11806e8c-5fc4-4d9d-8144-179356993aa7", "name": "Send Page Content", "type": "n8n-nodes-base.set", "position": [2740, 1480], "parameters": {"fields": {"values": [{"name": "page_content", "stringValue": "={{ $json.page_content.length < $('CONFIG').first()?.json?.query?.maxlimit ? $json.page_content : \"ERROR: PAGE CONTENT TOO LONG\" }}"}, {"name": "page_length", "type": "numberValue", "numberValue": "={{ $json.page_content.length }}"}]}, "include": "selected", "options": {}}, "typeVersion": 3.2}, {"id": "a3a6b199-517b-4987-8281-d7997a32f54b", "name": "HTTP_Request_Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [1103, 896], "parameters": {"name": "HTTP_Request_Tool", "workflowId": "={{ $workflow.id }}", "description": "Call this tool to fetch a webpage content. The input should be a stringified HTTP query parameter like this: \"?url=VALIDURL&method=SELECTEDMETHOD\". \"url\" parameter should contain the valid URL string. \"method\" key can be either \"full\" or \"simplified\". method=full will fetch the whole webpage content in the Markdown format, including page links and image links. method=simplified will return the Markdown content of the page but remove urls and image links from the page content for simplicity. Before calling this tool, think strategically which \"method\" to call. Best of all to use method=simplified. However, if you anticipate that the page request is not final or if you need to extract links from the page, pick method=full.", "responsePropertyName": "page_content"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "9db853c5-3658-47c1-b98a-5858b1c184ec", "connections": {"CONFIG": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Is error?": {"main": [[{"node": "Stringify error message", "type": "main", "index": 0}], [{"node": "Exctract HTML Body", "type": "main", "index": 0}]]}, "Simplify?": {"main": [[{"node": "Simplify output", "type": "main", "index": 0}], [{"node": "Convert to <PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Is error?", "type": "main", "index": 0}]]}, "QUERY_PARAMS": {"main": [[{"node": "CONFIG", "type": "main", "index": 0}]]}, "Simplify output": {"main": [[{"node": "Convert to <PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP_Request_Tool": {"ai_tool": [[{"node": "ReAct AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "ReAct AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Remove extra tags": {"main": [[{"node": "Simplify?", "type": "main", "index": 0}]]}, "Exctract HTML Body": {"main": [[{"node": "Remove extra tags", "type": "main", "index": 0}]]}, "Convert to Markdown": {"main": [[{"node": "Send Page Content", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "QUERY_PARAMS", "type": "main", "index": 0}]]}, "On new manual Chat Message": {"main": [[{"node": "ReAct AI Agent", "type": "main", "index": 0}]]}}}