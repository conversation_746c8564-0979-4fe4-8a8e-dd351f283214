{"id": "APCp9GPNjUSFPSfJ", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Business WhatsApp AI RAG Chatbot", "tags": [], "nodes": [{"id": "2c5b2dd1-c63f-4bc9-909e-5f4b2a385d01", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1020, 1040], "parameters": {"options": {}, "respondWith": "text", "responseBody": "={{ $json.query['hub.challenge'] }}"}, "typeVersion": 1.1}, {"id": "cc230fcd-f88c-40d4-8835-ac9dc6228b18", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1560, 1380], "parameters": {"text": "={{ $('Respond').item.json.body.entry[0].changes[0].value.messages[0].text.body }}", "agent": "conversationalAgent", "options": {"systemMessage": "You are an AI-powered assistant for an electronics store. Your primary goal is to assist customers by providing accurate and helpful information about products, troubleshooting tips, and general support. Use the provided knowledge base (retrieved documents) to answer questions with precision and professionalism.\n\n**Guidelines**:\n1. **Product Information**:\n - Provide detailed descriptions of products, including specifications, features, and compatibility.\n - Highlight key selling points and differences between similar products.\n - Mention availability, pricing, and promotions if applicable.\n\n2. **Technical Support**:\n - Offer step-by-step troubleshooting guides for common issues.\n - Suggest solutions for setup, installation, or configuration problems.\n - If the issue is complex, recommend contacting the store’s support team for further assistance.\n\n3. **Customer Service**:\n - Respond politely and professionally to all inquiries.\n - If a question is unclear, ask for clarification to provide the best possible answer.\n - For order-related questions (e.g., status, returns, or cancellations), guide customers on how to proceed using the store’s systems.\n\n4. **Knowledge Base Usage**:\n - Always reference the provided knowledge base (retrieved documents) to ensure accuracy.\n - If the knowledge base does not contain relevant information, inform the customer and suggest alternative resources or actions.\n\n5. **Tone and Style**:\n - Use a friendly, approachable, and professional tone.\n - Avoid technical jargon unless the customer demonstrates familiarity with the topic.\n - Keep responses concise but informative.\n\n**Example Interactions**:\n1. **Product Inquiry**:\n - Customer: \"What’s the difference between the XYZ Smartwatch and the ABC Smartwatch?\"\n - AI: \"The XYZ Smartwatch features a longer battery life (up to 7 days) and built-in GPS, while the ABC Smartwatch has a brighter AMOLED display and supports wireless charging. Both are compatible with iOS and Android devices. Would you like more details on either product?\"\n\n2. **Technical Support**:\n - Customer: \"My wireless router isn’t connecting to the internet.\"\n - AI: \"Please try the following steps: 1) Restart your router and modem. 2) Ensure all cables are securely connected. 3) Check if the router’s LED indicators show a stable connection. If the issue persists, you may need to reset the router to factory settings. Would you like a detailed guide for resetting your router?\"\n\n3. **Customer Service**:\n - Customer: \"How do I return a defective product?\"\n - AI: \"To return a defective product, please visit our Returns Portal on our website and enter your order number. You’ll receive a return label and instructions. If you need further assistance, our support team is <NAME_EMAIL>.\"\n\n**Limitations**:\n- If the question is outside the scope of the knowledge base or requires human intervention, inform the customer and provide contact details for the appropriate department.\n- Do not provide speculative or unverified information. Always rely on the knowledge base or direct the customer to official resources."}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "283df38d-1a2b-44d9-8e29-5e928ca1c4c9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [740, 1260], "parameters": {"width": 459, "height": 485, "content": "# STEP 4\n\n## RAG System\n\n\n\n\n\n\n\n\n\n\n\n\n\n* *Respond* webhook receives various POST Requests from Meta regarding WhatsApp messages (user messages + status notifications)\n* Check if the incoming JSON contains user message\n* Echo back the text message to the user. This is a custom message, not a WhatsApp Business template message\n"}, "typeVersion": 1}, {"id": "b8f5ac53-03fe-4151-ac56-b246245702b6", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1560, 1580], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "a02f4e76-1895-48ad-a2d5-6daf3347f181", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [460, 100], "parameters": {}, "typeVersion": 1}, {"id": "35a71dd7-ae08-46c5-acb2-e66d92b311cb", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [1440, 220], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "1538c8b1-f914-4991-b311-e533df625c5f", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [760, -40], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION", "method": "POST", "options": {}, "jsonBody": "{\n \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "423b73a6-2497-4635-9ad0-9e768f32018d", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [760, 220], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "9519866a-f28a-495a-9cb4-3b2170407943", "name": "Get folder", "type": "n8n-nodes-base.googleDrive", "position": [980, 220], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=test-whatsapp"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "c9a36259-8340-4382-8bb0-84b73a8288c6", "name": "Download Files", "type": "n8n-nodes-base.googleDrive", "position": [1200, 220], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "b20975d7-e367-49a3-ac8c-************", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1420, 420], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "4c2d02a4-c954-42c4-97b0-b94ee3198f56", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1600, 420], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "********-1691-4caf-bf63-c04db85708dc", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [1560, 580], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "cc74592d-6562-4816-917c-0d88913a8125", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [200, 1140], "parameters": {"color": 3, "width": 405, "height": 177, "content": "## Important!\n### Configure the webhook nodes this way:\n* Make sure that both *Verify* and *Respond* have the same URL\n* *Verify* should have GET HTTP Method\n* *Respond* should have POST HTTP Method"}, "typeVersion": 1}, {"id": "9c8d4973-dcc5-4506-967f-3b3a5df501fa", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [740, 800], "parameters": {"color": 5, "width": 618, "height": 392, "content": "# STEP 3\n\n## Create Webhook\n* Go to your [Meta for Developers App page](https://developers.facebook.com/apps/), navigate to the App settings\n* Add a **production webhook URL** as a new Callback URL\n* *Verify* webhook receives a GET Request and sends back a verification code\n* After that you can delete this\n"}, "typeVersion": 1}, {"id": "ec013e0c-a354-4f12-8ded-97013bb8fb21", "name": "Verify", "type": "n8n-nodes-base.webhook", "position": [780, 1040], "webhookId": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "parameters": {"path": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "options": {}, "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "253ddc93-5693-4362-aa6c-a80ab3f6df82", "name": "Respond", "type": "n8n-nodes-base.webhook", "position": [760, 1420], "webhookId": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "parameters": {"path": "f0d2e6f6-8fda-424d-b377-0bd191343c20", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "2d4b956e-92d9-41da-a6d3-9f588e453d2a", "name": "is Message?", "type": "n8n-nodes-base.if", "position": [980, 1420], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "959fbffc-876a-4235-87be-2dedba4926cd", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body.entry[0].changes[0].value.messages[0] }}", "rightValue": ""}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "2af633a9-f6b0-4989-9e85-abb619d2b3bb", "name": "Only message", "type": "n8n-nodes-base.whatsApp", "position": [1280, 1520], "parameters": {"textBody": "=You can only send text messages", "operation": "send", "phoneNumberId": "***************", "requestOptions": {}, "additionalFields": {}, "recipientPhoneNumber": "={{ $('Respond').item.json.body.entry[0].changes[0].value.contacts[0].wa_id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "5235dd06-2235-4edb-904e-872848e2ed79", "name": "Send", "type": "n8n-nodes-base.whatsApp", "position": [1980, 1380], "parameters": {"textBody": "={{ $json.output }}", "operation": "send", "phoneNumberId": "***************", "requestOptions": {}, "additionalFields": {}, "recipientPhoneNumber": "={{ $('Respond').item.json.body.entry[0].changes[0].value.contacts[0].wa_id }}"}, "credentials": {"whatsAppApi": {"id": "HDUOWQXeRXMVjo0Z", "name": "WhatsApp account"}}, "typeVersion": 1}, {"id": "dafe692e-7767-4ded-966c-df812f58ae63", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1760, 1580], "parameters": {}, "typeVersion": 1.3}, {"id": "ba6254bd-4dad-47bb-a535-7b6b708ea763", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [960, -100], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 1\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "83cf4483-cd45-4de6-9b88-e00727ed8352", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [740, 160], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 2\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "4e0a4245-370f-4596-b01b-4eed8acbe2c3", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1520, 1260], "parameters": {"width": 380, "height": 260, "content": "## Configure AI Agent\nSet System prompt and chat model. If you want you can set any tools"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "4eb1a148-185f-4f16-a6ad-01c3201d4fc0", "connections": {"Verify": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Respond": {"main": [[{"node": "is Message?", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send", "type": "main", "index": 0}]]}, "Get folder": {"main": [[{"node": "Download Files", "type": "main", "index": 0}]]}, "is Message?": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "Only message", "type": "main", "index": 0}]]}, "Download Files": {"main": [[{"node": "Qdrant Vector Store", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get folder", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store", "type": "ai_document", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create collection", "type": "main", "index": 0}, {"node": "Refresh collection", "type": "main", "index": 0}]]}}}