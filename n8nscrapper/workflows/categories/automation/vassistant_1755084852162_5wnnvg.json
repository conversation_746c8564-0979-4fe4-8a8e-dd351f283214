{"name": "vAssistant", "nodes": [{"parameters": {"url": "=https://api.hubapi.com/conversations/v3/conversations/threads/{{ $json[\"body\"][0][\"objectId\"] }}/messages/{{ $json[\"body\"][0][\"messageId\"] }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotAppToken", "options": {}}, "id": "************************************", "name": "getHubspotMessage", "type": "n8n-nodes-base.httpRequest", "position": [-540, 340], "typeVersion": 4.1}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/threads", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n \"messages\": [\n {\n \"role\": \"user\",\n \"content\": \"{{ $('getHubspotMessage').item.json[\"text\"] }}\"\n }\n ]\n}", "options": {}}, "id": "833a8a74-32bf-4430-89f3-781fcb7800f8", "name": "OpenAi Create Thread", "type": "n8n-nodes-base.httpRequest", "position": [440, 180], "typeVersion": 4.1}, {"parameters": {"method": "POST", "url": "=https://api.openai.com/v1/threads/{{ $json[\"OpenAI Thread ID\"] }}/runs", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n \"assistant_id\": \"asst_MA71Jq0SElVpdjmJa212CTFd\"\n}", "options": {}}, "id": "34f814f9-e504-4c59-ba0c-eee19dc2b57f", "name": "OpenAI Run", "type": "n8n-nodes-base.httpRequest", "position": [800, 180], "typeVersion": 4.1}, {"parameters": {"url": "=https://api.openai.com/v1/threads/{{ $json[\"thread_id\"] }}/runs/{{ $json[\"id\"] }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "options": {}}, "id": "7bb0eb14-59fe-4a2a-abdc-0988d9a92143", "name": "Get Run", "type": "n8n-nodes-base.httpRequest", "position": [1100, 360], "typeVersion": 4.1, "alwaysOutputData": true}, {"parameters": {"url": "=https://api.openai.com/v1/threads/{{ $json[\"thread_id\"] }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "options": {}}, "id": "e381841c-d043-4dde-9886-12c57e980ca3", "name": "Get Last Message", "type": "n8n-nodes-base.httpRequest", "position": [1700, 220], "typeVersion": 4.1}, {"parameters": {"url": "=https://www.listafirme.ro/api/search-v1.asp", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "982dc86a0c1bd4c71185d39ae9f36998"}, {"name": "src", "value": "={{JSON.parse($json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"function\"][\"arguments\"]).src}}"}]}, "options": {}}, "id": "65429dff-350d-439c-9f6b-8db1b5b48366", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "position": [2000, 720], "typeVersion": 4.1}, {"parameters": {"dataType": "string", "value1": "={{ $json.status }}", "rules": {"rules": [{"value2": "completed"}, {"value2": "requires_action", "output": 1}, {"operation": "=equal", "value2": "in_progress", "output": 2}, {"value2": "queued", "output": 3}]}}, "id": "b21df0d6-a3c9-4558-972d-ca4a30b9c39e", "name": "Completed, Action or Inprogress", "type": "n8n-nodes-base.switch", "position": [1300, 360], "typeVersion": 1}, {"parameters": {"unit": "seconds"}, "id": "8a111e1a-4f83-4e86-b648-b09a4ce2f258", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1540, 760], "webhookId": "e15c2bb6-e022-4c6d-869b-f361b1ec1259", "typeVersion": 1}, {"parameters": {"unit": "seconds"}, "id": "aee971c7-4a58-49ed-9aca-1453276ee331", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [1520, 520], "webhookId": "3a175bf4-c569-431e-bc56-abed3653ce9d", "typeVersion": 1}, {"parameters": {"method": "POST", "url": "=https://api.openai.com/v1/threads/{{ $('Select Function').item.json[\"thread_id\"] }}/runs/{{ $('Select Function').item.json[\"id\"] }}/submit_tool_outputs", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n \"tool_outputs\": [\n {\n \"tool_call_id\": \"{{ $('Select Function').item.json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"id\"] }}\",\n \"output\": \"{{$json.escapedJsonString}}\"\n }\n ]\n} ", "options": {}}, "id": "87708852-cea8-47ec-a40d-f84cea701a0a", "name": "Submit Data", "type": "n8n-nodes-base.httpRequest", "position": [2540, 720], "typeVersion": 4.1, "alwaysOutputData": true}, {"parameters": {"dataType": "string", "value1": "={{ $json.required_action.submit_tool_outputs.tool_calls[0].function.name }}", "rules": {"rules": [{"value2": "getAWBbyOrder"}, {"value2": "get_awb_history", "output": 1}]}}, "id": "6711aed7-6d57-47d7-9347-175671fdb7f6", "name": "Select Function", "type": "n8n-nodes-base.switch", "position": [1700, 460], "typeVersion": 1}, {"parameters": {"jsCode": "const item1 = $input.all()[0]?.json;\nconst jsonString = JSON.stringify(item1);\nconst escapedJsonString = jsonString.replace(/\"/g, '\\\\\"');\n\nreturn { escapedJsonString };\n"}, "id": "f77ca5ab-6a50-4c39-b9b5-19b0be01e225", "name": "Code1", "type": "n8n-nodes-base.code", "position": [2260, 720], "typeVersion": 2}, {"parameters": {"unit": "seconds"}, "id": "bb0e5f11-16ff-4270-8c4d-61fc548a46af", "name": "Wait2", "type": "n8n-nodes-base.wait", "position": [2900, 1160], "webhookId": "68ae5068-6a39-424c-b88d-019bfee78b6f", "typeVersion": 1}, {"parameters": {"url": "=https://www.listafirme.ro/api/info-v1.asp", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "982dc86a0c1bd4c71185d39ae9f36998"}, {"name": "data", "value": "={\"TaxCode\":\"{{JSON.parse($json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"function\"][\"arguments\"]).src}}\",\"NACE\":\"info\",\"VAT\":\"\", \"RegNo\":\"\", \"Status\":\"\", \"LegalForm\":\"\", \"Name\":\"\", \"Date\":\"\", \"TownCode\":\"\", \"County\":\"\", \"City\":\"\", \"Address\":\"\", \"Administrators\":\"\", \"Shareholders\":\"\", \"Balance\":\"latest\", \"Phone\":\"\", \"Mobile\":\"\", \"Fax\":\"\", \"Email\":\"\", \"Web\":\"\", \"Geolocation\":\"\", \"Description\":\"\", \"Trademarks\":\"\", \"Subsidiaries\":\"\", \"Branches\":\"\", \"FiscalActivity\":\"\", \"Obligations\":\"\", \"Links\":\"\"}"}]}, "options": {}}, "id": "ace7c892-fca4-4dc3-952a-9b02142e59cc", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "position": [2000, 940], "typeVersion": 4.1}, {"parameters": {"jsCode": "const item1 = $input.all()[0]?.json;\nconst jsonString = JSON.stringify(item1);\nconst escapedJsonString = jsonString.replace(/\"/g, '\\\\\"');\n\nreturn { escapedJsonString };\n"}, "id": "8b5e8648-22aa-4189-9eab-59902f88367d", "name": "Code", "type": "n8n-nodes-base.code", "position": [2240, 940], "typeVersion": 2}, {"parameters": {"method": "POST", "url": "=https://api.openai.com/v1/threads/{{ $('Select Function').item.json[\"thread_id\"] }}/runs/{{ $('Select Function').item.json[\"id\"] }}/submit_tool_outputs", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n \"tool_outputs\": [\n {\n \"tool_call_id\": \"{{ $('Select Function').item.json[\"required_action\"][\"submit_tool_outputs\"][\"tool_calls\"][0][\"id\"] }}\",\n \"output\": \"{{$json.escapedJsonString}}\"\n }\n ]\n} ", "options": {}}, "id": "787a130d-5033-43f7-9257-b97593c61c63", "name": "Submit Data1", "type": "n8n-nodes-base.httpRequest", "position": [2440, 940], "typeVersion": 4.1, "alwaysOutputData": true}, {"parameters": {"unit": "seconds"}, "id": "ba5d69be-64dd-4aba-b871-6c798e48591d", "name": "Wait3", "type": "n8n-nodes-base.wait", "position": [2640, 1120], "webhookId": "6d7d039c-8a4b-4178-8d31-57fb3c24ac14", "typeVersion": 1}, {"parameters": {"method": "POST", "url": "=https://api.hubapi.com/conversations/v3/conversations/threads/{{ $('getHubspotMessage').item.json[\"conversationsThreadId\"] }}/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotAppToken", "sendBody": true, "bodyParameters": {"parameters": [{"name": "type", "value": "MESSAGE"}, {"name": "richText", "value": "={{ $json.data[0].content[0].text.value }}"}, {"name": "senderActorId", "value": "A-5721819"}, {"name": "channelId", "value": "={{ $('getHubspotMessage').item.json.channelId }}"}, {"name": "channelAccountId", "value": "={{ $('getHubspotMessage').item.json.channelAccountId }}"}, {"name": "text", "value": "{{ $json.data[0].content[0].text.value }}"}]}, "options": {}}, "id": "7b8333b4-ab87-47ec-9d62-1d21228d00e9", "name": "respondHubspotMessage1", "type": "n8n-nodes-base.httpRequest", "position": [2000, 180], "typeVersion": 4.1}, {"parameters": {"conditions": {"string": [{"value1": "={{ $('getHubspotMessage').item.json[\"senders\"][0][\"actorId\"] }}", "operation": "notEqual", "value2": "A-5721819"}]}}, "id": "889d5f10-37db-4b23-9c0d-192497780d29", "name": "IF", "type": "n8n-nodes-base.if", "position": [-180, 360], "typeVersion": 1}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appGAPr0tOy8J0NXC", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC", "cachedResultName": "Hubspot Conversations ChatGPT"}, "table": {"__rl": true, "mode": "list", "value": "tbljZ0POq35jgnKES", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC/tbljZ0POq35jgnKES", "cachedResultName": "Conversations"}, "filterByFormula": "={Hubspot Thread ID}=\"{{ $json.conversationsThreadId }}\"", "options": {}}, "id": "4e82b107-34a3-468c-87dd-870fbdf4dd7f", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [40, 360], "typeVersion": 2, "alwaysOutputData": true}, {"parameters": {"conditions": {"string": [{"value1": "={{ $('Airtable').item.json.id }}", "operation": "isEmpty"}]}}, "id": "1a76ae49-37a3-4771-aaff-a2742cf1f6ee", "name": "IF1", "type": "n8n-nodes-base.if", "position": [220, 400], "typeVersion": 1}, {"parameters": {"operation": "create", "base": {"__rl": true, "mode": "list", "value": "appGAPr0tOy8J0NXC", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC", "cachedResultName": "Hubspot Conversations ChatGPT"}, "table": {"__rl": true, "mode": "list", "value": "tbljZ0POq35jgnKES", "cachedResultUrl": "https://airtable.com/appGAPr0tOy8J0NXC/tbljZ0POq35jgnKES", "cachedResultName": "Conversations"}, "columns": {"value": {"OpenAI Thread ID": "={{ $json[\"id\"] }}", "Hubspot Thread ID": "={{ $('getHubspotMessage').item.json.conversationsThreadId }}"}, "schema": [{"id": "Hubspot Thread ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Hubspot Thread ID", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "OpenAI Thread ID", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "OpenAI Thread ID", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}}, "id": "a88be828-d25f-42e3-aada-0a112694e881", "name": "createThread", "type": "n8n-nodes-base.airtable", "position": [620, 180], "typeVersion": 2}, {"parameters": {"method": "POST", "url": "=https://api.openai.com/v1/threads/{{ $('Airtable').item.json[\"OpenAI Thread ID\"] }}/runs", "authentication": "predefinedCredentialType", "nodeCredentialType": "openAiApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "openai-beta", "value": "assistants=v1"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n \"assistant_id\": \"asst_MA71Jq0SElVpdjmJa212CTFd\"\n}", "options": {}}, "id": "dce2fb6d-d7ec-43bd-9e6c-07890464c6d9", "name": "OpenAI Run1", "type": "n8n-nodes-base.httpRequest", "position": [800, 540], "typeVersion": 4.1, "alwaysOutputData": false, "continueOnFail": true}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json[\"body\"][0][\"messageId\"] }}", "operation": "isNotEmpty"}]}}, "id": "8945eaaf-fe6b-45a1-bf3f-9d70e7cb1d1c", "name": "IF2", "type": "n8n-nodes-base.if", "position": [-760, 360], "typeVersion": 1}, {"parameters": {"resource": "assistant", "assistantId": {"__rl": true, "mode": "list", "value": "asst_wVbEcnRttQ8K65DOV0fk1DJU", "cachedResultName": "Lista Firma Agent"}, "prompt": "define", "text": "={{ $('getHubspotMessage').item.json[\"text\"] }}", "options": {"baseURL": "https://api.openai.com/v1/threads/{{ $('Airtable').item.json[\"OpenAI Thread ID\"] }}/messages"}}, "id": "5f31d5b4-4a32-481b-bb94-dce3076749b9", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [460, 540], "typeVersion": 1.3}, {"parameters": {"httpMethod": "POST", "path": "hubspot-tinder", "options": {}}, "id": "************************************", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-980, 360], "webhookId": "637d5b46-b35f-4943-92a2-864ddce170f4", "typeVersion": 1}], "pinData": {}, "connections": {"IF": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "IF1": {"main": [[{"node": "OpenAi Create Thread", "type": "main", "index": 0}], [{"node": "OpenAI", "type": "main", "index": 0}]]}, "IF2": {"main": [[{"node": "getHubspotMessage", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Submit Data1", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Submit Data", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "OpenAI Run1", "type": "main", "index": 0}]]}, "Get Run": {"main": [[{"node": "Completed, Action or Inprogress", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "IF2", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}, "OpenAI Run": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "OpenAI Run1": {"main": [[{"node": "Get Run", "type": "main", "index": 0}]]}, "Submit Data": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Submit Data1": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "createThread": {"main": [[{"node": "OpenAI Run", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Select Function": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Get Last Message": {"main": [[{"node": "respondHubspotMessage1", "type": "main", "index": 0}]]}, "getHubspotMessage": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "OpenAi Create Thread": {"main": [[{"node": "createThread", "type": "main", "index": 0}]]}, "Completed, Action or Inprogress": {"main": [[{"node": "Get Last Message", "type": "main", "index": 0}], [{"node": "Select Function", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "88126990-c45f-42af-bc64-e850ef27815c", "meta": {"instanceId": "b2189e49751062fbda95b4ca9399267c1bf87960485d2972d5aae0b9437119d2"}, "id": "qP65pQPKSVi5YiAA", "tags": []}