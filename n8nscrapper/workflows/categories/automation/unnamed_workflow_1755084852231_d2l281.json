{"nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}}, "id": "fc4361c6-494a-4962-9488-2fe8dda1948c", "name": "Triggers if new file in watched folder", "type": "n8n-nodes-base.microsoftOneDriveTrigger", "position": [928, 432], "typeVersion": 1}, {"parameters": {}, "id": "7e914111-ed3b-42c1-9461-5d36739db2b3", "name": "Not CSV", "type": "n8n-nodes-base.stopAndError", "position": [1856, 544], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "43f092fe-9bc5-4e5f-a80b-db8aae53c9af", "name": "Extracts data from csv", "type": "n8n-nodes-base.extractFromFile", "position": [1856, 304], "typeVersion": 1}, {"parameters": {}, "id": "142f14e7-663f-4225-8e58-551c1e089450", "name": "Microsoft OneDrive2", "type": "n8n-nodes-base.microsoftOneDrive", "position": [2640, 304], "typeVersion": 1, "alwaysOutputData": true, "onError": "continueErrorOutput"}, {"parameters": {}, "id": "48186060-343b-458c-bf2e-a5df501445e2", "name": "Cleans the output", "type": "n8n-nodes-base.code", "position": [2080, 304], "notesInFlow": false, "typeVersion": 2}, {"parameters": {"content": ""}, "id": "5914197f-f217-447b-acab-b149f70b11ab", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2000, 48], "typeVersion": 1}, {"parameters": {}, "id": "c2d9e3a4-b9de-4b25-a06d-49d30402dab0", "name": "No Operation, do nothing", "type": "n8n-nodes-base.noOp", "position": [2880, 320], "typeVersion": 1}, {"parameters": {"content": ""}, "id": "8d65a94e-3946-4586-ad65-15f765f1b38c", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2832, 128], "typeVersion": 1}, {"parameters": {"filters": {}}, "id": "d2e0d8d2-f62a-4831-be0c-049d735c1fd5", "name": "Logs the update on sheet", "type": "n8n-nodes-base.microsoftExcel", "position": [1120, 432], "typeVersion": 2.1}, {"parameters": {}, "id": "ff21f07d-7b6a-46cc-87db-2a3fdcd9614f", "name": "Gets the new file infos", "type": "n8n-nodes-base.microsoftOneDrive", "position": [1296, 432], "typeVersion": 1}, {"parameters": {}, "id": "7cfa40a4-f3c9-464f-83d4-1eea2a869c75", "name": "Downloads the new file", "type": "n8n-nodes-base.microsoftOneDrive", "position": [1488, 432], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "e936a8ec-5544-4d13-9dca-747983ab7e50", "name": "Checks if it&apos;s CSV format", "type": "n8n-nodes-base.if", "position": [1680, 432], "typeVersion": 2.2}, {"parameters": {"options": {}}, "id": "f5b24101-4cb9-4a4a-be33-9b6191e20401", "name": "Prepares the fields to put in the excel table", "type": "n8n-nodes-base.set", "position": [2272, 304], "typeVersion": 3.4}, {"parameters": {"filters": {}}, "id": "47793366-1655-4322-ba74-14a5b31b03e8", "name": "Updates the excel table", "type": "n8n-nodes-base.microsoftExcel", "position": [2448, 304], "typeVersion": 2.1}], "connections": {"Triggers if new file in watched folder": {"main": [[{"node": "Logs the update on sheet", "type": "main", "index": 0}]]}, "Extracts data from csv": {"main": [[{"node": "Cleans the output", "type": "main", "index": 0}]]}, "Microsoft OneDrive2": {"main": [[], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Cleans the output": {"main": [[{"node": "Prepares the fields to put in the excel table", "type": "main", "index": 0}]]}, "Logs the update on sheet": {"main": [[{"node": "Gets the new file infos", "type": "main", "index": 0}]]}, "Gets the new file infos": {"main": [[{"node": "Downloads the new file", "type": "main", "index": 0}]]}, "Downloads the new file": {"main": [[{"node": "Checks if it&apos;s CSV format", "type": "main", "index": 0}]]}, "Checks if it&apos;s CSV format": {"main": [[{"node": "Extracts data from csv", "type": "main", "index": 0}], [{"node": "Not CSV", "type": "main", "index": 0}]]}, "Prepares the fields to put in the excel table": {"main": [[{"node": "Updates the excel table", "type": "main", "index": 0}]]}, "Updates the excel table": {"main": [[{"node": "Microsoft OneDrive2", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "003c0ba7d159a939f933f410e94e211942ac56e758c92fbd77e0167e22be261d"}}