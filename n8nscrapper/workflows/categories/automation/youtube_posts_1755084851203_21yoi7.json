{"name": "Youtube Posts", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1EHqn6bGcPdd4WywRS8Y4sgTgwqyP809vpe4vIN5IQSI", "mode": "list", "cachedResultName": "Youtube posts", "cachedResultUrl": "Google drive link"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "Google drive link"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [-280, -120], "id": "dc2aaade-1081-44af-9f22-a2c94607bddf", "name": "Google Sheets Trigger", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "UAXntbZXP7QOpWSu", "name": "Google Sheets Trigger account"}}}, {"parameters": {"jsCode": "// Get all input items (rows from Google Sheets)\nconst allRows = $input.all();\n\n// Get the last row\nconst lastRow = allRows[allRows.length - 1];\n\n// (Optional) Add a custom field to the last row\nlastRow.json.myNewField = 1;\n\n// Return only the last row\nreturn [lastRow];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, -120], "id": "e444375f-0104-4256-8217-c45068ba6551", "name": "Code"}, {"parameters": {"promptType": "define", "text": "={{ $json.Topics }}", "hasOutputParser": true, "options": {"systemMessage": "You are a helpful AI assistant that generates a 15-second narrative script tailored for YouTube video introductions. For every request, you will receive a video idea or topic as input. Based on this, you must output three things:\n\nTitle – A catchy, keyword-optimized YouTube title.\n\nDescription – A concise, engaging video description suitable for YouTube, including relevant keywords and a call to action (like subscribing).\n\nNarrative Script – A 15-second spoken script for the start of the video that:\n\nHooks the viewer\n\nBriefly introduces what the video is about\n\nEncourages them to keep watching"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [160, -120], "id": "96fe1b88-dfe9-49f9-9c4c-3363244d5bc0", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-20, 120], "id": "fbe54a5d-4e45-480e-ba84-85b73fb562d8", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "XUuhIwKDrgAobBOF", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.Topics }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [180, 100], "id": "469252aa-069e-4198-a99d-defd4fc14d85", "name": "Window Buffer Memory"}, {"parameters": {"jsonSchemaExample": "{\n\t\"Title\": \"California\",\n\t\"Description\": \"California\",\n\t\"Script\": \"Los Angeles\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [340, 120], "id": "333828a8-9306-41de-b098-b18226be3a04", "name": "Structured Output Parser"}, {"parameters": {"method": "POST", "url": "https://tavusapi.com/v2/videos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "api key value"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "replica_id", "value": "r0e341823b41"}, {"name": "script", "value": "={{ $json.output.Script }}"}, {"name": "video_name", "value": "={{ $json.output.Title }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, -120], "id": "8061fc32-f18a-488e-8b59-d76a30e4a072", "name": "HTTP Request"}, {"parameters": {"url": "=https://tavusapi.com/v2/videos/{{ $json.video_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key", "value": "api key value"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [740, -120], "id": "8ea15b9c-deba-4249-9043-f7f32aaaedfe", "name": "HTTP Request1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [960, -120], "id": "ce39eb70-99b2-496e-8bf5-e84a2f011d79", "name": "Remove Duplicates"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a751613a-ed63-4937-87b5-3d3c4e33b1f1", "leftValue": "={{ $json.status }}", "rightValue": "ready", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1180, -120], "id": "591ef6c2-60b4-4bf5-a715-398a6559a7ba", "name": "If"}, {"parameters": {"unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1400, -20], "id": "b3377d92-bdb6-4d07-afaa-74695c3a0733", "name": "Wait", "webhookId": "aaf2f0f5-12e4-4d3f-8ec4-ac883d8c6828"}, {"parameters": {"url": "={{ $json.download_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1400, -220], "id": "ce81e720-cd15-4806-abf1-09e14add520e", "name": "HTTP Request2"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?uploadType=multipart", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, -220], "id": "cdb3065e-c9af-4761-a554-c69f19eebf94", "name": "HTTP Request3", "credentials": {"youTubeOAuth2Api": {"id": "b1DFANAgU5Fj0u0c", "name": "YouTube account 3"}}}], "pinData": {}, "connections": {"Google Sheets Trigger": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "AI Agent": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f2ad7883-b4f5-431c-bcbb-85982b7afb15", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6c97abda19f53c864e448ccd73924e48de2c9faaca2a268f2ae17c7b52dd49ff"}, "id": "vpVPXfkvq8af0DRE", "tags": []}