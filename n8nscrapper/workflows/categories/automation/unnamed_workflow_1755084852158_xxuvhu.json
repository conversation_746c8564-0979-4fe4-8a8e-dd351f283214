{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-240, 1060], "id": "59ea67f6-4799-4419-bbe2-b860aea03a95", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"url": "https://f002.backblazeb2.com/file/creatomate-c8xg3hsxdu/42dcdfdf-d310-493d-9f17-2d9e8906d290.mp4", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, 1060], "id": "328f8ab9-f19e-4c93-9236-474de181a61d", "name": "HTTP Request"}, {"parameters": {"assignments": {"assignments": [{"id": "ff7e8255-9644-403b-bdb1-2a1c060fc818", "name": "Title", "value": "Baby Hippopotamus are cute ngl", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [200, 1060], "id": "50c92dc1-19b6-4783-a6ff-75c363076e35", "name": "<PERSON>"}, {"parameters": {"content": "\n\n# 👉 [Click here to sign up for Upload-post](https://www.upload-post.com/?linkId=lp_144414&sourceId=broccoli&tenantId=upload-post-app)\n\n# 👉 [Video tutorial](https://youtu.be/h_U_p4pQosM)", "height": 200, "width": 740, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-420, 800], "typeVersion": 1, "id": "8e2a1f76-7a33-4b5f-a82c-14c838f37e3e", "name": "<PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "https://api.upload-post.com/api/upload_photos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "api key"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "user", "value": "brocoli"}, {"name": "platform[]", "value": "instagram"}, {"name": "title", "value": "={{ $json.Title }}"}, {"name": "description", "value": "A description buddy!"}, {"parameterType": "formBinaryData", "name": "photos[]", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [580, 760], "id": "a786b38d-87a5-4abf-b4bd-8b5c5f465d34", "name": "Instagram"}, {"parameters": {"method": "POST", "url": "https://api.upload-post.com/api/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "api key"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "user", "value": "brocoli"}, {"name": "platform[]", "value": "x"}, {"name": "title", "value": "={{ $json.Title }}"}, {"parameterType": "formBinaryData", "name": "video", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [580, 960], "id": "2afc2b40-d227-45d8-9b9a-99024c6d61f2", "name": "Twitter/X"}, {"parameters": {"method": "POST", "url": "https://api.upload-post.com/api/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "api key"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "user", "value": "brocoli"}, {"name": "platform[]", "value": "linkedin"}, {"name": "title", "value": "={{ $json.Title }}"}, {"parameterType": "formBinaryData", "name": "video", "inputDataFieldName": "data"}, {"name": "visibility", "value": "PUBLIC"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [580, 1160], "id": "926305fa-d898-416c-a66a-387b8368c44f", "name": "Linkedin"}, {"parameters": {"method": "POST", "url": "https://api.upload-post.com/api/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "api key"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "user", "value": "brocoli"}, {"name": "platform[]", "value": "youtube"}, {"name": "title", "value": "={{ $json.Title }}"}, {"parameterType": "formBinaryData", "name": "video", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [580, 1360], "id": "4c8ada6f-6d46-44bd-95b1-8d75882f6dcc", "name": "Youtube"}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Linkedin", "type": "main", "index": 0}, {"node": "Twitter/X", "type": "main", "index": 0}, {"node": "Instagram", "type": "main", "index": 0}, {"node": "Youtube", "type": "main", "index": 0}]]}}, "pinData": {"When clicking ‘Execute workflow’": [{}]}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "ce11d2f10d93eff7b3ca4b8b4deb29007d99f7b17b7aa105891971725ff86969"}}