{"meta": {"instanceId": "d9e1cda642de4f8909345a14409b4a3b496085da88cc8ef314554b8bf411da6d"}, "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "RAG", "type": "string"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-340, -120], "id": "workflow-trigger", "name": "When Executed by Another Workflow"}, {"parameters": {"jsCode": "const input = $json;\nconsole.log('RAG Workflow received:', JSON.stringify(input, null, 2));\nconst ragQuery = input.RAG || input.rag || '';\nconsole.log('RAG Query:', ragQuery);\nif (!ragQuery) {\n  return [{\n    json: {\n      error: 'No RAG query provided',\n      debug_input: input\n    }\n  }];\n}\nreturn [{\n  json: {\n    query: ragQuery,\n    debug_input: input\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-160, -120], "id": "process-input", "name": "Process Input"}, {"parameters": {"promptType": "define", "text": "={{$json.query}}", "options": {"systemMessage": "Tu es un assistant IA spécialisé dans la recherche documentaire dans une base interne.\n\nTu dois uniquement répondre en utilisant les passages pertinents trouvés dans la base documentaire.\n\n- Ne réponds jamais sans preuves issues de la base.\n- Ne devine, n'invente ni n'extrapole rien.\n- Fais une synthèse claire, concise et factuelle basée uniquement sur les documents.\n- Si tu ne trouves pas d'information pertinente, répond simplement : \"Aucune information fiable trouvée dans les documents.\"\n- Renvoie la réponse au format JSON strict avec une seule clé non nulle :\n\n{\n  \"Coding\": null,\n  \"Reasoning\": null,\n  \"RAG\": \"texte de ta réponse\",\n  \"Conversation\": null\n}\n\n- Renvoie uniquement ce JSON, sans texte ni explication supplémentaires."}}, "id": "rag-agent", "name": "RAG Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [40, -120], "onError": "continueErrorOutput"}, {"parameters": {"model": "qwen3:8b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [-20, 60], "id": "ollama-chat", "name": "<PERSON><PERSON><PERSON> Chat Model", "credentials": {"ollamaApi": {"id": "uRmESWlaceJnOjmU", "name": "Ollama account"}}}, {"parameters": {"description": "Search company documents for relevant information"}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1.1, "position": [40, 180], "id": "vector-tool", "name": "Document Search Tool"}, {"parameters": {"qdrantCollection": {"__rl": true, "value": "company-docs", "mode": "list", "cachedResultName": "company-docs"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "typeVersion": 1.3, "position": [-120, 300], "id": "qdrant-store", "name": "Qdrant Vector Store", "credentials": {"qdrantApi": {"id": "UaRLhUI3EDG4Bheq", "name": "QdrantApi account"}}}, {"parameters": {"model": "nomic-embed-text:latest"}, "type": "@n8n/n8n-nodes-langchain.embeddingsOllama", "typeVersion": 1, "position": [-200, 440], "id": "embeddings", "name": "Embeddings <PERSON><PERSON><PERSON>", "credentials": {"ollamaApi": {"id": "uRmESWlaceJnOjmU", "name": "Ollama account"}}}, {"parameters": {"model": "qwen3:8b", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmOllama", "typeVersion": 1, "position": [200, 320], "id": "ollama-vector", "name": "Ollama Model for Vector Store", "credentials": {"ollamaApi": {"id": "uRmESWlaceJnOjmU", "name": "Ollama account"}}}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Process Input", "type": "main", "index": 0}]]}, "Process Input": {"main": [[{"node": "RAG Agent", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "RAG Agent", "type": "ai_languageModel", "index": 0}]]}, "Document Search Tool": {"ai_tool": [[{"node": "RAG Agent", "type": "ai_tool", "index": 0}]]}, "Qdrant Vector Store": {"ai_vectorStore": [[{"node": "Document Search Tool", "type": "ai_vectorStore", "index": 0}]]}, "Embeddings Ollama": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "Ollama Model for Vector Store": {"ai_languageModel": [[{"node": "Document Search Tool", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}}