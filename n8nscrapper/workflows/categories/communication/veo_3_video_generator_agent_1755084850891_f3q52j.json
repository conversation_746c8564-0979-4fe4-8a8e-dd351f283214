{"name": "VEO 3 Video Generator Agent", "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "name"}, {"name": "prompt"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-20, 0], "id": "6ce7f190-0ed1-4a53-a3e6-22ae53ecbf4a", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "https://api.replicate.com/v1/models/google/veo-3/predictions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Prefer", "value": "wait"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"input\": {\n    \"prompt\": \"{{ $json.prompt }}\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [180, 0], "id": "6bd026f3-6389-4136-9254-ec8985866e80", "name": "Generate Video", "credentials": {"httpHeaderAuth": {"id": "xlhyRcOYSjjkmrtL", "name": "Replicate"}}}, {"parameters": {"amount": 3, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [340, 0], "id": "c87c0421-3198-4023-9734-7bba87728c3a", "name": "Wait", "webhookId": "968ce256-bd84-4f80-bb09-35a2cf68e656"}, {"parameters": {"url": "={{ $json.urls.get }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Prefer", "value": "wait"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, 0], "id": "5276c7a9-a486-42d7-85b8-f5bd02075d52", "name": "Get Video", "credentials": {"httpHeaderAuth": {"id": "xlhyRcOYSjjkmrtL", "name": "Replicate"}}}, {"parameters": {"url": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, 0], "id": "bbb19ab6-8916-4793-84e3-8accb568f5ef", "name": "Download Video"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appJt0NXgy4Qdiwme", "mode": "list", "cachedResultName": "VEO 3 Videos", "cachedResultUrl": "https://airtable.com/appJt0NXgy4Qdiwme"}, "table": {"__rl": true, "value": "tblH2A94diY7VjjH7", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appJt0NXgy4Qdiwme/tblH2A94diY7VjjH7"}, "columns": {"mappingMode": "defineBelow", "value": {"Name": "={{ $json.name }}", "Prompt": "={{ $('When Executed by Another Workflow').item.json.prompt }}", "Video Link": "={{ $json.webViewLink }}", "Status": "Generated"}, "matchingColumns": [], "schema": [{"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Prompt", "displayName": "Prompt", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Video Link", "displayName": "Video Link", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Generated", "value": "Generated"}, {"name": "Ready To Post", "value": "Ready To Post"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": false}, {"id": "Tiktok", "displayName": "Tiktok", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Created TIme", "displayName": "Created TIme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1040, 0], "id": "d95a8117-14f2-4762-957b-c7d650b11e0d", "name": "Create New Record", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"name": "={{ $('When Executed by Another Workflow').item.json.name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1y070Pr3NZUdcLMILbDxwhmQdd1MyK8vk", "mode": "list", "cachedResultName": "VEO 3 Videos", "cachedResultUrl": "https://drive.google.com/drive/folders/1y070Pr3NZUdcLMILbDxwhmQdd1MyK8vk"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [880, 0], "id": "95c41b26-eb60-40e2-974a-2e9c46e9ad24", "name": "Upload Video", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.fields['Video Link'] }}", "mode": "url"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1220, 0], "id": "07daaafb-e10d-416a-bf06-9113e7f18326", "name": "Get Video For Reply", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"operation": "sendVideo", "chatId": "{insert chat id}", "binaryData": true, "additionalFields": {"caption": "=Your video: {{ $json.fields.Name }} , has been generated! Check it out and let me know what you think!"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1400, 0], "id": "cabaadf0-7765-4396-938f-d438f7feba16", "name": "Reply <PERSON>ram (update chat id)", "webhookId": "6fe64948-a4c0-404e-b723-b7f367de336b", "credentials": {"telegramApi": {"id": "rajpDvuc5KDPLJEx", "name": "Veo Video Agent"}}}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Generate Video", "type": "main", "index": 0}]]}, "Generate Video": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Get Video": {"main": [[{"node": "Download Video", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Upload Video", "type": "main", "index": 0}]]}, "Upload Video": {"main": [[{"node": "Create New Record", "type": "main", "index": 0}]]}, "Create New Record": {"main": [[{"node": "Get Video For Reply", "type": "main", "index": 0}]]}, "Get Video For Reply": {"main": [[{"node": "Reply <PERSON>ram (update chat id)", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "843e3776-cb7c-4eb3-b30d-f29a1248ff32", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d8c459f4ebcadd20c3258ade09582fe0f02de933363f6184259e0c9a36784090"}, "id": "qATpZdA7ehVojeHy", "tags": [{"createdAt": "2025-06-09T07:35:33.006Z", "updatedAt": "2025-06-09T07:35:33.006Z", "id": "OozOX9dMuHatAFQJ", "name": "W18: VEO 3 Video"}]}