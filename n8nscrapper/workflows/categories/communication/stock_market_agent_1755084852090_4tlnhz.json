{"nodes": [{"parameters": {"updateTypes": ["message"], "options": {}}, "id": "fe442c2e-6c81-40b6-bbad-9b4c949c4aba", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"functionCode": "// Input: Telegram message content\nconst message = $input.item.json.message.text;\n\n// Extract company name or ticker from message\nlet companyQuery = message.replace(/stock info for|stock price for|how is|price of|ticker for|stock data for/gi, '').trim();\n\n// Clean up the query\ncompanyQuery = companyQuery.replace(/[^a-zA-Z0-9\\s]/g, '');\n\n// Return the extracted company name/ticker\nreturn {\n  json: {\n    companyQuery,\n    originalMessage: message,\n    chatId: $input.item.json.message.chat.id,\n    userId: $input.item.json.message.from.id,\n    firstName: $input.item.json.message.from.first_name\n  }\n};"}, "id": "b3f1b0a5-31ec-49ac-b6d3-c855a0d9b50a", "name": "Stock Market Agent", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"content": "{\n  \"memory\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful stock market assistant. You extract company names from user messages to help identify stocks they're interested in.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"{{$json.originalMessage}}\"\n    }\n  ]\n}", "options": {}}, "id": "07e9fc8b-fd83-4073-aa65-dc7f71d9e612", "name": "Simple Memory", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 460]}, {"parameters": {"url": "https://api.apify.com/v2/acts/apify~google-search-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"fullResponse": true, "headers": {"Authorization": "Bearer {{$credentials.apifyApi}}", "Content-Type": "application/json"}}, "sendBody": true, "bodyParameters": {"parameters": {"queries": "{{$json.companyQuery}} stock ticker symbol"}}}, "id": "d4f5e6a7-5432-4bf2-9c8a-1b3a5c6d7e8f", "name": "Get Ticker Name", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [700, 460], "credentials": {"httpHeaderAuth": {"id": "apify-credentials", "name": "Apify API"}}}, {"parameters": {"chatId": "{{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "Looking up stock information for {{$json.companyQuery}}...", "additionalFields": {}}, "id": "2a3b4c5d-6e7f-8g9h-0i1j-2k3l4m5n6o7p", "name": "Send Initial Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [450, 620]}, {"parameters": {"requestMethod": "GET", "url": "https://api.apify.com/v2/acts/drobnikj~yahoo-finance-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"fullResponse": true, "headers": {"Authorization": "Bearer {{$credentials.apifyApi}}", "Content-Type": "application/json"}}, "queryParameters": {"ticker": "{{$node[\"Get Ticker Name\"].json[0].title.split(\" \")[0]}}"}}, "id": "9a8b7c6d-5e4f-3g2h-1i0j-9k8l7m6n5o4p", "name": "Get Stock Info", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [950, 300], "credentials": {"httpHeaderAuth": {"id": "apify-credentials", "name": "Apify API"}}}, {"parameters": {"functionCode": "// Process the stock data\nconst stockData = $input.item.json[0];\nconst ticker = $node[\"Get Ticker Name\"].json[0].title.split(\" \")[0];\nconst companyName = $node[\"Stock Market Agent\"].json.companyQuery;\n\nif (!stockData) {\n  return {\n    json: {\n      message: `Sorry, I couldn't find stock information for ${companyName}. Please check the company name and try again.`,\n      chatId: $node[\"Telegram Trigger\"].json.message.chat.id\n    }\n  };\n}\n\n// Format the stock data into a readable message\nconst formattedMessage = `\n📊 *Stock Information* 📊\n\n*${stockData.ticker || ticker}* - ${stockData.companyName || companyName}\n\n*Current Price:* $${stockData.currentPrice || 'N/A'}\n*Previous Close:* $${stockData.previousClose || 'N/A'}\n*Open:* $${stockData.open || 'N/A'}\n\n*Day Range:* ${stockData.dayRange || 'N/A'}\n*52 Week Range:* ${stockData[\"52WeekRange\"] || 'N/A'}\n\n*Market Cap:* ${stockData.marketCap || 'N/A'}\n*Volume:* ${stockData.volume || 'N/A'}\n*Avg Volume:* ${stockData.avgVolume || 'N/A'}\n\n*P/E Ratio:* ${stockData.peRatio || 'N/A'}\n*EPS:* ${stockData.eps || 'N/A'}\n*Dividend:* ${stockData.dividend || 'N/A'}\n*Dividend Yield:* ${stockData.dividendYield || 'N/A'}\n\nData from Yahoo Finance\n`;\n\nreturn {\n  json: {\n    message: formattedMessage,\n    chatId: $node[\"Telegram Trigger\"].json.message.chat.id\n  }\n};"}, "id": "3c4d5e6f-7g8h-9i0j-1k2l-3m4n5o6p7q8r", "name": "Format Stock Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1200, 300]}, {"parameters": {"chatId": "{{$json.chatId}}", "text": "{{$json.message}}", "additionalFields": {"parse_mode": "<PERSON><PERSON>"}}, "id": "4d5e6f7g-8h9i-0j1k-2l3m-4n5o6p7q8r9s", "name": "Telegram", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [1450, 300]}, {"parameters": {"apiKey": "{{$credentials.openRouterApi}}", "model": "anthropic/claude-3-haiku", "prompt": "You are a stock market assistant. Extract the company name or ticker symbol from this message: \"{{$json.originalMessage}}\". If there's a clear company name or ticker, just return that name without any explanation or additional text. If there's no clear company or stock mentioned, respond with \"No company found.\"\n\nMessage: {{$json.originalMessage}}", "options": {"temperature": 0.1, "maxTokens": 100}, "simplifyOutput": true}, "id": "1a2b3c4d-5e6f-7g8h-9i0j-1k2l3m4n5o6p", "name": "OpenRouter <PERSON>", "type": "n8n-nodes-base.openRouter", "typeVersion": 1, "position": [450, 140], "credentials": {"openRouterApi": {"id": "openrouter-credentials", "name": "OpenRouter API"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Stock Market Agent", "type": "main", "index": 0}]]}, "Stock Market Agent": {"main": [[{"node": "OpenRouter <PERSON>", "type": "main", "index": 0}]]}, "Simple Memory": {"main": [[{"node": "Get Ticker Name", "type": "main", "index": 0}]]}, "Get Ticker Name": {"main": [[{"node": "Send Initial Response", "type": "main", "index": 0}]]}, "Send Initial Response": {"main": [[{"node": "Get Stock Info", "type": "main", "index": 0}]]}, "Get Stock Info": {"main": [[{"node": "Format Stock Data", "type": "main", "index": 0}]]}, "Format Stock Data": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"main": [[{"node": "Simple Memory", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "pinData": {}, "versionId": "c64d7c2e-86d4-4c69-b1d4-11c8a16b3e77", "triggerCount": 0, "tags": [{"id": "1", "name": "Stock <PERSON>"}], "name": "Stock Market Agent"}