{"meta": {"instanceId": "4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e"}, "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "schedule-trigger-001", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 300]}, {"parameters": {"model": "gpt-4o", "options": {"temperature": 0.9, "maxTokens": 2000}}, "id": "openai-model-001", "name": "OpenAI GPT-4o Model", "type": "@n8n/n8n-nodes-langchain.lmOpenAi", "typeVersion": 1, "position": [400, 500], "credentials": {"openAiApi": {"id": "openai-creds-001", "name": "OpenAI Account"}}}, {"parameters": {"options": {"systemMessage": "You are a viral content creator specializing in educational and entertaining YouTube videos. Generate engaging, trending topics that would perform well on YouTube. Focus on current trends, educational content, how-to guides, and entertainment."}}, "id": "ai-agent-topic-001", "name": "Content Topic Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.5, "position": [460, 300]}, {"parameters": {"jsonSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"topic\": {\n      \"type\": \"string\",\n      \"description\": \"Main topic for the video\"\n    },\n    \"title\": {\n      \"type\": \"string\",\n      \"description\": \"Catchy YouTube title\"\n    },\n    \"tags\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"Array of relevant tags\"\n    },\n    \"category\": {\n      \"type\": \"string\",\n      \"description\": \"Content category\"\n    }\n  },\n  \"required\": [\"topic\", \"title\", \"tags\", \"category\"]\n}"}, "id": "output-parser-001", "name": "Topic Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1, "position": [620, 480]}, {"parameters": {"options": {"systemMessage": "You are a professional video script writer. Create engaging, informative scripts for YouTube videos that are 2-3 minutes long. Include a strong hook, valuable content, and a clear call-to-action. Format the script with clear sections: HOOK, MAIN CONTENT, and CONCLUSION."}}, "id": "ai-agent-script-001", "name": "Script Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.5, "position": [680, 300]}, {"parameters": {"jsonSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"script\": {\n      \"type\": \"string\",\n      \"description\": \"Full video script\"\n    },\n    \"hook\": {\n      \"type\": \"string\",\n      \"description\": \"Opening hook (first 15 seconds)\"\n    },\n    \"mainContent\": {\n      \"type\": \"string\",\n      \"description\": \"Main content section\"\n    },\n    \"conclusion\": {\n      \"type\": \"string\",\n      \"description\": \"Conclusion and CTA\"\n    },\n    \"estimatedDuration\": {\n      \"type\": \"string\",\n      \"description\": \"Estimated video duration\"\n    }\n  },\n  \"required\": [\"script\", \"hook\", \"mainContent\", \"conclusion\"]\n}"}, "id": "output-parser-002", "name": "Script Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1, "position": [840, 480]}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "heyGenApi", "requestMethod": "GET", "url": "https://api.heygen.com/v1/avatar.list", "options": {}}, "id": "http-get-avatars-001", "name": "Get Available Avatars", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 300], "credentials": {"heyGenApi": {"id": "heygen-creds-001", "name": "HeyGen API"}}}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "heyGenApi", "requestMethod": "GET", "url": "https://api.heygen.com/v1/voice.list", "options": {}}, "id": "http-get-voices-001", "name": "Get Available Voices", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 450], "credentials": {"heyGenApi": {"id": "heygen-creds-001", "name": "HeyGen API"}}}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "heyGenApi", "requestMethod": "POST", "url": "https://api.heygen.com/v2/video/generate", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"video_inputs\": [\n    {\n      \"character\": {\n        \"type\": \"avatar\",\n        \"avatar_id\": \"{{ $('Get Available Avatars').item.json.data.avatars[0].avatar_id }}\",\n        \"scale\": 1\n      },\n      \"voice\": {\n        \"type\": \"text\",\n        \"voice_id\": \"{{ $('Get Available Voices').item.json.data.voices[0].voice_id }}\",\n        \"input_text\": \"{{ $('Script Generator').item.json.script }}\"\n      },\n      \"background\": {\n        \"type\": \"color\",\n        \"value\": \"#FFFFFF\"\n      }\n    }\n  ],\n  \"dimension\": {\n    \"width\": 1920,\n    \"height\": 1080\n  },\n  \"aspect_ratio\": \"16:9\",\n  \"test\": false,\n  \"caption\": false\n}", "options": {}}, "id": "http-generate-video-001", "name": "Generate HeyGen Video", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 300], "credentials": {"heyGenApi": {"id": "heygen-creds-001", "name": "HeyGen API"}}}, {"parameters": {"unit": "seconds", "amount": 30}, "id": "wait-initial-001", "name": "Wait for Processing", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1280, 300], "webhookId": "webhook-wait-001"}, {"parameters": {"authentication": "predefinedCredentialType", "nodeCredentialType": "heyGenApi", "requestMethod": "GET", "url": "=https://api.heygen.com/v1/video_status.get?video_id={{ $('Generate HeyGen Video').item.json.data.video_id }}", "options": {}}, "id": "http-check-status-001", "name": "Check Video Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1440, 300], "credentials": {"heyGenApi": {"id": "heygen-creds-001", "name": "HeyGen API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-001", "leftValue": "={{ $json.data.status }}", "rightValue": "completed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "if-video-ready-001", "name": "If Video Ready", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1600, 300]}, {"parameters": {"unit": "seconds", "amount": 30}, "id": "wait-retry-001", "name": "Wait Before Retry", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1440, 500], "webhookId": "webhook-wait-retry-001"}, {"parameters": {"url": "={{ $('Check Video Status').item.json.data.video_url }}", "options": {"response": {"response": {"fullResponse": false, "responseFormat": "file"}}}}, "id": "http-download-video-001", "name": "Download Video", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1760, 200]}, {"parameters": {"resource": "file", "operation": "upload", "fileUi": {"fileContent": "={{ $binary.data }}", "fileName": "=video_{{ $now.format('YYYY-MM-DD_HH-mm-ss') }}.mp4"}, "options": {}}, "id": "google-drive-upload-001", "name": "Upload to Google Drive", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1920, 200], "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-creds-001", "name": "Google Drive account"}}}, {"parameters": {"operation": "upload", "title": "={{ $('Content Topic Generator').item.json.title }}", "description": "=Created with AI automation using n8n, HeyGen, and GPT-4\n\nTopic: {{ $('Content Topic Generator').item.json.topic }}\n\nTags: {{ $('Content Topic Generator').item.json.tags.join(', ') }}", "tags": "={{ $('Content Topic Generator').item.json.tags.join(',') }}", "categoryId": "22", "privacyStatus": "public", "binaryData": true, "binaryPropertyName": "data"}, "id": "youtube-upload-001", "name": "Upload to YouTube", "type": "n8n-nodes-base.youtube", "typeVersion": 1, "position": [2080, 200], "credentials": {"youTubeOAuth2Api": {"id": "youtube-creds-001", "name": "YouTube account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEET_ID", "mode": "id"}, "sheetName": "Sheet1", "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ $now.format('YYYY-MM-DD HH:mm:ss') }}", "topic": "={{ $('Content Topic Generator').item.json.topic }}", "title": "={{ $('Content Topic Generator').item.json.title }}", "category": "={{ $('Content Topic Generator').item.json.category }}", "youtube_url": "=https://youtube.com/watch?v={{ $('Upload to YouTube').item.json.id }}", "video_id": "={{ $('Upload to YouTube').item.json.id }}", "status": "published", "drive_url": "={{ $('Upload to Google Drive').item.json.webViewLink }}"}, "matchingColumns": [], "schema": [{"id": "timestamp", "displayName": "timestamp", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "topic", "displayName": "topic", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "youtube_url", "displayName": "youtube_url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_id", "displayName": "video_id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "drive_url", "displayName": "drive_url", "required": false, "defaultMatch": false, "canBeUsedToMatch": true}]}, "options": {}}, "id": "google-sheets-log-001", "name": "Log to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [2240, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-creds-001", "name": "Google Sheets account"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "=New YouTube Video Published: {{ $('Content Topic Generator').item.json.title }}", "message": "=A new video has been successfully created and published!\n\nTitle: {{ $('Content Topic Generator').item.json.title }}\nTopic: {{ $('Content Topic Generator').item.json.topic }}\nCategory: {{ $('Content Topic Generator').item.json.category }}\n\nYouTube URL: https://youtube.com/watch?v={{ $('Upload to YouTube').item.json.id }}\nGoogle Drive: {{ $('Upload to Google Drive').item.json.webViewLink }}\n\nGenerated at: {{ $now.format('YYYY-MM-DD HH:mm:ss') }}\n\nThis video was created automatically using AI automation.", "options": {}}, "id": "send-email-001", "name": "Send Notification Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [2400, 200], "credentials": {"smtp": {"id": "smtp-creds-001", "name": "SMTP account"}}}, {"parameters": {"content": "=🎬 New Video Published!\n\nTitle: {{ $('Content Topic Generator').item.json.title }}\nWatch: https://youtube.com/watch?v={{ $('Upload to YouTube').item.json.id }}\n\n#AIAutomation #YouTubeAutomation #ContentCreation", "additionalFields": {}}, "id": "discord-webhook-001", "name": "Discord Notification", "type": "n8n-nodes-base.discord", "typeVersion": 2, "position": [2400, 350], "credentials": {"discordWebhookApi": {"id": "discord-webhook-creds-001", "name": "Discord Webhook"}}}], "connections": {"Schedule Trigger": {"main": [[{"node": "Content Topic Generator", "type": "main", "index": 0}]]}, "Content Topic Generator": {"main": [[{"node": "Script Generator", "type": "main", "index": 0}]]}, "Script Generator": {"main": [[{"node": "Get Available Avatars", "type": "main", "index": 0}, {"node": "Get Available Voices", "type": "main", "index": 0}]]}, "Get Available Avatars": {"main": [[{"node": "Generate HeyGen Video", "type": "main", "index": 0}]]}, "Get Available Voices": {"main": [[{"node": "Generate HeyGen Video", "type": "main", "index": 0}]]}, "Generate HeyGen Video": {"main": [[{"node": "Wait for Processing", "type": "main", "index": 0}]]}, "Wait for Processing": {"main": [[{"node": "Check Video Status", "type": "main", "index": 0}]]}, "Check Video Status": {"main": [[{"node": "If Video Ready", "type": "main", "index": 0}]]}, "If Video Ready": {"main": [[{"node": "Download Video", "type": "main", "index": 0}], [{"node": "Wait Before Retry", "type": "main", "index": 0}]]}, "Wait Before Retry": {"main": [[{"node": "Check Video Status", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Upload to Google Drive", "type": "main", "index": 0}, {"node": "Upload to YouTube", "type": "main", "index": 0}]]}, "Upload to Google Drive": {"main": [[{"node": "Log to Google Sheets", "type": "main", "index": 0}]]}, "Upload to YouTube": {"main": [[{"node": "Log to Google Sheets", "type": "main", "index": 0}]]}, "Log to Google Sheets": {"main": [[{"node": "Send Notification Email", "type": "main", "index": 0}, {"node": "Discord Notification", "type": "main", "index": 0}]]}, "OpenAI GPT-4o Model": {"ai_languageModel": [[{"node": "Content Topic Generator", "type": "ai_languageModel", "index": 0}, {"node": "Script Generator", "type": "ai_languageModel", "index": 0}]]}, "Topic Output Parser": {"ai_outputParser": [[{"node": "Content Topic Generator", "type": "ai_outputParser", "index": 0}]]}, "Script Output Parser": {"ai_outputParser": [[{"node": "Script Generator", "type": "ai_outputParser", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [{"createdAt": "2025-01-27T19:08:00.000Z", "updatedAt": "2025-01-27T19:08:00.000Z", "id": "youtube-automation-tag", "name": "YouTube Automation"}, {"createdAt": "2025-01-27T19:08:00.000Z", "updatedAt": "2025-01-27T19:08:00.000Z", "id": "heygen-ai-tag", "name": "HeyGen AI"}, {"createdAt": "2025-01-27T19:08:00.000Z", "updatedAt": "2025-01-27T19:08:00.000Z", "id": "content-automation-tag", "name": "Content Automation"}], "triggerCount": 1, "updatedAt": "2025-01-27T19:08:00.000Z", "versionId": "youtube-heygen-automation-v1", "id": "youtube-heygen-automation-workflow", "name": "End-to-End YouTube Video Automation with HeyGen & GPT-4"}