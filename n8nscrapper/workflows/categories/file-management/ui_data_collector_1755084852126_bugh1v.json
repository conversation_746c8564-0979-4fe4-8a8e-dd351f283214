{"name": "UI Data Collector", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9 * * *"}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [80, 200]}, {"parameters": {"content": "## UI Data Collection Workflow\n\nThis workflow collects UI images and metadata from:\n1. Slack channels\n2. Local file system\n3. Future: Web scraping via Playwright\n\nCollected data is stored in Supabase `external_ui_data` table."}, "id": "sticky-note", "name": "Workflow Info", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, 80]}, {"parameters": {"options": {}}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [80, 320]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "slack-condition", "leftValue": "{{ $json.source }}", "rightValue": "slack", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "route-switch", "name": "Route Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [300, 260]}, {"parameters": {"jsCode": "// Initialize data collection sources\nconst sources = [\n  {\n    source: 'slack',\n    enabled: true,\n    description: 'Collect from Slack channels'\n  },\n  {\n    source: 'filesystem',\n    enabled: true,\n    description: 'Collect from local folder'\n  },\n  {\n    source: 'playwright',\n    enabled: false,\n    description: 'Future: Web scraping'\n  }\n];\n\nreturn sources.map(item => ({ json: item }));"}, "id": "init-sources", "name": "Initialize Sources", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, 440]}, {"parameters": {"resource": "channel", "operation": "history", "channelId": "={{ $env.SLACK_CHANNEL_ID }}", "additionalFields": {"count": 50, "inclusive": true}}, "id": "slack-history", "name": "Get Slack Messages", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [520, 200], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API Credentials"}}}, {"parameters": {"jsCode": "// Filter messages with images and extract metadata\nconst messages = $input.all();\nconst imageMessages = [];\n\nfor (const message of messages) {\n  const msg = message.json;\n  \n  if (msg.files && msg.files.length > 0) {\n    for (const file of msg.files) {\n      if (file.mimetype && file.mimetype.startsWith('image/')) {\n        imageMessages.push({\n          id: `slack_${msg.ts}_${file.id}`,\n          title: file.title || file.name || 'Untitled',\n          description: msg.text || '',\n          image_url: file.url_private,\n          source: 'slack',\n          metadata: {\n            slack_ts: msg.ts,\n            slack_user: msg.user,\n            slack_channel: msg.channel,\n            file_id: file.id,\n            file_type: file.filetype,\n            file_size: file.size\n          },\n          created_at: new Date(parseFloat(msg.ts) * 1000).toISOString()\n        });\n      }\n    }\n  }\n}\n\nreturn imageMessages.map(item => ({ json: item }));"}, "id": "process-slack-images", "name": "Process Slack Images", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [740, 200]}, {"parameters": {"path": "={{ $env.UI_IMAGES_FOLDER_PATH }}", "options": {"recursive": true}}, "id": "read-files", "name": "Read Image Files", "type": "n8n-nodes-base.readBinaryFiles", "typeVersion": 1, "position": [520, 320]}, {"parameters": {"jsCode": "// Process local image files\nconst files = $input.all();\nconst processedFiles = [];\n\nfor (const file of files) {\n  const fileName = file.json.fileName;\n  const filePath = file.json.filePath;\n  \n  // Extract title from filename (remove extension)\n  const title = fileName.replace(/\\.[^/.]+$/, \"\");\n  \n  // Try to extract description from folder structure or filename\n  const pathParts = filePath.split('/');\n  const parentFolder = pathParts[pathParts.length - 2] || '';\n  const description = `UI image from ${parentFolder}`;\n  \n  processedFiles.push({\n    id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n    title: title,\n    description: description,\n    image_url: filePath,\n    source: 'filesystem',\n    metadata: {\n      file_path: filePath,\n      file_name: fileName,\n      parent_folder: parentFolder,\n      file_size: file.binary?.data?.length || 0\n    },\n    created_at: new Date().toISOString()\n  });\n}\n\nreturn processedFiles.map(item => ({ json: item }));"}, "id": "process-files", "name": "Process Local Files", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [740, 320]}, {"parameters": {"jsCode": "// Mock Playwright web scraping node\n// This is a placeholder for future implementation\n\nconst mockWebUIData = {\n  id: `web_${Date.now()}`,\n  title: 'Mock Web UI Screenshot',\n  description: 'This is a mock entry for future Playwright integration',\n  image_url: 'https://example.com/mock-screenshot.png',\n  source: 'playwright',\n  metadata: {\n    url: 'https://example.com',\n    viewport: '1920x1080',\n    timestamp: Date.now(),\n    mock: true\n  },\n  created_at: new Date().toISOString()\n};\n\nreturn [{ json: mockWebUIData }];"}, "id": "playwright-mock", "name": "Playwright <PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [520, 440]}, {"parameters": {}, "id": "merge-data", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [960, 260]}, {"parameters": {"url": "={{ $env.SUPABASE_URL }}/rest/v1/external_ui_data", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "supabase-insert", "name": "Insert to Supabase", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 260], "credentials": {"httpHeaderAuth": {"id": "supabase-auth", "name": "Supabase Auth"}}}, {"parameters": {"jsCode": "// Log results and create summary\nconst results = $input.all();\nconst successful = results.filter(r => r.json.status >= 200 && r.json.status < 300);\nconst failed = results.filter(r => r.json.status >= 400);\n\nconst summary = {\n  total_processed: results.length,\n  successful_inserts: successful.length,\n  failed_inserts: failed.length,\n  timestamp: new Date().toISOString(),\n  details: {\n    successful: successful.map(r => ({ id: r.json.id, status: r.json.status })),\n    failed: failed.map(r => ({ id: r.json.id, status: r.json.status, error: r.json.error }))\n  }\n};\n\nconsole.log('UI Data Collection Summary:', JSON.stringify(summary, null, 2));\n\nreturn [{ json: summary }];"}, "id": "log-results", "name": "Log Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1400, 260]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Initialize Sources", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Initialize Sources", "type": "main", "index": 0}]]}, "Initialize Sources": {"main": [[{"node": "Route Switch", "type": "main", "index": 0}]]}, "Route Switch": {"main": [[{"node": "Get Slack Messages", "type": "main", "index": 0}], [{"node": "Read Image Files", "type": "main", "index": 0}], [{"node": "Playwright <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Slack Messages": {"main": [[{"node": "Process Slack Images", "type": "main", "index": 0}]]}, "Process Slack Images": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Read Image Files": {"main": [[{"node": "Process Local Files", "type": "main", "index": 0}]]}, "Process Local Files": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Playwright Mock Node": {"main": [[{"node": "Merge All Data", "type": "main", "index": 2}]]}, "Merge All Data": {"main": [[{"node": "Insert to Supabase", "type": "main", "index": 0}]]}, "Insert to Supabase": {"main": [[{"node": "Log Results", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-07T00:00:00.000Z", "updatedAt": "2025-01-07T00:00:00.000Z", "id": "ui-collector", "name": "UI Data Collection"}], "triggerCount": 1, "updatedAt": "2025-01-07T00:00:00.000Z", "versionId": "1"}