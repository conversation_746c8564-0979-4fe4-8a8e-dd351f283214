{"name": "IG_SpeechTo3DModel", "nodes": [{"parameters": {"model": "gpt-4o", "options": {"frequencyPenalty": 0.2, "temperature": 0.7}}, "id": "********-632a-403e-a92a-92d3e20520e1", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [800, 320], "typeVersion": 1, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "id": "0e389911-5679-4b24-8df6-b20f20df13de", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [940, 360], "typeVersion": 1}, {"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "8f7f13af-0c83-4ced-99ac-e6dc5cd5e17f", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-280, 180], "webhookId": "d63cdf55-1ec9-4688-8a40-023fc3239630", "typeVersion": 1, "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "79bf0970-80da-452f-b7be-d93ddf3d96ef", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [240, 300], "typeVersion": 1.2, "webhookId": "9a5c2041-43e7-4a12-a1ed-8ee05fdc2dcb", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}, "options": {}}, "id": "eb894ed7-a5c5-44ec-8869-829f65c4277b", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [620, 160], "typeVersion": 3.4}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "f32915d2-5af1-4978-82a2-ad2eda5c4600", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [240, 0], "typeVersion": 1.2, "webhookId": "814a2a70-f9a1-4c51-bc5b-23c8e477b0b3", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "", "temperature": 0.7}}, "id": "c4c92d73-7704-4055-9e61-596d3687bd28", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [420, 300], "typeVersion": 1.5, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "53d53a38-a0d1-4651-953a-aa30c6494747", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [0, 0], "typeVersion": 1.2, "webhookId": "08fc01ff-6c77-4099-87f9-a362a5673524", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/", "id": "d68f5557-eaf3-430c-b3e0-4c0bbb147c45"}]}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "a5e99f0a-e78e-4149-9c67-92e7ddb59ee8", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [0, 180], "typeVersion": 3.2}, {"parameters": {"promptType": "define", "text": "=Request: {{ $json.CombinedMessage }}\n\nGenerate a prompt for imagegen to render it as a 3d model on a white background.\n\n\n\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [820, 160], "id": "4766ac69-9f67-40b9-94bf-b29d2f7093f8", "name": "AI Agent1"}, {"parameters": {"method": "POST", "url": "https://api.replicate.com/v1/predictions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Prefer", "value": "wait"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"version\": \"e8f6c45206993f297372f5436b90350817bd9b4a0d52d2a76df50c1c8afa2b3c\",\n  \"input\": {\n    \"images\": [{{ (\"data:image/png;base64,\"+$('HTTP Request1').item.json.data[0].b64_json).toJsonString() }}],\n    \"texture_size\": 2048,\n    \"mesh_simplify\": 0.9,\n    \"generate_model\": true,\n    \"save_gaussian_ply\": true,\n    \"ss_sampling_steps\": 38\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 160], "id": "85b8bb3a-a707-4590-87e3-20d5c80816fb", "name": "HTTP Request", "credentials": {"httpBearerAuth": {"id": "TqeLW6MJrJpRngX4", "name": "Bear<PERSON> account 2"}, "httpHeaderAuth": {"id": "fQPv6vsDcl7pU5DU", "name": "Head<PERSON> Au<PERSON> account 2"}}}, {"parameters": {"content": "### Now send it to e-mail, telegram, etc"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1800, -40], "id": "3a365699-8fd5-44a7-92fe-51ea6ef93003", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "### Get the API-Key here: https://replicate.com/", "height": 80, "width": 200}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1140, 0], "id": "0c9a0d97-1072-434f-aad3-cc604245759c", "name": "Sticky Note1"}, {"parameters": {"chatId": "={{ $('Listen for incoming events').item.json.message.chat.id }}", "text": "=Get your 3D-model here: {{ $json.urls.web }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1860, 160], "id": "cffb9e8f-e85a-4a62-90ac-a3fa8628c00e", "name": "Telegram", "webhookId": "af3d2e5c-6e31-4881-b715-dc2f3c9db5d2", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}, {"name": "moderation", "value": "low"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 160], "id": "570b4778-b89e-42d4-88cc-c3873ab97dc9", "name": "HTTP Request1", "credentials": {"httpHeaderAuth": {"id": "VpbhAqVyynzzb44F", "name": "OpenAI"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1420, 160], "id": "957e0ec1-4d7f-4c01-a497-ca72ab7c0ac8", "name": "Convert to File"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e67e379c-e16b-472e-a191-c00184b40dbe", "meta": {"templateCredsSetupCompleted": true, "instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "SGRErPu01nliroPn", "tags": []}