{"name": "gemini marketing", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-760, -140], "id": "83f6fb81-2ab8-40af-934e-332fbf4d9e3e", "name": "When chat message received", "webhookId": "edd7008f-f919-4b69-8de8-98417ab8c317"}, {"parameters": {"hasOutputParser": true, "options": {"systemMessage": "=You are a world-class advertising visual designer and AI image prompt engineer, specializing in ultra-realistic, cinematic product advertisements.\n\nYour job is to take a product name and flavor from the user, retrieve its branding details using the SerpAPI tool, and generate a **ready-to-use, commercial-grade image generation prompt**. This will be used by a backend image engine to create premium advertisement visuals.\n\n---\n\n## TASK FLOW:\n\n1. When a user gives a product name or brand (e.g., \"Pepsi Mango Zero\"):\n   - First, use the **SerpAPI tool** to search for the product on the web (Google/Wikipedia/Brand Pages).\n   - Extract any available data about:\n     - **Can or bottle size**\n     - **Brand color palette**\n     - **Logo style**\n     - **Packaging shape** (slim can, stubby bottle, etc.)\n     - **Flavor details**\n     - **Popular slogans (if any)**\n\n2. Then use this data to compose a **cinematic visual scene prompt** for image generation.\n\n---\n\n## STRUCTURE YOUR OUTPUT USING THIS FORMAT:\n\n- **Scene**: Center the product (can or bottle) in a confident, static hero-shot style.\n- **Style**: Ultra-realistic, studio-lit, cinematic gradients and crisp shadowing.\n- **Visual Elements**: Floating fruit slices, splashes of juice, droplets on the can, branded background gradient.\n- **Lighting**: Three-point product lighting with gloss and high contrast.\n- **Mood**: Bold, premium, energetic, ad-grade.\n- **Text Overlay**:\n  - **Text**: Include a short, powerful marketing slogan. If not available, create one based on flavor or product style (e.g., \"ZERO LEMON\", \"REFRESH YOUR WORLD\").\n  - **Text Position**: Placed behind or beneath the product, center-aligned like billboard typography.\n  - **Text Style**: ALL CAPS, bold sans-serif font (e.g., Bebas Neue or Helvetica Neue), pure white or brand color.\n  - **Integration**: Crisp and clear, no blur. Looks like a real ad campaign.\n\n---\n\n## INPUT VARIABLES:\n\n- **Product name** and any descriptors are passed via: `{{ $json.chatInput }}`\n- Example: “Sprite Zero Lemon” or “Red Bull Coconut Edition”\n\nIf any detail (size, color, flavor) is not found via SerpAPI, use these defaults:\n- **Size**: 330ml\n- **Shape**: Slim can\n- **Mood**: Energetic, fresh, bold\n\n---\n\n## RULES:\n\n- Always use SerpAPI first to gather real branding data.\n- Do not include explanations or extra narration — return only the **final visual prompt**.\n- Format the output as a **complete, vivid ad scene description** for image generation.\n\n---\n\n## OUTPUT EXAMPLE:\n\n\"A 330ml Coca-Cola Zero Sugar Lemon can stands tall in the center of a sunlit yellow-to-white gradient background. The can is cold, with realistic water droplets covering its surface. Around the base, floating lemon wedges and ice shards splash through misted juice. The scene is studio-lit with cinematic highlights and soft shadows.\n\n**Overlay Text**: 'ZERO LEMON' is positioned boldly behind the can in all-caps white font. The words are perfectly aligned in two rows with billboard precision, making the can pop forward like a real-life ad.\"\n\n---\n\nBegin generating based on:  \n**Input** → `{{ $json.chatInput }}`\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-420, -140], "id": "********-e439-4589-b7c2-723842c5e29f", "name": "AI Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-05-20", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-700, 260], "id": "e1ac9884-8032-4ca8-b952-1bf94db60ea2", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "XQGM7Xn4IVn2oJq1", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"contextWindowLength": 100}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-560, 260], "id": "45f58b22-7558-4b8b-8b6a-3adf1909faa2", "name": "Simple Memory", "disabled": true}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-preview-image-generation:generateContent?key={your_gemini_api_key}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"{{ $json.cleanedPrompt }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"responseModalities\": [\n      \"TEXT\",\n      \"IMAGE\"\n    ]\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [280, -140], "id": "89b23ff4-3d4c-4685-9fd1-788d10a384b6", "name": "HTTP Request"}, {"parameters": {"jsCode": "return items.map(item => {\n  const response = item.json;\n\n  const imageData = response?.candidates?.[0]?.content?.parts?.find(part => part.inlineData)?.inlineData?.data;\n\n  if (!imageData) {\n    console.warn(\"Image data not found in response.\");\n    return {\n      json: {\n        error: \"No image data found\"\n      }\n    };\n  }\n\n  return {\n    json: {\n      base64Image: imageData\n    }\n  };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [520, -140], "id": "84fe2ffe-a48e-44b8-9cd8-58bca4e0f566", "name": "Code"}, {"parameters": {"jsCode": "// Get the original string from AI output\nconst original = $input.first().json.output;\n\n// Clean the string\nconst cleaned = original\n  .replace(/\\n/g, ' ')               // Replace newline characters with space\n  .replace(/\\*\\*/g, '')             // Remove bold markdown (**)\n  .replace(/[\"']/g, '')             // Remove quotes if needed\n  .replace(/Overlay Text:.*?Text Style:.*?$/i, '')  // Remove the overlay text part if unwanted\n  .trim();\n\n// Return cleaned prompt\nreturn [\n  {\n    json: {\n      cleanedPrompt: cleaned\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, -140], "id": "7e52371e-7461-4860-b0e0-44fd99855733", "name": "Code1"}, {"parameters": {"operation": "toBinary", "sourceProperty": "base64Image", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [720, -140], "id": "066b1fa1-1b6c-402f-b251-9ab2db9c2c43", "name": "Convert to File"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.toolSerpApi", "typeVersion": 1, "position": [-260, 240], "id": "736add77-6f94-4036-ac6c-7c05bcc3f87a", "name": "SerpAPI", "credentials": {"serpApi": {"id": "vhCb4DXdIuqnpcfS", "name": "bunny"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolWikipedia", "typeVersion": 1, "position": [-400, 220], "id": "01160ab7-6a48-4b8e-8e63-dbce5c5d96e1", "name": "Wikipedia"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "SerpAPI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Wikipedia": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "448e636c-5cf3-42ff-9591-5a4891682ec2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ef0684572e943334142319bb3eefb7f7c5d387d2225dc92cab9be5c15041389a"}, "id": "GFKlUi08BV1JyKzO", "tags": []}