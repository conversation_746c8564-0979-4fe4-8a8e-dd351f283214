{"name": "WhatsApp Social Poster (Community)", "nodes": [{"parameters": {"model": "gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [2020, 560], "id": "5d4985ef-8822-4725-b50c-49cbbf10acdf", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "p5ZcbXN4HeheAqoB", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "chat key", "contextWindowLength": 50}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [2200, 600], "id": "5613a06f-0051-43ac-9c58-62b52cb339a9", "name": "Window Buffer Memory"}, {"parameters": {"updates": ["messages"], "options": {}}, "type": "n8n-nodes-base.whatsAppTrigger", "typeVersion": 1, "position": [120, 300], "id": "9648aa5b-96cb-471f-97c8-661ffa79dc97", "name": "<PERSON><PERSON><PERSON><PERSON>", "webhookId": "a19a9f0e-e030-4254-95d3-bc69d667a602", "credentials": {"whatsAppTriggerApi": {"id": "PPWEjSRinUYG8tg8", "name": "WhatsApp501BLOG"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.messages[0].type }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1a52b406-1df9-4c47-85c7-1de7e4c5365e", "leftValue": "={{ $json.messages[0].type }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b982f2f8-9f79-4303-821b-4d82117edf09", "leftValue": "={{ $json.messages[0].type }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [600, 180], "id": "6c3f969e-3845-4aa0-b9ae-6cd0286ab3c6", "name": "Switch"}, {"parameters": {"url": "={{ $json.url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "curl/7.64.1"}, {"name": "Authorization", "value": "Bearer [your token]"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1100, 280], "id": "c1f37cb7-7109-4ad3-9e9f-4091f23598bb", "name": "Download"}, {"parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $json.messages[0].audio.id }}"}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [880, 280], "id": "7d45b2cb-6080-4c29-b58d-851b3e68fa4a", "name": "Get File", "webhookId": "36bbf2f8-735b-4037-ab6c-9d22e6297166", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"url": "={{ $json.url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "curl/7.64.1"}, {"name": "Authorization", "value": "Bearer [your token]"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [820, 460], "id": "********-5e87-4a27-965b-2460d97c3792", "name": "Download1"}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1340, 280], "id": "5d516c66-db1b-40a2-a342-0156ecfef4b3", "name": "OpenAI", "credentials": {"openAiApi": {"id": "p5ZcbXN4HeheAqoB", "name": "OpenAi account"}}}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "text": "=What's in this image?\n\nIMPORTANT: Also, you MUST including the following URL in your output.\n\nhttps://ucarecdn.com/{{ $('Upload to Temp1').item.json['{filename}'] }}/", "inputType": "base64", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1540, 460], "id": "c857d9fd-f706-4c6d-b2be-a69a235448ee", "name": "OpenAI1", "credentials": {"openAiApi": {"id": "p5ZcbXN4HeheAqoB", "name": "OpenAi account"}}}, {"parameters": {"resource": "media", "operation": "mediaUrlGet", "mediaGetId": "={{ $json.messages[0].image.id }}"}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [600, 460], "id": "f639b4dc-d862-41dc-a41e-************", "name": "Get File1", "webhookId": "97fe22d3-a8aa-4913-8828-44f5e4528158", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"assignments": {"assignments": [{"id": "c0fb56f9-3f37-4b79-a782-39bbd8030bb8", "name": "userMessage", "value": "={{ $json.text || $json.content || $json.body?.message || $('WhatsApp Trigger').item.json.messages?.[0]?.text?.body }}", "type": "string"}, {"id": "48a0a0d8-82e8-4bef-838d-11e98d60a56a", "name": "modal", "value": "={{ \n\n$json.text \n  ? \"audio\"\n  : ( $('WhatsApp Trigger').item.json.messages[0].type\n    ? \"image\"\n    : \"text\")\n\n}}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1760, 160], "id": "76e2ee18-6bf8-423b-9dba-3b801a586be0", "name": "Set Message"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "5bc3adc6-54fd-41cd-82af-0ce66a0eb75b", "leftValue": "={{ $json.messages }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [340, 300], "id": "b1d457ac-e0e3-4b4d-a5bd-92188a268a1d", "name": "Filter Whatsapp Events"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "39c1f3c0-0e12-402a-acbf-413efe979bc1", "leftValue": "={{ $('Set Message').item.json.modal }}", "rightValue": "audio", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3060, 180], "id": "2de64e2d-b375-4706-8a69-459a34730719", "name": "If2"}, {"parameters": {"resource": "media", "phoneNumberId": "***************", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [3440, 380], "id": "6f3e85ae-7250-4f0b-afe4-b865dfd1b909", "name": "Upload Audio", "webhookId": "17b6fcda-a46c-4d95-8a9e-2c1bbd1ffeac", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"resource": "audio", "model": "tts-1-hd", "input": "={{ $('AI Agent').item.json.output }}", "options": {"response_format": "aac"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [3220, 380], "id": "09a6d993-ca67-43f9-82f4-3132438a7579", "name": "Generate Audio", "credentials": {"openAiApi": {"id": "p5ZcbXN4HeheAqoB", "name": "OpenAi account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "messageType": "audio", "mediaPath": "useMediaId", "mediaId": "={{ $json.id }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [3780, 500], "id": "880e32cd-16b7-4ab3-9e88-1ae7f2f839ea", "name": "Send Audio", "webhookId": "85a71021-f80d-4cf9-91e0-c7cefa4357a8", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "={{ $('AI Agent').item.json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [3540, 180], "id": "20a36609-1bfc-4b3b-84d2-67c7a5418ce4", "name": "Send Message", "webhookId": "70e1ac9f-d19a-4380-8c36-b5956936b527", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "={{ $('AI Agent').item.json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [3960, 460], "id": "e5f428d0-6225-4cfe-a881-822bf3314b43", "name": "Send Text Message", "webhookId": "556e428c-21bb-43a2-b1d8-85cfa0643c01", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "You will be provided a user message on whatsapp. Analyze and extract any image urls. \n\nOutput the image urls in JSON in the following format.\n\n{\n\"imageUrls\": [\n{\"image\": https://www....\"},\n{\"image\": https://www....\"}\n]\n}\n\nIf there are no image urls, then output an empty array", "role": "system"}, {"content": "={{ $('AI Agent').item.json.output }}"}]}, "jsonOutput": true, "options": {"temperature": 0.2}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [4160, 160], "id": "fd915024-7204-43b9-9715-dd476cac4fb3", "name": "Extract URLs", "credentials": {"openAiApi": {"id": "p5ZcbXN4HeheAqoB", "name": "OpenAi account"}}}, {"parameters": {"fieldToSplitOut": "message.content.imageUrls", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [4260, 400], "id": "016c10bb-6e59-45ff-8760-2c689f94c5fe", "name": "Split Out"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "array", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [4640, 360], "id": "010fb484-72dd-41b4-bf70-70674bf8516d", "name": "Aggregate"}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "messageType": "image", "mediaLink": "={{ $json.image }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [4460, 500], "id": "9d1bc5a3-abc0-4625-967b-697e606cf8eb", "name": "Send Image", "webhookId": "c0c6944d-d7f3-4a84-9809-3b3fbe170557", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"options": {"timezone": "Europe/Dublin"}}, "type": "n8n-nodes-base.dateTime", "typeVersion": 2, "position": [1980, 200], "id": "37cdd00f-da1e-4d78-8b63-9b51b4e536e3", "name": "Get Date & Time"}, {"parameters": {"method": "POST", "url": "https://upload.uploadcare.com/base/", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "{filename}", "inputDataFieldName": "=data"}, {"name": "UPLOADCARE_PUB_KEY"}, {"name": "UPLOADCARE_STORE", "value": "0"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1060, 460], "id": "b20be0e9-0871-4350-b15f-9d7f647c8986", "name": "Upload to Temp1"}, {"parameters": {"promptType": "define", "text": "={{ $('Set Message').item.json.userMessage }}", "options": {"systemMessage": "=<INSERT SYSTEM MESSAGE>\n\nIf you are asked to generate an image/images, then output in markdown\n\nExample Output\n![Alt text describing the image](https://example.com/image.jpg)\n\nCurrent Date & Time:  {{ $json.currentDate }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [2560, 240], "id": "c62439b7-492d-41e5-8be6-517393213180", "name": "AI Agent"}, {"parameters": {"jsCode": "const output = $input.first().json.output;\nreturn { output: output };\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [4760, 180], "id": "4173258a-202f-4e9b-b433-0d5a3b93e426", "name": "Finish", "alwaysOutputData": true}, {"parameters": {"url": "={{ $('Get File1').item.json.url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "curl/7.64.1"}, {"name": "Authorization", "value": "Bearer [your token]"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 480], "id": "05009598-12c1-4aca-9d46-e2af00b60742", "name": "Download3"}, {"parameters": {"toolDescription": "This tool allows you to query the DataForSEO platform to fetch Google Search results for a specified query", "method": "POST", "url": "https://api.dataforseo.com/v3/serp/google/{type}/live/advanced", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "[{\"keyword\":\"{search_term}\", \"location_code\":2372, \"language_code\":\"en\", \"device\":\"desktop\", \"os\":\"windows\", \"depth\":10}]", "placeholderDefinitions": {"values": [{"name": "search_term", "description": "This is the search term to pass into Google Search. Only list the topic to be searched|EXAMPLE USER QUERY: latest LLM newsEXAMPLE OUTPUT: LLM", "type": "string"}, {"name": "type", "description": "This must be \"organic\" or \"news\""}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2380, 580], "id": "e749a475-6867-4e14-a3ff-3b6c4371e6bb", "name": "Google Search and News via DataForSEO", "credentials": {"httpHeaderAuth": {"id": "3Um6D9X0sM08HcjY", "name": "Perplexity"}}}, {"parameters": {"toolDescription": "Fetch Webpage Markdown via Jina AI. This is useful for getting details of a webpage found from the Google Search tools", "url": "https://r.jina.ai/{url}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "placeholderDefinitions": {"values": [{"name": "url", "description": "This is the url to be accessed via <PERSON><PERSON> to extract the pages markdown"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2520, 580], "id": "bd1a5c9b-8e08-49b0-8c2a-f9c73cd93853", "name": "<PERSON><PERSON> Markdown via Jina AI", "credentials": {"httpHeaderAuth": {"id": "3Um6D9X0sM08HcjY", "name": "Perplexity"}}}, {"parameters": {"toolDescription": "Call this tool to fetch a stock image. The input should be a search term (keep it concise)", "url": "https://pixabay.com/api/", "sendQuery": true, "parametersQuery": {"values": [{"name": "key", "valueProvider": "fieldValue", "value": "api key"}, {"name": "q", "valueProvider": "fieldValue", "value": "{search_term}"}, {"name": "per_page", "valueProvider": "fieldValue", "value": "5"}, {"name": "image_type", "valueProvider": "fieldValue", "value": "photo"}]}, "placeholderDefinitions": {"values": [{"name": "search_term", "description": "search term to reflect the users query", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2640, 580], "id": "3211b573-90af-4716-9b94-bb6897e1e11d", "name": "Fetch Stock Image"}, {"parameters": {"toolDescription": "This tool allows an agent to generate an AI Image with Flux 1.1 Pro Ultra", "method": "POST", "url": "https://fal.run/fal-ai/flux-pro", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "Content-Type", "valueProvider": "fieldValue", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"prompt\": \"{image_prompt}\",\n  \"num_images\": 2,\n  \"enable_safety_checker\": false,\n  \"safety_tolerance\": \"1\",\n    \"aspect_ratio\": \"{aspect_ratio}\"\n}", "placeholderDefinitions": {"values": [{"name": "image_prompt", "description": "Populate this with 16:9, 9:16 or 1:1 depending on the context and the social platform being posted to", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2780, 620], "id": "f851cb0b-cccb-4bb9-9857-4036bd6bee11", "name": "Generate AI Image", "credentials": {"httpHeaderAuth": {"id": "ZOaX35w9G540WB0k", "name": "Fal AI 1"}}}, {"parameters": {"toolDescription": "Request Publication of a Social Media Post", "method": "POST", "url": "[request publication response url]", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "parametersBody": {"values": [{"name": "request_type"}, {"name": "post_type"}, {"name": "text_message"}, {"name": "image_url"}, {"name": "chat_id", "valueProvider": "fieldValue", "value": "={{ $('Edit Fields').item.json.sessionId }}"}]}, "placeholderDefinitions": {"values": [{"name": "request_type", "description": "Must be one of 'Facebook, Twitter, LinkedIn, Instagram'", "type": "string"}, {"name": "post_type", "description": "Must be 'Text' or 'Image and Text'", "type": "string"}, {"name": "text_message", "description": "This is the text to post on the social platform", "type": "string"}, {"name": "image_url", "description": "If an image is being posted, the URL will go here", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [2940, 600], "id": "4ba5cabd-8221-4d25-b627-7a0d0d51df00", "name": "Request Posting to Social", "credentials": {"httpHeaderAuth": {"id": "Dy8YPyFG7SOMTqZp", "name": "Head<PERSON> Auth account 3"}}}, {"parameters": {"assignments": {"assignments": [{"id": "dab60581-da73-4453-82c1-e77740199f94", "name": "sessionId", "value": "={{ $('Switch').item.json.contacts[0].wa_id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2260, 200], "id": "713be1a7-86d6-461d-9fb6-8ee6fc1dc678", "name": "<PERSON>"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Filter Whatsapp Events", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Set Message", "type": "main", "index": 0}], [{"node": "Get File", "type": "main", "index": 0}], [{"node": "Get File1", "type": "main", "index": 0}]]}, "Download": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Get File": {"main": [[{"node": "Download", "type": "main", "index": 0}]]}, "Download1": {"main": [[{"node": "Upload to Temp1", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Set Message", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "Set Message", "type": "main", "index": 0}]]}, "Get File1": {"main": [[{"node": "Download1", "type": "main", "index": 0}]]}, "Filter Whatsapp Events": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Send Message", "type": "main", "index": 0}], [{"node": "Generate Audio", "type": "main", "index": 0}]]}, "Upload Audio": {"main": [[{"node": "Send Audio", "type": "main", "index": 0}]]}, "Generate Audio": {"main": [[{"node": "Upload Audio", "type": "main", "index": 0}]]}, "Send Audio": {"main": [[{"node": "Send Text Message", "type": "main", "index": 0}]]}, "Send Message": {"main": [[{"node": "Extract URLs", "type": "main", "index": 0}]]}, "Send Text Message": {"main": [[{"node": "Extract URLs", "type": "main", "index": 0}]]}, "Extract URLs": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Send Image", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Finish", "type": "main", "index": 0}]]}, "Send Image": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Set Message": {"main": [[{"node": "Get Date & Time", "type": "main", "index": 0}]]}, "Get Date & Time": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Upload to Temp1": {"main": [[{"node": "Download3", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "Download3": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "Google Search and News via DataForSEO": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Fetch Markdown via Jina AI": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Fetch Stock Image": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Generate AI Image": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Request Posting to Social": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "357ea575-3810-4e96-9969-d4ac58ae5213", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a8fafcb279d93b3f223424ce87564d8ff1d6d229f3169c2fb88370c4a5f6eb0e"}, "id": "RgDxada5yW0MyfSZ", "tags": []}