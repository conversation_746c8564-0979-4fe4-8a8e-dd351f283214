{"meta": {"instanceId": "ai-video-workflow-001"}, "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 2}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [140, 300]}, {"parameters": {"resource": "chat", "operation": "create", "chatInput": {"messages": {"values": [{"role": "system", "content": "You are a creative AI video content generator. Generate trending video concepts that would perform well on TikTok, Instagram Reels, and YouTube Shorts. Focus on viral content ideas that are visually engaging and shareable."}, {"role": "user", "content": "Generate 3 unique, creative video concepts for trending AI videos. Each concept should include: 1) A catchy title 2) Visual description 3) Target audience 4) Estimated viral potential (1-10). Format as structured JSON."}]}}, "options": {"temperature": 0.8, "maxTokens": 1000}}, "id": "openai-ideas-generator", "name": "OpenAI Ideas Generator", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [360, 300]}, {"parameters": {"schemaType": "object", "jsonSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"video_concepts\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"title\": {\"type\": \"string\"},\n          \"description\": {\"type\": \"string\"},\n          \"target_audience\": {\"type\": \"string\"},\n          \"viral_potential\": {\"type\": \"number\"},\n          \"visual_prompt\": {\"type\": \"string\"}\n        }\n      }\n    }\n  }\n}"}, "id": "structured-output-parser", "name": "Structured Output Parser", "type": "n8n-nodes-base.structuredOutputParser", "typeVersion": 1, "position": [580, 300]}, {"parameters": {"authentication": "oAuth2", "operation": "appendRow", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEET_ID", "mode": "id"}, "sheetName": {"__rl": true, "value": "Video_Ideas", "mode": "name"}, "columns": {"mappingMode": "defineBelow", "value": {"Title": "={{ $json.title }}", "Description": "={{ $json.description }}", "Target_Audience": "={{ $json.target_audience }}", "Viral_Potential": "={{ $json.viral_potential }}", "Visual_Prompt": "={{ $json.visual_prompt }}", "Status": "Generated", "Timestamp": "={{ $now.format('yyyy-MM-dd HH:mm:ss') }}"}}, "options": {}}, "id": "ideas-to-sheets", "name": "Ideas To Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [800, 300]}, {"parameters": {"resource": "chat", "operation": "create", "chatInput": {"messages": {"values": [{"role": "system", "content": "You are an expert video prompt engineer specializing in AI video generation. Create detailed, cinematic prompts that will produce stunning, viral-worthy videos using AI video generation tools like Veo 3 and similar platforms."}, {"role": "user", "content": "Transform this video concept into a detailed video generation prompt: {{ $json.visual_prompt }}. The prompt should be highly descriptive, include camera angles, lighting, mood, and specific visual elements. Make it optimized for AI video generation APIs."}]}}, "options": {"temperature": 0.7, "maxTokens": 500}}, "id": "ai-prompt-agent", "name": "AI Prompt Agent", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [1020, 300]}, {"parameters": {"method": "POST", "url": "https://api.fal.ai/v1/veo-3/generate", "authentication": "predefinedCredentialType", "nodeCredentialType": "falApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.choices[0].message.content }}"}, {"name": "duration", "value": "15"}, {"name": "aspect_ratio", "value": "9:16"}, {"name": "quality", "value": "hd"}]}, "options": {"timeout": 300000}}, "id": "http-video-request", "name": "HTTP Video Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1240, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-node", "name": "Wait", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1460, 300]}, {"parameters": {"method": "GET", "url": "={{ $('HTTP Video Request').item.json.status_url }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "falApi", "options": {"timeout": 30000}}, "id": "http-status-check", "name": "HTTP Status Check", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "video-ready-condition", "leftValue": "={{ $json.status }}", "rightValue": "completed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "video-ready-check", "name": "Video Ready Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1900, 300]}, {"parameters": {"authentication": "oAuth2", "operation": "updateRow", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEET_ID", "mode": "id"}, "sheetName": {"__rl": true, "value": "Video_Ideas", "mode": "name"}, "columnToMatchOn": "Title", "valueToMatchOn": "={{ $('Ideas To Sheets').item.json.Title }}", "columns": {"mappingMode": "defineBelow", "value": {"Video_URL": "={{ $json.video_url }}", "Status": "Video_Generated", "Generated_At": "={{ $now.format('yyyy-MM-dd HH:mm:ss') }}", "File_Size": "={{ $json.file_size || 'N/A' }}", "Duration": "={{ $json.duration || '15s' }}"}}, "options": {}}, "id": "update-sheets-with-video", "name": "Update Sheets With Video", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.4, "position": [2120, 300]}, {"parameters": {"sendTo": "<EMAIL>", "subject": "🎬 New AI Video Generated Successfully!", "emailFormat": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        .container { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }\n        .content { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 20px; }\n        .video-info { background: white; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #667eea; }\n        .btn { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🎬 AI Video Generated!</h1>\n            <p>Your automated video creation system has produced a new viral-ready video</p>\n        </div>\n        \n        <div class=\"content\">\n            <div class=\"video-info\">\n                <h3>📝 Video Details:</h3>\n                <p><strong>Title:</strong> {{ $('Ideas To Sheets').item.json.Title }}</p>\n                <p><strong>Description:</strong> {{ $('Ideas To Sheets').item.json.Description }}</p>\n                <p><strong>Target Audience:</strong> {{ $('Ideas To Sheets').item.json.Target_Audience }}</p>\n                <p><strong>Viral Potential:</strong> {{ $('Ideas To Sheets').item.json.Viral_Potential }}/10</p>\n            </div>\n            \n            <div class=\"video-info\">\n                <h3>🎥 Generated Video:</h3>\n                <p><strong>Video URL:</strong> <a href=\"{{ $json.video_url }}\">Download Video</a></p>\n                <p><strong>Status:</strong> ✅ Ready for Publishing</p>\n                <p><strong>Generated:</strong> {{ $now.format('yyyy-MM-dd HH:mm:ss') }}</p>\n            </div>\n            \n            <div style=\"text-align: center; margin-top: 30px;\">\n                <a href=\"{{ $json.video_url }}\" class=\"btn\">🎬 Download Video</a>\n                <a href=\"https://sheets.google.com\" class=\"btn\">📊 View Full Report</a>\n            </div>\n        </div>\n        \n        <div style=\"text-align: center; margin-top: 20px; color: #666;\">\n            <p>🤖 Generated by your AI Video Creation System</p>\n            <p>Ready for TikTok, Instagram Reels & YouTube Shorts!</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "email-notification", "name": "Email Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [2340, 300]}, {"parameters": {"amount": 60, "unit": "seconds"}, "id": "wait-retry", "name": "Wait Retry", "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1680, 500]}], "connections": {"Schedule Trigger": {"main": [[{"node": "OpenAI Ideas Generator", "type": "main", "index": 0}]]}, "OpenAI Ideas Generator": {"main": [[{"node": "Structured Output Parser", "type": "main", "index": 0}]]}, "Structured Output Parser": {"main": [[{"node": "Ideas To Sheets", "type": "main", "index": 0}]]}, "Ideas To Sheets": {"main": [[{"node": "AI Prompt Agent", "type": "main", "index": 0}]]}, "AI Prompt Agent": {"main": [[{"node": "HTTP Video Request", "type": "main", "index": 0}]]}, "HTTP Video Request": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "HTTP Status Check", "type": "main", "index": 0}]]}, "HTTP Status Check": {"main": [[{"node": "Video Ready Check", "type": "main", "index": 0}]]}, "Video Ready Check": {"main": [[{"node": "Update Sheets With Video", "type": "main", "index": 0}], [{"node": "Wait Retry", "type": "main", "index": 0}]]}, "Update Sheets With Video": {"main": [[{"node": "Email Notification", "type": "main", "index": 0}]]}, "Wait Retry": {"main": [[{"node": "HTTP Status Check", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "ai-video-automation", "name": "AI Video Automation"}], "triggerCount": 1, "updatedAt": "2025-01-01T00:00:00.000Z", "versionId": "ai-video-workflow-v1"}