{"id": "fqaNojXWrspqjfkY", "meta": {"instanceId": "69133932b9ba8e1ef14816d0b63297bb44feb97c19f759b5d153ff6b0c59e18d"}, "name": "RAG Workflow For Stock Earnings Report Analysis", "tags": [], "nodes": [{"id": "1a621f76-9636-430d-94dd-d5e7dcd5afdc", "name": "Pinecone Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [380, -60], "parameters": {"mode": "insert", "options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "company-earnings", "cachedResultName": "company-earnings"}}, "credentials": {"pineconeApi": {"id": "bQTNry52ypGLqt47", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "e5936e45-0f58-48e9-9ab4-cc69f2ef6578", "name": "Embeddings Google Gemini", "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "position": [300, 220], "parameters": {"modelName": "models/text-embedding-004"}, "credentials": {"googlePalmApi": {"id": "jLOqyTR4yTT1nYKi", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "e98dbc8e-6b4a-415d-a044-85e590fcb105", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [520, 200], "parameters": {"loader": "pdf<PERSON><PERSON>der", "options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "ae77f5f4-3704-4b66-9c3f-27d6bd3f68c3", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [560, 380], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "d939c9db-0edc-4205-b8e5-fb34b0076510", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-120, -60], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "4f8421b4-1a11-4ac3-a9ca-1d725a8ec98e", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-360, 640], "parameters": {}, "typeVersion": 1}, {"id": "c9e2ec39-c34d-4d8e-b772-d1c1cd823d9e", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-40, 640], "parameters": {"text": "Give me a report on Google's last 3 quarter earnings. Format it in markdown. Focus on the differences and trends. Spot any outliers.", "options": {"systemMessage": "You are a highly skilled financial analyst specializing in analyzing Google's (Alphabet Inc.) financial performance. You have access to two powerful tools:\n\n1. **Vector Store Tool:** This tool allows you to retrieve relevant information from the past three quarters of Google's earnings reports (PDF documents). The documents have been processed and stored as embeddings in a vector database, enabling semantic search. Use this tool to find specific information related to revenue, expenses, profits, losses, growth, key metrics, management commentary, and any other relevant financial data.\n2. **Google Docs Tool:** This tool allows you to create, edit, and format Google Docs. Use this tool to save your findings into a Google Doc.\n\nYour task is to answer user queries related to Google's financial performance based on the last three quarters' earnings reports. When a user asks a question:\n\n1. **Understand the User's Intent:** Carefully analyze the user's query to determine what specific financial information they are seeking. Identify keywords, timeframes (e.g., \"previous quarter\"), and the type of analysis requested (e.g., trend analysis, comparison, explanation).\n2. **Retrieve Relevant Information:** Use the Vector Store Tool to search for and retrieve the most relevant text passages from the earnings reports that address the user's query. Retrieve multiple, diverse chunks to ensure comprehensive coverage.\n3. **Synthesize and Analyze:** Analyze the information from the retrieved text chunks. Identify key trends, patterns, and insights related to the user's query.\n4. **Generate Report in Google Docs:** Use the Google Docs Tool to create a new Google Doc (or append to an existing one, if specified by the user). Structure the report with clear headings, bullet points, and concise paragraphs. Include the following in your report as appropriate:\n * **Executive Summary:** A brief overview of the key findings.\n * **Revenue Analysis:** Report on revenue figures, growth rates, and key revenue drivers.\n * **Expense Analysis:** Report on major expense categories and their impact on profitability.\n * **Profitability Analysis:** Discuss net income, profit margins, and earnings per share (EPS).\n * **Key Metrics:** Include other relevant financial metrics mentioned in the reports (e.g., operating income, cash flow, segment performance).\n * **Management Commentary:** Summarize any relevant insights or explanations provided by Google's management in the earnings calls or reports.\n * **Trend Analysis:** Compare the current quarter's performance to the previous two quarters, highlighting significant changes or trends.\n * **Visualizations:** If possible, use the Google Docs tool to insert basic charts or tables to visually represent the data. (You might need to guide the user on how to do this if the tool has limitations.)\n5. **Cite Sources:** Clearly indicate the source of your information (e.g., \"Q2 2023 Earnings Report\") for each data point or analysis.\n6. **Maintain a Professional Tone:** Write in a clear, concise, and objective tone, as expected of a financial analyst. Avoid speculation or making unsubstantiated claims.\n\nYour ultimate goal is to provide the user with a well-structured, informative, and accurate financial report based on the data available in the last three quarters of Google's earnings reports.\nSave the report in as a Google Doc using the available tool!"}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "40534b4d-3061-4054-8c0a-b08fe32deaf7", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [360, 860], "parameters": {"name": "company_financial_earnings_data_tool", "description": "Retrieve information about the last 3 quarters of Google Earnings"}, "typeVersion": 1}, {"id": "c584d5f6-1fac-420f-a28d-71f51b555e67", "name": "Google Gemini Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [620, 1060], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "jLOqyTR4yTT1nYKi", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "f4f993d0-c80a-4f26-bc51-fe7df1012606", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-160, 860], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "tQLWnWRzD8aebYvp", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "4aa3726e-a105-4bfe-b1df-06c3c9ece18a", "name": "Pinecone Vector Store (Retrieval)", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "position": [260, 1080], "parameters": {"options": {}, "pineconeIndex": {"__rl": true, "mode": "list", "value": "company-earnings", "cachedResultName": "company-earnings"}}, "credentials": {"pineconeApi": {"id": "bQTNry52ypGLqt47", "name": "PineconeApi account"}}, "typeVersion": 1}, {"id": "e08dd92a-a7a1-4204-bef9-54611a2dee92", "name": "Save Report to Google Docs", "type": "n8n-nodes-base.googleDocs", "position": [460, 640], "parameters": {"actionsUi": {"actionFields": [{"text": "={{ $json.output }}", "action": "insert"}]}, "operation": "update", "documentURL": "1aOUl-mnCaI4__tULmBZSvWlOQhTHdD-RUPesP7_sFT4"}, "credentials": {"googleDocsOAuth2Api": {"id": "nnE7RqZglLn8XarL", "name": "Google Docs account"}}, "typeVersion": 2}, {"id": "1984765a-3148-4bcf-9d20-fe29291fda6d", "name": "Embeddings Google Gemini (retrieval)", "type": "@n8n/n8n-nodes-langchain.embeddingsGoogleGemini", "position": [240, 1260], "parameters": {"modelName": "models/text-embedding-004"}, "credentials": {"googlePalmApi": {"id": "jLOqyTR4yTT1nYKi", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "9b0bff2e-06f4-4c89-b9dc-c54cfb79577c", "name": "List Of Files To Load (Google Sheets)", "type": "n8n-nodes-base.googleSheets", "position": [-380, -60], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": **********, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ckP-ZgAMs2l2sFUpLAXx-gWNOQrHXoAs48Vo271X3rs/edit#gid=**********", "cachedResultName": "GOOG"}, "documentId": {"__rl": true, "mode": "list", "value": "1ckP-ZgAMs2l2sFUpLAXx-gWNOQrHXoAs48Vo271X3rs", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1ckP-ZgAMs2l2sFUpLAXx-gWNOQrHXoAs48Vo271X3rs/edit?usp=drivesdk", "cachedResultName": "Watchlist"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "sRJmS2k8zdqVjtJL", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "b0d58ce5-9ac0-4f0f-ac7c-d6cb27551d82", "name": "Download File From Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [160, -60], "parameters": {"fileId": {"__rl": true, "mode": "url", "value": "={{ $('List Of Files To Load (Google Sheets)').item.json['File URL'] }}"}, "options": {"fileName": "={{ $('List Of Files To Load (Google Sheets)').item.json['10Q'] }}"}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "uixLsi5TmrfwXPeB", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "28817b3d-fb54-4dc2-83bc-3ac27320712b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1100, 80], "parameters": {"width": 500, "height": 740, "content": "## Set up steps\n1. Google Cloud Project & Vertex AI API:\n\t* Create a Google Cloud project.\n\t* Enable the Vertex AI API for your project.\n2. Google AI API key:\n\t* Obtain a Google AI API key from Google AI Studio.\n3. Pinecone account and API key:\n\t* Create a free account on the Pinecone website.\n\t* Obtain your API key from your Pinecone dashboard.\n\t* Create an index named company-earnings in your Pinecone project.\n4. Google Drive - download and save financial documents:\n\t* Go to a company you want to analize and download their quarterly earnings PDFs\n\t* Save the PDFs in Google Drive\n\t* Create a Google Sheet that stores a list of file URLs pointing to the PDFs you downloaded and saved to Google Drive\n5. Configure credentials in your n8n environment for:\n\t* Google Sheets OAuth2\n\t* Google Drive OAuth2\n\t* Google Docs OAuth2\n\t* Google Gemini(PaLM) Api (using your Google AI API key)\n\t* Pinecone API (using your Pinecone API key)\n6. Import and configure the workflow:\n\t* Import this workflow into your n8n instance.\n\t* Update the List Of Files To Load (Google Sheets) node to point to your Google Sheet.\n\t* Update the Download File From Google Drive to point to the column where the file URLs are\n\t* Update the Save Report to Google Docs node to point to your Google Doc where you want the report saved."}, "typeVersion": 1}, {"id": "eecb1c25-c019-44e4-b254-a919f80faee7", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [380, -260], "parameters": {"content": "## Loading data to Pinecone vector store"}, "typeVersion": 1}, {"id": "8371f7f8-29a7-4711-b635-d5538f3441b8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-40, 460], "parameters": {"content": "## AI Agent Report Generation using RAG"}, "typeVersion": 1}], "active": false, "pinData": {"AI Agent": [{"json": {"output": "# Google (Alphabet Inc.) Financial Report: Last 3 Quarters\n\n## Executive Summary\nGoogle has demonstrated solid revenue growth across the last three quarters, although there are notable fluctuations in operating income, net income, and other income/expense categories. While revenue from both Google Services and Cloud shows consistent year-over-year growth, the operating margins have shown variability. \n\n## Revenue Analysis\n- **Quarter 1:**\n - **Revenue:** $80.5 billion, a 15% year-over-year increase.\n - **Google Services Revenue:** Up $8.4 billion (14%).\n - **Google Cloud Revenue:** Up $2.1 billion (28%).\n\n- **Quarter 2:**\n - **Revenue:** $84.7 billion, a 14% year-over-year increase.\n - **Google Services Revenue:** Up $7.6 billion (12%).\n - **Google Cloud Revenue:** Up $2.3 billion (29%).\n\n- **Quarter 3:**\n - **Revenue:** $88.3 billion, a 15% year-over-year increase.\n - **Google Services Revenue:** Up $8.5 billion (13%).\n - **Google Cloud Revenue:** Up $2.9 billion (35%).\n\n### Key Trends\n- Consistent revenue growth across all three quarters.\n- Strong growth in Google Cloud, indicating it is a significant area of expansion.\n\n## Expense Analysis\n- **Cost of Revenue:**\n - **Quarter 1:** $33.7 billion (up 10% year-over-year).\n - Reason for increase: Higher total acquisition costs, content acquisition costs, and depreciation.\n\n- **Operating Income:**\n - **Quarter 1:** $17.415 billion (25% operating margin).\n - **Quarter 2:** $21.838 billion (29% operating margin).\n - **Quarter 3:** $21.343 billion (28% operating margin).\n\n### Observations\n- Operating margins have fluctuated, while overall costs have continued to rise.\n \n## Profitability Analysis\n- **Net Income:**\n - **Quarter 1:** $15.051 billion.\n - **Quarter 2:** $18.368 billion.\n - **Quarter 3:** $19.689 billion.\n \n- **Diluted EPS:**\n - **Quarter 1:** $1.17.\n - **Quarter 2:** $1.44.\n - **Quarter 3:** $1.55.\n\n### Summary\nWhile net income has increased, the fluctuations in other income and expense metrics have affected profitability.\n\n## Key Metrics\n- **Operating Margins:**\n - Q1: 25%\n - Q2: 29%\n - Q3: 28%\n\n- **Other Income (Expense), Net:**\n - Q1: $790 million.\n - Q2: $65 million.\n - Q3: -$146 million. (Downturn to a negative number)\n\n## Management Commentary\nManagement has pointed out that increased revenue performance in Google Cloud is encouraging, especially given the challenges in the overall economic environment.\n\n## Trend Analysis\n- **Comparative Performance:**\n - Revenue trends show consistency, ranging from 14%-15% growth year-over-year.\n - Operating income showed a decreasing trend from Q1 ($17.415 billion) to Q2 ($21.838 billion) and slightly decreased again in Q3 ($21.343 billion).\n \n### Noteworthy Observations\n- **Outliers:**\n - Significant volatility in other income/expense net, transitioning from $790 million in Q1 to a loss of $146 million in Q3.\n \n- **Operating Margins:** \n - Variability seen in margins from Q1 (25%) to Q2 (29%) and back down to Q3 (28%) shows a trend of volatility.\n\n## Conclusion\nGoogle has maintained a strong financial position characterized by solid revenue growth. However, the apparent volatility in other income/expense and operating margins warrants closer scrutiny, as it could impact future profitability. The continuous growth in Google Cloud is a positive indicator and suggests strong potential for the coming quarters.\n\n---\n\nThis report provides a comprehensive overview of Google's financial performance over the past three quarters, highlighting key metrics, trends, and outliers. If you require further details or specific analysis, please let me know!"}}]}, "settings": {"executionOrder": "v1"}, "versionId": "30c9a6f0-8ace-40c3-8ca7-a79fd91c12a7", "connections": {"AI Agent": {"main": [[{"node": "Save Report to Google Docs", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Download File From Google Drive", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Pinecone Vector Store": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Embeddings Google Gemini": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "Download File From Google Drive": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Pinecone Vector Store (Retrieval)": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Embeddings Google Gemini (retrieval)": {"ai_embedding": [[{"node": "Pinecone Vector Store (Retrieval)", "type": "ai_embedding", "index": 0}]]}, "List Of Files To Load (Google Sheets)": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}}