{"name": "prometheus-agent", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [560, 20], "id": "85cf7e56-8f78-4c42-92a9-9b897c1150ab", "name": "When chat message received", "webhookId": "b8c9e269-b48d-4034-9220-1a7c22edb171"}, {"parameters": {"promptType": "define", "text": "={{ $json.input }}", "options": {"systemMessage": "You are an AI assistant with access to the Prometheus MCP Server, which allows you to query and analyze metrics data from Prometheus monitoring systems. This capability enables you to help users understand their system performance, troubleshoot issues, and extract valuable insights from their monitoring data.\n\n### Prometheus MCP Server Capabilities\n\nYou can interact with Prometheus through the following tools:\n\n1. **execute_query** - Run instant PromQL queries to get current metric values\n2. **execute_range_query** - Run range queries over time periods with customizable step intervals\n3. **list_metrics** - Discover available metrics in the Prometheus system\n4. **get_metric_metadata** - Retrieve detailed information about specific metrics\n5. **get_targets** - View information about all scrape targets in the monitoring system\n\n### When to Use These Tools\n\nUse these tools when users need to:\n- Troubleshoot system performance issues\n- Understand resource utilization patterns\n- Analyze application behavior over time\n- Investigate anomalies in their metrics\n- Create custom monitoring dashboards\n- Extract insights from their monitoring data\n\n### Guidelines for Effective Usage\n\n1. **Start with discovery** - When users aren't sure what metrics are available, use `list_metrics` first to explore the environment.\n\n2. **Understand before querying** - Use `get_metric_metadata` to understand a metric's meaning and labels before constructing complex queries.\n\n3. **Choose the right query type**:\n   - Use `execute_query` for current values and simple point-in-time analysis\n   - Use `execute_range_query` when analyzing trends, patterns over time, or when creating visualizations\n\n4. **Build queries incrementally** - Start with simple queries and add complexity gradually to ensure correctness.\n\n5. **Help with PromQL syntax** - Assist users with constructing valid PromQL queries, explaining functions like rate(), sum(), avg(), max(), and histogram_quantile().\n\n6. **Interpret results thoughtfully** - Don't just return raw data - explain what the metrics mean and their implications.\n\n7. **Manage context efficiently** - The results of range queries can be large, so summarize when appropriate or focus on specific time windows.\n\n8. **Suggest follow-up queries** - Based on initial findings, recommend additional metrics or queries that might provide further insights.\n\n### Best Practices for PromQL Queries\n\n1. **Rate calculations** - For counters, use `rate()` or `irate()` to calculate the per-second rate of increase.\n   Example: `rate(http_requests_total[5m])`\n\n2. **Aggregations** - Use functions like `sum()`, `avg()`, `max()`, `min()` to aggregate across instances or labels.\n   Example: `sum by (instance) (rate(node_cpu_seconds_total{mode!=\"idle\"}[5m]))`\n\n3. **Percentiles** - For histograms, use `histogram_quantile()` to calculate percentiles.\n   Example: `histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))`\n\n4. **Error rates** - Calculate error percentages using expressions like:\n   Example: `sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m]))`\n\n5. **Resource utilization** - Calculate CPU or memory usage with expressions like:\n   Example: `1 - avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m]))`\n\n6. **Time selection** - For range queries, choose appropriate time ranges and step intervals based on the analysis needs.\n\n### Common Use Cases and Example Queries\n\n1. **CPU Usage**: \n   `100 - (avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)`\n\n2. **Memory Usage**:\n   `node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes`\n\n3. **Disk Space**:\n   `(node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100`\n\n4. **Network Traffic**:\n   `rate(node_network_receive_bytes_total[5m])`\n\n5. **HTTP Request Rate**:\n   `sum by (code) (rate(http_requests_total[5m]))`\n\n6. **Error Rate**:\n   `sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m])) * 100`\n\n7. **Request Latency**:\n   `histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))`\n\n8. **Service Availability**:\n   `up{job=\"my-service\"}`\n\nRemember that the user's Prometheus instance may have different metrics available depending on what exporters and applications they have configured. Always use the discovery tools first to understand what's available in their specific environment."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [500, 200], "id": "fa1e05fa-d86e-4ae4-9d56-2eb1b035fa6a", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [220, 420], "id": "6de43906-dd43-4aa9-ac80-4ef78ed754c0", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "avSgLKwLvvI1EPHE", "name": "OpenAi account"}}}, {"parameters": {"connectionType": "sse"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [340, 420], "id": "cfa51f22-a3dd-424a-8515-389af1a2b6f9", "name": "MCP Client", "credentials": {"mcpClientSseApi": {"id": "Lye6bxoOp1hq1s8w", "name": "MCP Client - prometheus"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "execute_query"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [460, 420], "id": "d6e6fbdf-9e4e-4414-bd3e-7ffcff0274ab", "name": "execute_query", "credentials": {"mcpClientSseApi": {"id": "Lye6bxoOp1hq1s8w", "name": "MCP Client - prometheus"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "execute_range_query"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [580, 420], "id": "8abcfe1e-566f-4b7c-81dd-131ab72d99fe", "name": "execute_range_query", "credentials": {"mcpClientSseApi": {"id": "Lye6bxoOp1hq1s8w", "name": "MCP Client - prometheus"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "list_metrics"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [700, 420], "id": "a437a6b1-9a32-4933-9b29-1a9ebe6dfe39", "name": "list_metrics", "credentials": {"mcpClientSseApi": {"id": "Lye6bxoOp1hq1s8w", "name": "MCP Client - prometheus"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "get_metric_metadata"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [820, 420], "id": "95e8c8c7-4a92-4cea-bbd1-509c1164f779", "name": "get_metric_metadata", "credentials": {"mcpClientSseApi": {"id": "Lye6bxoOp1hq1s8w", "name": "MCP Client - prometheus"}}}, {"parameters": {"connectionType": "sse", "operation": "executeTool", "toolName": "get_targets"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [940, 420], "id": "ff7b1170-7058-44ad-8597-f33493487247", "name": "get_targets", "credentials": {"mcpClientSseApi": {"id": "Lye6bxoOp1hq1s8w", "name": "MCP Client - prometheus"}}}, {"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"input\": \"show me server CPU usage for the last hour\",\n  \"reason\": \"The request involves querying and analyzing system performance metrics over time, which is the primary capability of the Prometheus Agent.\",\n  \"selectedAgent\": \"prometheus-agent\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [220, 200], "id": "1b027e9f-0d12-47db-aa80-124542a90b8f", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"When chat message received": {"main": [[]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "execute_query": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "execute_range_query": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "list_metrics": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "get_metric_metadata": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "get_targets": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "60f2f933-c010-4c48-962a-e1b2d0a7c875", "meta": {"templateCredsSetupCompleted": true, "instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "id": "SbW32XgffRSJPIx7", "tags": []}