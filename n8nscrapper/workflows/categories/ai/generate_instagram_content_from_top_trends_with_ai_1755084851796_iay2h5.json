{"id": "H7porcmXYj7StO23", "meta": {"instanceId": "35409808e3cc9dd8ecfa6f7b93ae931f074920a2f681e667da8974c0ecf81c52", "templateId": "2537", "templateCredsSetupCompleted": true}, "name": "Generate Instagram Content from Top Trends with AI Image Generation", "tags": [], "nodes": [{"id": "8c49be2b-6320-4eb0-8303-6448ced34636", "name": "If media status is finished", "type": "n8n-nodes-base.if", "position": [1420, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0304efee-33b2-499e-bad1-9238c1fc2999", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status_code }}", "rightValue": "FINISHED"}]}}, "typeVersion": 2.2}, {"id": "f0cc0be5-6d35-4334-a124-139fa8676d07", "name": "If media status is finished1", "type": "n8n-nodes-base.if", "position": [2000, 260], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0304efee-33b2-499e-bad1-9238c1fc2999", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status_code }}", "rightValue": "PUBLISHED"}]}}, "typeVersion": 2.2}, {"id": "c8d8d8cd-8501-4d1b-ac28-8cb3fa74d9d7", "name": "Telegram", "type": "n8n-nodes-base.telegram", "position": [1580, 440], "parameters": {"text": "Video upload edilmeden önce bir problem oldu", "chatId": "={{ $('Telegram Params').item.json.telegram_chat_id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "GcIVVl98RcazYBaB", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "ae91a5e0-4f70-4a1c-afa5-41f5449facab", "name": "Telegram1", "type": "n8n-nodes-base.telegram", "position": [2160, 100], "parameters": {"text": "Instagram Content is shared", "chatId": "={{ $('Telegram Params').item.json.telegram_chat_id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "GcIVVl98RcazYBaB", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "b8b38440-14a7-43f6-ac49-6ca9502ff54d", "name": "Telegram2", "type": "n8n-nodes-base.telegram", "position": [2160, 440], "parameters": {"text": "There was a problem when execution a upload content to instagram", "chatId": "={{ $('Telegram Params').item.json.telegram_chat_id }}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "GcIVVl98RcazYBaB", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "82e0e5d0-bf50-4b2e-8693-2612dffe53e2", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [-1000, 220], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "fb72beb1-1a6a-4148-9ee4-cdc564c4dc5c", "name": "Schedule Trigger1", "type": "n8n-nodes-base.scheduleTrigger", "position": [-3080, 300], "parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "5 13,19 * * *"}]}}, "typeVersion": 1.2}, {"id": "470f3406-19d2-420c-8f33-7031237d882c", "name": "Telegram Params", "type": "n8n-nodes-base.set", "position": [-2320, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "d18cdca7-d301-4c70-a4d0-8d6e7ecfc2d1", "name": "telegram_chat_id", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "********-7061-4d32-8921-d2e731eae9db", "name": "Instagram params", "type": "n8n-nodes-base.set", "position": [-2560, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1e380c14-e908-4eeb-90e0-957a422829d0", "name": "instagram_business_account_id", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "3cb5f27d-eb3b-4fdc-bb55-1b54f85298e5", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-2860, 20], "parameters": {"color": 4, "width": 1000, "height": 600, "content": "## All Credentials You Need\n** Instagram Business Account Id\n** Telegram Chat Id\n** Rapid Api Key\n** Replicate Token"}, "typeVersion": 1}, {"id": "2bc617b8-835c-48ba-8de6-341a6c87b853", "name": "Rapid Api params", "type": "n8n-nodes-base.set", "notes": "test", "position": [-2080, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "48a33ec7-2b4f-496a-ad77-e4d5f1907ee4", "name": "x-rapid-api-key", "type": "string", "value": ""}]}}, "notesInFlow": false, "typeVersion": 3.4}, {"id": "23bad41e-40ac-4488-8b2f-0d54d22a927a", "name": "filter the image content", "type": "n8n-nodes-base.code", "position": [-1480, 380], "parameters": {"jsCode": "const filteredData = $input.first().json.data.items.filter(item=> !item.is_video)\nreturn filteredData.map((item)=>{\n return {\n id: item.id,\n prompt: item.caption.text,\n content_code: item.code,\n thumbnail_url: item.thumbnail_url,\n tag: $input.first().json.data.additional_data.name\n }\n}) \n\n"}, "typeVersion": 2}, {"id": "a65690cd-4d30-4541-b80d-aae872326a77", "name": "get top trends on instagram #blender3d", "type": "n8n-nodes-base.httpRequest", "position": [-1720, 180], "parameters": {"url": "https://instagram-scraper-api2.p.rapidapi.com/v1/hashtag", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "hashtag", "value": "blender3d"}, {"name": "feed_type", "value": "top"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "instagram-scraper-api2.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "={{ $json['x-rapid-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "8707c475-7e28-4d80-92b8-ba24033c4632", "name": "get top trends on instagram #isometric", "type": "n8n-nodes-base.httpRequest", "position": [-1720, 380], "parameters": {"url": "https://instagram-scraper-api2.p.rapidapi.com/v1/hashtag", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "hashtag", "value": "isometric"}, {"name": "feed_type", "value": "top"}]}, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "instagram-scraper-api2.p.rapidapi.com"}, {"name": "x-rapidapi-key", "value": "={{ $json['x-rapid-api-key'] }}"}]}}, "typeVersion": 4.2}, {"id": "1c1bfd8f-b086-4147-ba08-578877f2a315", "name": "merge the array content", "type": "n8n-nodes-base.merge", "position": [-1280, 280], "parameters": {}, "typeVersion": 3}, {"id": "dcc2b6b6-9880-4676-8a1a-a3c21e583bba", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-3180, 20], "parameters": {"color": 3, "width": 280, "height": 600, "content": "## Schedule Your Time To Post\n"}, "typeVersion": 1}, {"id": "c1e0ac33-c4b7-47d8-bd2b-0b74b02afe38", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-2600, 160], "parameters": {"color": 5, "width": 180, "height": 300, "content": "## Guide \n** [Guide](https://docs.matillion.com/metl/docs/6957316//) of getting of Instagram Business Account Id "}, "typeVersion": 1}, {"id": "321680da-ca7a-4c6f-98d4-a0d8f8d0347f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-2360, 160], "parameters": {"color": 5, "width": 180, "height": 300, "content": "## Guide \n** [Guide](https://rapidapi.com/i-yqerddkq0t/api/telegram92/tutorials/how-to-get-the-id-of-a-telegram-channel,-chat,-user-or-bot%3F) of Getting of Telegram Chat Id "}, "typeVersion": 1}, {"id": "b3d07cf7-8d03-4644-88f7-2e94de0c43c2", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-2120, 160], "parameters": {"color": 5, "width": 180, "height": 300, "content": "## Guide \n** [Guide](https://docs.rapidapi.com/docs/keys-and-key-rotation) of Getting of Rapid Api Key "}, "typeVersion": 1}, {"id": "b6dbdfaa-fc71-4def-a723-bf6c0facd372", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [-2360, 480], "parameters": {"color": 7, "width": 180, "height": 120, "content": "## Warning\n**Don't forgot the create bot and send a message to bot first"}, "typeVersion": 1}, {"id": "81d598e2-**************-2e78dc26ad10", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-1820, 20], "parameters": {"width": 660, "height": 600, "content": "## Getting Top Trend Posts On Instagram\n** Change the topic you want to get on http request"}, "typeVersion": 1}, {"id": "6beb79ef-8205-4882-9bb0-6a2e1a33f1d4", "name": "Check Data on Database Is Exist", "type": "n8n-nodes-base.postgres", "onError": "continueErrorOutput", "position": [-760, 220], "parameters": {"table": {"__rl": true, "mode": "list", "value": "top_trends", "cachedResultName": "top_trends"}, "where": {"values": [{"value": "={{$json.content_code}}", "column": "code"}]}, "schema": {"__rl": true, "mode": "list", "value": "public", "cachedResultName": "public"}, "options": {}, "operation": "select"}, "credentials": {"postgres": {"id": "sBHQ2psBsfnHkFrZ", "name": "Postgres account"}}, "typeVersion": 2.5, "alwaysOutputData": true}, {"id": "5b0c05a8-3eb7-4ad8-88e8-ceef81fe7a61", "name": "If Data is Exist", "type": "n8n-nodes-base.if", "position": [-540, 240], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "9dc20983-ae4d-40db-b969-7d43fa8b0c3e", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ !$json.isEmpty() }}", "rightValue": "we"}, {"id": "0e1b9264-be56-4d0c-a83e-d9ca0b05b265", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "", "rightValue": ""}]}, "looseTypeValidation": true}, "executeOnce": false, "typeVersion": 2.2, "alwaysOutputData": false}, {"id": "557aa2c3-8d0b-42c4-b444-953a538d7ff4", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1120, 20], "parameters": {"width": 1060, "height": 600, "content": "## Looping Data And Checking For Is Exist On Database\n**We are checking until find a data we did not insert because we don't want to create content about in same content"}, "typeVersion": 1}, {"id": "9b510f11-9a44-4d54-b162-3ffb55d66677", "name": "send error message to telegram", "type": "n8n-nodes-base.telegram", "position": [-1000, 440], "parameters": {"text": "There was a problem execution a postgresql content", "chatId": "={{ $('Telegram Params').item.json.telegram_chat_id}}", "additionalFields": {}}, "credentials": {"telegramApi": {"id": "GcIVVl98RcazYBaB", "name": "Telegram account"}}, "typeVersion": 1.2}, {"id": "48bc61de-d416-4673-9e9b-8331ea841891", "name": "insert data on db", "type": "n8n-nodes-base.postgres", "position": [-260, 240], "parameters": {"table": {"__rl": true, "mode": "list", "value": "top_trends", "cachedResultName": "top_trends"}, "schema": {"__rl": true, "mode": "list", "value": "public"}, "columns": {"value": {"tag": "={{$('Loop Over Items').item.json.tag}}", "code": "={{$('Loop Over Items').item.json.content_code}}", "prompt": "={{$('Loop Over Items').item.json.prompt}}", "isposted": false, "thumbnail_url": "={{$('Loop Over Items').item.json.thumbnail_url}}"}, "schema": [{"id": "id", "type": "number", "display": true, "removed": true, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "prompt", "type": "string", "display": true, "required": true, "displayName": "prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "isposted", "type": "boolean", "display": true, "required": false, "displayName": "isposted", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "createdat", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "createdat", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "updatedat", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "updatedat", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "deletedat", "type": "dateTime", "display": true, "removed": true, "required": false, "displayName": "deletedat", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "code", "type": "string", "display": true, "required": false, "displayName": "code", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tag", "type": "string", "display": true, "required": false, "displayName": "tag", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "thumbnail_url", "type": "string", "display": true, "required": false, "displayName": "thumbnail_url", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "credentials": {"postgres": {"id": "sBHQ2psBsfnHkFrZ", "name": "Postgres account"}}, "typeVersion": 2.5}, {"id": "15e7d69d-a10f-48a1-b240-046e9950d077", "name": "Analyze Image and give the content", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [80, 240], "parameters": {"text": "Create a clear and concise description of the object in the image, focusing on its physical and general features. Avoid detailed environmental aspects like background, lighting, or colors. Describe the shape, texture, size, and any unique characteristics of the object. Mention any notable features that make the object stand out, such as its surface details, materials, and design. The description should be focused on the object itself, not its surroundings.\n\nFor example, describe the following image:\n", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "resource": "image", "imageUrls": "={{ $('Loop Over Items').item.json.thumbnail_url }}", "operation": "analyze"}, "credentials": {"openAiApi": {"id": "1TwEayhZUT90fq8N", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "93e253b1-da7d-4193-b899-a38e6fd9f4e4", "name": "Analyze Content And Generate Instagram Caption", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [280, 240], "parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "options": {}, "messages": {"values": [{"content": "=\nSummarize the following content description into a short, engaging Instagram caption under 150 words. The caption should focus on the content of the image, not the app. Keep it appealing to social media users, and highlight the visual details of the image. Include hashtags relevant to 3D modeling and design, such as #Blender3D, #3DArt, #DigitalArt, #3DModeling, and #ArtCommunity. Ensure the tone is friendly and inviting.\n\n\nContent description to summarize:\n{{ $json.content }}\n\nMake sure to craft the caption around the content's features, such as the color contrast, reflective surface, and artistic nature of the image.\n\n"}]}}, "credentials": {"openAiApi": {"id": "1TwEayhZUT90fq8N", "name": "OpenAi account"}}, "typeVersion": 1.8}, {"id": "9af1dc59-1d9e-4900-8f80-1eba946c4057", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-20, 20], "parameters": {"color": 4, "width": 860, "height": 600, "content": "## Analyze Post Content\n** We are analyzing the image\n** We are generating a instagram caption by content\n** Then we are generating the image"}, "typeVersion": 1}, {"id": "2259f6df-dca9-4a7e-babb-e63375f7207f", "name": "Prepare data on Instagram", "type": "n8n-nodes-base.facebookGraphApi", "position": [980, 260], "parameters": {"edge": "media", "node": "={{ $('Instagram params').item.json.instagram_business_account_id }}", "options": {"queryParameters": {"parameter": [{"name": "image_url", "value": "={{ $json.output[0] }}"}, {"name": "caption", "value": "={{ $('Analyze Content And Generate Instagram Caption').item.json.message.content }}"}]}}, "graphApiVersion": "v20.0", "httpRequestMethod": "POST"}, "credentials": {"facebookGraphApi": {"id": "ZFxxxLfZ25M7Va6r", "name": "Facebook Graph account"}}, "typeVersion": 1}, {"id": "bcbb6058-1966-4bb5-915a-1e65b9131117", "name": "Check Status Of Media Before Uploaded", "type": "n8n-nodes-base.facebookGraphApi", "position": [1200, 260], "parameters": {"node": "={{ $json.id }}", "options": {"fields": {"field": [{"name": "id"}, {"name": "status"}, {"name": "status_code"}]}}, "graphApiVersion": "v20.0"}, "credentials": {"facebookGraphApi": {"id": "ZFxxxLfZ25M7Va6r", "name": "Facebook Graph account"}}, "typeVersion": 1}, {"id": "518d87ff-7808-4c06-b137-4e97d8f2ca28", "name": "Publish Media on Instagram", "type": "n8n-nodes-base.facebookGraphApi", "position": [1600, 100], "parameters": {"edge": "media_publish", "node": "={{ $('Instagram params').item.json.instagram_business_account_id }}", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}]}}, "graphApiVersion": "v20.0", "httpRequestMethod": "POST"}, "credentials": {"facebookGraphApi": {"id": "ZFxxxLfZ25M7Va6r", "name": "Facebook Graph account"}}, "typeVersion": 1}, {"id": "a033d12b-524f-40e8-9208-5300bbc823d3", "name": "Check status of post ", "type": "n8n-nodes-base.facebookGraphApi", "position": [1800, 260], "parameters": {"node": "={{ $('Check Status Of Media Before Uploaded').item.json.id }}", "options": {"fields": {"field": [{"name": "id"}, {"name": "status"}, {"name": "status_code"}]}}, "graphApiVersion": "v20.0"}, "credentials": {"facebookGraphApi": {"id": "ZFxxxLfZ25M7Va6r", "name": "Facebook Graph account"}}, "typeVersion": 1}, {"id": "f136e907-2938-4175-b51f-4201fbe3477d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [880, 20], "parameters": {"color": 5, "width": 1580, "height": 600, "content": "## Publish On Instagram And Send Message When Published via Telegram\n"}, "typeVersion": 1}, {"id": "8145986c-5453-43ac-8d5c-c50a84a62136", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-1800, 100], "parameters": {"color": 5, "width": 260, "height": 500, "content": "## For More About Api\n** [Facebook Scraper Api Guide](https://rapidapi.com/social-api1-instagram/api/instagram-scraper-api2/playground/apiendpoint_a45552b2-9850-4da9-b5cb-bbdd3ac2199d)"}, "typeVersion": 1}, {"id": "02416fbb-4250-4278-af23-1f9189787123", "name": "filter the image content-2", "type": "n8n-nodes-base.code", "position": [-1480, 180], "parameters": {"jsCode": "const filteredData = $input.first().json.data.items.filter(item=> !item.is_video)\nreturn filteredData.map((item)=>{\n return {\n id: item.id,\n prompt: item.caption.text,\n content_code: item.code,\n thumbnail_url: item.thumbnail_url,\n tag: $input.first().json.data.additional_data.name\n }\n}) \n\n"}, "typeVersion": 2}, {"id": "2d1ea53d-1d32-4b86-8944-ce2ad4a69847", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-2820, 160], "parameters": {"color": 5, "width": 180, "height": 300, "content": "## Guide \n** [Guide](https://replicate.com) of getting of Replicate Token "}, "typeVersion": 1}, {"id": "c8b933af-356e-49ae-92d3-42eaf4ee3e9f", "name": "Replicate params", "type": "n8n-nodes-base.set", "position": [-2780, 300], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "1e380c14-e908-4eeb-90e0-957a422829d0", "name": "replicate_token", "type": "string", "value": ""}]}}, "typeVersion": 3.4}, {"id": "2c73cc9c-d436-459b-9b3c-bd870810b9b4", "name": "Generate image on flux", "type": "n8n-nodes-base.httpRequest", "position": [680, 260], "parameters": {"url": "https://api.replicate.com/v1/models/black-forest-labs/flux-schnell/predictions", "method": "POST", "options": {}, "jsonBody": "={\n \"input\": {\n \"prompt\": \"A highly detailed 3D isometric model of {{$('Analyze Image and give the content').item.json.content .replace(/\\\\n/g, ' ') \n.replace(/\\\\t/g, ' ') \n.replace(/\\s+/g, ' ')\n.trim(); }} rendered in a stylized miniature toy aesthetic. Materials: Matte plastic/painted metal/weathered stone texture with no self-shadowing. Lighting: - Completely shadowless rendering - Ultra bright and perfectly even illumination from all angles - Pure ambient lighting without directional shadows - Flat, consistent lighting across all surfaces - No ambient occlusion. Style specifications: - Clean, defined edges and surfaces - Slightly exaggerated proportions - Miniature/toy-like scale - Subtle wear and texturing - Rich color palette with muted tones - Isometric 3/4 view angle - Crisp details and micro-elements. Technical details: - 4K resolution - PBR materials without shadows - No depth of field - High-quality anti-aliasing - Perfect uniform lighting. Environment: Pure white background with zero shadows or gradients. Post-processing: High key lighting, maximum brightness, shadow removal.\",\n \"output_format\": \"jpg\",\n \"output_quality\": 100,\n \"go_fast\":false\n }\n}\n", "sendBody": true, "sendHeaders": true, "specifyBody": "=json", "bodyParameters": {"parameters": [{}]}, "headerParameters": {"parameters": [{"name": "Authorization", "value": "=Bearer {{ $('Replicate params').item.json.replicate_token}}"}, {"name": "Prefer", "value": "wait"}]}}, "typeVersion": 4.2}, {"id": "6f9e7dc6-1287-4235-8631-198d729f367f", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1120, -340], "parameters": {"color": 4, "width": 1060, "height": 320, "content": "## For top_trends Table\n```\nCREATE TABLE top_trends (\n id SERIAL PRIMARY KEY,\n isposted B<PERSON>OLEAN DEFAULT false,\n createdat TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n updatedat TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n deletedat TIMESTAMP WITHOUT TIME ZONE,\n prompt TEXT NOT NULL,\n thumbnail_url TEXT,\n code TEXT,\n tag TEXT\n);\n```"}, "typeVersion": 1}, {"id": "b19951bb-6346-44a7-a4c8-1bd0806c6019", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-660, -120], "parameters": {"color": 3, "width": 160, "height": 120, "content": "## Warning\n** Don't forgot the create top_trends table"}, "typeVersion": 1}, {"id": "3de6b8e5-c5e0-4999-871a-c349cb9b3ac0", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-3180, -940], "parameters": {"width": 620, "height": 840, "content": "\n## Automated Instagram Content Creation from Trending Posts\n\nThis workflow automates the process of discovering and recreating trending content on Instagram:\n\n1. Content Discovery:\n - Scrapes top trending posts from specific hashtags (#blender3d, #isometric)\n - Filters for image-only content (excludes videos)\n - Checks database to avoid duplicate content\n\n2. AI-Powered Content Generation:\n - Analyzes trending images using GPT-4 Vision\n - Generates detailed descriptions of visual elements\n - Creates engaging Instagram captions with relevant hashtags\n - Uses Flux AI to generate similar but unique images\n\n3. Publishing & Monitoring:\n - Automatically posts content to Instagram Business Account\n - Monitors post status and publishing process\n - Sends status updates via Telegram\n\nPerfect for content creators and businesses looking to maintain an active Instagram presence with AI-generated content inspired by current trends. The workflow runs on schedule and handles everything from content discovery to publication automatically.\n\nNote: Requires Instagram Business Account, Telegram Bot, OpenAI, and Replicate API credentials."}, "typeVersion": 1}, {"id": "dfd0d182-177c-4336-8950-4792ea739123", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [-2120, 480], "parameters": {"color": 7, "width": 180, "height": 120, "content": "##Warning\n** <PERSON><PERSON> forgot the subscribe [Instagram Scraper Api](https://rapidapi.com/social-api1-instagram/api/instagram-scraper-api2/playground/apiendpoint_a45552b2-9850-4da9-b5cb-bbdd3ac2199d)"}, "typeVersion": 1}, {"id": "********-3c6e-4152-8c51-f1d53f4424bc", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [-2120, 640], "parameters": {"width": 180, "height": 180, "content": "## Warning\n** You can check the [rate limit](https://rapidapi.com/social-api1-instagram/api/instagram-scraper-api2) of the Instagram Scraper Api on Rapid Api\n** Free version is monthly 500 request\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Europe/Istanbul", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "cc50f9e8-373b-433a-af43-824a264e762a", "connections": {"Telegram": {"main": [[]]}, "Loop Over Items": {"main": [[], [{"node": "Check Data on Database Is Exist", "type": "main", "index": 0}]]}, "Telegram Params": {"main": [[{"node": "Rapid Api params", "type": "main", "index": 0}]]}, "If Data is Exist": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "insert data on db", "type": "main", "index": 0}]]}, "Instagram params": {"main": [[{"node": "Telegram Params", "type": "main", "index": 0}]]}, "Rapid Api params": {"main": [[{"node": "get top trends on instagram #isometric", "type": "main", "index": 0}, {"node": "get top trends on instagram #blender3d", "type": "main", "index": 0}]]}, "Replicate params": {"main": [[{"node": "Instagram params", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "Replicate params", "type": "main", "index": 0}]]}, "insert data on db": {"main": [[{"node": "Analyze Image and give the content", "type": "main", "index": 0}]]}, "Check status of post ": {"main": [[{"node": "If media status is finished1", "type": "main", "index": 0}]]}, "Generate image on flux": {"main": [[{"node": "Prepare data on Instagram", "type": "main", "index": 0}]]}, "merge the array content": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "filter the image content": {"main": [[{"node": "merge the array content", "type": "main", "index": 1}]]}, "Prepare data on Instagram": {"main": [[{"node": "Check Status Of Media Before Uploaded", "type": "main", "index": 0}]]}, "Publish Media on Instagram": {"main": [[{"node": "Check status of post ", "type": "main", "index": 0}]]}, "filter the image content-2": {"main": [[{"node": "merge the array content", "type": "main", "index": 0}]]}, "If media status is finished": {"main": [[{"node": "Publish Media on Instagram", "type": "main", "index": 0}], [{"node": "Telegram", "type": "main", "index": 0}]]}, "If media status is finished1": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}], [{"node": "Telegram2", "type": "main", "index": 0}]]}, "Check Data on Database Is Exist": {"main": [[{"node": "If Data is Exist", "type": "main", "index": 0}], [{"node": "send error message to telegram", "type": "main", "index": 0}]]}, "Analyze Image and give the content": {"main": [[{"node": "Analyze Content And Generate Instagram Caption", "type": "main", "index": 0}]]}, "Check Status Of Media Before Uploaded": {"main": [[{"node": "If media status is finished", "type": "main", "index": 0}]]}, "get top trends on instagram #isometric": {"main": [[{"node": "filter the image content", "type": "main", "index": 0}]]}, "get top trends on instagram #blender3d": {"main": [[{"node": "filter the image content-2", "type": "main", "index": 0}]]}, "Analyze Content And Generate Instagram Caption": {"main": [[{"node": "Generate image on flux", "type": "main", "index": 0}]]}}}