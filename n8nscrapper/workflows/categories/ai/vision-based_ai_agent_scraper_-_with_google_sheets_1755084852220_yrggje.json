{"id": "PpFVCrTiYoa35q1m", "meta": {"instanceId": "b9faf72fe0d7c3be94b3ebff0778790b50b135c336412d28fd4fca2cbbf8d1f5", "templateCredsSetupCompleted": true}, "name": "Vision-Based AI Agent Scraper - with Google Sheets, ScrapingBee, and Gemini", "tags": [], "nodes": [{"id": "90ac8845-342e-4fdb-ae09-cb9d169b4119", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [160, 460], "parameters": {}, "typeVersion": 1}, {"id": "7a2bfc41-1527-448d-a52c-794ca4c9e7ee", "name": "ScrapingBee- Get page HTML", "type": "n8n-nodes-base.httpRequest", "position": [2280, 1360], "parameters": {"url": "https://app.scrapingbee.com/api/v1", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "<your_scrapingbee_apikey>"}, {"name": "url", "value": "={{$json.url}}"}]}}, "typeVersion": 4.2}, {"id": "a0ab6dcb-ffad-40bf-8a22-f2e152e69b00", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [2480, 880], "parameters": {"jsonSchemaExample": "[{\n \"product_title\":\"The title of the product\",\n \"product_price\":\"The price of the product\",\n \"product_brand\": \"The brand of the product\",\n \"promo\":\"true or false\",\n \"promo_percentage\":\"NUM %\"\n}]"}, "typeVersion": 1.2}, {"id": "34f50603-a969-425d-8a1a-ec8031a5cdfd", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1800, 900], "parameters": {"options": {}, "modelName": "models/gemini-1.5-pro-latest"}, "credentials": {"googlePalmApi": {"id": "", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "2054612e-f3e1-4633-9c1a-0644ae07613c", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "position": [2880, 460], "parameters": {"options": {}, "fieldToSplitOut": "output"}, "typeVersion": 1}, {"id": "1a59a962-f483-4a27-8686-607a7d375584", "name": "Google Sheets - Get list of URLs", "type": "n8n-nodes-base.googleSheets", "position": [620, 460], "parameters": {"options": {}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "", "cachedResultName": "List of URLs"}, "documentId": {"__rl": true, "mode": "list", "value": "", "cachedResultUrl": "", "cachedResultName": "Google Sheets - Workflow Vision-Based Scraping"}, "authentication": "serviceAccount"}, "credentials": {"googleApi": {"id": "", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "e33defac-e5c4-4bf5-ae31-98cf6f1d2579", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [76.**************, -6.***************], "parameters": {"color": 7, "width": 364.**************, "height": 652.*************, "content": "## Trigger\nThe default trigger is **When clicking ‘Test workflow’**, meaning the workflow will **need to be triggered manually**. \n\nYou can replace this by selecting a **trigger of your choice**.\n"}, "typeVersion": 1}, {"id": "9f56e57e-8505-4a7a-a531-f7df87a6ea9c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [480, -12.***************], "parameters": {"color": 7, "width": 364.**************, "height": 664.*************, "content": "## Google Sheets - List of URLs\n\nThe Google Sheet will contain two sheets: \n- **List of URLs to** scrape \n- **Results** page, populated with the scraping results and AI-extracted data.\n\nHere is an **[example Google Sheet](https://docs.google.com/spreadsheets/d/10Gc7ooUeTBbOOE6bgdNe5vSKRkkcAamonsFSjFevkOE/)** you can use. The \"Results\" sheet is pre-configured for e-commerce website scraping. You can adapt it to your specific needs, but remember to adjust the `Structured Output Parser` node accordingly.\n"}, "typeVersion": 1}, {"id": "e4497a81-6849-4c79-af45-40e518837e2e", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [880, -15.959302325581348], "parameters": {"color": 7, "width": 364.**************, "height": 667.2965116279074, "content": "## Set Fields\n\nThis node allows you to **define the fields** that will be sent to the **ScrapingBee HTTP Node** and the AI Agent. \n\nIn this template, **only one field** is pre-configured: **url**. You can customize it by adding additional fields as needed.\n"}, "typeVersion": 1}, {"id": "82dcdc23-3d71-4281-a3d0-fdbc27327dd0", "name": "Set fields", "type": "n8n-nodes-base.set", "position": [1040, 460], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c53c5ed2-9c7b-4365-9953-790264c722ab", "name": "url", "type": "string", "value": "={{ $json.url }}"}]}}, "typeVersion": 3.4}, {"id": "ad06f56f-4a02-49d6-9fda-94cdcfadec3b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1280, -20.537790697674154], "parameters": {"color": 7, "width": 364.**************, "height": 671.8750000000002, "content": "## ScrapingBee - Get Page Screenshot\n\nThis node uses Scraping<PERSON>ee, a powerful scraping tool, to capture a screenshot of the desired URL. \nYou can [try ScrapingBee](https://www.scrapingbee.com/) and enjoy 1,000 free requests (non-affiliate link). \n\nEnsure the `screenshot_full_page` parameter is set to *`true`* for a full-page screenshot. This is crucial for vision-based scraping with the AI Agent. \n\nAlternatively, you can **choose to screenshot only a specific part of the page**. However, keep in mind that the **AI Agent will extract data only from the visible section—it has vision**, but not a crystal ball 🔮!\n"}, "typeVersion": 1}, {"id": "01cbc1eb-2910-49b1-89e6-d32d340e5273", "name": "ScrapingBee - Get page screenshot", "type": "n8n-nodes-base.httpRequest", "position": [1440, 460], "parameters": {"url": "https://app.scrapingbee.com/api/v1", "options": {}, "sendQuery": true, "sendHeaders": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "<your_scrapingbee_apikey>"}, {"name": "url", "value": "={{ $json.url }}"}, {"name": "screenshot_full_page", "value": "true"}]}, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"}]}}, "typeVersion": 4.2}, {"id": "3e61d7cb-c2af-4275-b075-3dc14ed320b7", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1680, -26.831395348837077], "parameters": {"color": 7, "width": 1000.334302325581, "height": 679.5058139534889, "content": "## Vision-Based Scraping AI Agent\n\nThis is the central node of the workflow, powered by an AI Agent with two key prompts:\n\n- **System Prompt**: Instructs the AI on how and what data to extract from the screenshot. You can customize this to suit your needs. It also includes fallback instructions to call a tool for retrieving the HTML page if data extraction from the screenshot fails. \n- **User Message**: Provides the page URL for context.\n\n### Sub-Nodes\n\n1. **Google Gemini Chat Model** \n Chosen because tests show that **Gemini-1.5-Pro** outperforms GPT-4 and GPT-4-Vision in visual tasks. *Either my prompt wasn’t optimized for GPT models, or GPT might need glasses 👓*. \n**Other multimodal LLMs haven’t been tested yet**.\n\n2. **HTML-Based Scraping Tool** \n A **fallback tool** the agent **uses if it cannot extract data directly from the screenshot**.\n\n3. **Structured Output Parser** \n Formats the **extracted data into an easy-to-use structure**, ready to be added to the **results page in Google Sheets**."}, "typeVersion": 1}, {"id": "9fe8ee54-755a-44f2-a2bf-a695e3754b3d", "name": "HTML-based Scraping Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [2160, 900], "parameters": {"name": "HTMLScrapingTool", "workflowId": {"__rl": true, "mode": "list", "value": "PpFVCrTiYoa35q1m", "cachedResultName": "vb-scraping"}, "description": "=Call this tool ONLY when you need to retrieve the HTML content of a webpage.", "responsePropertyName": "data"}, "typeVersion": 1.2}, {"id": "12c4fd7e-b662-488a-b779-792cff5464e4", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1680, 720], "parameters": {"color": 6, "width": 305.625, "height": 337.03488372093034, "content": "### Google Gemini Chat Model\n\nThe **default model is gemini-1.5-pro**. It offers excellent performance for this use case, but **it’s not the most cost-effective option—use it judiciously**.\n\n"}, "typeVersion": 1}, {"id": "86cf37d9-a4c1-42f4-a98e-ef2ca4410efd", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2020, 720], "parameters": {"color": 6, "width": 305.625, "height": 337.03488372093034, "content": "### HTML-Based Scraping Tool\n\nThis tool is **invoked when the AI Agent requires the HTML** (*converted to Markdown*) to extract data because the **screenshot alone wasn’t sufficient**.\n"}, "typeVersion": 1}, {"id": "a3dc3c83-ed18-4a58-bc36-440efe9462a2", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2360, 720], "parameters": {"color": 6, "width": 305.625, "height": 337.03488372093034, "content": "### Structured Output Parser\n\nThis node **organizes the extracted data into an easy-to-use JSON format**. \n\nIn this template, the JSON is **designed for an e-commerce webpage**. Customize it to fit your specific needs.\n"}, "typeVersion": 1}, {"id": "939f0f2d-19c8-4447-9b25-accfcd5f6a16", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2740, -20], "parameters": {"color": 7, "width": 364.**************, "height": 671.8750000000002, "content": "## Split Out\n\nThis node **splits the array** created by the `Structured Output Parser` into **individual rows**, making them easy to append to the **subsequent Google Sheets node**.\n"}, "typeVersion": 1}, {"id": "71404369-d2f6-4ca5-ae87-47a51fabfa4a", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [3200, -20], "parameters": {"color": 7, "width": 364.**************, "height": 671.8750000000002, "content": "## Google Sheets - Create Rows\n\nThis node **creates rows** in the **Results** sheet using the extracted data. \n\nYou can use the **[example Google Sheet](https://docs.google.com/spreadsheets/d/10Gc7ooUeTBbOOE6bgdNe5vSKRkkcAamonsFSjFevkOE/)** as a template. However, ensure that the **columns in the Results sheet are aligned with the structure of the output** from the `Structured Output Parser node`.\n"}, "typeVersion": 1}, {"id": "226520d1-2edb-4ade-9940-0bae461eb161", "name": "Google Sheets - Create Rows", "type": "n8n-nodes-base.googleSheets", "position": [3340, 460], "parameters": {"columns": {"value": {"promo": "={{ $json.promo }}", "category": "={{ $('Set fields').item.json.url }}", "product_url": "={{ $json.product_title }}", "product_brand": "={{ $json.product_brand }}", "product_price": "={{ $json.product_price }}", "promo_percent": "={{ $json.promo_percentage }}"}, "schema": [{"id": "category", "type": "string", "display": true, "required": false, "displayName": "category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_url", "type": "string", "display": true, "required": false, "displayName": "product_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_price", "type": "string", "display": true, "required": false, "displayName": "product_price", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "product_brand", "type": "string", "display": true, "required": false, "displayName": "product_brand", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "promo", "type": "string", "display": true, "required": false, "displayName": "promo", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "promo_percent", "type": "string", "display": true, "required": false, "displayName": "promo_percent", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {}, "operation": "append", "sheetName": {"__rl": true, "mode": "list", "value": *********, "cachedResultUrl": "", "cachedResultName": "Results"}, "documentId": {"__rl": true, "mode": "list", "value": "1g81_39MJUlwnInX30ZuBtHUb-Y80WrYyF5lccaRtcu0", "cachedResultUrl": "", "cachedResultName": "Google Sheets - Workflow Vision-Based Scraping"}, "authentication": "serviceAccount"}, "credentials": {"googleApi": {"id": "", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "2c142537-d8fe-4fc1-9758-6a3538c43fc0", "name": "Vision-based Scraping Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2040, 460], "parameters": {"text": "=Here is the screenshot you need to use to extract data about the page:\n\n{{ $json.url }}", "options": {"systemMessage": "=Extract the following details from the input screenshot:\n\n- Product Titles\n- Product Prices\n- Brands\n- Promotional Information (e.g., if the product is on promo)\n\nStep 1: Image-Based Extraction\nAnalyze the provided screenshot to identify and extract all the required details: product titles, prices, brands, and promotional information.\nEnsure the extraction is thorough and validate the completeness of the information.\nCross-check all products for missing or unclear details.\nHighlight any limitations (e.g., text is unclear, partially cropped, or missing) in the extraction process.\n\nStep 2: HTML-Based Extraction (If Needed)\nIf you determine that any required information is:\n\nIncomplete or missing (e.g., not all titles, prices, or brands could be retrieved).\nAmbiguous or uncertain (e.g., unclear text or potential errors in OCR).\nUnavailable due to the limitations of image processing (e.g., product links).\n\nThen:\n\nCall the HTML-based tool with the input URL to access the page content.\nExtract the required details from the HTML to supplement or replace the image-based results.\nCombine data from both sources (if applicable) to ensure the final result is comprehensive and accurate.\n\nAdditional Notes\nAvoid redundant HTML tool usage—confirm deficiencies in image-based extraction before proceeding.\nFor products on promotion, explicitly label this status in the output.\nReport extraction errors or potential ambiguities (e.g., text illegibility).\n\nIn your output, include all these fields as shown in the example below. If there is no promotion, set \"promo\" to false and \"promo_percent\" to 0.\n\njson\nCopy code\n[{\n \"product_title\": \"The title of the product\",\n \"product_price\": \"The price of the product\",\n \"product_brand\": \"The brand of the product\",\n \"promo\": true,\n \"promo_percent\": 25\n}]", "passthroughBinaryImages": true}, "promptType": "define", "hasOutputParser": true}, "typeVersion": 1.7}, {"id": "f4acf278-edec-4bb4-a7cb-1e3c32a6ef4a", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1360, 1160], "parameters": {"color": 7, "width": 364.**************, "height": 357.10392441860495, "content": "## HTML-Scraping Tool Trigger\n\nThis **node serves as the entry point for the HTML scraping tool. \n\nIt is triggered by the **AI Agent only when it fails to extract data** from the screenshot. The **URL** is sent as a **parameter for the query**."}, "typeVersion": 1}, {"id": "79f7b4db-57f1-4004-88b3-51cfcfe9884e", "name": "HTML-Scraping Tool", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1480, 1360], "parameters": {}, "typeVersion": 1}, {"id": "94aa7169-30b5-49dd-864a-be2eabbf85d3", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1760, 1160], "parameters": {"color": 7, "width": 364.**************, "height": 357.10392441860495, "content": "## Set Fields - From AI Agent Query\n\nThis node sets the fields from the AI Agent’s query. \n\nIn this template, the only field configured is **url**.\n"}, "typeVersion": 1}, {"id": "f2615921-d060-410b-aef4-cd484edb2897", "name": "Set fields - from AI agent query", "type": "n8n-nodes-base.set", "position": [1880, 1360], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "c53c5ed2-9c7b-4365-9953-790264c722ab", "name": "url", "type": "string", "value": "={{ $json.query }}"}]}}, "typeVersion": 3.4}, {"id": "807e263a-97ce-4369-9ad0-8f973fc8dcc9", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [2180, 1160], "parameters": {"color": 7, "width": 364.**************, "height": 357.10392441860495, "content": "## ScrapingBee - Get Page HTML\n\nThis node utilizes the ScrapingBee API to **retrieve the HTML of the webpage**.\n"}, "typeVersion": 1}, {"id": "1cd32b9d-b07e-4dbb-9418-a99019c9deae", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [2600, 1160], "parameters": {"color": 7, "width": 364.**************, "height": 357.10392441860495, "content": "## HTML to Markdown\n\nThis node **converts the HTML from the previous node** into Markdown format, **helping to save tokens**. \n\nThe converted **Markdown is then automatically sent to the AI Agent** through this node.\n"}, "typeVersion": 1}, {"id": "3b9096d1-ab5a-48a8-90ee-465483881d95", "name": "HTML to Markdown", "type": "n8n-nodes-base.markdown", "position": [2740, 1360], "parameters": {"html": "={{ $json.data }}", "options": {}}, "typeVersion": 1}, {"id": "966ad92a-ddda-4fb9-86ac-9c62f47dfc37", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [-880.9927663601949, 0], "parameters": {"width": 829.9937466197946, "height": 646.0101744186061, "content": "# ✨ Vision-Based AI Agent Scraper - with Google Sheets, ScrapingBee, and Gemini\n\n## Important notes :\n### Check legal regulations: \nThis workflow involves scraping, so make sure to check the legal regulations around scraping in your country before getting started. Better safe than sorry!\n\n## Workflow description\nThis workflow leverages a **vision-based AI Agent**, integrated with Google Sheets, ScrapingBee, and the Gemini-1.5-Pro model, to **extract structured data from webpages**. The AI Agent primarily **uses screenshots for data extraction** but switches to HTML scraping when necessary, ensuring high accuracy. \n\nKey features include: \n- **Google Sheets Integration**: Manage URLs to scrape and store structured results. \n- **ScrapingBee**: Capture full-page screenshots and retrieve HTML data for fallback extraction. \n- **AI-Powered Data Parsing**: Use Gemini-1.5-Pro for vision-based scraping and a Structured Output Parser to format extracted data into JSON. \n- **Token Efficiency**: HTML is converted to Markdown to optimize processing costs.\n\nThis template is designed for e-commerce scraping but can be customized for various use cases. \n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "cf87b8bb-6218-4549-831f-02ff4be611eb", "connections": {"Split Out": {"main": [[{"node": "Google Sheets - Create Rows", "type": "main", "index": 0}]]}, "Set fields": {"main": [[{"node": "ScrapingBee - Get page screenshot", "type": "main", "index": 0}]]}, "HTML-Scraping Tool": {"main": [[{"node": "Set fields - from AI agent query", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Vision-based Scraping Agent", "type": "ai_languageModel", "index": 0}]]}, "HTML-based Scraping Tool": {"ai_tool": [[{"node": "Vision-based Scraping Agent", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Vision-based Scraping Agent", "type": "ai_outputParser", "index": 0}]]}, "ScrapingBee- Get page HTML": {"main": [[{"node": "HTML to Markdown", "type": "main", "index": 0}]]}, "Vision-based Scraping Agent": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Google Sheets - Get list of URLs": {"main": [[{"node": "Set fields", "type": "main", "index": 0}]]}, "Set fields - from AI agent query": {"main": [[{"node": "ScrapingBee- Get page HTML", "type": "main", "index": 0}]]}, "ScrapingBee - Get page screenshot": {"main": [[{"node": "Vision-based Scraping Agent", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Google Sheets - Get list of URLs", "type": "main", "index": 0}]]}}}