{"name": "Customer Support Agent Template", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "81fb02a2-8185-4eac-b1f6-221861465455", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": "YOUR_GMAIL_OAUTH2_CREDENTIAL"}}, {"parameters": {"inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "Customer Support", "description": "An email that is related to helping out a customer. They may be asking questions about our policies or questions about our products or services."}, {"category": "Other", "description": "Any email that is not customer support related"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1.1, "position": [220, 0], "id": "b0bdf9df-cc30-4c2d-9365-f9952ec5bb3b", "name": "Text Classifier"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [100, 220], "id": "cf4404da-5b10-4d19-a893-b0525ca4c07b", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": "YOUR_OPENROUTER_API_CREDENTIAL"}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [600, 160], "id": "b682e52d-f2c0-4cb0-bb74-617222d4953a", "name": "No Operation, do nothing"}, {"parameters": {"promptType": "define", "text": "={{ $('G<PERSON> Trigger').item.json.text }}", "options": {"systemMessage": "# Overview\nYou are a customer support agent at [YOUR_COMPANY_NAME]. Your job is to respond to incoming emails with relevant information using your knowledgebase.\n\n## Instructions\n- Your email should be friendly and use emojis\n- Sign off as [YOUR_AGENT_NAME] from [YOUR_COMPANY_NAME]"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [720, -400], "id": "cd3c884e-7008-4b70-9ca5-e9390a65d34d", "name": "AI Agent"}, {"parameters": {"mode": "retrieve-as-tool", "toolDescription": "Call this tool to access policy and FAQ information", "pineconeIndex": {"__rl": true, "value": "YOUR_PINECONE_INDEX", "mode": "list"}, "options": {"pineconeNamespace": "FAQ"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.3, "position": [1240, -80], "id": "7e2516ca-315d-491f-a2ec-5186cdd5170c", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": "YOUR_PINECONE_API_CREDENTIAL"}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [960, 20], "id": "d9a4d74e-0124-446d-8893-47e28bb0133c", "name": "Embeddings OpenAI", "credentials": {"openAiApi": "YOUR_OPENAI_API_CREDENTIAL"}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [660, -180], "id": "e41557cb-0cbb-42d9-a368-969ae4cf7d2e", "name": "OpenAI Chat Model", "credentials": {"openAiApi": "YOUR_OPENAI_API_CREDENTIAL"}}, {"parameters": {"operation": "reply", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "emailType": "text", "message": "={{ $('AI Agent').item.json.output }}", "options": {"appendAttribution": false, "replyToSenderOnly": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1640, -340], "id": "ed709d5f-aec7-44eb-bdaa-63c93d3a95d8", "name": "G<PERSON> Reply", "credentials": {"gmailOAuth2": "YOUR_GMAIL_OAUTH2_CREDENTIAL"}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1840, -320], "id": "1a326f2b-02f2-4bd0-bf1f-e53a36040a94", "name": "No Operation, do nothing1"}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "labelIds": ["YOUR_GMAIL_LABEL_ID"]}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1080, -400], "id": "520c43ea-e626-4d15-bc22-1a9c7392540e", "name": "Gmail Add Label", "credentials": {"gmailOAuth2": "YOUR_GMAIL_OAUTH2_CREDENTIAL"}}], "connections": {"Gmail Trigger": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Pinecone Vector Store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Gmail Add Label", "type": "main", "index": 0}]]}, "Gmail Reply": {"main": [[{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "Gmail Add Label": {"main": [[{"node": "G<PERSON> Reply", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "meta": {"templateCredsSetupCompleted": false}, "tags": []}