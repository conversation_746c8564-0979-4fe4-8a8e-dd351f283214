{"name": "Customer Service Agent", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1120, 980], "id": "24ba7229-bb81-45bc-8c79-a1c8ed909cc0", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "sYGWHRTm0aBGym7M", "name": "OpenAi account"}}}, {"parameters": {"dataType": "binary", "textSplittingMode": "custom", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1.1, "position": [1140, 1180], "id": "9da30689-2ea9-4421-8cc3-52435ca5095c", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [1520, 1040], "id": "78d19516-80a0-4e37-9c29-8ee056089187", "name": "Recursive Character Text Splitter"}, {"parameters": {"operation": "download", "fileId": "={{ $json.id }}"}, "type": "n8n-nodes-base.microsoftOneDrive", "typeVersion": 1, "position": [1000, 760], "id": "17f3249b-5684-4124-a9d6-48be61d8e146", "name": "Download a file", "credentials": {"microsoftOneDriveOAuth2Api": {"id": "vJlLbbop0qwPccHx", "name": "Microsoft Drive account"}}}, {"parameters": {"resource": "folder", "folderId": "={{ $json.id }}"}, "type": "n8n-nodes-base.microsoftOneDrive", "typeVersion": 1, "position": [780, 780], "id": "a45618f5-1074-4b6d-8999-8c33d0753254", "name": "Get items in a folder", "credentials": {"microsoftOneDriveOAuth2Api": {"id": "Aez11hvg2W20Xbwf", "name": "Microsoft Drive account 2"}}}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [1300, 760], "id": "f6c9e2df-e56e-4d03-a2e1-34c222c85e7d", "name": "Supabase Vector Store", "credentials": {"supabaseApi": {"id": "MLQEf4Iu5sXFOsXg", "name": "Supabase account"}}}, {"parameters": {"content": " # Rag / KB retrival ", "height": 720, "width": 1240, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [420, 680], "typeVersion": 1, "id": "6ba46685-f3d8-46ed-9ffe-d8111e528018", "name": "Sticky Note2"}, {"parameters": {"content": "# Customer service agent\n\n*  ## Looks up customer information In CRM \n\n*  ## creates contact's & updates them inside CRM \n\n*  ## Creates & retrives tickets \n\n*  ## Uses RAG for memory and KB\n\n*  ## Sends internal notifications to team members", "height": 440, "width": 1200}, "type": "n8n-nodes-base.stickyNote", "position": [-260, 140], "typeVersion": 1, "id": "ee799648-cf1b-4859-807c-33885abeb0d8", "name": "Sticky Note3"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.messages[0].from }}"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-920, 1020], "id": "2c8c6154-98bb-42ba-b5aa-33b57a85cac1", "name": "Postgres Chat Memory1", "credentials": {"postgres": {"id": "p0WrWpAKM8uhLC6V", "name": "Postgres account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1080, 1020], "id": "d00deb9f-f0d2-4351-aeb8-fa5dffdb3d23", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "sYGWHRTm0aBGym7M", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [-260, 1200], "id": "966a685a-6970-414f-a698-6d19d947b49c", "name": "Embeddings OpenAI5", "credentials": {"openAiApi": {"id": "sYGWHRTm0aBGym7M", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.messages[0].text.body }}", "options": {"systemMessage": "=Role:\nYou are \"OptiSupply AI Chatbot,\" a friendly and efficient customer service agent for an e-commerce supplement company, interacting via website live chat.\n\nTask:\nYour primary task is to assist customers, manage contact information, and create sales opportunities after a conversation is resolved or handed off.\n\nSpecifics:\n\nGreet & Identify: Start with a warm greeting. Ask if the customer is new or existing.\n\nInformation Gathering:\n\nFor new customers, request their name, email and phone number. Once you have collected there information Use the \"Create or update a contact in HubSpot\" tool to add their details, and create the contact. \n\nFor existing customers, ask for their email to search the contact. Use the \"Search contacts in HubSpot\" tool to search for there information. Once you have there information also search for any tickets using the \"Get many tickets in HubSpot\" tool and return there latest ticket, using the ID you have used to get the contact. If there is a ticket confirm if there issue is related to there previous ticket. If necessary, use \"Create or update a contact in HubSpot\" to ensure their information is current.\n\nAddress Inquiry & Resolve/Handoff:\n\nPolitely ask the reason for their chat.\n\nProvide information or guide them using the \"Supabase Vector Store Website Agent\" for queries or product information.\n\nconverse with the customer using the knowledge base, until you have successfully resolve the issue or inquiry \n\nIf the issue cannot be resolved, or requires human intervention (e.g., complex order issues, refunds), use the \"Send a text message in Telegram\" tool to alert a human agent, summarizing the customer's need.\n\nPost-Resolution Opportunity Creation:\n\nOnly after the customer's issue has been resolved, or the conversation has been handed off to a human agent, use the \"Send a text message in Telegram\" tool for internal notifications about the resolved issue or successful handoff. You Must then use the \"Create a ticket in HubSpot\" tool to create a new customer support ticket\n\nContext:\nThe AI operates within a website live chat, integrated with HighLevel CRM for contact and opportunity management, Supabase for website content search, and Telegram for internal human agent notifications.\n\nMain Goal:\nTo provide excellent customer service by resolving inquiries, accurately managing customer data post-conversation, and effectively escalating complex issues to human agents.\n\nExample Conversation / Scenarios:\n\nScenario 1: New Customer, Product Inquiry (Resolved by AI)\n\nYou: Hi there! I'm OptiSupply AI Chatbot. How can I help you today?\nCustomer: Hi! I'm new here and looking for a good multivitamin.\nYou: Welcome! To get started, could you please tell me your name and email address?\nCustomer: My name is Alex and my <NAME_EMAIL>.\nYou: Thanks, Alex! I'll just save your details for future reference. ( Use \"Create contact in HighLevel\")\nYou: For multivitamins, I recommend our 'Daily Essentials Multi'. It's packed with key nutrients. (Uses Supabase Vector Store Website Agent to find product info)\nCustomer: Sounds good! Where can I find more details?\nYou: You can find it directly on our website here: ambotix.com. Is there anything else I can help you with today?\nCustomer: No, that's all for now. Thanks!\nYou: You're welcome! Have a great day!\n(Conversation ends. Create an opportunity in HighLevel for \"Multivitamin Inquiry - Alex\".)\n(uses \"Send a text message in Telegram2\" tool with reason for customer enquiry)\n\nNotes:\n\nConciseness: Keep responses brief and to the point.\n\nTool Referencing: Explicitly mention the tool's conceptual action within the conversation flow (e.g., \"I'll just save your details,\" \"I'm checking your order now\") without revealing tool names.\n\nDelayed Opportunity/Internal Notification: The \"Send a text message in Telegram\" for resolution notifications are executed after the conversation is considered resolved or handed off, not during the live interaction.\n\nWorkflow Diagram (as reference for tools):\n\nCreate contact in HighLevel: Used for new customer details.\n\nUpdate a contact in HighLevel: Used for existing customer detail updates.\n\nSupabase Vector Store Website Agent: Used for fetching product/website information.\n\nCreate an opportunity in HighLevel: Used after resolution/handoff for sales leads.\n\nSend a text message in Telegram: Used for handoffs to humans during conversation, and after resolution/handoff for internal notifications."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-580, 740], "id": "a5291969-8a7d-47f5-98c6-d8b8e083915c", "name": "Website Chat Agent1"}, {"parameters": {"chatId": "**********", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Text', ``, 'string') }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegramTool", "typeVersion": 1.2, "position": [120, 1020], "id": "d4665833-0aee-4f58-9e18-a4fa8f4c8290", "name": "Send a text message in Telegram1", "webhookId": "c34f215f-7302-45ee-b0a4-28591cf934a7", "credentials": {"telegramApi": {"id": "0nTDcubyIXizTNHy", "name": "Telegram account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolDescription": "Access and utilise this tool for comprehensive information on products, pricing, company details, policies, and procedures for customer service. ", "tableName": {"__rl": true, "value": "documents", "mode": "list", "cachedResultName": "documents"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1.3, "position": [-200, 1020], "id": "de2a495b-38e9-466f-aa50-96b367fafb25", "name": "Supabase Vector Store Website Agent1", "credentials": {"supabaseApi": {"id": "MLQEf4Iu5sXFOsXg", "name": "Supabase account"}}}, {"parameters": {"authentication": "oAuth2", "email": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Email', ``, 'string') }}", "additionalFields": {"firstName": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('First_Name', ``, 'string') }}", "mobilePhoneNumber": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Mobile_Phone_Number', ``, 'string') }}"}, "options": {"resolveData": false}}, "type": "n8n-nodes-base.hubspotTool", "typeVersion": 2.1, "position": [-780, 1020], "id": "d6d8237a-81f5-470d-9370-e2f16189a458", "name": "Create or update a contact in HubSpot1", "credentials": {"hubspotOAuth2Api": {"id": "oYo9azzl0HqWz9Yq", "name": "HubSpot account 3"}}}, {"parameters": {"authentication": "oAuth2", "resource": "ticket", "pipelineId": "0", "stageId": "1", "ticketName": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Ticket_Name', ``, 'string') }}", "additionalFields": {"associatedContactIds": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Contact_Names_or_IDs', ``, 'string') }}", "description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}"}}, "type": "n8n-nodes-base.hubspotTool", "typeVersion": 2.1, "position": [-640, 1020], "id": "73c996ec-39c7-41ff-b730-5f626e656b31", "name": "Create a ticket in HubSpot1", "credentials": {"hubspotOAuth2Api": {"id": "7v9z1QuX5KXemgYP", "name": "HubSpot account"}}}, {"parameters": {"authentication": "oAuth2", "operation": "search", "returnAll": true, "additionalFields": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Query', ``, 'string') }}"}}, "type": "n8n-nodes-base.hubspotTool", "typeVersion": 2.1, "position": [-480, 1020], "id": "986e7744-636d-4887-b49a-81b52fb123de", "name": "Search contacts in HubSpot1", "credentials": {"hubspotOAuth2Api": {"id": "7v9z1QuX5KXemgYP", "name": "HubSpot account"}}}, {"parameters": {"authentication": "oAuth2", "resource": "ticket", "operation": "getAll", "returnAll": true, "additionalFields": {"properties": ["content", "subject"]}}, "type": "n8n-nodes-base.hubspotTool", "typeVersion": 2.1, "position": [-340, 1020], "id": "2caae2d7-7ba6-417e-b701-7fdef3164152", "name": "Get many tickets in HubSpot1", "credentials": {"hubspotOAuth2Api": {"id": "7v9z1QuX5KXemgYP", "name": "HubSpot account"}}}, {"parameters": {"content": "# Whats App Agent ", "height": 720, "width": 1410, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1140, 680], "typeVersion": 1, "id": "c3d3c846-64c1-4e2c-8a57-5130a3f65e89", "name": "Sticky Note1"}, {"parameters": {"updates": ["messages"], "options": {"messageStatusUpdates": ["all"]}}, "type": "n8n-nodes-base.whatsAppTrigger", "typeVersion": 1, "position": [-980, 780], "id": "76a98069-4c8c-4583-bfcd-78747f7b1e12", "name": "<PERSON><PERSON><PERSON><PERSON>", "webhookId": "7bdb70c5-7478-4012-abea-d7f55046a802", "credentials": {"whatsAppTriggerApi": {"id": "cPRPaRg9PouTf6zU", "name": "WhatsApp OAuth account 2"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.contacts[0].wa_id }}", "textBody": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [-100, 740], "id": "9cc9fe8a-b5fa-4004-a6c4-d323dec6e7bc", "name": "Send message and wait for response", "webhookId": "d2925c79-610a-459a-bde8-e4b2cf2dc019", "credentials": {"whatsAppApi": {"id": "bDwixc3XnAu7H7un", "name": "WhatsApp account 5"}}}, {"parameters": {"resource": "folder", "operation": "search", "query": "n8n knowledge base folder 1"}, "type": "n8n-nodes-base.microsoftOneDrive", "typeVersion": 1, "position": [480, 800], "id": "a2159a33-30d7-410f-a698-b76b56236db0", "name": "Search a folder", "credentials": {"microsoftOneDriveOAuth2Api": {"id": "Aez11hvg2W20Xbwf", "name": "Microsoft Drive account 2"}}}], "pinData": {}, "connections": {"Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Supabase Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Download a file": {"main": [[{"node": "Supabase Vector Store", "type": "main", "index": 0}]]}, "Get items in a folder": {"main": [[{"node": "Download a file", "type": "main", "index": 0}]]}, "Postgres Chat Memory1": {"ai_memory": [[{"node": "Website Chat Agent1", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Website Chat Agent1", "type": "ai_languageModel", "index": 0}]]}, "Embeddings OpenAI5": {"ai_embedding": [[{"node": "Supabase Vector Store Website Agent1", "type": "ai_embedding", "index": 0}]]}, "Send a text message in Telegram1": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "Supabase Vector Store Website Agent1": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "Create or update a contact in HubSpot1": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "Create a ticket in HubSpot1": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "Search contacts in HubSpot1": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "Get many tickets in HubSpot1": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Website Chat Agent1", "type": "main", "index": 0}]]}, "Website Chat Agent1": {"main": [[{"node": "Send message and wait for response", "type": "main", "index": 0}]]}, "Search a folder": {"main": [[{"node": "Get items in a folder", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "5d97d5d1-805e-4fd8-a21e-751a0e53045d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5e83950c16f424931a11129d242bc57414727cd7495fa9f2a8ca68a3f953658d"}, "id": "N2vcr2FblGHF1Sn9", "tags": []}