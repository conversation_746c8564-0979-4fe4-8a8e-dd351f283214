{"nodes": [{"parameters": {"formTitle": " Job Finder", "formFields": {"values": [{"fieldLabel": "City", "requiredField": true}, {"fieldLabel": "Job Title", "requiredField": true}, {"fieldLabel": "Country", "requiredField": true}, {"fieldLabel": "Job_type (Optional)", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Full-Time"}, {"option": "Part-Time"}, {"option": "Remote"}, {"option": "WFH"}, {"option": "Contract"}, {"option": "Internship"}, {"option": "Freelance"}]}}]}, "options": {}}, "id": "ccb16093-bf80-460b-ba5c-479062b2e965", "name": "Triggers workflow from job search form", "type": "n8n-nodes-base.formTrigger", "position": [432, 832], "webhookId": "37d0e84c-0cda-47f8-826d-9ae5b22ba1d5", "typeVersion": 2.2}, {"parameters": {"content": "Make A Copy Of This Sheet\n(https://docs.google.com/spreadsheets/d/1FmjpyNjus0tdN9hU9e-EsFcKl-W4EdWq1PYfIvGu8Ws/edit?gid=0#gid=0)", "height": 140, "width": 520, "color": 7}, "id": "e058f622-138b-4a44-8be6-a212621a1059", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [368, 240], "typeVersion": 1}, {"parameters": {"content": "📝 Trigger – User fills job title, city, country, job type", "height": 260, "width": 260}, "id": "e1151c95-cdd2-4e28-be8d-4631364a71fc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [368, 720], "typeVersion": 1}, {"parameters": {"content": "🧠 Format Input – Prepares input for Indeed scraping\n", "height": 300, "color": 3}, "id": "cf473a7d-be91-4b1d-8111-3fb7183f2436", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [832, 992], "typeVersion": 1}, {"parameters": {"jsCode": "const city = $json[\"City\"] || \"\";\nconst jobTitle = $json[\"Job Title\"] || \"\";\nconst country = $json[\"Country\"] || \"\";\nconst jobType = $json[\"Job_type (Optional)\"] || \"\";\n\nreturn [\n  {\n    json: {\n      input: [\n        {\n          country: country,\n          domain: country === \"FR\" ? \"fr.indeed.com\" : \"indeed.com\",\n          keyword_search: jobTitle,\n          location: city,\n          date_posted: \"Last 7 days\"\n        }\n      ],\n      custom_output_fields: [\n        \"jobid\",\n        \"company_name\",\n        \"date_posted_parsed\",\n        \"job_title\",\n        \"description_text\",\n        \"benefits\",\n        \"qualifications\",\n        \"location\",\n        \"salary_formatted\",\n        \"company_rating\",\n        \"company_reviews_count\",\n        \"country\",\n        \"date_posted\",\n        \"description\",\n        \"region\",\n        \"company_link\",\n        \"company_website\",\n        \"domain\",\n        \"apply_link\",\n        \"srcname\",\n        \"url\",\n        \"is_expired\",\n        \"discovery_input\",\n        \"job_location\",\n        \"job_description_formatted\",\n        \"logo_url\",\n        \"timestamp\",\n        \"warning\",\n        \"error\",\n        \"error_code\",\n        \"warning_code\"\n      ]\n    }\n  }\n];\n"}, "id": "5b05e06f-df14-470a-8808-e4efa608f01d", "name": "Formats form input for Bright Data Indeed API1", "type": "n8n-nodes-base.code", "position": [880, 1088], "typeVersion": 2}, {"parameters": {"content": "📤 Indeed API Call – Start job search on Indeed\n", "height": 280, "width": 260, "color": 2}, "id": "e361d724-e78c-448e-9421-50d3524f885b", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [848, 416], "typeVersion": 1}, {"parameters": {"method": "POST", "url": "https://api.brightdata.com/datasets/v3/trigger", "sendQuery": true, "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_lpfll7v5hcqtkxl6l"}, {"name": "include_errors", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "keyword"}, {"name": "limit_per_input", "value": "2"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"input\": [\n    {\n      \"location\": \"{{$node[\"Triggers workflow from job search form\"].json[\"City\"] ? $node[\"Triggers workflow from job search form\"].json[\"City\"].trim() : \"\"}}\",\n      \"keyword\": \"{{$node[\"Triggers workflow from job search form\"].json[\"Job Title\"] ? $node[\"Triggers workflow from job search form\"].json[\"Job Title\"].trim() : \"\"}}\",\n      \"country\": \"{{$node[\"Triggers workflow from job search form\"].json[\"Country\"] ? $node[\"Triggers workflow from job search form\"].json[\"Country\"].slice(0, 2).toUpperCase() : \"\"}}\",\n      \"time_range\": \"Past week\",\n      \"job_type\": \"{{ [\"full-time\", \"part-time\", \"internship\", \"contract\", \"temporary\"].includes($node[\"Triggers workflow from job search form\"].json[\"Job_type\"]?.toLowerCase()) ? $node[\"Triggers workflow from job search form\"].json[\"Job_type\"] : \"\" }}\"\n    }\n  ],\n  \"custom_output_fields\": [\n    \"url\",\n    \"job_posting_id\",\n    \"job_title\",\n    \"company_name\",\n    \"company_id\",\n    \"job_location\",\n    \"job_summary\",\n    \"job_seniority_level\",\n    \"job_function\",\n    \"job_employment_type\",\n    \"job_industries\",\n    \"job_base_pay_range\",\n    \"company_url\",\n    \"job_posted_time\",\n    \"job_num_applicants\",\n    \"discovery_input\",\n    \"apply_link\",\n    \"country_code\",\n    \"title_id\",\n    \"company_logo\",\n    \"job_posted_date\",\n    \"job_poster\",\n    \"application_availability\",\n    \"job_description_formatted\",\n    \"base_salary\",\n    \"timestamp\",\n    \"error\",\n    \"error_code\",\n    \"warning\",\n    \"warning_code\"\n  ]\n}\n", "options": {}}, "id": "cb22ebf9-28a8-499a-9297-5838b7af3065", "name": "Triggers job scraping on Indeed via Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [928, 496], "typeVersion": 4.2}, {"parameters": {"content": "⏳ Check Indeed Status – Is Indeed data ready?\n", "height": 260, "color": 4}, "id": "3af3ac0d-aaed-4644-85cc-7589a9144e8c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1168, 432], "typeVersion": 1}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "options": {}}, "id": "dcf3882a-82e9-44e7-904b-167dd610bce3", "name": "Checks if Bright Data has completed Indeed scraping", "type": "n8n-nodes-base.httpRequest", "position": [1232, 496], "typeVersion": 4.2}, {"parameters": {"content": "✅ Is Indeed Ready? – Proceed or wait\n", "height": 280, "color": 6}, "id": "5da46121-ef66-4346-aa4a-20b4d4e6eb18", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [1472, 416], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "35ed620d-b5d5-4e97-bcc5-52b283d85616", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "ready"}]}, "options": {}}, "id": "001734bd-e0b4-4b98-bfd3-d260b7cf2c76", "name": "IF node: Is Indeed data ready?", "type": "n8n-nodes-base.if", "position": [1552, 496], "typeVersion": 2.2}, {"parameters": {"content": "⏱️ Wait – Retry check after 1 minute\n", "height": 240, "width": 260, "color": 7}, "id": "f232138a-f3d4-4aff-a670-6028571aa396", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1760, 656], "typeVersion": 1}, {"parameters": {"amount": 1, "unit": "minutes"}, "id": "31ae32e5-e3a5-49b1-831f-062c967b9230", "name": "Pauses for 1 minute if data is not yet ready", "type": "n8n-nodes-base.wait", "position": [1792, 704], "webhookId": "7047efad-de41-4608-b95c-d3e0203ef620", "typeVersion": 1.1}, {"parameters": {"content": "📊 Data Found? – Continue only if jobs found on Indeed\n", "height": 280, "width": 260, "color": 4}, "id": "1ffbbd02-5ad4-4543-bfa0-7366aff7f5b5", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1760, 352], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f01bd215-c406-493c-a6e4-2b8ec5686b44", "operator": {"type": "number", "operation": "notEquals"}, "leftValue": "={{ $json.records }}", "rightValue": "={{ 0 }}"}]}, "options": {}}, "id": "d5766191-a99c-412e-adc1-e948f2c8aefe", "name": "Checks if any Indeed job records exist", "type": "n8n-nodes-base.if", "position": [1840, 480], "typeVersion": 2.2}, {"parameters": {"content": "📥 Get Indeed Jobs – Fetch results from Bright Data\n\n", "height": 260, "width": 260}, "id": "0969f9b6-2f0e-4fdc-b7d1-f10797b92531", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2048, 368], "typeVersion": 1}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "options": {}}, "id": "0e6d740c-517e-4da8-a7b8-b43c3e34bae4", "name": "Fetches scraped Indeed data using snapshot ID", "type": "n8n-nodes-base.httpRequest", "position": [2128, 464], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://api.brightdata.com/datasets/v3/trigger", "sendQuery": true, "queryParameters": {"parameters": [{"name": "dataset_id", "value": "gd_l4dx9j9sscpvs7no2"}, {"name": "include_errors", "value": "true"}, {"name": "type", "value": "discover_new"}, {"name": "discover_by", "value": "keyword"}, {"name": "limit_per_input", "value": "=2"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"input\": {{ $json[\"input\"] ? JSON.stringify($json[\"input\"]) : \"[]\" }},\n  \"custom_output_fields\": {{ $json[\"custom_output_fields\"] ? JSON.stringify($json[\"custom_output_fields\"]) : \"{}\" }}\n}\n", "options": {}}, "id": "8da861b3-df7a-4c6c-bf39-c2b6c6f11029", "name": "Triggers job scraping on LinkedIn via Bright Data", "type": "n8n-nodes-base.httpRequest", "position": [1152, 1088], "typeVersion": 4.2}, {"parameters": {"content": "📤 LinkedIn API Call – Start job search on LinkedIn\n", "height": 300, "color": 5}, "id": "4f1a540c-f076-42b9-a105-bcd9336eaf70", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1088, 992], "typeVersion": 1}, {"parameters": {"content": "⏳ Check LinkedIn Status – Is LinkedIn data ready?\n", "height": 280, "width": 260, "color": 3}, "id": "be52a15f-3950-456f-b026-f9132b0241c0", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [1360, 992], "typeVersion": 1}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/progress/{{ $json.snapshot_id }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "options": {}}, "id": "79052900-596e-4880-946a-72aa636dfebd", "name": "Checks if LinkedIn scraping is completed", "type": "n8n-nodes-base.httpRequest", "position": [1440, 1088], "typeVersion": 4.2}, {"parameters": {"content": "✅ Is LinkedIn Ready? – Proceed or wait\n", "height": 280, "width": 220, "color": 6}, "id": "2819bb3c-f0d3-4a99-85e6-2902baac3b7d", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [1632, 992], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "or", "conditions": [{"id": "35ed620d-b5d5-4e97-bcc5-52b283d85616", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.status }}", "rightValue": "ready"}]}, "options": {}}, "id": "1814afd6-f9b6-4448-ab41-20ce0552e380", "name": "IF node: Is LinkedIn data ready?", "type": "n8n-nodes-base.if", "position": [1680, 1088], "typeVersion": 2.2}, {"parameters": {"content": "⏱️ Wait – Retry LinkedIn status after 1 min\n", "height": 240, "color": 7}, "id": "e4efd227-5070-4d08-ad2e-a769c27733e3", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [1920, 1360], "typeVersion": 1}, {"parameters": {"amount": 1, "unit": "minutes"}, "id": "514b0926-fe1b-4989-a50e-f3210716b8d2", "name": "Waits before rechecking LinkedIn scraping", "type": "n8n-nodes-base.wait", "position": [1984, 1344], "webhookId": "7047efad-de41-4608-b95c-d3e0203ef620", "typeVersion": 1.1}, {"parameters": {"content": "📊 LinkedIn Data Found? – If yes, continue\n", "height": 260, "width": 280, "color": 2}, "id": "d05d5230-2d8b-4736-aeee-c529b958da77", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [1888, 1008], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f01bd215-c406-493c-a6e4-2b8ec5686b44", "operator": {"type": "number", "operation": "notEquals"}, "leftValue": "={{ $json.records }}", "rightValue": "={{ 0 }}"}]}, "options": {}}, "id": "341fc5a2-5e34-43fb-a84f-0f2451bcbe1c", "name": "Checks if any LinkedIn job records exist", "type": "n8n-nodes-base.if", "position": [2000, 1072], "typeVersion": 2.2}, {"parameters": {"url": "=https://api.brightdata.com/datasets/v3/snapshot/{{ $json.snapshot_id }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "format", "value": "json"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer BRIGHT_DATA_API_KEY"}]}, "options": {}}, "id": "ebec13ab-6727-4ba5-b2d6-efc0c9982200", "name": "Fetches scraped LinkedIn data using snapshot ID", "type": "n8n-nodes-base.httpRequest", "position": [2288, 1056], "typeVersion": 4.2}, {"parameters": {"content": "📥 Get LinkedIn Jobs – Fetch LinkedIn job listings\n\n", "height": 260, "width": 260, "color": 6}, "id": "1263996f-9d36-41d9-b97b-5e05dadf304a", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [2208, 992], "typeVersion": 1}, {"parameters": {"content": "🔗 <PERSON><PERSON> – <PERSON><PERSON><PERSON> both job sources\n", "height": 260, "width": 280, "color": 3}, "id": "8ed92618-ca54-444f-a0ce-732000e8fb56", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [2736, 624], "typeVersion": 1}, {"parameters": {}, "id": "6c63a361-3288-4a22-b17e-86cca223baf9", "name": "Combines Indeed + LinkedIn job results", "type": "n8n-nodes-base.merge", "position": [2816, 688], "typeVersion": 3.2}, {"parameters": {"content": "📄 Save to Sheet – Add job data to “Compare” sheet\n\n", "height": 260, "color": 4}, "id": "8bfe2047-5af4-46cc-aa57-021ee6b995f5", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [3040, 624], "typeVersion": 1}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": "1xkNBckPDGf4YR74bJQN07tAr3qlEoA-70pQc63nBqZ8", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xkNBckPDGf4YR74bJQN07tAr3qlEoA-70pQc63nBqZ8/edit?usp=drivesdk", "cachedResultName": "Job Finder sheet"}, "sheetName": {"__rl": true, "mode": "list", "value": 74196869, "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1xkNBckPDGf4YR74bJQN07tAr3qlEoA-70pQc63nBqZ8/edit#gid=74196869", "cachedResultName": "Compare"}, "columns": {"value": {"Salary ": "={{ $json.salary_formatted }}", "Location": "={{ $json.discovery_input.location }}", "Job Title": "={{ $json.job_title }}", "Job-Type ": "={{ $json.job_location }}", "Apply Link": "={{ $json.url }}", "Job Detail": "={{ $json.description_text }}", "Company Name ": "={{ $json.company_name }}"}, "schema": [{"id": "Company Name ", "type": "string", "display": true, "required": false, "displayName": "Company Name ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job Title", "type": "string", "display": true, "required": false, "displayName": "Job Title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Location", "type": "string", "display": true, "required": false, "displayName": "Location", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job Detail", "type": "string", "display": true, "required": false, "displayName": "Job Detail", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Apply Link", "type": "string", "display": true, "required": false, "displayName": "Apply Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job No. Application", "type": "string", "display": true, "required": false, "displayName": "Job No. Application", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Salary ", "type": "string", "display": true, "required": false, "displayName": "Salary ", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Job-Type ", "type": "string", "display": true, "required": false, "displayName": "Job-Type ", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "7b5d7dec-9ee3-4e27-995b-5f17168b0d11", "name": "Saves final job list to Google Sheet", "type": "n8n-nodes-base.googleSheets", "position": [3072, 688], "typeVersion": 4.6, "credentials": {}}], "connections": {"Triggers workflow from job search form": {"main": [[{"node": "Formats form input for Bright Data Indeed API1", "type": "main", "index": 0}, {"node": "Triggers job scraping on Indeed via Bright Data", "type": "main", "index": 0}]]}, "Formats form input for Bright Data Indeed API1": {"main": [[{"node": "Triggers job scraping on LinkedIn via Bright Data", "type": "main", "index": 0}]]}, "Triggers job scraping on Indeed via Bright Data": {"main": [[{"node": "Checks if Bright Data has completed Indeed scraping", "type": "main", "index": 0}]]}, "Checks if Bright Data has completed Indeed scraping": {"main": [[{"node": "IF node: Is Indeed data ready?", "type": "main", "index": 0}]]}, "IF node: Is Indeed data ready?": {"main": [[{"node": "Checks if any Indeed job records exist", "type": "main", "index": 0}], [{"node": "Pauses for 1 minute if data is not yet ready", "type": "main", "index": 0}]]}, "Pauses for 1 minute if data is not yet ready": {"main": [[{"node": "Checks if Bright Data has completed Indeed scraping", "type": "main", "index": 0}]]}, "Checks if any Indeed job records exist": {"main": [[{"node": "Fetches scraped Indeed data using snapshot ID", "type": "main", "index": 0}]]}, "Fetches scraped Indeed data using snapshot ID": {"main": [[{"node": "Combines Indeed + LinkedIn job results", "type": "main", "index": 0}]]}, "Triggers job scraping on LinkedIn via Bright Data": {"main": [[{"node": "Checks if LinkedIn scraping is completed", "type": "main", "index": 0}]]}, "Checks if LinkedIn scraping is completed": {"main": [[{"node": "IF node: Is LinkedIn data ready?", "type": "main", "index": 0}]]}, "IF node: Is LinkedIn data ready?": {"main": [[{"node": "Checks if any LinkedIn job records exist", "type": "main", "index": 0}], [{"node": "Waits before rechecking LinkedIn scraping", "type": "main", "index": 0}]]}, "Waits before rechecking LinkedIn scraping": {"main": [[{"node": "Checks if LinkedIn scraping is completed", "type": "main", "index": 0}]]}, "Checks if any LinkedIn job records exist": {"main": [[{"node": "Fetches scraped LinkedIn data using snapshot ID", "type": "main", "index": 0}]]}, "Fetches scraped LinkedIn data using snapshot ID": {"main": [[{"node": "Combines Indeed + LinkedIn job results", "type": "main", "index": 1}]]}, "Combines Indeed + LinkedIn job results": {"main": [[{"node": "Saves final job list to Google Sheet", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "003c0ba7d159a939f933f410e94e211942ac56e758c92fbd77e0167e22be261d", "templateCredsSetupCompleted": true}}