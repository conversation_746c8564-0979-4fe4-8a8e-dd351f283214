{"name": "sdr2", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [280, 20], "id": "ee5e77cf-b2fe-4bcc-9e53-2551a8e4a82a", "name": "<PERSON>gger on Enriched Lead Row", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "jzOKoQHQ1fzJUKun", "name": "Google Sheets Trigger account"}}}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"mistralai/mixtral-8x7b\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant that summarizes companies.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Summarize this company in 2 sentences:\\n{{ $json[\\\"Company Description\\\"] }}\"\n    }\n  ]\n}\n", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 200], "id": "4819e0e0-cbd2-446d-ae3d-3d9e826fc8a8", "name": "Generate Business Summary (OpenRouter)"}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"mistralai/mixtral-8x7b\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful assistant that categorizes companies.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"What industry does this company belong to? Return one word:\\n{{ $json[\\\"Company Description\\\"] }}\"\n    }\n  ]\n}\n", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 200], "id": "5a6f024e-e6fc-4028-9ecb-b8e349789a54", "name": "Detect Industry Tag (OpenRouter)"}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"mistralai/mixtral-8x7b\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are an evaluator of company fit.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"On a scale of 1 to 10, how qualified is this company for B2B SaaS outreach? Return only a number.\\n{{ $json[\\\"Company Description\\\"] }}\"\n    }\n  ]\n}\n", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 200], "id": "7efb5f89-14c6-4966-a931-a1b3288f3656", "name": "Score Lead (1–10) (OpenRouter)"}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"mistralai/mixtral-8x7b\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a cold email copywriter.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Write a one-line personalized opener for a cold email to this company:\\n{{ $json[\\\"Company Description\\\"] }}\"\n    }\n  ]\n}\n", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 380], "id": "ef9d6ab4-4488-40ab-9a0c-b488b92099e8", "name": "Write Outreach Line (OpenRouter)"}, {"parameters": {"assignments": {"assignments": [{"id": "fa63dfcf-0dc7-471e-8e1e-0e2710e743e5", "name": "Business Summary", "value": "={{ $node[\"Generate Business Summary (OpenRouter)\"].json[\"choices\"][0][\"message\"][\"content\"] }}", "type": "string"}, {"id": "9ac0edc1-1190-4e45-9f40-581235625a27", "name": "Industry Tag", "value": "={{ $node[\"Detect Industry Tag (OpenRouter)\"].json[\"choices\"][0][\"message\"][\"content\"] }}", "type": "string"}, {"id": "dbd6d963-2ef1-4f0a-839b-13ca9f0ef6d7", "name": "Lead Score", "value": "={{ $node[\"Score Lead (1–10) (OpenRouter)\"].json[\"choices\"][0][\"message\"][\"content\"] }}", "type": "string"}, {"id": "e2063409-942d-4753-bd6d-c45542351c41", "name": "Outreach Line", "value": "={{ $node[\"Write Outreach Line (OpenRouter)\"].json[\"choices\"][0][\"message\"][\"content\"] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 380], "id": "a094b826-ee12-4312-8eb1-c1677a6a804c", "name": "Combine AI Outputs (Set)"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "name ", "displayName": "name ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contact details", "displayName": "contact details", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [440, 380], "id": "458a5d8e-cd26-497e-80b3-a3cfaebb3276", "name": "Update Sheet with AI Insights", "credentials": {"googleSheetsOAuth2Api": {"id": "5cGO5mtMhF2M4IKE", "name": "Google Sheets account"}}}, {"parameters": {"content": "## AI Insight Enrichment \n(Company → Summary, Industry, Score, Line)\n\n", "height": 580, "width": 680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, -20], "id": "6efccd1c-5fb8-4d84-840f-a31cff8e1e6d", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Trigger on Enriched Lead Row": {"main": [[{"node": "Generate Business Summary (OpenRouter)", "type": "main", "index": 0}]]}, "Generate Business Summary (OpenRouter)": {"main": [[{"node": "Detect Industry Tag (OpenRouter)", "type": "main", "index": 0}]]}, "Detect Industry Tag (OpenRouter)": {"main": [[{"node": "Score Lead (1–10) (OpenRouter)", "type": "main", "index": 0}]]}, "Score Lead (1–10) (OpenRouter)": {"main": [[{"node": "Write Outreach Line (OpenRouter)", "type": "main", "index": 0}]]}, "Write Outreach Line (OpenRouter)": {"main": [[{"node": "Combine AI Outputs (Set)", "type": "main", "index": 0}]]}, "Combine AI Outputs (Set)": {"main": [[{"node": "Update Sheet with AI Insights", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "68c79b5b-4b7d-4c08-8fa5-f6e040168e44", "meta": {"templateCredsSetupCompleted": true, "instanceId": "57a3103abaee943256a1daab7aebcb0a5de91f02c7de16853df57583eefbff08"}, "id": "cOph8sleZdF21NZa", "tags": []}