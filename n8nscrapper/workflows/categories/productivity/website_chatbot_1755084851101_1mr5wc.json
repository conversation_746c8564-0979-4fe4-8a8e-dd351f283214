{"name": "Website Chatbot", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.sessionId }}", "contextWindowLength": 20}, "id": "a31a7a29-be0b-4d08-92ca-10cf83e73b23", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [120, 1120], "typeVersion": 1.2}, {"parameters": {"options": {}}, "id": "983c3cf3-1c70-4f1a-8c5c-e6ad582c6f72", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [540, 820], "typeVersion": 1.1}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {"temperature": 0.4}}, "id": "7fc5a652-353b-44bb-8da8-b8d306fb8154", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [20, 1000], "typeVersion": 1}, {"parameters": {"toolDescription": "Call this tool to make the appointment, ensure you send the user email, name, company, reason for the meeting and the appointment start time and the date in ISO String format with timezone for <timezone>. When creating an appointment, always send JSON.", "method": "POST", "url": "https://graph.microsoft.com/v1.0/me/events", "authentication": "predefinedCredentialType", "nodeCredentialType": "microsoftOutlookOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "Content-Type", "valueProvider": "fieldValue", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n \"subject\": \"Meetings with <name> at <company>\",\n \"start\": {\n \"dateTime\": \"{dateStartTime}\",\n \"timeZone\": \"Europe/London\"\n },\n \"end\": {\n \"dateTime\": \"{dateEndTime}\",\n \"timeZone\": \"Europe/London\"\n },\n \"body\": {\n \"contentType\": \"HTML\",\n \"content\": \"{reason}\"\n },\n \"attendees\": [\n {\n \"emailAddress\": {\n \"address\": \"{email}\",\n \"name\": \"{name}\"\n },\n \"type\": \"required\"\n }\n ],\n \"location\": {\n \"displayName\": \"Online Meeting\"\n },\n \"isOnlineMeeting\": true,\n \"onlineMeetingProvider\": \"teamsForBusiness\",\n \"showAs\": \"busy\",\n \"categories\": [\n \"Meeting\"\n ]\n}", "placeholderDefinitions": {"values": [{"name": "dateStartTime", "description": "The date and start time of the appointment in toISOString format with timezone for Europe/London", "type": "string"}, {"name": "dateEndTime", "description": "The date and end time of the appointment in toISOString format, always 30 minutes after the dateStartTime, format with timezone for Europe/London", "type": "string"}, {"name": "reason", "description": "Detailed description of the meeting, will be sent to us and the customer", "type": "string"}, {"name": "email", "description": "The customers email address.", "type": "string"}, {"name": "name", "description": "The customers full name, must be second and last name", "type": "string"}]}}, "id": "1392d4f5-f07d-44d0-af0c-f96eea3e52e9", "name": "Make Appointment", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [480, 1080], "typeVersion": 1.1}, {"parameters": {}, "id": "cd51bbee-5d86-4e12-b918-7c23edca9369", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-720, 500], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "c0b6e779-0f7b-41f0-81f8-457f2b31ccfe", "name": "response", "type": "array", "value": "={{ $json.freeTimeSlots.toJsonString() }}"}]}, "options": {}}, "id": "4358dc2f-de1c-4d5b-8d65-0d0237955d4d", "name": "varResponse", "type": "n8n-nodes-base.set", "position": [240, 380], "typeVersion": 3.4}, {"parameters": {"jsCode": "// Input: An array with objects containing a 'value' array of events.\nconst businessHoursStart = \"08:00:00Z\"; // Business hours start time\nconst businessHoursEnd = \"17:30:00Z\"; // Business hours end time\n\nconst inputData = items[0].json.value; // Assuming the input data is in the 'value' array of the first item\n\n// Function to convert ISO datetime string to a Date object with specified time\nfunction getDateWithTime(dateString, time) {\n const datePart = new Date(dateString).toISOString().split(\"T\")[0]; // Extract the date part (YYYY-MM-DD)\n return new Date(`${datePart}T${time}`);\n}\n\n// Function to get day of the week from a date string\nfunction getDayOfWeek(dateString) {\n const daysOfWeek = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\n return daysOfWeek[new Date(dateString).getUTCDay()];\n}\n\n// Organise events by date\nconst eventsByDate = {};\ninputData.forEach(event => {\n const eventDate = new Date(event.start.dateTime).toISOString().split(\"T\")[0]; // Extract the date\n if (!eventsByDate[eventDate]) {\n eventsByDate[eventDate] = [];\n }\n if (event.showAs === \"busy\") {\n eventsByDate[eventDate].push({\n start: new Date(event.start.dateTime),\n end: new Date(event.end.dateTime),\n timeZone: event.start.timeZone // Add timeZone to the event object\n });\n }\n});\n\n// Find free slots within business hours for each date\nconst freeTimeSlots = [];\n\nfor (const [date, busyEvents] of Object.entries(eventsByDate)) {\n // Sort events by their start time\n busyEvents.sort((a, b) => a.start - b.start);\n\n // Define business start and end times for the current date\n const businessStart = getDateWithTime(date, businessHoursStart);\n const businessEnd = getDateWithTime(date, businessHoursEnd);\n\n let freeStart = businessStart;\n\n // Loop through busy events to find free slots\n for (const event of busyEvents) {\n if (freeStart < event.start) {\n // Add free slot if there's a gap between freeStart and the event start\n freeTimeSlots.push({\n date,\n dayOfWeek: getDayOfWeek(date), // Add day of the week key\n freeStart: freeStart.toISOString(),\n freeEnd: event.start.toISOString(),\n timeZone: event.timeZone // Add the timezone for the free slot\n });\n }\n // Move freeStart to the end of the current busy event\n freeStart = event.end;\n }\n\n // Check if there's free time after the last busy event until the end of business hours\n if (freeStart < businessEnd) {\n freeTimeSlots.push({\n date,\n dayOfWeek: getDayOfWeek(date), // Add day of the week key\n freeStart: freeStart.toISOString(),\n freeEnd: businessEnd.toISOString(),\n timeZone: busyEvents[0].timeZone // Use the timezone of the first event for consistency\n });\n }\n}\n\n// Output the free time slots\nreturn [{ json: { freeTimeSlots } }];\n"}, "id": "4b736230-b9d2-462c-a43b-c9b16bf5b484", "name": "freeTimeSlots", "type": "n8n-nodes-base.code", "position": [20, 380], "typeVersion": 2}, {"parameters": {"url": "=https://graph.microsoft.com/v1.0/me/calendarView", "authentication": "predefinedCredentialType", "nodeCredentialType": "microsoftOutlookOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "startDateTime", "value": "={{ new Date(new Date().setDate(new Date().getDate() + 2)).toISOString() }}"}, {"name": "endDateTime", "value": "={{ new Date(new Date().setDate(new Date().getDate() + 16)).toISOString() }}"}, {"name": "$top", "value": "50"}, {"name": "select", "value": "start,end,categories,importance,isAllDay,recurrence,showAs,subject,type"}, {"name": "orderby", "value": "start/dateTime asc"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Prefer", "value": "outlook.timezone=\"Europe/London\""}]}, "options": {}}, "id": "68e0f8b0-0242-44dc-930e-b3d3bd60e136", "name": "Get Events", "type": "n8n-nodes-base.httpRequest", "position": [-200, 380], "typeVersion": 4.2}, {"parameters": {"name": "Get_availability", "description": "Call this tool to check my calendar for availability before booking an appointment. This will result in all events for the next 2 weeks. Review all events and do not double book.", "workflowId": {"__rl": true, "mode": "list", "value": "KD21RG8VeXYDS2Vf", "cachedResultName": "Website Chatbot"}, "fields": {"values": [{"name": "route", "stringValue": "availability"}]}}, "id": "13b0771f-52cf-4bdc-8c25-7f152c50315b", "name": "Get Availability", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [420, 1320], "typeVersion": 1.2}, {"parameters": {"name": "Send_email", "description": "Call this tool when the customer wants to speak to a human, or is not ready to make an appointment or if the customer has questions outside of your remit. The tool will send an email to our founder, <insert name>. Always send the customer's full name, company and email address along with a detailed message about the enquiry. You must always gather project details.", "workflowId": {"__rl": true, "mode": "list", "value": "KD21RG8VeXYDS2Vf", "cachedResultName": "Website Chatbot"}, "fields": {"values": [{"name": "route", "stringValue": "message"}]}, "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"email\": \"the customer's email\",\n \"subject\": \"the subject of the email\",\n \"message\": \"The customer's enquiry, must be a detailed description of their enquiry\",\n \"name\": \"the customer's full name\",\n \"company\": \"the customer company name\"\n}"}, "id": "a1305a71-e23d-4daf-8bf5-739ca2e38a54", "name": "Send Message", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [280, 1320], "typeVersion": 1.2}, {"parameters": {"public": true, "mode": "webhook", "options": {"allowedOrigins": "*", "responseMode": "responseNode"}}, "id": "40344540-732a-4d25-a594-a488e5abd0b6", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-720, 840], "webhookId": "f406671e-c954-4691-b39a-66c90aa2f103", "typeVersion": 1}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "availability"}]}, "renameOutput": true, "outputKey": "availability"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "52fd844b-cc8d-471f-a56a-40e119b66194", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "message"}]}, "renameOutput": true, "outputKey": "message"}]}, "options": {}}, "id": "428acf6b-1328-4f28-a3bb-74d4da22f9ce", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [-500, 500], "typeVersion": 3.2}, {"parameters": {"assignments": {"assignments": [{"id": "0d2ad084-9707-4979-84e4-297d1c21f725", "name": "response", "type": "string", "value": "={{ $json }}"}]}, "options": {"ignoreConversionErrors": false}}, "id": "c384e9a3-4fb8-4066-a31f-9d7d4afacfe4", "name": "varMessageResponse", "type": "n8n-nodes-base.set", "position": [20, 620], "typeVersion": 3.4}, {"parameters": {"toRecipients": "<EMAIL>", "subject": "={{ $('Execute Workflow Trigger').item.json.query.subject }}", "bodyContent": "=<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\n <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n <title>New Webchat Customer Enquiry</title>\n <style type=\"text/css\">\n /* Client-specific styles */\n body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }\n table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }\n img { -ms-interpolation-mode: bicubic; }\n\n /* Reset styles */\n body { margin: 0; padding: 0; }\n img { border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; }\n table { border-collapse: collapse !important; }\n body { height: 100% !important; margin: 0; padding: 0; width: 100% !important; }\n\n /* iOS BLUE LINKS */\n a[x-apple-data-detectors] {\n color: inherit !important;\n text-decoration: none !important;\n font-size: inherit !important;\n font-family: inherit !important;\n font-weight: inherit !important;\n line-height: inherit !important;\n }\n\n /* Styles for Outlook and other email clients */\n .ExternalClass { width: 100%; }\n .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div { line-height: 100%; }\n \n /* Responsive styles */\n @media screen and (max-width: 600px) {\n .container { width: 100% !important; }\n .content { padding: 15px !important; }\n .field { padding: 10px !important; }\n .header h1 { font-size: 20px !important; }\n .header p { font-size: 12px !important; }\n }\n </style>\n</head>\n<body style=\"margin: 0; padding: 0; background-color: #f4f4f4;\">\n <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n <tr>\n <td>\n <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"600\" style=\"border-collapse: collapse; background-color: #ffffff;\">\n <tr>\n <td align=\"center\" bgcolor=\"#1a1a1a\" style=\"padding: 30px 0; background: linear-gradient(135deg, #1a1a1a 0%, #2d1f3d 100%);\">\n <h1 style=\"color: #ffffff; font-family: Arial, sans-serif; font-size: 24px; font-weight: 700; margin: 0; text-transform: uppercase; letter-spacing: 1px;\">New Customer Enquiry</h1>\n <p style=\"color: #ffffff; font-family: Arial, sans-serif; font-size: 14px; line-height: 20px; margin: 10px 0 0; opacity: 0.8;\">A potential client has reached out through our webchat</p>\n </td>\n </tr>\n <tr>\n <td style=\"padding: 20px;\">\n <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n <tr>\n <td style=\"padding: 15px; background-color: #f9f9f9; border: 1px solid #e0e0e0; border-radius: 8px;\">\n <p style=\"font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #6a1b9a; font-weight: bold; margin: 0 0 5px 0;\">FROM</p>\n <p style=\"font-family: Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333333; margin: 0;\">{{ $('Execute Workflow Trigger').item.json.query.name }}</p>\n </td>\n </tr>\n <tr><td height=\"20\"></td></tr>\n <tr>\n <td style=\"padding: 15px; background-color: #f9f9f9; border: 1px solid #e0e0e0; border-radius: 8px;\">\n <p style=\"font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #6a1b9a; font-weight: bold; margin: 0 0 5px 0;\">EMAIL</p>\n <p style=\"font-family: Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333333; margin: 0;\">{{ $('Execute Workflow Trigger').item.json.query.email }}</p>\n </td>\n </tr>\n <tr><td height=\"20\"></td></tr>\n <tr>\n <td style=\"padding: 15px; background-color: #f9f9f9; border: 1px solid #e0e0e0; border-radius: 8px;\">\n <p style=\"font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #6a1b9a; font-weight: bold; margin: 0 0 5px 0;\">COMPANY</p>\n <p style=\"font-family: Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333333; margin: 0;\">{{ $('Execute Workflow Trigger').item.json.query.company }}</p>\n </td>\n </tr>\n <tr><td height=\"20\"></td></tr>\n <tr>\n <td style=\"padding: 15px; background-color: #f9f9f9; border: 1px solid #e0e0e0; border-radius: 8px;\">\n <p style=\"font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #6a1b9a; font-weight: bold; margin: 0 0 5px 0;\">MESSAGE</p>\n <p style=\"font-family: Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333333; margin: 0;\">{{ $('Execute Workflow Trigger').item.json.query.message }}</p>\n </td>\n </tr>\n </table>\n </td>\n </tr>\n <tr>\n <td align=\"center\" bgcolor=\"#e90ebb\" style=\"padding: 20px; background: linear-gradient(135deg, #e90ebb 0%, #6a1b9a 100%);\">\n <p style=\"font-family: Arial, sans-serif; font-size: 14px; line-height: 20px; color: #ffffff; margin: 0;\">This enquiry was automatically generated from our website's chat interface.</p>\n </td>\n </tr>\n </table>\n </td>\n </tr>\n </table>\n</body>\n</html>", "additionalFields": {"importance": "High", "bodyContentType": "html"}}, "id": "d4100b38-7218-48ef-a048-fa8bfceb4451", "name": "Send Message1", "type": "n8n-nodes-base.microsoftOutlook", "position": [-200, 620], "typeVersion": 2, "webhookId": "653cd9bb-12f4-4ac8-9840-9138544d346a"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}", "options": {"systemMessage": "=You are an intelligent personal assistant to <PERSON>, Founder at nocodecreative.io (ai consultancy and software development agency) responsible for coordinating appointments and gathering relevant information from customers. Your tasks are to:\n\n- Understand when the customer is available by asking for suitable days and times (ensuring they are aware we are in a UK timezone)\n- Check the calendar to identify available slots that match their preferences. Pay attention to each event's start and end time and do not double book, you will be given all events for the next 14 days\n- Ask the customer what they would like to discuss during the appointment to ensure proper preparation.\n- Get the customer's name, company name and email address to book the appointment\n- Make the conversation friendly and natural. Confirm the appointment details with the customer and let them know I’ll be ready to discuss what they’d like.\n- After you have checked the calendar, book the appointment accordingly, without double booking. Confirm the customer's timezone and adjust the appointment for EU/London.\n- If the customer isn't ready to book, you can send an email for a human to respond to, ensure you gather a detailed enquiry from the customer including contact details and project information.Ensure the message contains enough information for a human to respond, always include project details, if the customer hasn't provided project details, ask.\n- Alwways suggest an appointment before sending a message, appointment are you primary goal, message are a fall back\n\nExample questions:\n\n\"Hi there! we'd love to help arrange a time that works for us to meet. Could you let us know which days and times are best for you? We’ll check the calendar and book in a suitable slot.\"\n\n\"Could you please let us know what you’d like to discuss during the appointment? This helps us prepare in advance and make our time together as productive as possible.\"\n\n\"Before we put you in touch with a human, please can you provide more information about the project you have in mind?\" //You must gather project info at all times, even if the enquiry is about pricing/costs.\n\nIf the time the customer suggests is not available, suggest the nearest alternative appointment based on existing events, do not book an appointment outside of freeTimeSlots\n\nImportant information:\n- All appointments need 48 hours' notice from {{ \n new Date().toLocaleString(\"en-GB\", { timeZone: \"Europe/London\", hour12: false })\n .split(\", \")[0].split(\"/\").reverse().join(\"-\") \n + \"T\" + new Date().toLocaleTimeString(\"en-GB\", { timeZone: \"Europe/London\", hour12: false }) + \":00.000Z\" \n}} (current date and time in the UK) // this is non-negotiable, but discuss with care and be friendly, only let the customer know this if required\n- Business hours are 8am - 6pm Monday to Friday only Europe/London timezone, ensure the customer is aware of this and help them book during UK hours, you must confirm their timezone to do this!\n- Do not book appointments on a Saturday or sunday\n- Do not book appointments outside of freeTimeSlots\n- Always check the next 14 days, and review all events before providing availability \n- All appointments are for a max of 30 minutes\n- You must never offer an appointment without checking the calendar, if you cannot check the calendar, you cannot book and must let the customer know you can not book an appointment right now.\n- Always offer the soonest appointment available if the customer's preferred time is unavailable\n- When confirming an appointment, be thankful and excited!\n- Initial 30 minute consultation are free of charge\n\n\nMessages and description:\n- When creating descriptions or sending messages, always ensure enough detail is provided for preparation, meaning you can ask follow-up questions to extract further information as required. For example, if a customer asks about pricing, gather some information about the project so our team can provide accurate pricing, and apply this logic throughout\n\nComments:\n//!IMPORTANT! Do not offer any times without checking the calendar, do not make availability up\n//**Do not discuss anything other than appointment booking, if the query does not relate to an appointment, advise them you cannot help at this time.** be friendly and always offer to book an appointment to discuss their query\n//When the appointment is confirmed, let the customer know, by name, that they will be meeting our founder, Wayne for a 30 minute consultation, and that they will receive a calendar invite by email, ensure they accept the invite to confirm the appointment.\n//Always respond as a highly professional executive PA, remember this is the customer's first engagement, they do not know us or Wayne at this stage\n//Do not refer to yourself as me or I, instead communicate like an organisation, using terms like 'us'\n//Always gather project for descriptions and messages"}}, "id": "7b049f3e-b4c0-4217-8305-95f560b3c847", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [120, 820], "typeVersion": 1.6}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "158a0b91-534d-4745-b10e-8a7c97050861", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.chatInput }}", "rightValue": ""}]}, "options": {}}, "id": "10008cd2-a9cb-4769-8569-0a8b517640b3", "name": "If", "type": "n8n-nodes-base.if", "position": [-500, 840], "typeVersion": 2.2}, {"parameters": {"respondWith": "json", "responseBody": "{\n \"output\": \"<PERSON>, how can I help you today?\"\n}", "options": {}}, "id": "4d747994-8313-4744-ba51-aec31846e3cc", "name": "Respond With Initial Message", "type": "n8n-nodes-base.respondToWebhook", "position": [-200, 960], "typeVersion": 1.1}], "pinData": {}, "connections": {"If": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "Respond With Initial Message", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Get Events", "type": "main", "index": 0}], [{"node": "Send Message1", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Get Events": {"main": [[{"node": "freeTimeSlots", "type": "main", "index": 0}]]}, "Chat Trigger": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Send Message": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Send Message1": {"main": [[{"node": "varMessageResponse", "type": "main", "index": 0}]]}, "freeTimeSlots": {"main": [[{"node": "varResponse", "type": "main", "index": 0}]]}, "Get Availability": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Make Appointment": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c27e0319-09e7-4528-b9d1-728bc8d2bdd3", "meta": {"instanceId": "b2189e49751062fbda95b4ca9399267c1bf87960485d2972d5aae0b9437119d2"}, "id": "XTCi1T30A2J4EykL", "tags": []}