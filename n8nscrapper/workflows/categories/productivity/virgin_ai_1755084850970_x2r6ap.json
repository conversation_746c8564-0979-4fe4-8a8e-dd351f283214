{"name": "Virgin ai", "nodes": [{"parameters": {"updates": ["com.twilio.messaging.inbound-message.received"]}, "id": "1afa199a-ffc7-4997-85fe-03f5c587fe61", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.twilioTrigger", "position": [-440, 640], "webhookId": "bfc8f587-8183-46f8-9e76-3576caddf8c0", "typeVersion": 1, "credentials": {"twilioApi": {"id": "YDUlFZX20GJ8NXBZ", "name": "Twilio account"}}}, {"parameters": {"options": {}}, "id": "c25b71ad-2ed3-4247-bfde-be6cbb6b3144", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [600, 1820], "typeVersion": 1, "credentials": {"openAiApi": {"id": "IRc1T2ifB8GPPS5Q", "name": "OpenAi account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appO2nHiT9XPuGrjN", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>Scheduling-Agent"}, "table": {"__rl": true, "mode": "list", "value": "tblokH7uw63RpIlQ0", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN/tblokH7uw63RpIlQ0", "cachedResultName": "Lead Tracker"}, "filterByFormula": "=AND(\n {appointment_id} = '',\n {status} != 'STOP',\n {followup_count} < 3,\n DATETIME_DIFF(TODAY(), {last_followup_at}, 'days') >= 3\n)", "options": {}}, "id": "de3fb73a-35ce-4788-a7f5-440f13427774", "name": "Find Follow-Up Candidates", "type": "n8n-nodes-base.airtable", "position": [180, 1700], "typeVersion": 2.1, "credentials": {"airtableTokenApi": {"id": "Ypiyhw9MDGuBCATi", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"from": "={{ $('Find Follow-Up Candidates').item.json.twilio_service_number }}", "to": "={{ $('Find Follow-Up Candidates').item.json.session_id }}", "message": "={{ $('Generate Follow Up Message').item.json.text }}\nReply STOP to stop recieving these messages.", "options": {}}, "id": "2aab5e9f-ac66-4ad8-84ba-3758eb964d88", "name": "Send Follow Up Message", "type": "n8n-nodes-base.twilio", "position": [1340, 1700], "typeVersion": 1, "credentials": {"twilioApi": {"id": "YDUlFZX20GJ8NXBZ", "name": "Twilio account"}}}, {"parameters": {"content": "## Step 6. Filter Open Enquiries from Airtable\n\n### 💡Criteria For Follow Up Candidates\n* No Scheduled Appointment\n* No Request to STOP\n* No Previous Follow-up in Past 3 days\n* Follow-up is less than 3 times", "height": 515.*************, "width": 408.*************, "color": 7}, "id": "bc458a84-8399-46b5-8cea-c603f3671a6c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [40, 1400], "typeVersion": 1}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appO2nHiT9XPuGrjN", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>Scheduling-Agent"}, "table": {"__rl": true, "mode": "list", "value": "tblokH7uw63RpIlQ0", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN/tblokH7uw63RpIlQ0", "cachedResultName": "Lead Tracker"}, "columns": {"value": {"session_id": "={{ $('Find Follow-Up Candidates').item.json.session_id }}", "followup_count": "={{ ($('Find Follow-Up Candidates').item.json.followup_count ?? 0) + 1 }}", "last_followup_at": "={{ $now.toISO() }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "session_id", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "session_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "ACTIVE", "value": "ACTIVE"}, {"name": "STOP", "value": "STOP"}], "removed": true, "readOnly": false, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "customer_name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "customer_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "customer_summary", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "customer_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "chat_messages", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "chat_messages", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "scheduled_at", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "scheduled_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "appointment_id", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "appointment_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_message_at", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "last_message_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_followup_at", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "last_followup_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "followup_count", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "followup_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "assignee", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["session_id"]}, "options": {}}, "id": "a5596910-660c-473e-b57d-97294cf4c580", "name": "Update Follow-Up Count and Date", "type": "n8n-nodes-base.airtable", "position": [1160, 1700], "typeVersion": 2.1, "credentials": {"airtableTokenApi": {"id": "Ypiyhw9MDGuBCATi", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appO2nHiT9XPuGrjN", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>Scheduling-Agent"}, "table": {"__rl": true, "mode": "list", "value": "tblokH7uw63RpIlQ0", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN/tblokH7uw63RpIlQ0", "cachedResultName": "Lead Tracker"}, "columns": {"value": {"session_id": "={{ $('<PERSON><PERSON><PERSON>').item.json.From }}", "scheduled_at": "={{\n$('Appointment Scheduling Agent').item.json.output.has_appointment_scheduled\n ? $('Appointment Scheduling Agent').item.json.output.appointment.scheduled_at\n : (\n $('Get Existing Chat Session').item.json.isNotEmpty()\n ? $('Get Existing Chat Session').item.json.scheduled_at\n : $now.toISO()\n )\n}}", "chat_messages": "={{\nJSON.stringify(\n ($('Get Existing Chat Session').item.json.chat_messages ? JSON.parse($('Get Existing Chat Session').item.json.chat_messages) : [])\n .concat(\n { \"role\": \"human\", \"message\": $('<PERSON><PERSON><PERSON>').item.json.Body },\n { \"role\": \"assistant\", \"message\": $('Appointment Scheduling Agent').item.json.output.reply }\n )\n)\n}}", "customer_name": "={{\n !$('Get Existing Chat Session').item.json.customer_name &&\n $('Appointment Scheduling Agent').item.json.output.customer_name\n ? $('Appointment Scheduling Agent').item.json.output.customer_name\n : ($('Get Existing Chat Session').item.json.customer_name ?? '')\n}}", "appointment_id": "={{\n$('Appointment Scheduling Agent').item.json.output.has_appointment_scheduled\n ? $('Appointment Scheduling Agent').item.json.output.appointment.appointment_id\n : (\n $('Get Existing Chat Session').item.json.isNotEmpty()\n ? $('Get Existing Chat Session').item.json.appointment_id\n : ''\n )\n}}", "followup_count": "={{\n !$('Get Existing Chat Session').item.json.followup_count\n ? 0\n : $('Get Existing Chat Session').item.json.followup_count\n}}", "last_message_at": "={{ $now.toISO() }}", "customer_summary": "={{\n !$('Get Existing Chat Session').item.json.appointment_id\n && $('Appointment Scheduling Agent').item.json.output.has_appointment_scheduled\n ? $json.output.enquiry_summary\n : $('Get Existing Chat Session').item.json.customer_summary\n}}", "last_followup_at": "={{\n !$('Get Existing Chat Session').item.json.last_followup_at\n ? $now.toISO()\n : $('Get Existing Chat Session').item.json.last_followup_at\n}}", "twilio_service_number": "={{ $('<PERSON><PERSON><PERSON>').item.json.To }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "session_id", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "session_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "ACTIVE", "value": "ACTIVE"}, {"name": "STOP", "value": "STOP"}], "removed": true, "readOnly": false, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "customer_name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "customer_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "customer_summary", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "customer_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "chat_messages", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "chat_messages", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "scheduled_at", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "scheduled_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "appointment_id", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "appointment_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_message_at", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "last_message_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_followup_at", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "last_followup_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "followup_count", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "followup_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "assignee", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "twilio_service_number", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "twilio_service_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["session_id"]}, "options": {}}, "id": "28291ac2-976a-4bf6-b798-be3812b111a0", "name": "Create/Update Session", "type": "n8n-nodes-base.airtable", "position": [1700, 720], "typeVersion": 2.1, "credentials": {"airtableTokenApi": {"id": "Ypiyhw9MDGuBCATi", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appO2nHiT9XPuGrjN", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>Scheduling-Agent"}, "table": {"__rl": true, "mode": "list", "value": "tblokH7uw63RpIlQ0", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN/tblokH7uw63RpIlQ0", "cachedResultName": "Lead Tracker"}, "filterByFormula": "={session_id}=\"{{ $('<PERSON><PERSON><PERSON>').item.json.From }}\"", "returnAll": false, "limit": 1, "options": {}}, "id": "881a5265-b8d3-48dc-a8e6-fca85b5ad724", "name": "Get Existing Chat Session", "type": "n8n-nodes-base.airtable", "position": [200, 700], "typeVersion": 2.1, "alwaysOutputData": true, "credentials": {"airtableTokenApi": {"id": "Ypiyhw9MDGuBCATi", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"rule": {"interval": [{}]}}, "id": "193054eb-b51c-4d41-9097-ca1d62d29f50", "name": "Every 24hrs", "type": "n8n-nodes-base.scheduleTrigger", "position": [-320, 1620], "typeVersion": 1.2}, {"parameters": {"from": "={{ $('<PERSON><PERSON><PERSON>').item.json.To }}", "to": "={{ $('<PERSON><PERSON><PERSON>').item.json.From }}", "message": "={{ $('Appointment Scheduling Agent').item.json.output.reply }}", "options": {}}, "id": "fc71affe-d394-489d-aa1f-a20369e494c2", "name": "Send Reply", "type": "n8n-nodes-base.twilio", "position": [1880, 720], "typeVersion": 1, "credentials": {"twilioApi": {"id": "YDUlFZX20GJ8NXBZ", "name": "Twilio account"}}}, {"parameters": {"from": "={{ $('<PERSON><PERSON><PERSON>').item.json.To }}", "to": "={{ $('<PERSON><PERSON><PERSON>').item.json.From }}", "message": "Thank you. You won't receive any more messages from us!", "options": {}}, "id": "6bc8578b-e28f-4211-9b5b-5cb878803dd6", "name": "Send Confirmation", "type": "n8n-nodes-base.twilio", "position": [360, 180], "typeVersion": 1, "credentials": {"twilioApi": {"id": "YDUlFZX20GJ8NXBZ", "name": "Twilio account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appO2nHiT9XPuGrjN", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN", "cachedResultName": "<PERSON><PERSON><PERSON><PERSON>Scheduling-Agent"}, "table": {"__rl": true, "mode": "list", "value": "tblokH7uw63RpIlQ0", "cachedResultUrl": "https://airtable.com/appO2nHiT9XPuGrjN/tblokH7uw63RpIlQ0", "cachedResultName": "Lead Tracker"}, "columns": {"value": {"status": "STOP", "session_id": "={{ $('<PERSON><PERSON><PERSON>').item.json.From }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "session_id", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "session_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "options", "display": true, "options": [{"name": "ACTIVE", "value": "ACTIVE"}, {"name": "STOP", "value": "STOP"}], "removed": false, "readOnly": false, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "chat_messages", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "chat_messages", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "scheduled_at", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "scheduled_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_message_at", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "last_message_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "last_followup_at", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "last_followup_at", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "followup_count", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "followup_count", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "assignee", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "assignee", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["session_id"]}, "options": {}}, "id": "dae72906-d736-458e-bd7f-db51d06058af", "name": "User Request STOP", "type": "n8n-nodes-base.airtable", "position": [120, 180], "typeVersion": 2.1, "credentials": {"airtableTokenApi": {"id": "Ypiyhw9MDGuBCATi", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.Body }}", "rightValue": "STOP"}]}, "renameOutput": true, "outputKey": "STOP"}]}, "options": {"fallbackOutput": "extra"}}, "id": "e3cdfbc9-7b87-4fc9-ac9c-a79773ed3b5b", "name": "Check For Command Words", "type": "n8n-nodes-base.switch", "position": [-240, 640], "typeVersion": 3}, {"parameters": {"jsonSchemaExample": "{\n \"reply\": \"\",\n \"customer_name\": \"\",\n \"enquiry_summary\": \"\",\n\t\"has_appointment_scheduled\": false,\n \"appointment\": {\n \"appointment_id\": \"\",\n \"scheduled_at\": \"\"\n }\n}"}, "id": "ef70dbec-b12f-48b6-8786-6ff0ac5b61d1", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1420, 1020], "typeVersion": 1.2}, {"parameters": {"options": {}}, "id": "21cd4244-01db-4198-a9b7-40a246006f3a", "name": "Auto-fixing Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "position": [1280, 900], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "9d024958-8ce6-488a-b921-caf53949d178", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1300, 1020], "typeVersion": 1, "credentials": {"openAiApi": {"id": "IRc1T2ifB8GPPS5Q", "name": "OpenAi account"}}}, {"parameters": {"content": "## Step 3. Appointment Scheduling With AI\n[Learn about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent)\n\nUsing an AI Agent is a powerful way to simplify and enhance workflows using the latest in AI technology. Our appointment scheduling agent is equipped to converse with the customer and all the necessary tools to schedule, re-schedule and cancel appointments.\n\nUsing the **HTTP Tool** node, it's easy to connect to third party API services to perform actions. In this workflow, we're calling the Cal.com API to handle scheduling events.", "height": 917.************, "width": 1011.*************, "color": 7}, "id": "d1d4b3e9-20db-48b8-828a-d12d0b212c14", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [540, 400], "typeVersion": 1}, {"parameters": {"content": "## Step 2. Check for Existing Chat History\n[Read more about using Airtable](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.airtable)\n\nWe're using Airtable for customer session management and to capture chat history. Airtable is an ideal choice because it acts as a persistent database with a flexible API which could prove essential for further extension.\n\nWe'll pull any previous chat history and pass this to our agent to continue the conversation.", "height": 557.8466102697549, "width": 504.0066355303578, "color": 7}, "id": "be93a805-7269-4a34-8afb-dc8f069b23e4", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [20, 420], "typeVersion": 1}, {"parameters": {"content": "## Step 1. Wait For Customer SMS\n[Read more about Twilio trigger](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.twiliotrigger)\n\nFor this workflow, we'll use the twilio SMS trigger to receive enquiries from customers looking to book a PC or laptop repair.\n\nSince we'll be working with SMS, we'll have a check to see if the customer wishes to STOP any further follow-up messages. This is an optional step that we'll get to later.", "height": 479.4432905734608, "width": 523.6927529886705, "color": 7}, "id": "19212831-5b36-4f5a-bf72-86f2de5cd81c", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-540, 400], "typeVersion": 1}, {"parameters": {"content": "## Step 9. Cancelling Follow-Up Messages \n\nIf the customer messages the bot with the word STOP, we'll update our customer record in Airtable which will prevent further follow-ups from being trigger. A confirmation message is sent after to the customer.", "height": 358.6710117357418, "width": 563.7797724327219, "color": 7}, "id": "782c317f-3451-4d7e-9ea1-1f3b709cccdb", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "typeVersion": 1}, {"parameters": {"content": "## Step 4. Updating Airtable and Responding to the Customer \n[Read more about using Twilio](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twilio)\n\nOnce the agent formulates a response, we can update our appointment table accordingly ensuring the conversation at any stage is captured.\n\nIf no appointment is scheduled, we can move onto the second half of this workflow which covers following up with prospective customers and their enquiries.", "height": 558.7093446159199, "width": 521.5259177258192, "color": 7}, "id": "dab9b648-30fe-45e1-9306-45bbda28dd06", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1580, 420], "typeVersion": 1}, {"parameters": {"content": "## Step 5. Following Up With Open Enquiries\n[Read more about using scheduled trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger)\n\nThe second half of this workflow deals with identifying customers who have engaged our chatbot but have not yet confirmed an appointment. We intend to send a follow-up message asking if the enquiry is still valid and encourage an appointment to be made with the customer.", "height": 433.74984757777247, "width": 509.931737588259, "color": 7}, "id": "21c84fe6-67fa-44b0-bfab-a50d234e38d5", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [-520, 1400], "typeVersion": 1}, {"parameters": {"content": "## Step 7. Generating a Follow-Up Message\n[Read more about Basic LLM Chain](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nWith our session and chat history retrieved from Airtable, we can simple ask our AI to generate a nicely worded follow-up message to re-engage the customer.\n\nWhere the logic is linear, the Basic LLM chain is suitable for many workflows. An agent is not always required!", "height": 601.5572296901626, "width": 567.1169284476533, "color": 7}, "id": "d3008baf-e75e-4992-80fd-05de204240ff", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [460, 1400], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "=", "messages": {"messageValues": [{"message": "=You are an appointment scheduling assistant for PC and Laptop Repairs for a company called \"PC Parts Ltd\". You shall refer to yourself as the \"service team\". You had a conversation with a customer on {{ $json.last_message_at }} but the enquiry did not end with an appointment being scheduled.\n{{ $json.last_followup_at ? `You last sent a follow-up message on ${$json.last_followup_at}` : '' }}.\n\nYou task is to ask if the prospective customer would like to continue with the enquiry using the following information gather to construct a relevant follow-up message. Try to entice the user to continue the conversation and ultimately schedule an appointment.\n\n## About the customer\nname: {{ $json.customer_name ?? '<unknown>' }}\nenquiry summary: {{ $json.customer_summary ?? '<uknown>' }}\n\n# Existing conversation\nHere are the chat logs of the existing conversation:\n{{ $json.chat_messages }}"}]}}, "id": "f545a679-2343-4e69-b8cd-cc513f3ed6fc", "name": "Generate Follow Up Message", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [600, 1660], "typeVersion": 1.4}, {"parameters": {"content": "## Step 8. Update Follow-Up Properties and Send Message\n[Read more about using Twilio](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twilio/)\n\nFinally, we'll update our follow-up activity as part of the customer record in Airtable. Keeping track of the number of times we follow-up helps prevent spamming the customer unnecessarily.\n\nThe follow-up message is sent via Twilio and includes instruction to disable further follow-up messages using the keyword STOP.", "height": 526.084030034264, "width": 496.0833287715134, "color": 7}, "id": "28c3eb66-21b1-43ae-9359-b35fc27af498", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, 1400], "typeVersion": 1}, {"parameters": {"content": "## Try It Out!\n\n### This workflow implements an appointment scheduling chatbot which is powered by an AI tools agent.\n* Workflow is triggered by Customer enquires sent via SMS\n* Customer session management and chat history are captured in Airtable to enable the SMS conversation.\n* An AI Agent is equipped to answer any questions as well as schedule, re-schedule and cancel appointments on behalf of the customer.\n* The agent's reply is sent back to the customer via SMS.\n* Additional a follow-up system is implemented to re-engage customers who haven't scheduled an appointment.\n\n \n!\n\nHappy Hacking!", "height": 511.**************, "width": 437.*************}, "id": "1d34e87d-b016-46b9-b1cf-54bafe8ea372", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [-1020, 380], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "96e2e0aa-39fa-4e71-b23c-ea0332b1adf5", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [580, 880], "typeVersion": 1, "credentials": {"openAiApi": {"id": "IRc1T2ifB8GPPS5Q", "name": "OpenAi account"}}}, {"parameters": {"toolDescription": "Call this tool to get the appointment availability. Dates can be variable but times are fixed - startTime must always be 9am and endTime must be 7pm. Strictly use ISO format for dates eg. \"2024-01-01T09:00:00-00:00\". Input schema example: ```{ \"startTime\": \"...\", \"endTime\": \"...\"}```", "url": "https://api.cal.com/v2/slots/available", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "parametersQuery": {"values": [{"name": "eventTypeId", "valueProvider": "fieldValue", "value": "={{ 648297 }}"}, {"name": "startTime", "valueProvider": "fieldValue", "value": "{startTime}"}, {"name": "endTime", "valueProvider": "fieldValue", "value": "{endTime}"}]}, "placeholderDefinitions": {"values": [{"name": "startTime", "description": "start of daterange in ISO format. eg. 2024-01-01T09:00:00-00:00", "type": "string"}, {"name": "endTime", "description": "end of daterange in ISO format. eg. 2024-01-01T09:00:00-00:00", "type": "string"}]}}, "id": "e4468135-de7e-4fc1-bd26-76a275299851", "name": "Get Availability", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [720, 900], "typeVersion": 1, "credentials": {"httpHeaderAuth": {"id": "HOXlKCREwZ8i57Uy", "name": "Header Auth account"}}}, {"parameters": {"toolDescription": "Call this tool to get an existing booking using a booking \"uid\".", "url": "https://api.cal.com/v2/bookings/{bookingUid}", "authentication": "predefinedCredentialType", "nodeCredentialType": "calApi", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "cal-api-version", "valueProvider": "fieldValue", "value": "2024-08-13"}]}, "placeholderDefinitions": {"values": [{"name": "bookingUid", "description": "the uid of the booking (note: this is not the same as the id of the booking)", "type": "string"}]}}, "id": "757c52a6-a856-4d7a-865f-1b152656cc6f", "name": "Get Existing Booking", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1020, 900], "typeVersion": 1}, {"parameters": {"toolDescription": "Call this tool to search for an existing bookings with the user's email address and date. Use the \"uid\" field in the results as the primary booking identifier, ignore the \"id\" field.", "url": "https://api.cal.com/v2/bookings", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "specifyQuery": "json", "jsonQuery": "{\n \"status\": \"upcoming\",\n \"attendeeEmail\": \"{attendee_email}\",\n \"afterStart\": \"{date}\"\n}", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "cal-api-version", "valueProvider": "fieldValue", "value": "2024-08-13"}]}, "placeholderDefinitions": {"values": [{"name": "attendee_email", "description": "email address of attendee", "type": "string"}, {"name": "date", "description": "Filter bookings with start after this date string. The time is always fixed at 9am."}]}}, "id": "36985bba-93ae-474f-b2a6-ce837f372c2f", "name": "Find Existing Booking", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1160, 900], "typeVersion": 1, "credentials": {"httpHeaderAuth": {"id": "HOXlKCREwZ8i57Uy", "name": "Header Auth account"}}}, {"parameters": {"toolDescription": "Call this tool to reschedule a user's booking using a booking \"uid\".", "method": "POST", "url": "https://api.cal.com/v2/bookings/{bookingUid}/reschedule", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "cal-api-version", "valueProvider": "fieldValue", "value": "2024-08-13"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n \"start\": \"{start}\",\n \"reschedulingReason\": \"{reschedulingReason}\"\n}", "placeholderDefinitions": {"values": [{"name": "bookingUid", "description": "the uid of the booking. Note this is not the same as the id of the booking.", "type": "string"}, {"name": "start", "description": "start datetime of the appointment, for example: \"2024-05-30T12:00:00.000Z\"", "type": "string"}, {"name": "reschedulingReason", "description": "Reason for rescheduling the booking. If not given, value is \"Declined to give reason.\"", "type": "string"}]}}, "id": "f3250b44-9b5c-4a34-ac92-54745bf601a5", "name": "Reschedule Booking", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1020, 1080], "typeVersion": 1, "credentials": {"httpHeaderAuth": {"id": "HOXlKCREwZ8i57Uy", "name": "Header Auth account"}}}, {"parameters": {"toolDescription": "Call this tool to cancel a user's existing booking using a booking \"uid\".", "method": "POST", "url": "https://api.cal.com/v2/bookings/{bookingUid}/cancel", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "cal-api-version", "valueProvider": "fieldValue", "value": "2024-08-13"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n \"cancellationReason\": \"{cancellationReason}\"\n}", "placeholderDefinitions": {"values": [{"name": "bookingUid", "description": "the uid of the booking. Note this is not the same as the id of the booking.", "type": "string"}, {"name": "cancellationReason", "description": "Reason for cancelling the appointment", "type": "string"}]}}, "id": "a4c09073-f443-4771-8d4d-9b45cfd275f0", "name": "Cancel Booking", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [1160, 1080], "typeVersion": 1, "credentials": {"httpHeaderAuth": {"id": "HOXlKCREwZ8i57Uy", "name": "Header Auth account"}}}, {"parameters": {"toolDescription": "Call this tool to create a booking. Strictly use ISO format for dates eg. \"2024-01-01T09:00:00-00:00\" for API compatibility.", "method": "POST", "url": "https://api.cal.com/v2/bookings", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "Content-Type", "valueProvider": "fieldValue", "value": "application/json"}, {"name": "cal-api-version", "valueProvider": "fieldValue", "value": "2024-08-13"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n \"eventTypeId\": 648297,\n \"start\": \"{start}\",\n \"attendee\": {\n \"name\": \"{attendee_name}\",\n \"email\": \"{attendee_email}\",\n \"timeZone\": \"{attendee_timezone}\"\n },\n \"bookingFieldsResponses\": {\n \"title\": \"{summary_of_enquiry}\"\n }\n}", "placeholderDefinitions": {"values": [{"name": "start", "description": "The start time of the booking in ISO format. eg. \"2024-01-01T09:00:00Z\"", "type": "string"}, {"name": "attendee_name", "description": "Name of the attendee", "type": "string"}, {"name": "attendee_email", "description": "email of the attendee", "type": "string"}, {"name": "attendee_timezone", "description": "If timezone is unknown, assume Europe/London.", "type": "string"}, {"name": "summary_of_enquiry", "description": "short summary of the enquiry or purpose of the meeting", "type": "string"}]}}, "id": "6cfc8467-fa22-443b-906b-7807764d556c", "name": "Create a Booking", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [860, 900], "typeVersion": 1, "credentials": {"httpHeaderAuth": {"id": "HOXlKCREwZ8i57Uy", "name": "Header Auth account"}}}, {"parameters": {"content": "![alt](https://upload.wikimedia.org/wikipedia/commons/a/a5/Cal.com%2C_Inc._Logo.svg#100x80)\nYou'll need to set a custom Header Auth Credential for Cal.com API v2. See the following doc for more info: https://cal.com/docs/api-reference/v2/introduction", "height": 168.**************, "width": 261.*************, "color": 7}, "id": "773e2942-7630-440a-86f8-8a7b731c5755", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [400, 1060], "typeVersion": 1}, {"parameters": {"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨 Change EventTypeID Here!\n* EventTypeID must be a number.\n* Your event type dictates the allowed duration of the booking.\n* If Event Type set to 30mins and the agent attempts to book 60mins, this will fail so make sure the agent knows how long to set the booking for!", "height": 360.*************, "width": 301.************}, "id": "b5319637-f2d5-4beb-b807-1babfe106d56", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [680, 880], "typeVersion": 1}, {"parameters": {"hasOutputParser": true, "options": {"systemMessage": "=You are an appointment scheduling helper for a company called \"PC Parts Ltd\". Customers will message you enquirying for PC or laptop repairs and your job is to schedule a repair session for the user.This role is strictly to help schedule appointments so:\n* you may answer questions relating to the company, \"PC Parts Ltd\".\n* you may not answer questions relating to competitors of \"PC Parts Ltd\".\n* you may answer questions relating to general PC or laptop repair from a non-technical perspective.\n* you may not help to customer diagnose or assist in troubleshoot or debugging thei r PC or laptop issues. If the customer does ask, defer them to book an appointment where a suitable professional from PC Parts Ltd can help.\n* If an appointment is scheduled for the user then the conversation is completed and you should not continue to ask the user to schedule an appointment.\n* If an appointment is scheduled for the user, the user may ask for the following actions: ask about details of the existing appointment, reschedule the existing appointment or cancel an existing appointment.\n* If an appointment is scheduled for the user, the user cannot schedule another appointment until the existing appointment is cancelled.\n\n## About the company\nPC Parts Ltd is based in London, UK. They offer to repair low-end to high-end PC and Laptop consumer and small business machines. They also offer custom built machines such as for gaming. There is currently a summer sale going on for 20% selected machines for repairs. The company does not repair other electronic devices such as phones, tablets or monitors.\n\n## About the appointments\nAlways start your conversation by politely asking if the user wants to book a new appointment or enquire about an existing one. The date and time now is {{ $now.toISO() }}. All dates should be given in the ISO format. Each appointment should have a start and end date and time relative to today's date in the future and should be scheduled for 30 minutes.\n\n## To book an appointment\n* Before booking an appointment, ask if the user has an existing appointment.\n* Ensure you have the user's email address, full name and proposed date, preferred start time before booking an appointment.\n* Always check the calendar availability of the user's proposed date and time. If there is no availability, suggest the next available appointment slot.\n* If the appointment booking is successful, notify the user that an email confirmation will be sent to their provided email address.\n* If the appointment booking is unsuccessful, notify the user that you are unable to complete their request at the moment and to try again later.\n\n## To find an existing appointment\n* Ask the user for their email address and the date of the existing booking\n* Use the user's email and date to search for the existing booking.\n* If the user's email and date do not match the results or no results are returned, then the existing booking is not found.\n* If the existing booking is not found, notify the user and suggest a new booking should be made.\n* When the existing booking is found, ensure you tell them the booking's UID field.\n\n# To reschedule or cancel an existing appointment\n* First find the existing appointment so that you may obtain the existing appointment's booking UID.\n* Display this booking UID to the user.\n* Use this booking UID to reschedule or cancel an existing appointment.\n* If an existing appointment ID is not found or given, then notify the user that it is not possible to complete their request at this time and they should contact via email.\n* when user wants to cancel an appointment, ask for a reason for the cancellation and suggest rescheduling as an alternative. Confirm with user before cancelling an appointment.\n\n## About the user\n* The customer's session_id is \"{{ $('Twilio Trigger').item.json.From }}\"\n{{\n$json.chat_messages \n ? '* This is a returning prospective customer.' \n : '* This is a new customer. Ask for the details of their enquiry.'\n}}\n{{\n$json.appointment_id \n ? `* The customer has already scheduled an appointment at ${$json.scheduled_at} and their appointment_id is ${$json.appointment_id}`\n : '* This customer has not scheduled an appointment yet.'\n}}\n\n## Existing Conversation\n{{\n$json.chat_messages\n ? 'Here are the existing chat logs and should be used as context to continue the conversation:\\n```\\n' + JSON.parse($json.chat_messages).map(item => `${item.role}: ${item.message.replaceAll('\\n', ' ')}`).join('\\n') + '\\n```'\n : '* There is no existing conversation so far.'\n}}\n"}}, "id": "90a9dfe9-7c3a-45aa-88fc-7846da08ff05", "name": "Appointment Scheduling Agent1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [680, 660], "typeVersion": 1.6}], "pinData": {}, "connections": {"Every 24hrs": {"main": [[{"node": "Find Follow-Up Candidates", "type": "main", "index": 0}]]}, "Cancel Booking": {"ai_tool": [[{"node": "Appointment Scheduling Agent1", "type": "ai_tool", "index": 0}]]}, "Twilio Trigger": {"main": [[{"node": "Check For Command Words", "type": "main", "index": 0}]]}, "Create a Booking": {"ai_tool": [[{"node": "Appointment Scheduling Agent1", "type": "ai_tool", "index": 0}]]}, "Get Availability": {"ai_tool": [[{"node": "Appointment Scheduling Agent1", "type": "ai_tool", "index": 0}]]}, "User Request STOP": {"main": [[{"node": "Send Confirmation", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Generate Follow Up Message", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Appointment Scheduling Agent1", "type": "ai_languageModel", "index": 0}]]}, "Reschedule Booking": {"ai_tool": [[{"node": "Appointment Scheduling Agent1", "type": "ai_tool", "index": 0}]]}, "Get Existing Booking": {"ai_tool": [[{"node": "Appointment Scheduling Agent1", "type": "ai_tool", "index": 0}]]}, "Create/Update Session": {"main": [[{"node": "Send Reply", "type": "main", "index": 0}]]}, "Find Existing Booking": {"ai_tool": [[{"node": "Appointment Scheduling Agent1", "type": "ai_tool", "index": 0}]]}, "Check For Command Words": {"main": [[{"node": "User Request STOP", "type": "main", "index": 0}], [{"node": "Get Existing Chat Session", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Appointment Scheduling Agent1", "type": "ai_outputParser", "index": 0}]]}, "Find Follow-Up Candidates": {"main": [[{"node": "Generate Follow Up Message", "type": "main", "index": 0}]]}, "Get Existing Chat Session": {"main": [[{"node": "Appointment Scheduling Agent1", "type": "main", "index": 0}]]}, "Generate Follow Up Message": {"main": [[{"node": "Update Follow-Up Count and Date", "type": "main", "index": 0}]]}, "Appointment Scheduling Agent1": {"main": [[{"node": "Create/Update Session", "type": "main", "index": 0}]]}, "Update Follow-Up Count and Date": {"main": [[{"node": "Send Follow Up Message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "df92fbe5-061f-45a0-bcdd-fea45f215713", "meta": {"templateCredsSetupCompleted": true, "instanceId": "379dceebe179fb6715972007efd2bcf24d3fffceb6a04229b0fb570760659f53"}, "id": "evt0ARQFWEnFtENJ", "tags": []}