{"name": "AI Agent Army - Multi-Agent Orchestration System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "chat", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "ai-agent-chat"}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "You are the Orchestration Agent, the central command of an AI Agent Army. Your role is to understand user requests and delegate tasks to the appropriate specialized agents.\n\nYou have access to the following specialized agents as tools:\n- Email Agent: Use for sending emails, searching emails, and managing email communications\n- Calendar Agent: Use for scheduling meetings, checking availability, and managing calendar events\n- Company Knowledge Agent: Use for questions about company policies, procedures, and general company information\n- Company FAQ Agent: Use for frequently asked questions about the company\n- Personal Expenses Agent: Use for questions about personal expenses, financial data, and expense tracking\n\nAlways choose the most appropriate agent for the task. If a request involves multiple domains, break it down and use multiple agents as needed. Provide clear, helpful responses to the user based on the results from the specialized agents.", "model": {"model": "claude-3-5-sonnet-20241022", "type": "anthropic"}, "options": {"systemMessage": "You are the central orchestration agent in a multi-agent AI system. Analyze user requests carefully and delegate to the appropriate specialized agents."}, "memory": {"type": "bufferMemory", "sessionKey": "={{ $json.sessionId || 'default' }}", "maxTokenLimit": 2000}, "tools": [{"name": "Email Agent", "description": "Specialized agent for email operations including sending, searching, and managing emails", "workflowId": "email-agent-workflow"}, {"name": "Calendar Agent", "description": "Specialized agent for calendar operations including scheduling, checking availability, and managing events", "workflowId": "calendar-agent-workflow"}, {"name": "Company Knowledge Agent", "description": "Agent with access to company knowledge base for policies, procedures, and general information", "workflowId": "company-knowledge-agent"}, {"name": "Company FAQ Agent", "description": "Agent for frequently asked questions about the company", "workflowId": "company-faq-agent"}, {"name": "Personal Expenses Agent", "description": "Agent for personal expenses and financial data queries", "workflowId": "personal-expenses-agent"}]}, "id": "orchestration-agent", "name": "Orchestration Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"respondWith": "text", "responseBody": "={{ $json.output }}"}, "id": "webhook-response", "name": "Chat Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "You are an expert Email Agent specialized in email operations. You can send emails, search for emails, and manage email communications through Gmail.\n\nYour capabilities include:\n- Sending new emails with proper formatting\n- Searching and retrieving existing emails\n- Adding labels to organize messages\n- Managing email threads and conversations\n\nAlways ensure you have the necessary information (recipient, subject, body) before sending emails. If information is missing, ask for clarification.", "model": {"model": "gpt-4o", "type": "openai"}, "options": {"systemMessage": "You are a specialized email assistant. Handle all email-related tasks efficiently and professionally."}, "memory": {"type": "bufferMemory", "sessionKey": "email-session", "maxTokenLimit": 1000}, "tools": [{"name": "Gmail Send", "description": "Send an email through Gmail", "nodeId": "gmail-send"}, {"name": "Gmail Search", "description": "Search for emails in Gmail", "nodeId": "gmail-search"}, {"name": "Gmail Label", "description": "Add labels to Gmail messages", "nodeId": "gmail-label"}]}, "id": "email-agent", "name": "Email Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [240, 500]}, {"parameters": {"operation": "send", "resource": "message", "subject": "={{ $json.subject }}", "message": "={{ $json.body }}", "toList": "={{ $json.to }}", "options": {}}, "id": "gmail-send", "name": "Gmail Send", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [460, 450], "credentials": {"gmailOAuth2": {"id": "gmail-credentials", "name": "Gmail OAuth2"}}}, {"parameters": {"operation": "getAll", "resource": "message", "returnAll": false, "limit": 10, "simple": false, "options": {"query": "={{ $json.query }}"}}, "id": "gmail-search", "name": "Gmail Search", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [460, 550], "credentials": {"gmailOAuth2": {"id": "gmail-credentials", "name": "Gmail OAuth2"}}}, {"parameters": {"operation": "addLabels", "resource": "message", "messageId": "={{ $json.messageId }}", "labelIds": "={{ $json.labels }}"}, "id": "gmail-label", "name": "Gmail Add Label", "type": "n8n-nodes-base.gmail", "typeVersion": 2, "position": [460, 650], "credentials": {"gmailOAuth2": {"id": "gmail-credentials", "name": "Gmail OAuth2"}}}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "You are a specialized Calendar Agent focused on managing calendar events and scheduling. You can create, read, update, and delete calendar events through Google Calendar.\n\nYour capabilities include:\n- Creating new calendar events with proper details\n- Searching for existing events\n- Updating event information\n- Deleting events when requested\n- Checking availability and scheduling conflicts\n\nWhen creating events, ensure you have the title, date, time, and any attendees. Handle date/time parsing intelligently and ask for clarification if needed.", "model": {"model": "gpt-4o", "type": "openai"}, "options": {"systemMessage": "You are a specialized calendar assistant. Handle all calendar-related tasks efficiently."}, "memory": {"type": "bufferMemory", "sessionKey": "calendar-session", "maxTokenLimit": 1000}, "tools": [{"name": "Calendar Create Event", "description": "Create a new calendar event", "nodeId": "calendar-create"}, {"name": "Calendar Get Events", "description": "Retrieve calendar events", "nodeId": "calendar-get"}, {"name": "Calendar Update Event", "description": "Update an existing calendar event", "nodeId": "calendar-update"}, {"name": "Calendar Delete Event", "description": "Delete a calendar event", "nodeId": "calendar-delete"}]}, "id": "calendar-agent", "name": "Calendar Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [240, 800]}, {"parameters": {"operation": "create", "resource": "event", "calendarId": "primary", "summary": "={{ $json.title }}", "start": {"dateTime": "={{ $json.startDateTime }}"}, "end": {"dateTime": "={{ $json.endDateTime }}"}, "description": "={{ $json.description }}", "attendees": "={{ $json.attendees }}"}, "id": "calendar-create", "name": "Calendar Create Event", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 2, "position": [460, 750], "credentials": {"googleCalendarOAuth2": {"id": "calendar-credentials", "name": "Google Calendar OAuth2"}}}, {"parameters": {"operation": "getAll", "resource": "event", "calendarId": "primary", "returnAll": false, "limit": 20, "options": {"timeMin": "={{ $json.timeMin }}", "timeMax": "={{ $json.timeMax }}"}}, "id": "calendar-get", "name": "Calendar Get Events", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 2, "position": [460, 850], "credentials": {"googleCalendarOAuth2": {"id": "calendar-credentials", "name": "Google Calendar OAuth2"}}}, {"parameters": {"operation": "update", "resource": "event", "calendarId": "primary", "eventId": "={{ $json.eventId }}", "summary": "={{ $json.title }}", "start": {"dateTime": "={{ $json.startDateTime }}"}, "end": {"dateTime": "={{ $json.endDateTime }}"}, "description": "={{ $json.description }}"}, "id": "calendar-update", "name": "Calendar Update Event", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 2, "position": [460, 950], "credentials": {"googleCalendarOAuth2": {"id": "calendar-credentials", "name": "Google Calendar OAuth2"}}}, {"parameters": {"operation": "delete", "resource": "event", "calendarId": "primary", "eventId": "={{ $json.eventId }}"}, "id": "calendar-delete", "name": "Calendar Delete Event", "type": "n8n-nodes-base.googleCalendar", "typeVersion": 2, "position": [460, 1050], "credentials": {"googleCalendarOAuth2": {"id": "calendar-credentials", "name": "Google Calendar OAuth2"}}}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "You are the Company Knowledge Agent with access to the company's knowledge base. You can answer questions about company policies, procedures, guidelines, and general company information.\n\nUse the knowledge base to provide accurate, up-to-date information about:\n- Company policies and procedures\n- Employee guidelines and handbooks\n- Organizational structure and contacts\n- Company history and mission\n- Internal processes and workflows\n\nAlways base your answers on the information retrieved from the knowledge base and indicate when information might not be available.", "model": {"model": "gpt-4o", "type": "openai"}, "options": {"systemMessage": "You are a company knowledge specialist. Provide accurate information based on the company knowledge base."}, "memory": {"type": "bufferMemory", "sessionKey": "knowledge-session", "maxTokenLimit": 1000}, "tools": [{"name": "Company Knowledge Search", "description": "Search the company knowledge base", "nodeId": "company-knowledge-search"}]}, "id": "company-knowledge-agent", "name": "Company Knowledge Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [800, 500]}, {"parameters": {"operation": "retrieve", "pineconeIndex": "company-knowledge", "topK": 5, "query": "={{ $json.query }}", "options": {}}, "id": "company-knowledge-search", "name": "Company Knowledge Search", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1020, 500], "credentials": {"pineconeApi": {"id": "pinecone-credentials", "name": "Pinecone API"}}}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "You are the Company FAQ Agent specialized in answering frequently asked questions about the company. You have access to a curated FAQ knowledge base.\n\nYou can help with:\n- Common questions about company benefits\n- HR policies and procedures\n- IT support and technical questions\n- General company information\n- Employee resources and contacts\n\nProvide clear, concise answers based on the FAQ database and direct users to appropriate resources when needed.", "model": {"model": "gpt-4o", "type": "openai"}, "options": {"systemMessage": "You are a company FAQ specialist. Provide helpful answers to common company questions."}, "memory": {"type": "bufferMemory", "sessionKey": "faq-session", "maxTokenLimit": 1000}, "tools": [{"name": "Company FAQ Search", "description": "Search the company FAQ database", "nodeId": "company-faq-search"}]}, "id": "company-faq-agent", "name": "Company FAQ Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [800, 700]}, {"parameters": {"operation": "retrieve", "pineconeIndex": "company-faq", "topK": 3, "query": "={{ $json.query }}", "options": {}}, "id": "company-faq-search", "name": "Company FAQ Search", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1020, 700], "credentials": {"pineconeApi": {"id": "pinecone-credentials", "name": "Pinecone API"}}}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "You are the Personal Expenses Agent specialized in handling questions about personal expenses and financial data. You have access to a personal expenses database.\n\nYou can help with:\n- Expense tracking and categorization\n- Financial summaries and reports\n- Budget analysis and insights\n- Expense history and trends\n- Receipt and transaction details\n\nProvide accurate financial information based on the expenses database and help users understand their spending patterns.", "model": {"model": "gpt-4o", "type": "openai"}, "options": {"systemMessage": "You are a personal expenses specialist. Provide accurate financial insights based on expense data."}, "memory": {"type": "bufferMemory", "sessionKey": "expenses-session", "maxTokenLimit": 1000}, "tools": [{"name": "Personal Expenses Search", "description": "Search personal expenses database", "nodeId": "personal-expenses-search"}]}, "id": "personal-expenses-agent", "name": "Personal Expenses Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [800, 900]}, {"parameters": {"operation": "retrieve", "pineconeIndex": "personal-expenses", "topK": 10, "query": "={{ $json.query }}", "options": {}}, "id": "personal-expenses-search", "name": "Personal Expenses Search", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1020, 900], "credentials": {"pineconeApi": {"id": "pinecone-credentials", "name": "Pinecone API"}}}, {"parameters": {"httpMethod": "POST", "path": "upload", "responseMode": "responseNode", "options": {}}, "id": "file-upload-webhook", "name": "File Upload Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 1200], "webhookId": "file-upload"}, {"parameters": {"operation": "download", "fileUrl": "={{ $json.fileUrl }}", "options": {}}, "id": "file-download", "name": "Download File", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 1200]}, {"parameters": {"operation": "transcribe", "model": "whisper-1", "binaryPropertyName": "data", "options": {}}, "id": "audio-transcription", "name": "Audio Transcription", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [680, 1200], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"model": "tts-1", "input": "={{ $json.text }}", "voice": "alloy", "options": {}}, "id": "text-to-speech", "name": "Text to Speech", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [900, 1200], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"respondWith": "binaryData", "binaryPropertyName": "data"}, "id": "audio-response", "name": "Audio Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 1200]}, {"parameters": {"operation": "insert", "pineconeIndex": "company-knowledge", "documents": "={{ $json.documents }}", "options": {}}, "id": "knowledge-base-insert", "name": "Insert Company Knowledge", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [240, 1400], "credentials": {"pineconeApi": {"id": "pinecone-credentials", "name": "Pinecone API"}}}, {"parameters": {"operation": "insert", "pineconeIndex": "company-faq", "documents": "={{ $json.documents }}", "options": {}}, "id": "faq-insert", "name": "Insert Company FAQ", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [460, 1400], "credentials": {"pineconeApi": {"id": "pinecone-credentials", "name": "Pinecone API"}}}, {"parameters": {"operation": "insert", "pineconeIndex": "personal-expenses", "documents": "={{ $json.documents }}", "options": {}}, "id": "expenses-insert", "name": "Insert Personal Expenses", "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [680, 1400], "credentials": {"pineconeApi": {"id": "pinecone-credentials", "name": "Pinecone API"}}}], "connections": {"Chat Webhook": {"main": [[{"node": "Orchestration Agent", "type": "main", "index": 0}]]}, "Orchestration Agent": {"main": [[{"node": "Chat Response", "type": "main", "index": 0}]]}, "Email Agent": {"main": [[{"node": "Gmail Send", "type": "main", "index": 0}, {"node": "Gmail Search", "type": "main", "index": 0}, {"node": "Gmail Add Label", "type": "main", "index": 0}]]}, "Calendar Agent": {"main": [[{"node": "Calendar Create Event", "type": "main", "index": 0}, {"node": "Calendar Get Events", "type": "main", "index": 0}, {"node": "Calendar Update Event", "type": "main", "index": 0}, {"node": "Calendar Delete Event", "type": "main", "index": 0}]]}, "Company Knowledge Agent": {"main": [[{"node": "Company Knowledge Search", "type": "main", "index": 0}]]}, "Company FAQ Agent": {"main": [[{"node": "Company FAQ Search", "type": "main", "index": 0}]]}, "Personal Expenses Agent": {"main": [[{"node": "Personal Expenses Search", "type": "main", "index": 0}]]}, "File Upload Webhook": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Audio Transcription", "type": "main", "index": 0}]]}, "Audio Transcription": {"main": [[{"node": "Orchestration Agent", "type": "main", "index": 0}]]}, "Text to Speech": {"main": [[{"node": "Audio Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-07-25T00:00:00.000Z", "updatedAt": "2025-07-25T00:00:00.000Z", "id": "ai-agents", "name": "AI Agents"}, {"createdAt": "2025-07-25T00:00:00.000Z", "updatedAt": "2025-07-25T00:00:00.000Z", "id": "automation", "name": "Automation"}, {"createdAt": "2025-07-25T00:00:00.000Z", "updatedAt": "2025-07-25T00:00:00.000Z", "id": "multi-agent", "name": "Multi-Agent"}], "triggerCount": 2, "updatedAt": "2025-07-25T00:00:00.000Z", "versionId": "1"}