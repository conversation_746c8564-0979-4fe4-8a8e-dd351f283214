{"name": "X Scaper - Shared", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "bc47f40e-0850-4762-9efb-999608a2c096", "name": "counter", "value": "={{ $json.count }}", "type": "number"}, {"id": "92034c06-3976-4099-a7a5-28a252204472", "name": "cursor", "value": "={{ $json.cursor }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-100, 240], "id": "f60cc372-4b6a-491f-be58-c2f1dcf2b64d", "name": "Counter"}, {"parameters": {"assignments": {"assignments": [{"id": "b2f9c3c3-32f4-48b4-b72f-76f03c35b750", "name": "count", "value": 1, "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-340, 240], "id": "c60dd07b-3bad-489b-9ef8-ed916878cb08", "name": "Set Count"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "928894d0-2876-4996-9b1e-3d365b903771", "leftValue": "={{ $('Counter').item.json.counter }}", "rightValue": 3, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [880, 240], "id": "aa32cfa0-de5f-4f62-9469-9d0e61b4c01a", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "a252eb06-03cd-4e3f-84b0-2a46082606ca", "name": "counter", "value": "={{ $('Counter').item.json.counter }}", "type": "number"}, {"id": "43222ff8-c354-4afa-b050-7d55fe30c865", "name": "cursor", "value": "={{ $('Get Tweets').item.json.next_cursor }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [200, 560], "id": "5a2b91c8-ca8a-4692-9cfd-1c88f365c4e5", "name": "Set Increase"}, {"parameters": {"jsCode": "// This code should be placed in an n8n Function node\n\n// Get the input items\nconst items = $input.all();\n\n// Process each item in the array\nreturn items.map(item => {\n  // Create a new value for the count field\n  let newCount = 1;\n  \n  // If there's an existing counter value, use it as a base\n  if (item.json && item.json.counter !== undefined) {\n    newCount = item.json.counter + 1;\n  }\n  \n  // Ensure json property exists\n  if (!item.json) {\n    item.json = {};\n  }\n  \n  // Set the value to the new field name \"count\"\n  item.json.count = newCount;\n  \n  // Return the modified item\n  return item;\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [380, 560], "id": "219cb24c-801a-42ff-9b2f-358f6b7c9110", "name": "Increase Count"}, {"parameters": {"assignments": {"assignments": [{"id": "5be1f203-28ea-4635-b42d-01f2a5bb367f", "name": "count", "value": "={{ $json.count }}", "type": "string"}, {"id": "b68f8c17-c045-4b5b-8f8b-367ec72b72a3", "name": "cursor", "value": "={{ $('Set Increase').item.json.cursor }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [560, 560], "id": "6ee668c6-c9d4-45f5-929c-a674bee5edff", "name": "Set Count and Cursor"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1fo9UcILZsQKgpiaGVLeodjGkZRAYu1ROOpbTzdTNE3k", "mode": "list", "cachedResultName": "n8n - Twitter/X Data Template ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1fo9UcILZsQKgpiaGVLeodjGkZRAYu1ROOpbTzdTNE3k/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1_GeFBfE6vFYFv0C-e_HmpKMB6kDnq676IeidTaWFg0Y/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Tweet ID": "={{ $json.tweetId }}", "URL": "={{ $json.url }}", "Content": "={{ $json.content }}", "Likes": "={{ $json.likeCount }}", "Retweets": "={{ $json.retweetCount }}", "Replies": "={{ $json.replyCount }}", "Quotes": "={{ $json.quoteCount }}", "Views": "={{ $json.viewCount }}", "Date": "={{ $json.createdAt }}"}, "matchingColumns": [], "schema": [{"id": "Tweet ID", "displayName": "Tweet ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Content", "displayName": "Content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Replies", "displayName": "Replies", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Date", "displayName": "Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [520, 240], "id": "28536a4d-9d72-403e-9a87-be62c2065345", "name": "Add to Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "J7Jb3qY6SUr2btZB", "name": "Riverstreet Media Gsheets"}}}, {"parameters": {"jsCode": "// This approach handles both single tweets and collections\n// It focuses on properly formatting the output for n8n\n// First, let's log what we're working with for debugging\nconsole.log(\"Input item structure:\", JSON.stringify($input.item, null, 2));\n\n// Function to format the date in a more human-readable way\nfunction formatDate(dateString) {\n  if (!dateString) return '';\n  \n  try {\n    const date = new Date(dateString);\n    // Format: \"March 13, 2025 at 19:25\"\n    return date.toLocaleString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  } catch (error) {\n    console.log(\"Error formatting date:\", error);\n    return dateString; // Return original if parsing fails\n  }\n}\n\n// Check if this is a Twitter Search result with multiple tweets\nif ($input.item.json.tweets && Array.isArray($input.item.json.tweets) && $input.item.json.tweets.length > 0) {\n  // This is a collection of tweets\n  // In n8n, to output multiple items, we need to use an array of objects with a json property\n  const items = $input.item.json.tweets.map(tweet => {\n    return {\n      json: {\n        tweetId: tweet.id || '',\n        url: tweet.url || '',\n        content: tweet.text || '',\n        likeCount: tweet.likeCount || 0,\n        retweetCount: tweet.retweetCount || 0,\n        replyCount: tweet.replyCount || 0,\n        quoteCount: tweet.quoteCount || 0,\n        viewCount: tweet.viewCount || 0,\n        createdAt: formatDate(tweet.createdAt)\n      }\n    };\n  });\n  \n  console.log(`Processing ${items.length} tweets`);\n  \n  // Return all items\n  return items;\n} else {\n  // This is a single tweet, just extract and return its data\n  const tweetData = {\n    tweetId: $input.item.json.id || '',\n    url: $input.item.json.url || '',\n    content: $input.item.json.text || '',\n    likeCount: $input.item.json.likeCount || 0,\n    retweetCount: $input.item.json.retweetCount || 0,\n    replyCount: $input.item.json.replyCount || 0,\n    quoteCount: $input.item.json.quoteCount || 0,\n    viewCount: $input.item.json.viewCount || 0,\n    createdAt: formatDate($input.item.json.createdAt)\n  };\n  \n  console.log(\"Processing single tweet\");\n  \n  // Return as a single item\n  return {\n    json: tweetData\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, 240], "id": "37c77f40-7a96-4950-9568-b2a8e66809dc", "name": "Extract Info"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1100, 240], "id": "5263ee2a-b0f1-419b-8552-55d5e6408ee4", "name": "No Operation, do nothing"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [40, 560], "id": "78ff5660-6591-4dc5-b266-c0845edef03d", "name": "Limit"}, {"parameters": {"url": "https://api.twitterapi.io/twitter/tweet/advanced_search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('On form submission').item.json['Keyword To Scrape'] }}"}, {"name": "queryType", "value": "={{ $('On form submission').item.json['Latest or Top Posts'] }}"}, {"name": "cursor", "value": "={{ $json.cursor }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [100, 240], "id": "8ea650af-303a-4da8-ae46-291c48eb43bc", "name": "Get Tweets", "credentials": {"httpHeaderAuth": {"id": "KNmw1sp9SYE1TPKX", "name": "twitterapi.io"}}}, {"parameters": {"content": "# Increasing Count & Cursor\n", "height": 260, "width": 1360, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-620, 500], "id": "05165a7a-577e-45c2-9d14-ac7b2b33734a", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Checking Count\n", "height": 260, "width": 500}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [800, 160], "id": "dafc5c29-db75-4e38-966d-b41ef2a63f06", "name": "Sticky Note2"}, {"parameters": {"content": "# Scrape X Based On Keyword ", "height": 260, "width": 1360, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-620, 160], "id": "de730f2e-7733-49d4-8972-b0a0674ec6c1", "name": "Sticky Note1"}, {"parameters": {"formTitle": "Twitter Scraper ", "formFields": {"values": [{"fieldLabel": "Keyword To Scrape", "placeholder": "n8n, AI automation, Gumloop", "requiredField": true}, {"fieldLabel": "Latest or Top Posts", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Top"}, {"option": "Latest"}]}, "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-560, 240], "id": "c885c866-704e-4f06-8474-127117eb3c35", "name": "On form submission", "webhookId": "74eacd4a-4b34-43f6-a18a-f62e5e6bf4b3"}], "pinData": {}, "connections": {"Counter": {"main": [[{"node": "Get Tweets", "type": "main", "index": 0}]]}, "Set Count": {"main": [[{"node": "Counter", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}], [{"node": "Limit", "type": "main", "index": 0}]]}, "Set Increase": {"main": [[{"node": "Increase Count", "type": "main", "index": 0}]]}, "Increase Count": {"main": [[{"node": "Set Count and Cursor", "type": "main", "index": 0}]]}, "Set Count and Cursor": {"main": [[{"node": "Counter", "type": "main", "index": 0}]]}, "Add to Sheet": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Extract Info": {"main": [[{"node": "Add to Sheet", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Set Increase", "type": "main", "index": 0}]]}, "Get Tweets": {"main": [[{"node": "Extract Info", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Set Count", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "78931df5-ed8b-48ab-a0ce-38d6df67a4c6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e0f3afdfa8cdf759f3628c3983ef08ff7c153a90839a4c34ddd866854a12653a"}, "id": "BmyTyf3Nao8UeWAL", "tags": []}