{"meta": {"instanceId": "e4f78845dfed9ddcfba1945ae00d12e9a7d76eab052afd19299228ce02349d86"}, "nodes": [{"id": "a8b14ffd-4a8b-4a3d-ba54-5997197e5457", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-2880, 340], "parameters": {"color": 6, "width": 429.96763122593137, "height": 322, "content": "### Get Apprropraite Data\nThese Nodes are to extract only the Required Data from the Webhook Node"}, "typeVersion": 1}, {"id": "c8ae8766-3942-4b8d-8815-9e96ab9dc1de", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-840, 340], "parameters": {"color": 5, "width": 677, "height": 660, "content": "### <PERSON>8<PERSON> has the Node to convert PDF to Text, but PDF should be converted from Text. Scanned Image PDF will not be converted. \n\nYou can use ohter nodes here to convert word file to text or JPG to Text, possiblities are limitless."}, "typeVersion": 1}, {"id": "529f5756-c1a2-4c41-8245-38164543eb8e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1334.2232779572741, 340], "parameters": {"color": 7, "width": 477.2230145794152, "height": 648.5051458745238, "content": "### Download the File.\nIf you are using S3 to Upload attachments you can use S3 node here to download the attachment\n"}, "typeVersion": 1}, {"id": "101c6544-d319-495d-a14f-e180f51be1f0", "name": "Code", "type": "n8n-nodes-base.code", "position": [-2840, 480], "parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n item.json.myNewField = 1;\n}\n\nreturn $input.all();"}, "typeVersion": 2}, {"id": "5d0a1090-9be9-41e1-9a15-26bd6498fd95", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-3300, 340], "parameters": {"color": 5, "width": 398, "height": 642, "content": "### Applicant Applied for Job in ERPNext\n\nYou need to go to Webhooks in ERPNext and Create a Webhook on Job Applicant Doc Type and trigger should be on insert.\n\nFirst do the Test Wehbook and Pin the Webhook.\n\nFollow the Tutorial"}, "typeVersion": 1}, {"id": "4ed5dfbf-9b16-4f62-83e2-abb63421809b", "name": "ApplicantData", "type": "n8n-nodes-base.set", "position": [-2640, 480], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "8d600a4f-14d3-4840-aea0-665d26e7771b", "name": "body", "type": "object", "value": "={{ $json.body }}"}]}}, "typeVersion": 3.4}, {"id": "4f141057-8dd6-429a-b48f-fd80b2666882", "name": "ERPNext - Reject if Resume not Attached", "type": "n8n-nodes-base.erpNext", "position": [-2380, 800], "parameters": {"docType": "Job%20Applicant", "operation": "update", "properties": {"customProperty": [{"field": "status", "value": "Rejected"}]}, "documentName": "={{ $('ApplicantData').item.json.body.name }}"}, "credentials": {"erpNextApi": {"id": "PInpnsxvPkvaiW0z", "name": "ERPNext account"}}, "typeVersion": 1}, {"id": "de0a9900-4109-4092-ad3b-f36f02517fdf", "name": "Applied Against Job", "type": "n8n-nodes-base.if", "position": [-2140, 460], "parameters": {"options": {"ignoreCase": true}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": false, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "abbbd6f3-838e-43fb-a809-6bfffb153244", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.body.Job_opening }}", "rightValue": "None"}]}}, "typeVersion": 2.2}, {"id": "1c8e889b-dcd9-4807-8528-a7f0300bf558", "name": "ERPNext - Hold Applicant", "type": "n8n-nodes-base.erpNext", "position": [-2180, 800], "parameters": {"docType": "Job%20Applicant", "operation": "update", "properties": {"customProperty": [{"field": "status", "value": "Hold"}]}, "documentName": "={{ $('ApplicantData').item.json.body.name }}"}, "credentials": {"erpNextApi": {"id": "PInpnsxvPkvaiW0z", "name": "ERPNext account"}}, "typeVersion": 1}, {"id": "548c61dd-bcc3-4a7f-970e-5ff734926499", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-140, 343.*************], "parameters": {"color": 6, "width": 258, "height": 638.*************, "content": "### Get Job Opening Data\nFrom ERPNext\n\nAs Job is applied against a specific Job, you can now get the Job Opening Data which must be having the Job Description."}, "typeVersion": 1}, {"id": "6266e3e0-9bd7-4ab6-a488-55f1b524000f", "name": "Get Job Opening", "type": "n8n-nodes-base.erpNext", "position": [-80, 620], "parameters": {"docType": "Job%20Opening", "operation": "get", "documentName": "={{ $('ApplicantData').item.json.body.Job_opening }}"}, "credentials": {"erpNextApi": {"id": "PInpnsxvPkvaiW0z", "name": "ERPNext account"}}, "typeVersion": 1}, {"id": "536755cf-19d6-44a4-8449-635f4562b61f", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [140, 320], "parameters": {"color": 4, "width": 406.*************, "height": 662, "content": "### AI Agent to do its Job\nOpenAI\n\nSee full prompt which tells this AI Agent that you are a recruitment sepcialist and all the roles are defined in this node.\n\nIt will shortlist the candidate and will give the descriptive output with candidate fitlevel, score, rating and justification."}, "typeVersion": 1}, {"id": "0072826c-0e24-4c1f-89f9-388dc6e59ae5", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [980, 320], "parameters": {"color": 6, "width": 438, "height": 662, "content": "### Update data in ERPNext\nHere First node will do the formatting of the data and will convert output from AI Agent to Appropriate Fields which are defined in ERPNext and then will Send to Next Node\n\n### ERPNext (HTTP Node)\nThis node will take the Data from Previouis Node and will update the Applicant Data in ERPNext"}, "typeVersion": 1}, {"id": "28f23589-0608-4ed0-9e70-b6cbd31aa387", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [220, 800], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "pKFvSpPWSRFpnBoB", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "8bdd8f1c-e546-4ffb-a058-a3271fcad156", "name": "Convert to <PERSON>", "type": "n8n-nodes-base.code", "position": [660, 620], "parameters": {"jsCode": "// Input text from the `output` field\nconst textOutput = $json.output || '';\n\n// Function to extract values from the text\nfunction extractFields(text) {\n const fields = {};\n\n // Regular expressions to extract each field\n const fitLevelMatch = text.match(/FitLevel:\\s*(.+)\\n/);\n const scoreMatch = text.match(/Score:\\s*(\\d+)\\n/);\n const ratingMatch = text.match(/Rating:\\s*(\\d+)\\n/);\n const justificationMatch = text.match(/Justification:\\s*([\\s\\S]+)/);\n\n // Assign extracted values to the fields\n fields.fit_level = fitLevelMatch ? fitLevelMatch[1].trim() : null;\n fields.score = scoreMatch ? scoreMatch[1].trim() : null;\n fields.applicant_rating = ratingMatch ? ratingMatch[1].trim() : null;\n fields.justification_by_ai = justificationMatch ? justificationMatch[1].trim() : null;\n\n return fields;\n}\n\n// Extract fields from the output\nconst extractedFields = extractFields(textOutput);\n\n// Return the fields as JSO<PERSON>\nreturn {\n json: extractedFields\n};\n"}, "typeVersion": 2}, {"id": "ae1dc677-9ad7-4138-969f-2ee73b8537f9", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1460, 320], "parameters": {"color": 5, "width": 226.43884349833064, "height": 682, "content": "### Selected or Rejected\n\nThe criteia for score is that if Score is 80 or above Candidate will be selected <PERSON><PERSON> Rejected."}, "typeVersion": 1}, {"id": "090f4f22-639a-4fc7-8351-04dafcc09638", "name": "If score less than 80", "type": "n8n-nodes-base.if", "position": [1520, 620], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "5f845292-f4aa-44fb-a644-06c0736c3503", "operator": {"type": "number", "operation": "lt"}, "leftValue": "={{ $('Convert to <PERSON>').item.json.score }}", "rightValue": 80}]}, "looseTypeValidation": true}, "typeVersion": 2.2}, {"id": "0d7a84c2-0b1d-4e61-a3df-b81dd7981f1a", "name": "Reject Applicant", "type": "n8n-nodes-base.httpRequest", "position": [1880, 460], "parameters": {"url": "=https://erpnext.syncbricks.com/api/resource/Job Applicant/{{ $('ApplicantData').item.json.body.name }}", "method": "PUT", "options": {}, "jsonBody": "={\n \"status\": \"Rejected\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{}]}, "nodeCredentialType": "erpNextApi"}, "credentials": {"erpNextApi": {"id": "PInpnsxvPkvaiW0z", "name": "ERPNext account"}}, "typeVersion": 4.2}, {"id": "a55e70d1-7070-407d-9107-41cb33d2f0ae", "name": "Update Applicant Data", "type": "n8n-nodes-base.httpRequest", "position": [1140, 620], "parameters": {"url": "=https://erpnext.syncbricks.com/api/resource/Job Applicant/{{ $('ApplicantData').item.json.body.name }}", "method": "PUT", "options": {}, "jsonBody": "={\n \"applicant_rating\": \"{{ $json.applicant_rating }}\",\n \"custom_justification_by_ai\": \"{{ $json.justification_by_ai }}\",\n \"custom_fit_level\": \"{{ $json.fit_level }}\",\n \"custom_score\":\"{{ $json.score }}\"\n}\n\n\n\n\n", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{}]}, "nodeCredentialType": "erpNextApi"}, "credentials": {"erpNextApi": {"id": "PInpnsxvPkvaiW0z", "name": "ERPNext account"}}, "typeVersion": 4.2}, {"id": "044d26cb-2b34-4613-8d1d-20f40b47da29", "name": "Reume Attachment Link", "type": "n8n-nodes-base.set", "position": [-1820, 560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "7d0b8b98-0976-4c19-bc7c-738fabd60d28", "name": "body.resume_attachment", "type": "string", "value": "={{ $json.body.resume_link }}"}]}}, "typeVersion": 3.4}, {"id": "a17e9c94-0ec7-430f-adf3-109bcccf3bcb", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-2874.************, 680], "parameters": {"color": 3, "width": 875.************, "height": 302, "content": "## Resume Available?\nFirst Node : If Resume is not attached or Then Job Applicant will be Rejected\nSecond Node : If job is not applied against opening then the Applicant will be kept on hold."}, "typeVersion": 1}, {"id": "e2508130-4ecd-4d19-b41c-293006d17507", "name": "Resume Link Provided", "type": "n8n-nodes-base.if", "position": [-2400, 480], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3cd3da7d-d0fb-43fd-be10-3c1e627846b9", "operator": {"type": "string", "operation": "startsWith"}, "leftValue": "={{ $json.body.resume_link }}", "rightValue": "http"}]}}, "typeVersion": 2.2}, {"id": "e6770961-05b2-4488-99b1-7d1a219f8372", "name": "Accept Applicant", "type": "n8n-nodes-base.httpRequest", "position": [1860, 760], "parameters": {"url": "=https://erpnext.syncbricks.com/api/resource/Job Applicant/{{ $('ApplicantData').item.json.body.name }}", "method": "PUT", "options": {}, "jsonBody": "={\n \"status\": \"Accepted\"\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "predefinedCredentialType", "headerParameters": {"parameters": [{}]}, "nodeCredentialType": "erpNextApi"}, "credentials": {"erpNextApi": {"id": "PInpnsxvPkvaiW0z", "name": "ERPNext account"}}, "typeVersion": 4.2}, {"id": "6b7ad5c5-2a6f-4bb8-8296-3defb40f9605", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [-3800, 340], "parameters": {"color": 4, "width": 475.**************, "height": 636.*************, "content": "## Developed by <PERSON><PERSON>\n\nThank you for using this workflow template. It has taken me countless hours of hard work, research, and dedication to develop, and I sincerely hope it adds value to your work.\n\nIf you find this template helpful, I kindly ask you to consider supporting my efforts. Your support will help me continue improving and creating more valuable resources.\n\nYou can contribute via PayPal here:\n\nhttp://paypal.me/pmptraining\n\nFor Full Course about ERPNext or Automation using AI follow below link\n\nhttp://lms.syncbricks.com\n\nAdditionally, when sharing this template, I would greatly appreciate it if you include my original information to ensure proper credit is given.\n\nThank you for your generosity and support!\nEmail : <EMAIL>\nhttps://linkedin.com/in/amjidali\nhttps://syncbricks.com\nhttps://youtube.com/@syncbricks"}, "typeVersion": 1}, {"id": "173c6341-2ab7-4ee5-a6bd-0770ae19c013", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [-3180, 620], "webhookId": "f003f8ea-1f24-457c-8f28-762bd7942023", "parameters": {"path": "syncbricks-com-tutorial-candidate-shortlist", "options": {}, "httpMethod": "POST"}, "typeVersion": 2}, {"id": "cb0a8dd4-6673-4043-af76-0bf4537a8173", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [-1980, 340], "parameters": {"color": 6, "width": 613.5767730410602, "height": 654.6630436071407, "content": "### Get file of Resume Detail from ERPNext Applicant\nExtract the Resume Download Link and Decide which Attachment type it is as every attachment will be treated differently.\nCurrently I provided only for PDF Version but you can add seperate flow for other versions"}, "typeVersion": 1}, {"id": "101938bd-75cd-4557-a44f-ba64c4181f70", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [-2420, 340], "parameters": {"color": 6, "width": 429.96763122593137, "height": 310.20584626167124, "content": "### Validate if the Resume is Available and It is against a specific Job\nThese Nodes are to extract only the Required Data from the Webhook Node"}, "typeVersion": 1}, {"id": "596bcbd5-b94a-4a06-aa83-f7d9dc6264be", "name": "File Type", "type": "n8n-nodes-base.switch", "position": [-1600, 560], "parameters": {"rules": {"values": [{"outputKey": "pdf", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "endsWith"}, "leftValue": "={{ $json.body.resume_attachment }}", "rightValue": ".pdf"}]}, "renameOutput": true}, {"outputKey": "doc", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "dabe1bd2-9aba-4a61-b0b3-08c22856b213", "operator": {"type": "string", "operation": "contains"}, "leftValue": "={{ $json.body.resume_attachment }}", "rightValue": ".doc"}]}, "renameOutput": true}, {"outputKey": ".jpg", "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1101fc2e-4220-4795-9342-58d88ea146ce", "operator": {"type": "string", "operation": "endsWith"}, "leftValue": "={{ $json.body.resume_attachment }}", "rightValue": ".jpg"}]}, "renameOutput": true}]}, "options": {}}, "typeVersion": 3.2}, {"id": "cc7f4ca0-8e9b-49de-a7f1-f1f7913d6dcc", "name": "Download PDF Resume", "type": "n8n-nodes-base.httpRequest", "position": [-1160, 460], "parameters": {"url": "={{ $json.body.resume_attachment }}", "options": {}}, "typeVersion": 4.2}, {"id": "1209643a-7133-45cf-98a3-e242e2e1766b", "name": "PDF to Text", "type": "n8n-nodes-base.extractFromFile", "position": [-720, 440], "parameters": {"options": {}, "operation": "pdf"}, "typeVersion": 1}, {"id": "17430d0d-7bca-4ed9-b80e-d1f81dad5d58", "name": "Txt File to Text (Example)", "type": "n8n-nodes-base.extractFromFile", "position": [-740, 680], "parameters": {"options": {}, "operation": "text"}, "typeVersion": 1}, {"id": "2ab3f083-66d7-4a28-9b00-b9dabcd84282", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [-420, 620], "parameters": {}, "typeVersion": 3}, {"id": "e45d5828-1e7b-454e-9a79-7ab0f60e3cd9", "name": "Recruitment AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [200, 620], "parameters": {"text": "=System Prompt : \nYou are a highly skilled AI agent trained to compare and analyze text from resumes against job descriptions. Your primary goal is to assess whether the candidate is a good fit for the role based on the given inputs. You will receive two inputs:\n\n1. **Job Description**: A detailed description of the responsibilities, qualifications, and skills required for a specific job role.\n2. **Resume Text**: A summary of a candidate's qualifications, skills, experience, and education.\n\nYour task is to:\n1. **Analyze Match**: Compare the candidate's resume text against the job description and assess the alignment of:\n - Required skills\n - Relevant experience\n - Educational background\n - Certifications\n - Keywords mentioned in both texts (e.g., specific tools, methodologies, or terminologies).\n\n2. **Assess Fit**: Determine if the candidate is a strong, moderate, or weak fit for the role. Assign a score from 0 to 100 based on relevance:\n - **Strong Fit**: 80–100 (<PERSON>didate meets or exceeds the majority of the job requirements).\n - **Moderate Fit**: 50–79 (Candidate meets some key requirements but lacks in others).\n - **Weak Fit**: Below 50 (Candidate does not align with the role requirements).\n\n3. **Provide Justification**: Include a brief explanation of why the candidate is or isn’t a good fit, highlighting strengths, gaps, or missing criteria.\n\nOutput Format:\n- **Fit Level**: [Strong Fit / Moderate Fit / Weak Fit]\n- **Score**: [0–100]\n- **Rating**: [0–5]\n- **Justification**: A concise summary of the reasoning behind the fit level.\n\nRemember to maintain a neutral and objective tone in your analysis and ensure that your assessment is solely based on the information provided in the inputs.\"\n\n\nProvide me the output in the following format:\n\nFitLevel\n<fitLevel>\n\nScore:\n<score>\n\nRating:\n<rating>\n\nJustification:\n<justification>\n\nBelow are the inputs \n\nJob Title : {{ $json.job_title }}\nJob Desription : {{ $json.description }}\n\n\nHere here Job Applican't text from Resume : \n{{ $('PDF to Text').item.json.text }}\n", "agent": "reActAgent", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "02d40fbc-3b2a-4350-bd01-2dcca11cf23b", "name": "Sticky Note14", "type": "n8n-nodes-base.stickyNote", "position": [560, 320], "parameters": {"color": 6, "width": 374.6910896370089, "height": 662, "content": "### Create Fields\n\nThis Java Script will convert the extracted fieldws from AI Agent and will create fields which are already created in ERPNext.\n\nEnsure to create below fields in ERPNext : \n\njustification_by_ai\nfit_level\nscore\n\napplicant_rating field is already there, which will be updated with 1 to 5 star"}, "typeVersion": 1}, {"id": "fdc011c2-e734-44fe-8a83-975247445d16", "name": "Sticky Note15", "type": "n8n-nodes-base.stickyNote", "position": [1720, 320], "parameters": {"color": 3, "width": 394.2810709723076, "height": 308.0357387860514, "content": "### Update data in ERPNext\nAPI Call\nCandidate Rejected"}, "typeVersion": 1}, {"id": "3c4174cc-b947-4461-87a6-a7dbd0e3c78d", "name": "Sticky Note16", "type": "n8n-nodes-base.stickyNote", "position": [1720, 660], "parameters": {"color": 4, "width": 385.4472695263088, "height": 308.0357387860514, "content": "### Update data in ERPNext\nAPI Call\nCandidate Rejected"}, "typeVersion": 1}, {"id": "0116aa6d-2783-487b-b457-47a6b7d69f02", "name": "Microsoft Outlook", "type": "n8n-nodes-base.microsoftOutlook", "position": [2240, 460], "parameters": {"additionalFields": {}}, "credentials": {"microsoftOutlookOAuth2Api": {"id": "9gy3uvf3pmBdpEsq", "name": "Microsoft Outlook Al Ansari"}}, "typeVersion": 2}, {"id": "f2eac576-3a17-46e8-8800-1ba250e53047", "name": "Sticky Note17", "type": "n8n-nodes-base.stickyNote", "position": [2140, 331.1661985540012], "parameters": {"color": 6, "width": 338.3336618143153, "height": 623.107990360008, "content": "### Notifiy Applicant either by Email or Whatsapp or SMS, Options are Limitless"}, "typeVersion": 1}, {"id": "d4a6e756-923c-47c8-9c12-b1e8dcc873ca", "name": "WhatsApp Business Cloud", "type": "n8n-nodes-base.whatsApp", "position": [2280, 760], "parameters": {"operation": "send", "requestOptions": {}, "additionalFields": {}}, "credentials": {"whatsAppApi": {"id": "E8IjegSMj5LTd8lA", "name": "WhatsApp Syncbricks Access Token Never Expires"}}, "typeVersion": 1}], "pinData": {}, "connections": {"Code": {"main": [[{"node": "ApplicantData", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Get Job Opening", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "File Type": {"main": [[{"node": "Download PDF Resume", "type": "main", "index": 0}]]}, "PDF to Text": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "ApplicantData": {"main": [[{"node": "Resume Link Provided", "type": "main", "index": 0}]]}, "Get Job Opening": {"main": [[{"node": "Recruitment AI Agent", "type": "main", "index": 0}]]}, "Accept Applicant": {"main": [[{"node": "WhatsApp Business Cloud", "type": "main", "index": 0}]]}, "Reject Applicant": {"main": [[{"node": "Microsoft Outlook", "type": "main", "index": 0}]]}, "Convert to Fields": {"main": [[{"node": "Update Applicant Data", "type": "main", "index": 0}]]}, "Applied Against Job": {"main": [[{"node": "Reume Attachment Link", "type": "main", "index": 0}], [{"node": "ERPNext - Hold Applicant", "type": "main", "index": 0}]]}, "Download PDF Resume": {"main": [[{"node": "PDF to Text", "type": "main", "index": 0}]]}, "Recruitment AI Agent": {"main": [[{"node": "Convert to <PERSON>", "type": "main", "index": 0}]]}, "Resume Link Provided": {"main": [[{"node": "Applied Against Job", "type": "main", "index": 0}], [{"node": "ERPNext - Reject if Resume not Attached", "type": "main", "index": 0}]]}, "If score less than 80": {"main": [[{"node": "Reject Applicant", "type": "main", "index": 0}], [{"node": "Accept Applicant", "type": "main", "index": 0}]]}, "Reume Attachment Link": {"main": [[{"node": "File Type", "type": "main", "index": 0}]]}, "Update Applicant Data": {"main": [[{"node": "If score less than 80", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Recruitment AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Txt File to Text (Example)": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}}}