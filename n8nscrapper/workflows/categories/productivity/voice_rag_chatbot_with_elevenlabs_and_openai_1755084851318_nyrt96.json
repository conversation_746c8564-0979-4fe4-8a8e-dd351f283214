{"id": "ibiHg6umCqvcTF4g", "meta": {"instanceId": "a4bfc93e975ca233ac45ed7c9227d84cf5a2329310525917adaf3312e10d5462", "templateCredsSetupCompleted": true}, "name": "Voice RAG Chatbot with ElevenLabs and OpenAI", "tags": [], "nodes": [{"id": "5898da57-38b0-4d29-af25-fe029cda7c4a", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-180, 800], "parameters": {"text": "={{ $json.body.question }}", "options": {}, "promptType": "define"}, "typeVersion": 1.7}, {"id": "81bbedb6-5a07-4977-a68f-2bdc75b17aba", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [20, 1040], "parameters": {"name": "company", "description": "Risponde alle domande relative a ciò che ti viene chiesto"}, "typeVersion": 1}, {"id": "fd021f6c-248d-41f4-a4f9-651e70692327", "name": "Qdrant Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [-140, 1300], "parameters": {"options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "84aca7bb-4812-498f-b319-88831e4ca412", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [-140, 1460], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "82e430db-2ad7-427d-bcf9-6aa226253d18", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [-760, 520], "parameters": {"color": 5, "width": 1400, "height": 240, "content": "# STEP 4\n\n## RAG System\n\nClick on \"test workflow\" on n8n and \"Test AI agent\" on ElevenLabs. If everything is configured correctly, when you ask a question to the agent, the webhook on n8n is activated with the \"question\" field in the body filled with the question asked to the voice agent.\n\nThe AI ​​Agent will extract the information from the vector database, send it to the model to create the response which will be sent via the response webhook to ElevenLabs which will transform it into voice"}, "typeVersion": 1}, {"id": "6a19e9fa-50fa-4d51-ba41-d03c999e4649", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-780, -880], "parameters": {"color": 3, "width": 1420, "height": 360, "content": "# STEP 1\n\n## Create an Agent on ElevenLabs \n- Create an agent on ElevenLabs (eg. test_n8n)\n- Add \"First message\" (eg. Hi, Can I help you?)\n- Add the \"System Prompt\" message... eg:\n'You are the waiter of \"Pizz<PERSON> da Michele\" in Verona. If you are asked a question, use the tool \"test_chatbot_elevenlabs\". When you receive the answer from \"test_chatbot_elevenlabs\" answer the user clearly and precisely.'\n- In Tools add a Webhook called eg. \"test_chatbot_elevenlabs\" and add the following description:\n'You are the waiter. Answer the questions asked and store them in the question field.'\n- Add the n8n webhook URL (method POST)\n- Enable \"Body Parameters\" and insert in the description \"Ask the user the question to ask the place.\", then in the \"Properties\" add a data type string called \"question\", value type \"LLM Prompt\" and description \"user question\""}, "typeVersion": 1}, {"id": "ec053ee7-3a4a-4697-a08c-5645810d23f0", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-740, -200], "parameters": {}, "typeVersion": 1}, {"id": "3e71e40c-a5cc-40cf-a159-aeedc97c47d1", "name": "Create collection", "type": "n8n-nodes-base.httpRequest", "position": [-440, -340], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION", "method": "POST", "options": {}, "jsonBody": "{\n \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "240283fc-50ec-475c-bd24-e6d0a367c10c", "name": "Refresh collection", "type": "n8n-nodes-base.httpRequest", "position": [-440, -80], "parameters": {"url": "https://QDRANTURL/collections/COLLECTION/points/delete", "method": "POST", "options": {}, "jsonBody": "{\n \"filter\": {}\n}", "sendBody": true, "sendHeaders": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "qhny6r5ql9wwotpn", "name": "Qdrant API (Hetzner)"}}, "typeVersion": 4.2}, {"id": "7d10fda0-c6ab-4bf5-b73e-b93a84937eff", "name": "Get folder", "type": "n8n-nodes-base.googleDrive", "position": [-220, -80], "parameters": {"filter": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive", "cachedResultUrl": "https://drive.google.com/drive/my-drive", "cachedResultName": "My Drive"}, "folderId": {"__rl": true, "mode": "id", "value": "=test-whatsapp"}}, "options": {}, "resource": "fileFolder"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "c5761ad2-e66f-4d65-b653-0e89ea017f17", "name": "Download Files", "type": "n8n-nodes-base.googleDrive", "position": [0, -80], "parameters": {"fileId": {"__rl": true, "mode": "id", "value": "={{ $json.id }}"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}, "operation": "download"}, "credentials": {"googleDriveOAuth2Api": {"id": "HEy5EuZkgPZVEa9w", "name": "Google Drive account"}}, "typeVersion": 3}, {"id": "1f031a11-8ef3-4392-a7db-9bca00840b8f", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [380, 120], "parameters": {"options": {}, "dataType": "binary"}, "typeVersion": 1}, {"id": "7f614392-7bc7-408c-8108-f289a81d5cf6", "name": "Token Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterTokenSplitter", "position": [360, 280], "parameters": {"chunkSize": 300, "chunkOverlap": 30}, "typeVersion": 1}, {"id": "648c5b3d-37a8-4a89-b88c-38e1863f09dc", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-240, -400], "parameters": {"color": 6, "width": 880, "height": 220, "content": "# STEP 2\n\n## Create Qdrant Collection\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "a6c50f3c-3c73-464e-9bdc-49de96401c1b", "name": "Qdrant Vector Store1", "type": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "position": [240, -80], "parameters": {"mode": "insert", "options": {}, "qdrantCollection": {"__rl": true, "mode": "id", "value": "=COLLECTION"}}, "credentials": {"qdrantApi": {"id": "iyQ6MQiVaF3VMBmt", "name": "QdrantApi account"}}, "typeVersion": 1}, {"id": "7e19ac49-4d90-4258-bd44-7ca4ffa0128a", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [220, 120], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1.1}, {"id": "bfa104a2-1f9c-4200-ae7b-4659894c1e6f", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-460, -140], "parameters": {"color": 4, "width": 620, "height": 400, "content": "# STEP 3\n\n\n\n\n\n\n\n\n\n\n\n\n## Documents vectorization with Qdrant and Google Drive\nChange:\n- QDRANTURL\n- COLLECTION"}, "typeVersion": 1}, {"id": "a148ffcf-335f-455d-8509-d98c711ed740", "name": "Respond to ElevenLabs", "type": "n8n-nodes-base.respondToWebhook", "position": [380, 800], "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "5d19f73a-b8e8-4e75-8f67-************", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-300, 1040], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "802b76e1-3f3e-490c-9e3b-65dc5b28d906", "name": "Listen", "type": "n8n-nodes-base.webhook", "position": [-700, 800], "webhookId": "e9f611eb-a8dd-4520-8d24-9f36deaca528", "parameters": {"path": "test_voice_message_elevenlabs", "options": {}, "httpMethod": "POST", "responseMode": "responseNode"}, "typeVersion": 2}, {"id": "bdc55a38-1d4b-48fe-bbd8-29bf1afd954a", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-140, 1040], "parameters": {}, "typeVersion": 1.3}, {"id": "2d5dd8cb-81eb-41bc-af53-b894e69e530c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [200, 1320], "parameters": {"options": {}}, "credentials": {"openAiApi": {"id": "CDX6QM4gLYanh0P4", "name": "OpenAi account"}}, "typeVersion": 1}, {"id": "92d04432-1dbb-4d79-9edc-42378aee1c53", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-760, 1620], "parameters": {"color": 7, "width": 1400, "height": 240, "content": "# STEP 5\n\n## Add Widget\n\nAdd the widget to your business website by replacing AGENT_ID with the agent id you created on ElevenLabs\n\n<elevenlabs-convai agent-id=\"AGENT_ID\"></elevenlabs-convai><script src=\"https://elevenlabs.io/convai-widget/index.js\" async type=\"text/javascript\"></script>"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "6738abfe-e626-488d-a00b-81021cb04aaf", "connections": {"Listen": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to ElevenLabs", "type": "main", "index": 0}]]}, "Get folder": {"main": [[{"node": "Download Files", "type": "main", "index": 0}]]}, "Download Files": {"main": [[{"node": "Qdrant Vector Store1", "type": "main", "index": 0}]]}, "Token Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Qdrant Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Qdrant Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Refresh collection": {"main": [[{"node": "Get folder", "type": "main", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Qdrant Vector Store1", "type": "ai_document", "index": 0}]]}, "Qdrant Vector Store": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Create collection", "type": "main", "index": 0}, {"node": "Refresh collection", "type": "main", "index": 0}]]}}}