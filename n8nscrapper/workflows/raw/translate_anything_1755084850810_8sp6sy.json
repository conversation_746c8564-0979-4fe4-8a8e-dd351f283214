{"name": "Translate anything", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "15mz8HluRZFbGjYgFGlyxuJraAQHC--ts", "mode": "list", "cachedResultName": "Transcribe files", "cachedResultUrl": ""}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1240, -460], "id": "ca04144c-810e-49dd-86f4-3530c0cbe21f", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.webViewLink }}", "mode": "url"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-940, -460], "id": "1288a5ef-27e4-4561-99dc-9d8a35a59852", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "", "name": "Google Drive account"}}}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/speech-to-text", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "xi-api-key", "value": ""}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "model_id", "value": "scribe_v1"}, {"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}, {"name": "additional_formats", "value": "[{\"format\":\"pdf\"}]"}, {"name": "diarize", "value": "true"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-300, -480], "id": "03469f5a-8925-43bb-8007-1d789ffea180", "name": "HTTP Request"}, {"parameters": {"method": "POST", "url": "https://api-free.deepl.com/v2/translate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "DeepL-Auth-Key "}]}, "sendBody": true, "contentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "text", "value": "={{ $json.text }}"}, {"name": "target_lang", "value": "FR"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [320, -480], "id": "50bf0a4c-cab6-4f4b-a3ba-52be04f1c156", "name": "HTTP Request2"}, {"parameters": {"folderId": "15mz8HluRZFbGjYgFGlyxuJraAQHC--ts", "title": "Translated File"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [580, -480], "id": "42a29127-0815-4343-9f59-92884bf0d252", "name": "Google Docs", "credentials": {"googleDocsOAuth2Api": {"id": "fD5HV588c8GQ9K8p", "name": "Google Docs account"}}}, {"parameters": {"operation": "update", "documentURL": "={{ $json.id }}", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $('HTTP Request2').item.json.translations[0].text }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [820, -480], "id": "d4854d1f-f976-4dbe-a39f-feb44ce2e28f", "name": "Google Docs1", "credentials": {"googleDocsOAuth2Api": {"id": "", "name": "Google Docs account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileExtension }}", "rightValue": "mp4", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.fileExtension }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-720, -460], "id": "fbae767c-cca4-46bd-a183-bd166cf8c137", "name": "Switch"}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-300, -260], "id": "64c065ac-5e4d-4098-bc78-f2a4d9b22939", "name": "Extract from File"}, {"parameters": {"assignments": {"assignments": [{"id": "0bca3111-7708-46f2-b0ba-f227f7600fe8", "name": "text", "value": "={{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [20, -480], "id": "37fefe63-0c61-4477-895f-5b7fb6ec25df", "name": "Edit Fields1"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}]]}, "Google Docs": {"main": [[{"node": "Google Docs1", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f40c679d-8352-4cfb-9995-c86727303e93", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f21f53385ae396fcb0c1b69950e1ec16f2dcc4ddca34a170466835249ec1c42c"}, "id": "rU01P73nO8AoZFfq", "tags": []}