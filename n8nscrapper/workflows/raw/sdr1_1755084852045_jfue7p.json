{"name": "sdr1", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [260, 20], "id": "ce82afd7-804e-4d99-9501-4b00dce04758", "name": "Form Response Trigger", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "jzOKoQHQ1fzJUKun", "name": "Google Sheets Trigger account"}}}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/actor-tasks/GOvL4O4RwFqsdIqXF/run-sync?token=joXn39gz5BW7dhpU2Vh3MCPT657h9e3usKOs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"startUrls\": [\n    {\n      \"url\": \"={{ $json.name }}\"\n    }\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 220], "id": "8f27fb2e-4344-4ac6-9ff5-99f76f6bcfde", "name": "Scrape LinkedIn Profile (Apify)"}, {"parameters": {"url": "https://api.apify.com/v2/actor-tasks/AjfNXEI9qTA2IdaAX/run-sync?token=YOUR_APIFY_TOKEN", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"startUrls\": [\n    {\n      \"url\": {{$json[\"LinkedIn Profile URL\"]}}\n    }\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 220], "id": "25aff63c-3520-4888-a13a-b566e6628533", "name": "Scrape Company LinkedIn (Apify)"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/actor-tasks/vFDBqKNXeGllTgOLz/run-sync?token=YOUR_APIFY_TOKEN", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"startUrls\": [\n    {\n      \"url\": {{$json[\"LinkedIn Profile URL\"]}}\n    }\n  ]\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [420, 220], "id": "52b153e6-2567-44cb-89b9-b15ecd157b49", "name": "Scrape Company Website (Apify)"}, {"parameters": {"url": "=https://api.hunter.io/v2/domain-search?domain={{$json[\"Website URL\"]}}&api_key=YOUR_HUNTER_API_KEY", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 400], "id": "38efca9a-f3f6-4ee3-bb37-262b23d7c1cd", "name": "<PERSON> Em<PERSON> (Hunter.io)"}, {"parameters": {"url": "=https://api.zerobounce.net/v2/validate?api_key=YOUR_ZB_API&email={{$json[\"Email (Scraped)\"]}}", "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 400], "id": "c105968b-25da-45b2-b550-6edb5e735979", "name": "<PERSON><PERSON><PERSON> (ZeroBounce)"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "name ", "displayName": "name ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contact details", "displayName": "contact details", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [420, 400], "id": "43110fb8-04c8-4c54-8d61-da9cf493e0aa", "name": "Append Enriched Data to Sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "5cGO5mtMhF2M4IKE", "name": "Google Sheets account"}}}, {"parameters": {"content": "## Lead Enrichment Pipeline \n(Form → Scrape → Email → Sheet)\n\n\n", "height": 600, "width": 660}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, -20], "id": "2c3f0559-12bf-4825-8d46-eed921ce8bb2", "name": "<PERSON><PERSON>"}], "pinData": {"Form Response Trigger": [{"json": {"name": "https://www.linkedin.com/company/tata-group/", "code": 1}}, {"json": {"name": "Second item", "code": 2}}]}, "connections": {"Form Response Trigger": {"main": [[{"node": "Scrape LinkedIn Profile (Apify)", "type": "main", "index": 0}]]}, "Scrape LinkedIn Profile (Apify)": {"main": [[{"node": "Scrape Company LinkedIn (Apify)", "type": "main", "index": 0}]]}, "Scrape Company LinkedIn (Apify)": {"main": [[{"node": "Scrape Company Website (Apify)", "type": "main", "index": 0}]]}, "Scrape Company Website (Apify)": {"main": [[{"node": "<PERSON> Em<PERSON> (Hunter.io)", "type": "main", "index": 0}]]}, "Find Email (Hunter.io)": {"main": [[{"node": "<PERSON><PERSON><PERSON> (ZeroBounce)", "type": "main", "index": 0}]]}, "Verify Email (ZeroBounce)": {"main": [[{"node": "Append Enriched Data to Sheet", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ca92d6d3-cab4-43b7-9c84-a040e93b9eb9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "57a3103abaee943256a1daab7aebcb0a5de91f02c7de16853df57583eefbff08"}, "id": "DB4X2In1fPrwdREe", "tags": []}