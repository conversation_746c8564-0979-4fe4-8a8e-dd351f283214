{"name": "sdr4", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "737c4f92-162d-4fa1-b963-d61db09460dd", "name": "Run Every Day at [Time]"}, {"parameters": {"documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [220, 0], "id": "06048b89-1fd3-4c76-a940-014aedd0db3d", "name": "\tRead All Leads from She<PERSON>", "credentials": {"googleSheetsOAuth2Api": {"id": "5cGO5mtMhF2M4IKE", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "bf9c2d91-5d40-49ff-bafc-ff318983d54d", "leftValue": "<PERSON><PERSON>", "rightValue": "true", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [440, 0], "id": "4ac4ea38-877f-473d-bc4a-3161d0c62bf3", "name": "Check if Email Already Sent"}, {"parameters": {"assignments": {"assignments": [{"id": "c9fa4277-0e82-4d89-88d4-0565490ab0f5", "name": "toEmail", "value": "=\t={{ $json[\"Email (Scraped)\"] }}", "type": "string"}, {"id": "e3a43c47-7589-4bf2-a39d-04de8f41d32a", "name": "subject", "value": "={{ \"Hey \" + $json[\"First Name\"] + \", quick note\" }}", "type": "string"}, {"id": "4530ae63-e189-4fff-b727-f0e8460256d6", "name": "body", "value": "=={{   \"Hey \" + $json[\"First Name\"] + \",\\n\\n\" +  $json[\"Outreach Line\"] + \"\\n\\n\" +  \"Would love to chat for 10 mins if you're open.\\n\\n\" +  \"Cheers,\\nYour Name\"}}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [0, 220], "id": "8f66ea56-3a25-4893-954b-0aeca043a5a7", "name": "Compose Personalized Email"}, {"parameters": {"sendTo": "=\t={{ $json[\"toEmail\"] }}", "subject": "=\t={{ $json[\"subject\"] }}", "message": "={{ $json[\"body\"] }}", "options": {"senderName": "Your Name"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [220, 220], "id": "4d273fbd-4e99-46d6-98af-09b93d3d4817", "name": "Send Email via Gmail", "webhookId": "b8dd1a30-a164-4a31-9ac7-ba37b0d1cebe", "credentials": {"gmailOAuth2": {"id": "FeHvI24ngA7KxiO9", "name": "Gmail account"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "name ", "displayName": "name ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contact details", "displayName": "contact details", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [440, 220], "id": "********-7947-4cc9-ad11-2240fef1403f", "name": "\t<PERSON> as Emailed", "credentials": {"googleSheetsOAuth2Api": {"id": "5cGO5mtMhF2M4IKE", "name": "Google Sheets account"}}}, {"parameters": {"content": "## Automated Cold Email Campaign \n(Scheduled Gmail Sender)", "height": 500, "width": 700, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, -80], "id": "bd1ae229-6fc3-4bb2-8fc5-774ccf7d3a78", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Run Every Day at [Time]": {"main": [[{"node": "\tRead All Leads from She<PERSON>", "type": "main", "index": 0}]]}, "\tRead All Leads from Sheet": {"main": [[{"node": "Check if Email Already Sent", "type": "main", "index": 0}]]}, "Check if Email Already Sent": {"main": [[{"node": "Compose Personalized Email", "type": "main", "index": 0}]]}, "Compose Personalized Email": {"main": [[{"node": "Send Email via Gmail", "type": "main", "index": 0}]]}, "Send Email via Gmail": {"main": [[{"node": "\t<PERSON> as Emailed", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "2b55b2d7-87ac-4e56-8749-043690e2defb", "meta": {"templateCredsSetupCompleted": true, "instanceId": "57a3103abaee943256a1daab7aebcb0a5de91f02c7de16853df57583eefbff08"}, "id": "LnzuXFCmIwwsKjv4", "tags": []}