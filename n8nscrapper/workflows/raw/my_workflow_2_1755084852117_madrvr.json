{"name": "My workflow 2", "nodes": [{"parameters": {"httpMethod": "POST", "path": "c2b1dbbe-3a93-40bc-98b5-39137319746b", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-320, -60], "id": "5cda8a3b-8b6f-45e6-a700-1bf70730e9e9", "name": "Webhook", "webhookId": "c2b1dbbe-3a93-40bc-98b5-39137319746b"}, {"parameters": {"url": "=https://twitter154.p.rapidapi.com/user/details?username=omarmhaimdat&user_id=96479162", "sendQuery": true, "queryParameters": {"parameters": [{"name": "username", "value": "={{ $json.body.username }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-rapidapi-host", "value": "twitter154.p.rapidapi.com"}, {"name": "x-rapidapi-key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-100, -60], "id": "54f55957-b26a-4079-8303-fbd66041a6c8", "name": "Twitter Info"}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [120, -60], "id": "e77d250d-8f67-4862-8181-3bb2134bf602", "name": "Respond to Webhook"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Twitter Info", "type": "main", "index": 0}]]}, "Twitter Info": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8ff84ee5-1724-40e8-bf76-6f7089c83088", "meta": {"instanceId": "2e6ba6949158f8a76dc4840223dd935ba237136507121fcfa4e64a20747452b4"}, "id": "xop0WxGvoLupVMqq", "tags": []}