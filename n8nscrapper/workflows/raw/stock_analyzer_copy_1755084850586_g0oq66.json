{"name": "Stock Analyzer copy", "nodes": [{"parameters": {"modelName": "models/gemini-2.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [160, 140], "id": "f5a4288c-2089-4bc0-ba4c-9c412a853270", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1140, 160], "id": "9fb7f207-46fa-481b-a0a4-8df9e530bfbc", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [760, 160], "id": "a1755a49-f5ea-4443-a109-ff0a55a36725", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1580, -140], "id": "ceadb9e7-2269-4bbe-96bc-bbb3af63d484", "name": "Google Gemini Chat Model3", "credentials": {"googlePalmApi": {"id": "jjGURh3cp1oV0TXd", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "options": {"systemMessage": "=You are an API URL generator for stock analysis using Twelve Data API. Your goal is to generate a complete URL based on user requests for stock data analysis.\n\nUse this URL format:\nhttps://api.twelvedata.com/time_series?symbol={{SYMBOL}}&interval={{INTERVAL}}&outputsize={{OUTPUTSIZE}}&apikey={{API_KEY}}\n\nFill the placeholders:\n- SYMBOL = stock ticker (with exchange, e.g. TATAMOTORS.BSE or AAPL)\n- INTERVAL = 1min, 5min, 15min, 1day, 1week, or 1month\n- OUTPUTSIZE = number of data points (e.g. 1 for latest day, 5 for past 5 days)\n- API_KEY = just use `{{API_KEY}}`\n\n\n🔑 Here's the Twelve Data API Key: \n\n\n\nIf the user says:  \n\"Give me TATA Motors data for the last 3 days\" → Output:  \nhttps://api.twelvedata.com/time_series?symbol=TATAMOTORS.BSE&interval=1day&outputsize=3&apikey={{API_KEY}}\n\nOnly return the full URL. No explanation.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [160, 0], "id": "d0a47a65-711a-4f00-bcf7-b5cfe701d7fa", "name": "Twelve URL Generator"}, {"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-140, 0], "id": "f6ea30ed-b8bd-4ca0-ab12-1b56b88aafd4", "name": "GET Message", "webhookId": "2997e70e-5c87-4984-9ccd-d7479a7a67c2", "credentials": {"telegramApi": {"id": "JTYvKHnofhmFlk9A", "name": "Telegram account"}}}, {"parameters": {"url": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 0], "id": "fb1dc7a8-a927-4169-a85c-4d7a6599d1d1", "name": "TWELVE STOCK DATA"}, {"parameters": {"promptType": "define", "text": "=You are a JSON generator for chart-img.com using TradingView chart configuration.\n\nHere's the Stock data:\nSymbol: {{ $json.meta.symbol }}\nInterval: {{ $json.meta.interval }}\ncurrency: {{ $json.meta.currency }}\nexchange_timezone: {{ $json.meta.exchange_timezone }}\nexchange: {{ $json.meta.exchange }}\nmic_code: {{ $json.meta.mic_code }}\ntype: {{ $json.meta.type }}\ndatetime: {{ $json.values[0].datetime }}\nopen: {{ $json.values[0].open }}\nhigh: {{ $json.values[0].high }}\nlow: {{ $json.values[0].low }}\nclose: {{ $json.values[0].close }}\nvolume: {{ $json.values[0].volume }}\nstatus: {{ $json.status }}\n\nYour task is to create a JSON object that works with the Chart-IMG Advanced TradingView chart API.\n\nFrom the Stock Data JSON:\n- Extract the `symbol` and `exchange` from the `meta` section.\n- Convert them into TradingView format: \"<EXCHANGE>:<SYMBOL>\", e.g. \"NASDAQ:TSLA\".\n- Use the `interval` and convert it to TradingView format:\n  - \"1day\" → \"1D\", \"1week\" → \"1W\", \"1month\" → \"1M\"\n\nYour output must be in this format:\n\n{\n  \"theme\": \"dark\",\n  \"interval\": \"1W\",\n  \"symbol\": \"NASDAQ:MSFT\",\n  \"override\": {\n    \"showStudyLastValue\": false\n  },\n  \"studies\": [\n    {\n      \"name\": \"Volume\",\n      \"forceOverlay\": true\n    },\n    {\n      \"name\": \"MACD\",\n      \"override\": {\n        \"Signal.linewidth\": 2,\n        \"Signal.color\": \"rgb(255,65,129)\"\n      }\n    }\n  ]\n}\n\nOnly return the final JSON — no comments or extra explanation.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [800, 0], "id": "08fd23d7-ae82-45d0-a6ff-6c7ea8bdbae7", "name": "CHART IMG JSON"}, {"parameters": {"text": "={{ $json.output }}", "attributes": {"attributes": [{"name": "json", "description": "=transform the data in to a valid json"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.2, "position": [1160, 0], "id": "567c5e04-d7ef-459d-a6df-0f074b689559", "name": "JSON PURIFY"}, {"parameters": {"promptType": "define", "text": "=Stock Data:\nSymbol: {{ $('TWELVE STOCK DATA').item.json.meta.symbol }}\nInterval: {{ $('TWELVE STOCK DATA').item.json.meta.interval }}\ncurrency: {{ $('TWELVE STOCK DATA').item.json.meta.currency }}\nexchange_timezone: {{ $('TWELVE STOCK DATA').item.json.meta.exchange_timezone }}\nexchange: {{ $('TWELVE STOCK DATA').item.json.meta.exchange }}\nmic_code: {{ $('TWELVE STOCK DATA').item.json.meta.mic_code }}\ntype: {{ $('TWELVE STOCK DATA').item.json.meta.type }}\ndatetime: {{ $('TWELVE STOCK DATA').item.json.values[0].datetime }}\nopen: {{ $('TWELVE STOCK DATA').item.json.values[0].open }}\nhigh: {{ $('TWELVE STOCK DATA').item.json.values[0].high }}\nlow: {{ $('TWELVE STOCK DATA').item.json.values[0].low }}\nclose: {{ $('TWELVE STOCK DATA').item.json.values[0].close }}\nvolume: {{ $('TWELVE STOCK DATA').item.json.values[0].volume }}\nstatus: {{ $('TWELVE STOCK DATA').item.json.status }}\n\nfrom this stock data give me a info like this format:\n\nhere's an example:\n📊 TSLA Stock Summary – June 24, 2025\nExchange: NASDAQ\nInterval: 1 Day\nCurrency: USD\nOpen Price: $356.14\nHigh Price: $356.20\nLow Price: $340.50\nClose Price: $342.30\nVolume: 1,305,456\nChart Style: Candlestick\nIndicators: MACD, Volume\n\n📈 See the chart above for technical view.\n\nand lastly add a 2-sentence analysis of this stock’s performance today based on OHLC and volume.\n\nwithout these 2 things don't add any extra info.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1580, -300], "id": "63e74d55-0eda-457f-9a68-702c6d255207", "name": "STOCK ANALYZER"}, {"parameters": {"method": "POST", "url": "=https://api.chart-img.com/v2/tradingview/advanced-chart", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-api-key"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $('JSON PURIFY').item.json.output.json }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2120, -100], "id": "a6640c45-ecab-43ad-842d-2e2ebf047592", "name": "CHART IMAGE GENERATION"}, {"parameters": {"operation": "sendPhoto", "chatId": "={{ $('GET Message').item.json.message.chat.id }}", "binaryData": true, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2640, -220], "id": "596abb00-4685-4292-8dd7-74c970b709a5", "name": "IMAGE", "webhookId": "58cb2bcb-caa3-42bd-a554-e5cdb566d06b", "credentials": {"telegramApi": {"id": "JTYvKHnofhmFlk9A", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $('GET Message').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2660, 0], "id": "158169e3-75be-4b50-b98f-5b341fd6bb02", "name": "TEXT", "webhookId": "e0ce23b6-e997-40fb-b9da-a17298a63012", "credentials": {"telegramApi": {"id": "JTYvKHnofhmFlk9A", "name": "Telegram account"}}}, {"parameters": {"content": "## TWELVE DATA\n[TWELVE DATA REGISTER](https://twelvedata.com/register)", "height": 380, "width": 400, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, -100], "id": "51c542a6-32bf-4251-b32f-816f2f2c0c45", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## CHART IMAGE\n[CHART-IMG REGISTER](https://chart-img.com/)", "height": 380, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2000, -220], "id": "415af3ce-22e4-4a1b-9021-661e5b665152", "name": "Sticky Note1"}], "pinData": {}, "connections": {"Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Twelve URL Generator", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "JSON PURIFY", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "CHART IMG JSON", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model3": {"ai_languageModel": [[{"node": "STOCK ANALYZER", "type": "ai_languageModel", "index": 0}]]}, "Twelve URL Generator": {"main": [[{"node": "TWELVE STOCK DATA", "type": "main", "index": 0}]]}, "GET Message": {"main": [[{"node": "Twelve URL Generator", "type": "main", "index": 0}]]}, "TWELVE STOCK DATA": {"main": [[{"node": "CHART IMG JSON", "type": "main", "index": 0}]]}, "CHART IMG JSON": {"main": [[{"node": "JSON PURIFY", "type": "main", "index": 0}]]}, "JSON PURIFY": {"main": [[{"node": "STOCK ANALYZER", "type": "main", "index": 0}]]}, "STOCK ANALYZER": {"main": [[{"node": "CHART IMAGE GENERATION", "type": "main", "index": 0}]]}, "CHART IMAGE GENERATION": {"main": [[{"node": "IMAGE", "type": "main", "index": 0}, {"node": "TEXT", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d5f8659b-78cc-4b6a-ba47-20045bfa30e2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5c2f5b5b4cf20114a6c53aaed9430bfdabad5c4604b5a73e1363b96c75e842ec"}, "id": "elwuztKzHBAB0pIy", "tags": []}