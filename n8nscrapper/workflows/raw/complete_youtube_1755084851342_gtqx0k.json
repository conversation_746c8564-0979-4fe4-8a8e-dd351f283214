{"id": "XSyVFC1tsGSxNwX9", "meta": {"instanceId": "60ad864624415060d2d0a0e71acd8b3b40e4ee2e9ef4b439d9937d3d33537a96"}, "name": "Complete Youtube", "tags": [], "nodes": [{"id": "fd74706b-609b-4723-b4a4-067e1b064194", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [300, 60], "parameters": {"options": {"systemMessage": "=You help youtube creators find trending videos based on a specific niche.\n\nVerify if the user informed a niche before doing anything. If not, then ask him for one by giving him suggestions for him to select from.\n\nAfter you know what type of content the user might produce, use the \"youtube_search\" tool up to 3 times with different search terms based on the user's content type and niche.\n\nThe tool will answer with a list of videos from the last 2 days that had the most amount of relevancy. It returns a list of json's covering each video's id, view count, like count, comment count, description, channel title, tags and channel id. Each video is separated by \"### NEXT VIDEO FOUND: ###\".\n\nYou should then proceed to understand the data received then provide the user with insightful data of what could be trending from the past 2 days. Provide the user links to the trending videos which should be in this structure:\n\nhttps://www.youtube.com/watch?v={video_id}\n\nto reach the channel's link you should use:\n\nhttps://www.youtube.com/channel/{channel_id}\n\nFind patterns in the tags, titles and especially in the related content for the videos found.\n\nYour mission isn't to find the trending videos. It's to provide the user with valuable information of what is trending in that niche in terms of content news. Remember to provide the user with the numbers of views, likes and comments while commenting about any video. So you should not talk about any particular video, focus rather in explaining the overall senario of all that was found.\n\nExample of response:\n\n\"It seems like what is trending in digital marketing right now is talking about mental triggers, since 3 of the most trending videos in the last 2 days were about...\""}}, "typeVersion": 1.6}, {"id": "ced4b937-b590-4727-b1f2-a5e88b96091a", "name": "chat_message_received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [80, 60], "webhookId": "ff9622a4-a6ec-4396-b9de-c95bd834c23c", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "35a91359-5007-407d-a750-d6642e595690", "name": "youtube_search", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "position": [540, 180], "parameters": {"name": "youtube_search", "workflowId": {"__rl": true, "mode": "list", "value": "N9DveO781xbNf8qs", "cachedResultName": "Youtube Search Workflow"}, "description": "Call this tool to search for trending videos based on a query.", "jsonSchemaExample": "{\n\t\"search_term\": \"some_value\"\n}", "specifyInputSchema": true}, "typeVersion": 1.2}, {"id": "42f41096-531d-4587-833a-6f659ef78dd0", "name": "openai_llm", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [260, 180], "parameters": {"options": {}}, "typeVersion": 1}, {"id": "e4bda5b9-abd4-4cd6-8c95-126a01aa6e21", "name": "window_buffer_memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [400, 180], "parameters": {}, "typeVersion": 1.2}, {"id": "f6d86c5a-393a-4bcf-bdaf-3b06c79fa51d", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "parameters": {"color": 7, "width": 693.2572054941234, "height": 354.53098948245656, "content": "Main Workflow"}, "typeVersion": 1}, {"id": "4ddbc3f0-e3d7-4ce4-a732-d731c05024d2", "name": "find_video_data1", "type": "n8n-nodes-base.httpRequest", "position": [700, 720], "parameters": {"url": "https://www.googleapis.com/youtube/v3/videos?", "options": {}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "={{ $env[\"GOOGLE_API_KEY\"] }}"}, {"name": "id", "value": "={{ $json.id.videoId }}"}, {"name": "part", "value": "contentDetails, snippet, statistics"}]}}, "typeVersion": 4.2}, {"id": "fdb28635-801d-4ce0-8919-11446c6a7a82", "name": "get_videos1", "type": "n8n-nodes-base.youTube", "position": [280, 560], "parameters": {"limit": 3, "filters": {"q": "={{ $json.query.search_term }}", "regionCode": "US", "publishedAfter": "={{ new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() }}"}, "options": {"order": "relevance", "safeSearch": "moderate"}, "resource": "video"}, "credentials": {"youTubeOAuth2Api": {"id": "dCyrga3t1tlgQQy0", "name": "YouTube account"}}, "typeVersion": 1}, {"id": "60e9e61d-0e5e-4212-8b55-71299aeec4d5", "name": "response1", "type": "n8n-nodes-base.set", "position": [1100, 500], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "b9b9117b-ea14-482e-a13b-e68b8e6b441d", "name": "response", "type": "string", "value": "={{ $input.all() }}"}]}}, "typeVersion": 3.4}, {"id": "254a6740-8b25-4898-9795-4c3f0009471f", "name": "group_data1", "type": "n8n-nodes-base.set", "position": [1160, 700], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "47c172ad-90c8-4cf6-a9f5-50607e04cc90", "name": "id", "type": "string", "value": "={{ $json.items[0].id }}"}, {"id": "9e639efa-0714-4b06-9847-f7b4b2fbef59", "name": "viewCount", "type": "string", "value": "={{ $json.items[0].statistics.viewCount }}"}, {"id": "93328f00-91b8-425b-ad0f-a330b2f95242", "name": "likeCount", "type": "string", "value": "={{ $json.items[0].statistics.likeCount }}"}, {"id": "015b0fb2-2a98-464c-a21b-51100616f26a", "name": "commentCount", "type": "string", "value": "={{ $json.items[0].statistics.commentCount }}"}, {"id": "cf1e1ec3-a138-42b8-8747-d249afa58dd3", "name": "description", "type": "string", "value": "={{ $json.items[0].snippet.description }}"}, {"id": "c5c9a3a2-b820-4932-a38a-e21102992215", "name": "title", "type": "string", "value": "={{ $json.items[0].snippet.title }}"}, {"id": "38216ead-1f8d-4f93-b6ad-5ef709a1ad2a", "name": "channelTitle", "type": "string", "value": "={{ $json.items[0].snippet.channelTitle }}"}, {"id": "ff34194d-3d46-43a8-9127-84708987f536", "name": "tags", "type": "string", "value": "={{ $json.items[0].snippet.tags.join(', ') }}"}, {"id": "e50b0f7b-3e37-4557-8863-d68d4fa505c8", "name": "channelId", "type": "string", "value": "={{ $json.items[0].snippet.channelId }}"}]}}, "typeVersion": 3.4}, {"id": "124c19a9-cbbd-4010-be37-50523c05f64b", "name": "save_data_to_memory1", "type": "n8n-nodes-base.code", "position": [1360, 700], "parameters": {"mode": "runOnceForEachItem", "jsCode": "const workflowStaticData = $getWorkflowStaticData('global');\n\nif (typeof workflowStaticData.lastExecution !== 'object') {\n workflowStaticData.lastExecution = {\n response: \"\"\n };\n}\n\nfunction removeEmojis(text) {\n return text.replace(/[\\u{1F600}-\\u{1F64F}|\\u{1F300}-\\u{1F5FF}|\\u{1F680}-\\u{1F6FF}|\\u{2600}-\\u{26FF}|\\u{2700}-\\u{27BF}]/gu, '');\n}\n\nfunction cleanDescription(description) {\n return description\n .replace(/https?:\\/\\/\\S+/g, '')\n .replace(/www\\.\\S+/g, '')\n .replace(/ +/g, ' ')\n .trim();\n}\n\nconst currentItem = { ...$input.item };\n\nif (currentItem.description) {\n currentItem.description = cleanDescription(currentItem.description);\n}\n\nlet sanitizedItem = JSON.stringify(currentItem)\n .replace(/\\\\r/g, ' ')\n .replace(/https?:\\/\\/\\S+/g, '')\n .replace(/www\\.\\S+/g, '')\n .replace(/\\\\n/g, ' ')\n .replace(/\\n/g, ' ')\n .replace(/\\\\/g, '')\n .replace(/ +/g, ' ')\n .trim();\n\nif (workflowStaticData.lastExecution.response) {\n workflowStaticData.lastExecution.response += ' ### NEXT VIDEO FOUND: ### ';\n}\n\nworkflowStaticData.lastExecution.response += removeEmojis(sanitizedItem);\n\nreturn workflowStaticData.lastExecution;\n"}, "typeVersion": 2}, {"id": "67f92ec4-71c0-49df-a0ea-11d2e3cf0f94", "name": "retrieve_data_from_memory1", "type": "n8n-nodes-base.code", "position": [780, 500], "parameters": {"jsCode": "const workflowStaticData = $getWorkflowStaticData('global');\n\nconst lastExecution = workflowStaticData.lastExecution;\n\nreturn lastExecution;"}, "typeVersion": 2}, {"id": "685820ba-b089-4cdc-984d-52f134754b5c", "name": "loop_over_items1", "type": "n8n-nodes-base.splitInBatches", "position": [500, 560], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "3d4d5a4b-d06b-41db-bb78-a64a266d5308", "name": "if_longer_than_3_", "type": "n8n-nodes-base.if", "position": [880, 720], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "08ba3db9-6bcf-47f8-a74d-9e26f28cb08f", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "leftValue": "={{ \n (() => {\n const duration = $json.items[0].contentDetails.duration;\n\n // Helper function to convert ISO 8601 duration to seconds\n const iso8601ToSeconds = iso8601 => {\n const match = iso8601.match(/PT(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+)S)?/);\n const hours = parseInt(match[1] || 0, 10);\n const minutes = parseInt(match[2] || 0, 10);\n const seconds = parseInt(match[3] || 0, 10);\n return hours * 3600 + minutes * 60 + seconds;\n };\n\n // Convert duration to seconds\n const durationInSeconds = iso8601ToSeconds(duration);\n\n // Check if greater than 210 seconds (3 minutes 30 seconds)\n return durationInSeconds > 210;\n })() \n}}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "7c6b8b82-fd6c-4f44-bccf-88c5a76f0319", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 420], "parameters": {"color": 5, "width": 1607, "height": 520, "content": "This part should be abstracted to another workflow and called inside the \"youtube_search\" tool of the main AI Agent."}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "cea84238-2b82-4a32-85dd-0c71ad685d47", "connections": {"openai_llm": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "get_videos1": {"main": [[{"node": "loop_over_items1", "type": "main", "index": 0}]]}, "group_data1": {"main": [[{"node": "save_data_to_memory1", "type": "main", "index": 0}]]}, "youtube_search": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "find_video_data1": {"main": [[{"node": "if_longer_than_3_", "type": "main", "index": 0}]]}, "loop_over_items1": {"main": [[{"node": "retrieve_data_from_memory1", "type": "main", "index": 0}], [{"node": "find_video_data1", "type": "main", "index": 0}]]}, "if_longer_than_3_": {"main": [[{"node": "group_data1", "type": "main", "index": 0}], [{"node": "loop_over_items1", "type": "main", "index": 0}]]}, "save_data_to_memory1": {"main": [[{"node": "loop_over_items1", "type": "main", "index": 0}]]}, "window_buffer_memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "chat_message_received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "retrieve_data_from_memory1": {"main": [[{"node": "response1", "type": "main", "index": 0}]]}}}