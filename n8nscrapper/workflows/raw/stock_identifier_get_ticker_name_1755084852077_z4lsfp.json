{"nodes": [{"parameters": {}, "id": "d23ab01c-cb28-45d3-9cfd-7ae428aa0e5d", "name": "When Executed By Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [200, 300]}, {"parameters": {"url": "https://api.apify.com/v2/acts/apify~google-search-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"fullResponse": true, "headers": {"Authorization": "Bearer {{$credentials.apifyApi}}", "Content-Type": "application/json"}}, "sendBody": true, "bodyParameters": {"parameters": {"queries": "{{$json.companyName}} stock ticker symbol"}}}, "id": "1f2e3d4c-5b6a-7f8e-9d0c-1b2a3c4d5e6f", "name": "Google Search", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [450, 300], "credentials": {"httpHeaderAuth": {"id": "apify-credentials", "name": "Apify API"}}}, {"parameters": {"extractionValues": {"extractionValues": [{"key": "ticker", "css": "title", "returnArray": true}]}, "options": {}}, "id": "a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6", "name": "Information Extractor", "type": "n8n-nodes-base.extractHtml", "typeVersion": 1, "position": [700, 300]}, {"parameters": {"url": "https://query2.finance.yahoo.com/v7/finance/quote?symbols={{$json.results[0].split(\" \")[0]}}", "options": {"fullResponse": true}}, "id": "c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6", "name": "Ticker Name Search", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [950, 300]}, {"parameters": {"fields": {"values": [{"name": "ticker", "value": "={{$json.results[0].split(\" \")[0]}}"}, {"name": "companyName", "value": "={{$node[\"When Executed By Another Workflow\"].json.companyName}}"}]}, "options": {}}, "id": "e1f2g3h4-i5j6-k7l8-m9n0-o1p2q3r4s5t6", "name": "Set Ticker", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1200, 300]}, {"parameters": {"content": "{\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a helpful stock market assistant. You help identify the correct stock ticker symbol for companies. Look at the search results and extract the most likely ticker symbol. Only respond with the ticker symbol, nothing else.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Search results: {{$node[\"Google Search\"].json.body}}\"\n    }\n  ]\n}", "options": {}}, "id": "g1h2i3j4-k5l6-m7n8-o9p0-q1r2s3t4u5v6", "name": "OpenRouter Chat Model1", "type": "n8n-nodes-base.openRouter", "typeVersion": 1, "position": [450, 460], "credentials": {"openRouterApi": {"id": "openrouter-credentials", "name": "OpenRouter API"}}}], "connections": {"When Executed By Another Workflow": {"main": [[{"node": "Google Search", "type": "main", "index": 0}]]}, "Google Search": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}, {"node": "OpenRouter Chat Model1", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Ticker Name Search", "type": "main", "index": 0}]]}, "Ticker Name Search": {"main": [[{"node": "Set Ticker", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "pinData": {}, "versionId": "d64e7d3e-76e4-4c59-a1e4-10c8a16b3e78", "triggerCount": 0, "tags": [{"id": "1", "name": "Stock <PERSON>"}], "name": "Stock Identifier (Get Ticker Name)"}