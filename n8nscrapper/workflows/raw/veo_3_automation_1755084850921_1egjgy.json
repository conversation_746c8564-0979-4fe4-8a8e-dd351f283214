{"name": "Veo 3 automation", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-180, -60], "id": "fb4eb14f-e351-41cf-a0f9-1258be1232a1", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"method": "POST", "url": "https://queue.fal.run/fal-ai/veo3", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Key YOUR_API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"prompt\": \"Zoom in, then the camera follows a humanoid robot (half human, half robot) talking towards the camera in Egyptian Arabic saying: \\\"السلام ومرحبا بكم في قناة حسام. يلا نتعلم إزاي نعمل الفيديوهات دي بالذكاء الاصطناعي ونؤتمتها!\\\" The environment is ancient Egypt with pyramids and related monuments. Vertical video, realistic style.\"\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, -60], "id": "ac85fab5-a04a-46ce-81b9-5a22ae3b50b9", "name": "Veo 3 (Request creation)"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "53c1c33d-b3db-4eaf-83de-f39e64b94b7a", "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [480, -60], "id": "cfba11d2-e2d3-4ddc-8c58-9cc6e355b95d", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [200, -60], "id": "2003c05b-4288-4979-a6c8-41bdc5260a8a", "name": "Wait", "webhookId": "1b02f13f-da40-44b3-bc4b-bb927e8c88cb"}, {"parameters": {"url": "={{ $json.response_url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Key YOUR_API_KEY"}]}, "options": {"redirect": {"redirect": {}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, -80], "id": "6e4478f6-f936-4780-961c-301001e21300", "name": "Get result as URL"}, {"parameters": {"url": "={{ $json.video.url }}", "options": {"redirect": {"redirect": {}}, "response": {"response": {"responseFormat": "file", "outputPropertyName": "veo3_output.mp4"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [920, -80], "id": "4aef3707-35be-426e-ab2a-38bf8338f772", "name": "Veo 3 video Download"}, {"parameters": {"url": "={{ $('Veo 3 (Request creation)').item.json.status_url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Key YOUR_API_KEY"}]}, "sendBody": true, "bodyParameters": {"parameters": [{}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [340, -60], "id": "0ca467f9-aaca-471f-88b9-860553641549", "name": "Check Status"}, {"parameters": {"content": "## توليد فيديو عن طريق \"Google veo 3\"\n[API > Fal AI]\nhttps://fal.ai/models/fal-ai/veo3/api#api-call", "height": 320, "width": 440}, "type": "n8n-nodes-base.stickyNote", "position": [-260, -180], "typeVersion": 1, "id": "5511eb2b-0ba6-4ce1-93ca-fda943aaf200", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## التحقق من النتيجة (هل تم التوليد؟)\nاذا لا بينتظر شوية ويشوف تاني", "height": 320, "width": 420, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [200, -180], "typeVersion": 1, "id": "e0b19d39-7512-4ef8-a75c-74488fe6bf32", "name": "Sticky Note1"}, {"parameters": {"content": "## استرجاع رابط الفيديو اللي تم توليده\nوكمان تحميله كملف!", "height": 320, "width": 480, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [640, -180], "typeVersion": 1, "id": "6131e42f-9005-48ed-b3b8-79a6241578d7", "name": "Sticky Note2"}, {"parameters": {"content": "## انشر الفيديو بتاعك او ارفعه لاي منصة زي ما تحب :)\n\nمع تحياتي، حسام الدين حسن\n@epreneurs\n[![شاهد الفيديو على يوتيوب](upload://agh6prr59KzeFz6u94kiI3Vsjmc.jpeg)](https://youtu.be/VIDEO_ID)\n[@epreneurs](https://www.youtube.com/@ePreneurs)\n", "height": 340, "width": 300, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [1140, -180], "typeVersion": 1, "id": "e5536115-b8be-4ab5-be43-a5aecb52502e", "name": "Sticky Note3"}], "pinData": {"Get result as URL": [{"json": {"video": {"url": "https://v3.fal.media/files/penguin/1hj0rCjqX3haFajlZMMbs_output.mp4", "content_type": "video/mp4", "file_name": "output.mp4", "file_size": 1879481}}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Veo 3 (Request creation)", "type": "main", "index": 0}]]}, "Veo 3 (Request creation)": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Get result as URL", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Get result as URL": {"main": [[{"node": "Veo 3 video Download", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "62f52882-0601-4d6e-9457-bf90e24c2396", "meta": {"instanceId": "1c6fe1f86c2c8dcb4e3a7cd79c80882fb06f5ccb9ad56fc10285b4ecd3888265"}, "id": "7cQpB4p8TLMY9zvB", "tags": []}