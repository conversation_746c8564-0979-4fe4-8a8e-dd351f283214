{"name": "sdr3", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [0, 0], "id": "d11edcb4-f7c7-4e85-a580-fb018f01c588", "name": "<PERSON>gger on AI-Enriched Lead", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "jzOKoQHQ1fzJUKun", "name": "Google Sheets Trigger account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "fcf14d71-124d-4631-acfd-9c0065f1f8e8", "name": "subject", "value": "={{ \"Quick note for \" + $json[\"First Name\"] }}", "type": "string"}, {"id": "3da2c5af-489d-4056-9f92-7356db17647b", "name": "body", "value": "=={{   \"Hey \" + $json[\"First Name\"] + \",\\n\\n\" +  $json[\"Outreach Line\"] + \"\\n\\n\" +  \"Would love to chat for 10 mins about how we can help \" +   $json[\"Company\"] + \" grow faster.\\n\\n\" +  \"Cheers,\\nYour Name\"}}| toEmail | ={{ $json[\"Email (Scraped)\"] }} |", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [200, 0], "id": "acc2ae09-8827-4a17-9102-b81b72f46f90", "name": "\tSet Email Subject & Body"}, {"parameters": {"sendTo": "={{ $json[\"toEmail\"] }}", "subject": "=\t={{ $json[\"subject\"] }}", "message": "={{ $json[\"body\"] }}", "options": {"senderName": "harsha"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [0, 200], "id": "2dd7aa97-cdf8-4bdb-9382-f68f9882f0e9", "name": "\tSend Cold Email via Gmail", "webhookId": "f9d61bd3-3e40-47eb-96c8-f85aaba2569a", "credentials": {"gmailOAuth2": {"id": "FeHvI24ngA7KxiO9", "name": "Gmail account"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo", "mode": "list", "cachedResultName": "contanct data base ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1O5gmOOVB8SpoBev-IWGySyiEHFOG8t66k7VlUdJT6wo/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "name ", "displayName": "name ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "contact details", "displayName": "contact details", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [200, 200], "id": "3fd1b099-1464-4fab-83bd-d3adec9c1bc2", "name": "\t<PERSON> as Contacted", "credentials": {"googleSheetsOAuth2Api": {"id": "5cGO5mtMhF2M4IKE", "name": "Google Sheets account"}}}, {"parameters": {"content": "## Cold Email Sender \n(Gmail + Personalization)", "height": 460, "width": 460, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, -80], "id": "e8813d73-0c1d-4a59-aa1f-1ddc2b1c45aa", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Trigger on AI-Enriched Lead": {"main": [[{"node": "\tSet Email Subject & Body", "type": "main", "index": 0}]]}, "\tSet Email Subject & Body": {"main": [[{"node": "\tSend Cold Email via Gmail", "type": "main", "index": 0}]]}, "\tSend Cold Email via Gmail": {"main": [[{"node": "\t<PERSON> as Contacted", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e2ca79e8-5e9f-41a5-b97b-0d39bc41f5cc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "57a3103abaee943256a1daab7aebcb0a5de91f02c7de16853df57583eefbff08"}, "id": "GZs5m2UIFXm1IgHi", "tags": []}