{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [1140, 780], "parameters": {}, "typeVersion": 1}, {"name": "Note", "type": "n8n-nodes-base.stickyNote", "position": [1480, 760], "parameters": {"width": 560, "height": 400, "content": "## 2. Advanced way: Using Expressions\nIn this `Set` node, we set dates using [Luxon expressions](https://docs.n8n.io/code-examples/expressions/luxon/) for the following formats:\n\nNow - `{{$now}}`\nCurrent time with seconds - `{{$now.toLocaleString(DateTime.TIME_WITH_SECONDS)}}`\nToday - `{{$today}}`\nTomorrow - `{{$today.plus({days: 1})}}`\nOne hour ago - `{{$now.minus({hours: 1})}}`\nWeekday name - `{{$today.weekdayLong}}`\n\n"}, "typeVersion": 1}, {"name": "Note3", "type": "n8n-nodes-base.stickyNote", "position": [660, 780], "parameters": {"width": 420, "height": 100, "content": "### Click the `Execute Workflow` button and double click on the nodes to see the input and output items."}, "typeVersion": 1}, {"name": "12 Hours from now", "type": "n8n-nodes-base.dateTime", "position": [1520, 580], "parameters": {"value": "={{$now}}", "action": "calculate", "options": {}, "duration": 12, "timeUnit": "hours"}, "typeVersion": 1}, {"name": "Note1", "type": "n8n-nodes-base.stickyNote", "position": [1480, 400], "parameters": {"width": 560, "height": 340, "content": "## 1. Simple Way: Using the Date & Time node\nThere are two actions available within the `Date & Time` node:\n1. Calculating a date - adding/substracting minutes,hours, days, etc.\n2. Formatting a date\n\n"}, "typeVersion": 1}, {"name": "Note4", "type": "n8n-nodes-base.stickyNote", "position": [1980, 860], "parameters": {"width": 480, "height": 320, "content": "### 2.1 Working with an existing time string\nAs items pass between nodes, n8n saves dates as ISO strings. This means that in order to work with the data as a date again, we need to convert it back using `DateTime.fromISO('yyyy-mm-dd')`\n. Once doing that, we are able to apply date and time function again such as : `{{DateTime.fromISO($json[\"Now\"]).toFormat('yyyy LLL dd')}}`"}, "typeVersion": 1}, {"name": "Set times", "type": "n8n-nodes-base.set", "position": [1520, 1020], "parameters": {"values": {"string": [{"name": "Now", "value": "={{$now}}"}, {"name": "Current time with seconds", "value": "={{$now.toLocaleString(DateTime.TIME_WITH_SECONDS)}}"}, {"name": "Today", "value": "={{$today}}"}, {"name": "Tomorrow", "value": "={{$today.plus({days: 1})}}"}, {"name": "One hour from now", "value": "={{$now.minus({hours: 1})}}"}, {"name": "Weekday", "value": "={{$today.weekdayLong}}"}]}, "options": {}}, "typeVersion": 1}, {"name": "Edit times", "type": "n8n-nodes-base.set", "position": [2080, 1020], "parameters": {"values": {"string": [{"name": "Current time", "value": "={{DateTime.fromISO($json[\"Now\"])}}"}, {"name": "Current time formatted", "value": "={{DateTime.fromISO($json[\"Now\"]).toFormat('yyyy LLL dd')}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Format - MMMM DD YY", "type": "n8n-nodes-base.dateTime", "position": [1760, 580], "parameters": {"value": "={{$now}}", "options": {}, "toFormat": "MMMM DD YYYY"}, "typeVersion": 1}], "connections": {"Set times": {"main": [[{"node": "Edit times", "type": "main", "index": 0}]]}, "12 Hours from now": {"main": [[{"node": "Format - MMMM DD YY", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set times", "type": "main", "index": 0}, {"node": "12 Hours from now", "type": "main", "index": 0}]]}}}