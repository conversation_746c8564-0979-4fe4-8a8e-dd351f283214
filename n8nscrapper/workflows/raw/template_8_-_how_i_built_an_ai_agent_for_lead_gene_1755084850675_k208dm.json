{"name": "Template 8 - How I Built an AI Agent for Lead Generation Using Google Maps (No-Code)", "nodes": [{"parameters": {"options": {}}, "id": "5a174661-f5ca-48bc-9bbe-73e43428508d", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [740, 840], "webhookId": "91a4a184-2473-4f4e-ac45-fe5fb1daecdb"}, {"parameters": {"options": {}}, "id": "08e0d203-a0ed-452c-b0e4-f96ad2a7578d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [900, 1080], "credentials": {"openAiApi": {"id": "Xk9gHmNp0qRsT8UV", "name": "OpenAi Account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "7"}, "id": "625f3570-b3f2-4385-815f-59c75ca21dfd", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.2, "position": [1080, 1160]}, {"parameters": {"toolDescription": "This bot help you to scrap all contact information for lead gen from google map. Input query \"q\" for search input, \"ll\" for latlng, \"page\" is for the current page.", "url": "https://google.serper.dev/maps", "sendQuery": true, "parametersQuery": {"values": [{"name": "q"}, {"name": "ll"}, {"name": "page"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "X-API-KEY", "valueProvider": "fieldValue"}]}}, "id": "7bebcd94-43ca-4fe0-a178-6518860e3426", "name": "Map Searcher", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1260, 1100]}, {"parameters": {}, "id": "186681b6-e893-4410-b1e4-eb3ed550bb3f", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [720, 1420]}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "abc123XyZ789pQrStUvWxYz0123456789abcdefghijk", "mode": "list", "cachedResultName": "Map Searcher", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/abc123XyZ789pQrStUvWxYz0123456789abc<PERSON><PERSON><PERSON><PERSON><PERSON>/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/160mpEi2DqWmzU53jOyXCOti3is1fP16vkR-GBgbVycg/edit#gid=0"}, "columns": {"mappingMode": "autoMapInputData", "value": {}, "matchingColumns": [], "schema": [{"id": "Address", "displayName": "Address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Website", "displayName": "Website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Rating", "displayName": "Rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "OpeningHours", "displayName": "OpeningHours", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Opening Hours", "displayName": "Opening Hours", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}]}, "options": {}}, "id": "7c866dc4-7f9e-4413-8159-e218f7404203", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1160, 1420], "credentials": {"googleSheetsOAuth2Api": {"id": "Zt5g7vPqMNrwIwzC", "name": "Stanley Google Sheets account"}}}, {"parameters": {"name": "store_to_google_sheet", "description": "This is a tool to parse the input and store to google sheet, columns including:\n\n{\n\"Name\": xxx,\n\"Address\": xxx,\n\"Phone\": xxx,\n\"Website\": xxx,\n\"Rating\": xxx,\n\"Opening\" Hours: xxx\n}", "workflowId": {"__rl": true, "mode": "id", "value": "bbnYWeeNWQxxqbF0"}}, "id": "bb279032-83a4-4edf-bd85-eeeaf7d55d42", "name": "Store to Google Sheet", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.2, "position": [1360, 1000]}, {"parameters": {"jsCode": "return { json: { response: \"ok\" } }"}, "id": "04141f5b-77bb-4f8e-843b-0f0c0fb8e48f", "name": "Return response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1380, 1420]}, {"parameters": {"jsCode": "return JSON.parse($input.all()[0].json.query);"}, "id": "94d6a776-14bb-4500-aaa0-f89917beaffd", "name": "Parse content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 1420]}, {"parameters": {"options": {"systemMessage": "You are a research agent helping user to find the contact information from Google Map \"Map Searcher\" or website. After searching, you should structure the output for human readable list.\n\nAfter searching, put your result to \"Google Sheets\", when you call this tool, you must be in this json format in a single call:\n\n[\n{\n  \"json\": {\n    \"Address\": xxx,\n    \"Phone\": xxx,\n    \"Website\": xxx,\n    \"Rating\": xxx,\n    \"Opening\" Hours: xxx,\n    \"Email\": xxx\n  }\n},\n{\n  \"json\": {\n    \"Address\": xxx,\n    \"Phone\": xxx,\n    \"Website\": xxx,\n    \"Rating\": xxx,\n    \"Opening\" Hours: xxx,\n    \"Email\": xxx\n  }\n},...\n]"}}, "id": "5b8c1b80-6b3f-46ee-9536-24a96ec325c9", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [960, 840]}, {"parameters": {"content": "## AI Google Map Search Agent\n", "height": 531.3518006672114, "width": 909.9268172509238}, "id": "41983891-67ad-4497-ade3-514f65f6fb58", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [660, 780]}, {"parameters": {"content": "## Store to Google Sheet Workflow\n", "height": 260.76593592595685, "width": 913.1673665292614, "color": 4}, "id": "a3bcc3b0-03b6-42ac-a274-3c0f317c546c", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [660, 1340]}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Map Searcher": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Parse content", "type": "main", "index": 0}]]}, "Store to Google Sheet": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Return response", "type": "main", "index": 0}]]}, "Parse content": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6de741be-a355-484c-b4fa-3321977b57e3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "4221cc7208f142ff91856b22776eec81b71c256154ec8030569832c22d0c66d9"}, "id": "hLJZ8IWi72MCDnSs", "tags": [{"createdAt": "2025-01-28T07:53:03.821Z", "updatedAt": "2025-01-28T07:53:03.821Z", "id": "UmrR2LNV3n5EWSeP", "name": "andynocode free"}]}