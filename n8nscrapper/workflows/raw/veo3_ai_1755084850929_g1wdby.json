{"name": "Veo3 AI", "nodes": [{"parameters": {"model": "gpt-4o", "options": {"frequencyPenalty": 0.2, "temperature": 0.7}}, "id": "0611ae21-e9a4-42c9-abb5-ce3ff20d47c3", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1000, 380], "typeVersion": 1, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"sessionKey": "=chat_with_{{ $('Listen for incoming events').first().json.message.chat.id }}", "contextWindowLength": 10}, "id": "a21d838a-04ae-41f0-af43-9eec482da333", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1100, 380], "typeVersion": 1}, {"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "196e9b38-a191-46f7-ace5-fa15572d639c", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-80, 240], "webhookId": "d63cdf55-1ec9-4688-8a40-023fc3239630", "typeVersion": 1, "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "845942c0-5e20-49ab-981c-1e8e19a735df", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [440, 360], "typeVersion": 1.2, "webhookId": "9a5c2041-43e7-4a12-a1ed-8ee05fdc2dcb", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}, "options": {}}, "id": "db27e3fe-5f30-4eec-b1da-de9b11c7dcfd", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [820, 220], "typeVersion": 3.4}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "1d94159d-2ea8-4023-b5c6-640a94952fa4", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [440, 60], "typeVersion": 1.2, "webhookId": "814a2a70-f9a1-4c51-bc5b-23c8e477b0b3", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"language": "", "temperature": 0.7}}, "id": "038d96c8-f492-4921-9854-8a9732c95f85", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [620, 360], "typeVersion": 1.5, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "c44c150e-6417-4213-9ede-5829b3890602", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [200, 60], "typeVersion": 1.2, "webhookId": "08fc01ff-6c77-4099-87f9-a362a5673524", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/", "id": "d68f5557-eaf3-430c-b3e0-4c0bbb147c45"}]}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "01fef3f9-2dbf-4c14-90a0-620ba451e3d7", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [200, 240], "typeVersion": 3.2}, {"parameters": {"promptType": "define", "text": "=Request: {{ $json.CombinedMessage }}\n\nGenerate a VEO 3 Marketing video fitting the request, use this template as guide:\nA [visual style] 6-second ad shows a [main subject] in a [background setting], clearly struggling with [pain point]. The camera [camera movement] as they discover a free [lead magnet type] titled “[lead magnet name]”. The lighting shifts to [lighting/mood], and the color palette features [color palette]. Subtle [audio cue] plays. On-screen text reads: “[hook or CTA]”. Include subtitles in [language] / Do not include subtitles.\n\n\n\n\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1020, 220], "id": "70dd5125-b34d-451a-aeb3-6cabee447579", "name": "AI Agent1"}, {"parameters": {"method": "POST", "url": "https://api.replicate.com/v1/models/google/veo-3/predictions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "=\n\n{\n\t\"input\": {\n      \"prompt\": {{ $json.output.toJsonString() }}\n\t}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 220], "id": "b40d83ef-626a-44dc-95f5-bf6a4f3eb8c4", "name": "HTTP Request", "credentials": {"httpBearerAuth": {"id": "TqeLW6MJrJpRngX4", "name": "Bear<PERSON> account 2"}, "httpHeaderAuth": {"id": "fQPv6vsDcl7pU5DU", "name": "Head<PERSON> Au<PERSON> account 2"}}}, {"parameters": {"content": "### Now send it to e-mail, telegram, etc"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1600, 180], "id": "ce6fd0c1-9073-4aaa-97ba-f87320ed879f", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "### Get the API-Key here: https://replicate.com/google/veo-3 note: its 6$ per Video", "height": 80, "width": 320}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1000, 80], "id": "12677f57-6b2c-41c1-8d97-0aba1edca567", "name": "Sticky Note1"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1fb62cec-e9ea-4126-a5a5-7661fee5fa04", "meta": {"instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "AYIi56OVWaCvGLZh", "tags": []}