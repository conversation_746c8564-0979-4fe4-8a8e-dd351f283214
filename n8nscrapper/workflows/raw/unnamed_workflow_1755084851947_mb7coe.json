{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [470, 320], "parameters": {"triggerTimes": {"item": [{"hour": 8}]}}, "typeVersion": 1}, {"name": "NoOp", "type": "n8n-nodes-base.noOp", "position": [1120, 420], "parameters": {}, "typeVersion": 1}, {"name": "Get To Dos", "type": "n8n-nodes-base.notion", "position": [670, 320], "parameters": {"blockId": "bafdscf", "resource": "block", "operation": "getAll", "returnAll": true}, "credentials": {"notionApi": ""}, "typeVersion": 1}, {"name": "If task assigned to <PERSON><PERSON><PERSON>?", "type": "n8n-nodes-base.if", "notes": "Check if the task is incomplete", "position": [870, 320], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"to_do\"][\"text\"][1][\"mention\"][\"user\"][\"name\"]}}", "value2": "NAME"}], "boolean": [{"value1": "={{$json[\"to_do\"][\"checked\"]}}"}]}}, "notesInFlow": true, "typeVersion": 1}, {"name": "Create a Direct Message", "type": "n8n-nodes-base.slack", "position": [1120, 220], "parameters": {"options": {"users": ["U01JXLAJ6SE"]}, "resource": "channel", "operation": "open"}, "credentials": {"slackApi": ""}, "executeOnce": false, "typeVersion": 1}, {"name": "Send a Direct Message", "type": "n8n-nodes-base.slack", "position": [1320, 220], "parameters": {"text": "# TO DO", "channel": "={{$json[\"id\"]}}", "attachments": [{"title": "=☑️ {{$node[\"If task assigned to <PERSON><PERSON><PERSON>?\"].json[\"to_do\"][\"text\"][0][\"text\"][\"content\"]}}"}], "otherOptions": {"mrkdwn": true}}, "credentials": {"slackApi": ""}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Get To Dos", "type": "main", "index": 0}]]}, "Get To Dos": {"main": [[{"node": "If task assigned to <PERSON><PERSON><PERSON>?", "type": "main", "index": 0}]]}, "Create a Direct Message": {"main": [[{"node": "Send a Direct Message", "type": "main", "index": 0}]]}, "If task assigned to Harshil?": {"main": [[{"node": "Create a Direct Message", "type": "main", "index": 0}], [{"node": "NoOp", "type": "main", "index": 0}]]}}}