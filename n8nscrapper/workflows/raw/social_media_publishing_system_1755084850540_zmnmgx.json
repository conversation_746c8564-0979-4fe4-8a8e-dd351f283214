{"name": "Social Media Publishing System", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-20, 920], "id": "61d37dcf-444f-483e-a4c1-0ba5a8633bfc", "name": "Schedule Trigger"}, {"parameters": {"jsonSchemaExample": "{\n  \"instagram\": {\n    \"description\": \"...\"\n  },\n  \"youtube\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  },\n  \"facebook\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [500, 1100], "id": "71999ea4-0e5d-4dcf-ad96-02bcb905fe4d", "name": "Structured Output Parser"}, {"parameters": {"url": "={{ $json['Final Video'] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [720, 920], "id": "e4e1ecce-a957-4ca7-a78a-0ae65eb9083c", "name": "Download Video"}, {"parameters": {"content": "## Youtube Agent\n", "height": 220, "width": 540, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [900, 600], "id": "3526f20f-506e-4b6e-81d4-adb72cb14a00", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Instagram Agent", "height": 240, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [900, 840], "id": "cf461b31-cd3f-422b-989a-f9b1f5a21b7b", "name": "Sticky Note1"}, {"parameters": {"content": "## Facebook Agent\n", "height": 220, "width": 540, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [900, 1100], "id": "a3d55590-218d-42e1-9ab6-7b480b64f099", "name": "Sticky Note3"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "17841459721592041", "edge": "media", "options": {"queryParameters": {"parameter": [{"name": "video_url", "value": "={{ $json['Final Video'] }}"}, {"name": "caption", "value": "={{ $('Publishing Agent').item.json.output.instagram.description }}"}, {"name": "media_type", "value": "REELS"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [960, 920], "id": "5f873cf9-81a9-4f0d-bdbf-06218eb51e46", "name": "Create Container", "credentials": {"facebookGraphApi": {"id": "vBYfs9LRR8t8BNVY", "name": "Lumivaux Instagram"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "17841459721592041", "edge": "media_publish", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1120, 920], "id": "cb203dda-c52f-4018-b0d3-d6621d8edbc9", "name": "Instagram POST", "credentials": {"facebookGraphApi": {"id": "vBYfs9LRR8t8BNVY", "name": "Lumivaux Instagram"}}}, {"parameters": {"promptType": "define", "text": "=You are an AI specialized in crafting high-impact, viral titles and descriptions specifically optimized for Instagram Reels, YouTube Shorts, and Facebook Reels. You will receive a JSON input containing two fields:\n- **Name of Story**: {{$json.Name}}\n- **Description**: {{$json.Description}}\n\nYour task is to generate platform-specific content tailored to the unique characteristics and audience engagement styles of each platform:\n\n### 1. Instagram Reels:\n- **Description**: Brief, visually descriptive, incorporating relevant hashtags (max 5 hashtags) and a strong call-to-action for liking and sharing.\n\n### 2. YouTube Shorts:\n- **Title (under 50 chars)**: Direct, suspenseful, or surprising—focused heavily on clickability.\n- **Description**: Concise and compelling, including relevant keywords, and encouraging viewers to subscribe, like, and comment.\n\n### 3. Facebook Reels:\n- **Title (under 50 chars)**: Emotionally engaging, relatable, designed to instantly resonate.\n- **Description**: Engaging short description with an emotional hook, designed to encourage reactions, comments, and shares.\n\n---\n\nOutput your response strictly in valid JSON format as follows:\n\n```json\n{\n  \"instagram\": {\n    \"description\": \"...\"\n  },\n  \"youtube\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  },\n  \"facebook\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  }\n}\n```\n", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [280, 920], "id": "57726341-96dc-4fe2-841c-4b2d7278b8ed", "name": "Publishing Agent"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c087ebbf-22a5-45f4-82d5-ddabfd921889", "leftValue": "={{ $json.fields.Youtube }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "301baec8-c772-4042-bdc1-7d2d99f07043", "leftValue": "={{ $json.fields.Instagram }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "c7469347-06e8-4163-a280-d85ed47d167b", "leftValue": "={{ $json.fields.Facebook }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1520, 920], "id": "34c5fbad-e5f8-4c0c-a867-089b16470657", "name": "If"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appGl5TG2tqZKXdbj", "mode": "list", "cachedResultName": "Video Log", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj"}, "table": {"__rl": true, "value": "tblHWXdcFBZXsXMnR", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj/tblHWXdcFBZXsXMnR"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Get Video').item.json.id }}", "Youtube": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Final Video", "displayName": "Final Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "LinkedIn", "displayName": "LinkedIn", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1240, 660], "id": "6ee37572-0d16-4037-885b-d16759f01fd0", "name": "Youtube Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appGl5TG2tqZKXdbj", "mode": "list", "cachedResultName": "Video Log", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj"}, "table": {"__rl": true, "value": "tblHWXdcFBZXsXMnR", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj/tblHWXdcFBZXsXMnR"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Get Video').item.json.id }}", "Instagram": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Final Video", "displayName": "Final Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1280, 920], "id": "c293568e-86a8-44d4-a734-9e7b730b53f9", "name": "Instagram Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appGl5TG2tqZKXdbj", "mode": "list", "cachedResultName": "Video Log", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj"}, "table": {"__rl": true, "value": "tblHWXdcFBZXsXMnR", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj/tblHWXdcFBZXsXMnR"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Get Video').item.json.id }}", "Facebook": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Final Video", "displayName": "Final Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "LinkedIn", "displayName": "LinkedIn", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1140, 1180], "id": "87a957ca-c18e-4f71-a633-6f093486b3c2", "name": "Facebook Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appGl5TG2tqZKXdbj", "mode": "list", "cachedResultName": "Video Log", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj"}, "table": {"__rl": true, "value": "tblHWXdcFBZXsXMnR", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj/tblHWXdcFBZXsXMnR"}, "id": "={{ $('Search Record').item.json['Record ID'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [580, 920], "id": "47500bbd-7070-40a1-a52d-f56288020125", "name": "Get Video", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"model": "deepseek/deepseek-r1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [360, 1100], "id": "0b6980ff-91f4-4f03-8689-2fe925f004c8", "name": "Deepseek R1", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appGl5TG2tqZKXdbj", "mode": "list", "cachedResultName": "Video Log", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj"}, "table": {"__rl": true, "value": "tblHWXdcFBZXsXMnR", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj/tblHWXdcFBZXsXMnR"}, "filterByFormula": "{Status} = \"Ready To Upload\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [120, 920], "id": "22ac2299-4d9a-4484-a418-307b9840025e", "name": "Search Record", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('Publishing Agent').item.json.output.youtube.title }}", "regionCode": "SG", "categoryId": "1", "options": {"description": "={{ $('Publishing Agent').item.json.output.youtube.description }}"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1000, 660], "id": "66d929c1-da79-4734-9083-bfbc4b839311", "name": "Youtube Post", "credentials": {"youTubeOAuth2Api": {"id": "INhltBeDLkn5aZ0Z", "name": "YouTube account"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "me", "edge": "videos", "sendBinaryData": true, "binaryPropertyName": "data", "options": {"queryParameters": {"parameter": [{"name": "title", "value": "={{ $('Publishing Agent').item.json.output.facebook.title }}"}, {"name": "description", "value": "={{ $('Publishing Agent').item.json.output.facebook.description }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [960, 1180], "id": "fbdae4c0-b51b-4957-8a45-930c8353def3", "name": "Facebook Post", "credentials": {"facebookGraphApi": {"id": "wpgrNuem9OxqPYAo", "name": "Lumivaux Facebook "}}}, {"parameters": {"content": "## 🚀 Ultimate Reels Publishing Agent  \n**Get Millions of Views Across All Platforms — And Turn Them Into Income!**\n\n- 🎯 One-click scheduling across **Instagram Reels**, **YouTube Shorts**, and **Facebook Reels**  \n- 💬 Auto-generate **viral captions**, **engaging descriptions**, and **platform-optimized hashtags**  \n- 💸 No need for costly APIs — **100% budget-friendly**  \n- 🧠 Smart content engine tailored for **maximum reach & engagement**\n\n**Grow faster. Post smarter. Earn more.**\n\n(dainami ai workflows)", "height": 840, "width": 2260, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-160, 540], "id": "0ba2ab23-18a5-459b-8568-af25512c4f80", "name": "Sticky Note2"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appGl5TG2tqZKXdbj", "mode": "list", "cachedResultName": "Video Log", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj"}, "table": {"__rl": true, "value": "tblHWXdcFBZXsXMnR", "mode": "list", "cachedResultName": "Videos", "cachedResultUrl": "https://airtable.com/appGl5TG2tqZKXdbj/tblHWXdcFBZXsXMnR"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.id }}", "Status": "Completed"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Description", "displayName": "Description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Final Video", "displayName": "Final Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1740, 900], "id": "d9e37847-d593-482b-897f-805f1aa5b6eb", "name": "Video Published", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Search Record", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Publishing Agent", "type": "ai_outputParser", "index": 0}]]}, "Download Video": {"main": [[{"node": "Create Container", "type": "main", "index": 0}, {"node": "Facebook Post", "type": "main", "index": 0}, {"node": "Youtube Post", "type": "main", "index": 0}]]}, "Create Container": {"main": [[{"node": "Instagram POST", "type": "main", "index": 0}]]}, "Publishing Agent": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Instagram POST": {"main": [[{"node": "Instagram Posted", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Video Published", "type": "main", "index": 0}]]}, "Youtube Posted": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Instagram Posted": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Facebook Posted": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Get Video": {"main": [[{"node": "Download Video", "type": "main", "index": 0}]]}, "Deepseek R1": {"ai_languageModel": [[{"node": "Publishing Agent", "type": "ai_languageModel", "index": 0}]]}, "Search Record": {"main": [[{"node": "Publishing Agent", "type": "main", "index": 0}]]}, "Youtube Post": {"main": [[{"node": "Youtube Posted", "type": "main", "index": 0}]]}, "Facebook Post": {"main": [[{"node": "Facebook Posted", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5bd68318-6b4f-4a20-9cfa-72733ca13061", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d8c459f4ebcadd20c3258ade09582fe0f02de933363f6184259e0c9a36784090"}, "id": "FhIr9ViSZKwPI5Ak", "tags": [{"createdAt": "2025-03-23T08:55:33.454Z", "updatedAt": "2025-03-23T08:55:33.454Z", "id": "qZmoTsdmLBen8SFY", "name": "W3: Content Publishing"}]}