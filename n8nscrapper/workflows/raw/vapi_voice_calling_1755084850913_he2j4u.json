{"name": "Vapi Voice Calling", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1AgfnVti96l295F_XwvUqzV6RdFZKvhFTt0Jp5aVUiqE", "mode": "list", "cachedResultName": "CRM", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1AgfnVti96l295F_XwvUqzV6RdFZKvhFTt0Jp5aVUiqE/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1AgfnVti96l295F_XwvUqzV6RdFZKvhFTt0Jp5aVUiqE/edit#gid=0"}, "event": "rowUpdate", "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [0, 0], "id": "18767caa-cf92-43de-9a70-22bdb800a732", "name": "Google Sheets Trigger", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "KQAsQcq3BUFDfzbk", "name": "Google Sheets Trigger account"}}}, {"parameters": {"method": "POST", "url": "https://api.vapi.ai/call", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer "}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"phoneNumberId\": \"\",\n    \"assistantId\": \"\",\n    \"customer\": {\n        \"number\": \"+{{ $json.area_code }}{{ $json.phone_number }}\"\n    }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [300, 0], "id": "13e7fe1e-0c04-4e53-81b9-a6f7d7b470bd", "name": "HTTP Request"}], "pinData": {}, "connections": {"Google Sheets Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6bd51ccd-1c82-4299-9362-4f4a7275da38", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a0c1ca2e851d4b6570d763cf034a55daef24ff4c4e596ab36081981851aa2764"}, "id": "2LEO2KvM11tFpwsE", "tags": []}