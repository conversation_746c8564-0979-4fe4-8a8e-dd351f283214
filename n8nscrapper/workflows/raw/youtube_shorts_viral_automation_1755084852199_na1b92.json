{"name": "YouTube Shorts Viral Automation", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "6fad268b-de5a-4891-98f3-37035c795244", "name": "Check Every 6 Hours", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [-1100, 60]}, {"parameters": {"url": "https://www.googleapis.com/youtube/v3/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "part", "value": "snippet"}, {"name": "maxResults", "value": "20"}, {"name": "order", "value": "viewCount"}, {"name": "q", "value": "facts #shorts"}, {"name": "type", "value": "video"}, {"name": "videoDuration", "value": "short"}, {"name": "key", "value": "YOUR_YOUTUBE_API_KEY"}]}, "options": {}}, "id": "9740ed2a-46c2-4977-9a78-b52ffb08d7e8", "name": "Get Viral Shorts", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [-880, 60]}, {"parameters": {"jsCode": "// Extract video data for analysis\nconst videos = $input.all()[0].json.items;\n\n// Get top 10 videos with clean data\nconst topVideos = videos.slice(0, 10).map(v => ({\n  title: v.snippet.title.replace(/#shorts/gi, '').trim(),\n  videoId: v.id.videoId,\n  channel: v.snippet.channelTitle,\n  thumbnail: v.snippet.thumbnails.high.url,\n  publishedAt: v.snippet.publishedAt\n}));\n\n// Create analysis prompt for DeepSeek\nconst titlesForAnalysis = topVideos.map((v, i) => `${i + 1}. ${v.title}`).join('\\n');\n\nconst analysisPrompt = `Analyze these viral YouTube Shorts titles and extract success patterns:\n\n${titlesForAnalysis}\n\nProvide:\n1. Common hooks used (first 3-5 words)\n2. Emotional triggers\n3. Number/stat usage\n4. Title length pattern\n5. Top 3 viral formulas\n\nFormat as JSON.`;\n\n// Call DeepSeek API\nconst deepseekResponse = await this.helpers.httpRequest({\n  method: 'POST',\n  url: 'https://api.deepseek.com/v1/chat/completions',\n  headers: {\n    'Authorization': 'Bearer YOUR_DEEPSEEK_API_KEY',\n    'Content-Type': 'application/json'\n  },\n  body: {\n    model: 'deepseek-chat',\n    messages: [{\n      role: 'system',\n      content: 'You are a viral content analyst. Always respond with valid JSON.'\n    }, {\n      role: 'user',\n      content: analysisPrompt\n    }],\n    temperature: 0.7,\n    max_tokens: 1000\n  }\n});\n\n// Parse the analysis\nlet patterns;\ntry {\n  patterns = JSON.parse(deepseekResponse.choices[0].message.content);\n} catch (e) {\n  patterns = { error: 'Failed to parse', raw: deepseekResponse.choices[0].message.content };\n}\n\nreturn {\n  timestamp: new Date().toISOString(),\n  videosAnalyzed: topVideos,\n  patterns: patterns,\n  nextAction: 'generate_content'\n};"}, "id": "612aaaca-98eb-4a67-b122-798a8c8ee3ce", "name": "Ana<PERSON><PERSON> Viral Pattern<PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-660, 60]}, {"parameters": {"jsCode": "// Fixed code for Generate Video Concepts node\n// Get the patterns from previous node correctly\nconst previousData = $input.all()[0].json;\nconst patterns = previousData.patterns;\nconst timestamp = previousData.timestamp;\n\n// Create content generation prompt\nconst generationPrompt = `Based on these viral YouTube Shorts patterns:\n${typeof patterns === 'object' ? JSON.stringify(patterns, null, 2) : patterns}\n\nGenerate 5 YouTube Shorts concepts about interesting facts. Each concept should be a JSON object with these exact fields:\n{\n  \"title\": \"Catchy title with emoji (under 60 chars)\",\n  \"hook\": \"First 3 seconds script that grabs attention\",\n  \"facts\": [\"Fact 1\", \"Fact 2\", \"Fact 3\"],\n  \"visualDescription\": \"What viewers will see\",\n  \"callToAction\": \"Subscribe for more facts!\"\n}\n\nReturn ONLY a JSON array with 5 concepts. No additional text.`;\n\ntry {\n  // Generate with DeepSeek\n  const contentResponse = await this.helpers.httpRequest({\n    method: 'POST',\n    url: 'https://api.deepseek.com/v1/chat/completions',\n    headers: {\n      'Authorization': 'Bearer YOUR_DEEPSEEK_API_KEY',\n      'Content-Type': 'application/json'\n    },\n    body: {\n      model: 'deepseek-chat',\n      messages: [{\n        role: 'system',\n        content: 'You are a viral content creator. Always respond with valid JSON only, no additional text.'\n      }, {\n        role: 'user',\n        content: generationPrompt\n      }],\n      temperature: 0.8,\n      max_tokens: 2000\n    }\n  });\n\n  // Parse concepts\n  let concepts;\n  const responseContent = contentResponse.choices[0].message.content;\n  \n  try {\n    // Try to parse the response\n    concepts = JSON.parse(responseContent);\n  } catch (parseError) {\n    // If parsing fails, try to extract JSON from the response\n    const jsonMatch = responseContent.match(/\\[[\\s\\S]*\\]/);\n    if (jsonMatch) {\n      concepts = JSON.parse(jsonMatch[0]);\n    } else {\n      // Fallback: create one concept manually\n      concepts = [{\n        title: \"🤯 Mind-Blowing Facts You Won't Believe! #shorts\",\n        hook: \"Did you know your brain uses 20% of your body's energy?\",\n        facts: [\n          \"Your brain processes images 60,000x faster than text\",\n          \"You have a new skeleton every 10 years\", \n          \"Your heart beats 100,000 times per day\"\n        ],\n        visualDescription: \"Animated brain with energy flowing through it\",\n        callToAction: \"Follow for more amazing facts!\"\n      }];\n    }\n  }\n\n  // Make sure concepts is an array\n  if (!Array.isArray(concepts)) {\n    concepts = [concepts];\n  }\n\n  // Return each concept as a separate item for n8n\n  return concepts.map(concept => ({\n    json: {\n      ...concept,\n      generatedAt: timestamp,\n      status: 'ready_for_video_generation'\n    }\n  }));\n\n} catch (error) {\n  // Return error as valid n8n output\n  return [{\n    json: {\n      error: error.message,\n      title: \"🔥 Incredible Science Facts #shorts\",\n      hook: \"This will blow your mind...\",\n      facts: [\"Fact 1\", \"Fact 2\", \"Fact 3\"],\n      visualDescription: \"Science visuals\",\n      callToAction: \"Subscribe for daily facts!\",\n      status: 'error_but_usable'\n    }\n  }];\n}"}, "id": "5490e5af-874d-488b-8b05-cc11e749d179", "name": "Generate Video Concepts", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-440, 60]}, {"parameters": {"jsCode": "// Take the best concept and create image prompt\nconst concepts = $input.all();\nconst bestConcept = concepts[0].json; // Take first concept\n\n// Create visual prompt for thumbnail/first frame\nconst imagePrompt = `Create a stunning YouTube thumbnail: ${bestConcept.visualDescription || bestConcept.title}. Style: Viral YouTube Shorts, bright colors, eye-catching, professional.`;\n\n// Generate image with OpenAI DALL-E\nconst imageResponse = await this.helpers.httpRequest({\n  method: 'POST',\n  url: 'https://api.openai.com/v1/images/generations',\n  headers: {\n    'Authorization': 'Bearer YOUR_OPENAI_API_KEY',\n    'Content-Type': 'application/json'\n  },\n  body: {\n    model: 'dall-e-3',\n    prompt: imagePrompt,\n    n: 1,\n    size: '1024x1024',\n    quality: 'standard'\n  }\n});\n\n// Create script for video\nconst script = `\n[0-3s] HOOK: ${bestConcept.hook}\n[3-10s] FACT 1: ${bestConcept.facts ? bestConcept.facts[0] : bestConcept.mainContent}\n[10-20s] FACT 2: ${bestConcept.facts ? bestConcept.facts[1] : 'Amazing follow-up fact'}\n[20-27s] FACT 3: ${bestConcept.facts ? bestConcept.facts[2] : 'Mind-blowing conclusion'}\n[27-30s] CTA: ${bestConcept.callToAction || 'Follow for more amazing facts!'}\n`;\n\n// Prepare for video generation\nreturn {\n  title: bestConcept.title,\n  script: script,\n  imageUrl: imageResponse.data[0].url,\n  imagePrompt: imagePrompt,\n  concept: bestConcept,\n  readyForVideo: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "8bbf8d04-7530-4180-8291-f717aac5d724", "name": "Create Thumbnail & Script", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-220, 60]}, {"parameters": {"content": "## 🎬 Video Generation Options\n\nAt this point you have 3 options:\n\n### Option 1: Manual Creation (Free)\nTake the generated thumbnail and script, create video in CapCut\n\n### Option 2: Image Slideshow (Cheap)\nUse the Code node below to create a slideshow\n\n### Option 3: AI Video (Replicate)\nConnect to Replicate API for full video generation", "height": 223.8859180035651, "width": 355.93751525878895}, "id": "c8c6bff7-397c-41c3-9f96-caa22834def4", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"jsCode": "// Save to array for batch processing\nconst videoData = $input.all()[0].json;\n\n// Create a simple tracking object\nconst output = {\n  videoId: Math.random().toString(36).substring(7),\n  title: videoData.title,\n  script: videoData.script,\n  thumbnail: videoData.imageUrl,\n  status: 'ready_for_creation',\n  concept: videoData.concept,\n  createdAt: videoData.timestamp,\n  \n  // Stats for tracking\n  estimatedViews: '10K-100K',\n  estimatedRevenue: '$0.50-$5.00',\n  \n  // Next steps\n  nextSteps: [\n    '1. Download thumbnail image',\n    '2. Create video with script',\n    '3. Add captions',\n    '4. Upload to YouTube',\n    '5. Track performance'\n  ]\n};\n\n// For now, just return the data\n// In production, save to Google Sheets or database\nreturn output;"}, "id": "517a6375-9001-4fed-80b4-03df087fced7", "name": "Prepare for Upload", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [0, 260]}, {"parameters": {"content": "## 📊 Current Workflow Summary\n\nThis workflow:\n1. ✅ Finds viral YouTube Shorts every 6 hours\n2. ✅ Analyzes patterns with DeepSeek\n3. ✅ Generates new video concepts\n4. ✅ Creates thumbnail with DALL-E\n5. ✅ Prepares script and tracking\n\n**Next Steps:**\n- Set up Google Sheets for tracking\n- Add video creation (manual or automated)\n- Add upload workflow\n- Track performance", "height": 260, "width": 400}, "id": "17275fc8-90ca-4e58-976a-393ff5813993", "name": "Workflow Summary", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1100, 280]}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "YOUR_GOOGLE_SHEET_ID", "mode": "list"}, "sheetName": {"__rl": true, "value": "Sheet1", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"videoId": "={{ $json.videoId }}", "title": "={{ $json.title }}", "script": "={{ $json.script }}", "thumbnail": "={{ $json.thumbnail }}", "status": "={{ $json.status }}", "createdAt": "={{ $json.createdAt }}", "concept.hook": "={{ $json.concept.hook }}", "concept.facts.join": "={{ $json.concept.facts.join(', ') }}", "estimatedViews": "={{ $json.estimatedViews }}", "estimatedRevenue": "={{ $json.estimatedRevenue }}"}, "matchingColumns": ["videoId"], "schema": [{"id": "videoId", "displayName": "videoId", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "script", "displayName": "script", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "thumbnail", "displayName": "thumbnail", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "createdAt", "displayName": "createdAt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "concept.hook", "displayName": "concept.hook", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "concept.facts.join", "displayName": "concept.facts.join", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "estimatedViews", "displayName": "estimatedViews", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "estimatedRevenue", "displayName": "estimatedRevenue", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [220, 260], "id": "d4becd84-b11f-4fcc-91a7-d47ae859de53", "name": "Google Sheets"}], "pinData": {}, "connections": {"Check Every 6 Hours": {"main": [[{"node": "Get Viral Shorts", "type": "main", "index": 0}]]}, "Get Viral Shorts": {"main": [[{"node": "Ana<PERSON><PERSON> Viral Pattern<PERSON>", "type": "main", "index": 0}]]}, "Analyze Viral Patterns": {"main": [[{"node": "Generate Video Concepts", "type": "main", "index": 0}]]}, "Generate Video Concepts": {"main": [[{"node": "Create Thumbnail & Script", "type": "main", "index": 0}]]}, "Create Thumbnail & Script": {"main": [[{"node": "Prepare for Upload", "type": "main", "index": 0}]]}, "Prepare for Upload": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "cleaned-for-public", "meta": {"templateCredsSetupCompleted": true, "instanceId": "youtube-automation-public"}, "id": "youtube-shorts-automation", "tags": []}