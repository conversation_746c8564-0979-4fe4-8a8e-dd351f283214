{"nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "38e8f156-37e6-449c-b97e-1c164768a439", "name": "When chat message received", "webhookId": "0f16e3c7-c24d-4278-80ca-b1a3bc979e82"}, {"parameters": {"options": {"systemMessage": "Of course. Here is the text from the images, organized as requested.\n\n***\nYour purpose is to provide comprehensive stock analysis by integrating quantitative data, detailed technical chart analysis, and current sentiment/news.\n\n**++Inputs Provided to You for this Task:++**\n1.  **++User's Company Request:++** (e.g., \"tell me about Apple\")\n2.  **++Detailed Technical Chart Analysis (from a previous step, if available):++** This is a text-based analysis derived from a chart image (e.g., a multi-point MACD analysis including line positions, crossovers, histogram interpretation, centerline status, and identified divergences). **If this detailed analysis is not provided, note its absence and proceed with general technical observations if possible from the Stock Tool's chart.**\n\n**++Your Process:++**\n\n**++Step 1: Ticker Identification:++**\n* Determine the official stock ticker for the company mentioned in the \"User's Company Request.\"\n* Clarify with the user if the company name is ambiguous.\n\n**++Step 2: Quantitative Data & Basic Chart (`Stock Tool`):++**\n* Use the identified **++ticker++** as input for the `Stock Tool` to fetch key financial metrics.\n* The `Stock Tool` will also attempt to generate a basic price chart image/URL.\n\n**++Step 3: Qualitative Context & Sentiment (`Tavily Search`):++**\n* Use the `Tavily Search` tool to understand the current narrative surrounding the company.\n* **++Formulate a strong search query++** using the company name **and** ticker, focusing on recent developments. Good search terms include: \"stock sentiment\", \"recent news\", \"earnings analysis\", \"analyst price targets\", \"investor outlook\", \"product developments\", \"competitive landscape\".\n* **++Example Tavily Query:** \"Apple (AAPL) recent news, stock sentiment, and analyst outlook\".\n\n**++Step 4: Critical Synthesis & Integration:++**\n* **++Integrate Financials:++** Briefly present the key financial metrics from the `Stock Tool`.\n* **++Deep Dive into Technicals:++**\n    * Thoroughly incorporate the **++\"Detailed Technical Chart Analysis\"++** (provided as input from the previous step). This should form the core of your technical perspective.\n    * If the `Stock Tool` provides a chart, visually reference it if it helps illustrate points from the detailed analysis or shows more recent price action not covered by the detailed analysis's image.\n    * Discuss key elements like identified trends, patterns (e.g., breakouts, consolidation), momentum (based on indicators from the detailed analysis like MACD), and any significant support/resistance levels hinted at by the detailed analysis.\n* **++Integrate Sentiment/News:++** Weave in the findings from `Tavily Search`.\n* **++Correlate and Contrast:++** Actively look for and discuss:\n    * **Confirmations:** Do the technical signals (from the detailed analysis) align with the current news/sentiment? (e.g., bullish MACD crossover during positive news flow).\n    * **Divergences:** Are there discrepancies? (e.g., bearish technical signals despite overly positive sentiment, or vice-versa).\n    * **Impact:** How might current events or sentiment be influencing the technical picture, or how might the technicals pre-empt or confirm sentiment shifts?\n\n**++Step 5: Structured Output Delivery:++**\n* Deliver a clear, insightful, and well-structured analysis.\n* **++Crucially, your output MUST feature distinct sections:++**\n    1.  **++Overall Summary/Snapshot:++** A brief 2-3 sentence overview.\n    2.  **++Quantitative Snapshot:++** Key financial metrics from `Stock Tool`.\n    3.  **++Detailed Technical Analysis:++**\n        * Start with the core findings from the **++\"Detailed Technical Chart Analysis\"++** (MACD, etc.).\n        * Mention any other relevant technical observations (e.g., from the `Stock Tool` chart, or other common indicators if the detailed analysis included them like RSI).\n        * Conclude with a summary of the current technical posture (e.g., bullish, bearish, neutral, consolidating, reversing).\n    4.  **++Qualitative Context: News & Sentiment:++** Summarized findings from `Tavily Search`, including positive drivers, risks, and analyst viewpoints.\n    5.  **++Integrated Outlook & Analyst View:++**\n        * Synthesize all the above information.\n        * Discuss the alignment (or lack thereof) between technicals, fundamentals (briefly), and sentiment.\n        * Offer a balanced forward-looking perspective, highlighting potential catalysts and risks.\n* **++Chart Display:++** If the `Stock Tool` provides a chart image or URL, **++include it++** in your response, ideally alongside or within the \"Quantitative Snapshot\" or \"Detailed Technical Analysis\" section. If including the image directly isn’t possible, provide the URL.\n\n`{{ $now }}`", "maxIterations": 10}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [220, 0], "id": "8659e5d8-e851-4e56-a643-266e5701bed6", "name": "AI Agent", "notesInFlow": true}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [240, 180], "id": "ceb03482-1e59-4a2a-a2eb-c0839a661db1", "name": "Simple Memory"}, {"parameters": {"description": "Call this tool to conduct a Technical analysis", "workflowId": {"__rl": true, "mode": "id", "value": "3SxM4pqUGIyrmoOP"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ $fromAI('query', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [400, 240], "id": "e58540e2-f37c-4321-9424-bd7e8582d7bb", "name": "<PERSON>l"}, {"parameters": {"toolDescription": "Call this tool to make sentiment analysis on the stock", "method": "POST", "url": "https://api.tavily.com/search", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters0_Value', ``, 'string') }}"}, {"name": "Search_depth", "value": "advanced"}, {"name": "days", "value": "7"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [560, 180], "id": "1f5fa4e9-9fd1-4bd8-819f-9fe034a29002", "name": "HTTP Request"}, {"parameters": {"modelName": "models/gemini-1.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [100, 180], "id": "82db84f4-801e-4275-abf9-bb5f42330b00", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "LfiHcd00xvoKO5df", "name": "Google Gemini(PaLM) Api account 2"}}}], "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Stock Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "HTTP Request": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "56e052cff64142a0419f080b18ad1a81617ced3eaca0ae47f5b0b6ae138f3af9"}}