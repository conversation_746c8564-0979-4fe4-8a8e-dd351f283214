{"nodes": [{"parameters": {"options": {}}, "id": "8034d082-f50e-4f6b-8a79-ce7f86012d72", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [1536, 352], "webhookId": "syncbricks-social-posting-ai-agent", "typeVersion": 2.2}, {"parameters": {"options": {}}, "id": "e38a74d5-a733-4f5c-a97b-2c761b46919b", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1792, 848], "typeVersion": 1.7}, {"parameters": {"options": {}}, "id": "ca0a8aef-0251-45ed-bc9b-05ee977febf9", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1600, 1104], "typeVersion": 1}, {"parameters": {}, "id": "31e88059-94ca-4d4e-a673-e0f8cd1ac96e", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1920, 1104], "typeVersion": 1.2}, {"parameters": {"options": {}}, "id": "ffda1c79-faa4-4cc8-8655-6f7fc14b7e2d", "name": "Split Form Input", "type": "n8n-nodes-base.set", "position": [1936, 368], "typeVersion": 3.2}, {"parameters": {"options": {}}, "id": "37e443e1-6925-44a1-a07e-9eb1120e55a9", "name": "Upload Image", "type": "n8n-nodes-base.form", "position": [928, 1488], "webhookId": "0dc811ef-b2e0-4213-a0c9-e9b5fb9fcbf1", "typeVersion": 1}, {"parameters": {"options": {}}, "id": "265068ce-22fc-4c41-9ed2-93eadc0ce25f", "name": "Split Data", "type": "n8n-nodes-base.set", "position": [2176, 368], "typeVersion": 3.2}, {"parameters": {"options": {}}, "id": "b8470879-413e-4901-bf39-23a19beaee5f", "name": "Data for AI", "type": "n8n-nodes-base.set", "position": [2496, 368], "typeVersion": 3.4}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{}]}, "options": {}}, "id": "eef02dcb-7c7f-4d6c-a1fd-039c8b30f10d", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "position": [2288, 1088], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "097e7724-4dc0-4cc9-b12c-2dbdae4e024f", "name": "Form", "type": "n8n-nodes-base.form", "position": [2432, 2288], "webhookId": "e84b9492-038c-4701-a367-c68237ddebf6", "typeVersion": 1}, {"parameters": {"options": {}}, "id": "7b1d64fc-02ad-4b8f-ad1e-be8894abd932", "name": "Nest Top Meta", "type": "n8n-nodes-base.set", "position": [1552, 1488], "typeVersion": 3.3}, {"parameters": {}, "id": "11ddcfe8-1978-4f5a-8c29-67e72920431a", "name": "Rename Image Binary Top Image", "type": "n8n-nodes-base.code", "position": [576, 2288], "typeVersion": 2}, {"parameters": {"options": {}}, "id": "19892590-5ddf-47ac-b544-7a72a68bd45e", "name": "Publish to Facebook", "type": "n8n-nodes-base.facebookGraphApi", "position": [1056, 2400], "typeVersion": 1}, {"parameters": {"additionalFields": {}}, "id": "b3b0068b-c75a-427c-bb2d-77b21bc6ff16", "name": "Publish to LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [1040, 2160], "typeVersion": 1}, {"parameters": {"content": ""}, "id": "ed5fe0c2-a07a-45f1-90cb-65f21d416a1a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1472, 240], "typeVersion": 1}, {"parameters": {"additionalFields": {}}, "id": "c4a2e59d-165a-49cc-8074-9868ee4e0d6c", "name": "X", "type": "n8n-nodes-base.twitter", "position": [1056, 1920], "typeVersion": 2}, {"parameters": {"content": ""}, "id": "66ba98a9-24c0-4128-9ad9-792a0d400aae", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1472, 672], "typeVersion": 1}, {"parameters": {"content": ""}, "id": "02fafdc7-0272-4f34-9b66-dfbe8e16e70d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [368, 1280], "typeVersion": 1}, {"parameters": {"content": ""}, "id": "21d945c3-446c-4bb9-8696-a70034801485", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [368, 1808], "typeVersion": 1}, {"parameters": {"content": ""}, "id": "c9efdc38-94c6-494e-a932-45b75312bb54", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2240, 1808], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "93632f4f-9182-4b76-bbe0-e6d5dd230452", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1408, 1920], "typeVersion": 3.4}, {"parameters": {"options": {}}, "id": "89f22cf8-9499-4b7f-872d-97099c5d1e50", "name": "Image for Instagram", "type": "n8n-nodes-base.httpRequest", "position": [1360, 2608], "typeVersion": 4.2}, {"parameters": {"options": {}}, "id": "c415eeb9-f916-4c08-afb1-baed3f3280cc", "name": "Publish to Instagram", "type": "n8n-nodes-base.facebookGraphApi", "position": [1680, 2608], "typeVersion": 1}, {"parameters": {}, "id": "1c8f6ba6-c28b-45a8-ba0d-3f2f21ccbfa0", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [2000, 2272], "typeVersion": 3}, {"parameters": {"options": {}}, "id": "d9cfdcd1-18db-4d0d-9b6a-66897600e496", "name": "Upload Image to imgbb.com", "type": "n8n-nodes-base.httpRequest", "position": [1056, 2608], "typeVersion": 4.2}, {"parameters": {"content": ""}, "id": "f7e5b1b1-aeaa-4851-be04-53e8255fa343", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [368, 240], "typeVersion": 1}], "connections": {"On form submission": {"main": [[{"node": "Split Form Input", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Split Form Input": {"main": [[{"node": "Split Data", "type": "main", "index": 0}]]}, "Upload Image": {"main": [[{"node": "Nest Top Meta", "type": "main", "index": 0}]]}, "Split Data": {"main": [[{"node": "Data for AI", "type": "main", "index": 0}]]}, "Data for AI": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Upload Image", "type": "main", "index": 0}]]}, "Nest Top Meta": {"main": [[{"node": "Rename Image Binary Top Image", "type": "main", "index": 0}]]}, "Rename Image Binary Top Image": {"main": [[{"node": "X", "type": "main", "index": 0}, {"node": "Publish to LinkedIn", "type": "main", "index": 0}, {"node": "Publish to Facebook", "type": "main", "index": 0}, {"node": "Upload Image to imgbb.com", "type": "main", "index": 0}]]}, "Publish to Facebook": {"main": [[{"node": "Merge1", "type": "main", "index": 2}]]}, "Publish to LinkedIn": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "X": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Image for Instagram": {"main": [[{"node": "Publish to Instagram", "type": "main", "index": 0}]]}, "Publish to Instagram": {"main": [[{"node": "Merge1", "type": "main", "index": 3}]]}, "Merge1": {"main": [[{"node": "Form", "type": "main", "index": 0}]]}, "Upload Image to imgbb.com": {"main": [[{"node": "Image for Instagram", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "003c0ba7d159a939f933f410e94e211942ac56e758c92fbd77e0167e22be261d"}}