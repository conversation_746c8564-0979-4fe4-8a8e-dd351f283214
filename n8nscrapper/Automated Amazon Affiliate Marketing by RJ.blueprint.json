{"name": "Integration Google Gemini AI", "flow": [{"id": 1, "module": "gemini-ai:createACompletionGeminiPro", "version": 1, "parameters": {"__IMTCONN__": 4109822}, "mapper": {"model": "gemini-2.0-flash", "contents": [{"role": "user", "parts": [{"type": "text", "text": "Give me a trending or niche prodauct idea that sells well on Amazon in [Category Name] category. Which has high commision rate. Only return the product name or keyword."}]}], "generationConfig": {"thinkingConfig": {}}}, "metadata": {"designer": {"x": 0, "y": 300}, "restore": {"parameters": {"__IMTCONN__": {"label": "My Gemini AI connection", "data": {"scoped": "true", "connection": "gemini-ai-q9zyjp"}}}, "expect": {"model": {"mode": "chose", "label": "Gemini 2.0 Flash"}, "contents": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "parts": {"mode": "chose", "items": [{"type": {"mode": "chose", "label": "Text"}}]}}]}, "safetySettings": {"mode": "chose"}, "generationConfig": {"nested": {"stopSequences": {"mode": "chose"}, "responseModalities": {"mode": "chose"}, "thinkingConfig": {"nested": {"include_thoughts": {"mode": "chose"}}}}}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:gemini-ai-q9zyjp", "label": "Connection", "required": true}], "expect": [{"name": "model", "type": "select", "label": "AI Model", "required": true}, {"name": "contents", "type": "array", "label": "Messages", "required": true, "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "User", "value": "user"}, {"label": "Model", "value": "model"}]}, {"name": "parts", "spec": [{"name": "type", "type": "select", "label": "Message Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "text", "type": "text", "label": "Text", "required": false}]}, {"label": "File", "value": "file", "nested": [{"name": "file_data", "spec": [{"name": "mime_type", "type": "text", "label": "Mime Type", "required": false}, {"help": "You get this from the 'Upload a File' module.", "name": "file_uri", "type": "text", "label": "File URI", "required": false}], "type": "collection", "label": "File Data", "required": false}]}]}], "type": "array", "label": "Parts"}]}, {"name": "safetySettings", "type": "array", "label": "Safety Settings", "spec": [{"name": "category", "type": "select", "label": "Category", "options": [{"label": "Harassment content", "value": "HARM_CATEGORY_HARASSMENT"}, {"label": "Hate speech and content", "value": "HARM_CATEGORY_HATE_SPEECH"}, {"label": "Sexually explicit content.", "value": "HARM_CATEGORY_SEXUALLY_EXPLICIT"}, {"label": "Dangerous content:", "value": "HARM_CATEGORY_DANGEROUS_CONTENT"}]}, {"name": "threshold", "type": "select", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"label": "Block low and above.", "value": "BLOCK_LOW_AND_ABOVE"}, {"label": "Block medium and above.", "value": "BLOCK_MEDIUM_AND_ABOVE"}, {"label": "Block only high.", "value": "BLOCK_ONLY_HIGH"}, {"label": "Block none.", "value": "BLOCK_NONE"}]}]}, {"name": "generationConfig", "type": "collection", "label": "Generation configurations", "spec": [{"name": "stopSequences", "type": "array", "label": "Stop Sequences", "spec": {"type": "text", "label": "Stop Sequence", "name": "value"}}, {"name": "responseModalities", "type": "select", "label": "Response Modalities", "multiple": true, "validate": {"enum": ["text", "image"]}}, {"name": "maxOutputTokens", "type": "number", "label": "<PERSON> Output Tokens"}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 1, "min": 0}}, {"name": "topP", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "topK", "type": "number", "label": "Top K"}, {"name": "thinkingConfig", "type": "collection", "label": "Thinking Config", "spec": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "number", "label": "Thinking Budget"}, {"name": "include_thoughts", "type": "boolean", "label": "Include Thoughts"}]}, {"name": "responseMimeType", "type": "text", "label": "Response Mime Type"}, {"name": "responseSchema", "type": "any", "label": "Response Schema"}]}, {"name": "tools", "type": "any", "label": "Tools"}, {"name": "tool_config", "type": "any", "label": "<PERSON>l Config"}]}}, {"id": 2, "module": "apify-amazon-scraper:extractUsingActor", "version": 1, "parameters": {"__IMTCONN__": 4109865}, "mapper": {"categoryUrls": ["https://www.amazon.com/s?k= {{1.result}}"], "maxItemsPerStartUrl": "1"}, "metadata": {"designer": {"x": 300, "y": 300}, "restore": {"parameters": {"__IMTCONN__": {"label": "My Apify API Token connection (<EMAIL>)", "data": {"scoped": "true", "connection": "apify"}}}, "expect": {"categoryUrls": {"mode": "chose", "items": [null]}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "categoryUrls", "type": "array", "label": "Amazon URLs", "required": true, "spec": {"type": "text", "label": "Amazon URL", "name": "value"}}, {"name": "maxItemsPerStartUrl", "type": "integer", "label": "Maximum results per URL", "validate": {"min": 1}}]}}, {"id": 3, "module": "gemini-ai:createACompletionGeminiPro", "version": 1, "parameters": {"__IMTCONN__": 4109822}, "mapper": {"model": "gemini-2.0-flash", "contents": [{"role": "user", "parts": [{"text": "I am creating a Pinterest pin for an Amazon product.\r\n\r\nHere is the product title:\r\n{{2.title}}\r\n\nPlease generate A scroll-stopping Pinterest title (max 100 characters)\r\n\r\nDo not include any explanations or extra text — just the one title.", "type": "text"}]}], "generationConfig": {}}, "metadata": {"designer": {"x": 600, "y": 300}, "restore": {"expect": {"model": {"mode": "chose", "label": "Gemini 2.0 Flash"}, "contents": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "parts": {"mode": "chose", "items": [{"type": {"mode": "chose", "label": "Text"}}]}}]}, "safetySettings": {"mode": "chose"}, "generationConfig": {"nested": {"stopSequences": {"mode": "chose"}, "responseModalities": {"mode": "chose"}}}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "gemini-ai-q9zyjp"}, "label": "My Gemini AI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:gemini-ai-q9zyjp", "label": "Connection", "required": true}], "expect": [{"name": "model", "type": "select", "label": "AI Model", "required": true}, {"name": "contents", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "User", "value": "user"}, {"label": "Model", "value": "model"}]}, {"name": "parts", "spec": [{"name": "type", "type": "select", "label": "Message Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "text", "type": "text", "label": "Text", "required": false}]}, {"label": "File", "value": "file", "nested": [{"name": "file_data", "spec": [{"name": "mime_type", "type": "text", "label": "Mime Type", "required": false}, {"help": "You get this from the 'Upload a File' module.", "name": "file_uri", "type": "text", "label": "File URI", "required": false}], "type": "collection", "label": "File Data", "required": false}]}]}], "type": "array", "label": "Parts"}], "type": "array", "label": "Messages", "required": true}, {"name": "safetySettings", "spec": [{"name": "category", "type": "select", "label": "Category", "options": [{"label": "Harassment content", "value": "HARM_CATEGORY_HARASSMENT"}, {"label": "Hate speech and content", "value": "HARM_CATEGORY_HATE_SPEECH"}, {"label": "Sexually explicit content.", "value": "HARM_CATEGORY_SEXUALLY_EXPLICIT"}, {"label": "Dangerous content:", "value": "HARM_CATEGORY_DANGEROUS_CONTENT"}]}, {"name": "threshold", "type": "select", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"label": "Block low and above.", "value": "BLOCK_LOW_AND_ABOVE"}, {"label": "Block medium and above.", "value": "BLOCK_MEDIUM_AND_ABOVE"}, {"label": "Block only high.", "value": "BLOCK_ONLY_HIGH"}, {"label": "Block none.", "value": "BLOCK_NONE"}]}], "type": "array", "label": "Safety Settings"}, {"name": "generationConfig", "spec": [{"name": "stopSequences", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences"}, {"name": "responseModalities", "type": "select", "label": "Response Modalities", "multiple": true, "validate": {"enum": ["text", "image"]}}, {"name": "maxOutputTokens", "type": "number", "label": "<PERSON> Output Tokens"}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 1, "min": 0}}, {"name": "topP", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "topK", "type": "number", "label": "Top K"}], "type": "collection", "label": "Generation configurations"}, {"name": "tools", "type": "any", "label": "Tools"}, {"name": "tool_config", "type": "any", "label": "<PERSON>l Config"}]}}, {"id": 4, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 900, "y": 300}}, "routes": [{"flow": [{"id": 5, "module": "pinterest:createPin", "version": 2, "parameters": {"__IMTCONN__": 4109950}, "mapper": {"media_source": {"source_type": "image_url", "url": "{{2.thumbnailImage}}"}, "board_id": "954059571001167615", "title": "{{3.result}}", "description": "{{2.title}}", "link": "https://www.amazon.com/dp/{{2.asin}}/?tag=YOUR_TRACKING_ID"}, "metadata": {"designer": {"x": 1200, "y": 0}, "restore": {"parameters": {"__IMTCONN__": {"label": "My pinterest connection (rakin_bd)", "data": {"scoped": "true", "connection": "pinterest2"}}}, "expect": {"media_source": {"nested": {"source_type": {"mode": "chose", "label": "Image URL"}, "is_standard": {"mode": "chose"}}}, "board_id": {"mode": "chose", "label": "amzon products"}, "parent_pin_id": {"mode": "chose"}, "ad_account_id": {"mode": "chose"}, "board_section_id": {"mode": "chose"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:pinterest2", "label": "Connection", "required": true}], "expect": [{"name": "media_source", "type": "collection", "label": "Media Source", "required": true, "spec": [{"name": "source_type", "type": "select", "label": "Source Type", "required": true, "validate": {"enum": ["video_id", "image_url", "multiple_image_urls", "image_base64", "multiple_image_base64"]}}, {"name": "url", "type": "url", "label": "Image URL", "required": true}, {"name": "is_standard", "type": "boolean", "label": "Is Standard"}]}, {"name": "board_id", "type": "select", "label": "Board", "required": true}, {"name": "title", "type": "text", "label": "Title"}, {"name": "description", "type": "text", "label": "Description", "validate": {"max": 800}}, {"name": "alt_text", "type": "text", "label": "Alternative Text"}, {"name": "dominant_color", "type": "color", "label": "Dominant Color"}, {"name": "link", "type": "url", "label": "Link"}, {"name": "parent_pin_id", "type": "select", "label": "<PERSON><PERSON>"}, {"name": "note", "type": "text", "label": "Note"}, {"name": "ad_account_id", "type": "select", "label": "Ad Account"}, {"name": "board_section_id", "type": "select", "label": "Board Section"}]}}]}, {"flow": [{"id": 7, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "{{2.thumbnailImage}}", "gzip": true, "method": "get", "headers": [], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "", "serializeUrl": false, "shareCookies": false, "parseResponse": false, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 1200, "y": 300}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "GET"}, "headers": {"mode": "chose"}, "bodyType": {"label": "Empty"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 6, "module": "facebook-pages:CreatePostWithPhotos", "version": 6, "parameters": {"__IMTCONN__": 4110015}, "mapper": {"page_id": "102259092781849", "photos": [{"type": "data", "fileName": "my_image.jpg", "data": "{{7.data}}"}], "message": "{{3.result}}\n💥 Get this product - https://www.amazon.com/dp/{{2.asin}}/?tag=YOUR_TRACKING_ID"}, "metadata": {"designer": {"x": 1500, "y": 300}, "restore": {"parameters": {"__IMTCONN__": {"label": "My Facebook connection (<PERSON><PERSON>)", "data": {"scoped": "true", "connection": "facebook"}}}, "expect": {"page_id": {"mode": "chose", "label": "<PERSON><PERSON>"}, "photos": {"mode": "chose", "items": [{"type": {"mode": "chose", "label": "upload a photo"}}]}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:facebook", "label": "Connection", "required": true}], "expect": [{"name": "page_id", "type": "select", "label": "Page", "required": true}, {"name": "photos", "type": "array", "label": "Photos", "validate": {"maxItems": 30, "minItems": 1}, "required": true, "spec": [{"name": "type", "type": "select", "label": "I want to", "options": [{"label": "upload a photo", "value": "data", "nested": [{"name": "fileName", "type": "filename", "label": "File name", "required": true, "semantic": "file:name"}, {"name": "data", "type": "buffer", "label": "Data", "required": true, "semantic": "file:data"}], "default": true}, {"label": "download photo from URL", "value": "url", "nested": [{"name": "url", "type": "url", "label": "URL", "required": true}]}], "required": true}, {"name": "caption", "type": "text", "label": "Caption"}]}, {"name": "message", "type": "text", "label": "Message"}, {"name": "date", "type": "date", "label": "Date"}]}}]}, {"flow": [{"id": 9, "module": "instagram-business:CreatePostPhoto", "version": 1, "parameters": {"__IMTCONN__": 4110015}, "mapper": {"accountId": "*****************", "image_url": "{{2.thumbnailImage}}", "caption": "{{3.result}}\n💥 Get this product - https://www.amazon.com/dp/{{2.asin}}/?tag=YOUR_TRACKING_ID"}, "metadata": {"designer": {"x": 1200, "y": 600}, "restore": {"parameters": {"__IMTCONN__": {"label": "My Facebook connection (<PERSON><PERSON>)", "data": {"scoped": "true", "connection": "facebook"}}}, "expect": {"accountId": {"mode": "chose", "label": "<PERSON><PERSON> (@upwardengine)"}, "user_tags": {"mode": "chose"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:facebook", "label": "Connection", "required": true}], "expect": [{"name": "accountId", "type": "select", "label": "Page", "required": true}, {"name": "image_url", "type": "url", "label": "Photo URL", "required": true}, {"name": "caption", "type": "text", "label": "Caption"}, {"name": "user_tags", "type": "array", "label": "User Tags", "spec": [{"name": "username", "type": "text", "label": "Username", "required": true, "validate": {"pattern": "^(?!@)"}}, {"name": "x", "type": "number", "label": "X position", "required": true, "validate": {"max": 1, "min": 0}}, {"name": "y", "type": "number", "label": "Y position", "required": true, "validate": {"max": 1, "min": 0}}]}, {"name": "location_id", "type": "text", "label": "Location ID"}]}}]}]}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}