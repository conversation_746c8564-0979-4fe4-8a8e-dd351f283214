{"active": false, "connections": {"Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Send a message", "type": "main", "index": 0}], [{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Split Out2": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Convert to File1", "type": "main", "index": 0}]]}, "Convert to File1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Send a message": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "createdAt": "2025-07-23T13:03:04.276Z", "id": "jwvQ9nHsdrRhN4Uz", "isArchived": true, "meta": {"templateCredsSetupCompleted": true}, "name": "My workflow3", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 2}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "f9001798-34bc-447e-aa80-f66670319f86", "name": "Schedule Trigger"}, {"parameters": {"url": "https://daniel.cws.cslr-wga.net.br/cliente/listar?filtroStatus%5B%5D=1&cslr=01d31b570383a4f9e425596402c030af", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [208, 0], "id": "defa45f4-afe1-4e6e-b87a-996c7a7a7f5e", "name": "HTTP Request"}, {"parameters": {"url": "=https://daniel.cws.cslr-wga.net.br/cliente/listarContratoCliente?cslr=2ed7415ec2b8807072d936cd9982bfaa&id_cliente={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [864, 176], "id": "ef9eaf70-65de-4ea4-894d-5fda3bfcd17a", "name": "HTTP Request1"}, {"parameters": {"fieldToSplitOut": "data", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [416, 0], "id": "71560b5c-fb19-4a89-bfcf-262b7ed48f49", "name": "Split Out"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [672, 48], "id": "6c661fec-dfe7-4a93-9117-8bb584d40001", "name": "Loop Over Items"}, {"parameters": {"fieldToSplitOut": "data", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1088, 176], "id": "468aa377-54ab-464a-a479-6992b50c999f", "name": "Split Out2"}, {"parameters": {"assignments": {"assignments": [{"id": "9ab02941-0948-481d-ae7f-332d5e502315", "name": "seq_contrato", "value": "={{ $json.seq_contrato }}", "type": "number"}, {"id": "71b5b450-cc4a-426b-8cd0-29f18abef243", "name": "tipo", "value": "={{ $json.tipo }}", "type": "string"}, {"id": "2c5450a7-5989-4f00-b19e-ac701273db06", "name": "codigo_contrato_dynamics", "value": "={{ $json.codigo_contrato_dynamics }}", "type": "string"}, {"id": "6ee24e3b-756b-4b98-9cfd-927735a2f5d1", "name": "ds_nome_contrato", "value": "={{ $json.ds_nome_contrato }}", "type": "string"}, {"id": "b98f0711-0d12-497f-ae4c-cc930eecd7b0", "name": "dt_contrato", "value": "={{ $json.dt_contrato }}", "type": "string"}, {"id": "a94b8e89-ee21-499d-9155-dab377d7b676", "name": "dt_inicio_vigencia", "value": "={{ $json.dt_inicio_vigencia }}", "type": "string"}, {"id": "057c125b-7dcf-4e0b-929a-5a5890b49930", "name": "dt_dia_faturamento", "value": "={{ $json.dt_dia_faturamento }}", "type": "number"}, {"id": "71c65cec-7eca-40af-a175-0f7a86d69e6e", "name": "total_parcelas", "value": "={{ $json.total_parcelas }}", "type": "number"}, {"id": "baa21aae-1c37-4621-9a5b-3fa661ce6636", "name": "parcelas_pagas", "value": "={{ $json.parcelas_pagas }}", "type": "number"}, {"id": "2e5aeb9f-0cd5-41ea-b0b7-6aa6a3fed982", "name": "total_valor_equipes", "value": "={{ $json.total_valor_equipes }}", "type": "string"}, {"id": "b0e0ac9c-efd6-4004-8341-0c496b348047", "name": "total_contrato", "value": "={{ $json.total_contrato }}", "type": "string"}, {"id": "36bf110c-85b3-42c5-9403-fec405c5e4de", "name": "total_valor_produto", "value": "={{ $json.total_valor_produto }}", "type": "string"}, {"id": "7a212bde-53ea-4a7a-ab28-5f72a4fa33fd", "name": "status_code", "value": "={{ $json.status_code }}", "type": "number"}, {"id": "d5d35712-a809-4360-beee-8c04337a8172", "name": "seq_cliente", "value": "={{ $json.seq_cliente }}", "type": "number"}, {"id": "4181935d-f83a-4d6e-b579-d8b577e75cc2", "name": "ds_nome_fantasia", "value": "={{ $json.ds_nome_fantasia }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1280, 176], "id": "380ae494-11d3-4e69-86ab-8d926546e679", "name": "<PERSON>"}, {"parameters": {"binaryPropertyName": "={{ $json.ds_nome_fantasia }}", "options": {"delimiter": ";", "fileName": "={{ $json.ds_nome_fantasia }}.csv", "headerRow": true}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1504, 176], "id": "d3a4fa38-61fc-4089-b4df-846da86da600", "name": "Convert to File1"}, {"parameters": {"subject": "Relatório de Contratos", "message": "Olá! Em anexo, os contratos por cliente.", "options": {"attachmentsUi": {"attachmentsBinary": [{}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [880, -48], "id": "eae05d3b-bcdb-497e-8444-293e9d2d8e21", "name": "Send a message", "webhookId": "11fb2f35-7595-4e8a-8b09-e64ed8b12c9f", "credentials": {"gmailOAuth2": {"id": "dFwmTHYHZLzjshSr", "name": "Gmail account"}}}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1088, -48], "id": "ba803eb1-8fc0-4fe4-94b1-41774da43aa8", "name": "<PERSON><PERSON>"}], "pinData": {}, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "staticData": {"node:Schedule Trigger": {"recurrenceRules": []}}, "tags": [], "triggerCount": 1, "updatedAt": "2025-07-24T14:44:31.000Z", "versionId": "d372fb65-c123-4681-bb0a-1cab694b2596"}