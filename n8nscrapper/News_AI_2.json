{"name": "News AI 2", "nodes": [{"parameters": {"promptType": "define", "text": "=Topic of post: {{ $json.topic }}\n\nLanguage of post: {{ $json.language }}\n\nTarget audience of post: {{ $json.audience }}\n\nMake sure its recent and in 2025", "options": {"systemMessage": "=# Overview\nYou are an AI agent specialized in creating professional, educational, and engaging {{ $json.goalPlatform }} post based on any topic provided by the user.\n\n## Objectives:\nAlways begin by conducting a real-time search using the <PERSON>ly tool to gather the most accurate, up-to-date information on the topic. The post should be written to appeal to the provided target audience.\n\nBased on your research, generate a well-structured {{ $json.goalPlatform }} post that:\n- Starts with an engaging hook\n- {{ $json.goalPlatform }} specific in tone\n- Tone is {{ $json.tone }}\n- Clear and easy to read\n\n## Output Instructions:\n- Your ONLY output should be the final {{ $json.goalPlatform }} post text.\n- Do not include explanations, notes, or anything beyond the post itself.\n\n## Example Workflow:\n- Receive a topic (e.g., “The ROI of warehouse automation”)\n- Use Tavily to search and gather recent information or case studies\n- Draft a {{ $json.goalPlatform }} post using that research\n- Format it with source citations, clean structure, optional hashtags, and a call to action"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-100, -80], "id": "44c5d424-2a14-468e-b6bc-e6a269e90976", "name": "AI Agent"}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [60, 140], "id": "365fb35b-57c5-45f8-8872-a944a43e24f3", "name": "4.1", "credentials": {"openRouterApi": {"id": "7gFuLBYAdUiSiCO5", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('On form submission').item.json.goalPlatform }} Post:\n\n{{ $json.output }}", "options": {"systemMessage": "=# Overview\nYou are an AI agent that transforms {{ $('On form submission').item.json.goalPlatform }} posts into visual prompt descriptions for generating graphic marketing materials. These visuals are designed to be paired with the post on {{ $('On form submission').item.json.goalPlatform }}, helping communicate the message in a visually engaging, brand-aligned way.\n\n## Objective:\n- Read and analyze the given {{ $('On form submission').item.json.goalPlatform }} post.\n- Identify the main message, insight, or takeaway from the post.\n- Create a clear and compelling graphic prompt that can be used with a text-to-image generator.\n- The result should look {{ $('On form submission').item.json.visuals }} — that:\n1) Feels polished, modern, and engaging\n2) Created for {{ $('On form submission').item.json.goalPlatform }}\n\n## Output Instructions:\nOutput only the final image prompt. Do not output quotation marks.\n\nDo not repeat or rephrase the {{ $('On form submission').item.json.goalPlatform }} post.\n\nDo not add any explanations or extra content — just the image prompt.\n\nNever leave things blank like “Header area reserved for customizable callout text”\n\nOutput numeric stats when available in the original post\n\n## Style Guidelines:\nThink like a brand designer or marketing creative.\n\nYou can mention layout suggestions (e.g., “split screen design,” “header with bold title and subtle background illustration”).\n\nAssume the output will be generated using AI image tools — your prompt should guide those tools effectively.\n\n## Example Prompt Format:\nA modern flat-style graphic showing a human brain connected to mechanical gears, representing the fusion of AI and automation. Minimalist background, soft gradients, clean sans-serif text placement"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [260, -80], "id": "98fa4749-ad4e-4b7a-8edc-4184b58690df", "name": "AI Agent1"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $('AI Agent1').item.json.output }}"}, {"name": "size", "value": "1024x1024"}, {"name": "moderation", "value": "low"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [780, -80], "id": "0c9ecf62-2c1d-4db0-beff-a3129c70442a", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "VpbhAqVyynzzb44F", "name": "OpenAI"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1000, -80], "id": "db6ada7c-ab5d-45ef-a9d3-5f6c76b72e00", "name": "Convert to File"}, {"parameters": {"operation": "sendPhoto", "chatId": "={{ $('On form submission').item.json.chatId }}", "binaryData": true, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1200, -80], "id": "9b9b3ae3-88ea-4827-b5a7-4c33f46b3da7", "name": "Telegram", "webhookId": "c3970e70-5b31-4551-a3b7-1eac07254624", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "topic"}, {"name": "audience"}, {"name": "language"}, {"name": "goalPlatform"}, {"name": "tone"}, {"name": "visuals"}, {"name": "context"}, {"name": "chatId"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-340, -80], "id": "7c481b43-f51d-4294-a0f0-29901a92bfa5", "name": "On form submission"}, {"parameters": {"operation": "sendAndWait", "chatId": "={{ $('On form submission').item.json.chatId }}", "message": "=Here is the post we would generate for you, is that ok?\n\nVisuals:\n {{ $json.output }}\n\nPost text:\n{{ $('AI Agent').item.json.output }}", "options": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [580, -80], "id": "209f312d-90ee-4b47-a29a-47c3b2a66c0b", "name": "Telegram1", "webhookId": "751846a9-e5a2-4753-a2a8-82e65e97001f", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}], "pinData": {}, "connections": {"4.1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[]]}, "On form submission": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Telegram1": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c446299d-b10c-458b-a39e-ea61471e5e1a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "Nf5wyQ4pVUqXgA2U", "tags": []}