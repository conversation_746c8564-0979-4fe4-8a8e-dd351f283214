{"name": "UI Data Automation Workflow", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9 * * *"}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [80, 200]}, {"parameters": {"content": "## UI Data Collection & Automation Workflow\n\n日次または任意トリガーで以下の処理を実行:\n1. 社内従業員提出UI収集\n2. 公開プラットフォーム(Dribbble/Landbook)クローリング\n3. Supabase external_ui_data テーブル保存\n4. Replay-design-score-app /api/collect-data へ連携"}, "id": "sticky-note", "name": "Workflow Info", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [80, 80]}, {"parameters": {"options": {}}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [80, 320]}, {"parameters": {"jsCode": "// データソースの初期化\nconst dataSources = [\n  {\n    source: 'employee_submission',\n    enabled: true,\n    description: '社内従業員提出UI',\n    type: 'internal'\n  },\n  {\n    source: 'dribbble',\n    enabled: true,\n    description: 'Dribbble UI Collection',\n    type: 'web_scraping',\n    config: {\n      baseUrl: 'https://dribbble.com',\n      searchTerms: ['web design', 'ui design', 'mobile app'],\n      limit: 20\n    }\n  },\n  {\n    source: 'landbook',\n    enabled: true,\n    description: 'Landbook UI Collection',\n    type: 'web_scraping',\n    config: {\n      baseUrl: 'https://land-book.com',\n      categories: ['websites', 'portfolios', 'apps'],\n      limit: 20\n    }\n  }\n];\n\nreturn dataSources.map(source => ({ json: source }));"}, "id": "init-data-sources", "name": "Initialize Data Sources", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [280, 260]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "employee-condition", "leftValue": "{{ $json.source }}", "rightValue": "employee_submission", "operator": {"type": "string", "operation": "equals"}}, {"id": "dribbble-condition", "leftValue": "{{ $json.source }}", "rightValue": "dribbble", "operator": {"type": "string", "operation": "equals"}}, {"id": "landbook-condition", "leftValue": "{{ $json.source }}", "rightValue": "landbook", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "route-switch", "name": "Route Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [500, 260]}, {"parameters": {"resource": "channel", "operation": "history", "channelId": "={{ $env.SLACK_CHANNEL_ID }}", "additionalFields": {"count": 50, "inclusive": true}}, "id": "get-employee-submissions", "name": "Get Employee Submissions", "type": "n8n-nodes-base.slack", "typeVersion": 2.1, "position": [720, 140], "credentials": {"slackApi": {"id": "slack-credentials", "name": "Slack API Credentials"}}}, {"parameters": {"jsCode": "// 社内従業員提出UI処理\nconst messages = $input.all();\nconst uiSubmissions = [];\n\nfor (const message of messages) {\n  const msg = message.json;\n  \n  if (msg.files && msg.files.length > 0) {\n    for (const file of msg.files) {\n      if (file.mimetype && file.mimetype.startsWith('image/')) {\n        uiSubmissions.push({\n          id: `employee_${msg.ts}_${file.id}`,\n          title: file.title || file.name || 'Employee UI Submission',\n          description: msg.text || 'UI submitted by employee',\n          image_url: file.url_private,\n          source: 'employee_submission',\n          metadata: {\n            slack_ts: msg.ts,\n            slack_user: msg.user,\n            slack_channel: msg.channel,\n            file_id: file.id,\n            file_type: file.filetype,\n            file_size: file.size,\n            submission_type: 'internal'\n          },\n          created_at: new Date(parseFloat(msg.ts) * 1000).toISOString()\n        });\n      }\n    }\n  }\n}\n\nreturn uiSubmissions.map(item => ({ json: item }));"}, "id": "process-employee-submissions", "name": "Process Employee Submissions", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [940, 140]}, {"parameters": {"command": "const { chromium } = require('playwright');\n\nconst scrapeDribbble = async () => {\n  const browser = await chromium.launch({ headless: true });\n  const page = await browser.newPage();\n  \n  const results = [];\n  \n  try {\n    // Dribbble shots collection\n    await page.goto('https://dribbble.com/shots/popular/web-design');\n    await page.waitForSelector('.shot-thumbnail');\n    \n    const shots = await page.$$eval('.shot-thumbnail', elements => {\n      return elements.slice(0, 20).map(el => {\n        const img = el.querySelector('img');\n        const link = el.querySelector('a');\n        const title = el.querySelector('.shot-title');\n        \n        return {\n          title: title?.textContent?.trim() || 'Dribbble Design',\n          image_url: img?.src || img?.dataset?.src,\n          page_url: link?.href,\n          description: 'UI design from Dribbble'\n        };\n      });\n    });\n    \n    shots.forEach((shot, index) => {\n      if (shot.image_url) {\n        results.push({\n          id: `dribbble_${Date.now()}_${index}`,\n          title: shot.title,\n          description: shot.description,\n          image_url: shot.image_url,\n          source: 'dribbble',\n          metadata: {\n            page_url: shot.page_url,\n            platform: 'dribbble',\n            category: 'web-design',\n            scraping_timestamp: Date.now()\n          },\n          created_at: new Date().toISOString()\n        });\n      }\n    });\n    \n  } catch (error) {\n    console.error('Dribbble scraping error:', error);\n  } finally {\n    await browser.close();\n  }\n  \n  return results;\n};\n\nreturn await scrapeDribbble();"}, "id": "scrape-dribbble", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, 260]}, {"parameters": {"command": "const { chromium } = require('playwright');\n\nconst scrapeLandbook = async () => {\n  const browser = await chromium.launch({ headless: true });\n  const page = await browser.newPage();\n  \n  const results = [];\n  \n  try {\n    // Landbook websites collection\n    await page.goto('https://land-book.com/websites');\n    await page.waitForSelector('.website-card');\n    \n    const websites = await page.$$eval('.website-card', elements => {\n      return elements.slice(0, 20).map(el => {\n        const img = el.querySelector('img');\n        const link = el.querySelector('a');\n        const title = el.querySelector('.website-title');\n        \n        return {\n          title: title?.textContent?.trim() || 'Landbook Website',\n          image_url: img?.src || img?.dataset?.src,\n          page_url: link?.href,\n          description: 'Website design from Landbook'\n        };\n      });\n    });\n    \n    websites.forEach((website, index) => {\n      if (website.image_url) {\n        results.push({\n          id: `landbook_${Date.now()}_${index}`,\n          title: website.title,\n          description: website.description,\n          image_url: website.image_url,\n          source: 'landbook',\n          metadata: {\n            page_url: website.page_url,\n            platform: 'landbook',\n            category: 'websites',\n            scraping_timestamp: Date.now()\n          },\n          created_at: new Date().toISOString()\n        });\n      }\n    });\n    \n  } catch (error) {\n    console.error('Landbook scraping error:', error);\n  } finally {\n    await browser.close();\n  }\n  \n  return results;\n};\n\nreturn await scrapeLandbook();"}, "id": "scrape-landbook", "name": "Scrape Landbook", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [720, 380]}, {"parameters": {}, "id": "merge-all-data", "name": "Merge All Data", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [1160, 260]}, {"parameters": {"jsCode": "// データの前処理とバリデーション\nconst allData = $input.all();\nconst validatedData = [];\n\nfor (const item of allData) {\n  const data = item.json;\n  \n  // 必須フィールドのバリデーション\n  if (data.id && data.title && data.image_url && data.source) {\n    // 重複チェック用のハッシュ生成\n    const hash = require('crypto')\n      .createHash('md5')\n      .update(`${data.source}_${data.image_url}`)\n      .digest('hex');\n    \n    validatedData.push({\n      ...data,\n      hash: hash,\n      processed_at: new Date().toISOString(),\n      status: 'validated'\n    });\n  }\n}\n\nconsole.log(`Validated ${validatedData.length} items out of ${allData.length}`);\n\nreturn validatedData.map(item => ({ json: item }));"}, "id": "validate-data", "name": "Validate Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1380, 260]}, {"parameters": {"url": "={{ $env.SUPABASE_URL }}/rest/v1/external_ui_data", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "save-to-supabase", "name": "Save to Supabase", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1600, 260], "credentials": {"httpHeaderAuth": {"id": "supabase-auth", "name": "Supabase Auth"}}}, {"parameters": {"jsCode": "// Supabase保存後の処理結果を集約\nconst saveResults = $input.all();\nconst successfulSaves = [];\nconst failedSaves = [];\n\nfor (const result of saveResults) {\n  const response = result.json;\n  \n  if (response.status && response.status >= 200 && response.status < 300) {\n    successfulSaves.push({\n      id: response.body?.id || response.id,\n      status: 'saved',\n      timestamp: new Date().toISOString()\n    });\n  } else {\n    failedSaves.push({\n      error: response.error || 'Unknown error',\n      status: 'failed',\n      timestamp: new Date().toISOString()\n    });\n  }\n}\n\nconst summary = {\n  total_processed: saveResults.length,\n  successful_saves: successfulSaves.length,\n  failed_saves: failedSaves.length,\n  successful_ids: successfulSaves.map(s => s.id),\n  timestamp: new Date().toISOString()\n};\n\nconsole.log('Save Results Summary:', JSON.stringify(summary, null, 2));\n\nreturn [{ json: summary }];"}, "id": "process-save-results", "name": "Process Save Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1820, 260]}, {"parameters": {"url": "={{ $env.REPLAY_DESIGN_SCORE_APP_URL }}/api/collect-data", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"source\": \"n8n_automation\",\n  \"data_summary\": {{ $json }},\n  \"ui_data_ids\": {{ $json.successful_ids }},\n  \"timestamp\": \"{{ $json.timestamp }}\",\n  \"metadata\": {\n    \"workflow_name\": \"UI Data Automation Workflow\",\n    \"execution_id\": \"{{ $runIndex }}\",\n    \"trigger_type\": \"{{ $node.name }}\"\n  }\n}", "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "notify-replay-app", "name": "Notify Replay Design App", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2040, 260], "credentials": {"httpHeaderAuth": {"id": "replay-app-auth", "name": "<PERSON><PERSON> <PERSON><PERSON>"}}}, {"parameters": {"jsCode": "// 最終結果のログ出力\nconst supabaseResult = $input.first().json;\nconst replayResult = $input.all()[1]?.json;\n\nconst finalSummary = {\n  workflow_execution: {\n    completed_at: new Date().toISOString(),\n    status: 'completed'\n  },\n  supabase_save: {\n    total_processed: supabaseResult.total_processed,\n    successful_saves: supabaseResult.successful_saves,\n    failed_saves: supabaseResult.failed_saves\n  },\n  replay_app_notification: {\n    status: replayResult?.status || 'unknown',\n    response: replayResult?.body || replayResult\n  }\n};\n\nconsole.log('=== UI Data Automation Workflow Completed ===');\nconsole.log(JSON.stringify(finalSummary, null, 2));\n\nreturn [{ json: finalSummary }];"}, "id": "final-log", "name": "Final Log", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2260, 260]}], "connections": {"Schedule Trigger": {"main": [[{"node": "Initialize Data Sources", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Initialize Data Sources", "type": "main", "index": 0}]]}, "Initialize Data Sources": {"main": [[{"node": "Route Switch", "type": "main", "index": 0}]]}, "Route Switch": {"main": [[{"node": "Get Employee Submissions", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Scrape Landbook", "type": "main", "index": 0}]]}, "Get Employee Submissions": {"main": [[{"node": "Process Employee Submissions", "type": "main", "index": 0}]]}, "Process Employee Submissions": {"main": [[{"node": "Merge All Data", "type": "main", "index": 0}]]}, "Scrape Dribbble": {"main": [[{"node": "Merge All Data", "type": "main", "index": 1}]]}, "Scrape Landbook": {"main": [[{"node": "Merge All Data", "type": "main", "index": 2}]]}, "Merge All Data": {"main": [[{"node": "Validate Data", "type": "main", "index": 0}]]}, "Validate Data": {"main": [[{"node": "Save to Supabase", "type": "main", "index": 0}]]}, "Save to Supabase": {"main": [[{"node": "Process Save Results", "type": "main", "index": 0}]]}, "Process Save Results": {"main": [[{"node": "Notify Replay Design App", "type": "main", "index": 0}]]}, "Notify Replay Design App": {"main": [[{"node": "Final Log", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-07T00:00:00.000Z", "updatedAt": "2025-01-07T00:00:00.000Z", "id": "ui-automation", "name": "UI Data Automation"}], "triggerCount": 2, "updatedAt": "2025-01-07T00:00:00.000Z", "versionId": "1"}