{"name": "My workflow 94", "nodes": [{"parameters": {"formTitle": "Quote", "formFields": {"values": [{"fieldLabel": "First Name"}, {"fieldLabel": "Last Name"}, {"fieldLabel": "Email"}, {"fieldLabel": "Budget"}, {"fieldLabel": "How likely are you to hire", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Not likely"}, {"option": "Very likely"}]}}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [0, 0], "id": "dcc123da-a12b-42b5-9934-f8323ffff7c8", "name": "On form submission", "webhookId": "d413ea18-5e35-4916-901f-906bcc9914be"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1NJqLivvM6csVXPysFuaJCaol9HhA2qjFv5AqCU9ucjI", "mode": "list", "cachedResultName": "AI automation leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NJqLivvM6csVXPysFuaJCaol9HhA2qjFv5AqCU9ucjI/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NJqLivvM6csVXPysFuaJCaol9HhA2qjFv5AqCU9ucjI/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"First Name": "={{ $json['First Name'] }}", "Last Name": "={{ $json['Last Name'] }}", "Email": "={{ $json.Email }}", "Budget": "={{ $json.Budget }}", "How likely to hire": "={{ $json['How likely are you to hire'] }}"}, "matchingColumns": ["Email"], "schema": [{"id": "First Name", "displayName": "First Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Last Name", "displayName": "Last Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email", "displayName": "Email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Budget", "displayName": "Budget", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "How likely to hire", "displayName": "How likely to hire", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [220, 0], "id": "50ea75c0-e981-454a-8ee7-6f346dc2f43c", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "g2d7DmGP4bt1Fhxw", "name": "Google Sheets account 3"}}}, {"parameters": {"sendTo": "={{ $json.Email }}", "subject": "Thanks for inquiring with ABC company!", "emailType": "text", "message": "=Hey {{ $json['First Name'] }},\n\nThanks for inquiring with abc company....\n\nHere's a link to book on my calendar:\ncalendly.com/jono-catliff\n\n<PERSON>,\n<PERSON><PERSON>", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1000, 0], "id": "ae7e33a0-8d50-4ba9-a0bd-c5e09978282a", "name": "Gmail", "webhookId": "7a6511c7-d32f-4e20-824b-455efdd2ad52", "credentials": {"gmailOAuth2": {"id": "JTi4osPKFLhXOTFk", "name": "Gmail account 13"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "You're an intelligent bot capable of scoring new leads. Please assign points on a 1-5 rating based on these categories:\n1. If the budget is over 1000, please award 3 points\n2. If the email of the client is a business email, please award 1 point. Do not award points if its a gmail or yahoo email\n3. How likely they are to hire - award 1 point if they're very likely to hire\n\nPlease only generate as an output 1 key for \"score\"", "role": "system"}, {"content": "=Email: {{ $json.Email }}\nBudget: {{ $json.Budget }}\nHow likely client is to hire: {{ $json['How likely to hire'] }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [440, 0], "id": "a40145fc-1064-4d75-bc34-7ed91359e177", "name": "OpenAI", "credentials": {"openAiApi": {"id": "X5OhJruK6AtqRLZh", "name": "OpenAi account 5"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b66c0f54-fa0f-4342-9afa-375879d57634", "leftValue": "={{ $json.message.content.score }}", "rightValue": 3, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [800, 0], "id": "442cce0b-e49e-48cf-bbb7-8620533a6ea1", "name": "Filter"}, {"parameters": {"sendTo": "=YOUR_EMAIL_HERE", "subject": "New Lead", "emailType": "text", "message": "=New Lead\nName: {{ $('On form submission').item.json['First Name'] }} {{ $('On form submission').item.json['Last Name'] }}\nEmail: {{ $('On form submission').item.json.Email }}\nBudget: {{ $('On form submission').item.json.Budget }}\nLikely to hire: {{ $('On form submission').item.json['How likely are you to hire'] }}\n", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1180, 0], "id": "7a656185-8344-464b-9db9-2c0142593bc2", "name": "Gmail1", "webhookId": "7a6511c7-d32f-4e20-824b-455efdd2ad52", "credentials": {"gmailOAuth2": {"id": "JTi4osPKFLhXOTFk", "name": "Gmail account 13"}}}], "pinData": {"On form submission": [{"json": {"First Name": "<PERSON><PERSON>", "Last Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Budget": "1500", "How likely are you to hire": "Very likely", "submittedAt": "2025-07-10T11:42:42.332-04:00", "formMode": "test"}}]}, "connections": {"On form submission": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Gmail": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "41bfa9bc-ef26-43fc-9fc2-e53c7377b362", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d2017cc6d1e4b956d269a8123bffa72fb7aaa41ad37a73b7c0fb64c7d0e2edae"}, "id": "CSHi2FHH7bp8VVPW", "tags": []}