{"name": "document-agent", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "YOUR_UNPROCESSED_FOLDER_ID", "mode": "list", "cachedResultName": "Tax - unprocessed", "cachedResultUrl": "https://drive.google.com/drive/folders/YOUR_UNPROCESSED_FOLDER_ID"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [120, 80], "id": "50a77a78-5987-487c-965e-c1216064a0d7", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Your Google Drive Credential"}}}, {"parameters": {"inputDataFieldName": "=data", "options": {}}, "type": "n8n-nodes-tesseractjs.tesseractNode", "typeVersion": 1, "position": [600, 0], "id": "3c91aeaa-c644-4ea1-89fe-ca0fd4943a3c", "name": "Tesseract"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [260, 80], "id": "70672b47-5e89-472e-9c9b-4c372c7c6b18", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Your Google Drive Credential"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1480, 160], "id": "9076bf39-aceb-497f-8eac-ad938a78229f", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "Your OpenAI Credential"}}}, {"parameters": {"text": "={{ $json.markdown }}", "attributes": {"attributes": [{"name": "invoice_category", "description": "The category of the invoice. Choose one of: office supplies, bills (electricity, water, gas), rent", "required": true}, {"name": "invoice_number", "description": "The invoice number", "required": true}, {"name": "invoice_biller", "description": "The company that issue the invoice", "required": true}, {"name": "invoice_total_inc", "description": "The total paid on the invoice, including tax", "required": true}, {"name": "invoice_total_ex", "description": "The total paid on the invoice, excluding tax", "required": true}, {"name": "invoice_tax", "description": "The tax amount on the invoice", "required": true}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1.1, "position": [1500, 0], "id": "3346d3e2-ef92-4d63-9a2d-348e37149d30", "name": "Information Extractor"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "YOUR_TAX_LOG_SPREADSHEET_ID", "mode": "list", "cachedResultName": "tax-log", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_TAX_LOG_SPREADSHEET_ID/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_TAX_LOG_SPREADSHEET_ID/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"invoice_category": "={{ $json.output.invoice_category }}", "invoice_number": "={{ $json.output.invoice_number }}", "invoice_biller": "={{ $json.output.invoice_biller }}", "invoice_total_inc": "={{ $json.output.invoice_total_inc }}", "invoice_total_ex": "={{ $json.output.invoice_total_ex }}", "invoice_tax": "={{ $json.output.invoice_tax }}", "file_name": "={{ $('Google Drive Trigger').item.json.originalFilename }}", "file_url": "={{ $('Google Drive Trigger').item.json.webViewLink }}"}, "matchingColumns": ["row_number"], "schema": [{"id": "file_url", "displayName": "file_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "file_name", "displayName": "file_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "invoice_category", "displayName": "invoice_category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "invoice_number", "displayName": "invoice_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "invoice_biller", "displayName": "invoice_biller", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "invoice_total_inc", "displayName": "invoice_total_inc", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "invoice_total_ex", "displayName": "invoice_total_ex", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "invoice_tax", "displayName": "invoice_tax", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1800, 0], "id": "e86c7ba1-a92f-413a-af50-cd5bd1b06f0a", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "YOUR_GOOGLE_SHEETS_CREDENTIAL_ID", "name": "Your Google Sheets Credential"}}}, {"parameters": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "YOUR_PROCESSED_FOLDER_ID", "mode": "list", "cachedResultName": "Tax - processed", "cachedResultUrl": "https://drive.google.com/drive/folders/YOUR_PROCESSED_FOLDER_ID"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2080, 0], "id": "293fc2d9-ccbe-4020-b40e-b384fd5f5f33", "name": "Google Drive1", "credentials": {"googleDriveOAuth2Api": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Your Google Drive Credential"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Google Drive Trigger').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1940, 0], "id": "b72c2529-2715-48d0-b3eb-818ccc9eeadc", "name": "Google Drive2", "credentials": {"googleDriveOAuth2Api": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Your Google Drive Credential"}}}, {"parameters": {"operation": "deleteFile", "fileId": {"__rl": true, "value": "={{ $('Google Drive Trigger').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2220, 0], "id": "ed27c603-6c27-4476-9007-9db1b5ea5032", "name": "Google Drive3", "credentials": {"googleDriveOAuth2Api": {"id": "YOUR_GOOGLE_DRIVE_CREDENTIAL_ID", "name": "Your Google Drive Credential"}}}, {"parameters": {"content": "## New Invoice\nTriggers when a new invoice is added to the folder.\nThe new invoice is downloaded for use in the flow.\nWe select the best extraction process based on document type.", "width": 380, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, -100], "id": "861225d2-ea4b-478a-a6fc-6c0f0f9ce86f", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Extract data from the invoice\nWe use OCR (top route) for PNG\nWe use LlamaParse (bottom route) for PDF, DOCX, TXT", "width": 720, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [600, -180], "id": "da9b80bb-8c35-4c29-8a1b-6ebd5155e9c1", "name": "Sticky Note1"}, {"parameters": {"content": "## Clean Up Files\nOnce the invoice has been successfully processed, we then move the file to the processed folder, and wipe it from the unprocessed folder.", "width": 380, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1940, -180], "id": "23e530e0-b93c-48b1-8b2d-8612660e45b9", "name": "Sticky Note2"}, {"parameters": {"documentId": {"__rl": true, "value": "YOUR_TAX_LOG_SPREADSHEET_ID", "mode": "list", "cachedResultName": "tax-log", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_TAX_LOG_SPREADSHEET_ID/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/YOUR_TAX_LOG_SPREADSHEET_ID/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [860, 780], "id": "ab2cc830-9048-4b0a-bd79-92be91f18bd2", "name": "Google Sheets1", "credentials": {"googleSheetsOAuth2Api": {"id": "YOUR_GOOGLE_SHEETS_CREDENTIAL_ID", "name": "Your Google Sheets Credential"}}}, {"parameters": {"jsCode": "// Get all items\nconst rows = $input.all().map(item => item.json);\n\n// Start the summary text\nlet text = `Here is a list of expenses:\\n\\n`;\n\n// Loop through rows and build the summary\nfor (const row of rows) {\n  text += `- Invoice #${row.invoice_number} | $${row.invoice_total_inc} | ${row.invoice_category} | Biller: ${row.invoice_biller}\\n`;\n}\n\n// Return single output with combined summary\nreturn [{ json: { expenseSummary: text } }];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 780], "id": "99feda34-63e5-4cfb-a1c1-eb220225b362", "name": "Code"}, {"parameters": {"promptType": "define", "text": "={{ $json.expenseSummary }}", "options": {"systemMessage": "You are a financial report assistant.\n\nBelow is a list of business expenses. Please analyze this data and generate a plain text business report using the following structure.\n\nFormat the output with spacing and line breaks only — no Markdown, no bullet points, no symbols like asterisks. Just plain, readable text.\n\n---\n\n1. Executive Summary  \nWrite 2–3 sentences summarizing what the expenses are, any patterns observed, and anything that may require attention.\n\n2. Expense Breakdown  \nGroup expenses by category. For each category, report:\n- Number of expenses\n- Total amount (including tax)\n- Average amount (including tax)\n- Any repeated invoice numbers\n\n3. Vendor Insights  \nList vendors by total spend. Mention:\n- Total spend per vendor\n- Number of occurrences\n- Any suspicious duplicate entries\n\n4. Recommendations  \nProvide 2–3 business recommendations based on your analysis."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [1140, 780], "id": "e3622bdd-f6eb-4250-afca-666cdd44e72b", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1120, 940], "id": "fdc02939-026f-4d0b-a04b-3efb4199ea24", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "YOUR_OPENAI_CREDENTIAL_ID", "name": "Your OpenAI Credential"}}}, {"parameters": {"content": "## Financial Spending Report\nThis agent will generate a report on your financial spending.", "width": 540, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1140, 600], "id": "7d20f9dc-a4eb-4f35-88a7-debe4507393a", "name": "Sticky Note3"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Google Drive Trigger').item.json.fileExtension }}", "rightValue": "png", "operator": {"type": "string", "operation": "equals"}, "id": "36584cf8-8764-4e7a-bb1a-0877c2a78014"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PNG"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "30167564-7a8e-40a1-9009-64be1031fded", "leftValue": "={{ $('Google Drive Trigger').item.json.fileExtension }}", "rightValue": "pdf", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "PDF"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [400, 80], "id": "7964b6d3-0a43-4c86-97e6-d72d7efb8188", "name": "Switch"}, {"parameters": {"method": "POST", "url": "https://api.cloud.llamaindex.ai/api/v1/parsing/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_LLAMAINDEX_API_KEY"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 160], "id": "62cbbcda-d3c2-41aa-9963-c49dfee9c1b5", "name": "Upload"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [760, 160], "id": "1906b922-6bf5-43a5-bb1d-86c0f41c4a7f", "name": "Wait", "webhookId": "YOUR_WEBHOOK_ID"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"leftValue": "={{ $json.status }}", "rightValue": "SUCCESS", "operator": {"type": "string", "operation": "equals"}, "id": "5fe5fdf8-6137-4466-8e88-acaaebbf94a5"}], "combinator": "and"}, "renameOutput": true, "outputKey": "SUCCESS"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "a1480222-bc5d-4697-b169-5e94a5afa0ed", "leftValue": "={{ $json.status }}", "rightValue": "PENDING", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "PENDING"}]}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [1040, 160], "id": "9c2d967d-73c8-407b-9086-962b27d8c575", "name": "Switch1"}, {"parameters": {"url": "=https://api.cloud.llamaindex.ai/api/v1/parsing/job/{{ $json.id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_LLAMAINDEX_API_KEY"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 160], "id": "7975e139-7f3b-4423-a6d8-1164d29a9af1", "name": "Status"}, {"parameters": {"url": "=https://api.cloud.llamaindex.ai/api/v1/parsing/job/{{ $json.id }}/result/markdown", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer YOUR_LLAMAINDEX_API_KEY"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 160], "id": "36c053a7-e387-467f-b31c-3ca9eb2ac4bc", "name": "Get"}, {"parameters": {"jsCode": "const items = $input.all();\n\n// Build Markdown from parsed text block\nconst markdownItems = items.map(item => {\n  const text = item.json.text || '';\n  return {\n    json: {\n      markdown: `### Parsed Invoice\\n\\n\\`\\`\\`\\n${text.trim()}\\n\\`\\`\\``\n    }\n  };\n});\n\nreturn markdownItems;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [760, 0], "id": "db70ecc1-0f3d-404f-acd7-05811387fb17", "name": "Code1"}, {"parameters": {"content": "## Data Processing and Logging\nOnce we have extracted data from the invoice, we run it through a data extractor agent to pull out structured data. \nOnce we have pulled the data, we log it in google sheets.", "width": 540, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1360, -180], "id": "fb7d8438-6e38-4025-9ba9-be83a69f01ae", "name": "Sticky Note4"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1360, 0], "id": "31f33219-e2cf-4466-bc1d-3f64e6d45054", "name": "<PERSON><PERSON>"}, {"parameters": {"folderId": "default", "title": "Financial Year Expense Report"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1440, 780], "id": "8b86b5b0-5305-4a28-8249-c77eabf5247b", "name": "Generate", "credentials": {"googleDocsOAuth2Api": {"id": "YOUR_GOOGLE_DOCS_CREDENTIAL_ID", "name": "Your Google Docs Credential"}}}, {"parameters": {"operation": "update", "documentURL": "={{ $json.id }}", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $('AI Agent').item.json.output }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1580, 780], "id": "253e62f4-775c-4897-a733-7c0287bd1869", "name": "Update", "credentials": {"googleDocsOAuth2Api": {"id": "YOUR_GOOGLE_DOCS_CREDENTIAL_ID", "name": "Your Google Docs Credential"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [700, 780], "id": "148b64d2-a812-4912-866d-8c736b5207f7", "name": "When clicking 'Execute workflow'"}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Tesseract": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Information Extractor", "type": "ai_languageModel", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Google Drive2", "type": "main", "index": 0}]]}, "Google Drive2": {"main": [[{"node": "Google Drive1", "type": "main", "index": 0}]]}, "Google Drive1": {"main": [[{"node": "Google Drive3", "type": "main", "index": 0}]]}, "Google Sheets1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Generate", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Switch": {"main": [[{"node": "Tesseract", "type": "main", "index": 0}], [{"node": "Upload", "type": "main", "index": 0}]]}, "Upload": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Status", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Get", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Status": {"main": [[{"node": "Switch1", "type": "main", "index": 0}]]}, "Get": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Generate": {"main": [[{"node": "Update", "type": "main", "index": 0}]]}, "When clicking 'Execute workflow'": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "PLACEHOLDER_VERSION_ID", "meta": {"templateCredsSetupCompleted": true, "instanceId": "PLACEHOLDER_INSTANCE_ID"}, "id": "PLACEHOLDER_WORKFLOW_ID", "tags": []}