{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "0cbae229-457c-4768-b51b-2301109a64e5", "name": "user_input", "type": "string", "value": ""}, {"id": "e930ded6-311f-4468-8c92-e04f03c4ab03", "name": "brand_voice", "type": "string", "value": "funny"}, {"id": "a8573432-bd5e-45b1-aa1f-bbffcad735a0", "name": "platforms", "type": "string", "value": "\"twitter,linkedin,instagram,tiktok,youtube\""}, {"id": "52483276-3db0-447b-a255-556c91848d56", "name": "campaign_type", "type": "string", "value": "\"standard\""}]}, "options": {}}, "id": "b3de7fd9-a907-48e7-bc16-e49df2a79167", "name": "Set Default Inputs", "type": "n8n-nodes-base.set", "position": [768, 880], "typeVersion": 3.4}, {"parameters": {"url": "https://api.twitterapi.io/twitter/tweet/advanced_search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "manus"}, {"name": "queryType", "value": "Latest"}, {"name": "exclude", "value": "hashtags"}]}, "options": {}}, "id": "9c9ce9bd-3b33-4494-be6c-8afbc95e5945", "name": "Fetch Twitter Trends", "type": "n8n-nodes-base.httpRequest", "position": [2192, 688], "typeVersion": 4.2, "credentials": {}}, {"parameters": {"url": "https://api.twitterapi.io/twitter/user/mentions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "userName", "value": "ewara<PERSON>"}]}, "options": {}}, "id": "89401bf8-d40b-41a0-924f-4fcf6eadf19d", "name": "Fetch Twitter Mentions", "type": "n8n-nodes-base.httpRequest", "position": [992, 480], "typeVersion": 4.2, "credentials": {}}, {"parameters": {"url": "https://serpapi.com/search?engine=google_trends_trending_now", "authentication": "predefinedCredentialType", "nodeCredentialType": "serp<PERSON><PERSON>", "sendQuery": true, "queryParameters": {"parameters": [{"name": "engine", "value": "google_trends"}, {"name": "geo", "value": "US"}, {"name": "api_key", "value": "=API-KEY"}, {"name": "q", "value": "trending_searches"}]}, "options": {}}, "id": "ccc638fc-5d8a-49d0-a211-b8cc7c409144", "name": "SERP-Google Trends", "type": "n8n-nodes-base.httpRequest", "position": [1296, 1184], "typeVersion": 4.2, "credentials": {}}, {"parameters": {"url": "https://newsapi.org/v2/top-headlines", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "category", "value": "technology"}, {"name": "language", "value": "en"}]}, "options": {}}, "id": "58becbca-b7ab-4c96-9719-d6e1279e1a0c", "name": "Fetch Breaking News", "type": "n8n-nodes-base.httpRequest", "position": [1296, 880], "typeVersion": 4.2, "credentials": {}}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Analyze the sentiment of the following news headlines and classify it as \"positive\", \"negative\", or \"neutral\":\n\n\n- Headlines: {{ $json.news_headlines.map(item => item.title).join(\", \") }}\n\nReturn the overall sentiment (e.g., \"positive\")."}]}, "options": {}}, "id": "3b535acf-4372-435f-a578-71e98508cf97", "name": "Analyze News Sentiment", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1808, 880], "typeVersion": 1.8, "credentials": {}}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Analyze the sentiment of the following user-generated content to detect the audience&apos;s mood (e.g., \"excited\", \"frustrated\", \"neutral\"):\n- Content: {{ $json.translated_tweets }}\nReturn the mood as a string (e.g., \"excited\")."}]}, "options": {}}, "id": "85ab422d-ce13-420a-961d-0424ff05fd02", "name": "Detect Audience Mood", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1808, 480], "typeVersion": 1.8, "credentials": {}}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Translate the following text from Thai to English:\n\n\n- Text: {{ $json.tweets.map(item => item.text).join(\", \") }}\nReturn the translated text as a string.\n\n"}]}, "options": {}}, "id": "c34b4617-5740-4aad-8590-ad45f497c9c9", "name": "Translate Tweets to English", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1216, 480], "typeVersion": 1.8, "credentials": {}}, {"parameters": {"numberInputs": 5}, "id": "31a58826-fea7-4488-9355-27910fdb5b58", "name": "Combine All Data", "type": "n8n-nodes-base.merge", "position": [2400, 848], "typeVersion": 3.1}, {"parameters": {"jsCode": "// Combine all items into a single item\nconst combinedItem = {};\n\nitems.forEach(item => {\n  // Ensure the item has a .json sub-object\n  const data = item.json || {};\n\n  // If the data contains a &apos;message&apos; field (e.g., from OpenAI), map it to the expected field\n  if (data.message) {\n    // Assuming &apos;message&apos; contains the audience_mood or news_sentiment\n    // We can determine the field based on the node&apos;s context, but for now, let&apos;s assume it&apos;s audience_mood\n    data.audience_mood = data.message;\n    delete data.message; // Remove the &apos;message&apos; field to avoid conflicts\n  }\n\n  // Merge the data into the combined item\n  Object.assign(combinedItem, data);\n});\n\n// Return the combined item as a single item\nreturn [combinedItem];"}, "id": "3cd1c336-e710-4e0d-8d22-cc45cc69cfaa", "name": "Merge Items into Single Item", "type": "n8n-nodes-base.code", "position": [2624, 880], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "4ffece5c-d223-4285-8cb7-c9454b8e3d86", "name": "translated_tweets", "type": "string", "value": "={{ $json.message.content }}"}]}, "options": {}}, "id": "9f14c947-2824-49db-a340-f6de04cf2a44", "name": "Fix Translated Tweets Output", "type": "n8n-nodes-base.set", "position": [1584, 480], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "46698f2d-fe61-4423-a2f1-bd2fe5386f69", "name": "audience_mood", "type": "string", "value": "={{ $json.message.content }}"}, {"id": "c5c34398-35b8-434b-a485-00b589b87feb", "name": "translated_tweets", "type": "string", "value": "={{ $json.message.content }}"}]}, "options": {}}, "id": "e40fe69e-5a35-4c34-be08-d1ebd68025c5", "name": "Fix Audience Mood Output", "type": "n8n-nodes-base.set", "position": [2192, 480], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "a43642e2-3635-4753-962e-d0011fe76cc3", "name": "news_sentiment", "type": "string", "value": "={{ $json.message.content }}"}]}, "options": {}}, "id": "25f707ac-55c9-4e64-ad72-e9cd3cfb4f31", "name": "Fix News Sentiment Output", "type": "n8n-nodes-base.set", "position": [2192, 880], "typeVersion": 3.4}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Score the relevance of the following trends for a social media campaign with the given brand voice and audience mood (1-10, where 10 is most relevant). Each trend must be scored exactly as provided, without splitting, modifying, or omitting any trends. You must return a score for every trend listed below:\n\nTrends:\n{{ $json.trends.map(item => `- \"${item.name}\"`).join(\"\\n\") }}\n\n- Brand Voice: {{ $json.brand_voice }}\n- Audience Mood: {{ $json.audience_mood }}\n\nReturn a JSON array in the format: [{\"trend\": \"Trend 1\", \"score\": 8}, {\"trend\": \"Trend 2\", \"score\": 5}]. The array must include exactly one entry for each trend listed above, with the trend name exactly matching the input (including all punctuation and spacing)."}]}, "jsonOutput": true, "options": {}}, "id": "71f00ef1-c059-43c4-bcef-b89857946cf6", "name": "Trend Relevance Scoring (Wow Factor)", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3280, 880], "typeVersion": 1.8, "credentials": {}}, {"parameters": {"assignments": {"assignments": [{"id": "c5222171-8032-4eb5-9d2e-19d803f18db6", "name": "google_trends", "type": "string", "value": "="}]}, "options": {}}, "id": "ef2e28d3-40ab-4a2b-8771-fcc7b682eaf2", "name": "Extract Google Trends", "type": "n8n-nodes-base.set", "position": [2192, 1088], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "f9130df8-d6bc-41ab-8cdb-79a3ca89a4b8", "name": "news_headlines", "type": "string", "value": "={{ $json.articles.map(item => ({ title: item.title })) }}"}]}, "options": {}}, "id": "ff5752d2-3d58-4a1d-bb51-c90c63aa3c14", "name": "Extract News Headlines", "type": "n8n-nodes-base.set", "position": [1584, 880], "typeVersion": 3.4}, {"parameters": {"content": "# Trend Extraction and Scoring Workflow 🛠️\n\n## 📋 **Section 1: Trend Extraction and Scoring (Phase 1)**  \n**What It Does** 🌟  \nFetches trends, combines them with UGC, scores them using OpenAI, and stores them in Google Sheets.  \n\n**Steps** 🔍  \n- **Fetch Trends** 📡: Pulls from Reddit & Google Trends.  \n- **Combine Data** 🤝: Merges trends & UGC (tweets, mentions).  \n- **Score Trends** 🧠: OpenAI scores trends (1-10).  \n- **Store Results** 💾: Saves to Google Sheets.  \n\n---", "height": 1820, "width": 1980}, "id": "8730bd02-2ea8-4ab3-b2bb-2c581c11653e", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [368, 240], "typeVersion": 1}, {"parameters": {"jsCode": "console.log(JSON.stringify(items, null, 2));\nreturn items;"}, "id": "cc71da6e-0d29-4524-95af-18f7b02bd9e4", "name": "Log Reddit Response", "type": "n8n-nodes-base.code", "position": [1888, 1488], "typeVersion": 2}, {"parameters": {"operation": "getAll", "subreddit": "=technology", "limit": 3, "filters": {"category": "hot"}}, "id": "c9c883b1-c0c8-4461-8fa9-5b5addd7ed91", "name": "Posts from Reddit", "type": "n8n-nodes-base.reddit", "position": [1584, 1488], "typeVersion": 1, "credentials": {}}, {"parameters": {"jsCode": "// Map over the items array to extract title and score from each item&apos;s json\nconst redditTrends = items.map(item => ({\n  title: item.json.title,\n  score: item.json.score || item.json.ups || 0\n}));\n\n// Return the mapped data as a single item\nreturn [{ json: { reddit_trends: redditTrends } }];"}, "id": "e16e1624-4355-46d6-a579-a3366634dff8", "name": "Exctract Reddit Trends", "type": "n8n-nodes-base.code", "position": [2192, 1488], "typeVersion": 2}, {"parameters": {"sendTo": "emai<PERSON><PERSON>@gmail.com", "subject": "Google Trends API Failure", "message": "=Google Trends API failed", "options": {}}, "id": "bd473ff1-2e2d-4068-9106-2a04bb037cf4", "name": "Notify Google Trends Failure", "type": "n8n-nodes-base.gmail", "position": [2192, 1280], "webhookId": "7ebf0d89-f43b-40f8-8d22-76f41759897f", "typeVersion": 2.1, "credentials": {}}, {"parameters": {"jsCode": "// Ensure all fields are arrays, with a fallback to an empty array if undefined\nconst tweets = Array.isArray(items[0].json.tweets) ? items[0].json.tweets : [];\nconst redditTrends = Array.isArray(items[0].json.reddit_trends) ? items[0].json.reddit_trends : [];\nconst googleTrends = Array.isArray(items[0].json.google_trends) ? items[0].json.google_trends : [];\nconst twitterMentions = Array.isArray(items[0].json.twitter_mentions) ? items[0].json.twitter_mentions : [];\n\n// Combine all trends into a single array\nconst allTrends = tweets.concat(redditTrends, googleTrends, twitterMentions);\n\n// Map to a consistent format: {name: \"...\"}\nconst trends = allTrends.map(item => ({\n  name: item.name || item.title || item.query || item.text || \"Unknown Trend\"\n}));\n\n// Determine brand_voice based on audience_mood or set a default\nlet brandVoice = \"professional\"; // Default brand voice\nconst audienceMood = items[0].json.audience_mood || \"neutral\";\nif (audienceMood === \"excited\") {\n  brandVoice = \"playful\"; // Example: Map \"excited\" mood to a \"playful\" voice\n} else if (audienceMood === \"negative\") {\n  brandVoice = \"empathetic\";\n}\n\n// Return the combined data, preserving other fields\nreturn [{\n  json: {\n    trends: trends,\n    news_sentiment: items[0].json.news_sentiment || \"neutral\",\n    audience_mood: audienceMood,\n    translated_tweets: items[0].json.translated_tweets || \"\",\n    brand_voice: brandVoice\n  }\n}];"}, "id": "e5c0c11e-4631-4420-a976-9bd4ea9947ee", "name": "Combine Trends and UGCc", "type": "n8n-nodes-base.code", "position": [2848, 880], "typeVersion": 2}, {"parameters": {"jsCode": "const trendScores = items[0].json.trend_scores || [];\nconst topTrend = trendScores.length > 0\n  ? trendScores.reduce((max, item) => item.score > max.score ? item : max, trendScores[0])\n  : { trend: \"No trend available\", score: 0 };\n\nreturn [{\n  json: {\n    selected_idea: topTrend.trend,\n    brand_voice: items[0].json.brand_voice || \"professional\",\n    audience_mood: items[0].json.audience_mood || \"neutral\"\n  }\n}];"}, "id": "da9b1622-1ef7-4ae8-a793-a8ac211b599d", "name": "Select Top Trends", "type": "n8n-nodes-base.code", "position": [3136, 1824], "typeVersion": 2}, {"parameters": {"jsCode": "// 1. First, let&apos;s properly access the nested trends data\nconst messageContent = items[0]?.json?.message?.content || {};\nconst scoredTrends = messageContent.trends || [];\n\n// 2. Debug output - check the execution data tab in n8n for these\nconsole.log(\"Full input item:\", JSON.stringify(items[0], null, 2));\nconsole.log(\"Extracted trends:\", JSON.stringify(scoredTrends, null, 2));\n\n// 3. Process the trends we found\nif (scoredTrends.length > 0) {\n  return [{\n    json: {\n      trend_scores: scoredTrends.map(t => ({\n        trend: t.trend || \"Unnamed trend\",\n        score: typeof t.score === \"number\" ? t.score : 5\n      }))\n    }\n  }];\n}\n\n// 4. Final fallback if no trends found\nreturn [{\n  json: {\n    trend_scores: [{\n      trend: \"No trends available\", \n      score: 5\n    }]\n  }\n}];"}, "id": "14be1af0-4aa8-4f0e-9e6b-8645070b3284", "name": "Parse Trend Scores", "type": "n8n-nodes-base.code", "position": [3664, 880], "typeVersion": 2}, {"parameters": {"jsCode": "// Simplify audience_mood to a single word or short phrase\nlet audienceMood = items[0].json.audience_mood || \"neutral\";\nif (audienceMood.includes(\"excited\")) {\n  audienceMood = \"excited\";\n} else if (audienceMood.includes(\"joyous\")) {\n  audienceMood = \"joyous\";\n} else {\n  audienceMood = \"neutral\";\n}\n\n// Return the modified data with a default brand_voice\nreturn [{\n  json: {\n    trends: items[0].json.trends || [],\n    audience_mood: audienceMood,\n    brand_voice: items[0].json.brand_voice || \"professional\", // Default to \"professional\"\n    news_sentiment: items[0].json.news_sentiment || \"neutral\",\n    translated_tweets: items[0].json.translated_tweets || \"\"\n  }\n}];"}, "id": "0a01225f-fab1-4de2-adf2-4dfafc00e540", "name": "Prepare Trend Scoring Input", "type": "n8n-nodes-base.code", "position": [3072, 880], "typeVersion": 2}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Using the following selected idea, brand voice, and audience mood, generate social media content for the specified platforms:\n\n- Selected Idea: A promotional campaign involving Union Berlin, Deportivo Alavés, and Boavista, with a discount code \"M63\" for fans.\n- Brand Voice: professional\n- Audience Mood: excited\n\nGenerate content for the following platforms:\n- Twitter/X (280 characters max, include hashtags)\n- LinkedIn (professional tone, 500 characters max)\n- TikTok (script for a 15-30 second video, include hashtags)\n- YouTube Shorts (script for a 60-second video, include hashtags)\n\nReturn a JSON object in the format:\n{\n  \"twitter\": \"...\",\n  \"linkedin\": \"...\",\n  \"tiktok\": \"...\",\n  \"youtube_shorts\": \"...\"\n}\n\nEnsure the response is a valid JSON object. Do not include any additional text outside the JSON object."}]}, "options": {}}, "id": "210334c2-9e37-4529-a18c-a3bd7d1212ea", "name": "Generate Social Media Content", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3296, 1824], "typeVersion": 1.8, "credentials": {}}, {"parameters": {"content": "# 📋 Content Generation  \n**What It Does** 🎨  \nRetrieves trends, selects the top one, and generates social media content with OpenAI.  \n\n**Steps** 📝  \n- **Retrieve Trends** 📥: Gets trends from Google Sheets.  \n- **Generate Content** ✨: Creates content for Twitter/X, LinkedIn, TikTok, YouTube Shorts.", "height": 640, "width": 1580, "color": 2}, "id": "cb96b03e-87da-4d90-8a52-510bc8b64125", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [2608, 544], "typeVersion": 1}, {"parameters": {"jsCode": "// Get the OpenAI response\nlet content;\nif (items[0]?.json?.message?.content) {\n  content = items[0].json.message.content;\n} else if (items[0]?.json?.content) {\n  content = items[0].json.content;\n} else {\n  console.error(\"Unexpected OpenAI response structure:\", items[0]);\n  content = \"{}\"; // Fallback to empty JSON\n}\n\n// Parse the response as JSON if it&apos;s a string\nlet parsedContent;\nif (typeof content === \"string\") {\n  try {\n    parsedContent = JSON.parse(content);\n  } catch (error) {\n    console.error(\"Failed to parse OpenAI response:\", content);\n    // Fallback to default content if parsing fails\n    parsedContent = {\n      twitter: \"Could not generate content for Twitter/X. #Fallback\",\n      linkedin: \"Could not generate content for LinkedIn. Please try again later.\",\n      tiktok: \"Could not generate TikTok script. #Fallback\",\n      youtube_shorts: \"Could not generate YouTube Shorts script. #Fallback\"\n    };\n  }\n} else {\n  parsedContent = content;\n}\n\n// Ensure the response has the expected fields\nif (!parsedContent.twitter || !parsedContent.linkedin || !parsedContent.tiktok || !parsedContent.youtube_shorts) {\n  console.error(\"OpenAI response missing required fields:\", parsedContent);\n  parsedContent = {\n    twitter: parsedContent.twitter || \"Could not generate content for Twitter/X. #Fallback\",\n    linkedin: parsedContent.linkedin || \"Could not generate content for LinkedIn. Please try again later.\",\n    tiktok: parsedContent.tiktok || \"Could not generate TikTok script. #Fallback\",\n    youtube_shorts: parsedContent.youtube_shorts || \"Could not generate YouTube Shorts script. #Fallback\"\n  };\n}\n\n// Return the parsed content\nreturn [{ json: parsedContent }];"}, "id": "dce26c9f-2b54-4b97-b2f9-da1d7463101f", "name": "Parse Social Media Content", "type": "n8n-nodes-base.code", "position": [3696, 1824], "typeVersion": 2}, {"parameters": {"documentId": {"__rl": true, "mode": "list", "value": "1FoFvX11IpDydACUmdZqPLPRelDK9Ca_1gaZKNqLWtu4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FoFvX11IpDydACUmdZqPLPRelDK9Ca_1gaZKNqLWtu4/edit?usp=drivesdk", "cachedResultName": "Social Trend Scores"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FoFvX11IpDydACUmdZqPLPRelDK9Ca_1gaZKNqLWtu4/edit#gid=0", "cachedResultName": "Social Trend Scores"}, "options": {}}, "id": "189359a0-694d-4c48-aa56-1c7aa6c12452", "name": "Retrieve Latest Trend Scores", "type": "n8n-nodes-base.googleSheets", "position": [2736, 1824], "typeVersion": 4.5, "credentials": {}}, {"parameters": {"jsCode": "const sheetData = items[0].json;\nconst trends = sheetData.Trend.split(\"; \");\nconst scores = sheetData.Score.split(\"; \").map(Number);\n\nconst trendScores = trends.map((trend, index) => ({\n  trend: trend,\n  score: scores[index] || 5\n}));\n\nreturn [{\n  json: {\n    trend_scores: trendScores,\n    brand_voice: sheetData.BrandVoice || \"professional\",\n    audience_mood: sheetData.AudienceMood || \"neutral\"\n  }\n}];"}, "id": "3f26a949-0831-4a50-a212-6595ac64e8c2", "name": "Parse Retrieved Trend Scores", "type": "n8n-nodes-base.code", "position": [2912, 1824], "typeVersion": 2}, {"parameters": {"resource": "image", "prompt": "=Generate two images based on the following social media scripts:\n\n- TikTok Script: {{ $json.tiktok }}\n- YouTube Shorts Script: {{ $json.youtube_shorts }}\n\nFor each script, create a vibrant, eye-catching image that matches the described scene. Return the image URLs in a JSON object:\n{\n  \"tiktok_image\": \"...\",\n  \"youtube_shorts_image\": \"...\"\n}", "options": {"returnImageUrls": false}}, "id": "c6aac9d9-64e2-4820-a22d-405f1295407a", "name": "Generate Images", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3968, 1824], "typeVersion": 1.8, "credentials": {}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": "1FoFvX11IpDydACUmdZqPLPRelDK9Ca_1gaZKNqLWtu4", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FoFvX11IpDydACUmdZqPLPRelDK9Ca_1gaZKNqLWtu4/edit?usp=drivesdk", "cachedResultName": "Social Trend Scores"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1FoFvX11IpDydACUmdZqPLPRelDK9Ca_1gaZKNqLWtu4/edit#gid=0", "cachedResultName": "Social Trend Scores"}, "columns": {"value": {"Score": "={{ $json.trend_scores.map(item => item.score).join(\"; \") }}", "Trend": "={{ $json.trend_scores.map(item => item.trend).join(\"; \") }}", "Timestamp": "={{ new Date().toISOString() }}", "BrandVoice": "={{ $json.brand_voice || \"professional\" }}", "AudienceMood": "{{ $json.audience_mood || \"neutral\" }}"}, "schema": [{"id": "Timestamp", "type": "string", "display": true, "required": false, "displayName": "Timestamp", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Trend", "type": "string", "display": true, "required": false, "displayName": "Trend", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Score", "type": "string", "display": true, "required": false, "displayName": "Score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "BrandVoice", "type": "string", "display": true, "required": false, "displayName": "BrandVoice", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "AudienceMood", "type": "string", "display": true, "required": false, "displayName": "AudienceMood", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "be7785fd-3797-4b77-8309-86fd755f5686", "name": "Store Selected Trend", "type": "n8n-nodes-base.googleSheets", "position": [3888, 880], "typeVersion": 4.5, "credentials": {}}, {"parameters": {"postAs": "organization", "text": "={{ $json.linkedin }}", "additionalFields": {}}, "id": "e83ad3f9-fa0e-4de8-b47d-906a15cdb8f8", "name": "Post to LinkedIn", "type": "n8n-nodes-base.linkedIn", "position": [3968, 2112], "typeVersion": 1, "credentials": {}}, {"parameters": {"graphApiVersion": "v20.0", "node": "[your-unique-id]", "edge": "photos", "options": {}}, "id": "d379d18c-0401-4f71-8b4e-408eded9f6be", "name": "Post to Instagram", "type": "n8n-nodes-base.facebookGraphApi", "position": [3952, 1568], "typeVersion": 1, "credentials": {}}, {"parameters": {"jsCode": "// Initialize an empty array for google_trends\nlet googleTrends = [];\n\n// Check for trending_searches_days (frequency=daily)\nif (items[0]?.json?.trending_searches_days && Array.isArray(items[0].json.trending_searches_days)) {\n  const trendingSearches = items[0].json.trending_searches_days[0]?.trending_searches || [];\n  if (Array.isArray(trendingSearches)) {\n    googleTrends = trendingSearches.map(search => ({\n      query: search.query || \"Unknown Query\",\n      volume: search.traffic || 0\n    }));\n  }\n}\n// Check for realtime_searches (frequency=realtime)\nelse if (items[0]?.json?.realtime_searches && Array.isArray(items[0].json.realtime_searches)) {\n  const realtimeSearches = items[0].json.realtime_searches;\n  googleTrends = realtimeSearches.map(search => ({\n    query: search.query || \"Unknown Query\",\n    volume: search.traffic || 0\n  }));\n}\n\n// Return the mapped data as a single item, with an empty array as a fallback\nreturn [{ json: { google_trends: googleTrends } }];"}, "id": "750406c8-ff53-48d0-b6cd-96a741470fd7", "name": "Extract Google Trends2", "type": "n8n-nodes-base.code", "position": [1888, 1088], "typeVersion": 2}, {"parameters": {"assignments": {"assignments": [{"id": "4cf48199-eabf-4bb5-a4b8-2cdffcf0ccf8", "name": "google_trends", "type": "array", "value": ""}]}, "options": {}}, "id": "d4df7a09-28e5-4043-aacc-80bf7bc8bfe0", "name": "Google Trends Fallback", "type": "n8n-nodes-base.set", "position": [1888, 1280], "typeVersion": 3.4}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2e8e4480-771b-406a-85c4-28126dd62d3b", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.search_metadata.status }}", "rightValue": "Success"}]}, "options": {}}, "id": "cfb92877-4758-4428-8f22-32c627ed3eec", "name": "If Fallback", "type": "n8n-nodes-base.if", "position": [1584, 1184], "typeVersion": 2.2}, {"parameters": {"content": "# 📋 Posting to Social Media  \n*What It Does** 🚀  \nPosts content to social media and logs TikTok/YouTube for manual posting.  \n\n*Steps** 📲  \n- **Post to Platforms** 🌐: Posts to Twitter/X, LinkedIn, Instagram, Facebook.  \n- **Log TikTok/YouTube** 📜: Logs scripts for manual posting.  \n- **Store Results** 💾: Saves posting statuses in Google Sheets.", "height": 1000, "width": 1540, "color": 2}, "id": "16c49f7b-a13e-4860-9753-2a39c75dd6b3", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2608, 1312], "typeVersion": 1}, {"parameters": {"rule": {"interval": [{}]}}, "id": "0dd1f20f-5893-44b8-b2c5-20f090db30fb", "name": "Daily Trigger Idea", "type": "n8n-nodes-base.scheduleTrigger", "position": [560, 880], "typeVersion": 1.2}], "connections": {"Set Default Inputs": {"main": [[{"node": "Fetch Breaking News", "type": "main", "index": 0}, {"node": "Fetch Twitter Mentions", "type": "main", "index": 0}, {"node": "Fetch Twitter Trends", "type": "main", "index": 0}, {"node": "SERP-Google Trends", "type": "main", "index": 0}, {"node": "Posts from Reddit", "type": "main", "index": 0}]]}, "Fetch Twitter Trends": {"main": [[{"node": "Combine All Data", "type": "main", "index": 1}]]}, "Fetch Twitter Mentions": {"main": [[{"node": "Translate Tweets to English", "type": "main", "index": 0}]]}, "SERP-Google Trends": {"main": [[{"node": "If Fallback", "type": "main", "index": 0}]]}, "Fetch Breaking News": {"main": [[{"node": "Extract News Headlines", "type": "main", "index": 0}]]}, "Analyze News Sentiment": {"main": [[{"node": "Fix News Sentiment Output", "type": "main", "index": 0}]]}, "Detect Audience Mood": {"main": [[{"node": "Fix Audience Mood Output", "type": "main", "index": 0}]]}, "Translate Tweets to English": {"main": [[{"node": "Fix Translated Tweets Output", "type": "main", "index": 0}]]}, "Combine All Data": {"main": [[{"node": "Merge Items into Single Item", "type": "main", "index": 0}]]}, "Merge Items into Single Item": {"main": [[{"node": "Combine Trends and UGCc", "type": "main", "index": 0}]]}, "Fix Translated Tweets Output": {"main": [[{"node": "Detect Audience Mood", "type": "main", "index": 0}]]}, "Fix Audience Mood Output": {"main": [[{"node": "Combine All Data", "type": "main", "index": 0}]]}, "Fix News Sentiment Output": {"main": [[{"node": "Combine All Data", "type": "main", "index": 2}]]}, "Trend Relevance Scoring (Wow Factor)": {"main": [[{"node": "Parse Trend Scores", "type": "main", "index": 0}]]}, "Extract Google Trends": {"main": [[{"node": "Combine All Data", "type": "main", "index": 3}]]}, "Extract News Headlines": {"main": [[{"node": "Analyze News Sentiment", "type": "main", "index": 0}]]}, "Log Reddit Response": {"main": [[{"node": "Exctract Reddit Trends", "type": "main", "index": 0}]]}, "Posts from Reddit": {"main": [[{"node": "Log Reddit Response", "type": "main", "index": 0}]]}, "Exctract Reddit Trends": {"main": [[{"node": "Combine All Data", "type": "main", "index": 4}]]}, "Combine Trends and UGCc": {"main": [[{"node": "Prepare Trend Scoring Input", "type": "main", "index": 0}]]}, "Select Top Trends": {"main": [[{"node": "Generate Social Media Content", "type": "main", "index": 0}]]}, "Parse Trend Scores": {"main": [[{"node": "Store Selected Trend", "type": "main", "index": 0}]]}, "Prepare Trend Scoring Input": {"main": [[{"node": "Trend Relevance Scoring (Wow Factor)", "type": "main", "index": 0}]]}, "Generate Social Media Content": {"main": [[{"node": "Parse Social Media Content", "type": "main", "index": 0}]]}, "Parse Social Media Content": {"main": [[{"node": "Generate Images", "type": "main", "index": 0}, {"node": "Post to LinkedIn", "type": "main", "index": 0}, {"node": "Post to Instagram", "type": "main", "index": 0}]]}, "Retrieve Latest Trend Scores": {"main": [[{"node": "Parse Retrieved Trend Scores", "type": "main", "index": 0}]]}, "Parse Retrieved Trend Scores": {"main": [[{"node": "Select Top Trends", "type": "main", "index": 0}]]}, "Generate Images": {"main": [[]]}, "Store Selected Trend": {"main": [[{"node": "Retrieve Latest Trend Scores", "type": "main", "index": 0}]]}, "Extract Google Trends2": {"main": [[{"node": "Extract Google Trends", "type": "main", "index": 0}]]}, "Google Trends Fallback": {"main": [[{"node": "Notify Google Trends Failure", "type": "main", "index": 0}]]}, "If Fallback": {"main": [[{"node": "Extract Google Trends2", "type": "main", "index": 0}], [{"node": "Google Trends Fallback", "type": "main", "index": 0}]]}, "Daily Trigger Idea": {"main": [[{"node": "Set Default Inputs", "type": "main", "index": 0}]]}}, "pinData": {"Fetch Twitter Trends": [{"tweets": [{"id": "1910369534650831213", "url": "https://x.com/manus2bjs/status/1910369534650831213", "card": null, "lang": "ar", "text": "بايرن ميونخ إن إي سي نيميغن هوفنهايم أتلتيك بلباو\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63", "type": "tweet", "place": {}, "author": {"id": "426653635", "url": "https://x.com/manus2bjs", "name": "هلا ك&#x624;د خ&#x635;م كارد&#x64a;ال ك&#x624;بون", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "sjc", "userName": "manus2bjs", "createdAt": "Fri Dec 02 14:26:00 +0000 2011", "followers": 8, "following": 26, "isVerified": false, "mediaCount": 0, "twitterUrl": "https://twitter.com/manus2bjs", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "que pena que você não me ama !mesmo assim ainda te amo"}, "coverPicture": "https://pbs.twimg.com/profile_banners/426653635/1724931856", "isTranslator": false, "statusesCount": 127094, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1829122883542515712/bXhjklZw_normal.jpg", "favouritesCount": 0, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 16:29:24 +0000 2025", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/manus2bjs/status/1910369534650831213", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910369534650831213", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910369325044678955", "url": "https://x.com/churchkey/status/1910369325044678955", "card": null, "lang": "en", "text": "And I cancelled windsurf and manus today. Keeping cursor and using loveable for the UX.", "type": "tweet", "place": {}, "author": {"id": "267391862", "url": "https://x.com/churchkey", "name": "Churchkey🏴‍☠️🐉", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "Alpha Centauri", "userName": "churchkey", "createdAt": "Wed Mar 16 21:33:26 +0000 2011", "followers": 17913, "following": 3912, "isVerified": false, "mediaCount": 187, "twitterUrl": "https://twitter.com/churchkey", "automatedBy": null, "canMediaTag": false, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "\"In 1969, aliens landed on the moon.\" Data geek, ai consultant, community builder, info collector, Educato"}, "coverPicture": "https://pbs.twimg.com/profile_banners/267391862/1665246205", "isTranslator": false, "statusesCount": 7536, "isBlueVerified": true, "pinnedTweetIds": ["1639828783376023553"], "profilePicture": "https://pbs.twimg.com/profile_images/1647746985485561856/JHkKJ2l0_normal.jpg", "favouritesCount": 70973, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 16:28:34 +0000 2025", "likeCount": 0, "viewCount": 4, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/churchkey/status/1910369325044678955", "inReplyToId": null, "quoted_tweet": {"id": "1910342237856149516", "url": "https://x.com/minchoi/status/1910342237856149516", "card": null, "lang": "en", "text": "Less than 24 hours ago, Google dropped Firebase Studio.\n\nCursor AI, Lovable, Bolt, v0, deploy, all in the browser.\n\nFree.\n\nMinds are blown.\n\n10 wild examples: 👇", "type": "tweet", "place": {}, "author": {"id": "23113993", "url": "https://x.com/minchoi", "name": "<PERSON>", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "minchoi", "createdAt": "Fri Mar 06 20:29:30 +0000 2009", "followers": 235183, "following": 1179, "isVerified": false, "mediaCount": 6517, "twitterUrl": "https://twitter.com/minchoi", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "AI Educator. 𝕏 about AI, solutions and interesting things.  Showing how to leverage AI in practical ways for you and your business. Opinions are my own."}, "coverPicture": "https://pbs.twimg.com/profile_banners/23113993/1683435598", "isTranslator": false, "statusesCount": 41282, "isBlueVerified": true, "pinnedTweetIds": ["1910342237856149516"], "profilePicture": "https://pbs.twimg.com/profile_images/1638359113221517312/CBZaJFyA_normal.jpg", "favouritesCount": 44756, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 14:40:56 +0000 2025", "likeCount": 570, "viewCount": 71112, "quoteCount": 5, "replyCount": 27, "twitterUrl": "https://twitter.com/minchoi/status/1910342237856149516", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 54, "bookmarkCount": 898, "conversationId": "1910342237856149516", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910369325044678955", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910369027953655888", "url": "https://x.com/manus2bjs/status/1910369027953655888", "card": null, "lang": "sd", "text": "بارما موناكو ليستر سيتي\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63", "type": "tweet", "place": {}, "author": {"id": "426653635", "url": "https://x.com/manus2bjs", "name": "هلا ك&#x624;د خ&#x635;م كارد&#x64a;ال ك&#x624;بون", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "sjc", "userName": "manus2bjs", "createdAt": "Fri Dec 02 14:26:00 +0000 2011", "followers": 8, "following": 26, "isVerified": false, "mediaCount": 0, "twitterUrl": "https://twitter.com/manus2bjs", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "que pena que você não me ama !mesmo assim ainda te amo"}, "coverPicture": "https://pbs.twimg.com/profile_banners/426653635/1724931856", "isTranslator": false, "statusesCount": 127094, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1829122883542515712/bXhjklZw_normal.jpg", "favouritesCount": 0, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 16:27:23 +0000 2025", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/manus2bjs/status/1910369027953655888", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910369027953655888", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910368937704824869", "url": "https://x.com/MaxManus73/status/1910368937704824869", "card": null, "lang": "en", "text": "Pure Evil:\n\nDJT &amp; sycophants who blame this girl for \"ripping us off\" just for working hard for a living...\n\nRATHER THAN the American &amp; European Oligarchs who created:\n\n- fiat money system\n- inflation 1971-1980 (<PERSON><PERSON>)  \n- 2008 Crisis\n- 2020 COVID $ print\n- 2025 $ tariffs", "type": "tweet", "place": {}, "author": {"id": "1504515410716147719", "url": "https://x.com/MaxManus73", "name": "<PERSON>", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "MaxManus73", "createdAt": "Thu Mar 17 17:51:21 +0000 2022", "followers": 416, "following": 342, "isVerified": false, "mediaCount": 257, "twitterUrl": "https://twitter.com/MaxManus73", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "Right To Life, Property, Liberty \nNorwegian resistance fighter\nNo more Kool-Aid\nAccountability for Vaccine genocide\nand corruption\n\nLove Enemies\nFaith-Hope-Love"}, "coverPicture": "", "isTranslator": false, "statusesCount": 28917, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1504517688655290375/NLbNDSl6_normal.jpg", "favouritesCount": 105, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 16:27:02 +0000 2025", "likeCount": 0, "viewCount": 5, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/MaxManus73/status/1910368937704824869", "inReplyToId": null, "quoted_tweet": {"id": "1910354752224858353", "url": "https://x.com/MaxManus73/status/1910354752224858353", "card": null, "lang": "en", "text": "I&apos;m announcing tariffs against Starbucks and Whole Foods and Microsoft.\n\nThey have stubbornly refused to buy my software services.\n\nMy family can no longer subsidize these 3 companies.\n\nThey can no longer continue to \"rip off\" my family.\n\nhttps://t.co/4oPRkOq7MH", "type": "tweet", "place": {}, "author": {"id": "1504515410716147719", "url": "https://x.com/MaxManus73", "name": "<PERSON>", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "MaxManus73", "createdAt": "Thu Mar 17 17:51:21 +0000 2022", "followers": 416, "following": 342, "isVerified": false, "mediaCount": 257, "twitterUrl": "https://twitter.com/MaxManus73", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "Right To Life, Property, Liberty \nNorwegian resistance fighter\nNo more Kool-Aid\nAccountability for Vaccine genocide\nand corruption\n\nLove Enemies\nFaith-Hope-Love"}, "coverPicture": "", "isTranslator": false, "statusesCount": 28917, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1504517688655290375/NLbNDSl6_normal.jpg", "favouritesCount": 105, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"urls": [{"url": "https://t.co/4oPRkOq7MH", "indices": [239, 262], "display_url": "x.com/Kanthan2030/st…", "expanded_url": "https://x.com/Kanthan2030/status/1909762732515377234"}]}, "createdAt": "Thu Apr 10 15:30:40 +0000 2025", "likeCount": 0, "viewCount": 28, "quoteCount": 1, "replyCount": 0, "twitterUrl": "https://twitter.com/MaxManus73/status/1910354752224858353", "inReplyToId": null, "quoted_tweet": {"id": "1909762732515377234", "url": "", "card": null, "lang": "", "text": "", "type": "tweet", "place": {}, "author": {}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910354752224858353", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910368937704824869", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910368778787184924", "url": "https://x.com/Shivam1990638/status/1910368778787184924", "card": {"url": "https://t.co/UEjBH2RJar", "name": "summary_large_image", "card_platform": {"platform": {"device": {"name": "iPhone", "version": "13"}, "audience": {"name": "production"}}}, "binding_values": [{"key": "photo_image_full_size_large", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=800x419", "width": 800, "height": 419}}}, {"key": "thumbnail_image", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=280x150", "width": 280, "height": 148}}}, {"key": "description", "value": {"string_value": "<PERSON><PERSON> is a general AI agent that turns your thoughts into actions. It excels at various tasks in work and life, getting everything done while you rest."}}, {"key": "domain", "value": {"string_value": "manus.im"}}, {"key": "thumbnail_image_large", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=600x600", "width": 600, "height": 317}}}, {"key": "summary_photo_image_small", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=386x202", "width": 386, "height": 202}}}, {"key": "thumbnail_image_original", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=orig", "width": 1241, "height": 655}}}, {"key": "photo_image_full_size_small", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=386x202", "width": 386, "height": 202}}}, {"key": "summary_photo_image_large", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=800x419", "width": 800, "height": 419}}}, {"key": "thumbnail_image_small", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=144x144", "width": 144, "height": 76}}}, {"key": "thumbnail_image_x_large", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=png&name=2048x2048_2_exp", "width": 1241, "height": 655}}}, {"key": "photo_image_full_size_original", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=orig", "width": 1241, "height": 655}}}, {"key": "photo_image_full_size_alt_text", "value": {"string_value": "<PERSON><PERSON>"}}, {"key": "vanity_url", "value": {"scribe_key": "vanity_url", "string_value": "manus.im"}}, {"key": "photo_image_full_size", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=600x314", "width": 600, "height": 314}}}, {"key": "summary_photo_image_alt_text", "value": {"string_value": "<PERSON><PERSON>"}}, {"key": "thumbnail_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"red": 244, "blue": 242, "green": 244}, "percentage": 94.96}, {"rgb": {"red": 83, "blue": 78, "green": 83}, "percentage": 3.81}, {"rgb": {"red": 147, "blue": 144, "green": 146}, "percentage": 0.33}]}}}, {"key": "title", "value": {"string_value": "<PERSON><PERSON>"}}, {"key": "summary_photo_image_color", "value": {"image_color_value": {"palette": [{"rgb": {"red": 244, "blue": 242, "green": 244}, "percentage": 94.96}, {"rgb": {"red": 83, "blue": 78, "green": 83}, "percentage": 3.81}, {"rgb": {"red": 147, "blue": 144, "green": 146}, "percentage": 0.33}]}}}, {"key": "summary_photo_image_x_large", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=png&name=2048x2048_2_exp", "width": 1241, "height": 655}}}, {"key": "summary_photo_image", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=600x314", "width": 600, "height": 314}}}, {"key": "photo_image_full_size_color", "value": {"image_color_value": {"palette": [{"rgb": {"red": 244, "blue": 242, "green": 244}, "percentage": 94.96}, {"rgb": {"red": 83, "blue": 78, "green": 83}, "percentage": 3.81}, {"rgb": {"red": 147, "blue": 144, "green": 146}, "percentage": 0.33}]}}}, {"key": "photo_image_full_size_x_large", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=png&name=2048x2048_2_exp", "width": 1241, "height": 655}}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://t.co/UEjBH2RJar"}}, {"key": "summary_photo_image_original", "value": {"image_value": {"alt": "<PERSON><PERSON>", "url": "https://pbs.twimg.com/card_img/1910008482482511875/td3OI8za?format=jpg&name=orig", "width": 1241, "height": 655}}}]}, "lang": "lt", "text": "Indigo airlines stock analysis by manus ai\nhttps://t.co/UEjBH2RJar", "type": "tweet", "place": {}, "author": {"id": "1820318971624898560", "url": "https://x.com/Shivam1990638", "name": "<PERSON><PERSON>", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "Shivam1990638", "createdAt": "Mon Aug 05 04:40:55 +0000 2024", "followers": 1, "following": 31, "isVerified": false, "mediaCount": 1, "twitterUrl": "https://twitter.com/Shivam1990638", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": ""}, "coverPicture": "", "isTranslator": false, "statusesCount": 7, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://abs.twimg.com/sticky/default_profile_images/default_profile_normal.png", "favouritesCount": 1, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"urls": [{"url": "https://t.co/UEjBH2RJar", "indices": [43, 66], "display_url": "manus.im/share/CXfRg5eg…", "expanded_url": "https://manus.im/share/CXfRg5egJ24kcYTq7pAsha?replay=1"}]}, "createdAt": "Thu Apr 10 16:26:24 +0000 2025", "likeCount": 0, "viewCount": 4, "quoteCount": 0, "replyCount": 1, "twitterUrl": "https://twitter.com/Shivam1990638/status/1910368778787184924", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910368778787184924", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910368725464932363", "url": "https://x.com/joshuamanus/status/1910368725464932363", "card": null, "lang": "en", "text": "@lee_bigham @don<PERSON><PERSON> And if he actually has the proof?  Trump should be tried, right?", "type": "tweet", "place": {}, "author": {"id": "393775367", "url": "https://x.com/joshuamanus", "name": "<PERSON>", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "Tallahassee, Florida", "userName": "joshua<PERSON><PERSON>", "createdAt": "Wed Oct 19 01:57:39 +0000 2011", "followers": 136, "following": 341, "isVerified": false, "mediaCount": 52, "twitterUrl": "https://twitter.com/joshuamanus", "automatedBy": null, "canMediaTag": false, "description": "", "isAutomated": false, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/vOAQ2WwVPx", "indices": [0, 23], "display_url": "literallyanybodyelse.com", "expanded_url": "https://literallyanybodyelse.com/"}]}, "description": {}}, "description": "I only comment on what tweets pass through my For You. If you don&apos;t like it, blame <PERSON><PERSON> and the algorithm.  Both political parties suck."}, "coverPicture": "", "isTranslator": false, "statusesCount": 1750, "isBlueVerified": false, "pinnedTweetIds": ["1910130974307795321"], "profilePicture": "https://pbs.twimg.com/profile_images/1880449091949219841/HliCxsFi_normal.png", "favouritesCount": 281, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "Independent American", "id_str": "1518682747069419522", "indices": [0, 11], "screen_name": "lee_bigham"}, {"name": "<PERSON>", "id_str": "255812611", "indices": [12, 23], "screen_name": "<PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:26:11 +0000 2025", "likeCount": 0, "viewCount": 3, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/joshuamanus/status/1910368725464932363", "inReplyToId": "1910278381356138864", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910167554850783469", "inReplyToUserId": "1518682747069419522", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "lee_bigham"}, {"id": "1910368598066929748", "url": "https://x.com/ubukhary/status/1910368598066929748", "card": null, "lang": "en", "text": "@sama my gauss feature that will bring chatGPT close to Manus", "type": "tweet", "place": {"id": "00cc0d5640394308", "name": "Punjab", "country": "Pakistan", "full_name": "Punjab, Pakistan", "place_type": "admin", "country_code": "PK", "bounding_box_polygon": {"type": "Polygon", "coordinates": [[[69.328873, 27.708226], [69.328873, 34.019989], [75.382124, 34.019989], [75.382124, 27.708226], [69.328873, 27.708226]]]}}, "author": {"id": "405507631", "url": "https://x.com/ubukhary", "name": "<PERSON><PERSON>", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Sat Nov 05 12:13:31 +0000 2011", "followers": 202, "following": 1326, "isVerified": false, "mediaCount": 46, "twitterUrl": "https://twitter.com/ubukhary", "automatedBy": null, "canMediaTag": false, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "Transforming Fashion Retail | CDAIO Stylo Group"}, "coverPicture": "https://pbs.twimg.com/profile_banners/405507631/1426232273", "isTranslator": false, "statusesCount": 1336, "isBlueVerified": false, "pinnedTweetIds": ["1631598016485310464"], "profilePicture": "https://pbs.twimg.com/profile_images/1265559891135037449/XPMzXZuo_normal.jpg", "favouritesCount": 2295, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "<PERSON>", "id_str": "1605", "indices": [0, 5], "screen_name": "sama"}]}, "createdAt": "Thu Apr 10 16:25:41 +0000 2025", "likeCount": 0, "viewCount": 5, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/ubukhary/status/1910368598066929748", "inReplyToId": "1910334443690340845", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910334443690340845", "inReplyToUserId": "1605", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "sama"}, {"id": "1910368550000484406", "url": "https://x.com/vichitra_manus/status/1910368550000484406", "card": null, "lang": "mr", "text": "@mee__marathi @mybmcwardKW @mybmc @AashishShinde_ @AutonomousMH @Bulldozers1960 @ChuckDeo @ekikaranmarathi @gdeshmukh1984 @Marathi___ @Marathi_Akshay_ @memumbaikar2018 @mul_mumbaikar स्वामी समर्थ सर्कल जवळ तर पोलीस चौकी पण आहे. पोलीस चौकी इतक्या जवळ असून देखील जर पदपथावर अशी अनाधिकृत दुकाने थाटली जात असतील तर याला काय &apos;अर्थ&apos; आहे???\n@MumbaiPolice", "type": "tweet", "place": {}, "author": {"id": "1242301908758355968", "url": "https://x.com/vichitra_manus", "name": "मय<PERSON><PERSON><PERSON>श", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "हिंदुराष्ट्र", "userName": "vichitra_manus", "createdAt": "<PERSON><PERSON> Mar 24 04:07:20 +0000 2020", "followers": 236, "following": 231, "isVerified": false, "mediaCount": 602, "twitterUrl": "https://twitter.com/vichitra_manus", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": ""}, "coverPicture": "", "isTranslator": false, "statusesCount": 5646, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1747628226795421696/wi8X2dzr_normal.jpg", "favouritesCount": 28340, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "𑘦𑘲 𑘦𑘨𑘰𑘙𑘲| मी मराठी | हिंदी लादणे बंद करा 🚫", "id_str": "840067970801983488", "indices": [0, 13], "screen_name": "mee__marathi"}, {"name": "Ward KW BMC", "id_str": "1140637729065058305", "indices": [14, 26], "screen_name": "mybmcwardKW"}, {"name": "माझी Mumbai, आपली BMC", "id_str": "1200214398", "indices": [27, 33], "screen_name": "mybmc"}, {"name": "राज<PERSON><PERSON><PERSON>ा", "id_str": "1482971919771377664", "indices": [34, 49], "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_"}, {"name": "स्वायत्तमहाराष्ट्र | AutonomousMaharashtra", "id_str": "1567477420407799809", "indices": [50, 63], "screen_name": "AutonomousMH"}, {"name": "बुलडोझर - Bulldozer", "id_str": "1578629183634186240", "indices": [64, 79], "screen_name": "Bulldozers1960"}, {"name": "चक", "id_str": "1250383115467489280", "indices": [80, 89], "screen_name": "ChuckDeo"}, {"name": "मराठी एकीकरण समिती - Marathi <PERSON><PERSON><PERSON><PERSON>ti", "id_str": "808718920504877056", "indices": [90, 106], "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "गोवर्धन लताबाई सखाराम देशमुख 🚩 <PERSON><PERSON><PERSON>", "id_str": "277912153", "indices": [107, 121], "screen_name": "gdeshmukh1984"}, {"name": "योद्धा", "id_str": "138715030", "indices": [122, 133], "screen_name": "Marathi___"}, {"name": "#अभिजातमराठी", "id_str": "1244307261020704769", "indices": [134, 150], "screen_name": "Marathi_Akshay_"}, {"name": "महाराष्ट्रातमराठीचहवी(#StopHindiImposition)", "id_str": "1014891539942793216", "indices": [151, 167], "screen_name": "memumbaikar2018"}, {"name": "मूळ मुंबईकर_𑘦𑘳𑘯 -𑘦𑘳𑘽𑘤𑘃𑘎𑘨", "id_str": "3432717789", "indices": [168, 182], "screen_name": "mul_mum<PERSON><PERSON>r"}, {"name": "मुंबई पोलीस - Mumbai Police", "id_str": "4573405572", "indices": [334, 347], "screen_name": "MumbaiPolice"}]}, "createdAt": "Thu Apr 10 16:25:29 +0000 2025", "likeCount": 0, "viewCount": 5, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/vichitra_manus/status/1910368550000484406", "inReplyToId": "1909865547627372904", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1909865547627372904", "inReplyToUserId": "840067970801983488", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "mee__marathi"}, {"id": "1910368516303446316", "url": "https://x.com/manus2bjs/status/1910368516303446316", "card": null, "lang": "ar", "text": "أونيون برلين ألافيس بوافيستا\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63", "type": "tweet", "place": {}, "author": {"id": "426653635", "url": "https://x.com/manus2bjs", "name": "هلا ك&#x624;د خ&#x635;م كارد&#x64a;ال ك&#x624;بون", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "sjc", "userName": "manus2bjs", "createdAt": "Fri Dec 02 14:26:00 +0000 2011", "followers": 8, "following": 26, "isVerified": false, "mediaCount": 0, "twitterUrl": "https://twitter.com/manus2bjs", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "que pena que você não me ama !mesmo assim ainda te amo"}, "coverPicture": "https://pbs.twimg.com/profile_banners/426653635/1724931856", "isTranslator": false, "statusesCount": 127094, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1829122883542515712/bXhjklZw_normal.jpg", "favouritesCount": 0, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 16:25:21 +0000 2025", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/manus2bjs/status/1910368516303446316", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910368516303446316", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910368006573539421", "url": "https://x.com/manus2bjs/status/1910368006573539421", "card": null, "lang": "ar", "text": "استوريل أودينيزي رايو فاييكانو بورنموث يوفنتوس\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63", "type": "tweet", "place": {}, "author": {"id": "426653635", "url": "https://x.com/manus2bjs", "name": "هلا ك&#x624;د خ&#x635;م كارد&#x64a;ال ك&#x624;بون", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "sjc", "userName": "manus2bjs", "createdAt": "Fri Dec 02 14:26:00 +0000 2011", "followers": 8, "following": 26, "isVerified": false, "mediaCount": 0, "twitterUrl": "https://twitter.com/manus2bjs", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "que pena que você não me ama !mesmo assim ainda te amo"}, "coverPicture": "https://pbs.twimg.com/profile_banners/426653635/1724931856", "isTranslator": false, "statusesCount": 127094, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1829122883542515712/bXhjklZw_normal.jpg", "favouritesCount": 0, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {}, "createdAt": "Thu Apr 10 16:23:20 +0000 2025", "likeCount": 0, "viewCount": 1, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/manus2bjs/status/1910368006573539421", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910368006573539421", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}], "next_cursor": "DAADDAABCgABGoL--F6XUW0KAAIagv2UlhZAXQAIAAIAAAACCAADAAAAAAgABAAAAAAKAAUagv9qjkAnEAoABhqC_2qOP9jwAAA", "has_next_page": true}], "Fetch Twitter Mentions": [{"msg": "success", "status": "success", "tweets": [{"id": "1910373632523854064", "url": "https://x.com/flamingo3785/status/1910373632523854064", "card": null, "lang": "und", "text": "เด็กแสบของพี่แวน \n\nแกล้งตายเฉย ***************\n\nJOYOUS CELEBRATION ENGFA10 \n#อิงฟ้ามหาชน #EngfaWaraha @EWaraha", "type": "tweet", "place": {}, "author": {"id": "1416687031279788034", "url": "https://x.com/flamingo3785", "name": "亗•ᵖᵃʳᵗⁿᵉʳᶠᵒʳᵉᵛᵉʳ✿᭄ 🐶🩷🐰💚 (◍•ᴗ•◍)", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "flamingo3785", "createdAt": "Sun Jul 18 09:12:09 +0000 2021", "followers": 2712, "following": 1736, "isVerified": false, "mediaCount": 22696, "twitterUrl": "https://twitter.com/flamingo3785", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "เมนอิงฟ้า วราหะ (อิงอิง) เอ็นดูน้องชา🤍ละครเรื่องแรก#บางกอกคณิกา แม่กุหลาบ🌹 หนังเรื่องแรก#วิมานหนาม น้องโหม๋ นางเอก 100 ล้าน (ฟริ้ง=แฟนฟ้า) ปลาสวายของพี่ฟ้า🐠"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1416687031279788034/1738503584", "isTranslator": false, "statusesCount": 289291, "isBlueVerified": false, "pinnedTweetIds": ["1809512783865909257"], "profilePicture": "https://pbs.twimg.com/profile_images/1907213398405943296/HbLq1pnz_normal.jpg", "favouritesCount": 160977, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "อิงฟ้ามหาชน", "indices": [76, 88]}, {"text": "EngfaWaraha", "indices": [89, 101]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [102, 110], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:45:41 +0000 2025", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/flamingo3785/status/1910373632523854064", "inReplyToId": null, "quoted_tweet": {"id": "1910352172811960414", "url": "https://x.com/EWaraha/status/1910352172811960414", "card": null, "lang": "qme", "text": "@vanny_helsing @MGIBeyond https://t.co/c4hnaUYv8R", "type": "tweet", "place": {}, "author": {"id": "1507589527464214529", "url": "https://x.com/EWaraha", "name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Sat Mar 26 05:25:48 +0000 2022", "followers": 972908, "following": 118, "isVerified": false, "mediaCount": 2734, "twitterUrl": "https://twitter.com/EWaraha", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "Miss Grand Thailand 2022 🇹🇭 Contact for work : 098-8419789 คุณ อูด้ง ……….. 🤍"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1507589527464214529/1741064652", "isTranslator": false, "statusesCount": 12151, "isBlueVerified": true, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1898732061156126720/gID-QVIJ_normal.jpg", "favouritesCount": 90934, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "Vanny_phh", "id_str": "473614477", "indices": [0, 14], "screen_name": "vanny_helsing"}, {"name": "𝗠𝗚𝗜 𝗕𝗲𝘆𝗼𝗻𝗱", "id_str": "1617838236566712320", "indices": [15, 25], "screen_name": "MGIBeyond"}]}, "createdAt": "Thu Apr 10 15:20:25 +0000 2025", "likeCount": 2040, "viewCount": 28488, "quoteCount": 44, "replyCount": 188, "twitterUrl": "https://twitter.com/EWaraha/status/1910352172811960414", "inReplyToId": "1910323471177187761", "quoted_tweet": null, "retweetCount": 878, "bookmarkCount": 13, "conversationId": "1910319059171983675", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/c4hnaUYv8R", "type": "animated_gif", "sizes": {"large": {"h": 372, "w": 498}}, "id_str": "1910352163840376832", "indices": [26, 49], "media_key": "16_1910352163840376832", "video_info": {"variants": [{"url": "https://video.twimg.com/tweet_video/GoLvK-mawAAVddH.mp4", "bitrate": 0, "content_type": "video/mp4"}], "aspect_ratio": [83, 62]}, "display_url": "pic.twitter.com/c4hnaUYv8R", "expanded_url": "https://twitter.com/EWaraha/status/1910352172811960414/photo/1", "ext_alt_text": "Rat Playing Dead GIF", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAgoAARqC7yvpmsAACgACGoLvLgBaQF4AAA==", "result": {"id": "QXBpTWVkaWE6DAACCgABGoLvK+mawAAKAAIagu8uAFpAXgAA", "media_key": "16_1910352163840376832", "__typename": "ApiMedia"}}, "original_info": {"width": 498, "height": 372, "focus_rects": []}, "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GoLvK-mawAAVddH.jpg", "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910373632523854064", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910373552823353728", "url": "https://x.com/siddhu077943751/status/1910373552823353728", "card": null, "lang": "und", "text": "@EWaraha Yesss", "type": "tweet", "place": {}, "author": {"id": "1792862082842386432", "url": "https://x.com/siddhu077943751", "name": "siddhu", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "Kerala (india)🇮🇳", "userName": "siddhu077943751", "createdAt": "<PERSON><PERSON> May 21 10:17:17 +0000 2024", "followers": 14, "following": 67, "isVerified": false, "mediaCount": 183, "twitterUrl": "https://twitter.com/siddhu077943751", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"user_mentions": [{"name": "", "id_str": "0", "indices": [30, 38], "screen_name": "<PERSON><PERSON><PERSON>"}]}}, "description": "She/her 🏳️‍🌈\n\nLove of my life @Ewaraha 🤍"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1792862082842386432/1739943412", "isTranslator": false, "statusesCount": 35515, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1907073085771014144/xjh2ISd0_normal.jpg", "favouritesCount": 40722, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [0, 8], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:45:22 +0000 2025", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/siddhu077943751/status/1910373552823353728", "inReplyToId": "1910294573185892546", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910294573185892546", "inReplyToUserId": "1507589527464214529", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "1910373470997012762", "url": "https://x.com/nokosung/status/1910373470997012762", "card": null, "lang": "qme", "text": "@JisooandEngfa @EWaraha 🫶🏻\n\n#ENGFAxMobileSchoolByกสศ \n#อิงฟ้ามหาชน @EWaraha", "type": "tweet", "place": {}, "author": {"id": "312014634", "url": "https://x.com/nokosung", "name": "Nokosung", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "N 18°17&apos; 0&apos;&apos; / E 99°30&apos; 0&apos;&apos;", "userName": "<PERSON>kosung", "createdAt": "Mon Jun 06 13:03:01 +0000 2011", "followers": 66, "following": 65, "isVerified": false, "mediaCount": 311, "twitterUrl": "https://twitter.com/nokosung", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/ftp1LDy4qK", "indices": [0, 23], "display_url": "twitter.com/nokosung", "expanded_url": "https://twitter.com/nokosung"}]}, "description": {}}, "description": "music,fashion,sleeping,social network, Engfa"}, "coverPicture": "https://pbs.twimg.com/profile_banners/312014634/1702137844", "isTranslator": false, "statusesCount": 102492, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1883065635795853312/B-99i3Bp_normal.jpg", "favouritesCount": 56816, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [28, 52]}, {"text": "อิงฟ้ามหาชน", "indices": [54, 66]}], "user_mentions": [{"name": "Jisoo&Engfa", "id_str": "1123125318691086336", "indices": [0, 14], "screen_name": "JisooandEngfa"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [15, 23], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [67, 75], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:45:03 +0000 2025", "likeCount": 0, "viewCount": 0, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/nokosung/status/1910373470997012762", "inReplyToId": "1910339287251304737", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910339287251304737", "inReplyToUserId": "1123125318691086336", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "JisooandEngfa"}, {"id": "1910373198673437138", "url": "https://x.com/nujerry69/status/1910373198673437138", "card": null, "lang": "th", "text": "มีแต่ความน่ารักสดใสเต็มไปหมดเลย\n\n#ENGFAxMobileSchoolByกสศ\n#อิงฟ้ามหาชน #EngfaWaraha @EWaraha\n\n https://t.co/bmc8p3IIUr", "type": "tweet", "place": {}, "author": {"id": "1643929279825342464", "url": "https://x.com/nujerry69", "name": "nujerry69", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "nujerry69", "createdAt": "Thu Apr 06 10:51:22 +0000 2023", "followers": 750, "following": 706, "isVerified": false, "mediaCount": 6608, "twitterUrl": "https://twitter.com/nujerry69", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/ZFsPqk9iNY", "indices": [0, 23], "display_url": "youtube.com/@nujerry69", "expanded_url": "https://youtube.com/@nujerry69"}]}, "description": {"urls": [{"url": "https://t.co/QeDfhm7hS4", "indices": [58, 81], "display_url": "shadowban.yuzurisa.com/nujerry69", "expanded_url": "https://shadowban.yuzurisa.com/nujerry69"}], "user_mentions": [{"name": "", "id_str": "0", "indices": [17, 25], "screen_name": "nujerry"}]}}, "description": "แอดเดิมขิตไปแล้ว @nujerry                                 https://t.co/QeDfhm7hS4"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1643929279825342464/1738255929", "isTranslator": false, "statusesCount": 221081, "isBlueVerified": false, "pinnedTweetIds": ["1875211701802648020"], "profilePicture": "https://pbs.twimg.com/profile_images/1892938677472702464/KIvMadHG_normal.jpg", "favouritesCount": 72761, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [33, 57]}, {"text": "อิงฟ้ามหาชน", "indices": [58, 70]}, {"text": "EngfaWaraha", "indices": [71, 83]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [84, 92], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:43:58 +0000 2025", "likeCount": 0, "viewCount": 6, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/nujerry69/status/1910373198673437138", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910373198673437138", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/bmc8p3IIUr", "type": "video", "sizes": {"large": {"h": 1542, "w": 888}}, "id_str": "1910320723387973632", "indices": [95, 118], "media_key": "13_1910320723387973632", "video_info": {"variants": [{"url": "https://video.twimg.com/amplify_video/1910320723387973632/pl/xUFI67RBr081Cbak.m3u8?tag=14", "content_type": "application/x-mpegURL"}, {"url": "https://video.twimg.com/amplify_video/1910320723387973632/vid/avc1/320x554/BNWaLuBW6AB_q2Q0.mp4?tag=14", "bitrate": 632000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/amplify_video/1910320723387973632/vid/avc1/480x832/vXoQeedL_QR9MH6q.mp4?tag=14", "bitrate": 950000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/amplify_video/1910320723387973632/vid/avc1/720x1250/pMvIgh5cesgJCACi.mp4?tag=14", "bitrate": 2176000, "content_type": "video/mp4"}], "aspect_ratio": [148, 257], "duration_millis": 56343}, "display_url": "pic.twitter.com/bmc8p3IIUr", "expanded_url": "https://x.com/Smith95071856/status/1910321685527777442/video/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwABAoAARqC0pOcm4AAAAA=", "result": {"id": "QXBpTWVkaWE6DAAECgABGoLSk5ybgAAAAA==", "media_key": "13_1910320723387973632", "__typename": "ApiMedia"}}, "original_info": {"width": 888, "height": 1542, "focus_rects": []}, "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1910320723387973632/img/Ri4BF5o8zfT7uz_t.jpg", "source_user_id_str": "1441126451529457680", "source_status_id_str": "1910321685527777442", "additional_media_info": {"monetizable": false, "source_user": {"user_results": {"result": {"core": {"name": "<PERSON><PERSON><PERSON>", "created_at": "Thu Sep 23 19:45:33 +0000 2021", "screen_name": "Smith95071856"}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1895672365251510272/_KVI8WXg_normal.jpg"}, "banner": {"image_url": "https://pbs.twimg.com/profile_banners/1441126451529457680/1740798799"}, "privacy": {}, "rest_id": "1441126451529457680", "website": {"url": ""}, "location": {"location": ""}, "__typename": "User", "properties": {"has_extended_profile": true}, "profile_bio": {"entities": {"description": {}}, "description": ""}, "pinned_items": {}, "tweet_counts": {"tweets": 54863, "media_tweets": 6307}, "verification": {"is_blue_verified": false}, "action_counts": {"favorites_count": 74923}, "dm_permissions": {"can_dm": false}, "super_following": false, "profile_metadata": {"profile_link_color": "1DA1F2", "profile_interstitial_type": ""}, "media_permissions": {"can_media_tag": true}, "super_followed_by": false, "profile_translation": {"translator_type_enum": "None"}, "relationship_counts": {"followers": 701, "following": 896}, "super_follow_eligible": false, "notifications_settings": {}, "private_super_following": false, "exclusive_tweet_following": false, "relationship_perspectives": {}, "identity_profile_labels_highlighted_label": {}}, "rest_id": "1441126451529457680"}}}, "allow_download_status": {"allow_download": true}, "ext_media_availability": {"status": "Available"}}, {"url": "https://t.co/bmc8p3IIUr", "type": "photo", "sizes": {"large": {"h": 2048, "w": 1366}}, "id_str": "1910320723375308800", "indices": [95, 118], "features": {"all": {"tags": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "type": "user", "user_id": "1507589527464214529", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "type": "user", "user_id": "1507589527464214529", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "orig": {"faces": [{"h": 236, "w": 236, "x": 1088, "y": 240}, {"h": 478, "w": 478, "x": 400, "y": 346}]}, "large": {"faces": [{"h": 236, "w": 236, "x": 1088, "y": 240}, {"h": 478, "w": 478, "x": 400, "y": 346}]}}, "media_key": "3_1910320723375308800", "display_url": "pic.twitter.com/bmc8p3IIUr", "expanded_url": "https://x.com/Smith95071856/status/1910321685527777442/video/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAQoAARqC0pOb2kAACgACGoMCTXdbYdIAAA==", "result": {"id": "QXBpTWVkaWE6DAABCgABGoLSk5vaQAAKAAIagwJNd1th0gAA", "media_key": "3_1910320723375308800", "__typename": "ApiMedia"}}, "original_info": {"width": 1366, "height": 2048, "focus_rects": [{"h": 765, "w": 1366, "x": 0, "y": 283}, {"h": 1366, "w": 1366, "x": 0, "y": 0}, {"h": 1557, "w": 1366, "x": 0, "y": 0}, {"h": 2048, "w": 1024, "x": 153, "y": 0}, {"h": 2048, "w": 1366, "x": 0, "y": 0}]}, "media_url_https": "https://pbs.twimg.com/media/GoLSk5vaQAAPJK1.jpg", "source_user_id_str": "1441126451529457680", "source_status_id_str": "1910321685527777442", "allow_download_status": {"allow_download": true}, "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910372979730768041", "url": "https://x.com/cucumberEF/status/1910372979730768041", "card": null, "lang": "th", "text": "น้องมีฟันล่างนะคะ  แค่สั้นนิดหน่อย  ☺️\n\n#ENGFAxMobileSchoolByกสศ\n#อิงฟ้ามหาชน #EngfaWaraha @EWaraha\n https://t.co/u09bCMXbNY", "type": "tweet", "place": {}, "author": {"id": "749644538470281216", "url": "https://x.com/cucumberEF", "name": "คิวคัมเบอะ🌿เลขาคุณอาโป 😮‍💨", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "cucumberEF", "createdAt": "Sun Jul 03 16:42:40 +0000 2016", "followers": 1881, "following": 810, "isVerified": false, "mediaCount": 7766, "twitterUrl": "https://twitter.com/cucumberEF", "automatedBy": null, "canMediaTag": false, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"user_mentions": [{"name": "", "id_str": "0", "indices": [23, 31], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "description": "📌เป็นฟริ้งของคุณอิงฟ้า @EWaraha อยู่ไปอยู่มาวันดีคืนดีมีครีบกระโดดลงน้ำเป็นปลาสวายแสนอวบอั๋นแทน…เป็นได้ทั้งนั้นแหละแค่ได้อยู่ใน “รอยยิ้ม” เค้า📌 (ข้อยกเว้น 🐰)"}, "coverPicture": "https://pbs.twimg.com/profile_banners/749644538470281216/1739963751", "isTranslator": false, "statusesCount": 92416, "isBlueVerified": false, "pinnedTweetIds": ["1873188402352513137"], "profilePicture": "https://pbs.twimg.com/profile_images/1786071448005820416/SjcH3PRw_normal.jpg", "favouritesCount": 97920, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [40, 64]}, {"text": "อิงฟ้ามหาชน", "indices": [65, 77]}, {"text": "EngfaWaraha", "indices": [78, 90]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [91, 99], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:43:06 +0000 2025", "likeCount": 1, "viewCount": 11, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/cucumberEF/status/1910372979730768041", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 1, "bookmarkCount": 0, "conversationId": "1910372979730768041", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/u09bCMXbNY", "type": "video", "sizes": {"large": {"h": 1920, "w": 1080}}, "id_str": "1910293271227887617", "indices": [101, 124], "media_key": "7_1910293271227887617", "video_info": {"variants": [{"url": "https://video.twimg.com/ext_tw_video/1910293271227887617/pu/pl/-WTcZCAQGGdo4EI5.m3u8?tag=12", "content_type": "application/x-mpegURL"}, {"url": "https://video.twimg.com/ext_tw_video/1910293271227887617/pu/vid/avc1/320x568/1LoNZ-FIfTqpSwh6.mp4?tag=12", "bitrate": 632000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/ext_tw_video/1910293271227887617/pu/vid/avc1/480x852/2F4xe-kSrKtSRX1w.mp4?tag=12", "bitrate": 950000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/ext_tw_video/1910293271227887617/pu/vid/avc1/720x1280/hfovdzLzO9nTVdjX.mp4?tag=12", "bitrate": 2176000, "content_type": "video/mp4"}], "aspect_ratio": [9, 16], "duration_millis": 3041}, "display_url": "pic.twitter.com/u09bCMXbNY", "expanded_url": "https://x.com/vanitcheryl/status/1910293300231442736/video/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAwoAARqCuZvoW3ABCgACGoMCGn1bYKkAAA==", "result": {"id": "QXBpTWVkaWE6DAADCgABGoK5m+hbcAEKAAIagwIafVtgqQAA", "media_key": "7_1910293271227887617", "__typename": "ApiMedia"}}, "original_info": {"width": 1080, "height": 1920, "focus_rects": []}, "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1910293271227887617/pu/img/zUVcfnn5wSYoKc8a.jpg", "source_user_id_str": "**********", "source_status_id_str": "1910293300231442736", "additional_media_info": {"monetizable": false, "source_user": {"user_results": {"result": {"core": {"name": "มิสเชอ sᴍɪʟᴇʏ◡̈", "created_at": "Sat Dec 27 03:26:25 +0000 2014", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1613550385058447364/rtLWElct_normal.jpg"}, "banner": {"image_url": "https://pbs.twimg.com/profile_banners/**********/1657180880"}, "privacy": {}, "rest_id": "**********", "website": {"url": ""}, "location": {"location": " ᙏ̤̮ 🥕 "}, "__typename": "User", "properties": {"has_extended_profile": true}, "profile_bio": {"entities": {"description": {"user_mentions": [{"name": "", "id_str": "0", "indices": [33, 46], "screen_name": "itscharlotty"}, {"name": "", "id_str": "0", "indices": [67, 75], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "", "id_str": "0", "indices": [88, 96], "screen_name": "<PERSON><PERSON>ka<PERSON>"}]}}, "description": "Fan Account : \n\n🐰CharlotteAustin @itscharlotty โพลูก\n🐶EngfaWaraha  @EWaraha โพแฟน\n🐱Aoom @Aoomkap โพแฟน"}, "pinned_items": {"tweet_ids_str": ["1753060087441367332"]}, "tweet_counts": {"tweets": 123878, "media_tweets": 35790}, "verification": {"is_blue_verified": true}, "action_counts": {"favorites_count": 24165}, "dm_permissions": {"can_dm": true}, "super_following": false, "profile_metadata": {"profile_link_color": "F5ABB5", "profile_interstitial_type": ""}, "media_permissions": {"can_media_tag": true}, "super_followed_by": false, "profile_translation": {"translator_type_enum": "None"}, "relationship_counts": {"followers": 37184, "following": 264}, "super_follow_eligible": false, "notifications_settings": {}, "private_super_following": false, "exclusive_tweet_following": false, "relationship_perspectives": {}, "identity_profile_labels_highlighted_label": {}}, "rest_id": "**********"}}}, "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910372582785978637", "url": "https://x.com/allforeng/status/1910372582785978637", "card": null, "lang": "vi", "text": "@EWaraha Giỡn mặt =))", "type": "tweet", "place": {}, "author": {"id": "1584395994250510336", "url": "https://x.com/allforeng", "name": "❆ casey", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "allforeng", "createdAt": "Mon Oct 24 04:07:23 +0000 2022", "followers": 770, "following": 357, "isVerified": false, "mediaCount": 6582, "twitterUrl": "https://twitter.com/allforeng", "automatedBy": null, "canMediaTag": false, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"hashtags": [{"text": "อิงฟ้ามหาชน", "indices": [28, 40]}, {"text": "อิงฟ้าวราหะ", "indices": [44, 56]}, {"text": "ฟริ้งของอิงฟ้า", "indices": [59, 74]}], "user_mentions": [{"name": "", "id_str": "0", "indices": [12, 20], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "description": "||Only you (@EW<PERSON>ha) || || #อิงฟ้ามหาชน || #อิงฟ้าวราหะ ||#ฟริ้งของอิงฟ้า || 🐠 ||🇻🇳 ||"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1584395994250510336/1732766713", "isTranslator": false, "statusesCount": 97515, "isBlueVerified": false, "pinnedTweetIds": ["1903027586051068007"], "profilePicture": "https://pbs.twimg.com/profile_images/1891745308985655296/oKLEBsU3_normal.jpg", "favouritesCount": 84859, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [0, 8], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:41:31 +0000 2025", "likeCount": 0, "viewCount": 7, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/allforeng/status/1910372582785978637", "inReplyToId": "1910352172811960414", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910319059171983675", "inReplyToUserId": "1507589527464214529", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "1910372357849702712", "url": "https://x.com/Reload_xlx/status/1910372357849702712", "card": null, "lang": "th", "text": "อิงฟ้าในวัย​ 30​ คือไม่มีเวลานอน\n\n#ENGFAxMobileSchoolByกสศ\n@EWaraha https://t.co/e1PGR6C105", "type": "tweet", "place": {}, "author": {"id": "1427294593511428096", "url": "https://x.com/Reload_xlx", "name": "ᴍɪᴍᴏ_ꜰʀɪɴᴋ 🐜🐠🍒", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "Reload_xlx", "createdAt": "Mon Aug 16 15:43:16 +0000 2021", "followers": 1033, "following": 371, "isVerified": false, "mediaCount": 11293, "twitterUrl": "https://twitter.com/Reload_xlx", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"hashtags": [{"text": "อิงฟ้ามหาชน", "indices": [18, 30]}, {"text": "EngfaWaraha", "indices": [33, 45]}], "user_mentions": [{"name": "", "id_str": "0", "indices": [8, 16], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "description": "Support @EWaraha🪽\n#อิงฟ้ามหาชน​ ​#EngfaWaraha | fan account"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1427294593511428096/**********", "isTranslator": false, "statusesCount": 359842, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1892875739844005888/pDks6VPd_normal.jpg", "favouritesCount": 318854, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [34, 58]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [59, 67], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:40:37 +0000 2025", "likeCount": 2, "viewCount": 7, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/Reload_xlx/status/1910372357849702712", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 2, "bookmarkCount": 0, "conversationId": "1910372357849702712", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/e1PGR6C105", "type": "video", "sizes": {"large": {"h": 320, "w": 568}}, "id_str": "1910351025707622400", "indices": [68, 91], "media_key": "7_1910351025707622400", "video_info": {"variants": [{"url": "https://video.twimg.com/ext_tw_video/1910351025707622400/pu/pl/dmCecQtZ5pKRhLpj.m3u8?tag=12", "content_type": "application/x-mpegURL"}, {"url": "https://video.twimg.com/ext_tw_video/1910351025707622400/pu/vid/avc1/478x270/6E0_RU__kityAdUW.mp4?tag=12", "bitrate": 256000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/ext_tw_video/1910351025707622400/pu/vid/avc1/568x320/N_u5T9YMAvUqC1i3.mp4?tag=12", "bitrate": 832000, "content_type": "video/mp4"}], "aspect_ratio": [71, 40], "duration_millis": 83807}, "display_url": "pic.twitter.com/e1PGR6C105", "expanded_url": "https://x.com/EngfaThangjai/status/1910351142955172007/video/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAwoAARqC7iLrmyAACgACGoMBibJbYTgAAA==", "result": {"id": "QXBpTWVkaWE6DAADCgABGoLuIuubIAAKAAIagwGJslthOAAA", "media_key": "7_1910351025707622400", "__typename": "ApiMedia"}}, "original_info": {"width": 568, "height": 320, "focus_rects": []}, "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1910351025707622400/pu/img/cMEsIKzdSGKronjB.jpg", "source_user_id_str": "817117307415576577", "source_status_id_str": "1910351142955172007", "additional_media_info": {"monetizable": false, "source_user": {"user_results": {"result": {"core": {"name": "อิงฟ้าทั้งใจ ヅ", "created_at": "Thu Jan 05 21:15:22 +0000 2017", "screen_name": "EngfaThangjai"}, "avatar": {"image_url": "https://pbs.twimg.com/profile_images/1859981876473237504/1us3Oy91_normal.jpg"}, "banner": {"image_url": "https://pbs.twimg.com/profile_banners/817117307415576577/1743009431"}, "privacy": {}, "rest_id": "817117307415576577", "website": {"url": "https://t.co/lMv0IRsdok"}, "location": {"location": "TikTok | FB : อิงฟ้าทั้งใจ ヅ"}, "__typename": "User", "properties": {"has_extended_profile": true}, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/lMv0IRsdok", "indices": [0, 23], "display_url": "tiktok.com/@engfathangjai", "expanded_url": "https://www.tiktok.com/@engfathangjai"}]}, "description": {"hashtags": [{"text": "อิงฟ้าวราหะ", "indices": [41, 53]}, {"text": "อิงฟ้ามหาชน", "indices": [54, 66]}], "user_mentions": [{"name": "", "id_str": "0", "indices": [31, 39], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "description": "Fan Account for 𝐄𝐍𝐆𝐅𝐀 𝐖𝐀𝐑𝐀𝐇𝐀\n🤍 @EWaraha\n\n#อิงฟ้าวราหะ #อิงฟ้ามหาชน"}, "pinned_items": {"tweet_ids_str": ["1620729326063996928"]}, "tweet_counts": {"tweets": 31826, "media_tweets": 13771}, "verification": {"is_blue_verified": false}, "action_counts": {"favorites_count": 34842}, "dm_permissions": {"can_dm": false}, "super_following": false, "profile_metadata": {"profile_link_color": "1DA1F2", "profile_interstitial_type": ""}, "media_permissions": {"can_media_tag": false}, "super_followed_by": false, "profile_translation": {"translator_type_enum": "None"}, "relationship_counts": {"followers": 4995, "following": 1}, "super_follow_eligible": false, "notifications_settings": {}, "private_super_following": false, "exclusive_tweet_following": false, "relationship_perspectives": {}, "identity_profile_labels_highlighted_label": {}}, "rest_id": "817117307415576577"}}}, "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910372151905104314", "url": "https://x.com/nujerry69/status/1910372151905104314", "card": null, "lang": "th", "text": "มี้…..พี่ซันไม่ให้หนูกินมะม่วงเปี้ยว\n\n#ENGFAxMobileSchoolByกสศ\n#อิงฟ้ามหาชน #EngfaWaraha @EWaraha https://t.co/qJsbyrR7vf", "type": "tweet", "place": {}, "author": {"id": "1643929279825342464", "url": "https://x.com/nujerry69", "name": "nujerry69", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "nujerry69", "createdAt": "Thu Apr 06 10:51:22 +0000 2023", "followers": 750, "following": 706, "isVerified": false, "mediaCount": 6608, "twitterUrl": "https://twitter.com/nujerry69", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/ZFsPqk9iNY", "indices": [0, 23], "display_url": "youtube.com/@nujerry69", "expanded_url": "https://youtube.com/@nujerry69"}]}, "description": {"urls": [{"url": "https://t.co/QeDfhm7hS4", "indices": [58, 81], "display_url": "shadowban.yuzurisa.com/nujerry69", "expanded_url": "https://shadowban.yuzurisa.com/nujerry69"}], "user_mentions": [{"name": "", "id_str": "0", "indices": [17, 25], "screen_name": "nujerry"}]}}, "description": "แอดเดิมขิตไปแล้ว @nujerry                                 https://t.co/QeDfhm7hS4"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1643929279825342464/1738255929", "isTranslator": false, "statusesCount": 221081, "isBlueVerified": false, "pinnedTweetIds": ["1875211701802648020"], "profilePicture": "https://pbs.twimg.com/profile_images/1892938677472702464/KIvMadHG_normal.jpg", "favouritesCount": 72761, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [38, 62]}, {"text": "อิงฟ้ามหาชน", "indices": [63, 75]}, {"text": "EngfaWaraha", "indices": [76, 88]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [89, 97], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:39:48 +0000 2025", "likeCount": 2, "viewCount": 17, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/nujerry69/status/1910372151905104314", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 1, "bookmarkCount": 0, "conversationId": "1910372151905104314", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/qJsbyrR7vf", "type": "photo", "sizes": {"large": {"h": 582, "w": 328}}, "id_str": "1910372146507112450", "indices": [98, 121], "features": {"orig": {"faces": [{"h": 98, "w": 98, "x": 39, "y": 208}, {"h": 180, "w": 180, "x": 91, "y": 152}]}, "large": {"faces": [{"h": 98, "w": 98, "x": 39, "y": 208}, {"h": 180, "w": 180, "x": 91, "y": 152}]}}, "media_key": "3_1910372146507112450", "display_url": "pic.twitter.com/qJsbyrR7vf", "expanded_url": "https://twitter.com/nujerry69/status/1910372151905104314/photo/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAQoAARqDAVh9W2ACCgACGoMBWb8aMboAAA==", "result": {"id": "QXBpTWVkaWE6DAABCgABGoMBWH1bYAIKAAIagwFZvxoxugAA", "media_key": "3_1910372146507112450", "__typename": "ApiMedia"}}, "original_info": {"width": 328, "height": 582, "focus_rects": [{"h": 184, "w": 328, "x": 0, "y": 398}, {"h": 328, "w": 328, "x": 0, "y": 254}, {"h": 374, "w": 328, "x": 0, "y": 208}, {"h": 582, "w": 291, "x": 37, "y": 0}, {"h": 582, "w": 328, "x": 0, "y": 0}]}, "media_url_https": "https://pbs.twimg.com/media/GoMBWH1bYAIYiS-.jpg", "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910371901349724370", "url": "https://x.com/DaniWaraha/status/1910371901349724370", "card": null, "lang": "en", "text": "EngEng good night and good rest 🥰🤏🏾\n\n#อิงฟ้ามหาชน #EngfaWaraha @EWaraha https://t.co/4njIYMCITO", "type": "tweet", "place": {}, "author": {"id": "1646840018445508608", "url": "https://x.com/Dani<PERSON>", "name": "Waraha Sisters 🇧🇷🤍🇹🇭", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "Engfa Waraha ", "userName": "<PERSON><PERSON><PERSON><PERSON>", "createdAt": "Fri Apr 14 11:37:30 +0000 2023", "followers": 3059, "following": 244, "isVerified": false, "mediaCount": 28196, "twitterUrl": "https://twitter.com/Dani<PERSON>", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"user_mentions": [{"name": "", "id_str": "0", "indices": [131, 139], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "description": "<PERSON><PERSON> Accout 🇧🇷 Love Engfa 🤍 \n\n 1 st Runner UP Miss Grand Internacional 2022 - Miss Grand Thailand 2022 🇹🇭 - Engfa Waraha support 🤍\n@EWaraha"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1646840018445508608/1740277113", "isTranslator": false, "statusesCount": 95738, "isBlueVerified": false, "pinnedTweetIds": ["1861082638901227660"], "profilePicture": "https://pbs.twimg.com/profile_images/1892893942296793088/hh-VPeXw_normal.jpg", "favouritesCount": 64412, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "อิงฟ้ามหาชน", "indices": [37, 49]}, {"text": "EngfaWaraha", "indices": [50, 62]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [63, 71], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:38:48 +0000 2025", "likeCount": 0, "viewCount": 17, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/DaniWaraha/status/1910371901349724370", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910371901349724370", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/4njIYMCITO", "type": "photo", "sizes": {"large": {"h": 1920, "w": 1920}}, "id_str": "1910371887529517056", "indices": [72, 95], "features": {"all": {"tags": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "type": "user", "user_id": "1507589527464214529", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Waraha Sisters 🇧🇷🤍🇹🇭", "type": "user", "user_id": "1646840018445508608", "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "orig": {"faces": [{"h": 778, "w": 778, "x": 684, "y": 993}]}, "large": {"faces": [{"h": 778, "w": 778, "x": 684, "y": 993}]}}, "media_key": "3_1910371887529517056", "display_url": "pic.twitter.com/4njIYMCITO", "expanded_url": "https://twitter.com/DaniWaraha/status/1910371901349724370/photo/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAQoAARqDARwxFuAACgACGoMBH2jWgNIAAA==", "result": {"id": "QXBpTWVkaWE6DAABCgABGoMBHDEW4AAKAAIagwEfaNaA0gAA", "media_key": "3_1910371887529517056", "__typename": "ApiMedia"}}, "original_info": {"width": 1920, "height": 1920, "focus_rects": [{"h": 1075, "w": 1920, "x": 0, "y": 759}, {"h": 1920, "w": 1920, "x": 0, "y": 0}, {"h": 1920, "w": 1684, "x": 236, "y": 0}, {"h": 1920, "w": 960, "x": 720, "y": 0}, {"h": 1920, "w": 1920, "x": 0, "y": 0}]}, "media_url_https": "https://pbs.twimg.com/media/GoMBHDEW4AAOL9k.jpg", "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910371873688608798", "url": "https://x.com/MeyFang1205/status/1910371873688608798", "card": null, "lang": "th", "text": "ยิ้มจนเห็นฟัน / เปิดตาแบบโตๆ 5555555\n\nแล้วแม่กับลูกมันเหมือนจริงนะมาดูสิขอโทษพี่ฟ้าก่อนนะแต่เหมือนมาก😂😂😂😂😂😂\n \n#ENGFAxMobileSchoolByกสศ\n#อิงฟ้ามหาชน @EWaraha https://t.co/K04aA9R77l", "type": "tweet", "place": {}, "author": {"id": "1689509864677240833", "url": "https://x.com/MeyFang1205", "name": "<PERSON><PERSON>", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "MeyFang1205", "createdAt": "Thu Aug 10 05:32:04 +0000 2023", "followers": 2446, "following": 2231, "isVerified": false, "mediaCount": 14860, "twitterUrl": "https://twitter.com/MeyFang1205", "automatedBy": null, "canMediaTag": false, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"user_mentions": [{"name": "", "id_str": "0", "indices": [78, 86], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}}, "description": "𝒮𝓊𝓅𝓅𝑜𝓇𝓉 𝟣𝓈𝓉 𝑅𝓊𝓃𝓃𝑒𝓇𝒰𝒫 𝑀𝒾𝓈𝓈 𝒢𝓇𝒶𝓃𝒹 𝐼𝓃𝓉𝑒𝓇𝓃𝒶𝒸𝒾𝑜𝓃𝒶𝓁 𝟤𝟢𝟤𝟤 - 𝑀𝒾𝓈𝓈 𝒢𝓇𝒶𝓃𝒹 𝒯𝒽𝒶𝒾𝓁𝒶𝓃𝒹 𝟤𝟢𝟤𝟤 @EWaraha"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1689509864677240833/1703348083", "isTranslator": false, "statusesCount": 31973, "isBlueVerified": false, "pinnedTweetIds": ["1731823563248746879"], "profilePicture": "https://pbs.twimg.com/profile_images/1733299106372894720/3_o3L5bF_normal.jpg", "favouritesCount": 25351, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [110, 134]}, {"text": "อิงฟ้ามหาชน", "indices": [135, 147]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [148, 156], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:38:42 +0000 2025", "likeCount": 5, "viewCount": 86, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/MeyFang1205/status/1910371873688608798", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 7, "bookmarkCount": 0, "conversationId": "1910371873688608798", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/K04aA9R77l", "type": "photo", "sizes": {"large": {"h": 2048, "w": 1536}}, "id_str": "1910371863941070848", "indices": [157, 180], "features": {"orig": {"faces": [{"h": 448, "w": 448, "x": 98, "y": 210}, {"h": 544, "w": 544, "x": 64, "y": 1282}]}, "large": {"faces": [{"h": 448, "w": 448, "x": 98, "y": 210}, {"h": 544, "w": 544, "x": 64, "y": 1282}]}}, "media_key": "3_1910371863941070848", "display_url": "pic.twitter.com/K04aA9R77l", "expanded_url": "https://twitter.com/MeyFang1205/status/1910371873688608798/photo/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAQoAARqDARazG8AACgACGoMBGPgbYB4AAA==", "result": {"id": "QXBpTWVkaWE6DAABCgABGoMBFrMbwAAKAAIagwEY+BtgHgAA", "media_key": "3_1910371863941070848", "__typename": "ApiMedia"}}, "original_info": {"width": 1536, "height": 2048, "focus_rects": [{"h": 860, "w": 1536, "x": 0, "y": 30}, {"h": 1536, "w": 1536, "x": 0, "y": 0}, {"h": 1751, "w": 1536, "x": 0, "y": 0}, {"h": 2048, "w": 1024, "x": 0, "y": 0}, {"h": 2048, "w": 1536, "x": 0, "y": 0}]}, "media_url_https": "https://pbs.twimg.com/media/GoMBFrMbwAAUhaG.jpg", "allow_download_status": {"allow_download": true}, "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910371760786334144", "url": "https://x.com/flamingo3785/status/1910371760786334144", "card": null, "lang": "th", "text": "โครงการดีๆ แบบนี้ ไม่ได้มีมาบ่อยๆ \n\n“สงกรานต์นี้กลับบ้าน พาลูกหลานกลับมาเรียน” มีอาชีพ มีรายได้ มีวุฒิ \n\n#ENGFAxMobileSchoolByกสศ  \n #อิงฟ้ามหาชน #EngfaWaraha @EWaraha https://t.co/WlYbGzqbd5", "type": "tweet", "place": {}, "author": {"id": "1416687031279788034", "url": "https://x.com/flamingo3785", "name": "亗•ᵖᵃʳᵗⁿᵉʳᶠᵒʳᵉᵛᵉʳ✿᭄ 🐶🩷🐰💚 (◍•ᴗ•◍)", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "flamingo3785", "createdAt": "Sun Jul 18 09:12:09 +0000 2021", "followers": 2712, "following": 1736, "isVerified": false, "mediaCount": 22696, "twitterUrl": "https://twitter.com/flamingo3785", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "เมนอิงฟ้า วราหะ (อิงอิง) เอ็นดูน้องชา🤍ละครเรื่องแรก#บางกอกคณิกา แม่กุหลาบ🌹 หนังเรื่องแรก#วิมานหนาม น้องโหม๋ นางเอก 100 ล้าน (ฟริ้ง=แฟนฟ้า) ปลาสวายของพี่ฟ้า🐠"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1416687031279788034/1738503584", "isTranslator": false, "statusesCount": 289291, "isBlueVerified": false, "pinnedTweetIds": ["1809512783865909257"], "profilePicture": "https://pbs.twimg.com/profile_images/1907213398405943296/HbLq1pnz_normal.jpg", "favouritesCount": 160977, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [105, 129]}, {"text": "อิงฟ้ามหาชน", "indices": [133, 145]}, {"text": "EngfaWaraha", "indices": [146, 158]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [159, 167], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:38:15 +0000 2025", "likeCount": 2, "viewCount": 27, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/flamingo3785/status/1910371760786334144", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 2, "bookmarkCount": 0, "conversationId": "1910371760786334144", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/WlYbGzqbd5", "type": "photo", "sizes": {"large": {"h": 640, "w": 961}}, "id_str": "1910371754746535936", "indices": [168, 191], "features": {"orig": {"faces": [{"h": 72, "w": 72, "x": 465, "y": 192}]}, "large": {"faces": [{"h": 72, "w": 72, "x": 465, "y": 192}]}}, "media_key": "3_1910371754746535936", "display_url": "pic.twitter.com/WlYbGzqbd5", "expanded_url": "https://twitter.com/flamingo3785/status/1910371760786334144/photo/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAQoAARqDAP1Gm2AACgACGoMA/q6bYcAAAA==", "result": {"id": "QXBpTWVkaWE6DAABCgABGoMA/UabYAAKAAIagwD+rpthwAAA", "media_key": "3_1910371754746535936", "__typename": "ApiMedia"}}, "original_info": {"width": 961, "height": 640, "focus_rects": [{"h": 538, "w": 961, "x": 0, "y": 0}, {"h": 640, "w": 640, "x": 0, "y": 0}, {"h": 640, "w": 561, "x": 0, "y": 0}, {"h": 640, "w": 320, "x": 0, "y": 0}, {"h": 640, "w": 961, "x": 0, "y": 0}]}, "media_url_https": "https://pbs.twimg.com/media/GoMA_UabYAAbVg7.jpg", "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910371754809450884", "url": "https://x.com/numickey69/status/1910371754809450884", "card": null, "lang": "th", "text": "อิงฟ้ามหาชน vs 🐠ปลาสวายมหาชน\n\n#ENGFAxMobileSchoolByกสศ\n#อิงฟ้ามหาชน #EngfaWaraha @EWaraha https://t.co/09dDULKiFW", "type": "tweet", "place": {}, "author": {"id": "1793994779824627712", "url": "https://x.com/numickey69", "name": "numickey", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "numickey69", "createdAt": "Fri May 24 13:17:57 +0000 2024", "followers": 149, "following": 163, "isVerified": false, "mediaCount": 807, "twitterUrl": "https://twitter.com/numickey69", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/z6tVttX4IX", "indices": [0, 23], "display_url": "youtube.com/@nujerry69", "expanded_url": "https://youtube.com/@nujerry69"}]}, "description": {"urls": [{"url": "https://t.co/23e4mICi40", "indices": [26, 49], "display_url": "shadowban.yuzurisa.com/numickey69", "expanded_url": "https://shadowban.yuzurisa.com/numickey69"}], "user_mentions": [{"name": "", "id_str": "0", "indices": [8, 18], "screen_name": "nujerry69"}]}}, "description": "แอดหลัก @nujerry69 🍃☘️🍀🍃🪂 https://t.co/23e4mICi40"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1793994779824627712/1716661070", "isTranslator": false, "statusesCount": 16661, "isBlueVerified": false, "pinnedTweetIds": ["1884002975598981197"], "profilePicture": "https://pbs.twimg.com/profile_images/1793998341531193344/R1zxRZ5W_normal.jpg", "favouritesCount": 4263, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [30, 54]}, {"text": "อิงฟ้ามหาชน", "indices": [55, 67]}, {"text": "EngfaWaraha", "indices": [68, 80]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [81, 89], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:38:13 +0000 2025", "likeCount": 2, "viewCount": 19, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/numickey69/status/1910371754809450884", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 2, "bookmarkCount": 0, "conversationId": "1910371754809450884", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/09dDULKiFW", "type": "photo", "sizes": {"large": {"h": 1474, "w": 1179}}, "id_str": "1910371746928316416", "indices": [90, 113], "features": {"orig": {}, "large": {}}, "media_key": "3_1910371746928316416", "display_url": "pic.twitter.com/09dDULKiFW", "expanded_url": "https://twitter.com/numickey69/status/1910371754809450884/photo/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAQoAARqDAPt0mtAACgACGoMA/UpbYYQAAA==", "result": {"id": "QXBpTWVkaWE6DAABCgABGoMA+3Sa0AAKAAIagwD9SlthhAAA", "media_key": "3_1910371746928316416", "__typename": "ApiMedia"}}, "original_info": {"width": 1179, "height": 1474, "focus_rects": [{"h": 660, "w": 1179, "x": 0, "y": 75}, {"h": 1179, "w": 1179, "x": 0, "y": 0}, {"h": 1344, "w": 1179, "x": 0, "y": 0}, {"h": 1474, "w": 737, "x": 405, "y": 0}, {"h": 1474, "w": 1179, "x": 0, "y": 0}]}, "media_url_https": "https://pbs.twimg.com/media/GoMA-3Sa0AAyFrY.jpg", "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910371417209659427", "url": "https://x.com/SprhBygdwr46594/status/1910371417209659427", "card": null, "lang": "en", "text": "@EWaraha I can&apos;t answer as being your fan. I literally searched for your face first then look at others. Does that answer you question?😄❤️🙉", "type": "tweet", "place": {}, "author": {"id": "1841105996090839043", "url": "https://x.com/SprhBygdwr46594", "name": "BlueBirdy🏳️‍🌈", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "SprhBygdwr46594", "createdAt": "Tue Oct 01 13:21:10 +0000 2024", "followers": 28, "following": 59, "isVerified": false, "mediaCount": 123, "twitterUrl": "https://twitter.com/SprhBygdwr46594", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "Englot🏳️‍🌈 🇮🇱"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1841105996090839043/1739725803", "isTranslator": false, "statusesCount": 962, "isBlueVerified": false, "pinnedTweetIds": ["1869514828337061991"], "profilePicture": "https://pbs.twimg.com/profile_images/1891172237387915266/SJ-eXrAM_normal.jpg", "favouritesCount": 9210, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [0, 8], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:36:53 +0000 2025", "likeCount": 0, "viewCount": 3, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/SprhBygdwr46594/status/1910371417209659427", "inReplyToId": "1910336278987755686", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910336278987755686", "inReplyToUserId": "1507589527464214529", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "1910371295986147409", "url": "https://x.com/FightPan18/status/1910371295986147409", "card": null, "lang": "th", "text": "@EWaraha @vanny_helsing @MGIBeyond แกล้งตายไปเลยจบๆ", "type": "tweet", "place": {}, "author": {"id": "1007836157936222208", "url": "https://x.com/FightPan18", "name": "FightPan", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "FightPan18", "createdAt": "Sat Jun 16 04:03:59 +0000 2018", "followers": 38, "following": 105, "isVerified": false, "mediaCount": 209, "twitterUrl": "https://twitter.com/FightPan18", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "ติ่งได้หมดถ้าทำให้สดชื่น"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1007836157936222208/1608227428", "isTranslator": false, "statusesCount": 7394, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1875866321558376449/DxBvtVlQ_normal.jpg", "favouritesCount": 2733, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [0, 8], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Vanny_phh", "id_str": "473614477", "indices": [9, 23], "screen_name": "vanny_helsing"}, {"name": "𝗠𝗚𝗜 𝗕𝗲𝘆𝗼𝗻𝗱", "id_str": "1617838236566712320", "indices": [24, 34], "screen_name": "MGIBeyond"}]}, "createdAt": "Thu Apr 10 16:36:24 +0000 2025", "likeCount": 0, "viewCount": 7, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/FightPan18/status/1910371295986147409", "inReplyToId": "1910352172811960414", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910319059171983675", "inReplyToUserId": "1507589527464214529", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "1910371278675935473", "url": "https://x.com/lesserafcknpink/status/1910371278675935473", "card": null, "lang": "en", "text": "Good Night, Ms. @EWaraha 🩵 You inspire me everday 🫶", "type": "tweet", "place": {}, "author": {"id": "1565031119589986310", "url": "https://x.com/lesserafcknpink", "name": "BLACKPINK ET LE SSERAFIM 🩵🖤🩷", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "Abu Dhabi", "userName": "lesserafcknpink", "createdAt": "Wed Aug 31 17:38:05 +0000 2022", "followers": 47, "following": 210, "isVerified": false, "mediaCount": 704, "twitterUrl": "https://twitter.com/lesserafcknpink", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "It’s either a rant, dump or stan account, just stfu."}, "coverPicture": "https://pbs.twimg.com/profile_banners/1565031119589986310/**********", "isTranslator": false, "statusesCount": 10346, "isBlueVerified": false, "pinnedTweetIds": ["1619336921444130816"], "profilePicture": "https://pbs.twimg.com/profile_images/1820143115098857472/-OhrH5xU_normal.jpg", "favouritesCount": 132105, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [16, 24], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:36:20 +0000 2025", "likeCount": 0, "viewCount": 11, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/lesserafcknpink/status/1910371278675935473", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910371278675935473", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": null}, {"id": "1910371103954084230", "url": "https://x.com/wnphuh1378117/status/1910371103954084230", "card": null, "lang": "th", "text": "@Jiddee_mommyeng น่าภูมิใจที่สุดแล้วคะ\nแต่ ใดๆก็คือว่า มัมหมีจิตดีร่าเริงสดใสมากเลยค่ะ☺️\n\n#ENGFAxMobileSchoolByกสศ\n@EWaraha\n#อิงฟ้ามหาชน", "type": "tweet", "place": {}, "author": {"id": "1707653770514173952", "url": "https://x.com/wnphuh1378117", "name": "วันพุธ (ตี 5 )", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "wnphuh1378117", "createdAt": "Fri Sep 29 07:10:38 +0000 2023", "followers": 1143, "following": 2072, "isVerified": false, "mediaCount": 2050, "twitterUrl": "https://twitter.com/wnphuh1378117", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {"hashtags": [{"text": "ซีรีส์หยดฝนกลิ่นสนิม", "indices": [0, 21]}, {"text": "petrichorTheSeries", "indices": [27, 46]}, {"text": "อิงฟ้ามหาชน", "indices": [61, 73]}, {"text": "ชาล็อตออสติน", "indices": [95, 108]}, {"text": "อิงล็อต", "indices": [112, 120]}]}}, "description": "#ซีรีส์หยดฝนกลิ่นสนิม👩‍⚕️👮\n#petrichorTheSeries\nศิลปินที่รัก  #อิงฟ้ามหาชน\nหม่ามี๊อิงอิงที่รัก  #ชาล็อตออสติน \n  #อิงล็อต"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1707653770514173952/1730538154", "isTranslator": false, "statusesCount": 190769, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1883788252722913280/C8P6mQlr_normal.jpg", "favouritesCount": 218152, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [90, 114]}, {"text": "อิงฟ้ามหาชน", "indices": [124, 136]}], "user_mentions": [{"name": "มัมหมีจิตดีของอิงอิง👩‍🍼", "id_str": "1542472596804972544", "indices": [0, 16], "screen_name": "<PERSON>ddee_mommyeng"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [115, 123], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:35:38 +0000 2025", "likeCount": 0, "viewCount": 17, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/wnphuh1378117/status/1910371103954084230", "inReplyToId": "1910355442175463853", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910355442175463853", "inReplyToUserId": "1542472596804972544", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "<PERSON>ddee_mommyeng"}, {"id": "1910371099935989879", "url": "https://x.com/nokosung/status/1910371099935989879", "card": null, "lang": "th", "text": "@JisooandEngfa @EWaraha ส่วนผสมที่ลงตัว  \n\n#ENGFAxMobileSchoolByกสศ \n#อิงฟ้ามหาชน @EWaraha", "type": "tweet", "place": {}, "author": {"id": "312014634", "url": "https://x.com/nokosung", "name": "Nokosung", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "N 18°17&apos; 0&apos;&apos; / E 99°30&apos; 0&apos;&apos;", "userName": "<PERSON>kosung", "createdAt": "Mon Jun 06 13:03:01 +0000 2011", "followers": 66, "following": 65, "isVerified": false, "mediaCount": 311, "twitterUrl": "https://twitter.com/nokosung", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"url": {"urls": [{"url": "https://t.co/ftp1LDy4qK", "indices": [0, 23], "display_url": "twitter.com/nokosung", "expanded_url": "https://twitter.com/nokosung"}]}, "description": {}}, "description": "music,fashion,sleeping,social network, Engfa"}, "coverPicture": "https://pbs.twimg.com/profile_banners/312014634/1702137844", "isTranslator": false, "statusesCount": 102492, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1883065635795853312/B-99i3Bp_normal.jpg", "favouritesCount": 56816, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [43, 67]}, {"text": "อิงฟ้ามหาชน", "indices": [69, 81]}], "user_mentions": [{"name": "Jisoo&Engfa", "id_str": "1123125318691086336", "indices": [0, 14], "screen_name": "JisooandEngfa"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [15, 23], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [82, 90], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:35:37 +0000 2025", "likeCount": 0, "viewCount": 15, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/nokosung/status/1910371099935989879", "inReplyToId": "1910297972249932263", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910297972249932263", "inReplyToUserId": "1123125318691086336", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "JisooandEngfa"}, {"id": "1910371009813008845", "url": "https://x.com/mnxx007/status/1910371009813008845", "card": null, "lang": "th", "text": "พี่สื่อ : ฟ้าาาา เจอกันห้วยขวาง\n\nชอบความแซวกันของพี่ๆ สื่อกับอิงฟ้า แซวกันมา 3 ปีแล้วอ่ะ ทันกันหมด 5555555555555\n\n#ENGFAxMobileSchoolByกสศ\n#อิงฟ้ามหาชน #EngfaWaraha \n@EWaraha https://t.co/jMtbZiHVVN", "type": "tweet", "place": {}, "author": {"id": "1872843344906436608", "url": "https://x.com/mnxx007", "name": "<PERSON><PERSON><PERSON> (หมาน้อย)", "type": "user", "canDm": false, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "mnxx007", "createdAt": "Sat Dec 28 03:14:17 +0000 2024", "followers": 22, "following": 33, "isVerified": false, "mediaCount": 678, "twitterUrl": "https://twitter.com/mnxx007", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "Always Engfa Charlotte  • fan acc • 🐰🐶"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1872843344906436608/1735356555", "isTranslator": false, "statusesCount": 7788, "isBlueVerified": false, "pinnedTweetIds": [], "profilePicture": "https://pbs.twimg.com/profile_images/1872891358211653633/ZSrncMiq_normal.jpg", "favouritesCount": 6074, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": false, "entities": {"hashtags": [{"text": "ENGFAxMobileSchoolByกสศ", "indices": [114, 138]}, {"text": "อิงฟ้ามหาชน", "indices": [139, 151]}, {"text": "EngfaWaraha", "indices": [152, 164]}], "user_mentions": [{"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [166, 174], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:35:16 +0000 2025", "likeCount": 0, "viewCount": 19, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/mnxx007/status/1910371009813008845", "inReplyToId": null, "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910371009813008845", "inReplyToUserId": null, "retweeted_tweet": null, "extendedEntities": {"media": [{"url": "https://t.co/jMtbZiHVVN", "type": "video", "sizes": {"large": {"h": 1024, "w": 576}}, "id_str": "1910370952640421893", "indices": [175, 198], "media_key": "7_1910370952640421893", "video_info": {"variants": [{"url": "https://video.twimg.com/ext_tw_video/1910370952640421893/pu/pl/EQcHRWsmTxgchm7v.m3u8?tag=12", "content_type": "application/x-mpegURL"}, {"url": "https://video.twimg.com/ext_tw_video/1910370952640421893/pu/vid/avc1/320x568/djdbx5WoBkfqRZc2.mp4?tag=12", "bitrate": 632000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/ext_tw_video/1910370952640421893/pu/vid/avc1/480x852/kQvkcYYezpbw8gha.mp4?tag=12", "bitrate": 950000, "content_type": "video/mp4"}, {"url": "https://video.twimg.com/ext_tw_video/1910370952640421893/pu/vid/avc1/576x1024/Nb1bX997TTe0SD8n.mp4?tag=12", "bitrate": 2176000, "content_type": "video/mp4"}], "aspect_ratio": [9, 16], "duration_millis": 16300}, "display_url": "pic.twitter.com/jMtbZiHVVN", "expanded_url": "https://twitter.com/mnxx007/status/1910371009813008845/video/1", "media_results": {"id": "QXBpTWVkaWFSZXN1bHRzOgwAAwoAARqDAEKFW2AFCgACGoMAT9Ub0c0AAA==", "result": {"id": "QXBpTWVkaWE6DAADCgABGoMAQoVbYAUKAAIagwBP1RvRzQAA", "media_key": "7_1910370952640421893", "__typename": "ApiMedia"}}, "original_info": {"width": 576, "height": 1024, "focus_rects": []}, "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1910370952640421893/pu/img/JeulqTi_uSbedlmk.jpg", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}}]}, "inReplyToUsername": null}, {"id": "1910370852488831484", "url": "https://x.com/mahap13380/status/1910370852488831484", "card": null, "lang": "en", "text": "@justforfun_klr @EWaraha Hope you enjoy TH na 🤍", "type": "tweet", "place": {}, "author": {"id": "1704822078350970880", "url": "https://x.com/mahap13380", "name": "Sunday (Ant Army)", "type": "user", "canDm": true, "status": "", "entities": {"url": {}, "description": {"urls": []}}, "location": "", "userName": "mahap13380", "createdAt": "Thu Sep 21 11:38:06 +0000 2023", "followers": 3439, "following": 1146, "isVerified": false, "mediaCount": 3049, "twitterUrl": "https://twitter.com/mahap13380", "automatedBy": null, "canMediaTag": true, "description": "", "isAutomated": false, "profile_bio": {"entities": {"description": {}}, "description": "This Too Shall Pass (Fan Account) เมน: ENGFA WARAHA🤍"}, "coverPicture": "https://pbs.twimg.com/profile_banners/1704822078350970880/**********", "isTranslator": false, "statusesCount": 88332, "isBlueVerified": true, "pinnedTweetIds": ["1792563462776004812"], "profilePicture": "https://pbs.twimg.com/profile_images/1796917978732318720/wnBUlJCF_normal.jpg", "favouritesCount": 72910, "possiblySensitive": false, "fastFollowersCount": 0, "hasCustomTimelines": true, "withheldInCountries": [], "affiliatesHighlightedLabel": {}}, "source": "Twitter for iPhone", "isReply": true, "entities": {"user_mentions": [{"name": "LESLIE 🇺🇸🇵🇪", "id_str": "*********", "indices": [0, 15], "screen_name": "justforfun_klr"}, {"name": "<PERSON><PERSON><PERSON> (ตัวจริง)", "id_str": "1507589527464214529", "indices": [16, 24], "screen_name": "<PERSON><PERSON><PERSON><PERSON>"}]}, "createdAt": "Thu Apr 10 16:34:38 +0000 2025", "likeCount": 0, "viewCount": 3, "quoteCount": 0, "replyCount": 0, "twitterUrl": "https://twitter.com/mahap13380/status/1910370852488831484", "inReplyToId": "1910297354848337942", "quoted_tweet": null, "retweetCount": 0, "bookmarkCount": 0, "conversationId": "1910297354848337942", "inReplyToUserId": "*********", "retweeted_tweet": null, "extendedEntities": {}, "inReplyToUsername": "justforfun_klr"}], "next_cursor": "DAADDAABCgABGoMCsnrbYPAKAAIagwArM9th_AAIAAIAAAACCAADAAAAAAgABAAAAAAKAAUagwLapAAnEAoABhqDAtqj_9jwAAA", "has_next_page": true}], "Fetch Breaking News": [{"status": "ok", "articles": [{"url": "https://www.polygon.com/nintendo-switch-2/556379/us-console-supply-stockpile-vietnam-tariffs", "title": "Switch 2 consoles made in Vietnam earmarked for the U.S. - Polygon", "author": "<PERSON>", "source": {"id": "polygon", "name": "Polygon"}, "content": "A new Bloomberg report suggests Nintendo will be able to build a stockpile of millions of consoles shipped from Vietnam to the United States ahead of Junes Nintendo Switch 2launch. Thats based off da… [+2074 chars]", "urlToImage": "https://platform.polygon.com/wp-content/uploads/sites/2/2025/03/switch_2_etk.jpg?quality=90&strip=all&crop=0%2C10.732984293194%2C100%2C78.534031413613&w=1200", "description": "Lots of Nintendo Switch 2 consoles are already in the United States, per a Bloomberg report.", "publishedAt": "2025-04-10T13:42:36Z"}, {"url": "https://gizmodo.com/the-google-pixel-9a-is-one-of-the-best-deals-in-smartphones-2000587378", "title": "The Google Pixel 9a Is One of the Best Deals in Smartphones - Gizmodo", "author": "Florence Ion", "source": {"id": null, "name": "Gizmodo.com"}, "content": "Every year, Google’s Pixel A-series reminds me that I did not have to pay all that cash for the Pro variant to live a good life. The Pixel 9a may have been delayed coming out of the gate, but it rema… [+9503 chars]", "urlToImage": "https://gizmodo.com/app/uploads/2025/04/Google-Pixel-9a-Hero.jpg", "description": "With affordable phones like this, who needs flagships? The Pixel 9a is all the essentials (and AI) packed into a friendlier price point.", "publishedAt": "2025-04-10T13:01:44Z"}, {"url": "https://www.theverge.com/news/645883/the-flipper-zero-creators-have-a-new-tool-to-fight-work-distractions", "title": "The Busy Bar pixel display can help deter work distractions - The Verge", "author": "<PERSON>", "source": {"id": "the-verge", "name": "The Verge"}, "content": "The Busy Bar display lets co-workers know when youre busy and dont have time to chat.\r\nThe Busy Bar display lets co-workers know when youre busy and dont have time to chat.\r\nThe creators of the Flipp… [+3321 chars]", "urlToImage": "https://platform.theverge.com/wp-content/uploads/sites/2/2025/04/busybar1.jpg?quality=90&strip=all&crop=0%2C10.732984293194%2C100%2C78.534031413613&w=1200", "description": "The creators of the Flipper Zero have a new productivity device that can help keep you focused while deterring distracting co-workers.", "publishedAt": "2025-04-10T13:00:00Z"}, {"url": "https://www.theverge.com/news/646397/oppo-find-x8-ultra-china-release-thin-camera-battery", "title": "Oppo’s Find X8 Ultra squeezes better specs into a slightly thinner phone - The Verge", "author": "<PERSON>", "source": {"id": "the-verge", "name": "The Verge"}, "content": "The new camera-centric flagship offers a big battery without any extra bulk, but its only coming out in China.\r\nThe new camera-centric flagship offers a big battery without any extra bulk, but its on… [+2194 chars]", "urlToImage": "https://platform.theverge.com/wp-content/uploads/sites/2/2025/04/Find-X8-Ultra-Ultra-Design.jpg?quality=90&strip=all&crop=0%2C10.723165084465%2C100%2C78.55366983107&w=1200", "description": "The Oppo Find X8 Ultra offers a big battery without any extra bulk, but it’s only coming out in China.", "publishedAt": "2025-04-10T12:31:50Z"}, {"url": "https://www.livescience.com/technology/virtual-reality/in-a-first-breakthrough-3d-holograms-can-be-be-touched-grabbed-and-poked", "title": "In a first, breakthrough 3D holograms can be touched, grabbed and poked - Live Science", "author": "<PERSON>", "source": {"id": null, "name": "Live Science"}, "content": "Holograms\r\n that can be physically manipulated have made their way out of science fiction\r\n and into real life thanks to a breakthrough in mixed reality technology.\r\nIn a new study uploaded March 6 t… [+3440 chars]", "urlToImage": "https://cdn.mos.cms.futurecdn.net/jZXj2zsxZ3g7ssDDRVHPoe-1200-80.png", "description": "Futuristic holograms you can manipulate have become a reality sooner than we thought, thanks to breakthrough display.", "publishedAt": "2025-04-10T12:00:10Z"}, {"url": "https://www.eventhubs.com/news/2025/apr/10/fatal-fury-2-months-characters/", "title": "Fatal Fury developers say &apos;every two months you can expect a new character&apos; and &apos;we are looking to add most [of the Fatal Fury cast]&apos; - EventHubs", "author": "<PERSON>", "source": {"id": null, "name": "EventHubs"}, "content": "Yesterday, there was a grand showcase for Fatal Fury: City of the Wolves on the Evo channel which also contained the reveal of the final launch roster character, <PERSON><PERSON><PERSON><PERSON>.\r\nDuring the showcase, the… [+3283 chars]", "urlToImage": "https://media.eventhubs.com/images/2025/04/10_dlcbannert.webp", "description": "Yesterday, there was a grand showcase for Fatal Fury: City of the Wolves on the Evo channel which also contained the reveal of the final launch roster character, <PERSON><PERSON><PERSON><PERSON>.During the showcase, there was an opportunity for producer <PERSON><PERSON><PERSON> ...", "publishedAt": "2025-04-10T11:34:23Z"}, {"url": "https://www.videogameschronicle.com/news/ubisoft-says-players-suing-over-the-crew-shutdown-shouldnt-have-expected-to-own-the-game-forever/", "title": "Ubisoft says players suing over The Crew shutdown shouldn’t have expected to own the game forever - Video Games Chronicle", "author": "<PERSON>", "source": {"id": null, "name": "Video Games Chronicle"}, "content": "Ubisoft has again argued that players buying video games shouldn’t expect to own the game forever.\r\nThis argument stems from Ubisoft‘s decision to shut down The Crew last year. In December 2023, the … [+3007 chars]", "urlToImage": "https://www.videogameschronicle.com/files/2023/12/the-crew.jpg", "description": "The Crew game was shut down last year, rendering it unplayable", "publishedAt": "2025-04-10T09:24:16Z"}, {"url": "https://www.bbc.com/future/article/20250409-game-transfer-phenomenon-the-people-who-see-video-games-in-the-physical-world", "title": "Health indicators and power ups: The &apos;freaky and unpleasant&apos; world when video games leak into the physical realm - BBC", "author": "<PERSON>", "source": {"id": null, "name": "BBC News"}, "content": "(Credit: Serenity Strull/ BBC/ Getty Images)\r\nVideo games are the biggest form of entertainment in the world, but sometimes they bleed into people&apos;s lives offline in surprising and disturbing ways.\r\n… [+14219 chars]", "urlToImage": "https://ychef.files.bbci.co.uk/624x351/p0l3dsh2.jpg", "description": "Video games are the biggest form of entertainment in the world, but sometimes they bleed into people&apos;s lives offline in surprising and disturbing ways.", "publishedAt": "2025-04-10T09:01:04Z"}, {"url": "https://www.ign.com/articles/nintendo-switch-2-exclusive-games-mitigates-the-sticker-shock-because-you-want-that-content-so-bad-ex-playstation-boss-says", "title": "Nintendo Switch 2 Exclusive Games ‘Mitigates the Sticker Shock… Because You Want That Content So Bad,’ Ex-PlayStation Boss Says - IGN", "author": "<PERSON>", "source": {"id": "ign", "name": "IGN"}, "content": "The former boss of Sony Interactive Entertainment America has commented on the backlash to Nintendos Switch 2 pricing, saying the desire for exclusive Nintendo games mitigates the sticker shock.\r\nLas… [+7465 chars]", "urlToImage": "https://assets-prd.ignimgs.com/2025/04/03/mkw-preview-blog-1743669025446.jpg?width=1280", "description": "The former boss of Sony Interactive Entertainment America has commented on the backlash to Nintendo’s Switch 2 pricing, saying the desire for exclusive Nintendo games “mitigates the sticker shock.”", "publishedAt": "2025-04-10T08:35:42Z"}, {"url": "https://www.forbes.com/sites/zakdoffman/2025/04/10/microsofts-free-upgrade-offer-for-500-million-windows-users/", "title": "Microsoft’s Free Upgrade Offer For 500 Million Windows Users - Forbes", "author": "<PERSON><PERSON>", "source": {"id": null, "name": "Forbes"}, "content": "You need to take this offer now\r\nGetty Images\r\nThere are now almost exactly six months until the end of Windows 10. Microsofts most popular OS essentially retires on October 14, with critical securit… [+3449 chars]", "urlToImage": "https://imageio.forbes.com/specials-images/imageserve/67e16f26f3e59256a03d32a7/0x0.jpg?format=jpg&crop=2041,1162,x808,y757,safe&height=900&width=1600&fit=bounds", "description": "Don&apos;t leave it until it&apos;s too late,—here&apos;s why.", "publishedAt": "2025-04-10T08:34:10Z"}, {"url": "https://www.macrumors.com/2025/04/10/apple-larger-foldable-mass-production-2026/", "title": "Apple&apos;s 18.8-Inch Foldable Device to Enter Mass Production in Late 2026 - MacRumors", "author": "<PERSON>", "source": {"id": null, "name": "MacRumors"}, "content": "(&apos;https://www.macrumors.com/2025/04/10/apple-larger-foldable-mass-production-2026/&apos;)Along with an iPhone \"Fold,\" Apple is believed to be working on a larger foldable device that&apos;s somewhere around 19… [+3192 chars]", "urlToImage": "https://images.macrumors.com/t/AOnvCF_pCHeWE4577QkVciKhznc=/2250x/article-new/2025/02/iPhone-Fold-Vertical-Feature.jpg", "description": "Along with an iPhone \"Fold,\" Apple is believed to be working on a larger foldable device that&apos;s somewhere around 19 inches, and one...", "publishedAt": "2025-04-10T08:33:09Z"}, {"url": "https://www.ign.com/articles/as-backlash-against-10-nintendo-switch-2-welcome-tour-heats-up-reggie-fils-aim-tweets-the-story-of-wii-sports-pack-in-from-ign-interview-and-everyone-knows-the-point-hes-trying-to-make", "title": "As Backlash Against $10 Nintendo Switch 2 Welcome Tour Heats Up, <PERSON>-<PERSON><PERSON> Tweets the Story of Wii Sports Pack-in From IGN Interview — and Everyone Knows the Point He’s Trying to Make - IGN", "author": "<PERSON>", "source": {"id": "ign", "name": "IGN"}, "content": "Former Nintendo of America boss <PERSON><PERSON> has pointed to the story of Wii pack-in game Wii Sports in thinly-veiled tweets addressing the controversy surrounding Nintendos decision to charge fo… [+5497 chars]", "urlToImage": "https://assets-prd.ignimgs.com/2025/04/10/reggie-1744265564531.jpg?width=1280", "description": "Former Nintendo of America boss <PERSON><PERSON> has pointed to the story of Wii pack-in game Wii Sports in thinly-veiled tweets addressing the controversy surrounding Nintendo’s decision to charge for Switch 2 tutorial game Welcome Tour.", "publishedAt": "2025-04-10T06:23:01Z"}, {"url": "https://www.vooks.net/nintendo-confirms-no-download-codes-needed-for-physical-switch-2-edition-games/", "title": "Nintendo confirms no download codes needed for physical Switch 2 Edition games - vooks.net", "author": "<PERSON>", "source": {"id": null, "name": "<PERSON><PERSON>s"}, "content": "In the week following the Nintendo Switch 2 Direct, there’s been a steady drip of news about the various features and options of the Switch 2. Aside from small bits of text on websites and box art, p… [+1885 chars]", "urlToImage": "https://www.vooks.net/img/2025/04/switch2-editions.jpg", "description": "At least ones from Nintendo.", "publishedAt": "2025-04-10T05:14:58Z"}, {"url": "https://www.nintendolife.com/news/2025/04/round-up-the-first-impressions-of-mario-kart-world-are-in", "title": "Round Up: The First Impressions Of Mario Kart World Are In - Nintendo Life", "author": "<PERSON>", "source": {"id": null, "name": "Nintendo Life"}, "content": "Image: Nintendo\r\nWe&apos;ve now seen a fair bit of Mario Kart World footage thanks to the latest Switch 2 Direct and Treehouse broadcast.\r\nIf you&apos;re still curious to know what exactly it&apos;s like though, we… [+2908 chars]", "urlToImage": "https://images.nintendolife.com/c0e00828a0517/large.jpg", "description": "The race begins this June", "publishedAt": "2025-04-10T00:30:00Z"}, {"url": "https://www.eurogamer.net/star-citizen-has-now-raised-over-800m-with-still-no-10-release-in-sight", "title": "Star Citizen has now raised over $800m, with still no release in sight - Eurogamer", "author": "<PERSON>", "source": {"id": null, "name": "Eurogamer.net"}, "content": "Behold, your irregular Star Citizen funding update is here, with the news Cloud Imperium Games&apos; controversial space sim has now raised over $800m - despite there still being no hint of a version 1.0 … [+1682 chars]", "urlToImage": "https://assetsio.gnwcdn.com/crytek-wants-to-dismiss-its-own-lawsuit-against-the-star-citizen-developer-until-squadron-42-comes-out-1578155315461.jpg?width=1200&height=630&fit=crop&enable=upscale&auto=webp", "description": "Behold, your irregular Star Citizen funding update is here, with the news Cloud Imperium Games&apos; controversial space sim…", "publishedAt": "2025-04-10T00:03:08Z"}, {"url": "https://techcrunch.com/2025/04/09/google-says-itll-embrace-anthropics-standard-for-connecting-ai-models-to-data/", "title": "Google to embrace Anthropic’s standard for connecting AI models to data - TechCrunch", "author": "<PERSON>", "source": {"id": "techcrunch", "name": "TechCrunch"}, "content": "Just a few weeks after OpenAI said it would adopt rival Anthropics standard for connecting AI models to the systems where data resides, Google is following suit. \r\nIn a post on X on Wednesday, Google… [+1001 chars]", "urlToImage": "https://techcrunch.com/wp-content/uploads/2025/03/GettyImages-2169339854.jpg?resize=1200,857", "description": "Just a few weeks after OpenAI said it would adopt rival Anthropic’s standard for connecting AI models to the systems where data resides, Google is following suit.", "publishedAt": "2025-04-09T23:18:42Z"}, {"url": "https://www.bleepingcomputer.com/news/microsoft/windows-11-tests-sharing-apps-screen-and-files-with-copilot-ai/", "title": "Windows 11 tests sharing apps screen and files with Copilot AI - BleepingComputer", "author": "<PERSON><PERSON>", "source": {"id": null, "name": "BleepingComputer"}, "content": "Copilot on Windows 11 is testing OS-level integration that would allow you to share your favourite apps&apos; screen with Copilot.\r\nThis feature is rolling out to Windows Insiders in the United States, an… [+2076 chars]", "urlToImage": "https://www.bleepstatic.com/content/hl-images/2025/02/07/Windows-11.jpg", "description": "Copilot on Windows 11 is testing OS-level integration that would allow you to share your favourite apps&apos; screen with Copilot.", "publishedAt": "2025-04-09T22:42:47Z"}, {"url": "https://www.gamingbible.com/news/platform/xbox/xbox-gamers-slam-gta-6-price-leak-668898-20250409", "title": "Xbox gamers slam GTA 6 price leak: &apos;I just won&apos;t get it&apos; - GAMINGbible", "author": "<PERSON>", "source": {"id": null, "name": "Gamingbible.com"}, "content": "Theres one topic within the games industry right now that continues to be inescapable.\r\nYes, Im talking about pricing.\r\nIt all began several months ago when analysts surfaced claiming that it wouldnt… [+1425 chars]", "urlToImage": "https://images.ladbible.com/ogimage/v3/assets/bltbc1876152fcd9f07/blt5974febcd7bbe7d1/67f66a029fffbaa48c10e395/gtaxboxno.jpg", "description": "The debate over GTA VI&apos;s potential pricing rages on, with fans unlikely to part with their hard-earned money.", "publishedAt": "2025-04-09T22:00:01Z"}, {"url": "https://www.washingtonpost.com/technology/2025/04/09/ai-em-dash-writing-punctuation-chatgpt/", "title": "Some people think AI writing has a tell — the em dash. Writers disagree. - The Washington Post", "author": "<PERSON>", "source": {"id": "the-washington-post", "name": "The Washington Post"}, "content": "Suspicious that an awkward email or stilted college essay may have been written by artificial intelligence? Some people think theres a surefire way to tell the em dash.\r\nWriters, journalists and othe… [+5358 chars]", "urlToImage": "https://www.washingtonpost.com/wp-apps/imrs.php?src=https://arc-anglerfish-washpost-prod-washpost.s3.amazonaws.com/public/BN5BV7CLIBCP7GICCSVAJ6THEQ.jpg&w=1440", "description": "Em dashes have been derided as the “ChatGPT hyphen” — a punctuation mark overused by artificial intelligence. That’s not quite true.", "publishedAt": "2025-04-09T21:49:17Z"}, {"url": "https://www.gamespot.com/articles/save-100-on-ankers-popular-140w-portable-battery-pack-with-fast-charging/1100-6530745/", "title": "Save $100 On Anker&apos;s Popular 140W Portable Battery Pack With Fast Charging - GameSpot", "author": "<PERSON>", "source": {"id": null, "name": "GameSpot"}, "content": "This power bank should be of particular interest to those with a gaming laptop or a power-hungry portable PC. Most battery packs in the $50 price range can&apos;t keep up with the power drain when gaming … [+817 chars]", "urlToImage": "https://www.gamespot.com/a/uploads/screen_kubrick/1595/********/4473746-anker.jpg", "description": "The Anker 737 Power Bank is a powerful 140W portable charger with 24,000mAh capacity, and you can snag one for only $50--but probably not for long.", "publishedAt": "2025-04-09T21:11:05Z"}], "totalResults": 56}], "Detect Audience Mood": [{"index": 0, "message": {"role": "assistant", "content": "The overall sentiment of the content is \"joyous\" or \"excited.\" The use of playful language, laughter (e.g., \"***************\"), emojis, and expressions of pride and cheerfulness all contribute to this positive mood.", "refusal": null, "annotations": []}, "logprobs": null, "finish_reason": "stop"}], "Fix News Sentiment Output": [{"news_sentiment": "Overall, the sentiment of the news headlines is **neutral**. While some headlines express more positivity (e.g., innovations from Google and Samsung, discounts, and updates on gaming content), others highlight issues such as tariffs and product pricing challenges. The collection has a mix of sentiments, with no overwhelmingly positive or negative tone throughout."}], "Trend Relevance Scoring (Wow Factor)": [{"index": 0, "message": {"role": "assistant", "content": {"trends": [{"score": 3, "trend": "بايرن ميونخ إن إي سي نيميغن هوفنهايم أتلتيك بلباو\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63"}, {"score": 4, "trend": "And I cancelled windsurf and manus today. Keeping cursor and using loveable for the UX."}, {"score": 3, "trend": "بارما موناكو ليستر سيتي\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63"}, {"score": 2, "trend": "Pure Evil:\n\nDJT &amp; sycophants who blame this girl for \"ripping us off\" just for working hard for a living...\n\nRATHER THAN the American &amp; European Oligarchs who created:\n\n- fiat money system\n- inflation 1971-1980 (<PERSON><PERSON>)  \n- 2008 Crisis\n- 2020 COVID $ print\n- 2025 $ tariffs"}, {"score": 6, "trend": "Indigo airlines stock analysis by manus ai\nhttps://t.co/UEjBH2RJar"}, {"score": 5, "trend": "@lee_bigham @don<PERSON><PERSON> And if he actually has the proof?  Trump should be tried, right?"}, {"score": 6, "trend": "@sama my gauss feature that will bring chatGPT close to Manus"}, {"score": 4, "trend": "@mee__marathi @mybmcwardKW @mybmc @AashishShinde_ @AutonomousMH @Bulldozers1960 @ChuckDeo @ekikaranmarathi @gdeshmukh1984 @Marathi___ @Marathi_Akshay_ @memumbaikar2018 @mul_mumbaikar स्वामी समर्थ सर्कल जवळ तर पोलीस चौकी पण आहे. पोलीस चौकी इतक्या जवळ असून देखील जर पदपथावर अशी अनाधिकृत दुकाने थाटली जात असतील तर याला काय &apos;अर्थ&apos; आहे???\n@MumbaiPolice"}, {"score": 3, "trend": "أونيون برلين ألافيس بوافيستا\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63"}, {"score": 3, "trend": "استوريل أودينيزي رايو फاييκανो بورنموث يوفنتوس\n\nڪۆد خضم مباشر\n\n▬ڪاردىال▬ ← M63"}, {"score": 7, "trend": "Trump&apos;s tariffs force laptop makers like Dell and Lenovo to halt US shipments | The supply chain is in shambles, and technology companies are trying to adapt"}, {"score": 7, "trend": "Amazon CEO <PERSON> says he believes sellers will pass increased tariff costs on to consumers"}, {"score": 6, "trend": "Big Tech’s bet on <PERSON> has blown up in their face. No one is surprised but them"}]}, "refusal": null, "annotations": []}, "logprobs": null, "finish_reason": "stop"}], "Exctract Reddit Trends": [{"reddit_trends": [{"score": 2118, "title": "Trump&apos;s tariffs force laptop makers like Dell and Lenovo to halt US shipments | The supply chain is in shambles, and technology companies are trying to adapt"}, {"score": 10750, "title": "Amazon CEO <PERSON> says he believes sellers will pass increased tariff costs on to consumers"}, {"score": 30363, "title": "Big Tech’s bet on <PERSON> has blown up in their face. No one is surprised but them"}]}], "Generate Social Media Content": [{"index": 0, "message": {"role": "assistant", "content": "{\n  \"twitter\": \"Exciting news for fans of Union Berlin, Deportivo Alavés, and Boavista! 🎉 Use code \\\"M63\\\" to get exclusive discounts on merchandise! Show your team pride today! 🏆 #UnionBerlin #DeportivoAlaves #Boavista #SportsDiscount #M63\",\n  \"linkedin\": \"Attention sports enthusiasts! We&apos;re thrilled to announce a special promotional campaign featuring Union Berlin, Deportivo Alavés, and Boavista. As a token of appreciation for our dedicated fans, use discount code &apos;M63&apos; to enjoy exclusive savings on official merchandise. Celebrate your passion for the game and support your favorite clubs with pride. Join us in this exciting campaign and elevate your fan experience! #UnionBerlin #DeportivoAlaves #Boavista #SportsPromotion #M63\",\n  \"tiktok\": \"🎉 Exciting news for football fans! We’re teaming up with Union Berlin, Deportivo Alavés, and Boavista for an exclusive promotion! 🏆 Use code M63 to snag a fantastic discount on merch! 💥 Show off your fan pride and get in on the action today! #FootballFever #M63 #UnionBerlin #DeportivoAlaves #Boavista #DiscountDeal\",\n  \"youtube_shorts\": \"🚨 Attention, football fans! Big news! We’re excited to announce a special promotional campaign featuring Union Berlin, Deportivo Alavés, and Boavista! 🌟 Get ready to support your teams like never before! Use code &apos;M63&apos; for exclusive discounts on merchandise! 🎽 Don’t miss out on this chance to represent your club! Get yours today and show your pride! #SportsPromo #M63 #UnionBerlin #DeportivoAlaves #Boavista\"\n}", "refusal": null, "annotations": []}, "logprobs": null, "finish_reason": "stop"}]}, "meta": {"instanceId": "2f281fde0de64f39316b38bf7aeff647de66c777fd2c9178aac0dbc0cd948eca", "templateCredsSetupCompleted": true}}