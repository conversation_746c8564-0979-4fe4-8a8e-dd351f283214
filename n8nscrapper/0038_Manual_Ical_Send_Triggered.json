{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [350, 200], "parameters": {}, "typeVersion": 1}, {"name": "iCalendar", "type": "n8n-nodes-base.iCal", "position": [550, 200], "parameters": {"end": "2021-06-11T16:15:00.000Z", "start": "2021-06-11T15:30:00.000Z", "title": "n8n Community Meetup", "additionalFields": {}}, "typeVersion": 1}, {"name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [750, 200], "parameters": {"text": "Hey <PERSON><PERSON><PERSON>,\n\nWe are excited to invite you to the n8n community meetup!\n\nWith this email you will find the invite attached.\n\nLooking forward to seeing you at the meetup!\n\nCheers,\n<PERSON><PERSON><PERSON>", "options": {}, "subject": "n8n Community Meetup 🚀", "attachments": "data"}, "credentials": {"smtp": "Outlook Burner Credentials"}, "typeVersion": 1}], "connections": {"iCalendar": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "iCalendar", "type": "main", "index": 0}]]}}}