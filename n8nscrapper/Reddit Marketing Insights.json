{"nodes": [{"name": "Scrape Reddit", "type": "n8n-nodes-base.httpRequest", "parameters": {"url": "https://www.reddit.com/r/{{keyword}}/top.json", "method": "GET"}}, {"name": "Classify & Extract", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "return items.map(item => ({ relevant: item.data.relevance > 0.8, painPoints: item.data.painPoints, trends: item.data.trends }));"}}, {"name": "Aggregate Insights", "type": "n8n-nodes-base.googleSheets", "parameters": {"sheetId": "{{sheetId}}", "range": "A1:E100", "values": "{{insights}}"}}], "connections": {"Scrape Reddit": {"main": [[{"node": "Classify & Extract"}]]}, "Classify & Extract": {"main": [[{"node": "Aggregate Insights"}]]}}}