{"name": "Miniature shorts", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-180, -20], "id": "1ad7fa8f-a1a6-441d-8cc9-4e98c21cfaf2", "name": "Schedule Trigger"}, {"parameters": {"promptType": "define", "text": "=Respond to the system message below", "hasOutputParser": true, "options": {"systemMessage": "You are a creative director specializing in fun, single-scene miniature video animation prompts for short-form content on platforms like TikTok and YouTube Shorts.\n\nYour job is to generate:\n\nA visual storytelling prompt for a 30–60 second vertical video in 9:16 format\n\nA matching image generation prompt for use with AI image models (e.g., DALL·E, Midjourney) to visualize or storyboard the scene in vertical 9:16 layout format\n\nEach animation should feature:\n\nA single, self-contained scene in a whimsical, miniature diorama world (e.g., office desk, kitchen counter, garden bench)\n\nTiny characters or objects with personality (e.g., people, robots, animals, anthropomorphic tools)\n\nA clear story arc or emotional beat: tension, twist, humor, irony, or satisfaction\n\nA visual hook in the first 3 seconds to grab viewer attention\n\nPlayful interaction with oversized props that exaggerate familiar real-world moments\n\nOptional sound/music cue suggestions to enhance mood, timing, or comedy\n\nThe animation should be dialogue-free and rely purely on action, visuals, and mood to tell the story.\nThe visual layout must be vertical (9:16) to match TikTok and YouTube Shorts formatting and include this specific layout in the prompt.\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [40, -20], "id": "109c1a32-6339-4224-baf9-43d004476f08", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-20, 200], "id": "757268e1-351e-432c-83e1-8d4b58b4e274", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "AxcwYLkUt9aueZ9i", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.timestamp }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [100, 200], "id": "6c741985-7132-4147-ba6f-e15c40875ab0", "name": "Simple Memory"}, {"parameters": {"jsonSchemaExample": "{\n  \"title\": \"String - catchy, short name for the animation\",\n  \"concept_summary\": \"String - 1–2 sentence overview of the story or hook\",\n  \"scene_description\": \"String - vivid breakdown of the miniature setting, characters, props, and key action\",\n  \"sound_music_suggestion\": \"String - optional description of background music or sound effects to match the tone\",\n  \"video_format\": \"9:16 vertical layout for TikTok & YouTube Shorts\",\n  \"image_generation_prompt\": \"String - detailed, imaginative description of the scene suitable for an AI image model. Must include that the image is in 'vertical 9:16 portrait format'.\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [320, 200], "id": "1735254d-d254-47e0-95f8-9d1cb6c35b1e", "name": "Structured Output Parser"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": ""}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.image_generation_prompt }}\"\n    }\n  ],\n  \"parameters\": {\n    \"sampleCount\": 1\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [700, -20], "id": "d6b8991c-aaf4-4386-a0a3-10a67f619629", "name": "HTTP Request"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {"fileName": "Image"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1000, -20], "id": "8643a648-bf59-4802-8544-0aad201cdaa6", "name": "Convert to File"}, {"parameters": {"name": "={{ $('AI Agent').item.json.output.title }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1rgL9zyjRC15kzu2tK5bC3oO8kFly8BMZ", "mode": "list", "cachedResultName": "images", "cachedResultUrl": ""}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1400, -20], "id": "d4fdd109-7bb9-4003-868d-710ef85f15ce", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"operation": "share", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "permissionsUi": {"permissionsValues": {"role": "reader", "type": "anyone", "allowFileDiscovery": true}}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1460, 240], "id": "df1760b8-b9c8-449a-8d06-46beeadefd04", "name": "Google Drive1", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1460, 720], "id": "fbae398b-9ed7-4b6c-a1a5-30f49fe6b4ab", "name": "Wait", "webhookId": "85445dc9-7ea5-44c9-9b6e-d4dd27675df2"}, {"parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}, {"name": "X-Runway-Version", "value": "2024-11-06"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-780, 540], "id": "d5d91e63-aecb-45e3-abd9-b3b9f344d7da", "name": "Get details of generated video"}, {"parameters": {"method": "POST", "url": "https://api.dev.runwayml.com/v1/image_to_video", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer "}, {"name": "X-Runway-Version", "value": "2024-11-06"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $('Google Drive').item.json.webContentLink }}"}, {"name": "seed", "value": "4294967295"}, {"name": "model", "value": "gen3a_turbo"}, {"name": "promptText", "value": "={{ $('AI Agent').item.json.output.scene_description }}"}, {"name": "duration", "value": "5"}, {"name": "ratio", "value": "768:1280"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1460, 480], "id": "c48aaf9b-c989-4ecb-85e0-b1e30f4b57b0", "name": "Generate videos 1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ab70af23-8e0e-42d9-b1d5-24ea6468e467", "leftValue": "={{ $json.status }}", "rightValue": "SUCCEEDED", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-500, 540], "id": "5d344d4a-8f51-483e-9c8f-582c3c23d6be", "name": "If"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-280, 680], "id": "d1367d71-7a26-4703-b891-d4ccb78650c4", "name": "Wait1", "webhookId": "d74fc8a7-0161-4c34-ba87-d299d80e13ab"}, {"parameters": {"url": "={{ $json.output[0] }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "videofile"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-280, 520], "id": "24413981-5a10-49fd-bac7-6e795ad12eaa", "name": "HTTP Request1"}, {"parameters": {"inputDataFieldName": "videofile", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "182m9LSD5HgMrROgNM-6mEx78s0M7Xg-n", "mode": "list", "cachedResultName": "Video", "cachedResultUrl": ""}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-120, 520], "id": "20fb3af7-f4dc-419e-a59b-d99f2c18c005", "name": "Google Drive2", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('AI Agent').item.json.output.title }}", "regionCode": "CA", "categoryId": "24", "options": {"privacyStatus": "unlisted"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1100, 500], "id": "a01f8b27-fdaa-4177-af7a-750201873a56", "name": "YouTube", "credentials": {"youTubeOAuth2Api": {"id": "jnkGw3zbfMMg9MCG", "name": "YouTube account AI With Lenny"}}}, {"parameters": {"operation": "sendAndWait", "sendTo": "<EMAIL>", "subject": "Approval required", "message": "=New short ready for posting on Youtube, here is the scene description:{{ $('AI Agent').item.json.output.scene_description }}\nClick this link to view your video and provide approval before it's posted to your youtube channel:{{ $('Google Drive2').item.json.webViewLink }}", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [220, 520], "id": "8e49849a-ab11-412b-a3ff-957646f18e00", "name": "Gmail", "webhookId": "247199f0-a6e2-4fc3-a40e-e54d2f32ac8a", "credentials": {"gmailOAuth2": {"id": "u2GrwzDNu3KRPkqV", "name": "Gmail account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Google Drive2').item.json.id }}", "mode": "id"}, "options": {"binaryPropertyName": "data"}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [700, 500], "id": "2aada7f4-f82e-4fa4-a717-642235d4304c", "name": "Google Drive3", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"operation": "share", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "permissionsUi": {"permissionsValues": {"role": "writer", "type": "anyone", "allowFileDiscovery": true}}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [40, 520], "id": "3be03ea1-391a-43e2-9949-b1167cce175a", "name": "Google Drive4", "credentials": {"googleDriveOAuth2Api": {"id": "fqOGRBJdLg8sjUZi", "name": "Google Drive account 3"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f4e573d0-b5bc-4c5d-8464-d645e46a17c9", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [400, 520], "id": "57c7a1b3-355b-4e46-a066-f8275ba535ea", "name": "If1"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [500, 680], "id": "ff12798a-fd5f-4bf0-95f7-095178e9c674", "name": "No Operation, do nothing"}, {"parameters": {"content": "Gnerate video elements", "height": 540, "width": 520}, "type": "n8n-nodes-base.stickyNote", "position": [-40, -200], "typeVersion": 1, "id": "e1dba260-75a4-4837-a63b-c2079b126d49", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "Generate Image", "height": 520, "width": 580, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [620, -200], "typeVersion": 1, "id": "351fe21a-2b05-4838-8bc9-a61ad47dd19b", "name": "Sticky Note1"}, {"parameters": {"content": "Generate videos", "height": 440, "width": 380, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [1360, 360], "typeVersion": 1, "id": "a5faf6a8-d858-4c9a-b4a4-a85b44d04b06", "name": "Sticky Note2"}, {"parameters": {"content": "Post to youtube", "height": 360, "width": 1360, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-160, 440], "typeVersion": 1, "id": "255c0cc4-1d97-4faa-af1f-c377654629f0", "name": "Sticky Note3"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Google Drive1", "type": "main", "index": 0}]]}, "Google Drive1": {"main": [[{"node": "Generate videos 1", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Get details of generated video", "type": "main", "index": 0}]]}, "Generate videos 1": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Get details of generated video": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Google Drive2", "type": "main", "index": 0}]]}, "Google Drive2": {"main": [[{"node": "Google Drive4", "type": "main", "index": 0}]]}, "YouTube": {"main": [[]]}, "Gmail": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Google Drive3": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "Google Drive4": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Google Drive3", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d902ab10-ee97-45dc-b928-3fca270c2f87", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f21f53385ae396fcb0c1b69950e1ec16f2dcc4ddca34a170466835249ec1c42c"}, "id": "bbijmMUyZjWJn0MO", "tags": []}