{"name": "Airtable AI Agent", "nodes": [{"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [880, 300], "id": "0f4514ad-24f4-4bdc-8155-50b143f34b12", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "AMRESj6OfKA1MY3v", "name": "OpenAi Brady"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('When chat message received')?.item.json.sessionId || $('Webhook Trigger')?.item.json.session_id || 'default' }}", "contextWindowLength": 16}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [1000, 300], "id": "91dcab2b-5b1e-47b3-8568-346f49e1f466", "name": "Window Buffer Memory"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [1120, 300], "id": "ae1b0986-341f-483c-ac72-d25c1fb61b33", "name": "Calculator"}, {"parameters": {"options": {"timezone": "America/New_York"}}, "type": "n8n-nodes-base.dateTimeTool", "typeVersion": 2, "position": [1240, 300], "id": "01f1e872-8a73-4b36-9bd5-ecd0a4f726dc", "name": "Get Current Date"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [980, -40], "id": "6f8a7632-e3a2-42d7-acd2-7a3587279474", "name": "When chat message received", "webhookId": "666d19f2-23af-4456-b883-93918a3771d1"}, {"parameters": {"httpMethod": "POST", "path": "airtable-agent", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [980, 120], "id": "f759a5e9-a648-4ed1-a1e8-44f5e1e895c6", "name": "Webhook Trigger", "webhookId": "fda43e9a-1b69-499f-9fb9-799448b08ed7"}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"response\": $json.output, \"status\": \"success\" } }}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1780, 80], "id": "1c857b4b-1972-4783-8c78-2d256c81448b", "name": "Respond to Webhook"}, {"parameters": {"content": "## 🎯 Airtable AI Agent Setup\n\n**Required Steps:**\n1. Add your OpenAI API credentials\n2. Add your Airtable credentials to all Airtable nodes\n3. Configure your base ID and table name in each Airtable tool\n4. Test the webhook endpoint to verify functionality\n\n**Usage:**\nSend POST requests to the webhook with:\n```json\n{\n  \"message\": \"Your query here\",\n  \"session_id\": \"unique_session_id\"\n}\n```", "height": 420, "width": 360, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [520, -380], "typeVersion": 1, "id": "0255c70c-b9b4-429d-a5ea-16c318f587ca", "name": "Setup Instructions"}, {"parameters": {"content": "## 🔍 Available Operations\n\n**READ Operations:**\n- List all records from tables\n- Search records by field values\n- Get specific records by ID\n- Analyze data patterns and trends\n\n**WRITE Operations:**\n- Create new records\n- Update existing records\n- Delete records (use with caution)\n\n**ANALYSIS Features:**\n- Statistical analysis of numeric fields\n- Data visualization insights\n- Relationship analysis between tables", "height": 300, "width": 340, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [900, -380], "typeVersion": 1, "id": "2a438e36-ee99-401b-826b-a0b2d12ff392", "name": "Agent Capabilities"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "app934583498345", "mode": "id"}, "table": {"__rl": true, "value": "tbl90209230", "mode": "id"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2, "position": [1480, 300], "id": "81af07c3-a32b-4e14-be34-d06672bfe067", "name": "Airtable - Create Record", "credentials": {"airtableTokenApi": {"id": "nm0gQ2lklGckqLn0", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "app934583498345", "mode": "id"}, "table": {"__rl": true, "value": "tbl90209230", "mode": "id"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2, "position": [1600, 300], "id": "621e59ac-b21d-4da8-9741-d45ff6f9d181", "name": "Airtable - Update Record", "credentials": {"airtableTokenApi": {"id": "nm0gQ2lklGckqLn0", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "delete", "base": {"__rl": true, "value": "app934583498345", "mode": "id"}, "table": {"__rl": true, "value": "tbl90209230", "mode": "id"}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2, "position": [1720, 300], "id": "45bac5c3-9cc4-465a-a9b0-fdc1d739688b", "name": "Airtable - Delete Record", "credentials": {"airtableTokenApi": {"id": "nm0gQ2lklGckqLn0", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"base": {"__rl": true, "value": "app934583498345", "mode": "id"}, "table": {"__rl": true, "value": "tbl90209230", "mode": "id"}, "id": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Record_ID', '', 'string') }}", "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2, "position": [1840, 300], "id": "ea3e7978-7ad2-4a1b-ab61-f674c8c515b0", "name": "Airtable - Get Record", "credentials": {"airtableTokenApi": {"id": "nm0gQ2lklGckqLn0", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.message || $json.chatInput }}", "options": {"systemMessage": "=## Agent Identity\nYou are the **Airtable Data Agent**, a specialized AI assistant for managing and analyzing data in Airtable bases. You excel at CRUD operations, data analysis, and providing insights from structured data.\n\n## Available Tools\n\n### Tool 1: Airtable - List All or Search Records\n**Function**: `search_records`\n**Purpose**: Retrieve multiple records from specified table with filtering capabilities\n**Key Parameters**:\n- `limit`: Limit number of records returned (default: 100, max: 100)\n- `filterByFormula`: Airtable formula for filtering records\n\n**Use Cases**:\n- Get all records from a table\n- Filter records by specific criteria\n- Sort data by multiple fields\n- Paginate through large datasets\n\n### Tool 2: Airtable - Get Record\n**Function**: `get_record`\n**Purpose**: Retrieve a single record by its unique ID\n**Key Parameters**:\n- `recordId`: Unique record identifier (starts with 'rec')\n\n**Use Cases**:\n- Fetch specific record details\n- Verify record existence\n- Get complete field data for analysis\n\n### Tool 3: Airtable - Create Record\n**Function**: `create_record`\n**Purpose**: Add new records to specified table\n**Key Parameters**:\n- `fieldValues`: Array of field name-value pairs\n\n**Field Value Format**:\n```json\n[\n  {\"fieldId\": \"Name\", \"fieldValue\": \"John Doe\"},\n  {\"fieldId\": \"Email\", \"fieldValue\": \"<EMAIL>\"}\n]\n```\n\n### Tool 4: Airtable - Update Record\n**Function**: `update_record`\n**Purpose**: Modify existing records\n**Key Parameters**:\n- `recordId`: Record to update\n- `fieldValues`: Fields to modify\n\n**Update Strategy**: Only specified fields are updated, others remain unchanged\n\n### Tool 5: Airtable - Delete Record\n**Function**: `delete_record`\n**Purpose**: Remove records from table (use with extreme caution)\n**Key Parameters**:\n- `recordId`: Record to delete\n\n**Safety Protocol**: Always confirm deletion intent with user before executing\n\n## Data Analysis Capabilities\n\n### Statistical Analysis\n- Calculate averages, sums, min/max values\n- Identify trends and patterns in numeric data\n- Generate frequency distributions\n- Perform correlation analysis between fields\n\n### Data Insights\n- Identify missing or incomplete data\n- Suggest data quality improvements\n- Highlight outliers and anomalies\n- Recommend optimal field types and structures\n\n### Filtering & Search\n- Use Airtable formula syntax for complex filtering\n- Search across multiple fields simultaneously\n- Apply date range filters\n- Combine multiple criteria with AND/OR logic\n\n## Operational Guidelines\n\n### Error Handling\n- **Invalid Base/Table IDs**: Prompt user for correct identifiers\n- **Missing Fields**: List available fields before operations\n- **Permission Errors**: Verify API key has necessary permissions\n- **Rate Limits**: Implement exponential backoff for API calls\n\n### Data Validation\n- Verify field types before creating/updating records\n- Check required fields are populated\n- Validate email formats, URLs, and other structured data\n- Ensure numeric values are within acceptable ranges\n\n### Security & Privacy\n- Never log or store sensitive personal data\n- Respect data access permissions\n- Warn users before performing destructive operations\n- Maintain audit trail for data modifications\n\n## Response Formatting\n\n### Successful Operations\n- Provide clear confirmation of actions taken\n- Include relevant record IDs and key field values\n- Offer next steps or related operations\n\n### Data Presentation\n- Format tabular data in readable tables\n- Highlight key insights and patterns\n- Use bullet points for multiple findings\n- Include record counts and summary statistics\n\n### Error Messages\n- Explain what went wrong in plain language\n- Suggest corrective actions\n- Provide examples of correct parameter formats\n\n## Advanced Features\n\n### Batch Operations\n- Process multiple records efficiently\n- Provide progress updates for large datasets\n- Handle partial failures gracefully\n\n### Cross-Table Analysis\n- Link related data across tables\n- Perform lookups and joins\n- Identify relationships and dependencies\n\n### Automation Suggestions\n- Recommend workflow optimizations\n- Suggest field improvements\n- Identify repetitive tasks for automation\n\nAlways prioritize data accuracy, user privacy, and operational safety in all interactions. Provide clear, actionable insights that help users make informed decisions about their data.\n\n## Current Context\nCurrent date/time: {{ $now }}\nUser session: {{ $('When chat message received')?.item.json.sessionId || $('Webhook Trigger')?.item.json.session_id || 'default' }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1272, 80], "id": "fc4d0427-9b1a-4935-b3a9-e8b99f03a505", "name": "Airtable Agent"}, {"parameters": {"content": "## 🔧 Tool Configuration\n\n**Important Notes:**\n- Each Airtable tool needs your base ID and table ID\n- Base IDs start with 'app' (e.g., appXXXXXXXXXXXXXX)\n- Table IDs start with 'tbl' or use table names\n- You can manually change the selected table (or base) in any tools at any time.\n\n**Finding Your IDs:**\n1. Go to your Airtable base\n2. Base ID is in the URL after airtable.com/\n3. Table ID can be found in the URL or use table name", "height": 420, "width": 300, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [1260, -380], "typeVersion": 1, "id": "0a3cc4db-ccb2-4314-b965-d34d153a5607", "name": "Configuration Notes"}, {"parameters": {"content": "## 📊 Example Queries\n\n**Data Retrieval:**\n- \"Show me all records from the Customers table\"\n- \"Get records where Status equals 'Active'\"\n- \"Find customers with revenue > $10,000\"\n\n**Data Creation:**\n- \"Add a new customer named <PERSON> <NAME_EMAIL>\"\n- \"Create a record in Projects table with name 'Website Redesign'\"\n\n**Analysis:**\n- \"Calculate average revenue across all customers\"\n- \"Show me trends in monthly sales data\"\n- \"Find duplicate entries in the database\"", "height": 300, "width": 340}, "type": "n8n-nodes-base.stickyNote", "position": [1580, -380], "typeVersion": 1, "id": "3419fa36-a95d-4cc7-8f2e-d2fbcef894fc", "name": "Usage Examples"}, {"parameters": {"content": "![Agent Skins](https://agent-skins.nyc3.cdn.digitaloceanspaces.com/agent_skins_logo.png)\n## Agent Skins\n[Browse More Agents](https://agentskins.gumroad.com)", "height": 320, "width": 220, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [520, 60], "typeVersion": 1, "id": "a4e9bac6-c54e-41e2-aac2-6b64c765e551", "name": "Sticky Note2"}, {"parameters": {"content": "## 👇 More Agent Skins\n\n🔵 [Slack + Gmail AI Agent](https://agentskins.gumroad.com/l/slack-google-gmail-ai-agent-n8n-template)\n\n🔵 [MongoDB Data AI Agent for n8n](https://agentskins.gumroad.com/l/mongodb-data-agent-basic-n8n)\n\n🔵 [AI Chief Marketing Officer](https://agentskins.gumroad.com/l/chief-marketing-officer-ai-system-prompt)\n\n🔵 [Multi-Persona AI Customer Success Team](https://agentskins.gumroad.com/l/customer-success-team-ai-prompt-for-agents)\n\n🔵 [Talk to Your Inbox Like It's Your Assistant](https://agentskins.gumroad.com/l/google-gmail-agent-n8n-template)\n\n🔵 [Track All of Your Sales with Gumroad + Google Sheets](https://agentskins.gumroad.com/l/track-sales-with-gumroad-google-sheets-n8n-workflow)", "height": 300, "width": 440, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [1940, -380], "typeVersion": 1, "id": "4dfffca1-0381-4bbd-9b58-ea7598505355", "name": "<PERSON><PERSON>"}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to list all records from a table or search for records in a table by passing the Filter By Formula.", "operation": "search", "base": {"__rl": true, "value": "app934583498345", "mode": "id"}, "table": {"__rl": true, "value": "tbl90209230", "mode": "id"}, "filterByFormula": "={{ $fromAI('Filter_By_Formula', ``, 'string') }}", "returnAll": false, "limit": "={{ $fromAI('Limit', ``, 'number') }}", "options": {}}, "type": "n8n-nodes-base.airtableTool", "typeVersion": 2, "position": [1360, 300], "id": "2001747c-9129-4053-9218-15f7957972dd", "name": "Airtable - List All or Search Records", "credentials": {"airtableTokenApi": {"id": "nm0gQ2lklGckqLn0", "name": "Airtable Personal Access Token account"}}}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Airtable Agent", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Airtable Agent", "type": "ai_memory", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}, "Get Current Date": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Airtable Agent", "type": "main", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Airtable Agent", "type": "main", "index": 0}]]}, "Airtable - Create Record": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}, "Airtable - Update Record": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}, "Airtable - Delete Record": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}, "Airtable - Get Record": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}, "Airtable Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Airtable - List All or Search Records": {"ai_tool": [[{"node": "Airtable Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "60fafd75-81f3-416a-8bca-398b18fb7a7f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "82165f16ec120159b1af47043d75bae62a86c23c2786c49fff5bfbe22bbe4abe"}, "id": "k3epxUsToip7hHT0", "tags": [{"createdAt": "2025-05-09T01:28:24.803Z", "updatedAt": "2025-05-09T01:28:24.803Z", "id": "pqNlWhNm70Ouwmsh", "name": "agent"}, {"createdAt": "2025-06-04T21:20:28.169Z", "updatedAt": "2025-06-04T21:20:28.169Z", "id": "jSklMxQC4xwIi6I5", "name": "gumroad"}]}