{"name": "My workflow", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyX", "value": 15, "unit": "minutes"}]}, "simple": false, "filters": {}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-2400, 1620], "id": "41717d75-2f25-47f8-aee1-3a6333d84aa8", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}}, {"parameters": {"promptType": "define", "text": "=Topic: {{ $json.subject }}\nDescription: {{ $json.text }}\nSender: {{ $json.from.text }}", "hasOutputParser": true, "options": {"systemMessage": "=** Your Role **\nYou are an intelligent email assistant responsible for sorting incoming messages. Your task is to analyze the content, subject, and sender of each email and assign it to the appropriate category.\n\n** Categories **\nCategories for sorting (return exact lowercase value):\n\n- priority: messages requiring immediate attention (from supervisors, clients)\n- business: correspondence related to current projects, contracts, meetings  \n- newsletter: newsletter issues and subscriptions\n- administrative: invoices, contracts, formal documents\n- personal: private messages not related to work\n- notifications: messages from social media, apps, system notifications\n- support: customer service, help desk, technical support\n- finance: banking, payments, financial statements, expenses\n- travel: flights, hotels, bookings, visas\n- events: conferences, meetings, webinars, invitations\n- shopping: orders, receipts, shipping, e-commerce\n- security: login alerts, password resets, 2FA codes\n- marketing: promotions, product announcements (not spam)\n- urgent: time-sensitive but not priority-level\n- review: documents requiring approval/review\n- hr: human resources, benefits, company policies\n- it: software updates, system maintenance, tech tickets\n\n** Instructions **\nFor each email:\n\n- Analyze the content, subject, and sender\n- Assign it to the appropriate category\n- Add a label with the category\n- If you are unsure, do not label the message\n\n** Response Format **\nAlways return the result in JSON format:\n\n{\n  \"email_label\": \"business\"\n}\n\nIMPORTANT – the response must always be in lowercase."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-640, 2700], "id": "1a73898c-6001-4341-bed5-061f3d0e41d3", "name": "Generate Label (old-emails)"}, {"parameters": {"operation": "getAll", "limit": 5, "simple": false, "filters": {}, "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1520, 2800], "id": "6e6829a8-a489-41c4-a813-3d6ce6f1c279", "name": "Get Messages", "webhookId": "a0511bfe-4783-4d8a-a297-c1d75fba8f02", "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2400, 2700], "id": "ece6854a-9720-44e9-bec4-47de2a5be955", "name": "Setup & Process Existing"}, {"parameters": {"resource": "label", "operation": "create", "name": "={{ $('Split Out').item.json.labelName }}", "options": {"labelListVisibility": "labelShowIfUnread", "messageListVisibility": "show"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1740, 2600], "id": "1a6361b3-2880-491c-b99e-220ad6083c12", "name": "Create missing label", "webhookId": "9d1e5308-0dfd-45e0-9495-0f75579b339c", "executeOnce": false, "alwaysOutputData": false, "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}, "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "// Get all labels from the Get all labels node\nconst allLabels = $('Get all labels').all();\n\n// Extract user labels and format as \"name: id\"\nconst userLabels = allLabels\n  .filter(item => item.json.type === 'user')\n  .map(item => `${item.json.name}: ${item.json.id}`);\n\n// Return as single item with all formatted labels\nreturn [{\n  json: {\n    exsitingLabels: userLabels,\n    totalUserLabels: userLabels.length\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1300, 2600], "id": "0f9e30fb-eb4a-4f83-b0c0-ddf7242f8613", "name": "Labels Map"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-580, 2400], "id": "d53eb170-49e9-4d9b-9caa-c1c25d5d5a5b", "name": "Aggregate"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6c287523-4ae2-43c4-8f9a-faf756f01855", "leftValue": "={{ $('Get Messages').item.json.labelIds.join(',') }}", "rightValue": "Label", "operator": {"type": "string", "operation": "notContains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-1300, 2800], "id": "0d16223a-095e-4837-9fe7-9ed805a62973", "name": "Check Label (existing-emails)", "alwaysOutputData": false}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6c287523-4ae2-43c4-8f9a-faf756f01855", "leftValue": "={{ $('G<PERSON> Trigger').first().json.labelIds.join(',') }}", "rightValue": "Label", "operator": {"type": "string", "operation": "notContains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-1960, 1720], "id": "e8dc7f8b-e0d3-4f20-9583-b77c830f3107", "name": "Check Label (new-emails)", "alwaysOutputData": false}, {"parameters": {"resource": "label", "returnAll": true}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-1520, 2600], "id": "ba5085fe-3fd9-4188-acfd-7f365bbe402c", "name": "Get all labels", "webhookId": "57e9569d-52d4-4b72-abe8-fa60e7362203", "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ca2da048-5276-4578-82f1-fa9ac2460153", "name": "labels", "value": "=[\n  \"PRIORITY\",\n  \"BUSINESS\", \n  \"NE<PERSON>LETT<PERSON>\",\n  \"ADMINISTRATIVE\",\n  \"PERSONAL\",\n  \"NOTIFICATIONS\",\n  \"SUPPORT\",\n  \"FINANCE\",\n  \"TRAVEL\",\n  \"EVENTS\",\n  \"SHOPPING\",\n  \"SECURITY\",\n  \"MARKETING\",\n  \"URGENT\",\n  \"RE<PERSON>EW\",\n  \"HR\",\n  \"IT\"\n]", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-2180, 2600], "id": "********-2926-475d-81f9-ec999b1fdc6c", "name": "Labels"}, {"parameters": {"fieldToSplitOut": "labels", "options": {"destinationFieldName": "labelName"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1960, 2600], "id": "11f2a0ba-1ff8-4633-b718-2966a5d332fb", "name": "Split Out"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "id"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-1240, 1320], "id": "0cc86188-4ed8-47cd-af44-bfac925c9dff", "name": "Aggregate1"}, {"parameters": {"resource": "label", "returnAll": true}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-2180, 1520], "id": "2ef3b9c9-8a7e-4251-bf04-3cff456c6d28", "name": "Get all labels (new-emails)", "webhookId": "57e9569d-52d4-4b72-abe8-fa60e7362203", "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}}, {"parameters": {"jsCode": "// Get all labels from the Get all labels node\nconst allLabels = $('Get all labels (new-emails)').all();\n\n// Extract user labels and format as \"name: id\"\nconst userLabels = allLabels\n  .filter(item => item.json.type === 'user')\n  .map(item => `${item.json.name}: ${item.json.id}`);\n\n// Return as single item with all formatted labels\nreturn [{\n  json: {\n    exsitingLabels: userLabels,\n    totalUserLabels: userLabels.length\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1960, 1520], "id": "cda2403c-63c4-4141-9576-558986d20195", "name": "Labels Map (new-emails)"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1740, 1620], "id": "8ad6b6af-9805-4a26-8a95-46478031bf77", "name": "Merge (new-emails)"}, {"parameters": {"mode": "chooseBranch", "useDataOfInput": 2}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1080, 2700], "id": "1cc8e096-b046-46b0-bea0-388cf5df19f9", "name": "<PERSON>rge  (old-emails)"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-860, 2700], "id": "d5c07fe8-34b7-4a9b-be85-7050194e290b", "name": "Loop Over (old-emails)"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-1520, 1620], "id": "adc880eb-e7fd-4c8d-8430-ef41041f6524", "name": "Loop Over (new-emails)"}, {"parameters": {"promptType": "define", "text": "=Topic: {{ $json.subject }}\nDescription: {{ $json.text }}\nSender: {{ $json.from.text }}", "hasOutputParser": true, "options": {"systemMessage": "=** Your Role **\nYou are an intelligent email assistant responsible for sorting incoming messages. Your task is to analyze the content, subject, and sender of each email and assign it to the appropriate category.\n\n** Categories **\nCategories for sorting (return exact lowercase value):\n\n- priority: messages requiring immediate attention (from supervisors, clients)\n- business: correspondence related to current projects, contracts, meetings  \n- newsletter: newsletter issues and subscriptions\n- administrative: invoices, contracts, formal documents\n- personal: private messages not related to work\n- notifications: messages from social media, apps, system notifications\n- support: customer service, help desk, technical support\n- finance: banking, payments, financial statements, expenses\n- travel: flights, hotels, bookings, visas\n- events: conferences, meetings, webinars, invitations\n- shopping: orders, receipts, shipping, e-commerce\n- security: login alerts, password resets, 2FA codes\n- marketing: promotions, product announcements (not spam)\n- urgent: time-sensitive but not priority-level\n- review: documents requiring approval/review\n- hr: human resources, benefits, company policies\n- it: software updates, system maintenance, tech tickets\n\n** Instructions **\nFor each email:\n\n- Analyze the content, subject, and sender\n- Assign it to the appropriate category\n- Add a label with the category\n- If you are unsure, do not label the message\n\n** Response Format **\nAlways return the result in JSON format:\n\n{\n  \"email_label\": \"business\"\n}\n\nIMPORTANT – the response must always be in lowercase."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-1300, 1620], "id": "55506cc1-dcea-4495-a982-35151eccc5e2", "name": "Generate Label (new-emails)"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6cbeeb93-8940-4e90-97e2-de2a5310e484", "leftValue": "={{ $('Generate Label (old-emails)').item.json.output.email_label }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-280, 2700], "id": "956afe5a-4ccc-49bc-bff6-a9fde969f9de", "name": "If (old-emails)"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6cbeeb93-8940-4e90-97e2-de2a5310e484", "leftValue": "={{ $('Generate Label (new-emails)').item.json.output.email_label }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-940, 1620], "id": "9bf33ced-a1e6-4364-9e1c-f69be6704b2c", "name": "If (new-emails)"}, {"parameters": {"jsCode": "// Get the email label from AI (assuming it's 'business')\nconst emailLabel = $(\"Generate Label (old-emails)\").first().json.output.email_label;\n\n// Capitalize first letter to match label format\nconst upperCaseLabel = emailLabel.toUpperCase();\n\n// Get the existing labels array\nconst existingLabels = $('Labels Map').first().json.exsitingLabels;\n\n// Find the matching label\nconst matchingLabel = existingLabels.find(label => \n  label.startsWith(upperCaseLabel + ':')\n);\n\nif (matchingLabel) {\n  // Extract the label ID (everything after ': ')\n  const labelId = matchingLabel.split(': ')[1];\n  \n  return [{\n    json: {\n      success: true,\n      generatedLabel: emailLabel,\n      existingLabelName: upperCaseLabel,\n      existingLabelId: labelId\n    }\n  }];\n} else {\n  return [{\n    json: {\n      success: false,\n      error: `No label found for: ${upperCaseLabel}`,\n      generatedLabel: upperCaseLabel,\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, 2640], "id": "c300484a-d805-4aa6-b10b-726755484a1e", "name": "Get Label ID (old-emails)"}, {"parameters": {"jsCode": "// Get the email label from AI (assuming it's 'business')\nconst emailLabel = $('Generate Label (new-emails)').first().json.output.email_label;\n\n// Capitalize first letter to match label format\nconst upperCaseLabel = emailLabel.toUpperCase();\n\n// Get the existing labels array\nconst existingLabels = $(\"Labels Map (new-emails)\").first().json.exsitingLabels;\n\n// Find the matching label\nconst matchingLabel = existingLabels.find(label => \n  label.startsWith(upperCaseLabel + ':')\n);\n\nif (matchingLabel) {\n  // Extract the label ID (everything after ': ')\n  const labelId = matchingLabel.split(': ')[1];\n  \n  return [{\n    json: {\n      success: true,\n      generatedLabel: emailLabel,\n      existingLabelName: upperCaseLabel,\n      existingLabelId: labelId\n    }\n  }];\n} else {\n  return [{\n    json: {\n      success: false,\n      error: `No label found for: ${upperCaseLabel}`,\n      generatedLabel: upperCaseLabel,\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-720, 1540], "id": "7b1ab6d6-b43b-4fda-bc64-7cc33b652912", "name": "Get Label ID (new-emails)"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Get Label ID (old-emails)').first().json.success }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "b4d319b6-0068-4203-a233-c6f78f32086b"}], "combinator": "and"}, "renameOutput": true, "outputKey": "use-label"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d1bf9fef-df42-499c-ab24-a2f733708c41", "leftValue": "={{ $('Get Label ID (old-emails)').first().json.success }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "create-label"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [160, 2640], "id": "f7308175-61e4-4f48-bbba-b1ff38e3116a", "name": "Switch (old-emails)"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Get Label ID (new-emails)').first().json.success }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "b4d319b6-0068-4203-a233-c6f78f32086b"}], "combinator": "and"}, "renameOutput": true, "outputKey": "use-label"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d1bf9fef-df42-499c-ab24-a2f733708c41", "leftValue": "={{ $('Get Label ID (new-emails)').first().json.success }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "create-label"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-500, 1540], "id": "3c605710-6fa7-4c47-8a84-265660dcda2e", "name": "Switch (new-emails)"}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Loop Over (old-emails)').item.json.id }}", "labelIds": "={{ [$('Switch (old-emails)').item.json.existingLabelId] }}"}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [380, 2560], "id": "1b789b1c-85ac-4cfa-8e16-af6803ae7ea0", "name": "Set Label (old-emails)", "webhookId": "70219a19-4705-493c-9291-527655dd8099", "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}}, {"parameters": {"operation": "addLabels", "messageId": "={{ $('Loop Over (new-emails)').item.json.id }}", "labelIds": "={{ [$('Switch (new-emails)').item.json.existingLabelId] }}"}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-280, 1460], "id": "12034b0a-8c25-4afe-97e0-5a457f2dbecd", "name": "Set Existing (new-emails)", "webhookId": "70219a19-4705-493c-9291-527655dd8099", "credentials": {"gmailOAuth2": {"id": "lYmCPKubCL3ydOOB", "name": "Gmail Account (<EMAIL>)"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "name": "Replace (new-emails)", "typeVersion": 1, "position": [-60, 1700], "id": "dd06bcee-f2e2-4b1e-90a1-e24c0fe4503b"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "name": "Replace (old-emails)", "typeVersion": 1, "position": [600, 2780], "id": "3ca92458-52e9-4800-8602-030da2f2beb3"}, {"parameters": {"content": "# New emails (processed every 15 mins)", "height": 900, "width": 2780, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-2560, 1240], "typeVersion": 1, "id": "a8f38b58-e1f8-4739-9d35-6ec21767af96", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Setup and process existing emails", "height": 940, "width": 3440, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-2560, 2260], "typeVersion": 1, "id": "e2ba8d02-0903-4894-b12f-00380edcfcf7", "name": "Sticky Note1"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1340, 1840], "id": "47fa38dc-2d2b-4788-8547-fcdb4bbfcd80", "name": "OpenAI (new-emails)", "credentials": {"openAiApi": {"id": "DmevfRfMAARiwx4p", "name": "OpenAi Account (n8n-automations)"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"email_label\": \"business\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-1120, 1840], "id": "6d5994b6-cefe-4aea-bdfe-23d08a324c00", "name": "Structured Output Parser (new-emails)"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-680, 2920], "id": "873abea5-036d-48b9-a5f0-d21034008f73", "name": "OpenAI (old-emails)", "credentials": {"openAiApi": {"id": "DmevfRfMAARiwx4p", "name": "OpenAi Account (n8n-automations)"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"email_label\": \"business\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-460, 2920], "id": "2d1d641b-f205-44f3-81d1-0b8675485a5b", "name": "Structured Output Parser (old-emails)"}, {"parameters": {"content": "**Author:** <PERSON><PERSON>  \n**Title:** AI Integration Expert | Founder [AixonAI](https://aixonai.com)\n**Email:** <EMAIL>  \n**Website:** [https://mohitaneja.com](https://mohitaneja.com)\n**Version:** 1.0  \n**Last Updated:** July 10, 2025\n**Documentation**\n[https://mohitaneja.com/articles/n8n-workflow-gmail-ai-auto-sorter](https://mohitaneja.com/articles/n8n-workflow-gmail-ai-auto-sorter)\n\n**Get In Touch** [Support](https://mohitaneja.com/contact)\n**Hire Me** [Upwork](https://www.upwork.com/freelancers/mohitaneja)", "height": 900, "width": 600}, "type": "n8n-nodes-base.stickyNote", "position": [280, 1240], "typeVersion": 1, "id": "ef283f83-510a-46cd-8e65-389ffa75cbd4", "name": "Sticky Note2"}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Check Label (new-emails)", "type": "main", "index": 0}, {"node": "Get all labels (new-emails)", "type": "main", "index": 0}]]}, "Generate Label (old-emails)": {"main": [[{"node": "If (old-emails)", "type": "main", "index": 0}]]}, "Get Messages": {"main": [[{"node": "Check Label (existing-emails)", "type": "main", "index": 0}]]}, "Setup & Process Existing": {"main": [[{"node": "Labels", "type": "main", "index": 0}, {"node": "Get Messages", "type": "main", "index": 0}]]}, "Create missing label": {"main": [[{"node": "Get all labels", "type": "main", "index": 0}]]}, "Labels Map": {"main": [[{"node": "<PERSON>rge  (old-emails)", "type": "main", "index": 0}]]}, "Check Label (existing-emails)": {"main": [[{"node": "<PERSON>rge  (old-emails)", "type": "main", "index": 1}]]}, "Check Label (new-emails)": {"main": [[{"node": "Merge (new-emails)", "type": "main", "index": 1}]]}, "Get all labels": {"main": [[{"node": "Labels Map", "type": "main", "index": 0}]]}, "Labels": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Create missing label", "type": "main", "index": 0}]]}, "Get all labels (new-emails)": {"main": [[{"node": "Labels Map (new-emails)", "type": "main", "index": 0}]]}, "Labels Map (new-emails)": {"main": [[{"node": "Merge (new-emails)", "type": "main", "index": 0}]]}, "Merge (new-emails)": {"main": [[{"node": "Loop Over (new-emails)", "type": "main", "index": 0}]]}, "Merge  (old-emails)": {"main": [[{"node": "Loop Over (old-emails)", "type": "main", "index": 0}]]}, "Loop Over (old-emails)": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}], [{"node": "Generate Label (old-emails)", "type": "main", "index": 0}]]}, "Loop Over (new-emails)": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}], [{"node": "Generate Label (new-emails)", "type": "main", "index": 0}]]}, "Generate Label (new-emails)": {"main": [[{"node": "If (new-emails)", "type": "main", "index": 0}]]}, "If (old-emails)": {"main": [[{"node": "Get Label ID (old-emails)", "type": "main", "index": 0}], [{"node": "Replace (old-emails)", "type": "main", "index": 0}]]}, "If (new-emails)": {"main": [[{"node": "Get Label ID (new-emails)", "type": "main", "index": 0}], [{"node": "Replace (new-emails)", "type": "main", "index": 0}]]}, "Get Label ID (old-emails)": {"main": [[{"node": "Switch (old-emails)", "type": "main", "index": 0}]]}, "Get Label ID (new-emails)": {"main": [[{"node": "Switch (new-emails)", "type": "main", "index": 0}]]}, "Switch (old-emails)": {"main": [[{"node": "Set Label (old-emails)", "type": "main", "index": 0}], [{"node": "Replace (old-emails)", "type": "main", "index": 0}]]}, "Switch (new-emails)": {"main": [[{"node": "Set Existing (new-emails)", "type": "main", "index": 0}], [{"node": "Replace (new-emails)", "type": "main", "index": 0}]]}, "Set Label (old-emails)": {"main": [[{"node": "Replace (old-emails)", "type": "main", "index": 0}]]}, "Set Existing (new-emails)": {"main": [[{"node": "Replace (new-emails)", "type": "main", "index": 0}]]}, "Replace (new-emails)": {"main": [[{"node": "Loop Over (new-emails)", "type": "main", "index": 0}]]}, "Replace (old-emails)": {"main": [[{"node": "Loop Over (old-emails)", "type": "main", "index": 0}]]}, "OpenAI (new-emails)": {"ai_languageModel": [[{"node": "Generate Label (new-emails)", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser (new-emails)": {"ai_outputParser": [[{"node": "Generate Label (new-emails)", "type": "ai_outputParser", "index": 0}]]}, "OpenAI (old-emails)": {"ai_languageModel": [[{"node": "Generate Label (old-emails)", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser (old-emails)": {"ai_outputParser": [[{"node": "Generate Label (old-emails)", "type": "ai_outputParser", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "db3e181e-8ac5-47bf-9ed0-62ad61bd38d3", "meta": {"templateCredsSetupCompleted": true, "instanceId": "bd30516cdc3ca75ae9cfdccfbc3f50030245b4215c8090d6c1123ee7cfd89e7c"}, "id": "FVBLpeZji7xbXHj1", "tags": []}