{"name": "Content Publishing System 2.0 Template", "nodes": [{"parameters": {"content": "## 🚀 Ultimate Reels Publishing Agent 2.0\n**Get Millions of Views Across All Platforms — And Turn Them Into Income!**\n\n- Hybrid Content Publishing System\n- 🎯 One-click scheduling across **Instagram Reels**, **YouTube Shorts**, **Facebook Reels** and **TikTOk**\n- 💬 Auto-generate **viral captions**, **engaging descriptions**, and **platform-optimized hashtags**  \n- 💸 No need for costly APIs — **100% budget-friendly**  \n- 🧠 Smart content engine tailored for **maximum reach & engagement**\n\n**Grow faster. Post smarter. Earn more.**\n\n**Build by Dainami AI** :https://www.youtube.com/@dainami_ai/", "height": 1120, "width": 2280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [100, 0], "id": "7f5339fd-3ea8-4a80-98c1-f1647fb0506d", "name": "Sticky Note7"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "{insert instagram account id}", "edge": "media", "options": {"queryParameters": {"parameter": [{"name": "caption", "value": "={{ $('Publishing Agent').item.json.output.instagram.description }}"}, {"name": "media_type", "value": "REELS"}, {"name": "video_url", "value": "={{ $json.url }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1120, 600], "id": "ceb0dbff-41a1-4394-ace0-e35bd20e219c", "name": "Create Container", "credentials": {"facebookGraphApi": {"id": "4kwZbo5wff8w11D8", "name": "New Meta"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "{insert instagram account id}", "edge": "media_publish", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1420, 600], "id": "43d19dff-af38-4691-a030-17b3e04831ab", "name": "Instagram POST", "credentials": {"facebookGraphApi": {"id": "4kwZbo5wff8w11D8", "name": "New Meta"}}}, {"parameters": {"promptType": "define", "text": "=- **Input Text Field**: {{ $json['Details/Captions'] }}\n- **style_tone**: Boss<PERSON>by", "hasOutputParser": true, "messages": {"messageValues": [{"type": "=SystemMessagePromptTemplate", "message": "=You are an AI specialized in crafting high-impact, viral titles and descriptions optimized for TikTok Shorts, Instagram Reels, YouTube Shorts, and Facebook Reels. \n\nYou will receive a input containing informational fields which may be variably named (e.g., \"caption\", \"description\", etc.).\n\nYour task is to dynamically detect and use the first available text field from the input to generate platform-specific content tailored to each platform’s unique style and audience engagement.\n\nThe **tone and style** of the output should be customizable based on a user-defined variable called `style_tone`. This variable specifies the persona or voice you should adopt when generating content (e.g., \"bossbaby\", \"motivational coach\", \"comedy sketch\", etc.).\n\n---\n\n### Input variables:\n\n- **Input Text Field**: The text to base your content on (captions, description, name etc).  \n- **style_tone**: A string indicating the desired voice/persona for the content.\n\n---\n\n### Platform-specific output requirements:\n\n1. **Instagram Reels**:  \n   - Description: Brief, visually descriptive, up to 5 hashtags, strong call-to-action.\n\n2. **YouTube Shorts**:  \n   - Title (max 50 chars): Direct, suspenseful, clickbait style.  \n   - Description: Concise, keyword-rich, with engagement prompts.\n\n3. **Facebook Reels**:   \n   - Description: Short, emotional hook encouraging reactions.\n\n4. **TikTok**:  \n   - Description: Creative, catchy, platform-appropriate.\n\n---\n\n### Output format:\n\nReturn strictly this JSON structure, adapting content tone according to `style_tone`:\n{\n  \"instagram\": {\n    \"description\": \"...\"\n  },\n  \"youtube\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  },\n  \"facebook\": {\n    \"description\": \"...\"\n  },\n  \"tiktok\": {\n    \"description\": \"...\"\n  }\n}\n\n#Note \n- Return only the EXACT JSON Output and nothing else."}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [480, 440], "id": "41de213f-997e-41be-84f3-cb2f75f6efdd", "name": "Publishing Agent"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c087ebbf-22a5-45f4-82d5-ddabfd921889", "leftValue": "={{ $json.fields.YouTube }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "301baec8-c772-4042-bdc1-7d2d99f07043", "leftValue": "={{ $json.fields.Instagram }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "c7469347-06e8-4163-a280-d85ed47d167b", "leftValue": "={{ $json.fields.Facebook }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "a216f3c7-76f0-4bdb-89c6-e9e0dd389ce5", "leftValue": "={{ $json.fields.TikTok }}", "rightValue": "Uploaded", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1960, 600], "id": "51b133f7-c918-460f-afb1-892eab7652eb", "name": "If"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appkyh69NFvBjh0Jz", "mode": "list", "cachedResultName": "Content Publishing System 2.0", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz"}, "table": {"__rl": true, "value": "tblvjc0sIpiw7Ct8s", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz/tblvjc0sIpiw7Ct8s"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Search Record').item.json.id }}", "Youtube": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Video Name", "displayName": "Video Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Details/Captions", "displayName": "Details/Captions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Video Link (Gdrive)", "displayName": "Video Link (Gdrive)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Start Upload", "value": "Start Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "TikTok", "displayName": "TikTok", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1300, 120], "id": "896cd643-7096-4e22-b32a-0e4d1fb3a0e2", "name": "Youtube Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appkyh69NFvBjh0Jz", "mode": "list", "cachedResultName": "Content Publishing System 2.0", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz"}, "table": {"__rl": true, "value": "tblvjc0sIpiw7Ct8s", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz/tblvjc0sIpiw7Ct8s"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Search Record').item.json.id }}", "Instagram": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "Video Name", "displayName": "Video Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Details/Captions", "displayName": "Details/Captions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Video Link (Gdrive)", "displayName": "Video Link (Gdrive)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Start Upload", "value": "Start Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "TikTok", "displayName": "TikTok", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1560, 600], "id": "36dfeff2-c8d2-4c8e-ba38-b11a792acd25", "name": "Instagram Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appkyh69NFvBjh0Jz", "mode": "list", "cachedResultName": "Content Publishing System 2.0", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz"}, "table": {"__rl": true, "value": "tblvjc0sIpiw7Ct8s", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz/tblvjc0sIpiw7Ct8s"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Search Record').item.json.id }}", "Facebook": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Video Name", "displayName": "Video Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Details/Captions", "displayName": "Details/Captions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Video Link (Gdrive)", "displayName": "Video Link (Gdrive)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Start Upload", "value": "Start Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "TikTok", "displayName": "TikTok", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1740, 860], "id": "f5cbf805-a081-4538-8569-f85ff649a995", "name": "Facebook Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appkyh69NFvBjh0Jz", "mode": "list", "cachedResultName": "Content Publishing System 2.0", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz"}, "table": {"__rl": true, "value": "tblvjc0sIpiw7Ct8s", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz/tblvjc0sIpiw7Ct8s"}, "filterByFormula": "{Status} = \"Start Upload\"", "returnAll": false, "limit": 1, "options": {}, "sort": {"property": [{"field": "Status"}]}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [320, 440], "id": "b3cd42a8-7e05-4beb-a0db-b9f1e150fa93", "name": "Search Record", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('Publishing Agent').item.json.output.youtube.title }}", "regionCode": "SG", "categoryId": "1", "options": {"description": "={{ $('Publishing Agent').item.json.output.youtube.description }}"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1120, 120], "id": "fddeac45-486e-4e3d-976b-b88764f48608", "name": "Youtube Post", "credentials": {"youTubeOAuth2Api": {"id": "INhltBeDLkn5aZ0Z", "name": "YouTube account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appkyh69NFvBjh0Jz", "mode": "list", "cachedResultName": "Content Publishing System 2.0", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz"}, "table": {"__rl": true, "value": "tblvjc0sIpiw7Ct8s", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz/tblvjc0sIpiw7Ct8s"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $json.id }}", "Status": "Completed"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Video Name", "displayName": "Video Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Details/Captions", "displayName": "Details/Captions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Video Link (Gdrive)", "displayName": "Video Link (Gdrive)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Start Upload", "value": "Start Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": false}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "TikTok", "displayName": "TikTok", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2180, 580], "id": "ec2a360e-c459-4af4-845c-5f49afda6c28", "name": "Video Published", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"url": "=https://graph.facebook.com/v22.0/{{ $('Start Upload').item.json.video_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "fields", "value": "status"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1580, 860], "id": "f8b71b6e-4788-43fc-be86-aa9b98bd519c", "name": "Check Status", "credentials": {"facebookGraphApi": {"id": "fjBHdBd1sQfxGugl", "name": "Extend New Graph (July 25)"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "{insert facebook page id}/video_reels", "options": {"queryParameters": {"parameter": [{"name": "upload_phase", "value": "start"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1120, 860], "id": "2ae6d0e4-edba-491b-9652-d2473d50e3a2", "name": "Start Upload", "credentials": {"facebookGraphApi": {"id": "fjBHdBd1sQfxGugl", "name": "Extend New Graph (July 25)"}}}, {"parameters": {"method": "POST", "url": "={{ $json.upload_url }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "file_url", "value": "={{ $('Upload To Cloudinary').item.json.url }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1280, 860], "id": "4e06542e-447d-43b8-a126-36f065d27ebd", "name": "Upload to Rupload", "credentials": {"httpHeaderAuth": {"id": "jRDusgPWYlKZodhd", "name": "Runway"}, "facebookGraphApi": {"id": "fjBHdBd1sQfxGugl", "name": "Extend New Graph (July 25)"}}}, {"parameters": {"method": "POST", "url": "https://graph.facebook.com/v22.0/{insert facebook page id}/video_reels", "authentication": "predefinedCredentialType", "nodeCredentialType": "facebookGraphApi", "sendQuery": true, "queryParameters": {"parameters": [{"name": "video_id", "value": "={{ $('Start Upload').item.json.video_id }}"}, {"name": "upload_phase", "value": "FINISH"}, {"name": "video_state", "value": "PUBLISHED"}, {"name": "description", "value": "={{ $('Publishing Agent').item.json.output.facebook.description }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1440, 860], "id": "2465fa43-1ddd-4e4f-ad81-daf047bb3c82", "name": "Upload Reel to FB", "credentials": {"facebookGraphApi": {"id": "fjBHdBd1sQfxGugl", "name": "Extend New Graph (July 25)"}}}, {"parameters": {"method": "POST", "url": "https://api.upload-post.com/api/upload", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.output.tiktok.description }}"}, {"name": "user", "value": "tester123"}, {"name": "platform[]", "value": "tiktok"}, {"parameterType": "formBinaryData", "name": "video", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 360], "id": "93f0db19-e3ca-49db-9734-3bc9150b1e35", "name": "TikTok Post (NEW)", "retryOnFail": true, "waitBetweenTries": 5000, "credentials": {"httpHeaderAuth": {"id": "RScYZfzdT1tlOftE", "name": "Upload-Post"}}}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [140, 440], "id": "ed0a1188-42de-4087-bb42-7c1f53599fbe", "name": "Schedule Trigger2"}, {"parameters": {"jsonSchemaExample": "{\n  \"instagram\": {\n    \"description\": \"...\"\n  },\n  \"youtube\": {\n    \"title\": \"...\",\n    \"description\": \"...\"\n  },\n  \"facebook\": {\n    \"description\": \"...\"\n  },\n  \"tiktok\": {\n    \"description\": \"...\"\n  }\n}\n\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [640, 660], "id": "1a2d03bd-8e11-467f-b7f7-760b2ba7a6c3", "name": "Structured Output Parser1"}, {"parameters": {"content": "## Youtube Agent\n", "height": 220, "width": 420, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 60], "id": "3d6829b1-2bb1-48da-bd54-c5c774fad453", "name": "Sticky Note5"}, {"parameters": {"content": "## Instagram Agent", "height": 220, "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 540], "id": "279acbba-b153-4bbe-9e5a-37bc809b37c6", "name": "Sticky Note6"}, {"parameters": {"amount": 60}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1260, 600], "id": "8cbc1d3d-1c8a-4f75-b89e-60fc01f029d2", "name": "Wait1", "webhookId": "7aab8a79-281e-42c2-aa21-e6ecab9370c2"}, {"parameters": {"content": "## Facebook Agent", "height": 240, "width": 820, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 780], "id": "541c2f70-8833-4eda-96d3-2fcaf37b492e", "name": "Sticky Note8"}, {"parameters": {"content": "## TikTok Agent", "height": 220, "width": 420}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 300], "id": "70284a40-9ca0-41a5-a20b-7aa263453be4", "name": "Sticky Note9"}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appkyh69NFvBjh0Jz", "mode": "list", "cachedResultName": "Content Publishing System 2.0", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz"}, "table": {"__rl": true, "value": "tblvjc0sIpiw7Ct8s", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appkyh69NFvBjh0Jz/tblvjc0sIpiw7Ct8s"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('Search Record').item.json.id }}", "TikTok": "Uploaded"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "Video Name", "displayName": "Video Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Details/Captions", "displayName": "Details/Captions", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Video Link (Gdrive)", "displayName": "Video Link (Gdrive)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Start Upload", "value": "Start Upload"}, {"name": "Completed", "value": "Completed"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "TikTok", "displayName": "TikTok", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": false}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Created Time", "displayName": "Created Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Last Modified Time", "displayName": "Last Modified Time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1280, 360], "id": "69597d66-d237-48a5-b3ff-f0ed985d88ac", "name": "TikTok Posted", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Search Record').item.json['Video Link (Gdrive)'] }}", "mode": "url"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [820, 340], "id": "bccd09ec-df9c-462d-9e3d-91dd2759a569", "name": "Download Final Vid", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [480, 640], "id": "fa5ecad0-7777-4565-a057-282488edaf4d", "name": "gpt4o mini", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"method": "POST", "url": "https://api.cloudinary.com/v1_1/{insert cloudinary cloud id}/upload", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "=data"}, {"name": "upload_preset", "value": "n8n_upload"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [840, 600], "id": "4a4d3027-f182-4258-8e37-35ba277ac919", "name": "Upload To Cloudinary", "credentials": {"httpHeaderAuth": {"id": "jRDusgPWYlKZodhd", "name": "Runway"}, "httpBasicAuth": {"id": "ZGYLJUR6PAnlFnLj", "name": "Cloudinary"}}}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 4, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1820, 580], "id": "44160b06-c9ad-481f-9919-98b11c976caf", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Create Container": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Instagram POST": {"main": [[{"node": "Instagram Posted", "type": "main", "index": 0}]]}, "Publishing Agent": {"main": [[{"node": "Download Final Vid", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Video Published", "type": "main", "index": 0}]]}, "Youtube Posted": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Instagram Posted": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Facebook Posted": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "Search Record": {"main": [[{"node": "Publishing Agent", "type": "main", "index": 0}]]}, "Youtube Post": {"main": [[{"node": "Youtube Posted", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Facebook Posted", "type": "main", "index": 0}]]}, "Start Upload": {"main": [[{"node": "Upload to Rupload", "type": "main", "index": 0}]]}, "Upload to Rupload": {"main": [[{"node": "Upload Reel to FB", "type": "main", "index": 0}]]}, "Upload Reel to FB": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "TikTok Post (NEW)": {"main": [[{"node": "TikTok Posted", "type": "main", "index": 0}]]}, "Schedule Trigger2": {"main": [[{"node": "Search Record", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Publishing Agent", "type": "ai_outputParser", "index": 0}]]}, "Wait1": {"main": [[{"node": "Instagram POST", "type": "main", "index": 0}]]}, "TikTok Posted": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Download Final Vid": {"main": [[{"node": "Upload To Cloudinary", "type": "main", "index": 0}]]}, "gpt4o mini": {"ai_languageModel": [[{"node": "Publishing Agent", "type": "ai_languageModel", "index": 0}]]}, "Upload To Cloudinary": {"main": [[{"node": "Create Container", "type": "main", "index": 0}, {"node": "Start Upload", "type": "main", "index": 0}, {"node": "TikTok Post (NEW)", "type": "main", "index": 0}, {"node": "Youtube Post", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4271cf94-6cd0-472d-981c-3d857ff6ba5c", "meta": {"instanceId": "d8c459f4ebcadd20c3258ade09582fe0f02de933363f6184259e0c9a36784090"}, "id": "SfxGbphBogKbgt1Q", "tags": [{"createdAt": "2025-05-22T09:01:19.074Z", "updatedAt": "2025-05-22T09:01:19.074Z", "id": "aXL9lfc8B5lPmkMH", "name": "W17: Content Publish2"}]}