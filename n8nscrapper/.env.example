# Configuration for n8n workflow scraper

# Target website
TARGET_URL=https://n8nworkflows.xyz

# Output directory for downloaded workflows
OUTPUT_DIR=./workflows

# Scraping configuration
MAX_CONCURRENT_REQUESTS=3
REQUEST_DELAY_MS=2000
MAX_RETRIES=3
TIMEOUT_MS=30000

# Browser configuration
HEADLESS=true
USER_DATA_DIR=./browser-data

# Proxy configuration (optional)
# PROXY_URL=http://proxy:port
# PROXY_USERNAME=username
# PROXY_PASSWORD=password

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/scraper.log

# Rate limiting
REQUESTS_PER_MINUTE=30
BURST_LIMIT=5

# Cloudflare bypass
USE_STEALTH=true
WAIT_FOR_CLOUDFLARE=true
CLOUDFLARE_TIMEOUT=30000
