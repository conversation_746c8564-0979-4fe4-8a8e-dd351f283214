{"name": "IG_VoiceChangerElevelabs", "nodes": [{"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "2f039df5-3b63-489e-ba58-0b053e7b87af", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [580, -40], "webhookId": "3915bb32-e884-43ac-8036-8162bdb4b365", "typeVersion": 1, "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "509fa76a-b8d1-4c5f-a24e-21042204b9a1", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [860, -40], "typeVersion": 3.2}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "30d31336-2dae-483e-b56d-3ead84d51fab", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [820, -260], "typeVersion": 1.2, "webhookId": "a6b19c46-52db-4bb5-8640-9a8798ae08b2", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "880b772e-0b94-4ab6-97f3-97cb1de40405", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [1040, -140], "typeVersion": 1.2, "webhookId": "eb075354-14a8-43b4-8925-c79b3d17ec86", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "79c4d984-78fa-4312-933e-2c2fe2f1daaf", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [1100, 40], "typeVersion": 1.2, "webhookId": "9b94fc45-c80c-4c6e-b2d5-2fde634bbdf9", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"method": "POST", "url": "https://api.elevenlabs.io/v1/speech-to-speech/7898AMdRTtesf0Y9zS54?output_format=mp3_44100_128", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "model_id", "value": "eleven_multilingual_sts_v2"}, {"parameterType": "formBinaryData", "name": "audio", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, -140], "id": "98b5c33f-7456-49eb-a9e9-7c0beb77c2a6", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "76d5k5Kh0OdqkLbB", "name": "Elevenlabs"}}}, {"parameters": {"operation": "sendAudio", "chatId": "={{ $('Listen for incoming events').item.json.message.chat.id }}", "binaryData": true, "additionalFields": {"fileName": "tranformed_audio.mpeg"}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1520, -140], "id": "221f27b2-dd63-46df-aa4d-ff0751469fd0", "name": "Telegram", "webhookId": "e14a7eb1-1c43-457e-95fb-363956b8d293", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}], "pinData": {}, "connections": {"Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "Download voice file": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "03d1f5b7-91f1-46d0-8ac1-aee0f490b488", "meta": {"templateCredsSetupCompleted": true, "instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "iDirdshssstPQ7R9", "tags": []}