{"name": "Auto Image Post Generator", "nodes": [{"parameters": {"formTitle": "Generate Blog Post", "formFields": {"values": [{"fieldLabel": "topic", "requiredField": true}, {"fieldLabel": "audience", "requiredField": true}, {"fieldLabel": "language", "placeholder": "english", "requiredField": true}, {"fieldLabel": "targetEmail", "placeholder": "<EMAIL>", "requiredField": true}, {"fieldLabel": "goalPlatform", "placeholder": "LinkedIn", "requiredField": true}, {"fieldLabel": "tone", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "professional"}, {"option": "personal (brand)"}, {"option": "friendly (to close friends)"}, {"option": "story (brand)"}, {"option": "story (to close friends)"}]}, "requiredField": true}, {"fieldLabel": "visuals", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "realistic"}, {"option": "illustration"}, {"option": "studio gibli style"}, {"option": "artsy"}]}, "requiredField": true}, {"fieldLabel": "Type of content", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Educational and insightful"}, {"option": "Fun and entertaining"}, {"option": "Interesting and cool"}, {"option": "Sad and memorable"}, {"option": "Sexy and arousing"}]}, "requiredField": true}, {"fieldLabel": "Emoji <PERSON>", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "No Emoji use"}, {"option": "Light on emojis (use only when highly relevant and minimal)"}, {"option": "Use emojis if possible and fitting"}, {"option": "Use emojis as often as possible"}]}, "requiredField": true}, {"fieldLabel": "Attribution included?", "fieldType": "dropdown", "fieldOptions": {"values": [{}, {"option": "Includes proper source attribution (e.g., “according to [source]”)"}]}}, {"fieldLabel": "Hashtags", "fieldType": "dropdown", "fieldOptions": {"values": [{}, {"option": "Contains relevant hashtags to improve visibility"}]}}, {"fieldLabel": "Call to action", "placeholder": "Ends with a clear call to action (e.g., asking for thoughts, feedback, or shares)"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-560, -80], "id": "a5898534-bc0d-4748-a23a-0ec8b288bf40", "name": "On form submission", "webhookId": "929b35ea-8f9a-4649-b2b6-1e631935b210"}, {"parameters": {"promptType": "define", "text": "=Topic of post: {{ $json.topic }}\n\nLanguage of post: {{ $json.language }}\n\nTarget audience of post: {{ $json.audience }}\n\nMake sure its recent and in 2025", "options": {"systemMessage": "=# Overview\nYou are an AI agent specialized in creating professional, educational, and engaging {{ $json.goalPlatform }} post based on any topic provided by the user.\n\n## Objectives:\nAlways begin by conducting a real-time search using the <PERSON>ly tool to gather the most accurate, up-to-date information on the topic. The post should be written to appeal to the provided target audience.\n\nBased on your research, generate a well-structured {{ $json.goalPlatform }} post that:\n- Starts with an engaging hook\n- {{ $json.goalPlatform }} specific in tone\n- Tone is {{ $json.tone }}\n- Clear and easy to read\n- {{ $json['Type of content'] }}\n- {{ $json['Emoji Use'] }}\n- {{ $json['Attribution included?'] }}\n- {{ $json.Hashtags }}\n- {{ $json['Call to action'] }}\n\n## Output Instructions:\n- Your ONLY output should be the final {{ $json.goalPlatform }} post text.\n- Do not include explanations, notes, or anything beyond the post itself.\n\n## Example Workflow:\n- Receive a topic (e.g., “The ROI of warehouse automation”)\n- Use Tavily to search and gather recent information or case studies\n- Draft a {{ $json.goalPlatform }} post using that research\n- Format it with source citations, clean structure, optional hashtags, and a call to action"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-320, -80], "id": "fc81866a-d8aa-4350-9ac7-836adaf9cac4", "name": "AI Agent"}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-20, 140], "id": "60be4c9f-738a-423c-8b30-964c45253d5a", "name": "4.1", "credentials": {"openRouterApi": {"id": "7gFuLBYAdUiSiCO5", "name": "OpenRouter account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('On form submission').item.json.goalPlatform }} Post:\n\n{{ $json.output }}", "options": {"systemMessage": "=# Overview\nYou are an AI agent that transforms {{ $('On form submission').item.json.goalPlatform }} posts into visual prompt descriptions for generating graphic marketing materials. These visuals are designed to be paired with the post on {{ $('On form submission').item.json.goalPlatform }}, helping communicate the message in a visually engaging, brand-aligned way.\n\n## Objective:\n- Read and analyze the given {{ $('On form submission').item.json.goalPlatform }} post.\n- Identify the main message, insight, or takeaway from the post.\n- Create a clear and compelling graphic prompt that can be used with a text-to-image generator.\n- The result should look {{ $('On form submission').item.json.visuals }} — that:\n1) Feels polished, modern, and engaging\n2) Created for {{ $('On form submission').item.json.goalPlatform }}\n\n## Output Instructions:\nOutput only the final image prompt. Do not output quotation marks.\n\nDo not repeat or rephrase the {{ $('On form submission').item.json.goalPlatform }} post.\n\nDo not add any explanations or extra content — just the image prompt.\n\nNever leave things blank like “Header area reserved for customizable callout text”\n\nOutput numeric stats when available in the original post\n\n## Style Guidelines:\nThink like a brand designer or marketing creative.\n\nYou can mention layout suggestions (e.g., “split screen design,” “header with bold title and subtle background illustration”).\n\nAssume the output will be generated using AI image tools — your prompt should guide those tools effectively.\n\n## Example Prompt Format:\nA modern flat-style graphic showing a human brain connected to mechanical gears, representing the fusion of AI and automation. Minimalist background, soft gradients, clean sans-serif text placement"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [40, -80], "id": "b883f457-24dd-49f9-b9c2-a43511c7f509", "name": "AI Agent1"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.output }}"}, {"name": "size", "value": "1024x1024"}, {"name": "moderation", "value": "low"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, -80], "id": "0c8cef42-6bc0-44bb-bd12-99d540d4b0bf", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "VpbhAqVyynzzb44F", "name": "OpenAI"}}}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [620, -80], "id": "44682399-85f8-4039-a8b3-00c4a08b9f7e", "name": "Convert to File"}, {"parameters": {"sendTo": "={{ $('On form submission').item.json.targetEmail }}", "subject": "Post generated", "message": "=<!-- Post summary card -->\n<article style=\"font-family: Arial, sans-serif; max-width: 620px; margin: 0 auto; line-height: 1.55;\">\n  <!-- Header -->\n  <h2 style=\"text-align: center; color: #2c3e50; margin-bottom: 1.2rem;\">\n    Your post is ready 🚀\n  </h2>\n\n  <!-- Key-value details -->\n  <dl style=\"margin: 0 0 1.5rem 0;\">\n    <dt style=\"font-weight: 600;\">Topic</dt>\n    <dd>{{ $('On form submission').item.json.topic }}</dd>\n\n    <dt style=\"font-weight: 600; margin-top: .6rem;\">Audience</dt>\n    <dd>{{ $('On form submission').item.json.audience }}</dd>\n\n    <dt style=\"font-weight: 600; margin-top: .6rem;\">Platform</dt>\n    <dd>{{ $('On form submission').item.json.goalPlatform }}</dd>\n\n    <dt style=\"font-weight: 600; margin-top: .6rem;\">Language</dt>\n    <dd>{{ $('On form submission').item.json.language }}</dd>\n\n    <dt style=\"font-weight: 600; margin-top: .6rem;\">Time</dt>\n    <dd>{{ $('On form submission').item.json.submittedAt }}</dd>\n  </dl>\n\n  <!-- Token-usage line -->\n  <p style=\"margin: 0 0 1.5rem 0;\">\n    <strong>Token usage:</strong>\n    {{ $('HTTP Request').item.json.usage.total_tokens }} @ 4 $ / 100 000&nbsp;= \n    {{ $('HTTP Request').item.json.usage.total_tokens / 100000 * 4 }} $\n  </p>\n\n  <!-- Post body -->\n  <h3 style=\"margin: 0 0 .6rem 0;\">Post text</h3>\n  <blockquote\n    style=\"\n      background: #f9f9f9;\n      padding: 1rem 1.25rem;\n      border-left: 4px solid #2c3e50;\n      border-radius: 4px;\n      white-space: pre-wrap;\n    \"\n  >\n    {{ $('AI Agent').item.json.output }}\n  </blockquote>\n</article>\n", "options": {"attachmentsUi": {"attachmentsBinary": [{}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [840, -80], "id": "bb542b0c-6105-4053-8e63-216c5ff953a9", "name": "Gmail", "webhookId": "8a83d6e5-0d70-4403-b1c1-9e6fdcacd848", "credentials": {"gmailOAuth2": {"id": "FP7FiXRDASdZyHF0", "name": "Gmail account"}}}, {"parameters": {"toolDescription": "Tavily API", "method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"query\": \"{searchTerm}\",\n    \"topic\": \"general\",\n    \"search_depth\": \"advanced\",\n    \"time_range\": null,\n    \"days\": 7,\n    \"chunks_per_source\": 4,\n    \"include_answer\": true,\n    \"include_images\": false,\n    \"include_image_descriptions\": false,\n    \"include_raw_content\": false,\n    \"max_results\": 1,\n    \"include_domains\": [],\n    \"exclude_domains\": []\n}", "placeholderDefinitions": {"values": [{"name": "searchTerm", "description": "What the user is searching for"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [-180, 140], "id": "fd9f644e-3efe-4950-a15a-d19922445ebc", "name": "Tavily API", "credentials": {"httpHeaderAuth": {"id": "6viXv2ZpqIBxmDgK", "name": "Head<PERSON> Auth account 1"}}}], "pinData": {}, "connections": {"On form submission": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "4.1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "Tavily API": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "785915f3-d3bf-4f09-8efa-f422d1f33848", "meta": {"templateCredsSetupCompleted": true, "instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "0izglojJk5tL9SGb", "tags": []}