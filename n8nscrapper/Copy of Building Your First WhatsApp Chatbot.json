{"name": "Building Your First WhatsApp Chatbot", "nodes": [{"parameters": {"updates": ["messages"], "options": {}}, "id": "77ee6494-4898-47dc-81d9-35daf6f0beea", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.whatsAppTrigger", "position": [2260, 580], "webhookId": "aaa71f03-f7af-4d18-8d9a-0afb86f1b554", "typeVersion": 1}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "57210e27-1f89-465a-98cc-43f890a4bf58", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2860, 660], "typeVersion": 1}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "=whatsapp-75-{{ $json.messages[0].from }}"}, "id": "e1053235-0ade-4e36-9ad2-8b29c78fced8", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [2980, 660], "typeVersion": 1.2}, {"parameters": {"name": "query_product_brochure", "description": "Call this tool to query the product brochure. Valid for the year 2024."}, "id": "69f1b78b-7c93-4713-863a-27e04809996f", "name": "Vector Store Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "position": [3100, 660], "typeVersion": 1}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "170e8f7d-7e14-48dd-9f80-5352cc411fc1", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [3100, 940], "typeVersion": 1}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "ee78320b-d407-49e8-b4b8-417582a44709", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3340, 800], "typeVersion": 1}, {"parameters": {}, "id": "9dd89378-5acf-4ca6-8d84-e6e64254ed02", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [900, 620], "typeVersion": 1}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "e68fc137-1bcb-43f0-b597-3ae07f380c15", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "position": [1660, 840], "typeVersion": 1}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $('Extract from File').item.json.text }}", "options": {}}, "id": "2d31e92b-18d4-4f6b-8cdb-bed0056d50d7", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "position": [1800, 840], "typeVersion": 1}, {"parameters": {"chunkSize": 2000, "chunkOverlap": {}, "options": {}}, "id": "ca0c015e-fba2-4dca-b0fe-bac66681725a", "name": "Recursive Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "position": [1800, 960], "typeVersion": 1}, {"parameters": {"operation": "pdf", "options": {}}, "id": "63abb6b2-b955-4e65-9c63-3211dca65613", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [1260, 620], "typeVersion": 1}, {"parameters": {"url": "https://usa.yamaha.com/files/download/brochure/1/1474881/Yamaha-Powered-Loudspeakers-brochure-2024-en-web.pdf", "options": {}}, "id": "be2add9c-3670-4196-8c38-82742bf4f283", "name": "get Product Brochure", "type": "n8n-nodes-base.httpRequest", "position": [1080, 620], "typeVersion": 4.2}, {"parameters": {"operation": "send", "phoneNumberId": "477115632141067", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "={{ $json.output }}", "additionalFields": {"previewUrl": false}}, "id": "1ae5a311-36d7-4454-ab14-6788d1331780", "name": "Reply To User", "type": "n8n-nodes-base.whatsApp", "position": [3720, 580], "typeVersion": 1, "webhookId": "d0ce4009-6ee5-419d-a954-36e28606a595"}, {"parameters": {"operation": "send", "phoneNumberId": "477115632141067", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.messages[0].from }}", "textBody": "=I'm unable to process non-text messages. Please send only text messages. Thanks!", "additionalFields": {"previewUrl": false}}, "id": "b6efba81-18b0-4378-bb91-51f39ca57f3e", "name": "Reply To User1", "type": "n8n-nodes-base.whatsApp", "position": [2660, 940], "typeVersion": 1, "webhookId": "1a392c53-48dc-4678-b5c0-46b2da940c66"}, {"parameters": {"memoryKey": "whatsapp-75"}, "id": "52decd86-ac6c-4d91-a938-86f93ec5f822", "name": "Product Catalogue", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [3100, 800], "typeVersion": 1}, {"parameters": {"content": "## 1. Download Product Brochure PDF\n[Read more about the HTTP Request Tool](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest)\n\nImport your marketing PDF document to build your vector store. This will be used as the knowledgebase by the Sales AI Agent.\n\nFor this demonstration, we'll use the HTTP request node to import the YAMAHA POWERED LOUDSPEAKERS 2024 brochure ([Source](https://usa.yamaha.com/files/download/brochure/1/1474881/Yamaha-Powered-Loudspeakers-brochure-2024-en-web.pdf)) and an Extract from File node to extract the text contents. ", "height": 434.6875, "width": 640.4375, "color": 7}, "id": "6dd5a652-2464-4ab8-8e5f-568529299523", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [820, 400], "typeVersion": 1}, {"parameters": {"content": "## 2. Create Product Brochure Vector Store\n[Read more about the In-Memory Vector Store](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreinmemory/)\n\nVector stores are powerful databases which serve the purpose of matching a user's questions to relevant parts of a document. By creating a vector store of our product catalog, we'll allow users to query using natural language.\n\nTo keep things simple, we'll use the **In-memory Vector Store** which comes built-in to n8n and doesn't require a separate service. For production deployments, I'd recommend replacing the in-memory vector store with either [Qdrant](https://qdrant.tech) or [Pinecone](https://pinecone.io).", "height": 731.1875, "width": 614.6875, "color": 7}, "id": "116663bc-d8d6-41a5-93dc-b219adbb2235", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1480, 400], "typeVersion": 1}, {"parameters": {"mode": "insert", "memoryKey": "whatsapp-75", "clearStore": true}, "id": "86bd5334-d735-4650-aeff-06230119d705", "name": "Create Product Catalogue", "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "position": [1660, 660], "typeVersion": 1}, {"parameters": {"content": "## 3. Use the WhatsApp Trigger\n[Learn more about the WhatsApp Trigger](https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.whatsapptrigger/)\n\nThe WhatsApp Trigger allows you to receive incoming WhatsApp messages from customers. It requires a bit of setup so remember to follow the documentation carefully! Once ready however, it's quite easy to build powerful workflows which are easily accessible to users.\n\nNote that WhatsApp can send many message types such as audio and video so in this demonstration, we'll filter them out and just accept the text messages.", "height": 484.1875, "width": 546.6875, "color": 7}, "id": "b8078b0d-cbd7-423f-bb30-13902988be38", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2160, 320], "typeVersion": 1}, {"parameters": {"content": "### Want to handle all message types?\nCheck out my other WhatsApp template in my creator page! https://n8n.io/creators/jimleuk/", "height": 92, "width": 338}, "id": "5bf7ed07-282b-4198-aa90-3e5ae5180404", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2540, 1140], "typeVersion": 1}, {"parameters": {"content": "### 3a. Handle Unsupported Message Types\nFor non-text messages, we'll just reply with a simple message to inform the sender.", "height": 311.1875, "width": 337.6875, "color": 7}, "id": "a3661b59-25d2-446e-8462-32b4d692b69d", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2540, 820], "typeVersion": 1}, {"parameters": {"content": "## 4. Sales AI Agent Responds To Customers\n[Learn more about using AI Agents](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/)\n\nn8n's AI agents are powerful nodes which make it incredibly easy to use state-of-the-art AI in your workflows. Not only do they have the ability to remember conversations per individual customer but also tap into resources such as our product catalogue vector store to pull factual information and data for every question.\n\nIn this demonstration, we use an AI agent which is directed to help the user navigate the product brochure. A Chat memory subnode is attached to identify and keep track of the customer session. A Vector store tool is added to allow the Agent to tap into the product catalogue knowledgebase we built earlier.", "height": 929.1875, "width": 746.6875, "color": 7}, "id": "ea3c9ee1-505a-40e7-82fe-9169bdbb80af", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2740, 180], "typeVersion": 1}, {"parameters": {"content": "## 5. Repond to WhatsApp User\n[Learn more about the WhatsApp Node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.whatsapp/)\n\nThe WhatsApp node is the go-to if you want to interact with WhatsApp users. With this node, you can send text, images, audio and video messages as well as use your WhatsApp message templates.\n\nHere, we'll keep it simple by replying with a text message which is the output of the AI agent.", "height": 484.1875, "width": 495.4375, "color": 7}, "id": "5c72df8d-bca1-4634-b1ed-61ffec8bd103", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [3520, 300], "typeVersion": 1}, {"parameters": {"content": "## Try It Out!\n\n### This n8n template builds a simple WhatsApp chabot acting as a Sales Agent. The Agent is backed by a product catalog vector store to better answer user's questions.\n\n* This template is in 2 parts: creating the product catalog vector store and building the WhatsApp AI chatbot.\n* A product brochure is imported via HTTP request node and its text contents extracted.\n* The text contents are then uploaded to the in-memory vector store to build a knowledgebase for the chatbot.\n* A WhatsApp trigger is used to capture messages from customers where non-text messages are filtered out.\n* The customer's message is sent to the AI Agent which queries the product catalogue using the vector store tool.\n* The Agent's response is sent back to the user via the WhatsApp node.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!", "height": 582.6283033962263, "width": 401.25}, "id": "48ec809f-ca0e-4052-b403-9ad7077b3fff", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "text"}]}, "renameOutput": true, "outputKey": "Supported"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "89971d8c-a386-4e77-8f6c-f491a8e84cb6", "operator": {"type": "string", "operation": "notEquals"}, "leftValue": "={{ $json.messages[0].type }}", "rightValue": "text"}]}, "renameOutput": true, "outputKey": "Not Supported"}]}, "options": {}}, "id": "87cf9b41-66de-49a7-aeb0-c8809191b5a0", "name": "Handle Message Types", "type": "n8n-nodes-base.switch", "position": [2460, 580], "typeVersion": 3.2}, {"parameters": {"content": "### You only have to run this part once!\nRun this step to populate our product catalogue vector. Run again if you want to update the vector store with a new version.", "height": 114.53583720930231, "width": 345.10906976744184, "color": 5}, "id": "e52f0a50-0c34-4c4a-b493-4c42ba112277", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [820, 840], "typeVersion": 1}, {"parameters": {"content": "### Activate your workflow to use!\nTo start using the WhatsApp chatbot, you'll need to activate the workflow. If you are self-hosting ensure WhatsApp is able to connect to your server.", "height": 107.02804651162779, "width": 364.6293255813954, "color": 5}, "id": "c1a7d6d1-191e-4343-af9f-f2c9eb4ecf49", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [2160, 820], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "={{ $json.messages[0].text.body }}", "options": {"systemMessage": "You are an assistant working for a company who sells Yamaha Powered Loudspeakers and helping the user navigate the product catalog for the year 2024. Your goal is not to facilitate a sale but if the user enquires, direct them to the appropriate website, url or contact information.\n\nDo your best to answer any questions factually. If you don't know the answer or unable to obtain the information from the datastore, then tell the user so."}}, "id": "a36524d0-22a6-48cc-93fe-b4571cec428a", "name": "AI Sales Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [2860, 460], "typeVersion": 1.6}], "pinData": {}, "connections": {"AI Sales Agent": {"main": [[{"node": "Reply To User", "type": "main", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Handle Message Types", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Product Catalogue", "type": "ai_embedding", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Create Product Catalogue", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Sales Agent", "type": "ai_languageModel", "index": 0}]]}, "Product Catalogue": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Sales Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Create Product Catalogue", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Create Product Catalogue", "type": "ai_document", "index": 0}]]}, "Handle Message Types": {"main": [[{"node": "AI Sales Agent", "type": "main", "index": 0}], [{"node": "Reply To User1", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Sales Agent", "type": "ai_memory", "index": 0}]]}, "get Product Brochure": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "get Product Brochure", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "2465", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}