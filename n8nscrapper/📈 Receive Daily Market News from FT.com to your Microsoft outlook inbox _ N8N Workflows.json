{"nodes": [{"parameters": {}, "id": "d2a24a9b-9cf3-4de0-82e7-5d858658d4b4", "name": "Extract specific content", "type": "n8n-nodes-base.html", "position": [1376, 384], "notesInFlow": true, "typeVersion": 1.2, "notes": "Extract selected headlines, editor&apos;s picks, spotlight etc."}, {"parameters": {"options": {}}, "id": "38af5df2-65ce-4f04-aed3-6f71d81a37df", "name": "Get financial news online", "type": "n8n-nodes-base.httpRequest", "position": [1152, 384], "notesInFlow": true, "typeVersion": 4.2, "notes": "Url : https://www.ft.com/"}, {"parameters": {"rule": {"interval": [{}]}}, "id": "764b2209-bf20-4feb-b000-fa261459a617", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [928, 384], "typeVersion": 1.2}, {"parameters": {"options": {}}, "id": "96b337ba-6fe7-47ec-8385-58bfc6c789cb", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1776, 560], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "925eabf3-3619-4da2-be2c-bda97c605d4d", "name": "Gather the elements", "type": "n8n-nodes-base.set", "position": [1600, 384], "typeVersion": 3.4}, {"parameters": {"options": {}}, "id": "5445b14f-25e8-4759-82d4-985961ca7fdd", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1776, 384], "typeVersion": 1.6}, {"parameters": {"additionalFields": {}}, "id": "7f2b6e9a-8b14-4083-a05c-3b76aae601a8", "name": "Send the summary by e-mail", "type": "n8n-nodes-base.microsoftOutlook", "position": [2112, 384], "typeVersion": 2, "webhookId": "7674b939-b380-4130-a779-c7e45a723809"}], "connections": {"Extract specific content": {"main": [[{"node": "Gather the elements", "type": "main", "index": 0}]]}, "Get financial news online": {"main": [[{"node": "Extract specific content", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get financial news online", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Gather the elements": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send the summary by e-mail", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "2f281fde0de64f39316b38bf7aeff647de66c777fd2c9178aac0dbc0cd948eca"}}