{"name": "28_enrich leads n8n", "nodes": [{"parameters": {"formTitle": "Get Your Free Marketing Audit", "formDescription": "Discover untapped opportunities to grow your business. Our experts will analyze your current marketing and provide actionable insights.", "formFields": {"values": [{"fieldLabel": "First Name", "requiredField": true}, {"fieldLabel": "Last Name", "requiredField": true}, {"fieldLabel": "Business Email", "fieldType": "email", "requiredField": true}, {"fieldLabel": "Phone Number"}, {"fieldLabel": "Company Name", "requiredField": true}, {"fieldLabel": "Company Website"}, {"fieldLabel": "Job Title", "requiredField": true}, {"fieldLabel": "Company Size", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "1-10 employees"}, {"option": "11-50 employees"}, {"option": "51-200 employees"}, {"option": "201-1000 employees"}, {"option": "1000+ employees"}]}, "requiredField": true}, {"fieldLabel": "Industry", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "Technology"}, {"option": "Healthcare"}, {"option": "Finance"}, {"option": "Retail"}, {"option": "Manufacturing"}, {"option": "Education"}, {"option": "Real Estate"}, {"option": "Consulting"}, {"option": "Other"}]}, "requiredField": true}, {"fieldLabel": "What's your biggest marketing challenge?", "fieldType": "textarea", "placeholder": "Tell us about your current marketing goals and challenges..."}, {"fieldType": "hiddenField", "fieldName": "Industry", "fieldValue": "Other"}]}, "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-280, 240], "id": "29f6fc39-a7b9-4409-afe1-9b7ec7d9c006", "name": "On form submission", "webhookId": "d3d82a38-0c94-4089-9da3-90689ccf6ef2"}, {"parameters": {"url": "https://api.hunter.io/v2/email-finder", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "first_name", "value": "={{ $('On form submission').item.json['First Name'] }}"}, {"name": "domain", "value": "={{ $json.data.domain }}"}, {"name": "last_name", "value": "={{ $('On form submission').item.json['Last Name'] }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 240], "id": "a7e6f295-8396-4429-8e09-f31687bfdfce", "name": "Person Enrichment", "credentials": {"httpHeaderAuth": {"id": "LTIc4GyQAgMxmBN7", "name": "Shotstack Test"}, "httpQueryAuth": {"id": "dOPvUo3zDscibtDC", "name": "Hunter io"}}}, {"parameters": {"url": "https://api.hunter.io/v2/companies/find", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "domain", "value": "={{ $json['Company Website'] }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-60, 240], "id": "ac8bdb1e-5beb-4d58-b527-78164ab32c66", "name": "Company Enrichment", "credentials": {"httpQueryAuth": {"id": "dOPvUo3zDscibtDC", "name": "Hunter io"}}}, {"parameters": {"assignments": {"assignments": [{"id": "b3a35983-93a3-4871-a8d9-d2dd9da59f27", "name": "output", "value": "=company Name: {{ $json.data.company }}\ncompany Website: {{ $json.data.domain }}\ncompanySize:  {{ $('Company Enrichment').item.json.data.metrics.employees }} employees\n  revenue: {{ $('Company Enrichment').item.json.data.metrics.annualRevenue }}\n  industry: {{ $('On form submission').item.json.Industry }}\n  technologies: {{ $('Company Enrichment').item.json.data.tech }}\n  foundedYear: {{ $('Company Enrichment').item.json.data.foundedYear }}\n  companyLinkedIn: {{ $('Company Enrichment').item.json.data.linkedin.handle }}\n  contactFirstName: {{ $('On form submission').item.json['First Name'] }}\n contactLastName: {{ $('On form submission').item.json['Last Name'] }}\ncontactEmail: {{ $('On form submission').item.json['Business Email'] }}\ncontactReasonforReachingOut: {{ $('On form submission').item.json['What\\'s your biggest marketing challenge?'] }}\n  contactFullJobTitle: {{ $json.data.position }}\n  linkedInProfile: {{ $json.data.linkedin_url }}\n  verifiedPhone: {{ $json.data.phone_number }}\n  emailStatus: {{ $json.data.status }}\n", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [600, 240], "id": "0aa85d33-0537-43d5-aa86-64f237756ebf", "name": "<PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $json.output }}", "hasOutputParser": true, "options": {"systemMessage": "=You are a B2B lead scoring expert for a marketing agency. This lead has reached out to us for help. Score this lead from 0-100 based on the following criteria:\n\nSCORING CRITERIA:\n- Company Size: 500+ employees (30pts), 100-500 (20pts), 50-100 (10pts), <50 (5pts)\n- Industry Match: Technology/Software/SaaS (25pts), Business Services (15pts), Other (5pts)\n- Seniority: C-<PERSON> (40pts), VP (35pts), Director (30pts), Manager (20pts), Senior (15pts), IC (5pts)\n- Technology Stack: Each relevant tech tool adds 10pts (Salesforce, HubSpot, Marketo, etc.)\n- Email Verification: Verified (10pts), Unverified (0pts)\nAlso draft a reply email acknowledging them reaching out. Always sign off as Tom the AI assistant \n\nOUTPUT REQUIRED:\nReturn ONLY a JSON object with:\n{\n  \"leadScore\": [number 0-100],\n  \"leadGrade\": \"[A/B/C/D based on score ranges: A=80+, B=60-79, C=40-59, D=0-39]\",\n  \"isQualified\": [true if score ≥ 60, false otherwise],\n  \"reasoning\": \"[brief 1-2 sentence explanation of score]\"\n \"Email Subject\": [Reply to the lead/contact since they have filled out a form]\n \"Email Body\": [Reply to the lead/contact since they have filled out a form **ONLY IN HTML FORMAT**]\n \"Seniority\": \"Lead's seniority level\",\n  \"Department\": \"Lead's Department\"\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [820, 240], "id": "464e1d8a-d592-4988-a59e-fafeb5eda118", "name": "AI Agent"}, {"parameters": {"options": {"responseFormat": "json_object"}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [780, 440], "id": "79408b66-f96e-4787-9fa5-a6575b50dbb7", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "OKXl16r8Jnur9uD1", "name": "OpenRouter account 1"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"leadScore\": \"\",\n  \"leadGrade\": \"[A/B/C/D based on score ranges: A=80+, B=60-79, C=40-59, D=0-39]\",\n  \"isQualified\": \" \",\n  \"reasoning\": \" \",\n   \"Email Subject\": \"\",\n \"Email Body\": \"\",\n  \"Seniority\": \"\",\n  \"Department\": \"\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1100, 460], "id": "18edb6c5-4387-4247-a965-93c12baf7bb5", "name": "Structured Output Parser"}, {"parameters": {"url": "https://api.hunter.io/v2/email-verifier", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "email", "value": "={{ $json.data.email }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [380, 240], "id": "98a9face-9cb6-4f32-b680-c122809ffd7e", "name": "Email Veri<PERSON>r", "credentials": {"httpQueryAuth": {"id": "dOPvUo3zDscibtDC", "name": "Hunter io"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1U0U8OfubtcHBO6gRE3DEmQPDMOUDUmwhVJuttvnib4I", "mode": "list", "cachedResultName": "Enriched Lead Database", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1U0U8OfubtcHBO6gRE3DEmQPDMOUDUmwhVJuttvnib4I/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1U0U8OfubtcHBO6gRE3DEmQPDMOUDUmwhVJuttvnib4I/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"timestamp": "={{ $('On form submission').item.json.submittedAt }}", "source": "Form ", "first_name": "={{ $('On form submission').item.json['First Name'] }}", "last_name": "={{ $('On form submission').item.json['Last Name'] }}", "email": "={{ $('On form submission').item.json['Business Email'] }}", "phone": "={{ $('On form submission').item.json['Phone Number'] }}", "job_title_original": "={{ $('On form submission').item.json['Job Title'] }}", "job_title_enriched": "={{ $('Person Enrichment').item.json.data.position }}", "company_name": "={{ $('Person Enrichment').item.json.data.company }}", "company_domain": "={{ $('Person Enrichment').item.json.data.domain }}", "company_size": "={{ $('Company Enrichment').item.json.data.metrics.employees }}", "company_revenue": "={{ $('Company Enrichment').item.json.data.metrics.annualRevenue }}", "company_founded": "={{ $('Company Enrichment').item.json.data.foundedYear }}", "company_location": "={{ $('Company Enrichment').item.json.data.location }}", "industry": "={{ $('Company Enrichment').item.json.data.category.industry }}", "linkedin_company": "={{ $('Company Enrichment').item.json.data.linkedin.handle }}", "technologies": "={{ $('Company Enrichment').item.json.data.tech.toJsonString() }}", "linkedin_profile": "={{ $('Person Enrichment').item.json.data.linkedin_url }}", "email_verified": "={{ $('Email Verifier').item.json.data.status }}", "lead_score": "={{ $('AI Agent').item.json.output.leadScore }}", "lead_grade": "={{ $('AI Agent').item.json.output.leadGrade }}", "is_qualified": "={{ $('AI Agent').item.json.output.isQualified }}", "qualification_reason": "={{ $('AI Agent').item.json.output.reasoning }}", "seniority": "={{ $('AI Agent').item.json.output.Seniority }}", "department": "={{ $('AI Agent').item.json.output.Department }}"}, "matchingColumns": [], "schema": [{"id": "timestamp", "displayName": "timestamp", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "source", "displayName": "source", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "first_name", "displayName": "first_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "last_name", "displayName": "last_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "job_title_original", "displayName": "job_title_original", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "job_title_enriched", "displayName": "job_title_enriched", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "seniority", "displayName": "seniority", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "department", "displayName": "department", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_name", "displayName": "company_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_domain", "displayName": "company_domain", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_size", "displayName": "company_size", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_revenue", "displayName": "company_revenue", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "industry", "displayName": "industry", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_founded", "displayName": "company_founded", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_location", "displayName": "company_location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "technologies", "displayName": "technologies", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "linkedin_company", "displayName": "linkedin_company", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "linkedin_profile", "displayName": "linkedin_profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email_verified", "displayName": "email_verified", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "lead_score", "displayName": "lead_score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "lead_grade", "displayName": "lead_grade", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "is_qualified", "displayName": "is_qualified", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "qualification_reason", "displayName": "qualification_reason", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1420, 240], "id": "d475eff4-946b-4dbe-9de6-c5fccbb81ac2", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "IB4bnQ037MQjLvzB", "name": "Ecommojo-Google Sheets account"}}}, {"parameters": {"sendTo": "={{ $('On form submission').item.json['Business Email'] }}", "subject": "={{ $json.output['Email Subject'] }}", "message": "={{ $json.output['Email Body'] }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1200, 240], "id": "415987a8-6338-4998-9c90-2fff3af8e01c", "name": "Send Email", "webhookId": "12c7948c-8382-4807-902a-6c9cee1fabd1", "credentials": {"gmailOAuth2": {"id": "PYYey3KbxzpLg75J", "name": "Loopsera Gmail account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.lead_grade }}", "rightValue": "A", "operator": {"type": "string", "operation": "equals"}, "id": "4ed7f736-f96a-4008-9686-fb570a688768"}], "combinator": "and"}, "renameOutput": true, "outputKey": "High-Value Leads "}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cb1c788e-c9bf-4878-ac72-1923ac6dc7be", "leftValue": "={{ $json.lead_grade }}", "rightValue": "B", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Medium Leads"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d6ae5c00-9e7c-4abc-80f6-0d47411d8feb", "leftValue": "={{ $json.lead_grade }}", "rightValue": "C", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Low Leads"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "686044f9-5180-4862-8d65-c6070ec873f7", "leftValue": "={{ $json.lead_grade }}", "rightValue": "D", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Cold Lead"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [1640, 220], "id": "64472215-a8be-4f40-ae41-73e703edec8b", "name": "Switch"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Hot Lead Alert! ", "emailType": "text", "message": "=Hi Team,\n{{ $('Google Sheets').item.json.first_name }} {{ $('Google Sheets').item.json.last_name }} from {{ $('Google Sheets').item.json.company_name }} just filled out a form inquiring about {{ $('On form submission').item.json['What\\'s your biggest marketing challenge?'] }}. Please get back to him ASAP. \n\nThanks,\nAI Assistant.", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1880, 80], "id": "b3f84818-ff26-4ccb-951b-9964e86af375", "name": "Inform Sales", "webhookId": "b9a7164e-ea89-4c20-ab93-daf546ed9e69", "credentials": {"gmailOAuth2": {"id": "PYYey3KbxzpLg75J", "name": "Loopsera Gmail account"}}}, {"parameters": {"content": "## Thank you for downloading this n8n workflow template!  \n**This template was made by <PERSON>.**\n\nSubscribe to my YouTube channel for more AI automation tutorials: https://www.youtube.com/@alexsafari1\n\n\n\nThis AI-powered system enriches leads in real-time from a simple form submission — scoring and qualifying them with zero manual work. Perfect for sales teams, marketers, and AI automation builders.\n\n## Workflow Breakdown\nForm Submission Trigger\nA prospect fills out a marketing audit form with fields like name, company, email, industry, job title, and challenges.\n\nCompany Enrichment\nThe workflow uses Hunter.io’s Company API to extract company size, revenue, location, and tech stack based on domain.\n\nPerson Enrichment\nIt then uses the Hunter Email Finder to validate contact details and job title, and fetch LinkedIn profile links if available.\n\nEmail Verification\nThe lead's email is verified to ensure high deliverability.\n\nLead Data Formatting\nData is neatly formatted using a Set node for AI processing.\n\nAI Agent Scoring\nAn AI agent scores the lead (0–100) based on:\n-Company Size\n-Industry Fit\n-Seniority Level\n-Tech Stack\n\nEmail Verification\nThe output includes lead score, grade (A–D), qualification status, and a ready-to-send follow-up email in HTML.\n\nSend Email Response\nThe lead receives a tailored reply, signed by <PERSON><PERSON> the AI Assistant”.\n\nLog to Google Sheets\nAll enriched and scored data is logged in a connected Google Sheet.\n\nSales Alert\nIf the lead is scored A or B, the sales team is notified via Gmail.\n\nSmart Switch Node\nRoutes leads based on grade into segmented workflows for future actions.\n\n## Watch the Full Setup Tutorial\n\nThis workflow is featured in my YouTube tutorial:  https://youtu.be/D-F6Kycr5JE\n\n\n## Need Help?\n\nIf you have questions, leave a comment on the YouTube video or reach out directly—happy to help! \n\n## Want personalized help building n8n AI agents?\nI offer 4-hour 1-on-1 coaching sessions to help YOU create, troubleshoot, and optimize your AI agent workflows step-by-step!\n\nBook now:\nhttps://cal.com/loopsera/n8n-ai-agent-coaching-session\n\n## Need a custom AI solution for your business?\n\n📞 Book a Discovery Call:\nhttps://cal.com/loopsera/discoverycall \n\nEmail me📧 <EMAIL>\n\n", "height": 1520, "width": 960}, "type": "n8n-nodes-base.stickyNote", "position": [-1340, -80], "typeVersion": 1, "id": "56db3310-a4b8-415f-aa1d-e91d63091aa9", "name": "Sticky Note2"}], "pinData": {"On form submission": [{"json": {"First Name": "<PERSON>", "Last Name": "<PERSON>", "Business Email": "<EMAIL>", "Phone Number": "1234567890", "Company Name": "Stripe", "Company Website": "stripe.com", "Job Title": "Marketing Manager", "Company Size": "1000+ employees", "Industry": "Other", "What's your biggest marketing challenge?": "Scaling ads", "submittedAt": "2025-06-18T08:54:40.544+03:00", "formMode": "test"}}]}, "connections": {"On form submission": {"main": [[{"node": "Company Enrichment", "type": "main", "index": 0}]]}, "Person Enrichment": {"main": [[{"node": "Email Veri<PERSON>r", "type": "main", "index": 0}]]}, "Company Enrichment": {"main": [[{"node": "Person Enrichment", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Email Verifier": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Send Email": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Inform Sales", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "275bcbbf-77ab-43ce-9ec9-6b4ddc59e08d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "8e5aa95c0552a76b6f87328dfd800de4d3cc8f008434bf9076c4dfb3cb935d83"}, "id": "YoisBjsgc36nt6La", "tags": []}