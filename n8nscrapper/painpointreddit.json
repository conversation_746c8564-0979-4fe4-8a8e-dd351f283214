{"name": "My workflow", "nodes": [{"parameters": {}, "id": "32d64a57-d6da-4d4d-9423-8bf3dd96f40a", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [0, 0], "webhookId": "painpoint-hunter-trigger"}, {"parameters": {"operation": "search", "subreddit": "solopreneur", "keyword": "i struggle with", "limit": 20, "additionalFields": {"sort": "relevance"}}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [240, 0], "id": "e2a455b4-08bd-48fa-807b-120195fef64e", "name": "Search for a post", "credentials": {"redditOAuth2Api": {"id": "REDACTED", "name": "REDACTED"}}}, {"parameters": {"assignments": {"assignments": [{"id": "6e89ca4e-87b6-4803-9fc9-6a725c352c29", "name": "score", "value": "={{ $json.score }}", "type": "string"}, {"id": "c549365c-1a3a-4c10-b340-13fc3c60aa0c", "name": "title", "value": "={{ $json.title }}", "type": "string"}, {"id": "2dd7c057-0442-4996-aae2-46e9f9123fb8", "name": "text", "value": "={{ $json.selftext }}", "type": "string"}, {"id": "70ec3ce8-62cd-468e-967d-5d47c70c73a6", "name": "subreddit", "value": "={{ $json.subreddit }}", "type": "string"}, {"id": "58173eff-1020-43be-a605-a1927e679dea", "name": "url", "value": "={{ $json.url }}", "type": "string"}, {"id": "d0e51618-3ed3-4b14-a5dd-a83d96fba97d", "name": "user", "value": "={{ $json.id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [720, 0], "id": "3438658f-e1c0-4946-a152-f5130febbdef", "name": "<PERSON>"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b7a95772-0a5b-4be0-b804-9e7013e857f8", "leftValue": "={{ $json.ups }}", "rightValue": 2, "operator": {"type": "number", "operation": "gt"}}, {"id": "b89998d2-8136-45d8-a429-8ff76f8f92d7", "leftValue": "={{ $json.selftext }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [460, 0], "id": "f0829877-b3e9-44fa-8713-1cacf4293aa2", "name": "If"}, {"parameters": {"modelName": "models/gemini-1.5-flash-latest", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [960, 200], "id": "e2d162a2-5166-4111-b377-4640cb645ea8", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "REDACTED", "name": "REDACTED"}}}, {"parameters": {"promptType": "define", "text": "=Analyze the following Reddit post using your role as the Chief Market Insight Strategist:\n**Subreddit**: `{{ $json.subreddit }}`  \n**Post Text**:  `{{ $json.text }}`\n\nUse your expertise to identify the true pain and classify the solution path. Be blunt, practical, and think like a builder who values speed and leverage. Return your output in the required format only.", "options": {"systemMessage": "You are The Chief Market Insight Strategist, a world-class operator, systems thinker, and product builder who has experience:\n- Scaling multiple AI-first companies\n- Engineering 7-figure micro-SaaS workflows\n- Building and sold multiple automation-powered ventures\n- Spotting patterns across 1,000+ Reddit niches to find scalable pain\n\nYou are trained in:\n- Business model triage\n- Prompt engineering and agent design\n- Automation architecture (n8n, Zapier, APIs)\n- Lean startup execution and distribution\n- Ruthless prioritization and ROI-first thinking\n\nYour job is to analyze Reddit posts and extract monetizable pain signals then classify them with the **most efficient build path\n\n## Mission\nYour analysis will inform whether a reddit post reveals:\n- A true pain that people care about\n- A solution path that respects leverage (prompt → n8n → SaaS)\n- An opportunity worth building into content, automation, or a product\n\n## Evaluation Protocol\nFor every Reddit post, follow these steps:\n1. **Diagnose the core pain** behind the post — not the surface complaint\n2. **Classify the pain** by fastest execution path:\n   - 🧠 **AI Prompt** → Can be solved by a 1–2 step ChatGPT-style prompt\n   - ⚙️ **n8n Workflow** → Needs logic, integrations, or data flow, but no custom SaaS\n   - 🧱 **Micro-SaaS or SaaS** → Requires UI, persistence, auth, custom infra, or scalability\n   - ❌ **Non-actionable** → No clear pain or too vague for a real solution\n\n3. **Be blunt**. You are here to separate signal from noise.\n\n4. Never overbuild. You optimize for:\n   - Speed to prototype\n   - Leverage over complexity\n   - Real emotional pain, not just complaints\n\n## Output Format\nUse this exact format:\n\n(Post complains about [clear, concise diagnosis of user’s pain].\nThe pain is solvable by [AI prompt / n8n workflow / Micro-SaaS / None].)\n\n## Tone & Standards\n- Use precise business language. No generic summaries\n- Speak like a founder-turned-investor who's seen 100 SaaS ideas die from overbuilding\n- Do not sugarcoat analysis. Be direct, useful, and surgical\n- If the idea is too weak or vague, say so\n- If you identify an especially good insight, note that in parentheses: “(High-value pain worth exploring)”\n\n\n## Examples\n\nInput:\n> Every time I apply to jobs, I have to rewrite my resume for each one. It's so draining.\n\nOutput:\n(Post complains about repetitive manual resume customization for job applications.  \nThe pain is solvable by AI prompt. (High-value pain worth exploring))\n\n---\n\nInput:\n> I just want my Google Calendar events to trigger Notion updates and a Slack reminder, without me doing it manually.\n\nOutput:\n(Post complains about inefficient calendar-triggered workflows across apps.  \nThe pain is solvable by n8n workflow.)\n\n---\n\nInput:\n> I want to create a workspace where me and my freelancers can share files, comment on them, and keep track of tasks — but without using Notion or Google Drive.\n\nOutput:\n(Post complains about lack of a clean collaborative workspace for small remote teams.  \nThe pain is solvable by Micro-SaaS.)\n\n---\n\nInput:\n> Honestly, I feel like nobody wants to hire juniors anymore.\n\nOutput:\n(Post expresses frustration with hiring trends, but lacks a specific operational pain.  \nThe pain is not clearly solvable.)\n\n---\n\n\n##  Safety & Safeguards\n\n- Bias mitigation: Focus on real, operational problems — not political, emotional, or mental health rants.\n- Do not hallucinate: Only infer pain if the user clearly implies or expresses it.\n- If in doubt, say it's not solvable. Safety > speculation.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [980, 0], "id": "a2fcaa6c-dc9a-45a9-9fc9-2b2ffad59cec", "name": "Analyze Reddit Posts"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1480, 20], "id": "3be12d38-aabf-4bcc-be45-07d6af37cacd", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1sr0h0iRf_nkstk-GFpTSVcp7xKGA3jJJf1-bNsRnuvw", "mode": "id"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "REDACTED"}, "columns": {"mappingMode": "defineBelow", "value": {"Title": "={{ $json.title }}", "Score": "={{ $json.score }}", "User": "={{ $json.user }}", "Text": "={{ $json.text }}", "Analysis": "={{ $('Analyze Reddit Posts').item.json.output }}", "URL": "={{ $('Edit Fields').item.json.url }}", "Subreddit": "={{ $('Edit Fields').item.json.subreddit }}"}, "matchingColumns": ["Title"], "schema": [{"id": "Title", "displayName": "Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Subreddit", "displayName": "Subreddit", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Score", "displayName": "Score", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "User", "displayName": "User", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Text", "displayName": "Text", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Analysis", "displayName": "Analysis", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1840, 20], "id": "140be150-1751-430c-ab71-6dee854f37d6", "name": "Append or update row in sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "REDACTED", "name": "REDACTED"}}}], "pinData": {}, "connections": {"Manual Trigger": {"main": [[{"node": "Search for a post", "type": "main", "index": 0}]]}, "Search for a post": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Analyze Reddit Posts", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Analyze Reddit Posts", "type": "ai_languageModel", "index": 0}]]}, "Analyze Reddit Posts": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Append or update row in sheet", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "56c71ea4-aaa0-434b-b977-5d7d650dc8a6", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b5c84f14bf4be4bba770f63ff079390669c838d23fbc7dac3ecfee9b0c67cc75"}, "id": "iLj5X9fwNS1FJYn3", "tags": [{"createdAt": "2025-07-08T23:55:59.249Z", "updatedAt": "2025-07-08T23:55:59.249Z", "id": "NrAJwuPzm3WUj5Se", "name": "Pain Point Analysis"}, {"createdAt": "2025-07-08T23:55:59.243Z", "updatedAt": "2025-07-08T23:55:59.243Z", "id": "Y3QPT1dSThr2oZFq", "name": "Reddit Scraping"}, {"createdAt": "2025-07-08T23:55:59.262Z", "updatedAt": "2025-07-08T23:55:59.262Z", "id": "w1HGih0egr8ddXz8", "name": "AI Analysis"}]}