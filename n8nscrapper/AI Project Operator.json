{"name": "Project Management", "flow": [{"id": 1, "module": "asana:watchTasksOrSubtasks", "version": 2, "parameters": {"text": "", "limit": 10, "sort_by": "created_at", "tags.any": [], "completed": true, "teams.any": [], "workspace": "1209734101555173", "__IMTCONN__": 6604457, "assignee.any": ["1209734101555161"], "projects.any": ["1209734042888878"], "created_by.any": [], "portfolios.any": [], "assigned_by.any": [], "resource_subtype": "default_task"}, "mapper": {}, "metadata": {"designer": {"x": -49, "y": 271, "name": "Trigger when Task Added"}, "restore": {"parameters": {"project": {"mode": "chose"}, "sort_by": {"label": "Created Tasks"}, "tags.any": {"mode": "chose"}, "teams.any": {"mode": "chose"}, "workspace": {"label": "[Workspace] My workspace"}, "__IMTCONN__": {"data": {"scoped": "true", "connection": "asana"}, "label": "My Asana connection"}, "assignee.any": {"mode": "chose", "label": ["<PERSON><PERSON>"]}, "projects.any": {"mode": "chose", "label": ["Project Operator Demo"]}, "created_by.any": {"mode": "chose"}, "portfolios.any": {"mode": "chose"}, "assigned_by.any": {"mode": "chose"}, "resource_subtype": {"label": "Default Task"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:asana", "label": "Connection", "required": true}, {"name": "sort_by", "type": "select", "label": "Watch", "required": true, "validate": {"enum": ["created_at", "completed_at", "modified_at"]}}, {"name": "completed", "type": "hidden", "label": "Completed"}, {"name": "resource_subtype", "type": "select", "label": "Resource Subtype", "required": true, "validate": {"enum": ["default_task", "milestone"]}}, {"name": "workspace", "type": "select", "label": "Workspace", "required": true}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit", "required": true}, {"name": "text", "type": "text", "label": "Text"}, {"name": "assignee.any", "type": "select", "label": "Assignee", "multiple": true}, {"name": "portfolios.any", "type": "select", "label": "Portfolios", "multiple": true}, {"name": "projects.any", "type": "select", "label": "Projects", "multiple": true}, {"name": "project", "type": "select", "label": "Project"}, {"name": "tags.any", "type": "select", "label": "Tags", "multiple": true}, {"name": "teams.any", "type": "select", "label": "Teams", "multiple": true}, {"name": "created_by.any", "type": "select", "label": "Created By", "multiple": true}, {"name": "assigned_by.any", "type": "select", "label": "Assigned By", "multiple": true}, {"name": "is_blocking", "type": "boolean", "label": "Is Blocking"}, {"name": "is_blocked", "type": "boolean", "label": "Is Blocked"}, {"name": "has_attachment", "type": "boolean", "label": "Has Attachment"}, {"name": "is_subtask", "type": "boolean", "label": "Is Subtask"}]}, "onerror": [{"id": 24, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": -52, "y": 578}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 3, "module": "google-drive:searchForFilesFolders", "version": 4, "parameters": {"__IMTCONN__": 3789042}, "mapper": {"limit": "10", "query": "{{1.name}}", "select": "list", "folderId": "/1AqZ50kYM_rE013R6kIbqH71jJsT9lLPg", "operator": "contains", "retrieve": "file", "searchType": "title", "destination": "drive"}, "metadata": {"designer": {"x": 268, "y": 280, "name": "Collect Relevant Project File"}, "restore": {"expect": {"select": {"label": "Select from the list"}, "folderId": {"mode": "chose", "path": ["Project"]}, "operator": {"label": "Search for name containing the search term"}, "retrieve": {"label": "Files"}, "searchType": {"label": "Search within file/folder names"}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (aryan<PERSON><PERSON><PERSON><EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select the Method", "required": true, "validate": {"enum": ["map", "list"]}}, {"name": "retrieve", "type": "select", "label": "Retrieve", "required": true, "validate": {"enum": ["file", "folder", "file_folder"]}}, {"name": "searchType", "type": "select", "label": "Search", "validate": {"enum": ["title", "fulltext", "custom"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "Choose a Folder"}, {"name": "query", "type": "text", "label": "Query", "required": true}, {"name": "operator", "type": "select", "label": "Search options", "required": true, "validate": {"enum": ["=", "contains"]}}]}, "onerror": [{"id": 25, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 265, "y": 580}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 13, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 586, "y": 285, "name": "File Does Not Exist"}}, "routes": [{"flow": [{"id": 4, "module": "airtable:ActionCreateRecord", "version": 3, "parameters": {"__IMTCONN__": 3823404}, "mapper": {"base": "app8D936Xmvc8dza3", "table": "tbl2vuSbclhsrkkZD", "record": {"fldGuwzksrrDaFzNh": "{{1.due_on}}", "fldPDjOdbW2hWikZ0": "{{1.custom_fields[].text_value}}", "fldUNvCPkvQQY5DKE": "At risk", "flddsyvjgMi0NbHB0": "{{1.assignee.name}}", "fldsw0AcPNSa4OQAN": "{{1.name}}", "fldv0mw5ubsKjdh0r": "{{3.webViewLink}}"}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 899, "y": 285, "name": "Add Entry to Airtable"}, "restore": {"expect": {"base": {"label": "Project Tracker - <PERSON><PERSON>"}, "table": {"label": "Table 1", "nested": [{"name": "record", "spec": [{"name": "fldsw0AcPNSa4OQAN", "type": "text", "label": "Task Name"}, {"name": "flddsyvjgMi0NbHB0", "type": "text", "label": "Assigned To"}, {"name": "fldrwNQUUJKxWVMcn", "type": "text", "label": "Email"}, {"name": "fldGuwzksrrDaFzNh", "time": false, "type": "date", "label": "Due Date"}, {"name": "fldv0mw5ubsKjdh0r", "type": "text", "label": "File Doc"}, {"mode": "edit", "name": "fldUNvCPkvQQY5DKE", "type": "select", "label": "Status", "dynamic": true, "options": [{"label": "Off track", "value": "Off track"}, {"label": "On track", "value": "On track"}, {"label": "At risk", "value": "At risk"}], "validate": false}, {"name": "fldPDjOdbW2hWikZ0", "type": "text", "label": "Notes", "multiline": true}], "type": "collection", "label": "Record"}]}, "record": {"nested": {"fldUNvCPkvQQY5DKE": {"mode": "chose", "label": "At risk"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "record", "spec": [{"name": "fldsw0AcPNSa4OQAN", "type": "text", "label": "Task Name"}, {"name": "flddsyvjgMi0NbHB0", "type": "text", "label": "Assigned To"}, {"name": "fldrwNQUUJKxWVMcn", "type": "text", "label": "Email"}, {"name": "fldGuwzksrrDaFzNh", "time": false, "type": "date", "label": "Due Date"}, {"name": "fldv0mw5ubsKjdh0r", "type": "text", "label": "File Doc"}, {"mode": "edit", "name": "fldUNvCPkvQQY5DKE", "type": "select", "label": "Status"}, {"name": "fldPDjOdbW2hWikZ0", "type": "text", "label": "Notes"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Task Name", "type": "text", "label": "Task Name"}, {"name": "Status", "type": "text", "label": "Status"}, {"name": "Due Date", "time": false, "type": "date", "label": "Due Date"}, {"name": "Due Date Status", "type": "text", "label": "Due Date Status"}, {"name": "Attached File", "spec": [{"name": "label", "type": "text", "label": "Label"}, {"name": "url", "type": "url", "label": "URL"}], "type": "collection", "label": "Attached File"}, {"name": "Assigned To", "type": "text", "label": "Assigned To"}, {"name": "Email", "type": "text", "label": "Email"}, {"name": "Notes", "type": "text", "label": "Notes", "multiline": true}, {"name": "File Doc", "type": "text", "label": "File Doc"}]}, "onerror": [{"id": 26, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 888, "y": 587}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 5, "module": "airtable:ActionSearchRecords", "version": 3, "parameters": {"__IMTCONN__": 3823404}, "mapper": {"base": "app8D936Xmvc8dza3", "view": "viwhJjPzrtG1elRke", "table": "tbl2vuSbclhsrkkZD", "fields": ["Task Name", "Assigned To", "Email", "Due Date", "File Doc", "Status", "Notes"], "maxRecords": "10", "useColumnId": false}, "metadata": {"designer": {"x": 1200, "y": 300, "name": "Search Airtable"}, "restore": {"expect": {"base": {"mode": "chose", "label": "Project Tracker - <PERSON><PERSON>"}, "sort": {"mode": "chose"}, "view": {"mode": "chose", "label": "Grid view"}, "table": {"mode": "chose", "label": "Table 1"}, "fields": {"mode": "chose", "label": ["Task Name", "Assigned To", "Email", "Due Date", "File Doc", "Status", "Notes"]}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "formula", "type": "text", "label": "Formula"}, {"name": "maxRecords", "type": "integer", "label": "Limit"}, {"name": "sort", "spec": [{"name": "field", "type": "select", "label": "Field", "dynamic": true, "options": []}, {"name": "direction", "type": "select", "label": "Direction", "options": [{"label": "Descending", "value": "desc"}, {"label": "Ascending", "value": "asc"}]}], "type": "array", "label": "Sort"}, {"name": "view", "type": "select", "label": "View"}, {"name": "fields", "type": "select", "label": "Output Fields", "multiple": true}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Task Name", "type": "text", "label": "Task Name"}, {"name": "Assigned To", "type": "text", "label": "Assigned To"}, {"name": "Email", "type": "text", "label": "Email"}, {"name": "Due Date", "time": false, "type": "date", "label": "Due Date"}, {"name": "File Doc", "type": "text", "label": "File Doc"}, {"name": "Status", "type": "text", "label": "Status"}, {"name": "Notes", "type": "text", "label": "Notes", "multiline": true}]}}, {"id": 6, "module": "util:SetVariables", "version": 1, "parameters": {}, "mapper": {"scope": "execution", "variables": [{"name": "taskName", "value": "{{5.`Task Name`}}"}, {"name": "assignedTo", "value": "{{5.`Assigned To`}}"}, {"name": "dueDate", "value": "{{parseDate(5.`Due Date`; \"YYYY-MM-DD\")}}"}, {"name": "status", "value": "{{5.Status}}"}, {"name": "fileName", "value": "{{5.`File Doc`}}"}, {"name": "notes", "value": "{{5.Notes}}"}, {"name": "todayDate", "value": "{{parseDate(now; \"YYYY-MM-DD\")}}"}, {"name": "email", "value": "{{5.<PERSON><PERSON>}}"}]}, "metadata": {"designer": {"x": 1500, "y": 300, "name": "Save to Variables"}, "restore": {"expect": {"scope": {"label": "One execution"}, "variables": {"items": [null, null, null, null, null, null, null, null]}}}, "expect": [{"name": "variables", "spec": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "value", "type": "any", "label": "Variable value"}], "type": "array", "label": "Variables"}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}], "interface": [{"name": "taskName", "type": "any", "label": "taskName"}, {"name": "assignedTo", "type": "any", "label": "assignedTo"}, {"name": "dueDate", "type": "any", "label": "dueDate"}, {"name": "status", "type": "any", "label": "status"}, {"name": "fileName", "type": "any", "label": "fileName"}, {"name": "notes", "type": "any", "label": "notes"}, {"name": "todayDate", "type": "any", "label": "todayDate"}, {"name": "email", "type": "any", "label": "email"}], "advanced": true}}, {"id": 7, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 1800, "y": 300}}, "routes": [{"flow": [{"id": 9, "module": "util:SetVariables", "version": 1, "parameters": {}, "filter": {"name": "Overdue Tasks", "conditions": [[{"a": "{{6.dueDate}}", "b": "{{6.todayDate}}", "o": "number:less"}]]}, "mapper": {"scope": "roundtrip", "variables": [{"name": "taskName", "value": "{{6.<PERSON><PERSON><PERSON>}}"}, {"name": "assignedTo", "value": "{{6.<PERSON>To}}"}, {"name": "dueDate", "value": "{{6.dueDate}}"}, {"name": "status", "value": "{{6.status}}"}, {"name": "fileName", "value": "{{6.fileName}}"}, {"name": "notes", "value": "{{6.notes}}"}, {"name": "email", "value": "{{5.<PERSON><PERSON>}}"}, {"name": "todaysDate", "value": "{{6.todayDate}}"}]}, "metadata": {"designer": {"x": 2100, "y": 0, "name": "Save Overdue Tasks Details"}, "restore": {"expect": {"scope": {"label": "One cycle"}, "variables": {"items": [null, null, null, null, null, null, null, null]}}}, "expect": [{"name": "variables", "spec": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "value", "type": "any", "label": "Variable value"}], "type": "array", "label": "Variables"}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}], "interface": [{"name": "taskName", "type": "any", "label": "taskName"}, {"name": "assignedTo", "type": "any", "label": "assignedTo"}, {"name": "dueDate", "type": "any", "label": "dueDate"}, {"name": "status", "type": "any", "label": "status"}, {"name": "fileName", "type": "any", "label": "fileName"}, {"name": "notes", "type": "any", "label": "notes"}, {"name": "email", "type": "any", "label": "email"}, {"name": "todaysDate", "type": "any", "label": "todaysDate"}]}}, {"id": 12, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 3836153}, "mapper": {"model": "gpt-3.5-turbo", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are a helpul intelligent professional assistant."}, {"role": "system", "content": " Based on the input variables (task name, overdue duration), generate a concise and professional email subject line. The format should be:\n- If overdue duration is provided, you may include it in parentheses like “(Overdue by X days)”.\nKeep it short and to the point."}, {"role": "user", "content": "{\n  \"taskName\": \"Finalize Vendor Contract\",\n  \"todaysDate\": \"2024-11-25\",\n  \"dueDate\": \"2024-11-20\"\n}"}, {"role": "assistant", "content": "Overdue Task Notification - Finalize Vendor Contract (Overdue by 5 days)"}, {"role": "user", "content": "{\n  \"taskName\": \"{{9.taskName}}\",\n  \"dueDate\": \"{{9.dueDate}}\",\n  \"todaysDate\": \"{{9.todaysDate}}\",\n}"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 2400, "y": 0, "name": "Create Email Subject"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-3.5-turbo"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 11, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 3836153}, "mapper": {"model": "gpt-4o", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are a helpful, intelligent writing assisant."}, {"role": "system", "content": "You are a professional assistant responsible for crafting polite and professional task overdue notifications. Based on the input variables (task name, assignee, status, notes, overdue duration, and current date), generate a concise and well-structured email that - \n- Includes key details about the task.\t\n- Urges the assignee to take action.\n- Optionally offers assistance if required.Format the output in plain text with no unnecessary fluff.\n\n### Critical\nAVOID WRITING THE SUBJECT, BEGIN THE EMAIL STRAIGHT FROM THE BODY"}, {"role": "user", "content": "{\n  \"taskName\": \"Finalize Vendor Contract\",\n  \"assignedTo\": \"John Do<PERSON>\",\n  \"status\": \"In Progress\",\n  \"notes\": \"Please review Section 3 for SLA terms.\",\n  \"todaysDate\": \"2024-11-25\",\n  \"dueDate\": \"2024-11-20\"\n}", "imageDetail": "auto"}, {"role": "assistant", "content": "Hi <PERSON>, \n\nI hope this message finds you well. I am writing to remind you that the task \"Review Vendor Contract - Blue Ocean Logix\" assigned to you was due on November 19, 2024, and is currently marked as \"Off track.\"\n\nPlease review the contract, particularly ensuring that all SLAs and pricing terms align with company standards and highlight any discrepancies for legal review. Your prompt attention to this matter is crucial to prevent any potential delays.\n\nIf there are any obstacles preventing you from completing this task, please let me know how I might assist.\n\nThank you for your immediate action on this.\n\nBest regards,I hope this message finds you well. I am writing to remind you that the task \"Review Vendor Contract - Blue Ocean Logix\" assigned to you was due on November 19, 2024, and is currently marked as \"Off track.\"\n\nPlease review the contract, particularly ensuring that all SLAs and pricing terms align with company standards and highlight any discrepancies for legal review. Your prompt attention to this matter is crucial to prevent any potential delays.\n\nIf there are any obstacles preventing you from completing this task, please let me know how I might assist.\n\nThank you for your immediate action on this.\n\nBest regards,\n<PERSON>"}, {"role": "user", "content": "{\n  \"taskName\": \"{{9.taskName}}\",\n  \"assignedTo\": \"{{9.assignedTo}}\",\n  \"dueDate\": \"{{9.dueDate}}\",\n  \"status\": \"{{9.status}}\",\n  \"notes\": \"{{9.notes}}\",\n  \"todaysDate\": \"{{9.todaysDate}}\",\n   \"email\": \"{{9.email}}\"\n}", "imageDetail": "auto"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 2700, "y": 0, "name": "Craft Email Body"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}], "advanced": true}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 10, "module": "email:ActionSendEmail", "version": 7, "parameters": {"account": 3789042, "saveAfterSent": false}, "mapper": {"cc": [], "to": ["a<PERSON><PERSON><PERSON><PERSON><EMAIL>"], "bcc": [], "from": "", "text": "{{11.result}}", "sender": "", "headers": [], "replyTo": "", "subject": "{{12.result}}", "priority": "normal", "inReplyTo": "", "references": [], "attachments": [], "contentType": "text"}, "metadata": {"designer": {"x": 3000, "y": 0, "name": "Send Overdue Email"}, "restore": {"expect": {"cc": {"mode": "chose"}, "to": {"mode": "chose", "items": [null]}, "bcc": {"mode": "chose"}, "priority": {"label": "Normal"}, "references": {"mode": "chose"}, "attachments": {"mode": "chose"}, "contentType": {"label": "Plaintext"}}, "parameters": {"account": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (aryan<PERSON><PERSON><PERSON><EMAIL>)"}, "saveAfterSent": {"label": "No"}}}, "parameters": [{"name": "account", "type": "account:smtp,google-restricted,microsoft-smtp-imap", "label": "Connection", "required": true}, {"name": "saveAfterSent", "type": "select", "label": "Save message after sending", "required": true, "validate": {"enum": [true, false]}}], "expect": [{"name": "to", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "To", "required": true}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "contentType", "type": "select", "label": "Content Type", "required": true, "validate": {"enum": ["html", "text"]}}, {"name": "attachments", "spec": [{"name": "fileName", "type": "filename", "label": "File name", "required": true, "semantic": "file:name"}, {"name": "data", "type": "buffer", "label": "Data", "required": true, "semantic": "file:data"}, {"name": "cid", "type": "text", "label": "Content-ID"}], "type": "array", "label": "Attachments"}, {"name": "cc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Copy recipient"}, {"name": "bcc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Blind copy recipient"}, {"name": "from", "type": "text", "label": "From"}, {"name": "sender", "type": "text", "label": "Sender"}, {"name": "replyTo", "type": "text", "label": "Reply-To"}, {"name": "inReplyTo", "type": "text", "label": "In-Reply-To"}, {"name": "references", "spec": {"name": "value", "type": "text", "label": "Reference", "required": true}, "type": "array", "label": "References"}, {"name": "priority", "type": "select", "label": "Priority", "validate": {"enum": ["high", "normal", "low"]}}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key", "required": true}, {"name": "value", "type": "text", "label": "Value", "required": true}], "type": "array", "label": "Headers"}, {"name": "text", "type": "text", "label": "Content"}]}, "onerror": [{"id": 28, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 2989, "y": 309}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}]}, {"flow": [{"id": 37, "module": "util:TextAggregator", "version": 1, "parameters": {"feeder": 5, "rowSeparator": ""}, "mapper": {"value": "Task Name: {{6.taskName}}\nAssigne: {{6.assignedTo}}\nDue Date: {{6.dueDate}}\nStatus: {{6.status}}\nFile URL: {{6.fileName}}\nNotes: {{6.notes}}\nEmail: {{6.email}}\n----------"}, "metadata": {"designer": {"x": 2100, "y": 300}, "restore": {"extra": {"feeder": {"label": "Search Airtable - Search Records [5]"}}, "parameters": {"rowSeparator": {"label": "Empty"}}}, "parameters": [{"name": "rowSeparator", "type": "select", "label": "Row separator", "validate": {"enum": ["\n", "\t", "other"]}}], "expect": [{"name": "value", "type": "text", "label": "Text"}]}}, {"id": 15, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 3836153}, "filter": {"name": "Weekly Progress Reports", "conditions": []}, "mapper": {"model": "gpt-4o", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are a helpful, intelligent writing assisant."}, {"role": "system", "content": "You are a professional assistant responsible for generating a comprehensive, polished weekly progress report tailored for corporate leadership. Your task is to:\n\n- Categorize tasks into “On Track,” “At Risk,” and “Off Track” based on their status.\n- Use the provided tasks and their due dates to highlight overdue tasks by comparing the due date with today’s date.\n- Summarize the overall status of the week, emphasizing key areas that need attention or immediate action.\n- Present overdue tasks in a way that highlights their urgency, offering solutions where appropriate.\n- Conclude the report with actionable recommendations for decision-makers.\n\nThe report must be written in professional and thoughtful language, free from jargon, and easy to read. Use today’s date  for overdue task calculations.\n\n**Important:** The output should begin directly with the email body (no subject line) and must not repeat input fields verbatim. Focus on creating a cohesive and engaging summary, not just outputting raw data.\n\n\nAVOID THE USE OF \"*\" when making headers, bullet points or the content of the emails. "}, {"role": "user", "content": "[\n  {\n    \"taskName\": \"Finalize Vendor Contract\",\n    \"status\": \"Off Track\",\n    \"dueDate\": \"2024-11-20\",\n    \"notes\": \"Review SLA terms with vendor.\"\n  },\n  {\n    \"taskName\": \"Submit Monthly Operations Report\",\n    \"status\": \"At Risk\",\n    \"dueDate\": \"2024-11-26\",\n    \"notes\": \"Pending data from the operations team.\"\n  },\n  {\n    \"taskName\": \"Review Stakeholder Feedback\",\n    \"status\": \"On Track\",\n    \"dueDate\": \"2024-11-27\",\n    \"notes\": \"Focus on timeline adjustments.\"\n  }\n]", "imageDetail": "auto"}, {"role": "assistant", "content": "Dear Team,\n\nBelow is a summary of our progress for the week ending {{parseDate(now; \"YYY-MM-DD\")}}. Please review the highlights and take note of areas requiring immediate attention.\n\n---\n\nOverall Status:\nThis week, several projects are progressing well, while others require immediate attention to avoid further delays. Below is a categorized summary of the current tasks:\n\n---\n\nOn Track\nThe following tasks are proceeding as planned and require no immediate action:\n1. Review Stakeholder Feedback  \n   - Due Date: November 27, 2024  \n   - Notes: The team is on schedule to meet deliverables, focusing on timeline adjustments.\n\n---\n\nAt Risk\nThe following tasks require close monitoring to ensure timely completion:\n1. Submit Monthly Operations Report  \n   - Due Date: November 26, 2024  \n   - Notes: Data from the operations team is still pending. Please prioritize this task to avoid delays.\n\n---\n\nOff Track\nThe following tasks are overdue and require urgent attention:\n1. Finalize Vendor Contract  \n   - Due Date: November 20, 2024  \n   - Overdue by: 5 days  \n   - Notes: Review SLA terms with the vendor. Immediate escalation is recommended to meet critical deadlines.\n\n---\n\nNext Steps\n- Leadership Review: Schedule a meeting to address overdue tasks and discuss reallocation of resources if necessary.\n-  Team Prioritization : Teams should prioritize \"At Risk\" tasks and escalate any blockers to leadership.\n-  Follow-Up on Overdue Tasks: Ensure that the “Finalize Vendor Contract” task is resolved within the next two business days.\n\nLet me know if additional support or resources are needed to accelerate progress on any of these tasks.\n\nBest regards,  \n<PERSON>"}, {"role": "user", "content": "{{37.text}}", "imageDetail": "auto"}], "max_tokens": "2048", "temperature": ".6", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 2400, "y": 300, "name": "Weekly Progress Report"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}], "advanced": true}}, {"id": 20, "module": "email:ActionSendEmail", "version": 7, "parameters": {"account": 3789042, "saveAfterSent": false}, "mapper": {"cc": [], "to": ["a<PERSON><PERSON><PERSON><PERSON><EMAIL>"], "bcc": [], "from": "", "text": "{{15.result}}", "sender": "", "headers": [], "replyTo": "", "subject": "Weekly Progress Report - November 25, 2024", "priority": "normal", "inReplyTo": "", "references": [], "attachments": [], "contentType": "text"}, "metadata": {"designer": {"x": 2700, "y": 300}, "restore": {"expect": {"cc": {"mode": "chose"}, "to": {"mode": "chose", "items": [null]}, "bcc": {"mode": "chose"}, "priority": {"label": "Normal"}, "references": {"mode": "chose"}, "attachments": {"mode": "chose"}, "contentType": {"label": "Plaintext"}}, "parameters": {"account": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (aryan<PERSON><PERSON><PERSON><EMAIL>)"}, "saveAfterSent": {"label": "No"}}}, "parameters": [{"name": "account", "type": "account:smtp,google-restricted,microsoft-smtp-imap", "label": "Connection", "required": true}, {"name": "saveAfterSent", "type": "select", "label": "Save message after sending", "required": true, "validate": {"enum": [true, false]}}], "expect": [{"name": "to", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "To", "required": true}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "contentType", "type": "select", "label": "Content Type", "required": true, "validate": {"enum": ["html", "text"]}}, {"name": "attachments", "spec": [{"name": "fileName", "type": "filename", "label": "File name", "required": true, "semantic": "file:name"}, {"name": "data", "type": "buffer", "label": "Data", "required": true, "semantic": "file:data"}, {"name": "cid", "type": "text", "label": "Content-ID"}], "type": "array", "label": "Attachments"}, {"name": "cc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Copy recipient"}, {"name": "bcc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Blind copy recipient"}, {"name": "from", "type": "text", "label": "From"}, {"name": "sender", "type": "text", "label": "Sender"}, {"name": "replyTo", "type": "text", "label": "Reply-To"}, {"name": "inReplyTo", "type": "text", "label": "In-Reply-To"}, {"name": "references", "spec": {"name": "value", "type": "text", "label": "Reference", "required": true}, "type": "array", "label": "References"}, {"name": "priority", "type": "select", "label": "Priority", "validate": {"enum": ["high", "normal", "low"]}}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key", "required": true}, {"name": "value", "type": "text", "label": "Value", "required": true}], "type": "array", "label": "Headers"}, {"name": "text", "type": "text", "label": "Content"}]}, "onerror": [{"id": 29, "module": "builtin:Break", "version": 1, "metadata": {"designer": {"x": 2991, "y": 308}}}]}]}, {"flow": [{"id": 38, "module": "util:TextAggregator", "version": 1, "parameters": {"feeder": 5, "rowSeparator": ""}, "mapper": {"value": "Task Name: {{6.taskName}}\nAssigne: {{6.assignedTo}}\nDue Date: {{6.dueDate}}\nStatus: {{6.status}}\nFile URL: {{6.fileName}}\nNotes: {{6.notes}}\nEmail: {{6.email}}\n----------"}, "metadata": {"designer": {"x": 2100, "y": 600}, "restore": {"extra": {"feeder": {"label": "Search Airtable - Search Records [5]"}}, "parameters": {"rowSeparator": {"label": "Empty"}}}, "parameters": [{"name": "rowSeparator", "type": "select", "label": "Row separator", "validate": {"enum": ["\n", "\t", "other"]}}], "expect": [{"name": "value", "type": "text", "label": "Text"}]}}, {"id": 32, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 3836153}, "mapper": {"model": "gpt-4o", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are a helpful, intelligent project manager. "}, {"role": "system", "content": "Your task:\n- Extract relevant details such as task name, assignee, due date, status, file URLs, and notes.\n- Ensure uniform formatting with proper labeling.\n- Remove any duplicate or incomplete entries.\n- If a task lacks a status, assume \"Pending.\"\n- If a task lacks a due date, mark it as \"To Be Determined.\"\n- Ensure consistency in date format (YYYY-MM-DD).\n- If a task has a file URL, verify and structure it under \"Supporting Documents.\"\n- Summarize tasks concisely for easy retrieval.\n\nFormat the output in a structured format, ensuring it is optimized for vector search. Output each task as a separate entry in JSON format.\n\nExample Output:\n[\n  {\n    \"task_name\": \"Upload Stakeholder Feedback Document\",\n    \"assignee\": \"<PERSON>ryan Ma<PERSON>jan\",\n    \"due_date\": \"2025-03-17\",\n    \"status\": \"Off track\",\n    \"notes\": \"Submit documentation to client\",\n    \"file_url\": \"https://docs.google.com/document/d/1fidgKW-XK37-dO546Bu_EkbZhD0zIRammhjrQfILr58/edit?tab=t.0\"\n  },\n  {\n    \"task_name\": \"Vendor Contract\",\n    \"assignee\": \"<PERSON>ryan Mahajan\",\n    \"due_date\": \"2025-03-18\",\n    \"status\": \"Off track\",\n    \"notes\": \"No additional notes\",\n    \"file_url\": \"https://docs.google.com/document/d/1fidgKW-XK37-dO546Bu_EkbZhD0zIRammhjrQfILr58/edit?tab=t.0\"\n  }\n]\n\nHere is the raw project task data:\n{{38.text}}"}], "max_tokens": "2048", "temperature": ".6", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 2400, "y": 600, "name": "Prepare Data for Vector Upload"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4o (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "Developer / System"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}], "advanced": true}}, {"id": 41, "module": "openai-gpt-3:uploadFile", "version": 1, "parameters": {"__IMTCONN__": 3836153}, "mapper": {"purpose": "assistants", "fileData": "{{32.result}}", "fileName": "ProjectManager.txt"}, "metadata": {"designer": {"x": 2695, "y": 603, "name": "Prepare File"}, "restore": {"expect": {"purpose": {"mode": "chose", "label": "Assistants"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "fileName", "type": "text", "label": "File Name", "required": true}, {"name": "fileData", "type": "buffer", "label": "File Data", "required": true}, {"name": "purpose", "type": "select", "label": "Purpose", "required": true, "validate": {"enum": ["assistants", "vision", "fine-tune", "batch"]}}]}, "onerror": [{"id": 42, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 2688, "y": 865}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 43, "module": "openai-gpt-3:createVectorStoreFileBatch", "version": 1, "parameters": {"__IMTCONN__": 3836153}, "mapper": {"fileIds": ["file-C6r1MfM32bQfaw88a4t7M2"], "createMode": "choose", "vectorStoreId": "vs_67db5bca980881919eb952176ba8a548"}, "metadata": {"designer": {"x": 2995, "y": 603, "name": "Upload File to GPT"}, "restore": {"expect": {"fileIds": {"mode": "chose", "label": ["ProjectManager.txt"]}, "createMode": {"mode": "chose", "label": "Choose Vector Store"}, "vectorStoreId": {"mode": "chose", "label": "Project Manager"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "createMode", "type": "select", "label": "Batch Create Mode", "required": true, "validate": {"enum": ["choose", "create"]}}, {"name": "fileIds", "type": "select", "label": "File IDs", "multiple": true, "required": true}, {"name": "vectorStoreId", "type": "select", "label": "Vector Store ID", "required": true}]}, "onerror": [{"id": 44, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 2986, "y": 878}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}]}]}]}, {"flow": [{"id": 14, "module": "email:ActionSendEmail", "version": 7, "parameters": {"account": 3789042, "saveAfterSent": false}, "filter": {"name": "", "conditions": [[{"a": "{{3.id}}", "o": "notexist"}]]}, "mapper": {"cc": [], "to": ["{{1.custom_fields[].name}}"], "bcc": [], "from": "", "text": "Hi [Assignee],\n\nThe file required for the task \"[Task Name]\" is not available in Google Drive.  \n\nTask Details:\n- Task Name: {{1.memberships[].project.name}}\n- Due Date: {{1.due_on}}\n- Notes: {{1.notes}}\n\nPlease upload the file to the designated folder or contact support for assistance.  \n\nLet me know if you need help resolving this.\n\nBest regards,  \nAutomation Team", "sender": "", "headers": [], "replyTo": "", "subject": "Missing File for Task: {{1.memberships[].project.name}}", "priority": "normal", "inReplyTo": "", "references": [], "attachments": [], "contentType": "text"}, "metadata": {"designer": {"x": 571, "y": 588, "name": "Email <PERSON> to Upload File"}, "restore": {"expect": {"cc": {"mode": "chose"}, "to": {"mode": "chose", "items": [null]}, "bcc": {"mode": "chose"}, "priority": {"label": "Normal"}, "references": {"mode": "chose"}, "attachments": {"mode": "chose"}, "contentType": {"label": "Plaintext"}}, "parameters": {"account": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "My Google Restricted connection (aryan<PERSON><PERSON><PERSON><EMAIL>)"}, "saveAfterSent": {"label": "No"}}}, "parameters": [{"name": "account", "type": "account:smtp,google-restricted,microsoft-smtp-imap", "label": "Connection", "required": true}, {"name": "saveAfterSent", "type": "select", "label": "Save message after sending", "required": true, "validate": {"enum": [true, false]}}], "expect": [{"name": "to", "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}, "type": "array", "label": "To", "required": true}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "contentType", "type": "select", "label": "Content Type", "required": true, "validate": {"enum": ["html", "text"]}}, {"name": "attachments", "spec": [{"name": "fileName", "type": "filename", "label": "File name", "required": true, "semantic": "file:name"}, {"name": "data", "type": "buffer", "label": "Data", "required": true, "semantic": "file:data"}, {"name": "cid", "type": "text", "label": "Content-ID"}], "type": "array", "label": "Attachments"}, {"name": "cc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Copy recipient"}, {"name": "bcc", "spec": {"name": "value", "type": "email", "label": "Email address"}, "type": "array", "label": "Blind copy recipient"}, {"name": "from", "type": "text", "label": "From"}, {"name": "sender", "type": "text", "label": "Sender"}, {"name": "replyTo", "type": "text", "label": "Reply-To"}, {"name": "inReplyTo", "type": "text", "label": "In-Reply-To"}, {"name": "references", "spec": {"name": "value", "type": "text", "label": "Reference", "required": true}, "type": "array", "label": "References"}, {"name": "priority", "type": "select", "label": "Priority", "validate": {"enum": ["high", "normal", "low"]}}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key", "required": true}, {"name": "value", "type": "text", "label": "Value", "required": true}], "type": "array", "label": "Headers"}, {"name": "text", "type": "text", "label": "Content"}]}, "onerror": [{"id": 30, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 561, "y": 863}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}]}]}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": true, "freshVariables": false}, "designer": {"orphans": []}, "zone": "eu2.make.com", "notes": [{"moduleIds": [9], "content": "<p>Identifies overdue tasks and stores their details in variables for email generation.</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [20], "content": "<p>Sends the progress report to stakeholders, keeping everyone informed about project milestones.</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [15], "content": "<p>Uses ChatGPT to generate a polished weekly progress summary based on the aggregated data.</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [18], "content": "<p>Collects task data from Airtable to prepare a summary for the weekly progress report.</p><p></p><h3><strong>How the Aggregator Works</strong></h3><p></p><p><strong><em>Data Collection:</em></strong></p><ul><li>Feeder module set to Bundle 5 (Airtable Search)</li><li>Collects all related data points from previous modules</li><li>Maintains relationships between different data sources</li></ul><p></p><p><strong><em>Data Structure:</em></strong></p><ul><li>Creates a unified array of objects</li><li>Each object contains complete task information</li><li>Preserves all necessary fields for reporting</li></ul>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [7], "content": "<p>Directs the workflow into two separate paths:</p><p>1.\t<strong>Weekly Progress Report Path</strong></p><p></p><p>2.\t<strong>Overdue Task Email Path</strong></p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [6], "content": "<p>Stores data from the Airtable search results or new entries into variables for use in subsequent modules. This reduces redundancy and ensures smooth data transfer within the workflow.</p><p></p><h3><strong>Date Parsing Explained- </strong></h3><p></p><p><strong><em>Due Date Parsing:</em></strong></p><ul><li>Input: Raw date from Airtable (5.Due Date)</li><li>Format: Uses parseDate() function with \"YYYY-MM-DD\" format</li><li>Example: \"2024-03-25\" becomes a standardized date object</li><li>Purpose: Enables date comparisons for overdue calculations</li></ul><p></p><p><strong><em>Today's Date Parsing:</em></strong></p><ul><li>Input: Current timestamp (now)</li><li>Format: Uses parseDate() function with \"YYYY-MM-DD\" format</li><li>Example: Current date \"2024-03-20\" becomes comparable format</li><li>Purpose: Used as reference point for overdue calculations</li></ul><p></p><p><strong><em>Tips for Date Handling</em></strong></p><ul><li>Always use consistent date formats (YYYY-MM-DD)</li><li>The parseDate() function converts dates to comparable values</li><li>Date comparison uses numerical values (earlier dates are smaller)</li><li>Store both raw and parsed dates if needed for different purposes</li></ul>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [4], "content": "<p>Creates a new record in the Airtable database to log project details. This serves as a centralized hub to track task updates, deadlines, and file links in an organized manner.</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [13], "content": "<p>This condition checks if the required project file is missing. If true, the workflow moves to the email reminder module to prompt team members to upload the missing file.</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [3], "content": "<p>Searches Google Drive for files or folders linked to the specific project. This ensures all relevant documents are pulled into the workflow automatically.</p><p></p><p><strong>Tips:</strong></p><p>- Use precise folder paths to limit search scope</p><p>- Implement error handling for missing files</p><p>- Consider file naming conventions for better matching</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [24], "content": "<p>These breaks are placeholders to maintain smooth execution flow and help in debugging. They act as checkpoints between critical actions, ensuring each step processes correctly before moving forward.</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}, {"moduleIds": [1], "content": "<p>This module monitors tasks in your Asana project. It gets triggered whenever a new task or subtask is created, ensuring the workflow starts automatically for every new project activity.</p><p></p><p><strong>Tips:</strong></p><p>- Keep the limit reasonable (10-20) to prevent overwhelming the system</p><p>- Use the \"Created Tasks\" filter to focus on new entries</p><p>- Configure error handling with retry attempts for API reliability</p>", "isFilterNote": false, "metadata": {"color": "#9138FE"}}]}}