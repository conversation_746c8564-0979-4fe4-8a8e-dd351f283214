{"name": "Find the Cheapest Flights Automatically with Bright Data & n8n", "nodes": [{"parameters": {}, "id": "6da8e17f-3448-4195-b812-43564470d376", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "position": [2360, 2080], "typeVersion": 1}, {"parameters": {"content": "=======================================\n            WORKFLOW ASSISTANCE\n=======================================\nFor any questions or support, please contact:\n    <EMAIL>\n\nExplore more tips and tutorials here:\n   - YouTube: https://www.youtube.com/@YaronBeen/videos\n   - LinkedIn: https://www.linkedin.com/in/yaronbeen/\n=======================================\n", "height": 320, "width": 1300, "color": 4}, "id": "6e92c67b-3e06-4de8-ac8f-f09d33bc6115", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"content": "# 🧳 ✈️ **Flight Price Scraper from Skiplagged using Bright Data**\n\nThis automation helps you extract **cheap flight prices** from [Skiplagged.com](https://skiplagged.com) using **Bright Data** and automatically logs those prices into a **Google Sheet** for review and tracking.\n\n---\n\n## 🔹 **SECTION 1: Getting the Flight Prices**\n\n*Combining: Manual Trigger + HTTP Request (Bright Data)*\n\n### 🌩️ 1. **Manual Trigger**\n\n* **🧠 What it does**: Starts the workflow when you click **Execute Workflow** in n8n.\n* **👨‍🏫 For beginners**: Think of it like a **start button**. No schedule, just run it manually whenever you want to check for deals.\n\n---\n\n### 🌐 2. **Fetch Flight Details from Skiplagged via Bright Data**\n\n* **🔧 Node Type**: `HTTP Request` (POST)\n\n* **🌍 What's happening**:\n\n  * Uses Bright Data Web Unlocker to **bypass bot protection** and fetch HTML content from Skiplagged.\n  * You’re sending a POST request to `https://api.brightdata.com/request`, asking Bright Data to open:\n    `https://skiplagged.com/flights/DUB/LON/2024-06-30` (or a dynamic route).\n  * Headers & proxy make it **look like a real browser**.\n\n* **💡 Why it’s useful**:\n\n  * **Skiplagged is tricky to scrape** directly, but Bright Data makes it easy.\n  * You get the **entire HTML** of the search results page, ready to extract price info.\n\n* ✅ **Returns**: HTML of the page (not yet structured — we handle that next)\n\n---\n\n## 🔹 **SECTION 2: Extracting & Storing Flight Prices**\n\n*Combining: HTML Node + Google Sheets*\n\n### 🖥️ 3. **HTML Extract (extractHtmlContent)**\n\n* **🔧 Node Type**: `HTML Extract`\n\n* **🎯 Goal**: Pull out **all flight prices** from the raw HTML.\n\n* **How it works**:\n\n  * Uses a selector like `.flights-landing__flight-price` to find every element that matches.\n  * **Returns multiple values** if \"Return All Matches\" is enabled.\n\n* 🧠 **Beginner Note**: Imagine your browser looking for all `<div class=\"flights-landing__flight-price\">`. This is like **copying all price tags off the screen**.\n\n* ✅ **Returns**:\n\n  ```json\n  [\n    \"$104\", \"$117\", \"$99\", \"$88\"\n  ]\n  ```\n\n---\n\n### 📊 4. **Google Sheets (append: sheet)**\n\n* **🔧 Node Type**: `Google Sheets Append`\n\n* **📋 What it does**: Appends each flight price as a new row in your spreadsheet.\n\n* **🛠️ Tip**: Make sure your sheet has one column titled something like `Price` and authentication is set up.\n\n* ❌ **Error Warning**: If this node is red, likely issues are:\n\n  * Missing Google credentials\n  * Sheet name doesn’t match\n  * Field mapping mismatch\n\n* ✅ **Returns**: New rows in your sheet like:\n\n| Price |\n| ----- |\n| \\$88  |\n| \\$99  |\n| \\$104 |\n\n---\n\n## 🌟 Final Thoughts: Why This Is Powerful\n\n✅ **Perfect for spontaneous travelers** who want to grab last-minute deals.\n✅ **Low-cost** thanks to free tools (n8n + Google Sheets) and flexible Bright Data usage.\n✅ **Easy to expand**: Add logic to filter cheapest flight, add Telegram/email alert, or schedule it daily.\n\n---\n\n## 🧭 Next Steps You Could Add\n\n* 📅 Add a **Cron node** to automate daily checks.\n* 📬 Add **Telegram or Email node** to notify when price < \\$100.\n* 🔧 Use **Set node** to make route and date dynamic.\n\n---\n\n\n", "height": 2458, "width": 1289, "color": 4}, "id": "ffb0ba94-e697-409d-9190-1e6f8656c775", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 580], "typeVersion": 1}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "Title", "cssSelector": "<span class=\"flights-landing__flight-logo-txt\" data-translate=\"false\">United</span>"}, {"key": "Price", "cssSelector": "<div class=\"flights-landing__flight-price\">$114</div>"}]}, "options": {}}, "id": "3e3a26e0-eeed-4a0f-85bb-ac49e438eb43", "name": "HTML", "type": "n8n-nodes-base.html", "position": [2860, 2080], "typeVersion": 1.2}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": ""}, "sheetName": {"__rl": true, "mode": "list", "value": ""}}, "id": "4213760f-1d00-4d4c-b918-5b46728c78e8", "name": "Google Sheets", "type": "n8n-nodes-base.googleSheets", "position": [3080, 2080], "typeVersion": 4.6}, {"parameters": {"method": "POST", "url": "https://api.brightdata.com/request", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer API_KEY"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"zone\": \"n8n_unblocker\",\n  \"url\": \"https://skiplagged.com/flights/NYC/LAX/2024-07-01\",\n  \"country\": \"ie\",\n  \"format\": \"raw\",\n  \"headers\": {\n    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n    \"Accept-Language\": \"en-US,en;q=0.9\",\n    \"Accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\"\n  }\n}", "options": {}}, "id": "c1bda948-e9f0-4539-ba18-fa37ac431d91", "name": "Fetch flight details from skiplegged via bright data", "type": "n8n-nodes-base.httpRequest", "position": [2580, 2080], "typeVersion": 4.2}, {"parameters": {"content": "## 🔹 **SECTION 1: Getting the Flight Prices**\n\n*Combining: Manual Trigger + HTTP Request (Bright Data)*\n\n### 🌩️ 1. **Manual Trigger**\n\n* **🧠 What it does**: Starts the workflow when you click **Execute Workflow** in n8n.\n* **👨‍🏫 For beginners**: Think of it like a **start button**. No schedule, just run it manually whenever you want to check for deals.\n\n---\n\n### 🌐 2. **Fetch Flight Details from Skiplagged via Bright Data**\n\n* **🔧 Node Type**: `HTTP Request` (POST)\n\n* **🌍 What's happening**:\n\n  * Uses Bright Data Web Unlocker to **bypass bot protection** and fetch HTML content from Skiplagged.\n  * You’re sending a POST request to `https://api.brightdata.com/request`, asking Bright Data to open:\n    `https://skiplagged.com/flights/DUB/LON/2024-06-30` (or a dynamic route).\n  * Headers & proxy make it **look like a real browser**.\n\n* **💡 Why it’s useful**:\n\n  * **Skiplagged is tricky to scrape** directly, but Bright Data makes it easy.\n  * You get the **entire HTML** of the search results page, ready to extract price info.\n\n* ✅ **Returns**: HTML of the page (not yet structured — we handle that next)\n\n---\n\n", "height": 1200, "width": 400, "color": 3}, "id": "6f4009d4-bd47-4bda-a4b1-cd6bdfa59821", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2340, 1080], "typeVersion": 1}, {"parameters": {"content": "## 🔹 **SECTION 2: Extracting & Storing Flight Prices**\n\n*Combining: HTML Node + Google Sheets*\n\n### 🖥️ 3. **HTML Extract (extractHtmlContent)**\n\n* **🔧 Node Type**: `HTML Extract`\n\n* **🎯 Goal**: Pull out **all flight prices** from the raw HTML.\n\n* **How it works**:\n\n  * Uses a selector like `.flights-landing__flight-price` to find every element that matches.\n  * **Returns multiple values** if \"Return All Matches\" is enabled.\n\n* 🧠 **Beginner Note**: Imagine your browser looking for all `<div class=\"flights-landing__flight-price\">`. This is like **copying all price tags off the screen**.\n\n* ✅ **Returns**:\n\n  ```json\n  [\n    \"$104\", \"$117\", \"$99\", \"$88\"\n  ]\n  ```\n\n---\n\n### 📊 4. **Google Sheets (append: sheet)**\n\n* **🔧 Node Type**: `Google Sheets Append`\n\n* **📋 What it does**: Appends each flight price as a new row in your spreadsheet.\n\n* **🛠️ Tip**: Make sure your sheet has one column titled something like `Price` and authentication is set up.\n\n* ❌ **Error Warning**: If this node is red, likely issues are:\n\n  * Missing Google credentials\n  * Sheet name doesn’t match\n  * Field mapping mismatch\n\n* ✅ **Returns**: New rows in your sheet like:\n\n| Price |\n| ----- |\n| \\$88  |\n| \\$99  |\n| \\$104 |\n\n---\n", "height": 1460, "width": 400, "color": 5}, "id": "15115b20-c08a-4953-be0b-8e7a7f2a4030", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2820, 820], "typeVersion": 1}, {"parameters": {"content": "## I’ll receive a tiny commission if you join Bright Data through this link—thanks for fueling more free content!\n\n### https://get.brightdata.com/1tndi4600b25", "height": 240, "width": 380, "color": 7}, "id": "b151fc17-d628-4e38-a5e7-91b2555b8e1f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [3300, 820], "typeVersion": 1}], "pinData": {}, "connections": {"HTML": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Manual Trigger": {"main": [[{"node": "Fetch flight details from skiplegged via bright data", "type": "main", "index": 0}]]}, "Fetch flight details from skiplegged via bright data": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "5218", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}