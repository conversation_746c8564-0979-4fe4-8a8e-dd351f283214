{"name": "Example -> Newsletters to Content Engine", "flow": [{"id": 11, "module": "google-email:TriggerNewEmail", "version": 2, "parameters": {"from": "<EMAIL>", "text": "", "folder": "[Gmail]/All Mail", "account": 4177549, "subject": "", "criteria": "ALL", "markSeen": false, "maxResults": 1, "searchType": "simple"}, "mapper": {}, "metadata": {"designer": {"x": -286, "y": 449, "name": "Get Newsletters"}, "restore": {"parameters": {"folder": {"path": ["All Mail", "All Mail"]}, "account": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Outworks Google Email (<EMAIL>)"}, "criteria": {"label": "All emails"}, "searchType": {"label": "Simple filter"}}}, "parameters": [{"name": "account", "type": "account:google-restricted", "label": "Connection", "required": true}, {"name": "searchType", "type": "select", "label": "Filter type", "required": true, "validate": {"enum": ["simple", "gmail"]}}, {"name": "mark<PERSON><PERSON>", "type": "boolean", "label": "Mark email message(s) as read when fetched"}, {"name": "maxResults", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Maximum number of results", "required": true}, {"name": "folder", "type": "folder", "label": "Folder", "required": true}, {"name": "criteria", "type": "select", "label": "Criteria", "required": true, "validate": {"enum": ["ALL", "SEEN", "UNSEEN"]}}, {"name": "from", "type": "email", "label": "Sender email address"}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "text", "type": "text", "label": "Search phrase"}], "advanced": true}}, {"id": 12, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 2700011}, "mapper": {"model": "o4-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "Role:\nYou are an expert LinkedIn content strategist and professional copywriter.\n\n---\n\nContext:\nYou help professionals transform newsletter text into a concise summary of the main ideas, then generate viral LinkedIn post ideas based on those ideas. The newsletter content may include multiple articles, curated news, or a single piece. Sponsored sections or advertisements are often included and must be ignored completely.\n\n---\n\nTask:\n1. Analyze the provided newsletter text.\n2. Ignore any sponsored or promotional sections.\n3. Identify the 3 most important core ideas and summarize each in 1–2 sentences.\n4. For each core idea, create 3 LinkedIn post ideas that are likely to go viral.\n\nEach LinkedIn idea must follow this structure:\n\nHook: Attention-grabbing first sentence.\nNarrative: Context or story.\nInsight: Useful takeaway or perspective.\nCall to Action: Prompt to engage or comment.\n\nEnsure the style is professional, human, clear, and personal-brand friendly. Make the writing scroll-stopping but natural.\n\nLimit each idea to 1300 characters or less.\n\n---\n\nOutput Format:\nReturn all 3 core ideas and their post ideas as an array called \"content_idea\". Use valid JSON in the following format:\n\n\"content_ideas\": [\n  { \n    coreIdea: \"the summary of the idea\",\n    postIdea1: { ...the first LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea2: { ...the second LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea3: { ...the third LinkedIn idea (object with hook, narrative, insight, callToAction)... }\n  },\n  {\n    coreIdea2: \"the summary of the idea\",\n    postIdea1: { ...the first LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea2: { ...the second LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea3: { ...the third LinkedIn idea (object with hook, narrative, insight, callToAction)... }\n  }\n]\n\nExample format:\n\n\"content_ideas\": [\n  {\n    \"coreIdea\": \"Summary of the first core idea.\",\n    \"postIdea1\": {\n      \"hook\": \"Your opening hook.\",\n      \"narrative\": \"The context or story.\",\n      \"insight\": \"The takeaway or perspective.\",\n      \"callToAction\": \"The engagement prompt.\"\n    },\n    \"postIdea2\": {\n      \"hook\": \"Another compelling hook.\",\n      \"narrative\": \"Supporting story or context.\",\n      \"insight\": \"Relevant takeaway.\",\n      \"callToAction\": \"Prompt to comment or share.\"\n    },\n    \"postIdea3\": {\n      \"hook\": \"Third unique hook.\",\n      \"narrative\": \"Additional angle or context.\",\n      \"insight\": \"Different perspective.\",\n      \"callToAction\": \"Prompt to engage.\"\n    }\n  },\n  ...next core idea...\n]\n\nReturn only valid JSON, do not include code fences or explainations.\n\n----\n\nNewsletter Text:\n{{11.text}}", "imageDetail": "auto"}], "max_tokens": "10000", "temperature": "1", "n_completions": "1", "response_format": "json_object", "parseJSONResponse": true}, "metadata": {"designer": {"x": 51, "y": 441, "name": "Generate Ideas"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "o4-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "tool_choice": {"mode": "chose", "label": "Empty"}, "response_format": {"mode": "chose", "label": "JSON Object"}, "reasoning_effort": {"mode": "chose", "label": "Empty"}, "parseJSONResponse": {"mode": "chose"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "tool_choice", "type": "select", "label": "Tool Choice", "validate": {"enum": ["none", "auto", "required"]}}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"mode": "edit", "name": "tool_calls", "spec": {"spec": [{"name": "type", "type": "hidden", "default": "function"}, {"help": "Map this directly from the output of a previous **Create a Completion** module. Look for `Choices[]: Message.Tool Calls[]: ID`.", "name": "id", "type": "text", "label": "Tool call ID"}, {"name": "function", "spec": [{"help": "The name of the function previously called.", "name": "name", "type": "text", "label": "Name", "required": true}, {"help": "The arguments previously output by the AI.", "name": "arguments", "type": "text", "label": "Arguments", "required": true}], "type": "collection", "label": "Function"}], "type": "collection", "label": "Tool Call"}, "type": "array", "label": "Tool Calls", "labels": {"add": "Add tool call"}, "mappable": {"help": "You can map the entire `Choices[]: Message.Tool Calls` array from a previous Create a Completion module here."}}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Tool", "value": "tool", "nested": [{"help": "The return of the function. This role should only be used when you have processed a previous function call and want to send the output of the function execution back to the AI.", "name": "content", "type": "text", "label": "Text Content", "required": true}, {"help": "Map this directly from the output of a previous **Create a Completion** module. Look for `Choices[]: Message.Tool Calls[]: ID`.", "name": "tool_call_id", "type": "text", "label": "Tool Call ID.", "required": true}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "reasoning_effort", "type": "select", "label": "Reasoning Effort", "validate": {"enum": ["low", "medium", "high"]}}, {"name": "parseJSONResponse", "type": "boolean", "label": "Parse JSON Response", "required": true}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "tool_calls", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "function", "spec": [{"name": "name", "type": "text", "label": "Name"}, {"name": "arguments", "type": "text", "label": "Arguments"}], "type": "collection", "label": "Function"}], "type": "array", "label": "Tool Calls"}, {"name": "refusal", "type": "text", "label": "Refusal"}, {"name": "annotations", "spec": [{"name": "type", "type": "text", "label": "Type"}, {"name": "url_citation", "spec": [{"name": "end_index", "type": "number", "label": "End Index"}, {"name": "start_index", "type": "number", "label": "Start Index"}, {"name": "title", "type": "text", "label": "Title"}, {"name": "url", "type": "text", "label": "URL"}], "type": "collection", "label": "URL Citation"}], "type": "array", "label": "Annotations"}], "type": "collection", "label": "Message"}], "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}], "advanced": true}}, {"id": 13, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{12.result.content_ideas}}"}, "metadata": {"designer": {"x": 393, "y": 439}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 14, "module": "google-sheets:addRow", "version": 2, "parameters": {"__IMTCONN__": 2699982}, "mapper": {"from": "drive", "mode": "select", "values": {"0": "{{13.core<PERSON><PERSON>}}", "1": "{{13.postIdea1.hook}}", "2": "{{13.postIdea1.narrative}}", "3": "{{13.postIdea2.hook}}", "4": "{{13.postIdea2.narrative}}", "5": "{{13.postIdea3.hook}}", "6": "{{13.postIdea3.narrative}}", "7": "{{11.from.name}} - {{11.from.address}}"}, "sheetId": "Sheet2", "spreadsheetId": "/1R-jliXqwcJQ1h4t4bL_hUHOAPW9Wmpyq/15TcbwO4cXaHnOmt8ZiWSlQwBoPalvCYl/12pzrF8md21eF3dOTQKe38wUY-g-wbcNE/12b2UnJI3cc5XDE2bwerEIbB7K3oN0Y8fjQvNthUAijM", "includesHeaders": true, "insertDataOption": "INSERT_ROWS", "valueInputOption": "USER_ENTERED", "insertUnformatted": false}, "metadata": {"designer": {"x": 734, "y": 428, "name": "Save Ideas"}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Sheet2"}, "spreadsheetId": {"path": ["Examples", "Make", "Sheets", "Newsletters to Content Engine"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "Content Ideas (A)"}, {"name": "1", "type": "text", "label": "(B)"}, {"name": "2", "type": "text", "label": "(C)"}, {"name": "3", "type": "text", "label": "(D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "insertDataOption": {"mode": "chose", "label": "Insert rows"}, "valueInputOption": {"mode": "chose", "label": "User entered"}, "insertUnformatted": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "insertUnformatted", "type": "boolean", "label": "Unformatted", "required": true}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "insertDataOption", "type": "select", "label": "Insert data option", "validate": {"enum": ["INSERT_ROWS", "OVERWRITE"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Content Ideas (A)"}, {"name": "1", "type": "text", "label": "(B)"}, {"name": "2", "type": "text", "label": "(C)"}, {"name": "3", "type": "text", "label": "(D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": [[{"id": 9, "module": "google-sheets:getSheetContent", "version": 2, "parameters": {"__IMTCONN__": 2699982}, "mapper": {"from": "drive", "range": "A2:A100", "select": "list", "sheetId": "Sheet1", "spreadsheetId": "/1R-jliXqwcJQ1h4t4bL_hUHOAPW9Wmpyq/15TcbwO4cXaHnOmt8ZiWSlQwBoPalvCYl/12pzrF8md21eF3dOTQKe38wUY-g-wbcNE/12b2UnJI3cc5XDE2bwerEIbB7K3oN0Y8fjQvNthUAijM", "tableFirstRow": "A1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": -284, "y": 5, "name": "Get RSS Feeds", "messages": [{"category": "link", "severity": "warning", "message": "The module is not connected to the data flow."}]}, "restore": {"expect": {"from": {"label": "My Drive"}, "select": {"label": "Search by path"}, "sheetId": {"label": "Sheet1"}, "spreadsheetId": {"path": ["Examples", "Make", "Sheets", "Newsletters to Content Engine"]}, "includesHeaders": {"mode": "chose"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["list", "fromAll", "map"]}}, {"name": "range", "type": "text", "label": "Range", "required": true}, {"name": "includesHeaders", "type": "boolean", "label": "Table contains headers", "required": true}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "tableFirstRow", "type": "text", "label": "Row with headers"}, {"name": "select", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["list", "fromAll", "map"]}}, {"name": "range", "type": "text", "label": "Range", "required": true}, {"name": "includesHeaders", "type": "boolean", "label": "Table contains headers", "required": true}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "tableFirstRow", "type": "text", "label": "Row with headers"}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "RSS Feed URL (A)"}]}}, {"id": 1, "module": "rss:ActionReadArticles", "version": 4, "parameters": {"include": []}, "mapper": {"url": "{{9.`0`}}", "gzip": true, "password": "", "username": "", "maxResults": "1", "filterDateTo": "", "filterDateFrom": "{{addDays(now; -1)}}"}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {}, "parameters": [{"name": "include", "type": "select", "label": "Process RSS fields", "multiple": true, "validate": {"enum": ["google-merchant-center", "itunes"]}}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "username", "type": "text", "label": "User name"}, {"name": "password", "type": "text", "label": "Password"}, {"name": "filterDateFrom", "type": "date", "label": "Date from"}, {"name": "filterDateTo", "type": "date", "label": "Date to "}, {"name": "maxResults", "type": "number", "label": "Maximum number of returned items", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}], "interface": [{"name": "title", "type": "text", "label": "Title"}, {"name": "description", "type": "text", "label": "Description"}, {"name": "summary", "type": "text", "label": "Summary"}, {"name": "author", "type": "text", "label": "Author"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "dateUpdated", "type": "date", "label": "Date updated"}, {"name": "dateCreated", "type": "date", "label": "Date created"}, {"name": "comments", "type": "url", "label": "Comments"}, {"name": "image", "spec": [{"name": "title", "type": "text", "label": "Name"}, {"name": "url", "type": "url", "label": "URL"}], "type": "collection", "label": "Image"}, {"name": "categories", "spec": {"type": "text"}, "type": "array", "label": "Categories"}, {"name": "source", "spec": [{"name": "title", "type": "text", "label": "Name"}, {"name": "url", "type": "url", "label": "URL"}], "type": "collection", "label": "Source"}, {"name": "enclosures", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "length", "type": "number", "label": "Length"}], "type": "array", "label": "Enclosures"}, {"help": "Other RSS fields. All values are in text format.", "name": "rssFields", "spec": [{"name": "title", "type": "text", "label": "title"}, {"name": "link", "type": "text", "label": "link"}, {"name": "guid", "type": "text", "label": "guid"}, {"name": "category", "type": "text", "label": "category"}, {"name": "pubdate", "type": "text", "label": "pubdate"}], "type": "collection", "label": "RSS fields"}]}}, {"id": 2, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "{{1.url}}", "gzip": true, "method": "get", "headers": [], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "", "serializeUrl": false, "shareCookies": false, "parseResponse": false, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 300, "y": 0}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "GET"}, "headers": {"mode": "chose"}, "bodyType": {"label": "Empty"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 3, "module": "regexp:HTMLToText", "version": 1, "parameters": {}, "mapper": {"html": "{{2.data}}", "newline": "lf", "uppercaseHeadings": true}, "metadata": {"designer": {"x": 600, "y": 0}, "restore": {"expect": {"newline": {"label": "LF (\\n) - Unix/Mac OS X"}}}, "expect": [{"name": "html", "type": "text", "label": "HTML"}, {"name": "newline", "type": "select", "label": "Line break", "required": true, "validate": {"enum": ["lf", "crlf", "cr"]}}, {"name": "uppercaseHeadings", "type": "boolean", "label": "Uppercase headings", "required": true}]}}, {"id": 5, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 2700011}, "mapper": {"model": "o4-mini", "top_p": "1", "select": "chat", "messages": [{"role": "user", "content": "Role:\nYou are an expert LinkedIn content strategist and professional copywriter.\n\n---\n\nContext:\nYou help professionals transform newsletter text into a concise summary of the main ideas, then generate viral LinkedIn post ideas based on those ideas. The newsletter content may include multiple articles, curated news, or a single piece. Sponsored sections or advertisements are often included and must be ignored completely.\n\n---\n\nTask:\n1. Analyze the provided newsletter text.\n2. Ignore any sponsored or promotional sections.\n3. Identify the 3 most important core ideas and summarize each in 1–2 sentences.\n4. For each core idea, create 3 LinkedIn post ideas that are likely to go viral.\n\nEach LinkedIn idea must follow this structure:\n\nHook: Attention-grabbing first sentence.\nNarrative: Context or story.\nInsight: Useful takeaway or perspective.\nCall to Action: Prompt to engage or comment.\n\nEnsure the style is professional, human, clear, and personal-brand friendly. Make the writing scroll-stopping but natural.\n\nLimit each idea to 1300 characters or less.\n\n---\n\nOutput Format:\nReturn all 3 core ideas and their post ideas as an array called \"content_idea\". Use valid JSON in the following format:\n\n\"content_ideas\": [\n  { \n    coreIdea: \"the summary of the idea\",\n    postIdea1: { ...the first LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea2: { ...the second LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea3: { ...the third LinkedIn idea (object with hook, narrative, insight, callToAction)... }\n  },\n  {\n    coreIdea2: \"the summary of the idea\",\n    postIdea1: { ...the first LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea2: { ...the second LinkedIn idea (object with hook, narrative, insight, callToAction)... },\n    postIdea3: { ...the third LinkedIn idea (object with hook, narrative, insight, callToAction)... }\n  }\n]\n\nExample format:\n\n\"content_ideas\": [\n  {\n    \"coreIdea\": \"Summary of the first core idea.\",\n    \"postIdea1\": {\n      \"hook\": \"Your opening hook.\",\n      \"narrative\": \"The context or story.\",\n      \"insight\": \"The takeaway or perspective.\",\n      \"callToAction\": \"The engagement prompt.\"\n    },\n    \"postIdea2\": {\n      \"hook\": \"Another compelling hook.\",\n      \"narrative\": \"Supporting story or context.\",\n      \"insight\": \"Relevant takeaway.\",\n      \"callToAction\": \"Prompt to comment or share.\"\n    },\n    \"postIdea3\": {\n      \"hook\": \"Third unique hook.\",\n      \"narrative\": \"Additional angle or context.\",\n      \"insight\": \"Different perspective.\",\n      \"callToAction\": \"Prompt to engage.\"\n    }\n  },\n  ...next core idea...\n]\n\nReturn only valid JSON, do not include code fences or explainations.\n\n----\n\nNewsletter Text:\n{{3.text}}", "imageDetail": "auto"}], "max_tokens": "10000", "temperature": "1", "n_completions": "1", "response_format": "json_object", "parseJSONResponse": true}, "metadata": {"designer": {"x": 900, "y": 0, "name": "Generate Ideas"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "o4-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "tool_choice": {"mode": "chose", "label": "Empty"}, "response_format": {"mode": "chose", "label": "JSON Object"}, "reasoning_effort": {"mode": "chose", "label": "Empty"}, "parseJSONResponse": {"mode": "chose"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "My OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "tool_choice", "type": "select", "label": "Tool Choice", "validate": {"enum": ["none", "auto", "required"]}}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"help": "Recommended value: `Auto`", "name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}]}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"mode": "edit", "name": "tool_calls", "spec": {"spec": [{"name": "type", "type": "hidden", "default": "function"}, {"help": "Map this directly from the output of a previous **Create a Completion** module. Look for `Choices[]: Message.Tool Calls[]: ID`.", "name": "id", "type": "text", "label": "Tool call ID"}, {"name": "function", "spec": [{"help": "The name of the function previously called.", "name": "name", "type": "text", "label": "Name", "required": true}, {"help": "The arguments previously output by the AI.", "name": "arguments", "type": "text", "label": "Arguments", "required": true}], "type": "collection", "label": "Function"}], "type": "collection", "label": "Tool Call"}, "type": "array", "label": "Tool Calls", "labels": {"add": "Add tool call"}, "mappable": {"help": "You can map the entire `Choices[]: Message.Tool Calls` array from a previous Create a Completion module here."}}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Tool", "value": "tool", "nested": [{"help": "The return of the function. This role should only be used when you have processed a previous function call and want to send the output of the function execution back to the AI.", "name": "content", "type": "text", "label": "Text Content", "required": true}, {"help": "Map this directly from the output of a previous **Create a Completion** module. Look for `Choices[]: Message.Tool Calls[]: ID`.", "name": "tool_call_id", "type": "text", "label": "Tool Call ID.", "required": true}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "reasoning_effort", "type": "select", "label": "Reasoning Effort", "validate": {"enum": ["low", "medium", "high"]}}, {"name": "parseJSONResponse", "type": "boolean", "label": "Parse JSON Response", "required": true}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "tool_calls", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "function", "spec": [{"name": "name", "type": "text", "label": "Name"}, {"name": "arguments", "type": "text", "label": "Arguments"}], "type": "collection", "label": "Function"}], "type": "array", "label": "Tool Calls"}, {"name": "refusal", "type": "text", "label": "Refusal"}, {"name": "annotations", "spec": [{"name": "type", "type": "text", "label": "Type"}, {"name": "url_citation", "spec": [{"name": "end_index", "type": "number", "label": "End Index"}, {"name": "start_index", "type": "number", "label": "Start Index"}, {"name": "title", "type": "text", "label": "Title"}, {"name": "url", "type": "text", "label": "URL"}], "type": "collection", "label": "URL Citation"}], "type": "array", "label": "Annotations"}], "type": "collection", "label": "Message"}], "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}], "advanced": true}}, {"id": 7, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{5.result.content_ideas}}"}, "metadata": {"designer": {"x": 1242, "y": -2}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 8, "module": "google-sheets:addRow", "version": 2, "parameters": {"__IMTCONN__": 2699982}, "mapper": {"from": "drive", "mode": "select", "values": {"0": "{{7.core<PERSON><PERSON>}}", "1": "{{7.postIdea1.hook}}", "2": "{{7.postIdea1.narrative}}", "3": "{{7.postIdea2.hook}}", "4": "{{7.postIdea2.narrative}}", "5": "{{7.postIdea3.hook}}", "6": "{{7.postIdea3.narrative}}", "7": "{{1.url}}"}, "sheetId": "Sheet2", "spreadsheetId": "/1R-jliXqwcJQ1h4t4bL_hUHOAPW9Wmpyq/15TcbwO4cXaHnOmt8ZiWSlQwBoPalvCYl/12pzrF8md21eF3dOTQKe38wUY-g-wbcNE/12b2UnJI3cc5XDE2bwerEIbB7K3oN0Y8fjQvNthUAijM", "includesHeaders": true, "insertDataOption": "INSERT_ROWS", "valueInputOption": "USER_ENTERED", "insertUnformatted": false}, "metadata": {"designer": {"x": 1583, "y": -13, "name": "Save Ideas"}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Sheet2"}, "spreadsheetId": {"path": ["Examples", "Make", "Sheets", "Newsletters to Content Engine"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "Content Ideas (A)"}, {"name": "1", "type": "text", "label": "(B)"}, {"name": "2", "type": "text", "label": "(C)"}, {"name": "3", "type": "text", "label": "(D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "insertDataOption": {"mode": "chose", "label": "Insert rows"}, "valueInputOption": {"mode": "chose", "label": "User entered"}, "insertUnformatted": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "insertUnformatted", "type": "boolean", "label": "Unformatted", "required": true}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "insertDataOption", "type": "select", "label": "Insert data option", "validate": {"enum": ["INSERT_ROWS", "OVERWRITE"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "Content Ideas (A)"}, {"name": "1", "type": "text", "label": "(B)"}, {"name": "2", "type": "text", "label": "(C)"}, {"name": "3", "type": "text", "label": "(D)"}, {"name": "4", "type": "text", "label": "(E)"}, {"name": "5", "type": "text", "label": "(F)"}, {"name": "6", "type": "text", "label": "(G)"}, {"name": "7", "type": "text", "label": "(H)"}, {"name": "8", "type": "text", "label": "(I)"}, {"name": "9", "type": "text", "label": "(J)"}, {"name": "10", "type": "text", "label": "(K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}]]}, "zone": "us1.make.com", "notes": []}}