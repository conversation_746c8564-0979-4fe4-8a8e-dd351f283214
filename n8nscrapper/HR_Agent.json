{"name": "HR Agent", "nodes": [{"parameters": {"formTitle": "Job Application", "formDescription": "=Fill this for to apply for the role Automation Specialist:\n\nLocation: Remote\nExperience: Minimum 3 years\nEmployment Type: Full-time\n\nJob Description:\nWe are seeking a highly skilled Automation Specialist with at least 3 years of experience in designing and implementing workflow automation solutions. The ideal candidate will have expertise in tools such as n8n, Zapier, Make.com, or similar platforms, and a strong background in integrating APIs, streamlining processes, and enhancing operational efficiency.\n\nKey Responsibilities:\n\n Develop and implement automated workflows to optimize business processes.\n Integrate third-party APIs and systems to create seamless data flow.\n Analyze, debug, and improve existing automation setups.\n Collaborate with cross-functional teams to identify automation opportunities.\n Monitor and maintain automation systems to ensure reliability.\n\nRequired Skills & Qualifications:\n\n Proven 3+ years of experience in workflow automation and integration.\n Proficiency with tools like n8n, Zapier, or Make.com.\n Strong understanding of APIs, webhooks, and data transformation.\n Familiarity with scripting languages (e.g., JavaScript or Python).\n Excellent problem-solving and communication skills.\n\nPreferred Qualifications:\n\n Experience with database management and cloud services.\n Background in business process analysis or RPA tools.\n\nWhy Join Us?\n\n Opportunity to work on cutting-edge automation projects.\n Supportive and collaborative team environment.\n Competitive salary and benefits package.", "formFields": {"values": [{"fieldLabel": "First Name", "requiredField": true}, {"fieldLabel": "Last Name", "requiredField": true}, {"fieldLabel": "Email", "fieldType": "email", "requiredField": true}, {"fieldLabel": "Phone", "fieldType": "number", "requiredField": true}, {"fieldLabel": "Years of experience", "fieldType": "number", "requiredField": true}, {"fieldLabel": "Upload your CV", "fieldType": "file", "acceptFileTypes": ".pdf", "requiredField": true}]}, "options": {"appendAttribution": false, "buttonLabel": "Submit", "path": "automation-specialist-application", "ignoreBots": false, "useWorkflowTimezone": true}}, "id": "dff7d47f-ea04-49e8-93c7-eed9331e0bca", "name": "On form submission", "type": "n8n-nodes-base.formTrigger", "position": [-1660, 260], "webhookId": "18f7428c-9990-413f-aff3-bdcca1bbbe2d", "typeVersion": 2.2}, {"parameters": {"operation": "create", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"Name": "={{ $json.Name }}", "Phone": "={{ $json.Phone }}", "CV Link": "={{ $json[\"CV link\"] }}", "Applying for": "=[\"Automation Specialist\"]", "Email address": "={{ $json.email }}"}, "schema": [{"id": "Name", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": []}, "options": {"typecast": true}}, "id": "6276545e-684d-4d60-9156-8be2d97116aa", "name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [-1020, 260], "typeVersion": 2.1}, {"parameters": {"inputDataFieldName": "Upload_your_CV", "name": "={{ $binary.Upload_your_CV.fileName }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "1u_YBpqSU5TjNsu72sQKFMIesb62JKHXz", "cachedResultUrl": "https://drive.google.com/drive/folders/1u_YBpqSU5TjNsu72sQKFMIesb62JKHXz", "cachedResultName": "HR Test"}, "options": {}}, "id": "6b0fa3e7-5586-4d85-9eb0-4eba002f5661", "name": "Upload CV to google drive", "type": "n8n-nodes-base.googleDrive", "position": [-1440, 260], "typeVersion": 3}, {"parameters": {"assignments": {"assignments": [{"id": "bffff778-859a-4bb8-b973-39237ce7486e", "name": "Name", "type": "string", "value": "={{ $('On form submission').item.json['First Name'] + \" \" + $('On form submission').item.json['Last Name'] }}"}, {"id": "cd6e7372-c65f-4e6f-9612-6ea513bb8e15", "name": "Phone", "type": "number", "value": "={{ $('On form submission').item.json.Phone }}"}, {"id": "eb19138e-7ff3-4f0c-ad95-ac33f8835717", "name": "email", "type": "string", "value": "={{ $('On form submission').item.json.Email }}"}, {"id": "25172db9-91fb-45da-b036-ee9aea1e8b09", "name": "Experience", "type": "number", "value": "={{ $('On form submission').item.json[\"Years of experience\"] }}"}, {"id": "64393285-3770-47e0-bbbb-3c5d5e14f1f4", "name": "Applied On", "type": "string", "value": "={{ $('On form submission').item.json.submittedAt }}"}, {"id": "dc052fd6-f57d-4da1-9976-67fcd9496e58", "name": "CV link", "type": "string", "value": "={{ $json.webViewLink }}"}]}, "options": {}}, "id": "cb1737b1-4521-4bbb-a1fa-80c896593607", "name": "applicant details", "type": "n8n-nodes-base.set", "position": [-1220, 260], "typeVersion": 3.4}, {"parameters": {"content": "## Grab User Details and Update in Airtable\n", "height": 220, "width": 800, "color": 3}, "id": "f0ca4aae-2c43-489a-ade1-9e74dec01ef3", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-1680, 200], "typeVersion": 1}, {"parameters": {"content": "## Download the CV and get the job description and requirements.\n- ### Send the details to ChatGPT to score the viability of the candidate", "height": 460, "width": 820}, "id": "552928b9-d81a-4f3f-9a7d-6c3897d14aa5", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-840, 180], "typeVersion": 1}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "mode": "url", "value": "={{ $json.fields[\"CV Link\"] }}"}, "options": {}}, "id": "3641ac61-4051-4afe-863d-31a32ca1fe6f", "name": "download CV", "type": "n8n-nodes-base.googleDrive", "position": [-820, 280], "typeVersion": 3}, {"parameters": {"operation": "pdf", "options": {}}, "id": "8c6115d1-37ef-4c10-9235-d86af1ea5a3c", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [-600, 280], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "=Compare the following job description and resume. Assign a qualification score between 0 and 1, where 1 indicates the best match. Provide only the score and the reason for the score in less than 20 words.\nJob Description: Use Airtable tool to get the job description\nResume: \n{{ $json.text }}", "hasOutputParser": true, "options": {}}, "id": "d3f8dbbf-d18e-4a31-aaf3-54b200c11445", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-380, 280], "typeVersion": 1.7}, {"parameters": {"options": {}}, "id": "4b969eec-1c66-4d03-bc41-a3b27a9c22b4", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [-460, 480], "typeVersion": 1}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}}, "id": "e23ce7b8-800c-477a-914e-c59d2fff2aaa", "name": "Airtable1", "type": "n8n-nodes-base.airtableTool", "position": [-260, 480], "typeVersion": 2.1}, {"parameters": {"jsonSchemaExample": "{\n \"score\": 0.8,\n \"reason\": \"Does not meet required number of experience in years\"\n}"}, "id": "b3129efc-eb9a-4131-af38-7e4e5b1811cf", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-140, 480], "typeVersion": 1.2}, {"parameters": {"content": "## Update Airtable with score and reason for the score\n\n- ### if score is above 0.7, shortlist and continue flow.\n\n## Get questionnaires based on the JD and CV\n\n- ### Update the responses in Airtable", "height": 600, "width": 1220, "color": 2}, "id": "8d94d75e-df3b-435a-bc04-f74fc0309449", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [0, 40], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "7b4950b2-d218-4911-89cd-22a60b7465d8", "operator": {"type": "number", "operation": "gte"}, "leftValue": "={{ $json.output.score }}", "rightValue": 0.7}]}, "options": {}}, "id": "5f0cc90a-6b94-4f94-bd8b-7c13ade8854a", "name": "shortlisted?", "type": "n8n-nodes-base.if", "position": [0, 280], "typeVersion": 2.2}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id }}", "Stage": "No hire", "JD CV score": "={{ $json.output.score }}", "CV Score Notes": "={{ $json.output.reason }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": false, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}}, "id": "9285d308-7b37-43c8-9e34-66f074361309", "name": "Rejected", "type": "n8n-nodes-base.airtable", "position": [280, 420], "typeVersion": 2.1}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id }}", "Stage": "Interviewing", "JD CV score": "={{ $json.output.score }}", "CV Score Notes": "={{ $json.output.reason }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": false, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}}, "id": "5904ee50-944e-479d-8d76-b8989dcde231", "name": "Potential Hire", "type": "n8n-nodes-base.airtable", "position": [280, 220], "typeVersion": 2.1}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}}, "id": "5b84988a-019f-4350-a34d-740c0241a8b8", "name": "Airtable2", "type": "n8n-nodes-base.airtableTool", "position": [600, 460], "typeVersion": 2.1}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Given the following job description and candidate CV, create 5 insightful interview questions to gather more information about the candidate's suitability for the role. The questions should focus on:\n\n Specific projects the candidate has worked on.\n Key responsibilities and achievements in their previous roles.\n Skills relevant to the job description.\n Problem-solving abilities and how they handled challenges.\n Alignment with the company’s goals and values.\n\nProvide the questions in a clear, concise format.\n\nJob Description:\nUse the airtable tool to get the job description\n\nCandidate CV:\n{{ $('Extract from File').item.json.text }}"}]}, "jsonOutput": true, "options": {}}, "id": "e9b2b01d-d757-4798-870e-2f5b478378a6", "name": "generate questionnaires", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [500, 220], "typeVersion": 1.7}, {"parameters": {"formFields": {"values": [{"fieldLabel": "={{ $json.message.content.interview_questions[0].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[1].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[2].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[3].question }}", "requiredField": true}, {"fieldLabel": "={{ $json.message.content.interview_questions[4].question }}", "requiredField": true}]}, "options": {"formTitle": "Questionnaires", "formDescription": "Kindly fill in the following questions to proceed.", "buttonLabel": "Submit"}}, "id": "1c7f3745-0e50-4d51-b6e0-67a94a571bd1", "name": "questionnaires", "type": "n8n-nodes-base.form", "position": [860, 220], "webhookId": "3f654280-b5d0-4392-824f-bc384d91a1df", "typeVersion": 1}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('Airtable').item.json.id }}", "Questonnaires and responses": "={{ $('generate questionnaires').item.json.message.content.interview_questions[0].question }}: {{ $json['Can you describe one of the most complex automation projects you worked on, particularly detailing your role and the technologies you used?'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[1].question }}: {{ $json['What specific achievements in your previous roles do you believe demonstrate your ability to meet the responsibilities listed in the Automation Specialist position?'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[2].question }}: {{ $json['Given your experience with automation tools like n8n and APIs, can you provide an example of how you\\'ve successfully integrated different systems to improve operational efficiency?'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[3].question }}: {{ $json['Describe a challenging situation you faced during a project, how you approached the problem, and what the outcome was.'] }}\n\n\n{{ $('generate questionnaires').item.json.message.content.interview_questions[4].question }}: {{ $json['How do your values and career goals align with our company\\'s mission to optimize and enhance automation solutions?'] }}\n\n"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Questonnaires and responses", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Questonnaires and responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}}, "id": "1fb82f6d-c347-4296-957d-e4470dded085", "name": "update questionnaires", "type": "n8n-nodes-base.airtable", "position": [1080, 220], "typeVersion": 2.1}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}}, "id": "bf5fcc89-5a84-4ec1-a637-02c9d7e420cc", "name": "job_posting", "type": "n8n-nodes-base.airtableTool", "position": [1340, 460], "typeVersion": 2.1}, {"parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "id": "={{ $('update questionnaires').item.json.id }}", "options": {}}, "id": "42cea456-04cb-433b-917e-81c5e74e0417", "name": "candidate_insights", "type": "n8n-nodes-base.airtableTool", "position": [1460, 460], "typeVersion": 2.1}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "=Craft a personalized email to the interviewee, expressing interest in continuing the conversation over a phone call. The email should mention strengths or achievements from their CV or questionnaire responses, and include a polite request to have the phone conversation. Ensure the tone is professional and warm.\n\nProvide an output of \nTo:\nSubject:\nEmail Content:\n\nInputs:\n\n The candidate's CV.\n The job description.\n The candidate's questionnaire responses stored in Airtable.\n\n\nExample email:\nDear [Candidate's Name],\n\nThank you for submitting your application and responses to the questionnaire for the [Job Title] position. We were impressed by [specific strength or achievement from their CV or questionnaire, e.g., \"your experience in automating workflows using n8n, which aligns closely with our goals\"].\n\nWe’d love to continue the conversation to discuss your experience further. \n\nLooking forward to speaking with you soon.\n\n\n\nNOTE: \nSign off the email with\n\n<PERSON><PERSON>,\n<PERSON>"}]}, "jsonOutput": true, "options": {}}, "id": "e1c30ded-9346-437f-97c0-afb400d25344", "name": "Personalize email", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1300, 220], "typeVersion": 1.7}, {"parameters": {"assignments": {"assignments": [{"id": "b3d6e85e-c478-452d-aafc-c325dfbe2c9b", "name": "To", "type": "string", "value": "={{ $json.message.content.To }}"}, {"id": "f24eb1d5-fa61-48ce-8685-a0b2022bf576", "name": "Subject", "type": "string", "value": "={{ $json.message.content.Subject }}"}, {"id": "25de1423-b66a-4389-906f-8b0c9c1d3826", "name": "Email Content", "type": "string", "value": "={{ $json.message.content['Email Content'] }}"}]}, "options": {}}, "id": "7ee65849-0894-479d-8a1e-73e2c20a92c7", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1660, 220], "typeVersion": 3.4}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.To }}", "subject": "={{ $json.Subject }}", "emailFormat": "text", "text": "={{ $json['Email Content'] }}", "options": {"appendAttribution": false}}, "id": "2a6fa187-32a3-442a-a2f7-c1746a4936ed", "name": "Send Email", "type": "n8n-nodes-base.emailSend", "position": [1880, 220], "typeVersion": 2.1, "webhookId": "d41551f4-f42f-47ef-b22d-30fff15c319e"}, {"parameters": {"content": "## Personalize email and send\n\n## Schedule Meeting and update meeting time in AIrtable", "height": 560, "width": 1340, "color": 5}, "id": "0a023357-b1b5-4ccd-8490-99a66c8c8881", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1260, 80], "typeVersion": 1}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "=Check the interviewer's calendar for available 30-minute time slots within working hours (8 AM - 5 PM) the next day. Schedule the meeting and confirm the time with the candidate. Ensure that the meeting time is aligned with the candidate's and interviewer's availability.\n\nInputs:\n\n The interviewer's calendar for scheduling.\n Today's date: {{ $today }}\n\nUse the calendar tool to book the meeting\n\n\nGive back the follwoing information:\nStart time:\nEnd time:"}]}, "jsonOutput": true, "options": {}}, "id": "93e103a7-c37f-41f9-b783-faa929c51c40", "name": "Book Meeting", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2100, 220], "typeVersion": 1.7}, {"parameters": {"calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "start": "={{ $fromAI(\"start_time\", \"The start time for the meeting\", \"string\", \"2025-01-01T09:00:00Z\") }}\n", "end": "={{ $fromAI(\"end_time\", \"The end time for the meeting\", \"string\", \"2025-01-01T09:00:00Z\") }}", "additionalFields": {"location": "=Online"}}, "id": "c797a28b-9607-40f1-a5db-308cb01ec2eb", "name": "Google Calendar", "type": "n8n-nodes-base.googleCalendarTool", "position": [2200, 440], "typeVersion": 1.2}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('update questionnaires').item.json.id }}", "Phone interview": "={{ $json.message.content['Start time'] }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Questonnaires and responses", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Questonnaires and responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "dateTime", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}}, "id": "44f23a47-dc27-429f-a2b2-92b68cc55b4e", "name": "update phone meeting time", "type": "n8n-nodes-base.airtable", "position": [2480, 220], "typeVersion": 2.1}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "=Given the job description, along with the candidate's CV and their responses to the questionnaires, generate a list of screening questions that will help gauge the candidate's suitability for the role. The questions should focus on understanding the candidate’s relevant experience, skills, and cultural fit. The questions should take into account both the job description and the candidate's background and responses. Provide a minimum of 5 questions.\n\nUse the tools to get the job description and the applicant's responses to the questionnaires.\n\nApplicant's CV:\n{{ $('Extract from File').item.json.text }}\n\n\nGive the output as various sentences as a paragraph with every new question in a new line:\nScreening Questions:"}]}, "jsonOutput": true, "options": {}}, "id": "0fbe8636-ce6c-4790-94f1-3529f7002523", "name": "Screening Questions", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2700, 220], "typeVersion": 1.7}, {"parameters": {"operation": "search", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tbljhmLdPULqSya0d", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tbljhmLdPULqSya0d", "cachedResultName": "Positions"}, "options": {}}, "id": "b4e14cd6-8c7a-47d1-9e29-6a34e49c3805", "name": "job_posting1", "type": "n8n-nodes-base.airtableTool", "position": [2720, 440], "typeVersion": 2.1}, {"parameters": {"base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "id": "={{ $('update questionnaires').item.json.id }}", "options": {}}, "id": "a819d5ed-9308-4982-a9d5-3491d42eded4", "name": "candidate_insights1", "type": "n8n-nodes-base.airtableTool", "position": [2920, 440], "typeVersion": 2.1}, {"parameters": {"operation": "update", "base": {"__rl": true, "mode": "list", "value": "appublMkWVQfHkZ09", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09", "cachedResultName": "Simple applicant tracker"}, "table": {"__rl": true, "mode": "list", "value": "tblllvQaRTSnEr17a", "cachedResultUrl": "https://airtable.com/appublMkWVQfHkZ09/tblllvQaRTSnEr17a", "cachedResultName": "Applicants"}, "columns": {"value": {"id": "={{ $('update phone meeting time').item.json.id }}", "Phne interview screening questions": "={{ $json['Screening Questions'] }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "id", "defaultMatch": true}, {"id": "Name", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Email address", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Email address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Stage", "type": "options", "display": true, "options": [{"name": "No hire", "value": "No hire"}, {"name": "Interviewing", "value": "Interviewing"}, {"name": "Decision needed", "value": "Decision needed"}, {"name": "<PERSON>re", "value": "<PERSON>re"}], "removed": true, "readOnly": false, "required": false, "displayName": "Stage", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Applying for", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Applying for", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Link", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "JD CV score", "type": "number", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "JD CV score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "CV Score Notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "CV Score Notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Questonnaires and responses", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Questonnaires and responses", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phne interview screening questions", "type": "string", "display": true, "removed": false, "readOnly": false, "required": false, "displayName": "Phne interview screening questions", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Phone interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Phone interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview", "type": "dateTime", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interviewer", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interviewer", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview score", "type": "options", "display": true, "options": [{"name": "0 – No hire", "value": "0 – No hire"}, {"name": "1 – Probably no hire", "value": "1 – Probably no hire"}, {"name": "2 – Worth consideration", "value": "2 – Worth consideration"}, {"name": "3 – Good candidate", "value": "3 – Good candidate"}, {"name": "4 – Please hire this person", "value": "4 – Please hire this person"}], "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview score", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Onsite interview notes", "type": "string", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Onsite interview notes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Attachments", "type": "array", "display": true, "removed": true, "readOnly": false, "required": false, "displayName": "Attachments", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["id"]}, "options": {}}, "id": "71fb8fe5-5f41-4735-9ccc-c0e7eb27f94f", "name": "screening questions", "type": "n8n-nodes-base.airtable", "position": [3280, 220], "typeVersion": 2.1}, {"parameters": {"assignments": {"assignments": [{"id": "d51edc4a-60cd-41fe-8cc3-afc3c266d588", "name": "Screening Questions", "type": "string", "value": "={{ $json.message.content['Screening Questions'] }}"}]}, "options": {}}, "id": "deec4ee9-532e-4d51-a9cc-839b0a0ce515", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "position": [3060, 220], "typeVersion": 3.4}, {"parameters": {"content": "## Generate Screening Questions and post to Airtable", "height": 480, "width": 720}, "id": "f3c9e46d-9858-4f83-ac1e-495309da6a98", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [2680, 160], "typeVersion": 1}, {"parameters": {"content": "## Actions\n- ### Change the `Form Description` with the job description you are hiring for.\n- ### Make sure to check and change the prompts if need be to suit your use case.\n- ### Use the Simple Applicant Tracker template on Airtable to set up the tables required.", "height": 460, "width": 580}, "id": "9a6c6c1b-bda1-417f-8fd4-3f239214e004", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-2360, 80], "typeVersion": 1}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "shortlisted?", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "download CV", "type": "main", "index": 0}]]}, "Airtable1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Airtable2": {"ai_tool": [[{"node": "generate questionnaires", "type": "ai_tool", "index": 0}]]}, "Send Email": {"main": [[{"node": "Book Meeting", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "download CV": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "job_posting": {"ai_tool": [[{"node": "Personalize email", "type": "ai_tool", "index": 0}]]}, "Book Meeting": {"main": [[{"node": "update phone meeting time", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "screening questions", "type": "main", "index": 0}]]}, "job_posting1": {"ai_tool": [[{"node": "Screening Questions", "type": "ai_tool", "index": 0}]]}, "shortlisted?": {"main": [[{"node": "Potential Hire", "type": "main", "index": 0}], [{"node": "Rejected", "type": "main", "index": 0}]]}, "Potential Hire": {"main": [[{"node": "generate questionnaires", "type": "main", "index": 0}]]}, "questionnaires": {"main": [[{"node": "update questionnaires", "type": "main", "index": 0}]]}, "Google Calendar": {"ai_tool": [[{"node": "Book Meeting", "type": "ai_tool", "index": 0}]]}, "Extract from File": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Personalize email": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "applicant details": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "On form submission": {"main": [[{"node": "Upload CV to google drive", "type": "main", "index": 0}]]}, "candidate_insights": {"ai_tool": [[{"node": "Personalize email", "type": "ai_tool", "index": 0}]]}, "Screening Questions": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "candidate_insights1": {"ai_tool": [[{"node": "Screening Questions", "type": "ai_tool", "index": 0}]]}, "update questionnaires": {"main": [[{"node": "Personalize email", "type": "main", "index": 0}]]}, "generate questionnaires": {"main": [[{"node": "questionnaires", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Upload CV to google drive": {"main": [[{"node": "applicant details", "type": "main", "index": 0}]]}, "update phone meeting time": {"main": [[{"node": "Screening Questions", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "553a7112-57a5-48ae-a457-7abe57e33620", "meta": {"instanceId": "b2189e49751062fbda95b4ca9399267c1bf87960485d2972d5aae0b9437119d2"}, "id": "gfOCBGZIpypeQwwC", "tags": [{"name": "HR", "id": "1Q3hm5OJ06lRygVC", "createdAt": "2025-05-30T13:50:23.647Z", "updatedAt": "2025-05-30T13:50:23.647Z"}]}