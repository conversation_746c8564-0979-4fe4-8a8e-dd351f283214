{"active": false, "connections": {"Config": {"main": [[{"node": "Get file data", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Get File": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "File is new": {"main": [[{"node": "Create new file", "type": "main", "index": 0}]]}, "Merge Items": {"main": [[{"node": "isDiffOrNew", "type": "main", "index": 0}]]}, "isDiffOrNew": {"main": [[{"node": "Create sub path", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Same file - Do nothing", "type": "main", "index": 0}], [{"node": "File is different", "type": "main", "index": 0}], [{"node": "File is new", "type": "main", "index": 0}]]}, "Get file data": {"main": [[{"node": "If file too large", "type": "main", "index": 0}]]}, "Create new file": {"main": [[{"node": "Return", "type": "main", "index": 0}]]}, "Create sub path": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Send a text message", "type": "main", "index": 0}], [{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get many workflows", "type": "main", "index": 0}]]}, "File is different": {"main": [[{"node": "Edit existing file", "type": "main", "index": 0}]]}, "If file too large": {"main": [[{"node": "Get File", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Edit existing file": {"main": [[{"node": "Return", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Get many workflows", "type": "main", "index": 0}]]}, "Same file - Do nothing": {"main": [[{"node": "Return", "type": "main", "index": 0}]]}, "Send a text message": {"main": [[]]}, "Get many workflows": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "Config", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "createdAt": "2025-07-24T12:54:29.851Z", "id": "ZfhZvS4ZOvP5Ppg2", "isArchived": true, "meta": {"templateCredsSetupCompleted": true}, "name": "Orquestrado<PERSON>", "nodes": [{"parameters": {}, "id": "353dd76e-4655-47b6-a971-e35be5227c1b", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [1248, 144], "typeVersion": 1}, {"parameters": {"content": "## Subworkflow", "height": 731.7039821513649, "width": 1910.7813046051347, "color": 6}, "id": "0fa45b78-89b0-4ba1-b245-6def427498fe", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [768, 528], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "8d513345-6484-431f-afb7-7cf045c90f4f", "name": "Done", "type": "boolean", "value": true}]}, "options": {}}, "id": "bced9ada-0270-4eeb-9661-191b27b33d73", "name": "Return", "type": "n8n-nodes-base.set", "position": [2704, 736], "typeVersion": 3.3}, {"parameters": {"url": "={{ $json.download_url }}", "options": {}}, "id": "acacdac6-7712-4305-990a-fbc4d52fef41", "name": "Get File", "type": "n8n-nodes-base.httpRequest", "position": [1664, 608], "typeVersion": 4.2}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "45ce825e-9fa6-430c-8931-9aaf22c42585", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.content }}", "rightValue": ""}], "combinator": "and"}, "options": {}}, "id": "5c8266b8-542c-4a16-bb0a-8d2a0f9fa984", "name": "If file too large", "type": "n8n-nodes-base.if", "position": [1424, 624], "typeVersion": 2}, {"parameters": {}, "id": "c1e7fccd-36be-4e5e-88a0-45f471cabc9d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1424, 896], "typeVersion": 2}, {"parameters": {"jsCode": "const orderJsonKeys = (jsonObj) => {\n  const ordered = {};\n  Object.keys(jsonObj).sort().forEach(key => {\n    ordered[key] = jsonObj[key];\n  });\n  return ordered;\n}\n\n// Check if file returned with content\nif (Object.keys($input.all()[0].json).includes(\"content\")) {\n  // Decode base64 content and parse JSON\n  const origWorkflow = ($input.all()[0].json.content, 'base64');\n  const n8nWorkflow = $input.all()[1].json;\n  \n  // Order JSON objects\n  const orderedOriginal = orderJsonKeys(origWorkflow);\n  const orderedActual = orderJsonKeys(n8nWorkflow);\n\n  // Determine difference\n  if (JSON.stringify(orderedOriginal) === JSON.stringify(orderedActual)) {\n    $input.all()[0].json.github_status = \"same\";\n  } else {\n    $input.all()[0].json.github_status = \"different\";\n    $input.all()[0].json.n8n_data_stringy = JSON.stringify(orderedActual, null, 2);\n  }\n  $input.all()[0].json.content_decoded = orderedOriginal;\n// No file returned / new workflow\n} else if (Object.keys($input.all()[0].json).includes(\"data\")) {\n  const origWorkflow = JSON.parse($input.all()[0].json.data);\n  const n8nWorkflow = $input.all()[1].json;\n  \n  // Order JSON objects\n  const orderedOriginal = orderJsonKeys(origWorkflow);\n  const orderedActual = orderJsonKeys(n8nWorkflow);\n\n  // Determine difference\n  if (JSON.stringify(orderedOriginal) === JSON.stringify(orderedActual)) {\n    $input.all()[0].json.github_status = \"same\";\n  } else {\n    $input.all()[0].json.github_status = \"different\";\n    $input.all()[0].json.n8n_data_stringy = JSON.stringify(orderedActual, null, 2);\n  }\n  $input.all()[0].json.content_decoded = orderedOriginal;\n\n} else {\n  // Order JSON object\n  const n8nWorkflow = $input.all()[1].json;\n  const orderedActual = orderJsonKeys(n8nWorkflow);\n  \n  // Proper formatting\n  $input.all()[0].json.github_status = \"new\";\n  $input.all()[0].json.n8n_data_stringy = JSON.stringify(orderedActual, null, 2);\n}\n\n// Return items\nreturn $input.all();\n"}, "id": "c32769f1-744b-4d63-acbf-01216c35d052", "name": "isDiffOrNew", "type": "n8n-nodes-base.code", "position": [1616, 896], "typeVersion": 1}, {"parameters": {"dataType": "string", "value1": "={{$json.github_status}}", "rules": {"rules": [{"value2": "same"}, {"value2": "different", "output": 1}, {"value2": "new", "output": 2}]}}, "id": "fbfc5739-c65d-46b5-8035-e458c2ddb0a6", "name": "Check Status", "type": "n8n-nodes-base.switch", "position": [2016, 896], "typeVersion": 1}, {"parameters": {}, "id": "00a5bc29-3e85-47fb-897a-1d9c16d3985f", "name": "Same file - Do nothing", "type": "n8n-nodes-base.noOp", "position": [2240, 736], "typeVersion": 1}, {"parameters": {}, "id": "f8fb264a-444d-4072-a846-0d65d6d6f857", "name": "File is different", "type": "n8n-nodes-base.noOp", "position": [2240, 896], "typeVersion": 1}, {"parameters": {}, "id": "05fc9484-2c0c-4dd3-8578-9396c691acc2", "name": "File is new", "type": "n8n-nodes-base.noOp", "position": [2240, 1088], "typeVersion": 1}, {"parameters": {"resource": "file", "owner": {"__rl": true, "mode": "", "value": "={{ $('Config').first().item.repo_owner }}"}, "repository": {"__rl": true, "mode": "", "value": "={{ $('Config').first().item.repo_name }}"}, "filePath": "={{ $('Config').first().item.repo_path }}{{ $json.subPath }}{{$('Execute Workflow Trigger').first().json.id}}.json", "fileContent": "={{$('isDiffOrNew').item.json[\"n8n_data_stringy\"]}}", "commitMessage": "={{$('Execute Workflow Trigger').first().json.name}} ({{$json.github_status}})"}, "id": "0ffb1a83-1b6d-4f6b-aa3c-61c60bcadcb2", "name": "Create new file", "type": "n8n-nodes-base.github", "position": [2464, 1088], "typeVersion": 1, "webhookId": "f9bbe8c1-6e76-43ff-95db-3cf935b9eebe", "credentials": {"githubApi": {"id": "XGiOJx0hggCfQmnI", "name": "GitHub account"}}}, {"parameters": {"resource": "file", "operation": "edit", "owner": {"__rl": true, "mode": "", "value": "={{ $('Config').first().item.repo_owner }}"}, "repository": {"__rl": true, "mode": "", "value": "={{ $('Config').first().item.repo_name }}"}, "filePath": "={{ $('Config').first().item.repo_path }}{{ $json.subPath }}{{$('Execute Workflow Trigger').first().json.id}}.json", "fileContent": "={{$('isDiffOrNew').item.json[\"n8n_data_stringy\"]}}", "commitMessage": "={{$('Execute Workflow Trigger').first().json.name}} ({{$json.github_status}})"}, "id": "cee1e51c-381a-47a6-94eb-75239954ad44", "name": "Edit existing file", "type": "n8n-nodes-base.github", "position": [2464, 896], "typeVersion": 1, "webhookId": "31947fc8-d2f7-43e8-9262-51e1f31a32be", "credentials": {"githubApi": {"id": "XGiOJx0hggCfQmnI", "name": "GitHub account"}}}, {"parameters": {"options": {}}, "id": "063b3616-013c-479d-848f-689c16e32171", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1856, 256], "typeVersion": 3}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 1, "triggerAtMinute": 33}]}}, "id": "68150b15-9132-49ad-87ab-5bb796a47985", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1248, 352], "typeVersion": 1.2}, {"parameters": {"assignments": {"assignments": [{"id": "dae43d3b-56e5-4098-b602-862ebf5cd073", "name": "subPath", "type": "string", "value": "=Teste"}]}, "includeOtherFields": true, "options": {}}, "id": "6f9c3dc9-cd70-4dcd-a056-97315d1160a3", "name": "Create sub path", "type": "n8n-nodes-base.set", "position": [1824, 896], "typeVersion": 3.3}, {"parameters": {"content": "## Backup to GitHub \nThis workflow will backup all instance workflows to GitHub every 24 hours.\n\nThe files are saved into folders using `YYYY/MM/` for the directory path and `ID.json` for the filename.\n\nThe Repo Owner, Repo Name and Main folder are set using the **Variables** feature but can be replaced with the `Config` node in the subworkflow. \n\nThe workflow runs calls itself to help reduce memory usage, Once the workflow has completed it will send an optional notification to Slack.\n\n### Time to Run\nTested with 1423 workflows on `1.44.1` it took under 30 minutes for the first run and under 12 minutes once the initial run is complete.", "height": 417, "width": 385, "color": 4}, "id": "d7dcdced-8c9b-496a-8f15-02e7d56d1e0e", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [768, 64], "typeVersion": 1}, {"parameters": {"content": "## Main workflow loop", "height": 416.1856906618075, "width": 1272.6408145680155, "color": 7}, "id": "60848e19-b341-456f-8d3d-98fa9925b5e1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1216, 64], "typeVersion": 1}, {"parameters": {"resource": "file", "operation": "get", "owner": {"__rl": true, "value": "={{ $json.repo_owner }}", "mode": ""}, "repository": {"__rl": true, "value": "={{ $json.repo_name }}", "mode": ""}, "filePath": "README.md", "asBinaryProperty": false, "additionalParameters": {}}, "id": "43d7d7fc-7596-4d87-bdee-2099dd83a94e", "name": "Get file data", "type": "n8n-nodes-base.github", "position": [1216, 624], "typeVersion": 1, "alwaysOutputData": true, "webhookId": "7729aa2a-a3f6-4e62-bac5-57db74fd227c", "credentials": {"githubApi": {"id": "XGiOJx0hggCfQmnI", "name": "GitHub account"}}, "continueOnFail": true}, {"parameters": {"assignments": {"assignments": [{"id": "8f6d1741-772f-462a-811f-4c334185e4f0", "name": "repo_owner", "type": "string", "value": "=Daniel-P-Lima"}, {"id": "8cac215c-4fd7-422f-9fd2-6b2d1e5e0383", "name": "repo_name", "type": "string", "value": "=n8nWorkflows"}, {"id": "eee305e9-4164-462a-86bd-80f0d58a31ae", "name": "repo_path", "type": "string", "value": "=https://github.com/Daniel-P-Lima/n8nWorkflows"}]}, "includeOtherFields": true, "options": {}}, "id": "662e52a7-7a17-4df4-a16b-6179e87bb59b", "name": "Config", "type": "n8n-nodes-base.set", "position": [1216, 912], "typeVersion": 3.4}, {"parameters": {"chatId": "-**********", "text": "Workflow: ", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2272, 160], "id": "66fe5ffe-5331-4155-a621-f45847de8aeb", "name": "Send a text message", "webhookId": "83d6bd8c-7bfd-4d27-9490-7d1cd1faac7a", "credentials": {"telegramApi": {"id": "1cP0oPNKKi3w6UE0", "name": "Telegram account"}}}, {"parameters": {"filters": {}, "requestOptions": {}}, "id": "********-f845-48b5-b057-99a698dcf873", "name": "Get many workflows", "type": "n8n-nodes-base.n8n", "position": [1648, 256], "typeVersion": 1, "credentials": {"n8nApi": {"id": "GFEkrAnG6TdoFGjm", "name": "n8n account"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [976, 912], "id": "2c3231bd-0310-42f0-8c79-fa7e755f137e", "name": "When Executed by Another Workflow"}, {"parameters": {"workflowId": {"__rl": true, "value": "ZfhZvS4ZOvP5Ppg2", "mode": "list", "cachedResultName": "Orquestrado<PERSON>"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [2112, 320], "id": "01c578e6-0ae5-4d54-829c-e57b2c144aa0", "name": "Execute Workflow"}], "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-07-24T14:17:51.000Z", "versionId": "6c6fc77c-3d39-45bd-9973-1aa9c2a9802b"}