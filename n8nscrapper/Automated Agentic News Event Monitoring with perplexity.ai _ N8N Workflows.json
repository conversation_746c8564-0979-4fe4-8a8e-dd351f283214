{"nodes": [{"parameters": {"rule": {"interval": [{}]}}, "id": "f78605f4-cddb-481c-82c2-8d6cf768530b", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [800, 304], "typeVersion": 1.2}, {"parameters": {"options": {}}, "id": "601bbd26-d1a0-4c1a-87a7-bb48b3d6ec6e", "name": "Gmail", "type": "n8n-nodes-base.gmail", "position": [1968, 304], "typeVersion": 2.1, "webhookId": "f2ddb892-18f9-4eb0-bffc-b2a7f37586bf"}, {"parameters": {"options": {}}, "id": "361c74e4-7c15-4884-babd-173b795eca0e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.markdown", "position": [1376, 304], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "d0af0361-6e41-4c9e-89bc-bfbad41f1a7e", "name": "Perplexity", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1072, 528], "typeVersion": 1}, {"parameters": {}, "id": "352defa0-f354-491c-a35d-dd2a94f38553", "name": "Title", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1600, 304], "typeVersion": 1.4}, {"parameters": {"options": {}}, "id": "c19ff26f-8cb6-41d9-b110-46214323eca1", "name": "News Reporter", "type": "@n8n/n8n-nodes-langchain.agent", "position": [992, 304], "retryOnFail": true, "typeVersion": 1.6}], "connections": {"Schedule Trigger": {"main": [[{"node": "News Reporter", "type": "main", "index": 0}]]}, "Markdown": {"main": [[{"node": "Title", "type": "main", "index": 0}]]}, "Perplexity": {"ai_languageModel": [[{"node": "Title", "type": "ai_languageModel", "index": 0}, {"node": "News Reporter", "type": "ai_languageModel", "index": 0}]]}, "Title": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}, "News Reporter": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "2f281fde0de64f39316b38bf7aeff647de66c777fd2c9178aac0dbc0cd948eca"}}