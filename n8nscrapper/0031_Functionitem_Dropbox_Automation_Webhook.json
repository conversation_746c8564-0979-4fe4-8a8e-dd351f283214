{"id": "105", "name": "screenshot", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 580], "parameters": {}, "typeVersion": 1}, {"name": "Create Web + Email Item", "type": "n8n-nodes-base.functionItem", "position": [630, 580], "parameters": {"functionCode": "item.website = \"https://uproc.io\";\nitem.email = \"<EMAIL>\";\n\nreturn item;"}, "typeVersion": 1}, {"name": "Send Email", "type": "n8n-nodes-base.awsSes", "position": [1660, 600], "parameters": {"body": "=Hi,\n<br><br>\nThese are your screenshots:<br>\n<table border=\"0\">\n<tr>\n<th>Simple screenshot</th><th>Fullpage screenshot</th>\n<tr>\n<td style=\"vertical-align: top; text-align: center\"><img src=\"{{$node[\"Generate Screenshot\"].json[\"message\"][\"result\"]}}\" width=\"320\"></td>\n<td style=\"vertical-align: top; text-align: center\"><img src=\"{{$node[\"Generate FullPage\"].json[\"message\"][\"result\"]}}\" width=\"320\"></td>\n</tr>\n</table>\n<br><br>\nThank you!", "subject": "Your screenshots!", "fromEmail": "<EMAIL>", "isBodyHtml": true, "toAddresses": ["={{$node[\"Create Web + Email Item\"].json[\"email\"]}}"], "additionalFields": {}}, "credentials": {"aws": "ses"}, "typeVersion": 1}, {"name": "Generate FullPage", "type": "n8n-nodes-base.uproc", "position": [850, 510], "parameters": {"url": "={{$node[\"Create Web + Email Item\"].json[\"website\"]}}", "tool": "getUrlScreenshot", "group": "image", "width": "640", "fullpage": "yes", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}, {"name": "Generate Screenshot", "type": "n8n-nodes-base.uproc", "position": [840, 680], "parameters": {"url": "={{$node[\"Create Web + Email Item\"].json[\"website\"]}}", "tool": "getUrlScreenshot", "group": "image", "width": "640", "fullpage": "no", "additionalOptions": {}}, "credentials": {"uprocApi": "miquel-uproc"}, "typeVersion": 1}, {"name": "Get File", "type": "n8n-nodes-base.httpRequest", "position": [1050, 510], "parameters": {"url": "={{$node[\"Generate FullPage\"].json[\"message\"][\"result\"]}}", "options": {}, "responseFormat": "file", "allowUnauthorizedCerts": true}, "typeVersion": 1}, {"name": "Get File1", "type": "n8n-nodes-base.httpRequest", "position": [1050, 680], "parameters": {"url": "={{$node[\"Generate Screenshot\"].json[\"message\"][\"result\"]}}", "options": {}, "responseFormat": "file", "allowUnauthorizedCerts": true}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1460, 600], "parameters": {"mode": "passThrough"}, "typeVersion": 1}, {"name": "Upload Screenshot", "type": "n8n-nodes-base.dropbox", "position": [1270, 680], "parameters": {"path": "/screenshots/sample.png", "binaryData": true}, "credentials": {"dropboxApi": "dropbox-miquel"}, "typeVersion": 1}, {"name": "Upload fullpage", "type": "n8n-nodes-base.dropbox", "position": [1270, 510], "parameters": {"path": "/screenshots/sample_fullpage.png", "binaryData": true}, "credentials": {"dropboxApi": "dropbox-miquel"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Merge": {"main": [[{"node": "Send Email", "type": "main", "index": 0}]]}, "Get File": {"main": [[{"node": "Upload fullpage", "type": "main", "index": 0}]]}, "Get File1": {"main": [[{"node": "Upload Screenshot", "type": "main", "index": 0}]]}, "Upload fullpage": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Generate FullPage": {"main": [[{"node": "Get File", "type": "main", "index": 0}]]}, "Upload Screenshot": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Generate Screenshot": {"main": [[{"node": "Get File1", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Create Web + Email Item", "type": "main", "index": 0}]]}, "Create Web + Email Item": {"main": [[{"node": "Generate FullPage", "type": "main", "index": 0}, {"node": "Generate Screenshot", "type": "main", "index": 0}]]}}}