{"name": "My workflow 2", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, 0], "id": "6990d9a0-5829-425a-9ccc-c30e45f641cd", "name": "Schedule Trigger"}, {"parameters": {"method": "POST", "url": "https://apibox.erweima.ai/api/v1/generate", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <>"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "A calm and relaxing piano track with soft melodies"}, {"name": "style", "value": "Lo-fi"}, {"name": "title", "value": "Peaceful Piano Meditation"}, {"name": "customMode", "value": true}, {"name": "instrumental", "value": true}, {"name": "model", "value": "V3_5"}, {"name": "negativeTags", "value": "Heavy Metal, Upbeat Drums"}, {"name": "callBackUrl", "value": "https://api.example.com/callback"}]}, "options": {"redirect": {"redirect": {}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "d4d192a8-d163-4993-aa86-1e1cc3c983bd", "name": "HTTP Request", "disabled": true}, {"parameters": {"url": "https://apibox.erweima.ai/api/v1/generate/record-info?taskId=ffb17f558ff5cc2b61f7fafc8fb7846a", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <>"}]}, "options": {"redirect": {"redirect": {}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "c03144ab-0f25-4617-abcd-88b03b60923b", "name": "HTTP Request1"}, {"parameters": {"amount": 2}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [660, 0], "id": "e4971c7a-bcbd-4445-a2f3-bbd4f8aa5084", "name": "Wait", "webhookId": "7cf0bf68-9f32-49e4-9c2b-fa4843ab53d4"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b6ed9357-0d35-431d-b069-4b203243d8bc", "leftValue": "={{ $json.data.status }}", "rightValue": "SUCCESS", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [960, 0], "id": "ceae5378-6691-4c0a-8590-c4932795b0aa", "name": "If"}, {"parameters": {"resource": "image", "prompt": "=Generate an image of {{ $json.data.response.sunoData[1].title }} in 16:9 layout to be used for my youtube long form video", "options": {"quality": "hd", "style": "natural"}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1320, -100], "id": "3bab028c-d24b-4e62-a2ce-62bcba1b7e4b", "name": "OpenAI", "credentials": {"openAiApi": {"id": "Q2DrwmpfAnCe9zKS", "name": "OpenAi account"}}}, {"parameters": {"operation": "write", "fileName": "/tmp/image.jpg", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1640, -100], "id": "16dcfe49-37ef-427d-a24e-8803184a7beb", "name": "Read/Write Files from Disk"}, {"parameters": {"url": "={{ $json.data.response.sunoData[0].audioUrl }}", "options": {"response": {"response": {"responseFormat": "file", "outputPropertyName": "musicfile"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 80], "id": "0d41cbf5-4b78-45fe-8f02-6fe0b7e2e51c", "name": "HTTP Request2"}, {"parameters": {"operation": "write", "fileName": "/tmp/musicfile.mp3", "dataPropertyName": "musicfile", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1660, 80], "id": "6f1bc406-9fd7-48e3-8e19-5618c267c629", "name": "Read/Write Files from Disk1"}, {"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1960, 40], "id": "9cb9d099-d6e5-44f7-93f9-b258e66251f1", "name": "Wait1", "webhookId": "fb84d2db-f792-4441-9ac0-b30c650a4c29"}, {"parameters": {"command": "ffmpeg -y -i /tmp/musicfile.mp3 -ss 0 -t 15 -c copy /tmp/trimmedaudio.mp3"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2180, 40], "id": "c13e2586-2c9d-4ebd-9750-00fbeeff198f", "name": "Execute Command"}, {"parameters": {"command": "ffmpeg -y -loop 1 -i /tmp/image.jpg -i /tmp/trimmedaudio.mp3 -c:v libx264 -tune stillimage -c:a aac -b:a 192k -shortest /tmp/outputvideo.mp4"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2400, 40], "id": "b3086b3e-1a8c-40c7-8e77-aa36f6cd93ab", "name": "Execute Command1"}, {"parameters": {"fileSelector": "/tmp/outputvideo.mp4", "options": {"fileExtension": ".mp4"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2620, 40], "id": "807f330c-cda7-491f-a65d-39c5c03a76a9", "name": "Read/Write Files from Disk2"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/upload/youtube/v3/videos?uploadType=multipart", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2840, 40], "id": "87e9d9f6-1893-46d8-9a00-c833cbb0db63", "name": "HTTP Request3", "credentials": {"youTubeOAuth2Api": {"id": "7mzDEdmvxUkX3gQy", "name": "YouTube account"}}}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}, {"node": "HTTP Request2", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Read/Write Files from Disk1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk1": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Read/Write Files from Disk": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Execute Command1", "type": "main", "index": 0}]]}, "Execute Command1": {"main": [[{"node": "Read/Write Files from Disk2", "type": "main", "index": 0}]]}, "Read/Write Files from Disk2": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e65fc2b8-5222-4a32-87b7-c36303f9de7c", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5e042ebff1f55bf5f6b5313c7ce55787764df5f56387a144249e5b979f6a19cc"}, "id": "jYqh4WLuB5fGS8O6", "tags": []}