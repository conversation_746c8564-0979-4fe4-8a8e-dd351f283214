{"name": "Reddit Content Ideation System", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [240, 380], "id": "569d026a-7d23-4066-a696-afa14aa352ee", "name": "Schedule Trigger1"}, {"parameters": {"operation": "search", "subreddit": "smallbusiness", "keyword": "Automation", "limit": 5, "additionalFields": {"sort": "new"}}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [460, 380], "id": "1752ecce-66d4-458e-93bf-ee81594876b3", "name": "r/smallbusiness/automation", "credentials": {"redditOAuth2Api": {"id": "Q1q1D9xLzqN2wr1b", "name": "<PERSON>'s Reddit account"}}}, {"parameters": {"operation": "search", "subreddit": "smallbusiness", "keyword": "AI Automation", "limit": 5, "additionalFields": {"sort": "new"}}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [460, 180], "id": "9a19a0e4-5e53-4921-8538-467ef5455343", "name": "r/smallbusiness/AI Automation", "credentials": {"redditOAuth2Api": {"id": "Q1q1D9xLzqN2wr1b", "name": "<PERSON>'s Reddit account"}}}, {"parameters": {"operation": "getAll", "subreddit": "n8n", "limit": 10, "filters": {"category": "hot"}}, "type": "n8n-nodes-base.reddit", "typeVersion": 1, "position": [460, 580], "id": "be138c1c-5180-4db5-9f33-c36459ddaa94", "name": "r/n8n", "credentials": {"redditOAuth2Api": {"id": "Q1q1D9xLzqN2wr1b", "name": "<PERSON>'s Reddit account"}}}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [660, 380], "id": "b44eca9b-8aa6-4df1-8b22-8abbc34d69e5", "name": "<PERSON><PERSON>"}, {"parameters": {"jsonSchemaExample": "{\n\t\"summary\": \"This is an example summary\",\n\t\"relevancy_score\": 0.75,\n    \"detail_score\": 0.90\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1540, 60], "id": "70443b5c-6ec4-415b-a212-bb9164b93e8b", "name": "Structured Output Parser"}, {"parameters": {"jsonSchemaExample": "{\n\t\"summary\": \"This is an example summary\",\n\t\"relevancy_score\": 0.75,\n    \"detail_score\": 0.90\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1540, 360], "id": "28e44909-473d-46ec-aa7a-32f67e5b449c", "name": "Structured Output Parser1"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1980, 200], "id": "c6dcadb7-b34a-412c-b138-b08604b74692", "name": "Merge1"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {"temperature": 0.1}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [920, 360], "id": "1ad33480-8598-4e7d-aeae-a0937ca7e75d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "uYPoJuWq6gGBiBp0", "name": "Noah's OpenAI"}}}, {"parameters": {"jsonSchemaExample": "{\n\t\"summary\": \"This is an example summary\",\n\t\"relevancy_score\": 0.85\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1540, 680], "id": "e5ac74c1-469e-4fa0-836b-92a38a25a7ef", "name": "Structured Output Parser2"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1360, 60], "id": "3b33b318-df33-4d08-828a-907aae39a382", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "uYPoJuWq6gGBiBp0", "name": "Noah's OpenAI"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1360, 360], "id": "d8be5afd-8fc3-4b2d-ab90-978b7f37ce7f", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "uYPoJuWq6gGBiBp0", "name": "Noah's OpenAI"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1360, 680], "id": "900f33e9-7769-4aee-8505-a4bddf9bddcf", "name": "OpenAI Chat Model3", "credentials": {"openAiApi": {"id": "uYPoJuWq6gGBiBp0", "name": "Noah's OpenAI"}}}, {"parameters": {"assignments": {"assignments": [{"id": "9855d661-58cd-4485-b2d8-3a25b9eb0ff6", "name": "Type", "value": "System", "type": "string"}, {"id": "f27af707-515e-41f9-8fba-cf9374270876", "name": "Summary", "value": "={{ $json.output.summary }}", "type": "string"}, {"id": "0e37256f-826b-4e5e-ac72-180a61cf4ce8", "name": "Relevancy Score", "value": "={{ $json.output.relevancy_score }}", "type": "string"}, {"id": "a23964d1-36c4-4fb6-ab1a-5027bbd61aaa", "name": "Detail Score", "value": "={{ $json.output.detail_score }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1700, 200], "id": "0c04077d-6e04-41b7-aeea-e378499de1ba", "name": "Edit Fields2"}, {"parameters": {"assignments": {"assignments": [{"id": "9855d661-58cd-4485-b2d8-3a25b9eb0ff6", "name": "Type", "value": "Question", "type": "string"}, {"id": "f27af707-515e-41f9-8fba-cf9374270876", "name": "Summary", "value": "={{ $json.output.summary }}", "type": "string"}, {"id": "0e37256f-826b-4e5e-ac72-180a61cf4ce8", "name": "Relevancy Score", "value": "={{ $json.output.relevancy_score }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1700, 520], "id": "76a229bd-902e-4c7b-bff6-10e740a1b4cd", "name": "Edit Fields3"}, {"parameters": {"assignments": {"assignments": [{"id": "9855d661-58cd-4485-b2d8-3a25b9eb0ff6", "name": "Type", "value": "Request", "type": "string"}, {"id": "f27af707-515e-41f9-8fba-cf9374270876", "name": "Summary", "value": "={{ $json.output.summary }}", "type": "string"}, {"id": "0e37256f-826b-4e5e-ac72-180a61cf4ce8", "name": "Relevancy Score", "value": "={{ $json.output.relevancy_score }}", "type": "string"}, {"id": "a23964d1-36c4-4fb6-ab1a-5027bbd61aaa", "name": "Detail Score", "value": "={{ $json.output.detail_score }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1700, -100], "id": "3ee7977e-ff6e-42b8-9e42-cd3b09c46f24", "name": "Edit Fields4"}, {"parameters": {"content": "# Fetch Reddit Posts & Find Content Ideas\n\n## How to Set Up\n1. Add your preferred keyword or Sub-reddit in each Reddit node\n2. Add your LLM api-key\n3. Optional: Re-write AI agent prompts to fit your needs\n4. Create an Airtable/Google Sheet to match the columns + add your connection", "height": 660, "width": 620, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [180, -100], "typeVersion": 1, "id": "ca4e3199-0d9d-43e6-ad22-240599175c94", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appg2URmi7xqyyXXR", "mode": "list", "cachedResultName": "Personal Base", "cachedResultUrl": "https://airtable.com/appg2URmi7xqyyXXR"}, "table": {"__rl": true, "value": "tblW2s3f4auvVuQyd", "mode": "list", "cachedResultName": "Content Ideation System", "cachedResultUrl": "https://airtable.com/appg2URmi7xqyyXXR/tblW2s3f4auvVuQyd"}, "columns": {"mappingMode": "defineBelow", "value": {"Post or Comment?": "Post", "Type": "={{ $('Merge1').item.json.Type }}", "Summary": "={{ $('Merge1').item.json.Summary }}", "Relevancy Score": "={{ $('Merge1').item.json['Relevancy Score'] }}", "Detail Score": "={{ $('Merge1').item.json['Detail Score'] }}", "OG Post": "={{ $('Merge').item.json.title }} - {{ $('Merge').item.json.selftext }}", "Source": "=r/{{ $('Merge').item.json.subreddit }}", "OG Link": "={{ $('Merge').item.json.url }}", "Link": "={{ $('Merge').item.json.url }}"}, "matchingColumns": [], "schema": [{"id": "Source", "displayName": "Source", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Post or Comment?", "displayName": "Post or Comment?", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Post", "value": "Post"}, {"name": "Comment", "value": "Comment"}], "readOnly": false, "removed": false}, {"id": "Type", "displayName": "Type", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Request", "value": "Request"}, {"name": "System", "value": "System"}, {"name": "Question", "value": "Question"}], "readOnly": false, "removed": false}, {"id": "OG Post", "displayName": "OG Post", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "OG Link", "displayName": "OG Link", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Summary", "displayName": "Summary", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Relevancy Score", "displayName": "Relevancy Score", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Detail Score", "displayName": "Detail Score", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Link", "displayName": "Link", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [2180, 200], "id": "5e5da393-a673-481f-b5d6-cbd857706a6c", "name": "Add to Airtable", "credentials": {"airtableTokenApi": {"id": "371XJRkvx0FN8DHo", "name": "statsnoah Airtable"}}}, {"parameters": {"inputText": "={{ $json.selftext }}", "categories": {"categories": [{"category": "Automation Request", "description": "Use this category if the post appears to possibly come from a business owner requesting help with a certain AI or automation process. For context, I am an AI and automation expert, specifically with low-code automated workflow, AI agents, and more."}, {"category": "Automation System", "description": "Use this category if the post appears to possibly come from somebody who has built an AI automation system that I could use as an idea for a system that I could create myself. For context, I am an AI and automation expert, specifically with low-code automated workflow, AI agents, and more."}, {"category": "Question", "description": "Use this category if the user appears to be asking a general AI and Automation question, not referencing a specific constructible system"}]}, "options": {"fallback": "discard", "systemPromptTemplate": "Please classify the text provided by the reddit user into one of the following categories: {categories}, and use the provided formatting instructions below. Don't explain, and only output the json."}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [940, 200], "id": "300dc7b2-cc5c-41c0-9739-2b5cb6370796", "name": "Request, System, or Question?"}, {"parameters": {"promptType": "define", "text": "={{ $json.selftext }}", "hasOutputParser": true, "options": {"systemMessage": "=## Role\nYou are an expert AI and Automation consultant.\n\n## Task\nYour task is to:\n\n1. Analyze the provided reddit post/comment from a user who is requesting an AI automation system to be created.\n2. Summarize the request into a concise, yet comprehensive paragraph detailing the exact system the user wants to be built.\n3. Assign a relevancy score to the system based off its relevancy to my business, skills, and offerings, on a scale from 0.05-0.95.\n4. Assign a detail score to the system based on the amount of detail the user provided. Anything over 0.9 should have enough information to recreate the system in N8N. Anything over 0.7 should have some details regarding the workflow.\n\nFor context, I run an AI Automation agency which builds automated workflows, AI agents, and more, primarily using N8N.\n\nOnly output the summary and the scores. Do not provide an explanation."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1360, -100], "id": "fa7a346e-f4ab-4f4f-8f45-9387f55b756a", "name": "Request"}, {"parameters": {"promptType": "define", "text": "={{ $json.selftext }}", "hasOutputParser": true, "options": {"systemMessage": "## Role\nYou are an expert AI and Automation consultant.\n\n## Task\nYour task is to:\n\n1. Analyze the provided reddit post/comment from a user who has displayed their AI automation system.\n2. Summarize the system described into a concise, yet comprehensive paragraph detailing the exact system the user wants to be built.\n3. Assign a relevancy score to the system based off its relevancy to my business, skills, and offerings, on a scale from 0.05-0.95.\n4. Assign a detail score to the system based on the amount of detail the user provided. Anything over 0.9 should have enough information to recreate the system in N8N. Anything over 0.7 should have some details regarding the workflow.\n\nFor context, I run an AI Automation agency which builds automated workflows, AI agents, and more, primarily using N8N.\n\nOnly output the summary and the scores. Do not provide an explanation."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1360, 200], "id": "fc2b03ac-8a1a-4a54-84b1-1c079efb2021", "name": "System"}, {"parameters": {"promptType": "define", "text": "={{ $json.selftext }}", "hasOutputParser": true, "options": {"systemMessage": "## Role\nYou are an expert AI and Automation consultant.\n\n## Task\nYour task is to:\n\n1. Analyze the provided reddit post/comment from a user who has displayed their AI automation system.\n2. Summarize the question to remove unnecessary filler while keeping all the important aspects of the question.\n3. Assign a score to the question based off its relevancy to my business, skills, and offerings, on a scale from 0.05-0.95.\n\nFor context, I run an AI Automation agency which builds automated workflows, AI agents, and more, primarily using N8N. These questions will be used as content inspiration\n\nOnly output the summary and the score. Do not provide an explanation."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1360, 520], "id": "04ec15f1-3f40-445b-94e7-5cbb3b23217b", "name": "Question"}], "pinData": {}, "connections": {"Schedule Trigger1": {"main": [[{"node": "r/smallbusiness/automation", "type": "main", "index": 0}, {"node": "r/smallbusiness/AI Automation", "type": "main", "index": 0}, {"node": "r/n8n", "type": "main", "index": 0}]]}, "r/smallbusiness/automation": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "r/smallbusiness/AI Automation": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "r/n8n": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Merge": {"main": [[{"node": "Request, System, or Question?", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Request", "type": "ai_outputParser", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "System", "type": "ai_outputParser", "index": 0}]]}, "Merge1": {"main": [[{"node": "Add to Airtable", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Request, System, or Question?", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "Question", "type": "ai_outputParser", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Request", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "System", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Question", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Edit Fields3": {"main": [[{"node": "Merge1", "type": "main", "index": 2}]]}, "Edit Fields4": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Request, System, or Question?": {"main": [[{"node": "Request", "type": "main", "index": 0}], [{"node": "System", "type": "main", "index": 0}], [{"node": "Question", "type": "main", "index": 0}]]}, "Request": {"main": [[{"node": "Edit Fields4", "type": "main", "index": 0}]]}, "System": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Question": {"main": [[{"node": "Edit Fields3", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "828d8f33-b042-4414-93f6-cca930b923b2", "meta": {"instanceId": "8e09f28fbdd3404ca1f111b2cece3db78dffeeb474cae326e59d6de2cdc48774"}, "id": "J1p7V1oViSIBNKvU", "tags": []}