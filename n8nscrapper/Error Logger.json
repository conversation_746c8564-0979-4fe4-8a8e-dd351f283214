{"name": "<PERSON><PERSON><PERSON>", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [0, 0], "id": "e1eb707f-206c-4e68-a4b0-e84a6bba0a99", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1hcq8VkYhy4TiiV0LRaaFAituRS6you6A8PyGc72i_dI", "mode": "list", "cachedResultName": "<PERSON><PERSON><PERSON>", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1hcq8VkYhy4TiiV0LRaaFAituRS6you6A8PyGc72i_dI/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1hcq8VkYhy4TiiV0LRaaFAituRS6you6A8PyGc72i_dI/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Timestamp": "={{ $now.format('yyyy-MM-dd hh:mma') }}", "URL": "={{ $json.execution.url }}", "Node": "={{ $json.execution.error.node.name }}", "Error Message": "={{ $json.execution.error.message }}", "Workflow": "={{ $json.workflow.name }}"}, "matchingColumns": [], "schema": [{"id": "Timestamp", "displayName": "Timestamp", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Workflow", "displayName": "Workflow", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Node", "displayName": "Node", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Error Message", "displayName": "Error Message", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [280, -140], "id": "b4230162-0717-453e-9978-f2c196948e1a", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "VftE0eOzfWVcNhgX", "name": "Google Sheets account"}}}, {"parameters": {"authentication": "oAuth2", "select": "channel", "channelId": {"__rl": true, "value": "C08UP5RRABG", "mode": "list", "cachedResultName": "thebuildroom"}, "text": "=There was an error in the workflow: {{ $json.workflow.name }} at {{ $now.format('yyyy-MM-dd hh:mma') }}\n\nThe error was in the node: {{ $json.execution.error.node.name }}\n\nWith the error message: {{ $json.execution.error.message }}\n\nGo to workflow: {{ $json.execution.url }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [400, 100], "id": "10a777d2-3112-4ab5-9de7-5f3fa83d10a0", "name": "<PERSON><PERSON>ck", "webhookId": "9f06323c-f851-4a54-935a-aea1b8f72808", "credentials": {"slackOAuth2Api": {"id": "5MnX8nmJOgNEE0fI", "name": "the build room connection"}}}], "pinData": {"Error Trigger": [{"json": {"execution": {"id": "7148", "url": "https://duncanrogoff.app.n8n.cloud/workflow/Gp01bEJMMHfzsH8t/executions/7148", "error": {"level": "warning", "tags": {}, "context": {}, "functionality": "regular", "name": "NodeOperationError", "timestamp": 1751849442941, "node": {"parameters": {"notice": "", "promptType": "define", "text": "=tell me a joke about: {{ $json.message.text }}", "hasOutputParser": false, "messages": {}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [220, 0], "id": "d31e8a0c-800c-4ffc-9dc2-a11180abc119", "name": "The Joker"}, "messages": [], "message": "A Model sub-node must be connected and enabled", "stack": "NodeOperationError: A Model sub-node must be connected and enabled\n    at ExecuteContext.getInputConnectionData (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.78.1_encoding@0.1.13_zod@3.24.1_/node_modules/n8n-core/src/execution-engine/node-execution-context/utils/get-input-connection-data.ts:115:10)\n    at ExecuteContext.getInputConnectionData (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.78.1_encoding@0.1.13_zod@3.24.1_/node_modules/n8n-core/src/execution-engine/node-execution-context/execute-context.ts:160:39)\n    at processItem (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+n8n-nodes-langchain@file+packages+@n8n+nodes-langchain_f35e7d377a7fe4d08dc2766706b5dbff/node_modules/@n8n/n8n-nodes-langchain/nodes/chains/ChainLLM/methods/processItem.ts:11:25)\n    at /usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+n8n-nodes-langchain@file+packages+@n8n+nodes-langchain_f35e7d377a7fe4d08dc2766706b5dbff/node_modules/@n8n/n8n-nodes-langchain/nodes/chains/ChainLLM/ChainLlm.node.ts:80:19\n    at Array.map (<anonymous>)\n    at ExecuteContext.execute (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/@n8n+n8n-nodes-langchain@file+packages+@n8n+nodes-langchain_f35e7d377a7fe4d08dc2766706b5dbff/node_modules/@n8n/n8n-nodes-langchain/nodes/chains/ChainLLM/ChainLlm.node.ts:79:33)\n    at WorkflowExecute.runNode (/usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.78.1_encoding@0.1.13_zod@3.24.1_/node_modules/n8n-core/src/execution-engine/workflow-execute.ts:1185:9)\n    at /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.78.1_encoding@0.1.13_zod@3.24.1_/node_modules/n8n-core/src/execution-engine/workflow-execute.ts:1534:27\n    at /usr/local/lib/node_modules/n8n/node_modules/.pnpm/n8n-core@file+packages+core_openai@4.78.1_encoding@0.1.13_zod@3.24.1_/node_modules/n8n-core/src/execution-engine/workflow-execute.ts:2098:11"}, "lastNodeExecuted": "The Joker", "mode": "webhook"}, "workflow": {"id": "Gp01bEJMMHfzsH8t", "name": "<PERSON>"}}}]}, "connections": {"Error Trigger": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "94c55727-51b5-4669-814f-61b73d06bc70", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ca3f3f1b54754cc6a8bf7018ef2c4b3d517f36ec4d32b4741e6f00bea7ac9b4a"}, "id": "lJVqmV1BhTOu32an", "tags": []}