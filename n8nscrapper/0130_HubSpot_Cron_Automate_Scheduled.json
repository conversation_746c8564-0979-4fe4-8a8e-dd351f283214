{"nodes": [{"name": "Hubspot", "type": "n8n-nodes-base.hubspot", "position": [750, 900], "parameters": {"resource": "contact", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"hubspotApi": {"id": "21", "name": "hubspot_nodeqa"}}, "typeVersion": 1}, {"name": "Pipedrive", "type": "n8n-nodes-base.pipedrive", "position": [750, 710], "parameters": {"resource": "person", "operation": "getAll", "returnAll": true, "additionalFields": {}}, "credentials": {"pipedriveApi": {"id": "15", "name": "as<PERSON>s"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [950, 800], "parameters": {"mode": "removeKeyMatches", "propertyName1": "email[0].value", "propertyName2": "identity-profiles[0].identities[0].value"}, "typeVersion": 1}, {"name": "HubSpot2", "type": "n8n-nodes-base.hubspot", "position": [1150, 800], "parameters": {"email": "={{$json[\"email\"][0][\"value\"]}}", "resource": "contact", "additionalFields": {"firstName": "={{$json[\"first_name\"]}}"}}, "credentials": {"hubspotApi": {"id": "21", "name": "hubspot_nodeqa"}}, "typeVersion": 1}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "position": [550, 800], "parameters": {"triggerTimes": {"item": [{"mode": "everyMinute"}]}}, "typeVersion": 1}], "connections": {"Cron": {"main": [[{"node": "Pipedrive", "type": "main", "index": 0}, {"node": "Hubspot", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "HubSpot2", "type": "main", "index": 0}]]}, "Hubspot": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Pipedrive": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}