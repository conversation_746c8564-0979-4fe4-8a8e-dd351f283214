{"name": "Calendly to Meeting Prep (No Creds)", "nodes": [{"parameters": {"content": "## Calendly Research Tool Workflow\n\nThis workflow automatically researches meeting attendees when they book via Calendly:\n\n1. **Webhook Trigger**: Receives Calendly booking data\n2. **Extract Details**: Pulls out attendee name, email, company, and additional details\n3. **Research Individual**: Uses HTTP Request to Perplexity AI for professional background\n4. **Research Company**: Uses HTTP Request to Perplexity AI for company information\n5. **Analyze Goals**: Uses OpenAI to analyze booking details and infer meeting goals\n6. **Create Briefing**: Compiles all research into a structured report\n7. **Generate Audio**: Converts report to speech using OpenAI TTS\n8. **Send Results**: Emails report with audio attachment to your email\n\n**Required Credentials**:\n- Perplexity AI API\n- OpenAI API\n- Gmail OAuth2 (for email)\n", "height": 500, "width": 400, "color": 7}, "id": "c8b17b85-41da-47cb-9309-1aee37ec36a3", "name": "Workflow Overview", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-820, -20]}, {"parameters": {"assignments": {"assignments": [{"id": "name-field", "name": "name", "value": "={{ $json.payload.name }}", "type": "string"}, {"id": "email-field", "name": "email", "value": "={{ $json.payload.email }}", "type": "string"}, {"id": "company-field", "name": "companyName", "value": "={{ $json.payload.questions_and_answers[0].answer }}", "type": "string"}, {"id": "details-field", "name": "additionalDetails", "value": "={{ $json.payload.questions_and_answers[1].answer }}", "type": "string"}]}, "options": {}}, "id": "573594de-2abc-40c5-a89b-957982c015a3", "name": "Extract Booking Details", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [900, 15]}, {"parameters": {"content": "## Configuration Notes\n\n**Credentials Required:**\n1. **Perplexity AI**: Add Bearer token in HTTP Header Auth\n2. **OpenAI**: Standard API key\n3. **Gmail**: OAuth2 connection\n\n**Webhook Setup:**\n- Configure Calendly to send webhook to the n8n webhook URL\n- Configure the event type in Calendly to have a \"Company Name\" field in the booking form. \n- Ensure webhook sends questions_and_answers array - including company name.\n\n**Customization:**\n- Adjust research prompts based on your industry\n- Modify email template in Format Email node\n- Change audio voice in Generate Audio node\n- Add error handling nodes as needed", "height": 420, "width": 250, "color": 5}, "id": "6ed162c5-3ad7-4cc7-a955-34db44dad466", "name": "Configuration Notes", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [3700, -540]}, {"parameters": {"scope": "organization", "events": ["invitee.created"]}, "type": "n8n-nodes-base.calendlyTrigger", "typeVersion": 1, "position": [680, 15], "id": "13e50c1d-8106-4a90-9724-16b13f623f1a", "name": "<PERSON><PERSON><PERSON>", "webhookId": "ad0e0cf7-c1de-42ef-8597-b737daf5de53", "credentials": {"calendlyApi": {"id": "hqn2ccKL5rIdpYdC", "name": "<PERSON>ly"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "=Meeting Prep - {{ $json.name }}", "message": "={{ $json.choices[0].message.content }}", "options": {"attachmentsUi": {"attachmentsBinary": [{"property": "={{ 'data' }}"}]}}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [3320, 340], "id": "4de1a5f1-b3d6-46d6-ac7e-d7ca36af5e1e", "name": "Gmail", "webhookId": "1cebcf99-25d5-464b-bd47-eb5b33142cac", "credentials": {"gmailOAuth2": {"id": "IO3v16mwrLQmGYMX", "name": "<PERSON>'s Gmail"}}}, {"parameters": {"modelId": {"__rl": true, "value": "o3-mini", "mode": "list", "cachedResultName": "O3-MINI"}, "messages": {"values": [{"content": "=You are a meeting assistant for an AI Agency that consults with clients to implement AI into their business systems.\n\nAnalyze the following calendar booking details to identify the requester's possible goals, pain points, and expectations for our upcoming meeting. Summarize their likely needs and suggest a few tailored questions or prep points I should consider before the call.\n\nAdditional details from booking: {{ $json.additionalDetails }}"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1160, -60], "id": "976946d7-8d57-4308-846a-fb2d5cf9ae7d", "name": "Meeting Goal Analysis", "credentials": {"openAiApi": {"id": "KSKdcXliKsG4jYZC", "name": "OpenAi account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "o3-mini", "mode": "list", "cachedResultName": "O3-MINI"}, "messages": {"values": [{"content": "=You are a meeting prep assistant helping me consolidate research into a single, easy-to-read report.\n\nPlease compile the following information into a structured briefing report that flows naturally when read aloud (for use with a text-to-speech tool later). The report should be concise (under 1500 characters)\n\nThe report should include:\n- A brief summary of the individual (title, background, accomplishments, communication style)\n- A summary of the company (industry, mission, recent news, market position)\n- Key insights or red flags from both\n- Inferred goals, needs, or expectations for the meeting (from calendar data)\n- 3-5 personalized questions I could ask to show preparedness and build rapport\n\n### Research on the individual\n{{ $json.individualResearch[0] }}\n\n### Research on the company\n{{ $json.companyResearch[0] }}\n\n### Booking detail analysis\n{{ $json.goalsAnalysis[0] }}"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2156, -60], "id": "7ffe709f-96ce-4813-aedf-9fc21963b8bc", "name": "Meeting Prep Report Generator", "credentials": {"openAiApi": {"id": "KSKdcXliKsG4jYZC", "name": "OpenAi account"}}}, {"parameters": {"modelId": {"__rl": true, "value": "o4-mini", "mode": "list", "cachedResultName": "O4-MINI"}, "messages": {"values": [{"content": "=Summarize the content here into concise bullet points to be sent in an email.\n\n* The output should use HTML for readable formatting\n* The introduction to the email can be simple.\n\nExample:\n\"Hello,\n\nHere is a summary of the meeting cheat sheet prepared for your upcoming meeting.\"\n\n* No signature is required.\n* Include ONLY the HTML body of the email. Do not include any leading commentary.\n\nContent to summarize:\n{{ $json.message.content }}"}]}, "simplify": false, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2660, -300], "id": "947c6c30-694d-43c4-8996-6dc0fcd3188e", "name": "Meeting Brief Em<PERSON>", "credentials": {"openAiApi": {"id": "KSKdcXliKsG4jYZC", "name": "OpenAi account"}}}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar-deep-research\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Summarize the professional background and relevant information about this individual:\\n\\nName: {{ $json.name }} \\nEmail: {{ $json.email }} \\nCompany Name: {{ $json.companyName }} \\n\\nFocus on job title, industry, notable press mentions, and any interesting professional highlights.\"\n    }\n  ],\n  \"web_search_options\": {\n    \"search_context_size\": \"medium\"\n  }\n}", "options": {}}, "id": "712cafcb-7194-41f1-bc3f-05a86b7a09fd", "name": "Perplexity: Research Attendee", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1198, -260], "credentials": {"httpHeaderAuth": {"id": "TpEEa9nGA0DSehtv", "name": "<PERSON>'s Perplexity"}}, "notes": "Uses Perplexity AI to research the individual's professional background"}, {"parameters": {"method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"sonar-deep-research\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"Provide a comprehensive analysis of:\\n\\nCompany: {{ $json.companyName }} \\n\\nInclude its mission, key products or services, leadership team, company history, recent news or developments, and funding history (if applicable). Highlight what makes the company notable and any strategic initiatives or challenges it may be facing.\"\n    }\n  ],\n  \"web_search_options\": {\n    \"search_context_size\": \"medium\"\n  }\n}", "options": {}}, "id": "00e58c5e-14cc-43eb-ac97-13f5dcda0ee2", "name": "Perplexity: Research Company Info", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1198, 240], "credentials": {"httpHeaderAuth": {"id": "TpEEa9nGA0DSehtv", "name": "<PERSON>'s Perplexity"}}}, {"parameters": {"assignments": {"assignments": [{"id": "3ff2c7fe-81dd-4fbf-9527-5966e0ca854e", "name": "individualResearch", "value": "={{ $json.choices[0].message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1496, -260], "id": "c89cc39f-f584-4405-95d4-a081820a1a3f", "name": "Extract Attendee Research"}, {"parameters": {"assignments": {"assignments": [{"id": "54cb21c8-f80e-41f3-a45e-7c3d7ae283dd", "name": "goalsAnalysis", "value": "={{ $json.message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1496, -60], "id": "162d70a4-862c-4824-a9ab-95e88084259a", "name": "Extract Meeting Goals"}, {"parameters": {"assignments": {"assignments": [{"id": "6402853b-4101-4e05-96c3-87c0d9a5682b", "name": "companyResearch", "value": "={{ $json.choices[0].message.content }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1496, 240], "id": "29f31410-870c-44d0-b424-f1580c69dcc1", "name": "Extract Company Research"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1716, -60], "id": "56a41ac1-7d03-4a4e-8259-733d703fa7db", "name": "Combine Research Results"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "individualResearch"}, {"fieldToAggregate": "goalsAnalysis"}, {"fieldToAggregate": "companyResearch"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1936, -60], "id": "47ae4ad0-0202-46ed-92c2-b57ba374592d", "name": "Consolidate Research Data"}, {"parameters": {"resource": "audio", "input": "={{ $json.message.content }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2532, -60], "id": "99765ed6-227e-446b-9057-a9b33bacbb68", "name": "Generate Audio Briefing", "credentials": {"openAiApi": {"id": "KSKdcXliKsG4jYZC", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "********-b14b-44ca-a807-ba2bf9b688df", "name": "data", "value": "={{ $binary }}", "type": "string"}, {"id": "a597e109-1ded-4aa1-a172-fa86cfaedbf5", "name": "Mime Type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "e85b5ee1-b58d-4850-aeb9-3fdfdd91adad", "name": "File Name", "value": "={{ $json.fileName }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2830, -60], "id": "6a13ba5d-093d-4707-96e6-2b03ad8e755f", "name": "Prepare Audio Attachment"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [3100, 340], "id": "c13f8781-dd69-4cf2-898f-44e9e8cc617a", "name": "Assemble Email Package"}], "pinData": {"Calendly Trigger": [{"json": {"created_at": "2025-06-22T14:26:54.000000Z", "created_by": "https://api.calendly.com/users/851c0b6b-8b8b-4853-9567-86449e6e2c9c", "event": "invitee.created", "payload": {"cancel_url": "https://calendly.com/cancellations/8fc8f57b-bae5-4f8f-9871-05707d4138d5", "created_at": "2025-06-22T14:26:53.409214Z", "email": "<EMAIL>", "event": "https://api.calendly.com/scheduled_events/8be1d909-220c-4d69-8276-6822d93343b7", "first_name": null, "invitee_scheduled_by": null, "last_name": null, "name": "<PERSON><PERSON>", "new_invitee": null, "no_show": null, "old_invitee": null, "payment": null, "questions_and_answers": [{"answer": "Prompt Advisers", "position": 0, "question": "Company Name"}, {"answer": "needing a chatbot", "position": 1, "question": "Please share anything that will help prepare for our meeting."}], "reconfirmation": null, "reschedule_url": "https://calendly.com/reschedulings/8fc8f57b-bae5-4f8f-9871-05707d4138d5", "rescheduled": false, "routing_form_submission": null, "scheduled_event": {"created_at": "2025-06-22T14:26:53.393101Z", "end_time": "2025-06-30T18:15:00.000000Z", "event_guests": [], "event_memberships": [{"user": "https://api.calendly.com/users/851c0b6b-8b8b-4853-9567-86449e6e2c9c", "user_email": "<EMAIL>", "user_name": "<PERSON>"}], "event_type": "https://api.calendly.com/event_types/f50a3105-e144-4ba1-a1fa-f8212a0ad8bd", "invitees_counter": {"total": 1, "active": 1, "limit": 1}, "location": {"join_url": "https://calendly.com/events/8be1d909-220c-4d69-8276-6822d93343b7/google_meet", "status": "processing", "type": "google_conference"}, "meeting_notes_html": null, "meeting_notes_plain": null, "name": "15-min Touchbase", "start_time": "2025-06-30T18:00:00.000000Z", "status": "active", "updated_at": "2025-06-22T14:26:53.393101Z", "uri": "https://api.calendly.com/scheduled_events/8be1d909-220c-4d69-8276-6822d93343b7"}, "scheduling_method": null, "status": "active", "text_reminder_number": null, "timezone": "America/New_York", "tracking": {"utm_campaign": null, "utm_source": null, "utm_medium": null, "utm_content": null, "utm_term": null, "salesforce_uuid": null}, "updated_at": "2025-06-22T14:26:53.409214Z", "uri": "https://api.calendly.com/scheduled_events/8be1d909-220c-4d69-8276-6822d93343b7/invitees/8fc8f57b-bae5-4f8f-9871-05707d4138d5"}}}]}, "connections": {"Extract Booking Details": {"main": [[{"node": "Perplexity: Research Attendee", "type": "main", "index": 0}, {"node": "Meeting Goal Analysis", "type": "main", "index": 0}, {"node": "Perplexity: Research Company Info", "type": "main", "index": 0}, {"node": "Assemble Email Package", "type": "main", "index": 0}]]}, "Calendly Trigger": {"main": [[{"node": "Extract Booking Details", "type": "main", "index": 0}]]}, "Meeting Goal Analysis": {"main": [[{"node": "Extract Meeting Goals", "type": "main", "index": 0}]]}, "Meeting Prep Report Generator": {"main": [[{"node": "Generate Audio Briefing", "type": "main", "index": 0}, {"node": "Meeting Brief Em<PERSON>", "type": "main", "index": 0}]]}, "Meeting Brief Email Formatter": {"main": [[{"node": "Assemble Email Package", "type": "main", "index": 1}]]}, "Perplexity: Research Attendee": {"main": [[{"node": "Extract Attendee Research", "type": "main", "index": 0}]]}, "Perplexity: Research Company Info": {"main": [[{"node": "Extract Company Research", "type": "main", "index": 0}]]}, "Extract Attendee Research": {"main": [[{"node": "Combine Research Results", "type": "main", "index": 0}]]}, "Extract Meeting Goals": {"main": [[{"node": "Combine Research Results", "type": "main", "index": 1}]]}, "Extract Company Research": {"main": [[{"node": "Combine Research Results", "type": "main", "index": 2}]]}, "Combine Research Results": {"main": [[{"node": "Consolidate Research Data", "type": "main", "index": 0}]]}, "Consolidate Research Data": {"main": [[{"node": "Meeting Prep Report Generator", "type": "main", "index": 0}]]}, "Generate Audio Briefing": {"main": [[{"node": "Prepare Audio Attachment", "type": "main", "index": 0}]]}, "Prepare Audio Attachment": {"main": [[{"node": "Assemble Email Package", "type": "main", "index": 2}]]}, "Assemble Email Package": {"main": [[{"node": "Gmail", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "81bd9c56-4e05-4262-a997-23d0c6b28528", "meta": {"templateCredsSetupCompleted": true, "instanceId": "aaadb797535f05587ee95b776c942a7c3f7a46fd7aa0c9b6a9d64e1e595f8af1"}, "id": "2UJmgdsJoGDWo2Cm", "tags": []}