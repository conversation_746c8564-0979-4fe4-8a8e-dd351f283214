{"id": "159", "name": "Create, update and get a case in TheHive", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [270, 340], "parameters": {}, "typeVersion": 1}, {"name": "TheHive", "type": "n8n-nodes-base.theHive", "position": [470, 340], "parameters": {"tags": "n8n, theHive", "owner": "<PERSON><PERSON><PERSON>", "title": "n8n", "options": {}, "resource": "case", "severity": 1, "operation": "create", "startDate": "2020-12-03T10:08:14.000Z", "description": "Creating a case from n8n"}, "credentials": {"theHiveApi": "hive"}, "typeVersion": 1}, {"name": "TheHive1", "type": "n8n-nodes-base.theHive", "position": [670, 340], "parameters": {"id": "={{$node[\"TheHive\"].json[\"id\"]}}", "resource": "case", "operation": "update", "updateFields": {"severity": 3}}, "credentials": {"theHiveApi": "hive"}, "typeVersion": 1}, {"name": "TheHive2", "type": "n8n-nodes-base.theHive", "position": [870, 340], "parameters": {"id": "={{$node[\"TheHive\"].json[\"id\"]}}", "resource": "case", "operation": "get"}, "credentials": {"theHiveApi": "hive"}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"TheHive": {"main": [[{"node": "TheHive1", "type": "main", "index": 0}]]}, "TheHive1": {"main": [[{"node": "TheHive2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "TheHive", "type": "main", "index": 0}]]}}}