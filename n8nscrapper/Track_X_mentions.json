{"name": "Track X mentions", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-300, -20], "id": "7c0cf085-20ff-4bf1-aecc-5c89bc7e6276", "name": "Schedule Trigger"}, {"parameters": {"assignments": {"assignments": [{"id": "f58ceda5-0d6c-4142-9228-be542d2fcd6b", "name": "keyword", "value": "apify", "type": "string"}, {"id": "6844c689-b8e9-434c-8eaf-959ade45e5d4", "name": "ignore users", "value": "api<PERSON>,architjn", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, -20], "id": "eba3fd92-8c91-413e-9fff-8429bef5cf4e", "name": "Ignored Users"}, {"parameters": {"jsCode": "const today = new Date();\nconst currentDate = today.toISOString().split('T')[0];\n\nconst prevDate = new Date(today);\nprevDate.setDate(today.getDate() - 1);\nconst previousDate = prevDate.toISOString().split('T')[0];\n\nconst usernames = $input.first().json['ignore users'] || '';\nconst excludeAccounts = usernames\n  .split(',')\n  .filter(u => u.trim())\n  .map(u => `-from:${u.trim()}`)\n  .join(' ');\n\nreturn {\n  currentDate,\n  previousDate,\n  excludeAccounts\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [140, -20], "id": "75e8ed39-6abb-4aab-a9fb-109a0618f602", "name": "Get Date, Excluded Users"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/danek~twitter-scraper-ppr/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "maxItems", "value": "10"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"max_posts\": 10,\n    \"query\": \"\\\"{{ $('Ignored Users').item.json.keyword }}\\\" -filter:retweets -from:apify since:{{ $json.previousDate }} until:{{ $json.currentDate }} -filter:retweets -filter:replies lang:en\",\n    \"search_type\": \"Latest\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [360, -20], "id": "e0deb6be-69b7-41b4-b507-4bbd48495ff7", "name": "Get Mentions", "credentials": {"httpQueryAuth": {"id": "iNQkR7FRp8EukQCZ", "name": "Apify"}}}, {"parameters": {"assignments": {"assignments": [{"id": "6c119f88-10aa-47f6-ac41-bff5e4f619bd", "name": "tweetId", "value": "={{ $json.tweet_id }}", "type": "string"}, {"id": "4536db27-538b-4712-9102-36658b0508ce", "name": "twitterPost", "value": "={{ $json.text }}", "type": "string"}, {"id": "87c40022-3f32-4863-9a53-9f2e9b48183c", "name": "username", "value": "={{ $json.screen_name }}", "type": "string"}, {"id": "2404c736-ef36-43cb-aec4-e16077b84161", "name": "date", "value": "={{ $json.created_at }}", "type": "string"}, {"id": "fdcf7087-cd3a-4eae-8da7-6a019b88f266", "name": "tweetURL", "value": "=https://x.com/{{ $json.screen_name }}/status/{{ $json.tweet_id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [580, -20], "id": "0ba048cc-1903-40e4-bc6e-d47106f7f547", "name": "Clean Data"}, {"parameters": {"content": "## Set Product Name & Ignored Users\n- Set the name of the product to track the mentions\n- Add the list of users to ignore for alert", "height": 460, "width": 420}, "type": "n8n-nodes-base.stickyNote", "position": [-360, -260], "typeVersion": 1, "id": "68ef326b-6292-443b-b8ae-b625efafea52", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Get Posts\n- Get's last days posts with mentions\n- Ignores the users we don't want\n\n## Setup\n- Register on Apify - [here](https://www.architjn.com/r/apify)\n- Apify - [Tutorial](https://x.com/search?q=from%3Aarchitjn%20%22n8n%20Basics%22%20%22apify%22&src=typed_query&f=top)", "height": 460, "width": 440, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [80, -260], "typeVersion": 1, "id": "2058cfbf-2cbd-446b-8a5c-84a92738413c", "name": "Sticky Note1"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1yIHVmxzgNiaDl9cy6SSkPkC6ReAhTvBjAB0pMQrTpCQ", "mode": "list", "cachedResultName": "Product Mentioned", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yIHVmxzgNiaDl9cy6SSkPkC6ReAhTvBjAB0pMQrTpCQ/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1yIHVmxzgNiaDl9cy6SSkPkC6ReAhTvBjAB0pMQrTpCQ/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {" 𝕏 Id": "={{ $json.tweetId }}", "Post Text": "={{ $json.twitterPost }}", "Username": "={{ $json.username }}", "Date": "={{ $json.date }}", " 𝕏 URL": "={{ $json.tweetURL }}"}, "matchingColumns": [], "schema": [{"id": " 𝕏 Id", "displayName": " 𝕏 Id", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Post Text", "displayName": "Post Text", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Username", "displayName": "Username", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Date", "displayName": "Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": " 𝕏 URL", "displayName": " 𝕏 URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [800, -20], "id": "3b495cab-a145-4df0-b4e8-ff4ea781f344", "name": "Save Posts", "credentials": {"googleSheetsOAuth2Api": {"id": "Rn3BYzXFYtmdsek1", "name": "Google Sheets account"}}}, {"parameters": {"content": "## Save Posts\n- Save Mentioned Posts\n\n## Setup\n- Google Sheets - [Tutorial](https://x.com/search?q=from%3Aarchitjn%20%22n8n%20Basics%22%20%22Google%20Sheets%22&src=typed_query&f=top)", "height": 460, "width": 420, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [540, -260], "typeVersion": 1, "id": "ce9f5dbd-5e61-458e-add2-e7c4fbcc0b61", "name": "Sticky Note2"}, {"parameters": {"content": "# Track Product Mentions on 𝕏", "height": 80, "width": 1320, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-360, -360], "typeVersion": 1, "id": "86640aac-2450-4ba5-9751-844260f9aaad", "name": "Sticky Note3"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Ignored Users", "type": "main", "index": 0}]]}, "Ignored Users": {"main": [[{"node": "Get Date, Excluded Users", "type": "main", "index": 0}]]}, "Get Date, Excluded Users": {"main": [[{"node": "Get Mentions", "type": "main", "index": 0}]]}, "Get Mentions": {"main": [[{"node": "Clean Data", "type": "main", "index": 0}]]}, "Clean Data": {"main": [[{"node": "Save Posts", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f4bfac06-bef6-411b-9e3e-5ecb906e9d40", "meta": {"templateCredsSetupCompleted": true, "instanceId": "933df83c84196d70ce3ffd0bf5d3e1aca31b7366f2f84e1f3482640096e4a3a9"}, "id": "8kgvqLEQPnA9Xpfx", "tags": []}