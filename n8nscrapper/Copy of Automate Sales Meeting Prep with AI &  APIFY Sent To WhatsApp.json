{"name": "Automate Sales Meeting Prep with AI &  APIFY Sent To WhatsApp", "nodes": [{"parameters": {"operation": "get", "messageId": "={{ $json.id }}", "simple": false, "options": {}}, "id": "201ef455-2d65-4563-8ec1-318211b1fa6a", "name": "Get Message Contents", "type": "n8n-nodes-base.gmail", "position": [2780, 1400], "webhookId": "fa1d496f-17fa-4e50-bae9-84ca85ed4502", "typeVersion": 2.1}, {"parameters": {"assignments": {"assignments": [{"id": "2006c806-42db-4457-84c2-35f59ed39018", "name": "date", "type": "string", "value": "={{ $json.date }}"}, {"id": "872278d2-b97c-45ba-a9d3-162f154fe7dc", "name": "subject", "type": "string", "value": "={{ $json.subject }}"}, {"id": "282f03e9-1d0f-4a17-b9ed-75b44171d4ee", "name": "text", "type": "string", "value": "={{ $json.text }}"}, {"id": "9421776c-ff53-4490-b0e1-1e610534ba25", "name": "from", "type": "string", "value": "={{ $json.from.value[0].name }} ({{ $json.from.value[0].address }})"}, {"id": "3b6716e8-5582-4da3-ae9d-e8dd1afad530", "name": "to", "type": "string", "value": "={{ $json.to.value[0].name }} ({{ $json.to.value[0].address }})"}]}, "options": {}}, "id": "ded010af-e977-4c47-87dd-8221d601af74", "name": "Simplify Emails", "type": "n8n-nodes-base.set", "position": [2940, 1400], "typeVersion": 3.4}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "n8n-events"}, "limit": 1, "options": {"timeMin": "={{ $now.toUTC() }}", "timeMax": "={{ $now.toUTC().plus(1, 'hour') }}", "singleEvents": true, "orderBy": "startTime"}}, "id": "816bf787-ff9c-4b97-80ac-4b0c6ae5638b", "name": "Check For Upcoming Meetings", "type": "n8n-nodes-base.googleCalendar", "position": [1240, 720], "typeVersion": 1.2}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "234d5c79-bf40-44bb-8829-c6ccf8648359", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1620, 880], "typeVersion": 1}, {"parameters": {"text": "=start: {{ $json.start.dateTime }}\nmeeting url: {{ $json.hangoutLink }}\nsummary: {{ $json.summary }}\ndescription: {{ $json.description }}\norganiser: {{ $json.organizer.displayName }} ({{ $json.organizer.email }})\nattendees: {{ $json.attendees.filter(item => !item.organizer).map(item => item.email).join(',') }}", "schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"attendees\": {\n          \"type\": \"array\",\n          \"description\": \"list of attendees excluding the meeting organiser\",\n          \"items\": {\n\t\t\t\"type\": \"object\",\n\t\t\t\"properties\": {\n\t\t\t  \"name\": { \"type\": \"string\" },\n              \"email\": { \"type\": \"string\" },\n              \"linkedin_url\": { \"type\": \"string\" }\n\t\t\t}\n          }\n\t\t}\n\t}\n}", "options": {"systemPromptTemplate": "You are an expert extraction algorithm. Try to link any information found in the description to help fill in the attendee details.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "id": "445aa0f4-d41a-4d46-aa2f-e79a9cdb04b5", "name": "Extract Attendee Information", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1620, 720], "typeVersion": 1}, {"parameters": {}, "id": "390743d8-acfd-4951-8901-212f162dcbb4", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [1620, 1480], "typeVersion": 1}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "ea9c76a0-40a0-413a-a93a-ad99069d0d91", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3160, 1540], "typeVersion": 1}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {"sender": "={{ $json.email }}"}}, "id": "8d9df9e4-1815-44a2-a6fc-a9af42a77153", "name": "Get Last Correspondence", "type": "n8n-nodes-base.gmail", "position": [2440, 1400], "webhookId": "b00c960c-3689-4fa1-9f0f-7d6c9479f0c6", "typeVersion": 2.1, "alwaysOutputData": true}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "23c7161f-60e2-4a99-9279-ff1dca5efc1c", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [4720, 2220], "typeVersion": 1}, {"parameters": {"model": "gpt-4o-2024-08-06", "options": {}}, "id": "9ab535aa-bd8c-4bd6-a7a0-f7182d8d7123", "name": "OpenAI Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [3420, 880], "typeVersion": 1}, {"parameters": {"operation": "send", "phoneNumberId": "477115632141067", "recipientPhoneNumber": "***********", "textBody": "={{ $json.text }}", "additionalFields": {}}, "id": "410acb11-a16c-4abd-9f10-7582168d100e", "name": "WhatsApp Business Cloud", "type": "n8n-nodes-base.whatsApp", "position": [4060, 760], "typeVersion": 1, "webhookId": "28769bb9-d0df-4eb2-8c98-b3e5d3650cf7"}, {"parameters": {"content": "## 1. Periodically Search For Upcoming Meetings\n[Read about the Scheduled Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger)\n\nLet's use the Scheduled Trigger node to trigger our Assistant to notify about upcoming meetings. Here, we'll set it for 1 hour intervals to check for meetings scheduled in our Google Calendar. You may need to play with the intervals and frequency depending on how many meetings you typically have.", "height": 449.1424626006906, "width": 616.7897454470152, "color": 7}, "id": "a7e8195d-eb73-4acb-aae1-eb04f8290d24", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [880, 500], "typeVersion": 1}, {"parameters": {"rule": {"interval": [{"field": "hours"}]}}, "id": "1aebb209-e440-4ef2-8527-381e5e70b4ea", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1040, 720], "typeVersion": 1.2}, {"parameters": {"content": "## 2. Extract Attendee Details From Invite\n[Learn more about the Information Extractor node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor/)\n\nOnce we have our upcoming meeting, it'll be nice to prepare for it by reminding the user what the meeting is about and some context with the attendees. This will be the goal this template and of our assistant! However, first we'll need to extract some contact information of the attendees to do so.\n\nFor this demonstration, we'll assume that attendee's email and LinkedIn profile URLs are included in the meeting invite. We'll extract this information for each attendee using the Information Extractor node. This convenient node uses AI to parse and extract which saves us from writing complex pattern matching code otherwise.\n\nIn your own scenario, feel free to use your CRM to get this information instead.", "height": 670.4114154200236, "width": 655.5654775604146, "color": 7}, "id": "95758053-fcc2-45c6-96c2-ec0bf89bcb82", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1520, 380], "typeVersion": 1}, {"parameters": {"content": "## 3. Fetch Recent Correspondance & LinkedIn Activity\n[Learn more about the Execute Workflow node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow)\n\nAs both email fetching and LinkedIn scraping actions are quite complex, we'll split them out as subworkflow executions. Doing so (in my honest opinion), helps with development and maintainability of the template. Here, we'll make perform the research for all applicable attendees by making 2 calls to the subworkflow and merging them back into a single node at the end.\n\nHead over to the subworkflow (see below - step 3a) to see how we pull the summaries from Gmail and LinkedIn.", "height": 670.8210817031078, "width": 1020.0959898041108, "color": 7}, "id": "bd17aed0-9c96-4301-b09b-e61a03ebc1ac", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [2200, 380], "typeVersion": 1}, {"parameters": {"content": "## 4. Generate Pre-Meeting Notification\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nNow that we have (1) our upcoming meeting details and (2) recent email and/or Linkedin summaries about our attendee, let's feed them into our LLM node to generate the best pre-meeting notification ever seen! Of course, we'll need to keep it short as we intend to send this notification via WhatsApp message but should you choose to use another channel such as email, feel free to adjust the length of the message which suits.", "height": 582.1201380897592, "width": 610.3630186140072, "color": 7}, "id": "ae804039-32e0-4d2d-a2ef-a6e8d65f7ce2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [3260, 460], "typeVersion": 1}, {"parameters": {"content": "## 5. Send Notification via WhatsApp\n[Learn more about the WhatsApp node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.whatsapp)\n\nThe WhatsApp node is a super convenient way to send messages to WhatsApp which is one of the many messaging apps supported by n8n out of the box. Not using WhatsApp? Simply swap this our for Twilio, Telegram, Slack and others.", "height": 454.24485615650235, "width": 466.8967433831988, "color": 7}, "id": "045eb1d9-fd80-4f9c-8218-ae66583d0186", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [3880, 540], "typeVersion": 1}, {"parameters": {"content": "## 3.2: Fetch Last Email Correspondance\n[Learn more about Gmail node](https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail)\n\nFetching our attendee's last email will definitely help the user \"pick up\" from when they last last off. To do this, we'll assume a Gmail user and use the Gmail node to filter messages by the attendee's email address.", "height": 190.5042226526524, "width": 519.1145893777881, "color": 7}, "id": "46d35c68-88d7-445f-9834-b8b37ce90619", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [2440, 1160], "typeVersion": 1}, {"parameters": {"content": "## 3.4 Scraping LinkedIn With [Apify.com](https://www.apify.com?fpr=414q6)\n[Learn more about Apify.com for Web Scraping](https://www.apify.com?fpr=414q6)\n\nTo get the attendee's recent LinkedIn activity, we'll need a webscraper capable of rendering the user's LinkedIn profile. We'll use [Apify.com](https://www.apify.com?fpr=414q6) which is a commercial web scraping service but has a very generous monthly free tier ($5/mo).\n\nWhile Apify offers a number of dedicated LinkedIn scrapers, we'll build our own which works by impersonating our own LinkedIn account using our login cookie - this can be obtained by inspecting network requests when logged into Linkedin. **Add your LinkedIn Cookie to the node below!**", "height": 259.*************, "width": 667.*************, "color": 7}, "id": "fe1c751c-4879-482b-bb6f-89df23e1faa8", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [2440, 1760], "typeVersion": 1}, {"parameters": {"content": "## 3.1 Attendee Researcher SubWorkflow\n[Learn more about using Execute Workflow Trigger](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflowtrigger/)\n\nThe Attendee Researcher SubWorkflow's aims to collect and summarize both an attendee's last correspondance  with the user (if applicable) and the attendee's LinkedIn profile (if available). It uses the router pattern to handle both branches allowing for shorter execution chains. Using the Switch node, this subworkflow is either triggered to fetch emails or scrape LinkedIn but never both simultaneously.", "height": 231.49547088049098, "width": 572.0305871208889, "color": 7}, "id": "a648cf7d-b859-4fec-8ae7-6450c70e6333", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [1620, 1220], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "fc4b63a7-ad4d-49ff-9d42-715760910f6a", "name": "linkedin_summary", "type": "string", "value": "={{ $json.text }}"}]}, "options": {}}, "id": "8a8dbe4f-86b1-41a4-9b7e-3affdee8e524", "name": "Return LinkedIn Success", "type": "n8n-nodes-base.set", "position": [5060, 2080], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "bf5a0781-3bad-4f63-a49c-273b03204747", "name": "linkedin_summary", "type": "string", "value": "No activities found."}]}, "options": {}}, "id": "537a399b-1f78-440b-abc4-ad2e91c5950a", "name": "Return LinkedIn Error", "type": "n8n-nodes-base.set", "position": [3080, 2220], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "9a7efc9e-26b0-48c9-83aa-ae989f20b1df", "name": "email_summary", "type": "string", "value": "No correspondance found."}]}, "options": {}}, "id": "a68e7df7-8467-46e2-8ea8-fcf270755d12", "name": "Return Email <PERSON>r", "type": "n8n-nodes-base.set", "position": [2780, 1580], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "fc4b63a7-ad4d-49ff-9d42-715760910f6a", "name": "email_summary", "type": "object", "value": "={{ $json.text }}"}]}, "options": {}}, "id": "00df2b18-22ca-48d6-b053-12fe502effc5", "name": "Return Email Success", "type": "n8n-nodes-base.set", "position": [3500, 1400], "typeVersion": 3.4}, {"parameters": {"mode": "raw", "jsonOutput": "={{ Object.assign({ \"route\": \"email\" }, $json) }}", "options": {}}, "id": "cdae9f9f-11c0-4f26-9ba1-5d5ed279ebfc", "name": "Set Route Email", "type": "n8n-nodes-base.set", "position": [2300, 640], "typeVersion": 3.4}, {"parameters": {"mode": "raw", "jsonOutput": "={{ Object.assign({ \"route\": \"linkedin\" }, $json) }}", "options": {}}, "id": "b01371f6-8871-4ad9-866d-888e22e7908e", "name": "Set Route Linkedin", "type": "n8n-nodes-base.set", "position": [2300, 800], "typeVersion": 3.4}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "email"}]}, "renameOutput": true, "outputKey": "email"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ba71a258-de67-4f61-a24a-33c86bd4c4f5", "operator": {"type": "string", "operation": "equals"}, "leftValue": "={{ $json.route }}", "rightValue": "linkedin"}]}, "renameOutput": true, "outputKey": "linkedin"}]}, "options": {}}, "id": "c4907171-b239-46a6-a0b0-6bf66570005f", "name": "Router", "type": "n8n-nodes-base.switch", "position": [1800, 1480], "typeVersion": 3.2}, {"parameters": {"assignments": {"assignments": [{"id": "bf5a0781-3bad-4f63-a49c-273b03204747", "name": "linkedin_summary", "type": "string", "value": "No activities found."}]}, "options": {}}, "id": "45554355-57ad-464d-b768-5b00d707fc58", "name": "Return LinkedIn Error1", "type": "n8n-nodes-base.set", "position": [2140, 1780], "typeVersion": 3.4}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "ff11640a-33e4-4695-a62c-7dcab57f0ae5", "operator": {"type": "object", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json }}", "rightValue": ""}]}, "options": {}}, "id": "05b04c17-eeeb-42f2-8d94-bc848889f17c", "name": "Has Emails?", "type": "n8n-nodes-base.if", "position": [2600, 1400], "typeVersion": 2.2}, {"parameters": {"assignments": {"assignments": [{"id": "9a7efc9e-26b0-48c9-83aa-ae989f20b1df", "name": "email_summary", "type": "string", "value": "No correspondance found."}]}, "options": {}}, "id": "c24aca66-6222-46ae-bb9b-1838b01f3100", "name": "Return Email Error1", "type": "n8n-nodes-base.set", "position": [2140, 1600], "typeVersion": 3.4}, {"parameters": {"content": "## 3.5: Extract LinkedIn Profile & Recent Activity\n[Learn more about the HTML node](https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.html)\n\nOnce we have our scraped LinkedIn profile, it's just a simple case of parsing and extracting the relevant sections from the page.\nFor the purpose of our workflow, we'll only need the \"About\" and \"Activity\" sections which we'll pull out of the page using a series of HTML nodes. Feel free to extract other sections to suit your needs! Once extracted, we'll combine the about and activities data in preparation of sending it to our LLM.", "height": 219.59936012669806, "width": 682.7350931085596, "color": 7}, "id": "22f3ccbf-19a2-4ca5-ba23-f91963b52c0a", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [3260, 1820], "typeVersion": 1}, {"parameters": {"fieldToSplitOut": "sections", "options": {"destinationFieldName": "data"}}, "id": "49b1fc8f-1259-4596-84b0-b37fae1c098c", "name": "Sections To List", "type": "n8n-nodes-base.splitOut", "position": [3420, 2080], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "b4354c00-cc1a-4a55-8b44-6ba4854cc6ba", "name": "linkedin_profile_url", "type": "string", "value": "={{ $json.linkedin_url }}"}, {"id": "4888db89-2573-4246-8ab9-c106a7fe5f38", "name": "linkedin_cookies", "type": "string", "value": "<COPY_YOUR_LINKEDIN_COOKIES_HERE>"}]}, "options": {}}, "id": "875b278d-44c6-4315-87e3-459a90799a9b", "name": "Set LinkedIn <PERSON>", "type": "n8n-nodes-base.set", "position": [2500, 2080], "typeVersion": 3.4}, {"parameters": {"content": "### Be aware of LinkedIn T&Cs!\nFor production, you may want to consider not using your main Linkedin account if you can help it!", "height": 110.**************, "width": 308.**************, "color": 3}, "id": "91da49ab-86a1-4539-b673-106b9edaeae9", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [2100, 2140], "typeVersion": 1}, {"parameters": {"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n### 🚨 Input Required!\nYou need to add your cuurent linkedIn Cookies here to continue.", "height": 301.*************, "width": 209.**************}, "id": "7abd390f-36a6-49af-b190-5bb720bd2ae8", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [2440, 2060], "typeVersion": 1}, {"parameters": {"fieldToSplitOut": "output.attendees", "options": {}}, "id": "40dfb438-76c2-40b5-8945-94dcf7cafcf7", "name": "Attendees to List", "type": "n8n-nodes-base.splitOut", "position": [1960, 720], "typeVersion": 1}, {"parameters": {"mode": "raw", "jsonOutput": "={{ Object.assign({}, $('Attendees to List').item.json, $json) }}", "options": {}}, "id": "cc7f8416-6ea1-4425-a320-3f8217d2ad4e", "name": "<PERSON><PERSON> with <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "position": [2860, 720], "typeVersion": 3.4}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1382e335-bfae-4665-a2ee-a05496a7b463", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.email }}", "rightValue": ""}]}, "options": {}}, "id": "459c5f2b-5dd5-491f-8bed-475ae5af7ac0", "name": "Has <PERSON><PERSON> Address?", "type": "n8n-nodes-base.if", "position": [1980, 1480], "typeVersion": 2.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "1382e335-bfae-4665-a2ee-a05496a7b463", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.linkedin_url }}", "rightValue": ""}]}, "options": {}}, "id": "610e9849-f06c-4534-a269-d1982dcab259", "name": "Has LinkedIn URL?", "type": "n8n-nodes-base.if", "position": [1980, 1660], "typeVersion": 2.2}, {"parameters": {"workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "mode": "each", "options": {"waitForSubWorkflow": true}}, "id": "43e5192e-c1b0-4d71-8d0e-aa466aa9930c", "name": "Get Correspondance", "type": "n8n-nodes-base.executeWorkflow", "position": [2480, 640], "typeVersion": 1.1, "onError": "continueRegularOutput"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "id": "4662f928-d38b-42e1-8a70-5676eb638ce1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2700, 720], "typeVersion": 3}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "attendees", "options": {}}, "id": "3eaf5d5b-d99c-4f9f-beaa-53b859bf482e", "name": "Aggregate Attendees", "type": "n8n-nodes-base.aggregate", "position": [3040, 720], "typeVersion": 1}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "activity", "options": {}}, "id": "752afdd3-0561-4e53-8b18-391741a2f43b", "name": "Activities To Array", "type": "n8n-nodes-base.aggregate", "position": [4380, 2260], "typeVersion": 1}, {"parameters": {"operation": "extractHtmlContent", "dataPropertyName": "body", "extractionValues": {"values": [{"key": "name", "cssSelector": "h1"}, {"key": "tagline", "cssSelector": ".pv-text-details__left-panel--full-width .text-body-medium"}, {"key": "location", "cssSelector": ".pv-text-details__left-panel--full-width + div .text-body-small"}, {"key": "num_connections", "cssSelector": "a[href=\"/mynetwork/invite-connect/connections/\"]"}, {"key": "num_followers", "cssSelector": "a[href=\"https://www.linkedin.com/feed/followers/\"]"}, {"key": "sections", "cssSelector": "section[data-view-name]", "returnValue": "html", "returnArray": true}]}, "options": {}}, "id": "a35dc751-62a0-4f5c-92cb-2801d060c613", "name": "Extract Profile Metadata", "type": "n8n-nodes-base.html", "position": [3260, 2080], "typeVersion": 1.2}, {"parameters": {"fieldToSplitOut": "activity", "options": {}}, "id": "5685ec9f-c219-41b4-94d7-787daef8a628", "name": "Activities To List", "type": "n8n-nodes-base.splitOut", "position": [4060, 2260], "typeVersion": 1}, {"parameters": {"url": "https://api.apify.com/v2/acts/apify~web-scraper/run-sync-get-dataset-items", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"startUrls\": [\n    {\n      \"url\": \"{{ $json.linkedin_profile_url }}\",\n      \"method\": \"GET\"\n    }\n  ],\n  \"initialCookies\": [\n    {\n      \"name\": \"li_at\",\n      \"value\": \"{{ $json.linkedin_cookies.match(/li_at=([^;]+)/)[1] }}\",\n      \"domain\": \".www.linkedin.com\"\n    }\n  ],\n  \"breakpointLocation\": \"NONE\",\n  \"browserLog\": false,\n  \"closeCookieModals\": false,\n  \"debugLog\": false,\n  \"downloadCss\": false,\n  \"downloadMedia\": false,\n  \"excludes\": [\n    {\n      \"glob\": \"/**/*.{png,jpg,jpeg,pdf}\"\n    }\n  ],\n  \"headless\": true,\n  \"ignoreCorsAndCsp\": false,\n  \"ignoreSslErrors\": false,\n  \n  \"injectJQuery\": true,\n  \"keepUrlFragments\": false,\n  \"linkSelector\": \"a[href]\",\n  \"maxCrawlingDepth\": 1,\n  \"maxPagesPerCrawl\": 1,\n  \"maxRequestRetries\": 1,\n  \"maxResultsPerCrawl\": 1,\n  \"pageFunction\": \"// The function accepts a single argument: the \\\"context\\\" object.\\n// For a complete list of its properties and functions,\\n// see https://apify.com/apify/web-scraper#page-function \\nasync function pageFunction(context) {\\n\\n    await new Promise(res => { setTimeout(res, 6000) });\\n    // This statement works as a breakpoint when you're trying to debug your code. Works only with Run mode: DEVELOPMENT!\\n    // debugger; \\n\\n    // jQuery is handy for finding DOM elements and extracting data from them.\\n    // To use it, make sure to enable the \\\"Inject jQuery\\\" option.\\n    const $ = context.jQuery;\\n    const title = $('title').first().text();\\n\\n    // Clone the body to avoid modifying the original content\\n    const bodyClone = $('body').clone();\\n    bodyClone.find('iframe, img, script, style, object, embed, noscript, svg, video, audio').remove();\\n    const body = bodyClone.html();\\n\\n    // Return an object with the data extracted from the page.\\n    // It will be stored to the resulting dataset.\\n    return {\\n        url: context.request.url,\\n        title,\\n        body\\n    };\\n}\",\n  \"postNavigationHooks\": \"// We need to return array of (possibly async) functions here.\\n// The functions accept a single argument: the \\\"crawlingContext\\\" object.\\n[\\n    async (crawlingContext) => {\\n        // ...\\n    },\\n]\",\n  \"preNavigationHooks\": \"// We need to return array of (possibly async) functions here.\\n// The functions accept two arguments: the \\\"crawlingContext\\\" object\\n// and \\\"gotoOptions\\\".\\n[\\n    async (crawlingContext, gotoOptions) => {\\n        // ...\\n    },\\n]\\n\",\n  \"proxyConfiguration\": {\n    \"useApifyProxy\": true\n  },\n  \"runMode\": \"PRODUCTION\",\n  \n  \"useChrome\": false,\n  \"waitUntil\": [\n    \"domcontentloaded\"\n  ],\n  \"globs\": [],\n  \"pseudoUrls\": [],\n  \"proxyRotation\": \"RECOMMENDED\",\n  \"maxConcurrency\": 50,\n  \"pageLoadTimeoutSecs\": 60,\n  \"pageFunctionTimeoutSecs\": 60,\n  \"maxScrollHeightPixels\": 5000,\n  \"customData\": {}\n}", "options": {}}, "id": "71240827-3e0d-4276-afb0-9ed72878ea4c", "name": "APIFY Web Scraper", "type": "n8n-nodes-base.httpRequest", "position": [2700, 2080], "typeVersion": 4.2}, {"parameters": {"operation": "extractHtmlContent", "dataPropertyName": "activity", "extractionValues": {"values": [{"key": "header", "cssSelector": ".feed-mini-update-optional-navigation-context-wrapper", "returnValue": "attribute", "attribute": "aria-label"}, {"key": "url", "cssSelector": ".feed-mini-update-optional-navigation-context-wrapper", "returnValue": "attribute", "attribute": "href"}, {"key": "content", "cssSelector": ".inline-show-more-text--is-collapsed"}, {"key": "num_reactions", "cssSelector": ".social-details-social-counts__reactions-count"}, {"key": "num_comments", "cssSelector": ".social-details-social-counts__comments"}, {"key": "num_reposts", "cssSelector": ".social-details-social-counts__item--truncate-text"}]}, "options": {}}, "id": "01659121-44f9-4d53-b973-cea29a8b0301", "name": "Get Activity Details", "type": "n8n-nodes-base.html", "position": [4220, 2260], "typeVersion": 1.2}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "title", "cssSelector": "h2 [aria-hidden=true]"}, {"key": "content", "cssSelector": "*", "returnValue": "html"}]}, "options": {}}, "id": "420a3a3e-ca99-49fb-b6b7-e9757f27b5d4", "name": "Get Sections", "type": "n8n-nodes-base.html", "position": [3580, 2080], "typeVersion": 1.2}, {"parameters": {"assignments": {"assignments": [{"id": "79d7943f-45a5-456c-a15b-cef53903409d", "name": "html", "type": "string", "value": "={{\n$input.all()\n  .find(input => input.json.title.toLowerCase().trim() === 'about')\n  .json\n  .content\n}}"}]}, "options": {}}, "id": "4983c987-79a7-4725-9913-630a71608f41", "name": "Get About Section", "type": "n8n-nodes-base.set", "position": [3740, 2080], "executeOnce": true, "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "79d7943f-45a5-456c-a15b-cef53903409d", "name": "html", "type": "string", "value": "={{\n$input.all()\n  .find(input => input.json.title.toLowerCase().trim() === 'activity')\n  .json\n  .content\n}}"}]}, "options": {}}, "id": "0e8bed5b-a622-4dbd-a11e-24df5d68f038", "name": "Get Activity Section", "type": "n8n-nodes-base.set", "position": [3740, 2260], "executeOnce": true, "typeVersion": 3.4}, {"parameters": {"operation": "extractHtmlContent", "dataPropertyName": "html", "extractionValues": {"values": [{"key": "activity", "cssSelector": ".profile-creator-shared-feed-update__mini-container", "returnValue": "html", "returnArray": true}]}, "options": {}}, "id": "5dd2677f-a4fc-447f-af7d-28e90dda46e8", "name": "Extract Activities", "type": "n8n-nodes-base.html", "position": [3900, 2260], "typeVersion": 1.2}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "id": "1a32808f-e465-47ef-b8bd-52b19c26ff1a", "name": "Merge1", "type": "n8n-nodes-base.merge", "position": [4560, 2080], "typeVersion": 3}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "3861abc7-7699-4459-b983-0c8b33e090b5", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.body }}", "rightValue": ""}]}, "options": {}}, "id": "6e452337-55a3-4466-a094-ec9106b36498", "name": "Is <PERSON><PERSON><PERSON> Successful?", "type": "n8n-nodes-base.if", "position": [2880, 2080], "typeVersion": 2.2}, {"parameters": {"operation": "extractHtmlContent", "dataPropertyName": "html", "extractionValues": {"values": [{"key": "about", "cssSelector": "body"}]}, "options": {}}, "id": "51a79d99-46af-4951-a99e-64f1d59f556e", "name": "Extract About", "type": "n8n-nodes-base.html", "position": [3900, 2080], "typeVersion": 1.2}, {"parameters": {"content": "## 3.6 Summarize LinkedIn For Attendee\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nFinally, we'll use the Basic LLM node to summarize our attendee's LinkedIn profile and recent activity. Our goal here is to identify and send back interesting tidbits of information which may be relevant to the meeting as well as inform the user. Should you require different criteria, simply edit the summarizer to get the response you need.", "height": 212.26880753952497, "width": 508.12647286359606, "color": 7}, "id": "d943fbde-f8fc-42b1-8b7e-f73735b81394", "name": "Sticky Note11", "type": "n8n-nodes-base.stickyNote", "position": [4560, 1840], "typeVersion": 1}, {"parameters": {"content": "## 3.3: Summarize Correspondance For Attendee\n[Read more about the Basic LLM node](https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm)\n\nNext, we'll generate a shorter version of the email(s) using the Basic LLM node - useful if the email was part of a large chain. The goal here is, if applicable, to remind the user of the conversion with this attendee and highlight any expectations which might be set before going into the meeting.", "height": 196.6490014749014, "width": 593.8676556715506, "color": 7}, "id": "b64bbfb0-ebd6-4fe7-9c02-3c1b72407df5", "name": "Sticky Note12", "type": "n8n-nodes-base.stickyNote", "position": [3160, 1180], "typeVersion": 1}, {"parameters": {"workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "mode": "each", "options": {"waitForSubWorkflow": true}}, "id": "a2dd5060-dd12-463b-8bbe-327ed691bdb9", "name": "Get LinkedIn Profile & Activity", "type": "n8n-nodes-base.executeWorkflow", "position": [2480, 800], "typeVersion": 1.1, "onError": "continueRegularOutput"}, {"parameters": {"content": "## Try It Out!\n\n### This workflow builds an AI meeting assistant who sends information-dense pre-meeting notifications for a user's upcoming meetings. This template is ideal for busy professional who is constantly on the move and wants to save time and make an impression.\n\n### How It Works\n* A scheduled trigger fires hourly and checks for upcoming meetings within the hour.\n* When found, a search for last correspondence and LinkedIn profile + recent activity is performed for each attendee.\n* Using both available correspondance and/or Linkedin profile, an AI/LLM is used to summarize this information and generate a short notification message which should help the user prepare for the meeting.\n* The notification is finally sent to the user's WhatsApp.\n\n### Need Help?\nJoin the [Discord](https://discord.com/invite/XPKeKXeB7d) or ask in the [Forum](https://community.n8n.io/)!\n\nHappy Hacking!", "height": 588.3011632094225, "width": 453.4804561790962}, "id": "fde0fa35-e692-4ca9-83ef-14e527f2f8d2", "name": "Sticky Note13", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "=from: {{ $json.from }}\nto: {{ $json.to }}\ndate: {{ $json.date }}\nsubject: {{ $json.subject }}\ntext:\n{{ $json.text }}", "messages": {"messageValues": [{"message": "=You are helping the \"to\" user recap the last correspondance they had in this email thread. Summarize succiently what was discussed, changed or agreed to help the user prepare for their upcoming meeting."}]}}, "id": "f2f19824-9865-465b-a612-7d3215209c79", "name": "Correspondance Recap Agent", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3160, 1400], "typeVersion": 1.4}, {"parameters": {"promptType": "define", "text": "=meeting date: {{ $('Check For Upcoming Meetings').item.json.start.dateTime }}\nmeeting url: {{ $('Check For Upcoming Meetings').item.json.hangoutLink }}\nmeeting summary: {{ $('Check For Upcoming Meetings').first().json.summary }}\nmeeting description: {{ $('Check For Upcoming Meetings').item.json.description }}\nmeeting with: {{ $json.attendees.map(item => item.name).join(',') }}\n---\n{{\n$json.attendees.map(item => {\n  return\n`attendee name: ${item.name}\n${item.name}'s last correspondance: ${item.email_summary.replaceAll('\\n', ' ') || `We have not had any correspondance with ${item.name}`}\n${item.name}'s linkedin profile: ${item.linkedin_summary.replaceAll('\\n', ' ') || `We were unable to find the linkedin profile for ${$json.name}`}\n`\n}).join('\\n---\\n')\n}}", "messages": {"messageValues": [{"message": "=You are a personal meeing assistant.\nYou are helping to remind user of an upcoming meeting with {{ $json.attendees.map(item => item.name).join(',') }} (aka \"the attendee(s)\"}. You will structure your notification using the following guidance:\n1. Start by providing the meeting summary, mentioning the date, with whom and providing the meeting link.\n2. For each attendee, give a short bullet point summary of their last correspondance. Assess if the correspondance has any relevance to the meeting and if so, identify any important todos or items which should be mentioned during the meeting. Additionally, give a short bullet point summary of attendee's recent activity which makes for good talking points. These need not be relevant to the meeting.\n\nWrite your response in a casual tone as if sending a SMS message to the user. USe bullet points where appropriate."}]}}, "id": "42641933-edf6-4b01-a17f-8cda2be7a093", "name": "Attendee Research Agent", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3420, 720], "typeVersion": 1.4}, {"parameters": {"promptType": "define", "text": "=### name\n{{ $('Extract Profile Metadata').item.json.name }}\n### about\n\"{{ $('Extract Profile Metadata').item.json.tagline }}\"\n{{ $json.about.replaceAll('\\n', ' ')}}\n### recent activity\n{{\n$json.activity.map((item, idx) => {\n  return [\n    item.header.replace('View full post.', ''),\n    `(${item.url})`,\n    ' - ',\n    item.content.replaceAll('\\n', ' ').replaceAll('…show more', '')\n  ].join(' ')\n}).join('\\n---\\n')\n}}", "messages": {"messageValues": [{"message": "=Summarize briefly the person and their recent activities as seen in the given feed and highlight noteworthy awards or achievements which make for good talking points."}]}}, "id": "1916515d-8b85-4da9-ac17-1c08485cdf04", "name": "LinkedIn Summarizer Agent", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [4720, 2080], "typeVersion": 1.4}], "pinData": {}, "connections": {"Merge": {"main": [[{"node": "<PERSON><PERSON> with <PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "LinkedIn Summarizer Agent", "type": "main", "index": 0}]]}, "Router": {"main": [[{"node": "Has <PERSON><PERSON> Address?", "type": "main", "index": 0}], [{"node": "Has LinkedIn URL?", "type": "main", "index": 0}]]}, "Has Emails?": {"main": [[{"node": "Get Message Contents", "type": "main", "index": 0}], [{"node": "Return Email <PERSON>r", "type": "main", "index": 0}]]}, "Get Sections": {"main": [[{"node": "Get About Section", "type": "main", "index": 0}, {"node": "Get Activity Section", "type": "main", "index": 0}]]}, "Extract About": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Set Route Email": {"main": [[{"node": "Get Correspondance", "type": "main", "index": 0}]]}, "Simplify Emails": {"main": [[{"node": "Correspondance Recap Agent", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Check For Upcoming Meetings", "type": "main", "index": 0}]]}, "Sections To List": {"main": [[{"node": "Get Sections", "type": "main", "index": 0}]]}, "APIFY Web Scraper": {"main": [[{"node": "Is <PERSON><PERSON><PERSON> Successful?", "type": "main", "index": 0}]]}, "Attendees to List": {"main": [[{"node": "Set Route Email", "type": "main", "index": 0}, {"node": "Set Route Linkedin", "type": "main", "index": 0}]]}, "Get About Section": {"main": [[{"node": "Extract About", "type": "main", "index": 0}]]}, "Has LinkedIn URL?": {"main": [[{"node": "Set LinkedIn <PERSON>", "type": "main", "index": 0}], [{"node": "Return LinkedIn Error1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Correspondance Recap Agent", "type": "ai_languageModel", "index": 0}]]}, "Activities To List": {"main": [[{"node": "Get Activity Details", "type": "main", "index": 0}]]}, "Extract Activities": {"main": [[{"node": "Activities To List", "type": "main", "index": 0}]]}, "Get Correspondance": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Has Email Address?": {"main": [[{"node": "Get Last Correspondence", "type": "main", "index": 0}], [{"node": "Return Email Error1", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "LinkedIn Summarizer Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Extract Attendee Information", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "Attendee Research Agent", "type": "ai_languageModel", "index": 0}]]}, "Set Route Linkedin": {"main": [[{"node": "Get LinkedIn Profile & Activity", "type": "main", "index": 0}]]}, "Activities To Array": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Aggregate Attendees": {"main": [[{"node": "Attendee Research Agent", "type": "main", "index": 0}]]}, "Set LinkedIn Cookie": {"main": [[{"node": "APIFY Web Scraper", "type": "main", "index": 0}]]}, "Get Activity Details": {"main": [[{"node": "Activities To Array", "type": "main", "index": 0}]]}, "Get Activity Section": {"main": [[{"node": "Extract Activities", "type": "main", "index": 0}]]}, "Get Message Contents": {"main": [[{"node": "Simplify Emails", "type": "main", "index": 0}]]}, "Is Scrape Successful?": {"main": [[{"node": "Extract Profile Metadata", "type": "main", "index": 0}], [{"node": "Return LinkedIn Error", "type": "main", "index": 0}]]}, "Attendee Research Agent": {"main": [[{"node": "WhatsApp Business Cloud", "type": "main", "index": 0}]]}, "Get Last Correspondence": {"main": [[{"node": "Has Emails?", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Router", "type": "main", "index": 0}]]}, "Extract Profile Metadata": {"main": [[{"node": "Sections To List", "type": "main", "index": 0}]]}, "LinkedIn Summarizer Agent": {"main": [[{"node": "Return LinkedIn Success", "type": "main", "index": 0}]]}, "Correspondance Recap Agent": {"main": [[{"node": "Return Email Success", "type": "main", "index": 0}]]}, "Check For Upcoming Meetings": {"main": [[{"node": "Extract Attendee Information", "type": "main", "index": 0}]]}, "Extract Attendee Information": {"main": [[{"node": "Attendees to List", "type": "main", "index": 0}]]}, "Merge Attendee with Summaries": {"main": [[{"node": "Aggregate Attendees", "type": "main", "index": 0}]]}, "Get LinkedIn Profile & Activity": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "2582", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}