{"name": "Create an RSS feed based on a website's content", "nodes": [{"parameters": {}, "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [380, 440], "typeVersion": 1, "id": "dbcda021-2623-4b84-aa4b-9405f64d802f"}, {"parameters": {"fieldToSplitOut": "post", "options": {}}, "name": "Item Lists", "type": "n8n-nodes-base.itemLists", "position": [1260, 440], "typeVersion": 1, "id": "aa6ab2b3-85f7-46bc-b0e2-b50f56a1fbc8"}, {"parameters": {"extractionValues": {"values": [{"key": "post", "cssSelector": ".blog-listing__post-content", "returnValue": "html", "returnArray": true}]}, "options": {}}, "name": "Extract Posts", "type": "n8n-nodes-base.htmlExtract", "position": [1040, 440], "typeVersion": 1, "id": "6f8a7c04-77e7-44a9-905f-f34ff4158fbc"}, {"parameters": {"url": "={{$json[\"base_domain\"]}}/blog/category/release", "responseFormat": "string", "options": {"timeout": 10000}}, "name": "Fetch Website", "type": "n8n-nodes-base.httpRequest", "position": [820, 440], "typeVersion": 1, "id": "f396ecf0-b26a-49f7-8fb6-226d14bd0b8f"}, {"parameters": {"values": {"string": [{"name": "base_domain", "value": "https://baserow.io"}]}, "options": {}}, "name": "Set URL", "type": "n8n-nodes-base.set", "position": [600, 440], "typeVersion": 1, "id": "357b7cf4-7abf-4d03-9dbd-05534434b9ea"}, {"parameters": {"values": {"string": [{"name": "link", "value": "={{$item(0).$node[\"Set URL\"].json[\"base_domain\"]}}{{$json[\"link\"]}}"}]}, "options": {}}, "name": "Complete Link", "type": "n8n-nodes-base.set", "position": [380, 640], "typeVersion": 1, "id": "6eb9e446-0482-4a63-846f-7e5924af43cc"}, {"parameters": {"value": "={{$json[\"date\"]}}", "dataPropertyName": "date", "toFormat": "YYYY-MM-DD", "options": {}}, "name": "Format Date", "type": "n8n-nodes-base.dateTime", "position": [600, 640], "typeVersion": 1, "id": "ae3457b3-579a-46b4-9c9e-0af711299d3b"}, {"parameters": {"functionCode": "return {\n  rss_item: \n`<item>\n  <title>${item.title}</title>\n  <link>${item.link}</link>\n  <description>${item.description}</description>\n  <pubDate>${item.date}</pubDate>\n</item>`\n}"}, "name": "Create RSS Items", "type": "n8n-nodes-base.functionItem", "position": [820, 640], "typeVersion": 1, "id": "de3aa09d-3fc0-4e0c-9874-c9739f60c471"}, {"parameters": {"path": "baserow-releases", "responseMode": "responseNode", "options": {}}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [380, 240], "webhookId": "27c1e4db-568f-4bf9-9474-0898ce1173f7", "typeVersion": 1, "id": "c86663d4-71fe-451e-9157-13236f3a3f8d"}, {"parameters": {"respondWith": "text", "responseBody": "={{$json[\"feed\"]}}", "options": {"responseHeaders": {"entries": [{"name": "content-type", "value": "application/xml"}]}}}, "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1260, 640], "typeVersion": 1, "id": "965bb59d-04e2-4713-b252-85db17e9b3a1"}, {"parameters": {"functionCode": "let feed =\n`<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n<rss version=\"2.0\">\n\n<channel>\n  <title>Baserow Releases</title>\n  <link>https://baserow.io/blog/category/release</link>\n  <description>Stay up to date with the latest changes and updates of Baserow</description>\n  ${items.map(e => e.json.rss_item).join('\\n')}\n</channel>\n\n</rss>`;\n\nreturn [{\n  json: {\n    feed: feed\n  }\n}];"}, "name": "Prepare Response", "type": "n8n-nodes-base.function", "position": [1040, 640], "typeVersion": 1, "id": "326b0d00-f72e-486a-a96d-1a03d963400c"}, {"parameters": {"dataPropertyName": "post", "extractionValues": {"values": [{"key": "date", "cssSelector": ".blog-listing__post-info > strong"}, {"key": "title", "cssSelector": ".blog-listing__post-title"}, {"key": "link", "cssSelector": ".blog-listing__post-title > a", "returnValue": "attribute", "attribute": "href"}, {"key": "description", "cssSelector": ".blog-listing__post-description"}]}, "options": {}}, "name": "Extract Fields", "type": "n8n-nodes-base.htmlExtract", "position": [1480, 440], "typeVersion": 1, "id": "c348a4b9-6791-4d03-a5da-167d6e4a2884"}], "pinData": {}, "connections": {"Set URL": {"main": [[{"node": "Fetch Website", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set URL", "type": "main", "index": 0}]]}, "Item Lists": {"main": [[{"node": "Extract Fields", "type": "main", "index": 0}]]}, "Format Date": {"main": [[{"node": "Create RSS Items", "type": "main", "index": 0}]]}, "Complete Link": {"main": [[{"node": "Format Date", "type": "main", "index": 0}]]}, "Extract Posts": {"main": [[{"node": "Item Lists", "type": "main", "index": 0}]]}, "Fetch Website": {"main": [[{"node": "Extract Posts", "type": "main", "index": 0}]]}, "Extract Fields": {"main": [[{"node": "Complete Link", "type": "main", "index": 0}]]}, "Create RSS Items": {"main": [[{"node": "Prepare Response", "type": "main", "index": 0}]]}, "Prepare Response": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Set URL", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "1418", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}