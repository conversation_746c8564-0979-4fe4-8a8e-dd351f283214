{"name": "AI Email Agent", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "filters": {"includeSpamTrash": true}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-1380, 60], "id": "7c14727a-e514-47a9-91d0-6f030a8055b9", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "g3DT5QDFZ8sd8DIn", "name": "Gmail account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-1080, 400], "id": "d0dae84b-bf32-4c27-afff-7ee79ddd6c71", "name": "Simple Memory"}, {"parameters": {"operation": "sendAndWait", "sendTo": "=", "subject": "Approval required for email", "message": "=Message sender: {{ $('Gmail Trigger').item.json.From }}\nOriginal message : {{ $('Gmail Trigger').item.json.snippet }}\nAI Response: {{ $json.output }}", "approvalOptions": {"values": {"approvalType": "double"}}, "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [-680, 60], "id": "2486e026-833e-4bdc-bb86-99ea2829d767", "name": "Gmail1", "webhookId": "d2bdf0c1-8d58-4062-9124-6a9c0de91b69", "credentials": {"gmailOAuth2": {"id": "g3DT5QDFZ8sd8DIn", "name": "Gmail account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6568a907-25fd-44ec-8170-d37364f55ec3", "leftValue": "={{ $json.data.approved }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-380, 100], "id": "896a6689-818f-44c5-9ad9-9e4ee3334fc3", "name": "If"}, {"parameters": {"operation": "reply", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "message": "=", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [0, 0], "id": "77f57ebf-1924-40d0-9e46-4c5bcad75175", "name": "Gmail2", "webhookId": "0a0a0cc3-a04e-4b67-a5c3-41cf5543d6ee", "credentials": {"gmailOAuth2": {"id": "g3DT5QDFZ8sd8DIn", "name": "Gmail account"}}}, {"parameters": {"operation": "mark<PERSON><PERSON>n<PERSON>", "messageId": "="}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [0, 280], "id": "759aca34-9bd1-4e50-a65e-9a85bff0f773", "name": "Gmail3", "webhookId": "0cb3f2d5-f23d-4e20-b00f-bec04239e15c", "credentials": {"gmailOAuth2": {"id": "g3DT5QDFZ8sd8DIn", "name": "Gmail account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.snippet }}", "options": {"systemMessage": "You are a Helpful Email Assistant. Your primary role is to assist me in responding to emails that come into my inbox. Your goal is to ensure timely, professional, and accurate communication on my behalf.\n\nDrafting Responses\n\nWrite clear, concise, and professional draft replies based on the context of the incoming email.\n\nMatch the tone and style appropriate for the sender (formal, friendly, neutral, etc.).\n\nAddress all questions or requests in the email, summarizing or clarifying if necessary.\n\nMaintain a friendly, professional tone at all times.\n\nEnsure quick turnaround times for important emails (within a few hours if urgent).\n\nAlways protect confidentiality and safeguard sensitive information.\n\nCommunicate proactively if you need more information to complete a response."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-1100, 60], "id": "13782c5c-82ac-45a0-8c5f-9ecd978a4578", "name": "AI Agent1"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1240, 340], "id": "84c8d215-db63-4f0a-b6ee-5f6361330f69", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "OGHNy3kucZD4Jn4A", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"Gmail1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Gmail2", "type": "main", "index": 0}], [{"node": "Gmail3", "type": "main", "index": 0}]]}, "Gmail Trigger": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent1", "type": "ai_memory", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Gmail1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "********-4c1b-41bf-a734-b6f2bbb5fdfd", "meta": {"templateCredsSetupCompleted": true, "instanceId": "72e5f4352ba75e0d263533019a323befce4bd6efd9846c035a7e719f82853e09"}, "id": "rnIaOWlVSKRPwnkI", "tags": []}