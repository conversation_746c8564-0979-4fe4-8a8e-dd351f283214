{"name": "Generate Unlimited Images For Free", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "documentId": {"__rl": true, "value": "1qnow_O1qheN3HbJkEP7b2ONT7w52SFVgWdHzKrvJUpA", "mode": "list", "cachedResultName": "Images Spreadsheet", "cachedResultUrl": ""}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": ""}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTrigger", "typeVersion": 1, "position": [-300, -140], "id": "e5070749-7995-4dee-9817-a3ae99740c62", "name": "Google Sheets Trigger", "credentials": {"googleSheetsTriggerOAuth2Api": {"id": "BO9H5QxTqANGMXTK", "name": "Google Sheets Trigger account"}}}, {"parameters": {"amount": 45}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [600, -140], "id": "f855ec59-544e-47ad-8a29-46e8d33e8188", "name": "Wait", "webhookId": "8c463f25-d3cd-421c-b9f4-e1487e9ab727"}, {"parameters": {"url": "=https://stablehorde.net/api/v2/generate/status/{{ $json.id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": ""}]}, "options": {"response": {"response": {"responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, -140], "id": "86be6d25-b4b5-4e0d-ba87-4301fa0146cc", "name": "HTTP Request1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6de48184-e286-40f6-b2f2-d7587a5f18af", "leftValue": "={{ $json.done }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1040, -140], "id": "bfc207bb-2161-4bcf-b32e-c3ccc93a445c", "name": "If"}, {"parameters": {"url": "={{ $('HTTP Request1').item.json.generations[0].img }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1480, -160], "id": "d6889c28-7b80-4c1f-80b2-818ead0fbfa7", "name": "HTTP Request2"}, {"parameters": {"jsCode": "// Get all input items (rows from Google Sheets)\nconst allRows = $input.all();\n\n// Get the last row\nconst lastRow = allRows[allRows.length - 1];\n\n// (Optional) Add a custom field to the last row\nlastRow.json.myNewField = 1;\n\n// Return only the last row\nreturn [lastRow];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-80, -140], "id": "1f473afe-9952-47ff-8cd6-a8a7d848f8ea", "name": "Code"}, {"parameters": {"assignments": {"assignments": [{"id": "2a75eca3-4dc9-4130-a23f-6837fdcd80df", "name": "prompt", "value": "={{ $json.Prompt }}", "type": "string"}, {"id": "fb35da55-a19c-4578-b7d3-d06757d4cfb6", "name": "params", "value": "={\n  \"n\": 1,\n  \"width\": 512,\n  \"height\": 512,\n  \"steps\": 20,\n  \"sampler_name\": \"k_euler_a\",\n  \"cfg_scale\": 7.5\n}", "type": "object"}]}, "options": {"ignoreConversionErrors": true}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [140, -140], "id": "987e20a2-b81c-4570-8a7d-4ed46b8b7cb5", "name": "Edit Fields1"}, {"parameters": {"method": "POST", "url": "https://stablehorde.net/api/v2/generate/async", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": ""}, {"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {"batching": {"batch": {"batchSize": 1}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [360, -140], "id": "6cf4cc16-08e3-49a6-8526-ea2e1e79ea8e", "name": "HTTP Request3"}, {"parameters": {"name": "={{ $('Code').item.json.Prompt }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1rgL9zyjRC15kzu2tK5bC3oO8kFly8BMZ", "mode": "list", "cachedResultName": "images", "cachedResultUrl": ""}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1700, -160], "id": "82d7f5df-36ee-46b0-b2bd-1174899b31c3", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "Ga76b0UnnmSq0q0z", "name": "Google Drive account"}}}], "pinData": {}, "connections": {"Google Sheets Trigger": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}, "HTTP Request3": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0665522b-fa74-41d9-8b34-a0270e749d84", "meta": {"templateCredsSetupCompleted": true, "instanceId": "72e5f4352ba75e0d263533019a323befce4bd6efd9846c035a7e719f82853e09"}, "id": "gR8YsiYtrqSwLBP5", "tags": []}