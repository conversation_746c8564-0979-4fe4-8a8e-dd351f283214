{"name": "Apify (TikTok) -> AirTable", "flow": [{"id": 12, "module": "apify:finishedActorRuns", "version": 1, "parameters": {"__IMTHOOK__": 518783}, "mapper": {}, "metadata": {"designer": {"x": 17, "y": 158, "name": "Finish Actor"}, "restore": {"parameters": {"__IMTHOOK__": {"data": {"editable": "false"}, "label": "My Finished Actor <PERSON> webhook"}}}, "parameters": [{"name": "__IMTHOOK__", "type": "hook:apify", "label": "Webhook", "required": true}]}}, {"id": 13, "module": "apify:fetchDatasetItems", "version": 1, "parameters": {"__IMTCONN__": 1870996}, "mapper": {"type": "none", "limit": "100", "format": "json", "datasetId": "{{12.defaultDatasetId}}"}, "metadata": {"designer": {"x": 295, "y": 152, "name": "Get Database Item"}, "restore": {"expect": {"type": {"mode": "chose", "label": "None"}, "format": {"mode": "chose", "label": "JSON"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "apify"}, "label": "My Apify connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "datasetId", "type": "text", "label": "Dataset ID", "required": true}, {"name": "type", "type": "select", "label": "Data transformation", "required": true, "validate": {"enum": ["clean", "simplified", "none"]}}, {"name": "format", "type": "select", "label": "Format", "required": true, "validate": {"enum": ["json", "csv", "html", "xml", "rss", "xlsx"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit", "validate": {"max": 100000, "min": 1}}, {"name": "offset", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Offset", "validate": {"min": 0}}]}, "onerror": [{"id": 23, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "15"}, "metadata": {"designer": {"x": 290, "y": 438}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}, {"id": 14, "module": "airtable:ActionSearchRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"base": "app62VdtYp2c7gydx", "table": "tblaAgdwupzCJCCwq", "fields": ["Username", "YouTube Channel Name", "Channel", "Profile Pic", "Nickname", "Signature", "BioLink", "Followers", "Following", "Friends", "Total Likes", "Total Videos", "Run ID", "Platform ID", "Verified", "Record ID", "Videos", "Last Video Created", "Oldest Post Date", "Last Modified", "Videos 2", "Output (Report)"], "formula": "{Username} = \"{{13.authorMeta.name}}\"", "maxRecords": "100", "useColumnId": false}, "metadata": {"designer": {"x": 600, "y": 150, "name": "<PERSON><PERSON>me"}, "restore": {"expect": {"base": {"mode": "chose", "label": "Content Engine 3.0"}, "sort": {"mode": "chose"}, "view": {"mode": "chose"}, "table": {"mode": "chose", "label": "Users"}, "fields": {"mode": "chose", "label": ["Username", "YouTube Channel Name", "Channel", "Profile Pic", "Nickname", "Signature", "BioLink", "Followers", "Following", "Friends", "Total Likes", "Total Videos", "Run ID", "Platform ID", "Verified", "Record ID", "Videos", "Last Video Created", "Oldest Post Date", "Last Modified", "Videos 2", "Output (Report)"]}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "formula", "type": "text", "label": "Formula"}, {"name": "maxRecords", "type": "integer", "label": "Limit"}, {"name": "sort", "spec": [{"name": "field", "type": "select", "label": "Field", "dynamic": true, "options": []}, {"name": "direction", "type": "select", "label": "Direction", "options": [{"label": "Descending", "value": "desc"}, {"label": "Ascending", "value": "asc"}]}], "type": "array", "label": "Sort"}, {"name": "view", "type": "select", "label": "View"}, {"name": "fields", "type": "select", "label": "Output Fields", "multiple": true}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Username", "type": "text", "label": "Username"}, {"name": "YouTube Channel Name", "type": "text", "label": "YouTube Channel Name"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Profile Pic", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "Profile Pic"}, {"name": "Nickname", "type": "text", "label": "Nickname"}, {"name": "Signature", "type": "text", "label": "Signature", "multiline": true}, {"name": "BioLink", "type": "text", "label": "BioLink"}, {"name": "Followers", "type": "number", "label": "Followers"}, {"name": "Following", "type": "number", "label": "Following"}, {"name": "Friends", "type": "number", "label": "Friends"}, {"name": "Total Likes", "type": "number", "label": "Total Likes"}, {"name": "Total Videos", "type": "number", "label": "Total Videos"}, {"name": "Run ID", "type": "text", "label": "Run ID"}, {"name": "Platform ID", "type": "text", "label": "Platform ID"}, {"name": "Verified", "type": "boolean", "label": "Verified"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Videos", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "Last Video Created", "type": "array", "label": "Last Video Created"}, {"name": "Oldest Post Date", "type": "text", "label": "Oldest Post Date"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Videos 2", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "Output (Report)", "spec": {"label": "Record ID"}, "type": "array", "label": "Output (Report)"}]}}, {"id": 15, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 900, "y": 150}}, "routes": [{"flow": [{"id": 16, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "filter": {"name": "Video", "conditions": [[{"a": "{{13.videoMeta}}", "o": "exist"}]]}, "mapper": {"array": "{{13.videoMeta.subtitleLinks}}"}, "metadata": {"designer": {"x": 1200, "y": 0}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 17, "module": "http:ActionGetFile", "version": 3, "parameters": {"handleErrors": false}, "filter": {"name": "Language = ENG", "conditions": [[{"a": "{{16.language}}", "b": "eng-US", "o": "text:equal"}]]}, "mapper": {"url": "{{16.downloadLink}}", "method": "get", "serializeUrl": false, "shareCookies": false}, "metadata": {"designer": {"x": 1500, "y": 0, "name": "Get Transcirption"}, "restore": {}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "hidden", "label": "Method"}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}]}}, {"id": 18, "module": "util:SetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "srt", "scope": "roundtrip", "value": "{{17.data}}"}, "metadata": {"designer": {"x": 1800, "y": 0, "name": "Transcription"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "srt", "type": "any", "label": "srt"}]}}, {"id": 20, "module": "airtable:ActionCreateRecord", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"base": "app62VdtYp2c7gydx", "table": "tbl74w570Z8MgVZcn", "record": {"fld3wlzxZQ7seM2c4": [{"url": "{{13.mediaUrls[]}}"}], "fld6cQySXv9jlsJ36": "{{13.diggCount}}", "fld9bCIXQpMjL6DHi": "{{18.srt}}", "fldBS5rCSqDKLzv4o": "{{13.commentCount}}", "fldD7AXgFuxWKhFME": "{{13.videoMeta.height}}", "fldE1QH5FXVgF3Rby": "{{13.shareCount}}", "fldECYZn9LnCWNksW": "{{13.videoMeta.width}}", "fldFYMJDJ3w6jHKon": "{{13.createTimeIS<PERSON>}}", "fldKgqHVNSaLFUDta": [{"url": "{{13.videoMeta.originalCoverUrl}}"}], "fldZoQa4QdgkDBIE9": "{{13.text}}", "fldaerFaYqpiWrtSd": "{{13.videoMeta.duration}}", "fldb1hnaF63jSMNe2": "{{13.id}}", "flddl9pTgSor1kOdB": "{{13.collectCount}}", "fldeIWUXAj9ebESg7": "{{13.playCount}}", "fldgEf9eHSCeoxNmH": ["{{14.id}}"], "fldlodhFIWyqvrece": "{{13.webVideoUrl}}", "flduOWaMDhai9wcnQ": "{{14.Channel}}"}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 2100, "y": 0, "name": "Create Video"}, "restore": {"expect": {"base": {"label": "Content Engine 3.0"}, "table": {"label": "Content", "nested": [{"name": "record", "spec": [{"name": "fldb1hnaF63jSMNe2", "type": "text", "label": "ID"}, {"name": "fldgEf9eHSCeoxNmH", "spec": {"label": "Record ID"}, "type": "array", "label": "User"}, {"name": "fldxWS3gA2cH6PCO5", "type": "boolean", "label": "Generate Script"}, {"name": "fldKgqHVNSaLFUDta", "spec": [{"help": "Airtable will download the file at the given url and keep its own copy of it.", "name": "url", "type": "text", "label": "File URL"}, {"help": "If empty, Airtable will generate the name automatically.", "name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Cover", "editable": true}, {"name": "fld3wlzxZQ7seM2c4", "spec": [{"help": "Airtable will download the file at the given url and keep its own copy of it.", "name": "url", "type": "text", "label": "File URL"}, {"help": "If empty, Airtable will generate the name automatically.", "name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Media", "editable": true}, {"name": "fldZoQa4QdgkDBIE9", "type": "text", "label": "Caption", "multiline": true}, {"name": "fld6cQySXv9jlsJ36", "type": "number", "label": "<PERSON>s"}, {"name": "fldE1QH5FXVgF3Rby", "type": "number", "label": "Shares"}, {"name": "fldeIWUXAj9ebESg7", "type": "number", "label": "Views"}, {"name": "flddl9pTgSor1kOdB", "type": "number", "label": "Saves"}, {"name": "fldD7AXgFuxWKhFME", "type": "number", "label": "Height"}, {"name": "fldECYZn9LnCWNksW", "type": "number", "label": "<PERSON><PERSON><PERSON>"}, {"name": "fldaerFaYqpiWrtSd", "type": "number", "label": "Duration"}, {"name": "fldBS5rCSqDKLzv4o", "type": "number", "label": "Comments"}, {"name": "fld9bCIXQpMjL6DHi", "type": "text", "label": "SRT", "multiline": true}, {"name": "fldFYMJDJ3w6jHKon", "time": true, "type": "date", "label": "Video Created Time"}, {"name": "fldNQncEACHOOAirG", "spec": {"label": "Record ID"}, "type": "array", "label": "Viral Scripts"}, {"mode": "edit", "name": "flduOWaMDhai9wcnQ", "type": "select", "label": "Channel", "dynamic": true, "options": [{"label": "YouTube", "value": "YouTube"}, {"label": "TikTok", "value": "TikTok"}, {"label": "Facebook", "value": "Facebook"}, {"label": "Instagram", "value": "Instagram"}, {"label": "LinkedIn", "value": "LinkedIn"}], "validate": false}, {"name": "fld5rYZ4Ehw5Wznnk", "type": "text", "label": "Post Copy", "multiline": true}, {"name": "fldlodhFIWyqvrece", "type": "text", "label": "Vid URL"}, {"name": "fldVQPTUaKWUejGWt", "spec": {"label": "Record ID"}, "type": "array", "label": "Users"}], "type": "collection", "label": "Record"}]}, "record": {"nested": {"fld3wlzxZQ7seM2c4": {"mode": "chose", "items": [null]}, "fldKgqHVNSaLFUDta": {"mode": "chose", "items": [null]}, "fldNQncEACHOOAirG": {"mode": "chose"}, "fldVQPTUaKWUejGWt": {"mode": "chose"}, "fldgEf9eHSCeoxNmH": {"mode": "chose", "items": [null]}, "flduOWaMDhai9wcnQ": {"mode": "edit"}, "fldxWS3gA2cH6PCO5": {"mode": "chose"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "record", "spec": [{"name": "fldb1hnaF63jSMNe2", "type": "text", "label": "ID"}, {"name": "fldgEf9eHSCeoxNmH", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "User"}, {"name": "fldxWS3gA2cH6PCO5", "type": "boolean", "label": "Generate Script"}, {"name": "fldKgqHVNSaLFUDta", "spec": [{"name": "url", "type": "text", "label": "File URL"}, {"name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Cover"}, {"name": "fld3wlzxZQ7seM2c4", "spec": [{"name": "url", "type": "text", "label": "File URL"}, {"name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Media"}, {"name": "fldZoQa4QdgkDBIE9", "type": "text", "label": "Caption"}, {"name": "fld6cQySXv9jlsJ36", "type": "number", "label": "<PERSON>s"}, {"name": "fldE1QH5FXVgF3Rby", "type": "number", "label": "Shares"}, {"name": "fldeIWUXAj9ebESg7", "type": "number", "label": "Views"}, {"name": "flddl9pTgSor1kOdB", "type": "number", "label": "Saves"}, {"name": "fldD7AXgFuxWKhFME", "type": "number", "label": "Height"}, {"name": "fldECYZn9LnCWNksW", "type": "number", "label": "<PERSON><PERSON><PERSON>"}, {"name": "fldaerFaYqpiWrtSd", "type": "number", "label": "Duration"}, {"name": "fldBS5rCSqDKLzv4o", "type": "number", "label": "Comments"}, {"name": "fld9bCIXQpMjL6DHi", "type": "text", "label": "SRT"}, {"name": "fldFYMJDJ3w6jHKon", "time": true, "type": "date", "label": "Video Created Time"}, {"name": "fldNQncEACHOOAirG", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Viral Scripts"}, {"mode": "edit", "name": "flduOWaMDhai9wcnQ", "type": "select", "label": "Channel"}, {"name": "fld5rYZ4Ehw5Wznnk", "type": "text", "label": "Post Copy"}, {"name": "fldlodhFIWyqvrece", "type": "text", "label": "Vid URL"}, {"name": "fldVQPTUaKWUejGWt", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Users"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "🆔", "type": "text", "label": "🆔"}, {"name": "User", "spec": {"label": "Record ID"}, "type": "array", "label": "User"}, {"name": "📰 Headline", "type": "text", "label": "📰 Headline", "multiline": true}, {"name": "⚙️ Content Action", "spec": {"label": "Record ID"}, "type": "array", "label": "⚙️ Content Action"}, {"name": "✍️ Generate Content", "type": "boolean", "label": "✍️ Generate Content"}, {"name": "Progress", "type": "text", "label": "Progress"}, {"name": "🌠 Cover", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "🌠 Cover"}, {"name": "🎥 Media", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "🎥 Media"}, {"name": "Open URL", "spec": [{"name": "label", "type": "text", "label": "Label"}, {"name": "url", "type": "url", "label": "URL"}], "type": "collection", "label": "Open URL"}, {"name": "<PERSON>s", "type": "number", "label": "<PERSON>s"}, {"name": "Shares", "type": "number", "label": "Shares"}, {"name": "Views", "type": "number", "label": "Views"}, {"name": "Saves", "type": "number", "label": "Saves"}, {"name": "Height", "type": "number", "label": "Height"}, {"name": "<PERSON><PERSON><PERSON>", "type": "number", "label": "<PERSON><PERSON><PERSON>"}, {"name": "Duration", "type": "number", "label": "Duration"}, {"name": "Comments", "type": "number", "label": "Comments"}, {"name": "💬 Caption", "type": "text", "label": "💬 Caption", "multiline": true}, {"name": "Video Created Time", "time": true, "type": "date", "label": "Video Created Time"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Viral Scripts", "spec": {"label": "Record ID"}, "type": "array", "label": "Viral Scripts"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Post Copy", "type": "text", "label": "Post Copy", "multiline": true}, {"name": "Vid URL", "type": "text", "label": "Vid URL"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Users", "spec": {"label": "Record ID"}, "type": "array", "label": "Users"}, {"name": "Transformations URL (from ⚙️ Content Action)", "type": "array", "label": "Transformations URL (from ⚙️ Content Action)"}, {"name": "⚙️ Content Transformations Tasks (from ⚙️ Content Action)", "type": "array", "label": "⚙️ Content Transformations Tasks (from ⚙️ Content Action)"}]}, "onerror": [{"id": 21, "module": "builtin:Break", "version": 1, "parameters": {}, "mapper": {"count": "3", "retry": true, "interval": "4"}, "metadata": {"designer": {"x": 2400, "y": 0}, "restore": {"expect": {"retry": {"mode": "chose"}}}, "expect": [{"name": "retry", "type": "boolean", "label": "Automatically complete execution", "required": true}, {"name": "count", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Number of attempts", "required": true, "validate": {"max": 10000, "min": 1}}, {"name": "interval", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Interval between attempts", "required": true, "validate": {"max": 44640, "min": 1}}]}}]}]}, {"flow": [{"id": 22, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "filter": {"name": "Order Position", "conditions": [[{"a": "{{13.`__IMTINDEX__`}}", "b": "1", "o": "text:equal"}]]}, "mapper": {"id": "{{14.id}}", "base": "app62VdtYp2c7gydx", "table": "tblaAgdwupzCJCCwq", "record": {"fld05G1G7cFnNAOKU": "{{13.authorMeta.following}}", "fld2HBLhnWU0WPaAG": "{{13.authorMeta.friends}}", "fld3E72IonPWZiPuq": "{{13.authorMeta.bioLink}}", "fldBoyFX262Bhphb4": [{"url": "{{13.authorMeta.avatar}}"}], "fldLLOs008GhG02TW": "{{13.authorMeta.signature}}", "fldk9UpuuVyqZKIRn": "{{13.authorMeta.nickName}}", "fldmXDR2kGjxauw8a": "{{13.authorMeta.heart}}", "fldnbPgJ0l26NxC1Z": "{{13.authorMeta.video}}", "fldqUhjxq8DfePbtx": "{{13.authorMeta.id}}", "fldxIrzx9DEY8coBi": "{{13.authorMeta.fans}}"}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1200, "y": 300, "name": "Update User Profile"}, "restore": {"expect": {"base": {"label": "Content Engine 3.0"}, "table": {"label": "Users"}, "record": {"nested": {"fld5XSZblnZOLCmTe": {"mode": "chose"}, "fldAhaynubBmfvKal": {"mode": "chose"}, "fldBoyFX262Bhphb4": {"mode": "chose", "items": [null]}, "fldGDs3retYm9V9mt": {"mode": "chose"}, "fldKSEsoGXOGlZ53Y": {"mode": "chose"}, "fldkNktlvOa8WhYwa": {"mode": "edit"}, "fldy2FVOdprlEmO2y": {"mode": "chose"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fldLukkB6DLW8jsg5", "type": "text", "label": "Username"}, {"name": "fldwddGQnsvnB7eFw", "type": "text", "label": "YouTube Channel Name"}, {"mode": "edit", "name": "fldkNktlvOa8WhYwa", "type": "select", "label": "Channel"}, {"name": "fldGDs3retYm9V9mt", "type": "boolean", "label": "Run # Now"}, {"name": "fldBoyFX262Bhphb4", "spec": [{"name": "url", "type": "text", "label": "File URL"}, {"name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Profile Pic"}, {"name": "fldk9UpuuVyqZKIRn", "type": "text", "label": "Nickname"}, {"name": "fldLLOs008GhG02TW", "type": "text", "label": "Signature"}, {"name": "fld3E72IonPWZiPuq", "type": "text", "label": "BioLink"}, {"name": "fldxIrzx9DEY8coBi", "type": "number", "label": "Followers"}, {"name": "fld05G1G7cFnNAOKU", "type": "number", "label": "Following"}, {"name": "fld2HBLhnWU0WPaAG", "type": "number", "label": "Friends"}, {"name": "fldmXDR2kGjxauw8a", "type": "number", "label": "Total Likes"}, {"name": "fldnbPgJ0l26NxC1Z", "type": "number", "label": "Total Videos"}, {"name": "fldrxFL0VvAcEbyC2", "type": "text", "label": "Run ID"}, {"name": "fldqUhjxq8DfePbtx", "type": "text", "label": "Platform ID"}, {"name": "fldKSEsoGXOGlZ53Y", "type": "boolean", "label": "Verified"}, {"name": "fldAhaynubBmfvKal", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "fldy2FVOdprlEmO2y", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "fld5XSZblnZOLCmTe", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Output (Report)"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Username", "type": "text", "label": "Username"}, {"name": "YouTube Channel Name", "type": "text", "label": "YouTube Channel Name"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Run # Now", "type": "boolean", "label": "Run # Now"}, {"name": "Profile Pic", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "Profile Pic"}, {"name": "Nickname", "type": "text", "label": "Nickname"}, {"name": "Signature", "type": "text", "label": "Signature", "multiline": true}, {"name": "BioLink", "type": "text", "label": "BioLink"}, {"name": "Followers", "type": "number", "label": "Followers"}, {"name": "Following", "type": "number", "label": "Following"}, {"name": "Friends", "type": "number", "label": "Friends"}, {"name": "Total Likes", "type": "number", "label": "Total Likes"}, {"name": "Total Videos", "type": "number", "label": "Total Videos"}, {"name": "Run ID", "type": "text", "label": "Run ID"}, {"name": "Platform ID", "type": "text", "label": "Platform ID"}, {"name": "Verified", "type": "boolean", "label": "Verified"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Videos", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "Last Video Created", "type": "array", "label": "Last Video Created"}, {"name": "Oldest Post Date", "type": "text", "label": "Oldest Post Date"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Videos 2", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "Output (Report)", "spec": {"label": "Record ID"}, "type": "array", "label": "Output (Report)"}]}}]}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": true, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}