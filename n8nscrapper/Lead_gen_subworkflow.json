{"name": "Enrich and Save Lead", "nodes": [{"id": "1", "type": "n8n-nodes-base.httpRequest", "position": [300, 200], "parameters": {"authentication": "none", "url": "https://api.example.com/enrich", "method": "POST", "bodyParametersUi": {"parameter": [{"name": "email", "value": "={{$json[\"email\"]}}"}]}, "options": {}}, "name": "<PERSON><PERSON>"}, {"id": "2", "type": "n8n-nodes-base.postgres", "position": [600, 200], "parameters": {"authentication": "pgAuth", "operation": "execute<PERSON>uery", "query": "INSERT INTO leads (name, email, company, position) VALUES ('{{$json[\"name\"]}}', '{{$json[\"email\"]}}', '{{$json[\"company\"]}}', '{{$json[\"position\"]}}');"}, "name": "Save to Database"}], "connections": {"Enrich Lead": {"main": [[{"node": "Save to Database", "type": "main", "index": 0}]]}}}