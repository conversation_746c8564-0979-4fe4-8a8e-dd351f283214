{"name": "My workflow 13", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-140, 0], "id": "544a02d2-f095-491e-9081-f56227e227a1", "name": "<PERSON>eg<PERSON>", "webhookId": "3d3f14ad-37ba-49b1-a0f7-dadaa17f208b", "credentials": {"telegramApi": {"id": "JTYvKHnofhmFlk9A", "name": "Telegram account"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "options": {"systemMessage": "=You are a smart, helpful AI assistant that helps users schedule meetings, check availability, create events in Google Calendar, and send meeting details via email.\nAlways respond like a friendly, professional human assistant.\n\nCore Process:\n\nALWAYS use Date and Time tool FIRST to get current date/time\nALWAYS check Google Calendar for conflicts before booking new meetings\nOnly create meetings if no time conflicts exist\n\nCritical Rules:\n\nDate Accuracy: When user says \"tomorrow,\" \"next Monday,\" etc., use Date and Time tool to calculate exact dates.\n\nConflict Prevention: Before creating any meeting, check if user already has something booked at that time. If conflict exists, inform user and offer alternatives.\n\nNever double-book: Always resolve conflicts before creating new meetings.\n\nTool Usage:\n\nDate and Time tool: Use first to establish current date, then for any relative date calculations\n\nGoogle Calendar tool: Check for conflicts first, then create events only if no conflicts\n\nGmail tool: Use when user asks to send meeting details or email invites\n\nExample Workflows:\n\nBooking Request: \"Book meeting with <PERSON> tomorrow at 2 PM\"\n\nGet current date → Calculate tomorrow's exact date\nCheck calendar for that time slot\nIf no conflict: Create meeting / If conflict: \"You already have [meeting] at 2 PM on [date]. Different time?\"\n\nMeeting Inquiry: \"Do I have meetings tomorrow?\"\n\nGet current date → Calculate tomorrow's exact date\nSearch calendar for that date\nList meetings with specific date: \"Here are your meetings for [exact date]:\"\n\n\nBe proactive. Make scheduling easy and human-like. Don’t ask the user to write structured inputs — understand what they mean naturally."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [80, 0], "id": "b58851a6-f70a-4fe2-bc50-c6011026e4cc", "name": "AI Agent"}, {"parameters": {"sendTo": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('To', ``, 'string') }}", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "emailType": "text", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', ``, 'string') }}", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [560, 240], "id": "dca60431-6e0b-404d-b339-1a81c81496ef", "name": "Gmail", "webhookId": "544e077e-e370-458f-982a-6740070b19ed", "credentials": {"gmailOAuth2": {"id": "rBxGurNzrzEKoBLD", "name": "Gmail account"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [440, 0], "id": "4106388f-d35f-4be7-a696-0391956a1d6a", "name": "Telegram", "webhookId": "c939004c-728c-4431-969d-35c88ff6f52c", "credentials": {"telegramApi": {"id": "JTYvKHnofhmFlk9A", "name": "Telegram account"}}}, {"parameters": {"options": {"timezone": "Asia/Dhaka"}}, "type": "n8n-nodes-base.dateTimeTool", "typeVersion": 2, "position": [680, 240], "id": "d463454f-5cdb-45c0-bc14-ee51a70f338f", "name": "Date & Time"}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "For the Agent"}, "limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('After', ``, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Before', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [200, 240], "id": "e5703aa8-212e-4b9a-94f8-e1c4025c9e64", "name": "Get", "credentials": {"googleCalendarOAuth2Api": {"id": "kOPdo61owCpiJY9s", "name": "Google Calendar account"}}}, {"parameters": {"operation": "update", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "For the Agent"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "updateFields": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [100, 240], "id": "7040b297-2d88-4025-a6bb-9f069ee3a2d5", "name": "Update", "credentials": {"googleCalendarOAuth2Api": {"id": "kOPdo61owCpiJY9s", "name": "Google Calendar account"}}}, {"parameters": {"operation": "delete", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "For the Agent"}, "eventId": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Event_ID', ``, 'string') }}", "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [300, 240], "id": "e8daccf5-dbfa-4aea-8335-bbb8bc6b5134", "name": "Delete", "credentials": {"googleCalendarOAuth2Api": {"id": "1033dtb87tp6gEhn", "name": "Google Calendar account 2"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "For the Agent"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', ``, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', ``, 'string') }}", "additionalFields": {"attendees": ["={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('attendees0_Attendees', ``, 'string') }}"], "description": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Description', ``, 'string') }}", "summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', ``, 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [400, 240], "id": "7fd0ac29-5bbe-4f02-a727-12df98b25e4d", "name": "Create", "credentials": {"googleCalendarOAuth2Api": {"id": "kOPdo61owCpiJY9s", "name": "Google Calendar account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-240, 240], "id": "22d39794-4fc2-4813-b048-aa6ac0bb54ca", "name": "OpenAI", "credentials": {"openAiApi": {"id": "2Pt837d3ZMKdLsqW", "name": "n8n free OpenAI API credits"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-120, 240], "id": "df938fd6-2f55-411c-8dd3-ab8a6f12fc41", "name": "Memory"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Gmail": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Date & Time": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Get": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Update": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Delete": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Create": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "b06ad900-394f-4d11-a4be-ca5f5af0d41a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "5c2f5b5b4cf20114a6c53aaed9430bfdabad5c4604b5a73e1363b96c75e842ec"}, "id": "tBBhmC9RcS72xkjn", "tags": []}