{"name": "My workflow 2", "nodes": [{"parameters": {"content": "# 🎬 Social Media Content Generator\n\n## Workflow Overview\nAutomated social media content creation from video transcripts\n\n### 🎯 Trigger: Airtable Webhook\n- **Action**: Receives webhook from Airtable automation\n- **Data**: RecordId and action type (e.g., \"post-ig\")\n- **Purpose**: Starts the content generation pipeline\n\n### 📊 Step 1: Fetch Record\n- **Node**: Airtable (Get Record)\n- **Action**: Retrieves full record data using RecordId\n- **Data**: Name, transcript, and other fields\n\n### 📁 Step 2: Create Google Drive Folder\n- **Node**: Google Drive (Create Folder)\n- **Action**: Creates blue folder in /tutorials directory\n- **Name**: Uses record Name field\n- **Updates**: Stores folder ID back to Airtable\n\n### 🤖 Step 3: AI Content Analysis\n- **Node**: AI Agent with Google Gemini 2.5 Flash\n- **Input**: Video transcript from Airtable\n- **Structured Output**: JSON with all social formats:\n  - YouTube title & description\n  - YouTube thumbnail text\n  - Twitter thread (array)\n  - LinkedIn post\n  - Instagram caption\n  - TikTok caption\n  - YouTube Shorts caption\n  - Relevant tags\n\n### 💾 Step 4: Save Transcript File\n- **Node**: Google Drive (Create from Text)\n- **Action**: Saves transcript as text file\n- **Location**: Inside the created folder\n- **Name**: Uses record Name field\n\n### 📋 Step 5: Update Airtable Results\n- **Node**: Airtable (Update Record)\n- **Data**: All AI-generated social media content\n- **Special**: Twitter thread array joined with newlines\n\n---\n\n**🎯 Result**: Complete social media content suite ready for multi-platform publishing, organized in Google Drive with all data stored in Airtable.", "height": 940, "width": 2180}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, 120], "id": "db475f06-8881-4203-8d99-2c15854d85c4", "name": "Workflow Documentation"}, {"parameters": {"base": {"__rl": true, "value": "appTFomwpoQ8GVsSo", "mode": "list", "cachedResultName": "Netkreatives.com", "cachedResultUrl": "https://airtable.com/appTFomwpoQ8GVsSo"}, "table": {"__rl": true, "value": "tblRnaXqxrvcQhqBw", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appTFomwpoQ8GVsSo/tblRnaXqxrvcQhqBw"}, "id": "={{ $json.query.RecordId }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [360, 500], "id": "80784e9f-e6ec-4e2e-b1ea-fe2fdf575063", "name": "1. Get Record Data", "credentials": {"airtableTokenApi": {"id": "3ml0uw3TOJygSsTu", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"path": "2d9f3a0a-e2e4-4005-8ac9-f192349a59fd", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [40, 500], "id": "0b3abf35-6d54-4b36-85c3-5a32ca02f4ba", "name": "🎯 Webhook Trigger", "webhookId": "2d9f3a0a-e2e4-4005-8ac9-f192349a59fd"}, {"parameters": {"promptType": "define", "text": "=Analyze this script: {{ $json.transcript }}\n\nCreate engaging, discoverable content that sparks genuine curiosity without being clickbait. Focus on value-driven content that clearly communicates what viewers will learn or gain.\n\nProvide the following in JSON format:\n\n**YouTube Title**: Create a compelling title (50-60 characters) that:\n- Clearly states the main benefit or outcome\n- Uses power words that indicate value (How, Why, What, Complete, Ultimate, etc.)\n- Includes relevant keywords naturally\n- Promises specific knowledge or skills\n\n**YouTube Description**: Write a comprehensive description (125-150 words) that:\n- Opens with a hook that summarizes the key value proposition\n- Includes 3-4 specific takeaways viewers will gain\n- Contains relevant keywords naturally throughout\n- Ends with a call-to-action for engagement\n- Uses line breaks for readability\n\n**YouTube Thumbnail Text**: Create punchy text (3-5 words max) that:\n- Highlights the main benefit or surprising insight\n- Uses action words or numbers when relevant\n- Complements the title without repeating it exactly\n\n**Relevant Tags**: Generate 10-15 tags that include:\n- Primary topic keywords\n- Secondary related terms\n- Skill-based keywords\n- Industry/niche terms\n- Long-tail variations\n\n**Twitter Thread**: Create a 5-7 tweet thread that:\n- Tweet 1: Hook with the main insight/benefit\n- Tweets 2-5: Break down key points with specific examples\n- Tweet 6: Actionable takeaway or next step\n- Tweet 7: Engagement question and thread summary\n- Use emojis strategically and include relevant hashtags\n\n**LinkedIn Post**: Write a professional post (150-200 words) that:\n- Opens with a thought-provoking question or insight\n- Shares 2-3 key professional takeaways\n- Includes industry-relevant context\n- Ends with a discussion starter\n- Uses professional but conversational tone\n\n**Instagram Caption**: Create an engaging caption (100-120 words) that:\n- Starts with an attention-grabbing first line\n- Breaks down the content into digestible insights\n- Uses relevant hashtags (8-12) naturally within the text\n- Includes a clear call-to-action\n- Uses line breaks and emojis for visual appeal\n\n**TikTok Description**: Write a short, engaging description (80-100 characters) that:\n- Creates immediate curiosity or relatability\n- Uses trending language and relevant hashtags\n- Promises quick value or entertainment\n- Includes a hook for the first 3 seconds\n\n**YouTube Shorts Description**: Create a brief description (60-80 words) that:\n- Emphasizes the quick value or insight\n- Uses urgency or curiosity-driven language\n- Includes relevant hashtags for discoverability\n- Encourages immediate action (like, share, follow)\n\nFocus on authentic value delivery while optimizing for each platform's unique audience behavior and algorithm preferences.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [820, 140], "id": "1bf39a5d-96d1-4417-88d7-0837441aa4a7", "name": "🤖 AI Content Generator"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-06-05", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [780, 380], "id": "a365a18e-3bef-4bd3-bb58-d608261f20c6", "name": "🧠 Gemini Pro Model", "credentials": {"googlePalmApi": {"id": "zEwV1tMUHpEXhikc", "name": "Gemini"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"youtube_title\": \"Your Video Title Here\",\n  \"youtube_description\": \"A concise, engaging description of what viewers will learn or see in your video.\",\n  \"youtube_thumbnail_text\": \"Key Phrase for Thumbnail\",\n  \"relevant_tags\": [\n    \"tag1\",\n    \"tag2\",\n    \"tag3\"\n  ],\n  \"twitter_thread\": [\n    \"First tweet text.\",\n    \"Second tweet text.\",\n    \"Third tweet text.\"\n  ],\n  \"linkedin_post\": \"A LinkedIn-style post summarizing your content and linking back to it.\",\n  \"instagram_caption\": \"An Instagram caption that hooks the audience and points them to your bio link.\",\n  \"tiktok_caption\": \"Short, punchy TikTok description with relevant hashtags.\",\n  \"youtube_shorts_caption\": \"A quick call-to-action for your Shorts viewers.\"\n}", "autoFix": true}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [920, 360], "id": "dc11d7b0-1076-4ba9-8487-bc7c4e873ef9", "name": "📋 JSON Output Parser"}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1020, 560], "id": "0998b715-8303-4a8d-b62c-930bcb2028b5", "name": "⚡ Gemini Flash Model", "credentials": {"googlePalmApi": {"id": "zEwV1tMUHpEXhikc", "name": "Gemini"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appTFomwpoQ8GVsSo", "mode": "list", "cachedResultName": "Netkreatives.com", "cachedResultUrl": "https://airtable.com/appTFomwpoQ8GVsSo"}, "table": {"__rl": true, "value": "tblRnaXqxrvcQhqBw", "mode": "list", "cachedResultName": "Table 1", "cachedResultUrl": "https://airtable.com/appTFomwpoQ8GVsSo/tblRnaXqxrvcQhqBw"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('1. Get Record Data').item.json.id }}", "youtube_title": "={{ $json.output.youtube_title }}", "youtube_description": "={{ $json.output.youtube_description }}", "linkedin_post": "={{ $json.output.linkedin_post }}", "instagram_caption": "={{ $json.output.instagram_caption }}", "tiktok_caption": "={{ $json.output.tiktok_caption }}", "youtube_shorts_caption": "={{ $json.output.youtube_shorts_caption }}", "youtube_thumbnail_text": "={{ $json.output.youtube_thumbnail_text }}", "twitter_thread": "={{ $json.output.twitter_thread.join('\\n\\n') }}\n\n\n"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "transcript", "displayName": "transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "start", "displayName": "start", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "boolean", "readOnly": false, "removed": true}, {"id": "youtube_title", "displayName": "youtube_title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "youtube_description", "displayName": "youtube_description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "youtube_thumbnail_text", "displayName": "youtube_thumbnail_text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "twitter_thread", "displayName": "twitter_thread", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "linkedin_post", "displayName": "linkedin_post", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "instagram_caption", "displayName": "instagram_caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "tiktok_caption", "displayName": "tiktok_caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "youtube_shorts_caption", "displayName": "youtube_shorts_caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1300, 340], "id": "123b2d1a-880b-4af2-b01c-bd14c5490576", "name": "4. Save Social Media Content", "credentials": {"airtableTokenApi": {"id": "3ml0uw3TOJygSsTu", "name": "Airtable Personal Access Token account"}}, "notes": "Saves all AI-generated social media content to Airtable record"}, {"parameters": {"resource": "folder", "name": "={{ $json.Name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "10BP0rscwtRsG1tYr7N2xfxVRLch_1-MO", "mode": "list", "cachedResultName": "tutorials", "cachedResultUrl": "https://drive.google.com/drive/folders/10BP0rscwtRsG1tYr7N2xfxVRLch_1-MO"}, "options": {"folderColorRgb": "#0E66E9"}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [940, 740], "id": "257c9a7d-32ec-402c-8308-f7b4a87b5387", "name": "2. Create Project Folder", "credentials": {"googleDriveOAuth2Api": {"id": "cMdQMNaewaEQ6Mi3", "name": "Google Drive account"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appTFomwpoQ8GVsSo", "mode": "list", "cachedResultName": "Netkreatives.com", "cachedResultUrl": "https://airtable.com/appTFomwpoQ8GVsSo"}, "table": {"__rl": true, "value": "tblRnaXqxrvcQhqBw", "mode": "list", "cachedResultName": "Youtube tool", "cachedResultUrl": "https://airtable.com/appTFomwpoQ8GVsSo/tblRnaXqxrvcQhqBw"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "={{ $('🎯 Webhook Trigger').item.json.query.RecordId }}", "google drive": "={{ $json.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true, "removed": false}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "google drive", "displayName": "google drive", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "transcript", "displayName": "transcript", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "start", "displayName": "start", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "youtube_title", "displayName": "youtube_title", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "youtube_description", "displayName": "youtube_description", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "youtube_thumbnail_text", "displayName": "youtube_thumbnail_text", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "twitter_thread", "displayName": "twitter_thread", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "linkedin_post", "displayName": "linkedin_post", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "instagram_caption", "displayName": "instagram_caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "tiktok_caption", "displayName": "tiktok_caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "youtube_shorts_caption", "displayName": "youtube_shorts_caption", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Record ID", "displayName": "Record ID", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1300, 740], "id": "15c140d8-dd97-47d0-b387-9e31cfa6906d", "name": "5. <PERSON> to Record", "credentials": {"airtableTokenApi": {"id": "3ml0uw3TOJygSsTu", "name": "Airtable Personal Access Token account"}}}, {"parameters": {"operation": "createFromText", "content": "={{ $json.fields.transcript }}", "name": "={{ $json.fields.Name }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "={{ $('2. Create Project Folder').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1520, 740], "id": "cfef3343-d5fe-4c7e-904c-01d010cbb6ce", "name": "6. Save Transcript File", "credentials": {"googleDriveOAuth2Api": {"id": "cMdQMNaewaEQ6Mi3", "name": "Google Drive account"}}}], "pinData": {"🎯 Webhook Trigger": [{"json": {"headers": {"host": "n8n.netkreatives.com", "user-agent": "Mozilla/5.0 (compatible; AirtableScripting; +https://airtable.com/developers/scripting;)", "accept": "*/*", "accept-encoding": "gzip,deflate", "x-airtable-source": "appTFomwpoQ8GVsSo/wflR8uEfcTvT3GBkL", "x-forwarded-for": "**********", "x-forwarded-host": "n8n.netkreatives.com", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "9d5a55f11021", "x-real-ip": "**********"}, "params": {}, "query": {"RecordId": "recx2pne4rSvwwsVN", "action": "post-ig"}, "body": {}, "webhookUrl": "https://n8n.netkreatives.com/webhook-test/2d9f3a0a-e2e4-4005-8ac9-f192349a59fd", "executionMode": "test"}}]}, "connections": {"1. Get Record Data": {"main": [[{"node": "2. Create Project Folder", "type": "main", "index": 0}, {"node": "🤖 AI Content Generator", "type": "main", "index": 0}]]}, "🎯 Webhook Trigger": {"main": [[{"node": "1. Get Record Data", "type": "main", "index": 0}]]}, "🤖 AI Content Generator": {"main": [[{"node": "4. Save Social Media Content", "type": "main", "index": 0}]]}, "🧠 Gemini Pro Model": {"ai_languageModel": [[{"node": "🤖 AI Content Generator", "type": "ai_languageModel", "index": 0}]]}, "📋 JSON Output Parser": {"ai_outputParser": [[{"node": "🤖 AI Content Generator", "type": "ai_outputParser", "index": 0}]]}, "⚡ Gemini Flash Model": {"ai_languageModel": [[{"node": "📋 JSON Output Parser", "type": "ai_languageModel", "index": 0}]]}, "2. Create Project Folder": {"main": [[{"node": "5. <PERSON> to Record", "type": "main", "index": 0}]]}, "5. Link Folder to Record": {"main": [[{"node": "6. Save Transcript File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"instanceId": "3cc6c4256e2bbdec1b3708c49dc3889c7266357f3e239651c157c2de760e6c65"}, "tags": []}