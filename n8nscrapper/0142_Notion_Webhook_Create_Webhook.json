{"nodes": [{"name": "Get Team Members", "type": "n8n-nodes-base.function", "position": [1030, 150], "parameters": {"functionCode": "const newItems = [];\n\nfor (const item of items[0].json.body.teamMembers) {\n  const newItem = { json: item }\n  newItems.push(newItem);\n}\n\nreturn newItems;\n"}, "typeVersion": 1, "alwaysOutputData": false}, {"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1250, 460], "parameters": {"mode": "multiplex"}, "typeVersion": 1}, {"name": "Query Current Semester", "type": "n8n-nodes-base.notion", "position": [700, 20], "parameters": {"options": {"sort": {"sortValue": [{"key": "created_time", "direction": "descending", "timestamp": true}]}, "filter": {"singleCondition": {"key": "Is Current?|checkbox", "condition": "equals", "checkboxValue": true}}}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": "2003319a-bc73-423a-9378-01999b4884fb"}, "credentials": {"notionApi": "Oasis Hub Production"}, "typeVersion": 1}, {"name": "Select Semester ID and Projects Count", "type": "n8n-nodes-base.set", "position": [1030, 330], "parameters": {"values": {"number": [{"name": "projectsCount", "value": "={{$json[\"Projects\"].length}}"}], "string": [{"name": "semesterID", "value": "={{$json[\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "executeOnce": true, "typeVersion": 1}, {"name": "Use Default Name if Not Specified", "type": "n8n-nodes-base.set", "position": [1470, 460], "parameters": {"values": {"string": [{"name": "projectName", "value": "={{ $json[\"projectName\"] == \"\" ? \"Project Group \" + ($json[\"projectsCount\"] + 1) : $json[\"projectName\"] }}"}]}, "options": {}}, "typeVersion": 1}, {"name": "Select Project Showcase ID", "type": "n8n-nodes-base.set", "position": [1890, 460], "parameters": {"values": {"string": [{"name": "projectID", "value": "={{$json[\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Get Project Name & Idea", "type": "n8n-nodes-base.set", "position": [820, 480], "parameters": {"values": {"string": [{"name": "projectName", "value": "={{$json[\"body\"][\"projectName\"]}}"}, {"name": "projectIdea", "value": "={{$json[\"body\"][\"projectIdea\"]}}"}], "boolean": []}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Create Project", "type": "n8n-nodes-base.notion", "position": [1690, 460], "parameters": {"blockUi": {"blockValues": []}, "resource": "databasePage", "databaseId": "f9c8a070-d398-482b-a7a4-5e42c7982e6a", "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{$json[\"projectName\"]}}"}, {"key": "Semesters|relation", "relationValue": ["={{$json[\"semesterID\"]}}"]}, {"key": "Project Idea|rich_text", "textContent": "={{$json[\"projectIdea\"]}}"}]}}, "credentials": {"notionApi": "Oasis Hub Production"}, "typeVersion": 1}, {"name": "If user exists", "type": "n8n-nodes-base.if", "position": [1690, 170], "parameters": {"conditions": {"string": [], "boolean": [{"value1": "={{Object.keys($json).includes(\"id\") }}", "value2": true}]}}, "executeOnce": false, "typeVersion": 1, "alwaysOutputData": false}, {"name": "Create User", "type": "n8n-nodes-base.notion", "position": [1890, 270], "parameters": {"resource": "databasePage", "databaseId": "27a30c5b-c418-4200-8f48-d7fb7b043fbe", "propertiesUi": {"propertyValues": [{"key": "Name|title", "title": "={{$json[\"name\"]}}"}, {"key": "Email|email", "emailValue": "={{$json[\"email\"]}}"}]}}, "credentials": {"notionApi": "Oasis Hub Production"}, "typeVersion": 1}, {"name": "Query for User", "type": "n8n-nodes-base.notion", "position": [1250, 260], "parameters": {"options": {"filter": {"singleCondition": {"key": "Email|email", "condition": "equals", "emailValue": "={{$json[\"email\"]}}"}}}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": "27a30c5b-c418-4200-8f48-d7fb7b043fbe"}, "credentials": {"notionApi": "Oasis Hub Production"}, "executeOnce": false, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Merge1", "type": "n8n-nodes-base.merge", "position": [1460, 170], "parameters": {"mode": "mergeByKey", "propertyName1": "email", "propertyName2": "Email"}, "typeVersion": 1}, {"name": "Merge2", "type": "n8n-nodes-base.merge", "position": [2750, -160], "parameters": {"mode": "multiplex"}, "typeVersion": 1}, {"name": "Update Semester for User", "type": "n8n-nodes-base.notion", "position": [3240, -160], "parameters": {"pageId": "={{$json[\"id\"]}}", "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Semesters|relation", "relationValue": ["={{$json[\"allSemesterIDs\"].join(',')}}"]}]}}, "credentials": {"notionApi": "Oasis Hub Production"}, "typeVersion": 1}, {"name": "Query User", "type": "n8n-nodes-base.notion", "position": [2460, 170], "parameters": {"options": {"filter": {"singleCondition": {"key": "Email|email", "condition": "equals", "emailValue": "={{$json[\"email\"]}}"}}}, "resource": "databasePage", "operation": "getAll", "returnAll": true, "databaseId": "27a30c5b-c418-4200-8f48-d7fb7b043fbe"}, "credentials": {"notionApi": "Oasis Hub Production"}, "typeVersion": 1, "alwaysOutputData": true}, {"name": "Select Semester ID", "type": "n8n-nodes-base.set", "position": [1020, -180], "parameters": {"values": {"string": [{"name": "semesterID", "value": "={{$json[\"id\"]}}"}]}, "options": {}, "keepOnlySet": true}, "executeOnce": true, "typeVersion": 1}, {"name": "Update Project Relation", "type": "n8n-nodes-base.notion", "position": [3240, 440], "parameters": {"pageId": "={{$json[\"id\"]}}", "resource": "databasePage", "operation": "update", "propertiesUi": {"propertyValues": [{"key": "Project|relation", "relationValue": ["={{$json[\"allProjectIDs\"].join(\",\")}}"]}]}}, "credentials": {"notionApi": "Oasis Hub Production"}, "typeVersion": 1}, {"name": "Merge3", "type": "n8n-nodes-base.merge", "position": [2750, 440], "parameters": {"mode": "multiplex"}, "typeVersion": 1}, {"name": "Concatenate SemesterIDs", "type": "n8n-nodes-base.function", "position": [3010, -160], "parameters": {"functionCode": "for (item of items) {\n  // Get the current semester ID\n  const currentSemesterID = item.json[\"semesterID\"]\n  let allSemesterIDs = [currentSemesterID];\n\n  // Add semesters that the user is already associated with\n  if (item.json[\"Semesters\"]?.length > 0) {\n    allSemesterIDs = allSemesterIDs.concat(item.json[\"Semesters\"].filter(semesterID => semesterID !== currentSemesterID));\n  }\n\n  // Set allSemesterIDs which is used to update the relation\n  item.json[\"allSemesterIDs\"] = allSemesterIDs\n}\n\nreturn items;\n"}, "typeVersion": 1}, {"name": "Concatenate ProjectIDs", "type": "n8n-nodes-base.function", "position": [3000, 440], "parameters": {"functionCode": "for (item of items) {\n  // Get the project id for the new project\n  const newProjectID = item.json[\"projectID\"]\n  let allProjectIDs = [newProjectID];\n\n  // Add projects that the user already has\n  if (item.json[\"Project\"]?.length > 0) {\n    allWorkspaceIDs = allWorkspaceIDs.concat(item.json[\"Project\"].filter(projectID => projectID !== newProjectID));\n  }\n\n  // Set allProjectIDs which is used to update the relation\n  item.json[\"allProjectIDs\"] = allProjectIDs\n}\n\nreturn items;\n"}, "typeVersion": 1}, {"name": "Merge4", "type": "n8n-nodes-base.merge", "position": [2240, 170], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON> Email", "type": "n8n-nodes-base.set", "position": [2060, 270], "parameters": {"values": {"string": [{"name": "email", "value": "={{$json[\"Email\"]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Team Creation", "type": "n8n-nodes-base.webhook", "notes": "Example Input Data:\n{\"projectIdea\":\"A hub for all things Oasis\",\"projectName\":\"Oasis Hub\",\"teamMembers\":[{\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]}", "position": [460, 150], "webhookId": "6f000a46-9bbf-4e1c-8e11-b64d9b8c8fb7", "parameters": {"path": "team-create", "options": {"responseData": ""}, "httpMethod": "POST", "authentication": "basicAuth"}, "credentials": {"httpBasicAuth": "Oasis Basic Auth Creds"}, "notesInFlow": true, "typeVersion": 1}], "connections": {"Merge": {"main": [[{"node": "Use Default Name if Not Specified", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "If user exists", "type": "main", "index": 0}]]}, "Merge2": {"main": [[{"node": "Concatenate SemesterIDs", "type": "main", "index": 0}]]}, "Merge3": {"main": [[{"node": "Concatenate ProjectIDs", "type": "main", "index": 0}]]}, "Merge4": {"main": [[{"node": "Query User", "type": "main", "index": 0}]]}, "Set Email": {"main": [[{"node": "Merge4", "type": "main", "index": 1}]]}, "Query User": {"main": [[{"node": "Merge2", "type": "main", "index": 1}, {"node": "Merge3", "type": "main", "index": 0}]]}, "Create User": {"main": [[{"node": "<PERSON> Email", "type": "main", "index": 0}]]}, "Team Creation": {"main": [[{"node": "Get Project Name & Idea", "type": "main", "index": 0}, {"node": "Get Team Members", "type": "main", "index": 0}, {"node": "Query Current Semester", "type": "main", "index": 0}]]}, "Create Project": {"main": [[{"node": "Select Project Showcase ID", "type": "main", "index": 0}]]}, "If user exists": {"main": [[{"node": "Merge4", "type": "main", "index": 0}], [{"node": "Create User", "type": "main", "index": 0}]]}, "Query for User": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Get Team Members": {"main": [[{"node": "Query for User", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 0}]]}, "Select Semester ID": {"main": [[{"node": "Merge2", "type": "main", "index": 0}]]}, "Concatenate ProjectIDs": {"main": [[{"node": "Update Project Relation", "type": "main", "index": 0}]]}, "Query Current Semester": {"main": [[{"node": "Select Semester ID and Projects Count", "type": "main", "index": 0}, {"node": "Select Semester ID", "type": "main", "index": 0}]]}, "Concatenate SemesterIDs": {"main": [[{"node": "Update Semester for User", "type": "main", "index": 0}]]}, "Get Project Name & Idea": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Select Project Showcase ID": {"main": [[{"node": "Merge3", "type": "main", "index": 1}]]}, "Use Default Name if Not Specified": {"main": [[{"node": "Create Project", "type": "main", "index": 0}]]}, "Select Semester ID and Projects Count": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}}