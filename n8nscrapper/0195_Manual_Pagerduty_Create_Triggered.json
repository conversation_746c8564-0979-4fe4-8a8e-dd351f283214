{"id": "158", "name": "Create, update, and get an incident on PagerDuty", "nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [240, 260], "parameters": {}, "typeVersion": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.pagerDuty", "position": [440, 260], "parameters": {"email": "", "title": "Firewall on Fire", "serviceId": "", "additionalFields": {}}, "credentials": {"pagerDutyApi": ""}, "typeVersion": 1}, {"name": "PagerDuty2", "type": "n8n-nodes-base.pagerDuty", "position": [840, 260], "parameters": {"operation": "get", "incidentId": "={{$node[\"PagerDuty1\"].json[\"id\"]}}"}, "credentials": {"pagerDutyApi": ""}, "typeVersion": 1}, {"name": "PagerDuty1", "type": "n8n-nodes-base.pagerDuty", "position": [640, 260], "parameters": {"email": "={{$node[\"PagerDuty\"].parameter[\"email\"]}}", "operation": "update", "incidentId": "={{$node[\"PagerDuty\"].json[\"id\"]}}", "updateFields": {"title": "Firewalls on Fire"}}, "credentials": {"pagerDutyApi": ""}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"PagerDuty": {"main": [[{"node": "PagerDuty1", "type": "main", "index": 0}]]}, "PagerDuty1": {"main": [[{"node": "PagerDuty2", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}}