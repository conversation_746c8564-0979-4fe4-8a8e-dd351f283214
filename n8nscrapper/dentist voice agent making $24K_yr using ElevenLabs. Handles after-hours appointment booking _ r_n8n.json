{"name": "The Recap AI - Dentist Voice Agent", "nodes": [{"parameters": {"httpMethod": "POST", "path": "4fe15a31-6365-4b96-a3d5-3b02bbe3d31a", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "id": "a352b87c-e6b9-45b2-bdf8-78d892543168", "name": "webhook_trigger", "webhookId": "4fe15a31-6365-4b96-a3d5-3b02bbe3d31a"}, {"parameters": {"resource": "calendar", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "timeMin": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start_Time', `The start timestamp for the dental appointment.`, 'string') }}", "timeMax": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End_Time', `The end time will always be the Start Time plus 1 hour.`, 'string') }}", "options": {"timezone": {"__rl": true, "value": "America/Chicago", "mode": "list", "cachedResultName": "America/Chicago"}}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [496, 288], "id": "5b2a46d3-2444-4dce-b2de-1f6526b761b3", "name": "get_availability", "credentials": {"googleCalendarOAuth2Api": {"id": "TG3WC49qzJvApajF", "name": "Lucas Google Calendar"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [336, 288], "id": "82d08e14-44d7-45b0-be6b-b4736cf5f8be", "name": "think"}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "start": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Start', `Start time for the dental appointment. You should assume this is a central timezone timestamp (CST).`, 'string') }}", "end": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('End', `End time for the dental appointment. This should be 1 hour AFTER the start time. This will be in the Central Time Zone (CST).`, 'string') }}", "additionalFields": {"summary": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Summary', 'The title/summary of this event should be in the format of \"Dental Appointment | {patient_name}\" where `patient_name` is the provided name of the patient.', 'string') }}"}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [656, 288], "id": "05ece507-6dea-4f3a-9a3b-aed813319118", "name": "create_appointment", "credentials": {"googleCalendarOAuth2Api": {"id": "TG3WC49qzJvApajF", "name": "Lucas Google Calendar"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [816, 0], "id": "cb872431-a32d-4098-b112-776cab3b4a2b", "name": "respond_to_webhook"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [0, 288], "id": "a9f97d50-49d0-4b5e-a35f-3d16a205ad17", "name": "gemini-2.5-flash", "credentials": {"googlePalmApi": {"id": "qp1NlMiIctmGD0Uu", "name": "Google Gemini (PaLM)"}}}, {"parameters": {"promptType": "define", "text": "=## Request Data\n\n{{ JSON.stringify($json.body, null, 2)}}\n", "options": {"systemMessage": "=## Role\n\nYou are an AI agent responsible for handling back office operations at the dental office \"Pearly Whites Dental\". You must make a decision about the data you receive and make a call into the appropriate tool in order to process this request and return the appropriate data necessary.\n\nYou should look at the provided `tool` value in the request body to help decided which tool to use.\n\nPay close attention to the constraints for number of times a tool is able to be used.\n\nYou have secure access to the following internal tools:\n  - `think` → you MUST use this to think carefully about how to handle the provided request data. This tool must be used on every turn and tool call interaction.\n  - `get_availability` → returns true/false availability on the Dental Office Calendar for the given start timestamp in CST (Central Time). **For availability requests, you MUST call this tool multiple times to find AT LEAST 2 available timeslots if they exist.** Matches the `get_availability` tool value included in the request.\n  - `create_appointment` → creates a 1-hour appointment event for the provided start time. This tool may only be called ONCE (1 time) in a given request. Do NOT use this tool multiple times. Matches the `create_appointment` tool value included in the request. If you use this tool more than once, your task will be considered a FAILURE.\n  - `log_patient_details` → logs the provided call details and patient details to a Google Sheet. This should ONL<PERSON> be called and used once for a provided request since we are logging details ONCE per call/patient. In order to use this tool, you need to be given the patient name / insurance provider / optional questions and concerns - if you don't have this information, you should NOT use this tool. This will be used only at the very end of the call when all details are provided. Matches the `log_patient_details` tool value included in the request. If you use this tool more than once, your task will be considered a FAILURE.\n\n\n## Special Instructions for get_availability Tool\n\nWhen handling availability requests:\n\n1. **Always aim to return 2 available timeslots** when possible\n2. **Call get_availability multiple times** to check different time slots on the requested date\n3. **Search strategy:**\n   - Start with the requested time (if provided)\n   - If that's not available, check nearby times in 30-minute or 1-hour increments\n   - Check both earlier and later times on the same day\n   - Continue checking until you find 2 available slots OR exhaust reasonable options\n\n4. **Response format:** Return an array of available timeslots in ISO format (Central Time Zone CST):\n   ```json\n   {\n     \"availableSlots\": [\n       \"2024-01-15T14:00:00Z\",\n       \"2024-01-15T16:00:00Z\"\n     ]\n   }\n   ```\n\n5. **If fewer than 2 slots are found:**\n   - Return whatever available slots you found (even if just 1)\n   - It's better to return 1 slot than none\n\n6. **Time checking sequence example:**\n   - If user requests \"2:00 PM on Tuesday\"\n   - Check: 2:00 PM, 1:30 PM, 2:30 PM, 1:00 PM, 3:00 PM, 12:30 PM, 3:30 PM, etc.\n   - Stop when you have 2 available slots or have checked reasonable business hours\n\n7. **Business hours assumption:** \n   - Check times between 8:00 AM and 5:00 PM unless specified otherwise\n   - Skip lunch hour (12:00-1:00 PM) if applicable\n\nRemember: The get_availability tool can be called multiple times for availability requests, but create_appointment and log_patient_details must only be called ONCE per request.\n\nRemember: All times are in CST (Central Time Zone)"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [304, 0], "id": "2ecea651-1cca-4eef-8772-3ecfe9ef3a5f", "name": "dental_agent"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1D9WO3JbzZ472pDvR0GGKDm5Jz8KkTpg_EtSgK9yVZtg", "mode": "list", "cachedResultName": "<PERSON>y Whites Dental Appointments", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D9WO3JbzZ472pDvR0GGKDm5Jz8KkTpg_EtSgK9yVZtg/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Appointments", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1D9WO3JbzZ472pDvR0GGKDm5Jz8KkTpg_EtSgK9yVZtg/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Call Timestamp": "={{ $now.toString() }}", "Patient Name": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Patient_Name', `Name of the patient booking their appointment.`, 'string') }}", "Insurance Provider": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Insurance_Provider', `Name of the insurance provider.`, 'string') }}", "Questions & Concerns": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Questions___Concerns', `Optional questions or concerns raised by the patient booking the appointment.`, 'string') }}", "Appointment Timestamp": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Appointment_Timestamp', `Timestamp the appointment is booked for.`, 'string') }}"}, "matchingColumns": ["Patient Name"], "schema": [{"id": "Call Timestamp", "displayName": "Call Timestamp", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Patient Name", "displayName": "Patient Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Insurance Provider", "displayName": "Insurance Provider", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Questions & Concerns", "displayName": "Questions & Concerns", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Appointment Timestamp", "displayName": "Appointment Timestamp", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheetsTool", "typeVersion": 4.6, "position": [816, 288], "id": "17d6dacb-b331-4def-8469-f11f0f59234c", "name": "log_patient_details", "credentials": {"googleSheetsOAuth2Api": {"id": "C8jshnaDinQz6p7c", "name": "Google Sheets"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('webhook_trigger').item.json.headers['cf-ray'] }}", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [176, 288], "id": "fc64c0e2-838c-43a8-91f3-dcf1c062c13d", "name": "simple-memory"}], "pinData": {}, "connections": {"webhook_trigger": {"main": [[{"node": "dental_agent", "type": "main", "index": 0}]]}, "get_availability": {"ai_tool": [[{"node": "dental_agent", "type": "ai_tool", "index": 0}]]}, "think": {"ai_tool": [[{"node": "dental_agent", "type": "ai_tool", "index": 0}]]}, "create_appointment": {"ai_tool": [[{"node": "dental_agent", "type": "ai_tool", "index": 0}]]}, "gemini-2.5-flash": {"ai_languageModel": [[{"node": "dental_agent", "type": "ai_languageModel", "index": 0}]]}, "dental_agent": {"main": [[{"node": "respond_to_webhook", "type": "main", "index": 0}]]}, "log_patient_details": {"ai_tool": [[{"node": "dental_agent", "type": "ai_tool", "index": 0}]]}, "simple-memory": {"ai_memory": [[{"node": "dental_agent", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "63c2c27d-3110-43f8-a3cc-94d3928551ea", "meta": {"templateCredsSetupCompleted": true, "instanceId": "06e5009344f682419c20ccd4ecdcb5223bbb91761882af93ac6d468dbc2cbf8d"}, "id": "r2cQWoLPpK7BNtlA", "tags": []}