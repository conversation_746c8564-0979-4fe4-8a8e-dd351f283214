{"name": "E-Mail Workflow Human in the Loop", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "simple": false, "filters": {}, "options": {}}, "type": "n8n-nodes-base.gmailTrigger", "typeVersion": 1.2, "position": [-360, -80], "id": "b1df57d9-4cb6-43de-ae70-8ceff96507c3", "name": "<PERSON><PERSON>", "credentials": {"gmailOAuth2": {"id": "K2I0dGADm08eTOwt", "name": "Gmail account niklas.fastlane"}}}, {"parameters": {"operation": "sendAndWait", "chatId": "**********", "message": "=Hier ist eine E-Mail von: {{ $json.headers.from }}\n\nHier ist der Betreff: {{ $json.subject }}\n\nHier ist der Text: {{ $json.text }}", "responseType": "customForm", "formFields": {"values": [{"fieldLabel": "E-Mail selbst beantworten?", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "<PERSON>a"}, {"option": "<PERSON><PERSON>"}, {"option": "<PERSON>ch möchte ein <PERSON> erstellen"}, {"option": "Ich möchte direkt antworten (lossenden)"}]}, "multiselect": true}]}, "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-80, -80], "id": "c4ef351b-3eec-4b21-addd-9851a712ec6b", "name": "Telegram", "webhookId": "175f3509-dfaf-492c-8d04-17eff6d60a26", "credentials": {"telegramApi": {"id": "mCo7SkagFQ9iMYiT", "name": "HUMAN IN THE LOOP BOT"}}}, {"parameters": {"promptType": "define", "text": "=Hier sind die Informationen über die E-Mail um die es geht:\nAbsender:  {{ $('Gmail Trigger').item.json.to.value[0].address }}\n\nBetreff: {{ $('Gmail Trigger').item.json.subject }}\n\nText:\n{{ $('Gmail Trigger').item.json.text }}\n\nDas hat der Benutzer zu der Frage gestellt, ob er die E-MAil als Draft beantworten sollte, oder direkt abgesendet werden soll:\n\n{{ $json.data['E-Mail selbst beantworten?'][1] }}\n\n", "hasOutputParser": true, "options": {"systemMessage": "=\n## Rolle\n\nDu bist ein E-Mail-Antwort-Agent für Niklas Bechtel. Deine Aufgabe ist es, auf eingegangene E-Mails professionell zu antworten und dabei herauszufinden, ob der Absender Interesse an einem der Produkte von Niklas hat.\n\n## Ziel\n\n- Antworte auf eingehende E-Mails.\n- Die Formulierungen, Tonalität und Ansprache (Du/Sie) richtest du ausschließlich nach den Informationen aus dem Tool **„Schreibstil/Zielgruppe (Vector Store)“** aus.\n\n## Tools\n\n1. **Schreibstil/Zielgruppe (Vector Store)**  \n   Enthält alle Informationen zur Zielgruppe und zum Sprachstil.  \n   **MUSS vor jeder E-Mail aktiviert und vollständig gelesen werden.**  \n   Dient als alleinige Grundlage für Tonalität, Argumentation und Ausdrucksweise.\n\n2. **antwort_lossenden**  \n   Sendet die generierte E-Mail direkt an den Empfänger.\n\n3. **create_draft**  \n   Speichert die Antwort als Entwurf.\n\nDU AKTIVIERST NUR EINES VON BEIDEN. JENACHDEM OB DER BENUTZER SELBER LOSSCHICKEN MÖCHTE, ODER NICHT\n\n## SOP (Standard Operating Procedure)\n\n1. Lies die empfangene Ursprungsnachricht.\n2. Aktiviere und lies den Vector Store **Schreibstil/Zielgruppe** vollständig.\n3. Verfasse eine professionelle E-Mail-Antwort, die den Dialog fortsetzt.\n4. Nutze je nach Fall:\n   - `antwort_lossenden` → zum direkten Versenden\n   - `create_draft` → zum Speichern als Entwurf\n5. Gib IMMER beide Blöcke aus:\n\n\n{\n  \"antwort\": \"[GENERIERTE ANTWORT-E-MAIL ALS VOLLSTÄNDIGER TEXT]\",\n  \"ursprungsnachricht\": \"[TEXT DER EMPFANGENEN E-MAIL]\",\n}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [400, -80], "id": "6dc0177c-182d-442d-8d06-fea4de497bef", "name": "AI Agent"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1361852c-c1fa-48b5-a887-68405231f3cc", "leftValue": "={{ $json.data['E-Mail selbst beantworten?'][0] }}", "rightValue": "<PERSON>a", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [180, -80], "id": "3087e918-a804-4fe5-a662-62da5b69514d", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [880, -360], "id": "584ffa25-663e-4d5a-806e-1aef0801a093", "name": "No Operation, do nothing"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [200, 140], "id": "2465b5ff-3eb9-482f-937f-c4c3d4b353b3", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "h9Zx5W04F2phP2kY", "name": "OpenAi account AKTUELL"}}}, {"parameters": {"operation": "mark<PERSON><PERSON>n<PERSON>", "messageId": "={{ $('Gmail Trigger').item.json.id }}"}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [520, -360], "id": "3b4b045d-a60f-4c88-bade-ecf64237ab73", "name": "Gmail2", "webhookId": "68401e8a-303b-47c1-acd2-09f303ece55b", "credentials": {"gmailOAuth2": {"id": "K2I0dGADm08eTOwt", "name": "Gmail account niklas.fastlane"}}}, {"parameters": {"resource": "draft", "subject": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Subject', ``, 'string') }}", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', `Always add benath each e-mail: the original sign off should be included AS WELL. (mit freundlichen Grü<PERSON>n,\nNiklas)\n\nNiklas Bechtel\nGeschäftsführer\nFastlane AI GmbH\nMobil: ‭+49 173 9039484‬\nMail: <EMAIL>\nfastlane-ai.de\n\n`, 'string') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [600, 200], "id": "06f1677a-ffae-46af-a49d-28dd7e854f80", "name": "create_draft", "webhookId": "b38f8d18-4913-40ca-b31a-3e4c04363202", "credentials": {"gmailOAuth2": {"id": "K2I0dGADm08eTOwt", "name": "Gmail account niklas.fastlane"}}}, {"parameters": {"operation": "reply", "messageId": "={{ $('Gmail Trigger').item.json.id }}", "emailType": "text", "message": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Message', `Always add benath each e-mail: the original sign off should be included AS WELL. (mit freundlichen Grü<PERSON>n,\nNiklas)\n\nNiklas Bechtel\nGeschäftsführer\nFastlane AI GmbH\nMobil: ‭+49 173 9039484‬\nMail: <EMAIL>\nfastlane-ai.de\n\n`, 'string') }}", "options": {}}, "type": "n8n-nodes-base.gmailTool", "typeVersion": 2.1, "position": [820, 200], "id": "7f19f9c0-9a00-4b8f-9f37-bc2a264a41c9", "name": "antwort_lossenden", "webhookId": "ea2472e5-f795-4a44-9fc5-518a3ac88b24", "credentials": {"gmailOAuth2": {"id": "K2I0dGADm08eTOwt", "name": "Gmail account niklas.fastlane"}}}, {"parameters": {"model": {"__rl": true, "value": "o3-mini", "mode": "list", "cachedResultName": "o3-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [500, 380], "id": "80fbfb3c-1e89-4a38-a0a5-a20f62c76ce9", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "h9Zx5W04F2phP2kY", "name": "OpenAi account AKTUELL"}}}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "emailagent", "mode": "list", "cachedResultName": "emailagent"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [180, 320], "id": "6ae5a887-2ae3-4b2b-883d-1543c029d89a", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "lbK8UH6njcpmrKl8", "name": "privat gmail pinecone"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [120, 480], "id": "1ae58858-8d2f-4524-a906-d1c059b6e3c2", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "h9Zx5W04F2phP2kY", "name": "OpenAi account AKTUELL"}}}, {"parameters": {"name": "email_data", "description": "Hier sind Informationen drin wie die Zielgruppe der E-Mails ist, und wie der Schreibstil der E-Mail sein soll.\n\n", "topK": 10}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [300, 140], "id": "8189f55e-6a41-4ccc-aba3-1f1c860a1417", "name": "Schreibstil/Zielgruppe"}, {"parameters": {"operation": "sendAndWait", "chatId": "**********", "message": "=Ich bin jetzt soweit, und habe die E-Mail erstellt.\n\nHier ist nochmal die Ursprungse-mail:\n{{ $json.output['ursprungse-mail'] }}\n\nUnd hier ist meine generierte Antwort:\n{{ $json.output['Antwort E-Mail'] }}\n\nMöchtest du, dass ich die Email in der Datenbank abspeicher, oder wollen wir die E-MAil nochmal für die Datenbank bearbeiten?", "responseType": "customForm", "formFields": {"values": [{"fieldLabel": "Möchtest du, dass ich die E-Mail in der Musterdatenbank  für gite E-Mails abspeichereP", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "<PERSON>a"}, {"option": "<PERSON><PERSON>"}]}, "requiredField": true}]}, "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [760, -80], "id": "40e3201d-6904-4729-aed1-809670cb6129", "name": "Telegram1", "webhookId": "462fce1d-e2d4-4509-aedc-659293fbab7a", "credentials": {"telegramApi": {"id": "mCo7SkagFQ9iMYiT", "name": "HUMAN IN THE LOOP BOT"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"ursprungse-mail\": \"<PERSON><PERSON><PERSON> Tag, ich interessiere mich für Ihr Produkt und hätte dazu einige Fragen.\",\n  \"Antwort E-Mail\": \"Hallo, vielen Dank für Ihre Anfrage. Gerne beantworte ich Ihre Fragen wie folgt: ...\"\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [600, -200], "id": "a202277e-7a04-4678-9ac0-975e528f96fa", "name": "Structured Output Parser"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b9e29201-b836-45b5-b447-4dddd3ccfb1e", "leftValue": "={{ $json.data['Möchtest du, dass ich die E-Mail in der Musterdatenbank  für gite E-Mails abspeichereP'] }}", "rightValue": "<PERSON>a", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [960, -80], "id": "8debb884-7571-43c4-a4fb-3b06487d908e", "name": "If1"}, {"parameters": {"folderId": "1xQUFtfKt4XO2sQUF58_NZk-yVN11REOa", "title": "Ergänzung"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1320, -260], "id": "502e8316-bda8-4299-a42f-f9116a63ab36", "name": "Google Docs", "credentials": {"googleDocsOAuth2Api": {"id": "Iwjza7ZYKrL85h9A", "name": "infolfastlane Docs"}}}, {"parameters": {"operation": "update", "documentURL": "={{ $json.id }}", "actionsUi": {"actionFields": [{"action": "insert", "text": "=Hier ist die Ursprungse-mail: \n{{ $('AI Agent').item.json.output['Antwort E-Mail'] }}\n\nHier ist eine Musterantwort darauf\n"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [1500, -240], "id": "e20e5bd2-04ff-41eb-94bc-3195f09fe1e9", "name": "Google Docs1", "credentials": {"googleDocsOAuth2Api": {"id": "Iwjza7ZYKrL85h9A", "name": "infolfastlane Docs"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.documentId }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "application/pdf"}}}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1680, -240], "id": "c2100348-79c1-49bf-aff0-2b324205b3fe", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "I4U0JX88MEnkQYLn", "name": "Fatlane SELF-HOSTED"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "emailagent", "mode": "list", "cachedResultName": "emailagent"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.1, "position": [2040, -220], "id": "f5af3237-a9d0-4a38-856c-38242ceac772", "name": "Pinecone Vector Store1", "credentials": {"pineconeApi": {"id": "lbK8UH6njcpmrKl8", "name": "privat gmail pinecone"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2220, 40], "id": "77cca23a-e169-436c-a469-c1978927eacc", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "h9Zx5W04F2phP2kY", "name": "OpenAi account AKTUELL"}}}, {"parameters": {"dataType": "binary", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2660, -20], "id": "15417ba0-ff1c-4ba9-afa7-599cf69e7f1c", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [2240, 140], "id": "351f355a-3930-4736-a694-7198a3eeb687", "name": "Recursive Character Text Splitter"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1040, 160], "id": "166305ac-9ee4-41d4-9d09-2e25f82a3878", "name": "No Operation, do nothing1"}, {"parameters": {"chatId": "**********", "text": "Die E-Mail wurde erfolgreich in der Datanbank abgespeichert. Vielen Dankl für deine Energie Niklas", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2740, -200], "id": "248cffc0-8eca-4197-9a9c-0a568ee44e14", "name": "Telegram2", "webhookId": "871b9870-ba7f-41b3-97d2-8ab5317f7e11", "credentials": {"telegramApi": {"id": "mCo7SkagFQ9iMYiT", "name": "HUMAN IN THE LOOP BOT"}}}], "pinData": {}, "connections": {"Gmail Trigger": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Gmail2", "type": "main", "index": 0}], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Gmail2": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}, {"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "create_draft": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "antwort_lossenden": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Schreibstil/Zielgruppe", "type": "ai_languageModel", "index": 0}]]}, "Pinecone Vector Store": {"ai_vectorStore": [[{"node": "Schreibstil/Zielgruppe", "type": "ai_vectorStore", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Schreibstil/Zielgruppe": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Telegram1", "type": "main", "index": 0}]]}, "Telegram1": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "If1": {"main": [[{"node": "Google Docs", "type": "main", "index": 0}], [{"node": "No Operation, do nothing1", "type": "main", "index": 0}]]}, "Google Docs": {"main": [[{"node": "Google Docs1", "type": "main", "index": 0}]]}, "Google Docs1": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Pinecone Vector Store1", "type": "main", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Pinecone Vector Store1", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store1", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Pinecone Vector Store1": {"main": [[{"node": "Telegram2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9d55b566-7532-4361-90f4-a39a7d32325b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "4859b1835e199ab2a6c3412de22db564b818e1d0ef1418c63000d7155a3647fd"}, "id": "Ctqrgrx3zDcXsJDX", "tags": []}