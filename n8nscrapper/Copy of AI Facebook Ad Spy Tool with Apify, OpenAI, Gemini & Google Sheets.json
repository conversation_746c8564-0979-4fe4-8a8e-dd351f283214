{"name": "AI Facebook Ad Spy Tool with Apify, OpenAI, Gemini & Google Sheets", "nodes": [{"parameters": {"content": "## 🕵️ STEP 1: Facebook Ad Library Scraping\n\nThis workflow extracts competitor ads from Facebook's public ad library:\n\n1. **Run Ad Library Scraper**: Uses Apify to scrape active Facebook ads based on search terms\n2. **Filter For Likes**: Filters out low-quality advertisers (less than 1000 page likes)\n3. **Switch**: Routes ads to different processing paths based on content type\n\n**Setup**: Replace <your-apify-api-key-here> with actual Apify API key and customize search terms", "height": 200, "width": 380}, "id": "sticky-note-1", "type": "n8n-nodes-base.stickyNote", "position": [500, 400], "typeVersion": 1, "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## 📹 STEP 2: Video Ad Processing Pipeline\n\nFor video ads, this complex pipeline analyzes content:\n\n1. **Download Video**: Gets video file from Facebook\n2. **Upload to Drive**: Stores video for Gemini processing\n3. **Begin Gemini Session**: Initiates video upload to Gemini AI\n4. **Upload to Gemini**: Transfers video for analysis\n5. **Analyze Video**: Uses Gemini's video understanding to describe content\n6. **Generate Summary**: Creates competitor analysis and rewritten ad copy\n\n**Note**: Video processing requires Gemini API for multi-modal analysis", "height": 240, "width": 420}, "id": "sticky-note-2", "type": "n8n-nodes-base.stickyNote", "position": [1500, 0], "typeVersion": 1, "name": "Sticky Note1"}, {"parameters": {"content": "## 🖼️ STEP 3: Image & Text Ad Processing\n\n**Image Route:**\n• **Analyze Image**: Uses GPT-4 Vision to describe image content\n• **Output Summary**: Generates competitor analysis with image insights\n• **Add as Type = Image**: Stores results with image prompt for recreation\n\n**Text Route:**\n• **Output Text Summary**: Analyzes text-only ads for strategy and messaging\n• **Add as Type = Text**: Stores analysis and rewritten copy\n\n**Result**: Comprehensive competitor intelligence database with recreation prompts", "height": 220, "width": 400}, "id": "sticky-note-3", "type": "n8n-nodes-base.stickyNote", "position": [1500, 700], "typeVersion": 1, "name": "Sticky Note2"}, {"parameters": {"content": "## 💰 BUSINESS VALUE & APPLICATIONS\n\n**Competitive Intelligence:**\n• Analyze competitor ad strategies across text, image, and video formats\n• Track messaging evolution and creative approaches\n• Identify high-performing advertisers (filtered by page likes)\n\n**Revenue Opportunities:**\n• Sell as spy tool service for $2,000+ to marketing agencies\n• Offer competitor analysis reports to PPC clients\n• Use insights for your own ad campaign optimization\n\n**Scalable System**: Process hundreds of ads automatically with detailed analysis", "height": 220, "width": 400}, "id": "sticky-note-4", "type": "n8n-nodes-base.stickyNote", "position": [2300, 400], "typeVersion": 1, "name": "Sticky Note3"}, {"parameters": {}, "id": "18536cf3-1626-44ee-95d5-6eb28baa712b", "name": "When clicking ‘Execute workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [400, 660], "typeVersion": 1}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "9dbbe6e7-fd80-44ab-88f0-ab438cfd997e", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.snapshot.videos[0].video_sd_url }}", "rightValue": "=https://video-"}]}, "renameOutput": true, "outputKey": "Video"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0b7cee75-ca57-4946-971e-1f526b49324f", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.snapshot.images[0].original_image_url }}", "rightValue": "https://scontent-ho"}]}, "renameOutput": true, "outputKey": "Image"}]}, "options": {"fallbackOutput": "extra", "renameFallbackOutput": "Text"}}, "id": "e201ba62-cc45-45e6-b50c-08f913a27c87", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [1060, 660], "typeVersion": 3.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "c1d74591-13e8-4c79-9a34-117cc6816dfb", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.advertiser.ad_library_page_info.page_info.likes }}", "rightValue": 1000}]}, "options": {}}, "id": "ca9ee8b5-c562-4339-b6ad-6c7e8d31e4ef", "name": "Filter For Likes", "type": "n8n-nodes-base.filter", "position": [840, 660], "typeVersion": 2.2}, {"parameters": {"amount": 1}, "id": "abdf6521-2599-4741-9dab-f6ce60775ec7", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [2080, 960], "webhookId": "1cda7407-6321-4d8d-b532-19f42514eb63", "typeVersion": 1.1}, {"parameters": {"amount": 1}, "id": "56c2f9ea-5427-4cd1-a7ce-0f3b918960d2", "name": "Wait1", "type": "n8n-nodes-base.wait", "position": [2340, 660], "webhookId": "1cda7407-6321-4d8d-b532-19f42514eb63", "typeVersion": 1.1}, {"parameters": {"url": "={{ $json.snapshot.videos[0].video_sd_url }}", "options": {}}, "id": "1b41ce15-b4b1-4189-940e-f4d38062619e", "name": "Download Video", "type": "n8n-nodes-base.httpRequest", "position": [1580, 280], "typeVersion": 4.2}, {"parameters": {"name": "Example File", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "options": {}}, "id": "9a1730f8-c73c-4771-a718-a6729f68374c", "name": "Upload Video to Drive", "type": "n8n-nodes-base.googleDrive", "position": [1800, 280], "typeVersion": 3}, {"parameters": {"amount": 1}, "id": "decfca56-689f-4332-9813-00edf6ec00b5", "name": "Wait2", "type": "n8n-nodes-base.wait", "position": [3800, 360], "webhookId": "1cda7407-6321-4d8d-b532-19f42514eb63", "typeVersion": 1.1}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/XtaWFhbtfxyzqrFmd/run-sync-get-dataset-items", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <your<PERSON><PERSON><PERSON><PERSON>>"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"count\": 200,\n    \"period\": \"last7d\",\n    \"scrapeAdDetails\": true,\n    \"scrapePageAds.activeStatus\": \"active\",\n    \"urls\": [\n        {\n            \"url\": \"https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=US&is_targeted_country=false&media_type=all&q=%22ai%20automation%22&search_type=keyword_exact_phrase&start_date[min]=2025-06-12&start_date[max]\",\n            \"method\": \"GET\"\n        }\n    ]\n}", "options": {}}, "id": "6adbf90c-a132-4ef1-88f7-9ab798c955a3", "name": "Run Ad Library Scraper", "type": "n8n-nodes-base.httpRequest", "position": [620, 660], "typeVersion": 4.2}, {"parameters": {"options": {}}, "id": "1380cc04-7732-4070-8306-7c986c513ce7", "name": "Loop Over Image Ads", "type": "n8n-nodes-base.splitInBatches", "position": [1280, 660], "typeVersion": 3}, {"parameters": {"options": {}}, "id": "dafb1f75-ba45-4c08-8581-5e6554de9089", "name": "Loop Over Text Ads", "type": "n8n-nodes-base.splitInBatches", "position": [1280, 960], "typeVersion": 3}, {"parameters": {"options": {}}, "id": "66fc9090-11f2-4da1-bdec-9a24be1f5669", "name": "Loop Over Video Ads", "type": "n8n-nodes-base.splitInBatches", "position": [1280, 360], "typeVersion": 3}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/upload/v1beta/files", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "<yourApi<PERSON>ey>"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Goog-Upload-Protocol", "value": "resumable"}, {"name": "X-Goog-Upload-Command", "value": "start"}, {"name": "X-Goog-Upload-Header-Content-Length", "value": "={{ $json.size }}"}, {"name": "Content-Type", "value": "application/json"}]}, "options": {"response": {"response": {"fullResponse": true}}}}, "id": "2a753af7-acfd-4813-9fc7-1feeae3e38ec", "name": "Begin Gemini Upload Session", "type": "n8n-nodes-base.httpRequest", "position": [2000, 280], "typeVersion": 4.2}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "mode": "id", "value": "={{ $('Upload Video to Drive').item.json.id }}"}, "options": {}}, "id": "fed903ba-79f3-466f-b1e3-6cc72f05f341", "name": "Redownload Video", "type": "n8n-nodes-base.googleDrive", "position": [2220, 280], "typeVersion": 3}, {"parameters": {"method": "POST", "url": "={{ $json.headers['x-goog-upload-url'] }}", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "<yourApi<PERSON>ey>"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Length", "value": "={{ $('Upload Video to Drive').item.json.size }}"}, {"name": "X-Goog-Upload-Offset", "value": "0"}, {"name": "X-Goog-Upload-Command", "value": "upload, finalize"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "=data", "options": {}}, "id": "bf9a84ef-a025-4d08-bc70-0627c1e5f553", "name": "Upload Video to Gemini", "type": "n8n-nodes-base.httpRequest", "position": [2440, 280], "typeVersion": 4.2}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "<yourApi<PERSON>ey>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"Describe this video in excruciating detail. Do not output anything but the description of the video.\"\n        },\n        {\n          \"file_data\": {\n            \"mime_type\": \"{{ $json.file.mimeType }}\",\n            \"file_uri\": \"{{ $json.file.uri }}\"\n          }\n        }\n      ]\n    }\n  ]\n}", "options": {}}, "id": "9f389a74-0097-4e4f-847f-c80494d9b8a8", "name": "Analyze Video with Gemini", "type": "n8n-nodes-base.httpRequest", "position": [2920, 280], "retryOnFail": true, "typeVersion": 4.2, "waitBetweenTries": 15}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent advertisement analysis bot. You analyze advertisements.", "role": "system"}, {"content": "Your task is to take as input a scraped javascript object from an advertisement in the FB ad library, and then summarize it + spin (repurpose, rewrite) the ad copy. \n\nYou're doing this for strategic intelligence. We run an advertising agency and we're always looking at what our competitor advertisers are doing.\n\nOutput your results in this JSON format:\n\n{\"summary\":\"\",\"rewrittenAdCopy\":\"\"}\n\nRules:\n- The intended style and attitude is generally analytical, inquisitive, and precise, despite exploring complex topics, in the “classic style” of Western writing.\nthe level of formality should be inverse to the topic’s novelty: the weirder something is, the more formal. For ‘safer’ topics, one should cut loose with the humor, epigraphs, typographical stunts and experiments, etc.\n- Avoid hedging and qualifying, even at the risk of making overly-strong claims. It is a slippery slope.\n- Use casual abbreviations (like San Francisco -> SF, thanks -> thx, question -> q ), casual contractions (like \"I've\"), shortened forms of common nouns (like \"info\", \"vid\", \"app\") as this signals more human written speech. Do not use em dashes (eliminate — from your vocabulary completely!\n- No rhetorical questions.\n- Make your summary extremely comprehensive and analytical."}, {"content": "=JSON scrape:\n\n{{ $('Loop Over Video Ads').item.json.toJsonString() }}\n\nVideo description:\n\n{{ $json.candidates[0].content.parts[0].text }}"}]}, "jsonOutput": true, "options": {"temperature": 0.7}}, "id": "1528dc3d-fff4-452c-ad42-2d2e9bdf1f4e", "name": "Output Video Summary", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [3200, 280], "typeVersion": 1.8}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": "1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit?usp=drivesdk", "cachedResultName": "Facebook Ad Library Analyzer DB"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit#gid=0", "cachedResultName": "Ads"}, "columns": {"value": {"type": "video", "page_id": "={{ $('Loop Over Video Ads').item.json.page_id }}", "summary": "={{ $json.message.content.summary }}", "page_url": "={{ $('Loop Over Video Ads').item.json.snapshot.page_profile_uri }}", "page_name": "={{ $('Loop Over Video Ads').item.json.snapshot.page_name }}", "date_added": "={{ $now }}", "image_prompt": "=", "video_prompt": "={{ $('Analyze Video with Gemini').item.json.candidates[0].content.parts[0].text }}", "ad_archive_id": "={{ $('Loop Over Video Ads').item.json.ad_archive_id }}", "rewritten_ad_copy": "={{ $json.message.content.rewrittenAdCopy }}"}, "schema": [{"id": "ad_archive_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ad_archive_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_added", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date_added", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "rewritten_ad_copy", "type": "string", "display": true, "removed": false, "required": false, "displayName": "rewritten_ad_copy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "image_prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "video_prompt", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "e3fc12fa-d845-435c-95e7-1fc121f0bb48", "name": "Add as Type = Video", "type": "n8n-nodes-base.googleSheets", "position": [3560, 280], "typeVersion": 4.6}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "GPT-4O"}, "text": "What's in this image? Be extremely comprehensive.", "imageUrls": "={{ $json.snapshot.images[0].original_image_url }}", "options": {}}, "id": "47291e4b-ae46-4bff-8134-8cf330cfb7aa", "name": "Analyze Image", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1580, 580], "typeVersion": 1.8}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent advertisement analysis bot. You analyze advertisements.", "role": "system"}, {"content": "Your task is to take as input a scraped javascript object from an advertisement in the FB ad library, and then summarize it + spin (repurpose, rewrite) the ad copy. \n\nYou're doing this for strategic intelligence. We run an advertising agency and we're always looking at what our competitor advertisers are doing.\n\nOutput your results in this JSON format:\n\n{\"summary\":\"\",\"rewrittenAdCopy\":\"\"}\n\nRules:\n- The intended style and attitude is generally analytical, inquisitive, and precise, despite exploring complex topics, in the “classic style” of Western writing.\nthe level of formality should be inverse to the topic’s novelty: the weirder something is, the more formal. For ‘safer’ topics, one should cut loose with the humor, epigraphs, typographical stunts and experiments, etc.\n- Avoid hedging and qualifying, even at the risk of making overly-strong claims. It is a slippery slope.\n- Use casual abbreviations (like San Francisco -> SF, thanks -> thx, question -> q ), casual contractions (like \"I've\"), shortened forms of common nouns (like \"info\", \"vid\", \"app\") as this signals more human written speech. Do not use em dashes (eliminate — from your vocabulary completely!\n- No rhetorical questions.\n- Make your summary extremely comprehensive and analytical."}, {"content": "=JSON scrape:\n\n{{ $('Loop Over Image Ads').item.json.toJsonString() }}\n\nImage description:\n\n{{ $json.content }}"}]}, "jsonOutput": true, "options": {"temperature": 0.7}}, "id": "34ee9e0e-4be8-41f2-b3fc-1868ec912fb8", "name": "Output Image Summary", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1780, 580], "typeVersion": 1.8}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": "1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit?usp=drivesdk", "cachedResultName": "Facebook Ad Library Analyzer DB"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit#gid=0", "cachedResultName": "Ads"}, "columns": {"value": {"type": "image", "page_id": "={{ $('Loop Over Image Ads').item.json.page_id }}", "summary": "={{ $json.message.content.summary }}", "page_url": "={{ $('Loop Over Image Ads').item.json.snapshot.page_profile_uri }}", "page_name": "={{ $('Loop Over Image Ads').item.json.snapshot.page_name }}", "date_added": "={{ $now }}", "image_prompt": "={{ $('Analyze Image').item.json.content }}", "ad_archive_id": "={{ $('Loop Over Image Ads').item.json.ad_archive_id }}", "rewritten_ad_copy": "={{ $json.message.content.rewrittenAdCopy }}"}, "schema": [{"id": "ad_archive_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ad_archive_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_added", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date_added", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "rewritten_ad_copy", "type": "string", "display": true, "removed": false, "required": false, "displayName": "rewritten_ad_copy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "image_prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "video_prompt", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "522169ec-669f-441a-9d20-3679426d0b9d", "name": "Add as Type = Image", "type": "n8n-nodes-base.googleSheets", "position": [2140, 580], "typeVersion": 4.6}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4.1", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "You're a helpful, intelligent advertisement analysis bot. You analyze advertisements.", "role": "system"}, {"content": "Your task is to take as input a scraped javascript object from an advertisement in the FB ad library, and then summarize it + spin (repurpose, rewrite) the ad copy. \n\nYou're doing this for strategic intelligence. We run an advertising agency and we're always looking at what our competitor advertisers are doing.\n\nOutput your results in this JSON format:\n\n{\"summary\":\"\",\"rewrittenAdCopy\":\"\"}\n\nRules:\n- The intended style and attitude is generally analytical, inquisitive, and precise, despite exploring complex topics, in the “classic style” of Western writing.\nthe level of formality should be inverse to the topic’s novelty: the weirder something is, the more formal. For ‘safer’ topics, one should cut loose with the humor, epigraphs, typographical stunts and experiments, etc.\n- Avoid hedging and qualifying, even at the risk of making overly-strong claims. It is a slippery slope.\n- Use casual abbreviations (like San Francisco -> SF, thanks -> thx, question -> q ), casual contractions (like \"I've\"), shortened forms of common nouns (like \"info\", \"vid\", \"app\") as this signals more human written speech. Do not use em dashes (eliminate — from your vocabulary completely!\n- No rhetorical questions.\n- Make your summary extremely comprehensive and analytical."}, {"content": "={{ $json.toJsonString() }}"}]}, "jsonOutput": true, "options": {"temperature": 0.7}}, "id": "f43bb15e-9c7d-4a97-9a10-7c9ce835f1f6", "name": "Output Text Summary", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1500, 880], "typeVersion": 1.8}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "list", "value": "1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit?usp=drivesdk", "cachedResultName": "Facebook Ad Library Analyzer DB"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1Cgm0DfKQxgtsjdw6h2LFVBtZnLAioTntzdXgfJCJIqo/edit#gid=0", "cachedResultName": "Ads"}, "columns": {"value": {"type": "text", "page_id": "={{ $('Loop Over Text Ads').item.json.page_id }}", "summary": "={{ $json.message.content.summary }}", "page_url": "={{ $('Loop Over Text Ads').item.json.snapshot.page_profile_uri }}", "page_name": "={{ $('Loop Over Text Ads').item.json.snapshot.page_name }}", "date_added": "={{ $now }}", "ad_archive_id": "={{ $('Loop Over Text Ads').item.json.ad_archive_id }}", "rewritten_ad_copy": "={{ $json.message.content.rewrittenAdCopy }}"}, "schema": [{"id": "ad_archive_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "ad_archive_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_id", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "type", "type": "string", "display": true, "removed": false, "required": false, "displayName": "type", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "date_added", "type": "string", "display": true, "removed": false, "required": false, "displayName": "date_added", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "page_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "page_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "rewritten_ad_copy", "type": "string", "display": true, "removed": false, "required": false, "displayName": "rewritten_ad_copy", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "image_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "image_prompt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "video_prompt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "video_prompt", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "62aec040-3729-447b-864d-00685857d721", "name": "Add as Type = Text", "type": "n8n-nodes-base.googleSheets", "position": [1860, 880], "typeVersion": 4.6}, {"parameters": {"content": "## AI Facebook Ad Spy Tool\n\nSteps:\n1. Add API key to \"Run Ad Library Scraper\" node\n2. Add filtering threshold in \"Filter For Likes\" node\n3. Add Gemini API key to \"Begin Gemini Upload Session\", \"Upload Video to Gemini\", and \"Analyze Video with Gemini\" nodes\n4. Adjust prompts to AI as needed and swap in your Google Sheet in the Google Sheets nodes.\n\n\n### Happy building!", "height": 260, "width": 700}, "id": "7079dc10-2ce5-4df9-889a-0e861f1025a5", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}, {"parameters": {"amount": 15}, "id": "33c52cea-4169-43b1-91e9-f8c6c35f06e1", "name": "Wait3", "type": "n8n-nodes-base.wait", "position": [2660, 280], "webhookId": "ac11c85a-5b73-4a29-9667-615e811a5ad8", "typeVersion": 1.1}], "pinData": {}, "connections": {"Wait": {"main": [[{"node": "Loop Over Text Ads", "type": "main", "index": 0}]]}, "Wait1": {"main": [[{"node": "Loop Over Image Ads", "type": "main", "index": 0}]]}, "Wait2": {"main": [[{"node": "Loop Over Video Ads", "type": "main", "index": 0}]]}, "Wait3": {"main": [[{"node": "Analyze Video with Gemini", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Loop Over Video Ads", "type": "main", "index": 0}], [{"node": "Loop Over Image Ads", "type": "main", "index": 0}], [{"node": "Loop Over Text Ads", "type": "main", "index": 0}]]}, "Analyze Image": {"main": [[{"node": "Output Image Summary", "type": "main", "index": 0}]]}, "Download Video": {"main": [[{"node": "Upload Video to Drive", "type": "main", "index": 0}]]}, "Filter For Likes": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Redownload Video": {"main": [[{"node": "Upload Video to Gemini", "type": "main", "index": 0}]]}, "Add as Type = Text": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Loop Over Text Ads": {"main": [[], [{"node": "Output Text Summary", "type": "main", "index": 0}]]}, "Add as Type = Image": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}, "Add as Type = Video": {"main": [[{"node": "Wait2", "type": "main", "index": 0}]]}, "Loop Over Image Ads": {"main": [[], [{"node": "Analyze Image", "type": "main", "index": 0}]]}, "Loop Over Video Ads": {"main": [[], [{"node": "Download Video", "type": "main", "index": 0}]]}, "Output Text Summary": {"main": [[{"node": "Add as Type = Text", "type": "main", "index": 0}]]}, "Output Image Summary": {"main": [[{"node": "Add as Type = Image", "type": "main", "index": 0}]]}, "Output Video Summary": {"main": [[{"node": "Add as Type = Video", "type": "main", "index": 0}]]}, "Upload Video to Drive": {"main": [[{"node": "Begin Gemini Upload Session", "type": "main", "index": 0}]]}, "Run Ad Library Scraper": {"main": [[{"node": "Filter For Likes", "type": "main", "index": 0}]]}, "Upload Video to Gemini": {"main": [[{"node": "Wait3", "type": "main", "index": 0}]]}, "Analyze Video with Gemini": {"main": [[{"node": "Output Video Summary", "type": "main", "index": 0}]]}, "Begin Gemini Upload Session": {"main": [[{"node": "Redownload Video", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Run Ad Library Scraper", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "48262a99-b107-4a8f-960a-a5671fa45b80", "meta": {"templateId": "5417", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "id": "2csrkdzRKCCwHAdh", "tags": []}