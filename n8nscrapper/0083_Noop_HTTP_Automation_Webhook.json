{"id": 11, "name": "Plex Automatic Throttler", "nodes": [{"name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [60, 440], "webhookId": "72a05ff6-05f5-4e7a-9eee-54a350bb6a47", "parameters": {"path": "72a05ff6-05f5-4e7a-9eee-54a350bb6a47", "options": {}, "httpMethod": "POST"}, "typeVersion": 1}, {"name": "Switch", "type": "n8n-nodes-base.switch", "position": [640, 580], "parameters": {"rules": {"rules": [{"value2": "media.resume", "operation": "contains"}, {"output": 1, "value2": "media.play", "operation": "contains"}, {"output": 2, "value2": "media.pause", "operation": "contains"}, {"output": 3, "value2": "media.stop", "operation": "contains"}]}, "value1": "={{$node[\"Webhook\"].json[\"body\"][\"payload\"]}}", "dataType": "string"}, "typeVersion": 1}, {"name": "Resume", "type": "n8n-nodes-base.noOp", "position": [860, 280], "parameters": {}, "typeVersion": 1}, {"name": "Check if Local", "type": "n8n-nodes-base.if", "position": [460, 440], "parameters": {"conditions": {"string": [{"value1": "={{$json[\"body\"][\"payload\"]}}", "value2": "\"local\":false", "operation": "contains"}]}}, "typeVersion": 1}, {"name": "Play", "type": "n8n-nodes-base.noOp", "position": [860, 440], "parameters": {}, "typeVersion": 1}, {"name": "Don't Do Anything", "type": "n8n-nodes-base.noOp", "position": [660, 220], "parameters": {}, "typeVersion": 1}, {"name": "Pause", "type": "n8n-nodes-base.noOp", "position": [860, 680], "parameters": {}, "typeVersion": 1}, {"name": "Stop", "type": "n8n-nodes-base.noOp", "position": [860, 840], "parameters": {}, "typeVersion": 1}, {"name": "Get QB Cookie", "type": "n8n-nodes-base.httpRequest", "position": [1260, 360], "parameters": {"url": "=http://{{$node[\"Global Variables\"].json[\"qbittorent\"][\"internalIP\"]}}:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}/api/v2/auth/login", "options": {"fullResponse": true}, "responseFormat": "string", "queryParametersUi": {"parameter": [{"name": "username", "value": "={{$node[\"Global Variables\"].json[\"qbittorent\"][\"username\"]}}"}, {"name": "password", "value": "={{$node[\"Global Variables\"].json[\"qbittorent\"][\"password\"]}}"}]}, "headerParametersUi": {"parameter": [{"name": "<PERSON><PERSON><PERSON>", "value": "=http://localhost:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}"}]}}, "typeVersion": 1}, {"name": "Get QB Cookie1", "type": "n8n-nodes-base.httpRequest", "position": [1260, 760], "parameters": {"url": "=http://{{$node[\"Global Variables\"].json[\"qbittorent\"][\"internalIP\"]}}:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}/api/v2/auth/login", "options": {"fullResponse": true}, "responseFormat": "string", "queryParametersUi": {"parameter": [{"name": "username", "value": "={{$node[\"Global Variables\"].json[\"qbittorent\"][\"username\"]}}"}, {"name": "password", "value": "={{$node[\"Global Variables\"].json[\"qbittorent\"][\"password\"]}}"}]}, "headerParametersUi": {"parameter": [{"name": "<PERSON><PERSON><PERSON>", "value": "=http://localhost:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}"}]}}, "typeVersion": 1}, {"name": "Global Variables", "type": "n8n-nodes-base.set", "position": [280, 440], "parameters": {"values": {"string": [{"name": "qbittorent.username", "value": "yourusername"}, {"name": "qbittorent.password", "value": "yourpassword"}, {"name": "qbittorent.internalIP", "value": "*************"}, {"name": "qbittorent.port", "value": "2020"}]}, "options": {}}, "typeVersion": 1}, {"name": "Check Throttle State", "type": "n8n-nodes-base.httpRequest", "position": [1460, 360], "parameters": {"url": "=http://{{$node[\"Global Variables\"].json[\"qbittorent\"][\"internalIP\"]}}:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}/api/v2/transfer/speedLimitsMode", "options": {"fullResponse": true}, "requestMethod": "POST", "queryParametersUi": {"parameter": [{"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}, "headerParametersUi": {"parameter": [{"name": "<PERSON><PERSON><PERSON>", "value": "=http://localhost:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}"}, {"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}}, "typeVersion": 1}, {"name": "Check if Throttled", "type": "n8n-nodes-base.if", "position": [1680, 360], "parameters": {"conditions": {"number": [{"value1": "={{$json[\"body\"]}}", "value2": 1, "operation": "equal"}], "string": []}}, "typeVersion": 1}, {"name": "Do Nothing", "type": "n8n-nodes-base.noOp", "position": [1900, 260], "parameters": {}, "typeVersion": 1}, {"name": "Check Throttle State2", "type": "n8n-nodes-base.httpRequest", "position": [1460, 760], "parameters": {"url": "=http://{{$node[\"Global Variables\"].json[\"qbittorent\"][\"internalIP\"]}}:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}/api/v2/transfer/speedLimitsMode", "options": {"fullResponse": true}, "requestMethod": "POST", "queryParametersUi": {"parameter": [{"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie1\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}, "headerParametersUi": {"parameter": [{"name": "<PERSON><PERSON><PERSON>", "value": "=http://localhost:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}"}, {"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie1\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}}, "typeVersion": 1}, {"name": "Check if Throttled1", "type": "n8n-nodes-base.if", "position": [1660, 760], "parameters": {"conditions": {"number": [{"value1": "={{$json[\"body\"]}}", "value2": 1, "operation": "equal"}], "string": []}}, "typeVersion": 1}, {"name": "Do Nothing1", "type": "n8n-nodes-base.noOp", "position": [1900, 860], "parameters": {}, "typeVersion": 1}, {"name": "Throttle Connection", "type": "n8n-nodes-base.noOp", "position": [1060, 360], "parameters": {}, "typeVersion": 1}, {"name": "Resume Downloads", "type": "n8n-nodes-base.noOp", "position": [1060, 760], "parameters": {}, "typeVersion": 1}, {"name": "Disable Throttle", "type": "n8n-nodes-base.httpRequest", "position": [1900, 660], "parameters": {"url": "=http://{{$node[\"Global Variables\"].json[\"qbittorent\"][\"internalIP\"]}}:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}/api/v2/transfer/toggleSpeedLimitsMode", "options": {}, "requestMethod": "POST", "queryParametersUi": {"parameter": [{"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie1\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}, "headerParametersUi": {"parameter": [{"name": "<PERSON><PERSON><PERSON>", "value": "=http://localhost:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}"}, {"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie1\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}}, "typeVersion": 1}, {"name": "Enable Throttle", "type": "n8n-nodes-base.httpRequest", "position": [1900, 440], "parameters": {"url": "=http://{{$node[\"Global Variables\"].json[\"qbittorent\"][\"internalIP\"]}}:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}/api/v2/transfer/toggleSpeedLimitsMode", "options": {}, "requestMethod": "POST", "queryParametersUi": {"parameter": [{"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}, "headerParametersUi": {"parameter": [{"name": "<PERSON><PERSON><PERSON>", "value": "=http://localhost:{{$node[\"Global Variables\"].json[\"qbittorent\"][\"port\"]}}"}, {"name": "<PERSON><PERSON>", "value": "={{$node[\"Get QB Cookie\"].json[\"headers\"][\"set-cookie\"][0].match(/[^;]*/).toString()}}"}]}}, "typeVersion": 1}], "active": true, "settings": {}, "connections": {"Play": {"main": [[{"node": "Throttle Connection", "type": "main", "index": 0}]]}, "Stop": {"main": [[{"node": "Resume Downloads", "type": "main", "index": 0}]]}, "Pause": {"main": [[{"node": "Resume Downloads", "type": "main", "index": 0}]]}, "Resume": {"main": [[{"node": "Throttle Connection", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Resume", "type": "main", "index": 0}], [{"node": "Play", "type": "main", "index": 0}], [{"node": "Pause", "type": "main", "index": 0}], [{"node": "Stop", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Global Variables", "type": "main", "index": 0}]]}, "Get QB Cookie": {"main": [[{"node": "Check Throttle State", "type": "main", "index": 0}]]}, "Check if Local": {"main": [[{"node": "Don't Do Anything", "type": "main", "index": 0}], [{"node": "Switch", "type": "main", "index": 0}]]}, "Get QB Cookie1": {"main": [[{"node": "Check Throttle State2", "type": "main", "index": 0}]]}, "Global Variables": {"main": [[{"node": "Check if Local", "type": "main", "index": 0}]]}, "Resume Downloads": {"main": [[{"node": "Get QB Cookie1", "type": "main", "index": 0}]]}, "Check if Throttled": {"main": [[{"node": "Do Nothing", "type": "main", "index": 0}], [{"node": "Enable Throttle", "type": "main", "index": 0}]]}, "Check if Throttled1": {"main": [[{"node": "Disable Throttle", "type": "main", "index": 0}], [{"node": "Do Nothing1", "type": "main", "index": 0}]]}, "Throttle Connection": {"main": [[{"node": "Get QB Cookie", "type": "main", "index": 0}]]}, "Check Throttle State": {"main": [[{"node": "Check if Throttled", "type": "main", "index": 0}]]}, "Check Throttle State2": {"main": [[{"node": "Check if Throttled1", "type": "main", "index": 0}]]}}}