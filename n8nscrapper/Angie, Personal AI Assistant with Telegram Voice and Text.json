{"meta": {"instanceId": "2723a3a635131edfcb16103f3d4dbaadf3658e386b4762989cbf49528dccbdbd"}, "nodes": [{"id": "c70236ea-91ab-4e47-b6f6-63a70ede5d3c", "name": "Google Calendar", "type": "n8n-nodes-base.googleCalendarTool", "position": [1000, 680], "parameters": {"options": {"fields": "=items(summary, start(dateTime))", "timeMin": "={{$fromAI(\"date\",\"the date after which to fetch the messages in format YYYY-MM-DDTHH:MM:SS\")}}"}, "calendar": {"__rl": true, "mode": "list", "value": "<EMAIL>", "cachedResultName": "<EMAIL>"}, "operation": "getAll"}, "credentials": {"googleCalendarOAuth2Api": {"id": "qx8JdPX4I5Xk9c46", "name": "Google Calendar account"}}, "typeVersion": 1.1}, {"id": "d2287bea-de47-4180-8ee6-55d4ab1a89da", "name": "Window Buffer Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [760, 680], "parameters": {"sessionKey": "={{ $('Listen for incoming events').first().json.message.from.id }}", "sessionIdType": "customKey"}, "typeVersion": 1.2}, {"id": "fa955731-86f6-4e4d-8604-dab5f52dee87", "name": "Get Email", "type": "n8n-nodes-base.gmailTool", "position": [880, 680], "parameters": {"filters": {"labelIds": ["INBOX", "UNREAD"], "readStatus": "unread", "receivedAfter": "={{$fromAI(\"date\",\"the date after which to fetch the messages in format YYYY-MM-DDTHH:MM:SS\")}}"}, "operation": "getAll"}, "credentials": {"gmailOAuth2": {"id": "tojOpzEqFprdxS46", "name": "Gmail account"}}, "typeVersion": 2.1}, {"id": "46511f47-1687-4cbe-ae41-ceb205ed1f11", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [640, 680], "parameters": {"model": "gpt-4o-mini", "options": {}}, "credentials": {"openAiApi": {"id": "5oYe8Cxj7liOPAKk", "name": "<PERSON>"}}, "typeVersion": 1}, {"id": "64fe44db-af19-43eb-9ff1-de0a72a9e645", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [-160, 360], "webhookId": "322dce18-f93e-4f86-b9b1-3305519b7834", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1}, {"id": "e35c04ff-a050-4564-8c1b-5b22b556872f", "name": "Telegram", "type": "n8n-nodes-base.telegram", "onError": "continueErrorOutput", "position": [1280, 360], "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "additionalFields": {"parse_mode": "<PERSON><PERSON>", "appendAttribution": false}}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "e791d4f8-2c19-4c14-a71e-39a04f22e944", "name": "If", "type": "n8n-nodes-base.if", "position": [200, 360], "parameters": {"options": {}, "conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "a0bf9719-4272-46f6-ab3b-eda6f7b44fd8", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": ""}]}}, "typeVersion": 2.2}, {"id": "5bd1788a-3d08-4eb3-8e03-3ce82f44d2a7", "name": "Speech to Text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [620, 360], "parameters": {"options": {}, "resource": "audio", "operation": "transcribe"}, "credentials": {"openAiApi": {"id": "5oYe8Cxj7liOPAKk", "name": "<PERSON>"}}, "typeVersion": 1.3}, {"id": "b67a2a93-517b-469e-aaa4-32c422710743", "name": "Voice or Text", "type": "n8n-nodes-base.set", "position": [40, 360], "parameters": {"fields": {"values": [{"name": "text", "stringValue": "={{ $json?.message?.text || \"\" }}"}]}, "options": {}}, "typeVersion": 3.2}, {"id": "8105c39f-9e87-44c4-9215-b3777f0b4164", "name": "Get Voice File", "type": "n8n-nodes-base.telegram", "position": [380, 360], "parameters": {"fileId": "={{ $('Listen for incoming events').item.json.message.voice.file_id }}", "resource": "file"}, "credentials": {"telegramApi": {"id": "Ov00cT0t4h4AFtZ0", "name": "Telegram account"}}, "typeVersion": 1.1}, {"id": "759b975f-d17c-4386-a5b3-12413f0361f4", "name": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "@n8n/n8n-nodes-langchain.agent", "position": [780, 360], "parameters": {"text": "={{ $json.text }}", "options": {"systemMessage": "=You are a helpful assistant.\n\nToday's date is {{ $now }}.\n\nGuidelines:\n- When fetching emails, filter out any promotional emails. \n- When summarizing emails, include Sender, Message date, subject, and brief summary of email.\n- if the user did not specify a date in the request assume they are asking for today\n- Use baserow tool to answer questions about tasks\n- When answering questions about calendar events, filter out events that don't apply to the question.  For example, the question is about events for today, only reply with events for today. Don't mention future events if it's more than 1 week away"}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "5537c777-f003-4673-b48a-4993a0c10520", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [20, 260], "parameters": {"color": 5, "width": 496.25, "height": 278.75, "content": "## Process Telegram Request\n"}, "typeVersion": 1}, {"id": "40e92679-b47a-4213-bb23-3f8d086459f2", "name": "Tasks", "type": "n8n-nodes-base.baserowTool", "position": [1120, 680], "parameters": {"tableId": 372174, "databaseId": 146496, "additionalOptions": {}}, "credentials": {"baserowApi": {"id": "jsgACn0VxAPoD0E2", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "570a0647-b571-4ebc-9dfe-40244b5a0b2a", "name": "Contacts", "type": "n8n-nodes-base.baserowTool", "position": [1240, 680], "parameters": {"tableId": 372177, "databaseId": 146496, "descriptionType": "manual", "toolDescription": "Useful for getting contact information.  For example emails or phone numbers.", "additionalOptions": {}}, "credentials": {"baserowApi": {"id": "jsgACn0VxAPoD0E2", "name": "Baserow account"}}, "typeVersion": 1}, {"id": "7fb1d95a-a8d6-4040-9271-5197296be7da", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-620, 220], "parameters": {"color": 5, "width": 386.*************, "height": 389.**************, "content": "## Start here: Step-by Step Youtube Tutorial :star:\n\n[![Building an AI Personal Assistant](https://img.youtube.com/vi/pXjowPc6V2s/sddefault.jpg)](https://youtu.be/pXjowPc6V2s)\n"}, "typeVersion": 1}], "pinData": {}, "connections": {"If": {"main": [[{"node": "Get Voice File", "type": "main", "index": 0}], [{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "main", "index": 0}]]}, "Tasks": {"ai_tool": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "ai_tool", "index": 0}]]}, "Contacts": {"ai_tool": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "ai_tool", "index": 0}]]}, "Get Email": {"ai_tool": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "ai_tool", "index": 0}]]}, "Voice or Text": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Get Voice File": {"main": [[{"node": "Speech to Text", "type": "main", "index": 0}]]}, "Speech to Text": {"main": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "main", "index": 0}]]}, "Google Calendar": {"ai_tool": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "ai_languageModel", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "<PERSON>, AI Assistant 👩🏻‍🏫", "type": "ai_memory", "index": 0}]]}, "Listen for incoming events": {"main": [[{"node": "Voice or Text", "type": "main", "index": 0}]]}, "Angie, AI Assistant 👩🏻‍🏫": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}}}