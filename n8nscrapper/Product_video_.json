{"name": "Product_video_", "nodes": [{"parameters": {"formTitle": "Go 2 Market", "formDescription": "Give us a product photo, title, and description, and we'll get back to you with professional marketing materials. ", "formFields": {"values": [{"fieldLabel": "Product Photo", "fieldType": "file", "multipleFiles": false, "requiredField": true}, {"fieldLabel": "Product Title", "placeholder": "Toothpaste", "requiredField": true}, {"fieldLabel": "Product Description", "requiredField": true}, {"fieldLabel": "Email", "fieldType": "email", "placeholder": "<EMAIL>", "requiredField": true}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [580, 100], "id": "ad2c396d-62fe-4969-918f-55defcf2d4ea", "name": "On form submission", "webhookId": "af13c24d-f792-46fb-ab9f-e1660bb6f068"}, {"parameters": {"content": "## Form Trigger", "height": 280, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, 0], "id": "8c8379da-a147-4f53-9edd-ac0c2b368bdf", "name": "<PERSON><PERSON>"}, {"parameters": {"inputDataFieldName": "Product_Photo", "name": "={{ $json['Product Title'] }} (Original)", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1b77GO5hLvZNoHbd68iw7dE7iKjhTiVpV", "mode": "list", "cachedResultName": "Product Creatives", "cachedResultUrl": "https://drive.google.com/drive/folders/1b77GO5hLvZNoHbd68iw7dE7iKjhTiVpV"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [820, 100], "id": "b4b23f26-c900-48a2-8c85-45d8c1cf2a77", "name": "Upload Photo"}, {"parameters": {"content": "## Upload File", "height": 280, "width": 180}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [780, 0], "id": "4cdefad1-66e3-46bb-9fad-46b90e5d090c", "name": "Sticky Note1"}, {"parameters": {"model": "openai/gpt-4.1", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1040, 180], "id": "02f0a18b-7e73-4989-80d3-539971b21e48", "name": "GPT 4.1"}, {"parameters": {"content": "## Image Prompt", "height": 280, "width": 360, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [980, 0], "id": "065e68c4-9767-43eb-b2f0-08559eba061b", "name": "Sticky Note2"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Upload Photo').item.json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1400, 100], "id": "b2fd8be4-f697-40c7-9d88-f24a17935404", "name": "Download File"}, {"parameters": {"content": "## Download File", "height": 280, "width": 180}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1360, 0], "id": "9e274a49-6d5c-4811-b08a-f879504760da", "name": "Sticky Note3"}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/edits", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "image", "inputDataFieldName": "data"}, {"name": "prompt", "value": "={{ $('Product Photography Agent').item.json.output }}"}, {"name": "model", "value": "gpt-image-1"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1600, 100], "id": "37e0568a-354c-4de3-b309-33e6341305a1", "name": "Create Image"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1740, 100], "id": "100f3c60-550f-44c0-82df-f348d4848f96", "name": "Convert to File"}, {"parameters": {"content": "## Image Generation", "height": 280, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1560, 0], "id": "69ea6b9a-3366-4127-8d45-6934ac58eecf", "name": "Sticky Note4"}, {"parameters": {"method": "POST", "url": "https://api.imgbb.com/1/upload", "authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "image", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, 400], "id": "369d3bfe-c6ef-442e-94ba-c0644cf38902", "name": "Get URL"}, {"parameters": {"method": "POST", "url": "https://api.dev.runwayml.com/v1/image_to_video", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "promptImage", "value": "={{ $json.data.url }}"}, {"name": "model", "value": "gen4_turbo"}, {"name": "promptText", "value": "=Create a highly professional marketing video from the provided product photo. Simulate a smooth, realistic 3D turntable rotation around the product, as if the product is slowly rotating in place. The movement should be continuous, slow, and elegant — no sudden pans, jerks, or camera cuts. Always keep the entire product fully in frame, centered, and clearly visible at all times. Avoid zooming in or cropping. Focus on a premium, clean, and modern aesthetic that feels suitable for commercial marketing materials. No flashy effects, transitions, or overlays — only a subtle, realistic 3D rotation that highlights the product in the most polished way possible."}, {"name": "duration", "value": "10"}, {"name": "ratio", "value": "960:960"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, 400], "id": "1472fadc-62b8-435e-bf56-e14849254f58", "name": "Generate Video"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b45aec04-a6dc-4118-8318-42f40f903935", "leftValue": "={{ $json.status }}", "rightValue": "RUNNING", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1220, 400], "id": "183d946f-c6d7-4d4f-894d-686320c5d25a", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1420, 400], "id": "d5f0d056-397d-4454-9073-6dba0a3f0f14", "name": "5 Secs", "webhookId": "dd3eae86-f75c-41fe-a48c-58788f1ee73d"}, {"parameters": {"url": "=https://api.dev.runwayml.com/v1/tasks/{{ $json.id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Runway-Version", "value": "2024-11-06"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1080, 400], "id": "4a8e782a-4325-41aa-b576-d364980946f2", "name": "Get Video"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [920, 400], "id": "ac2b01d1-5cab-46e2-81af-393ac8c82fbe", "name": "60 Seconds", "webhookId": "ae4ab48f-6ebe-4768-88f0-743989181262"}, {"parameters": {"content": "## Get URL", "height": 260, "width": 180, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, 300], "id": "f969b393-2182-49c8-a5fa-f1a0b6c07285", "name": "Sticky Note5"}, {"parameters": {"content": "## Video Generation", "height": 260, "width": 640, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [720, 300], "id": "3d2f7c10-9423-4259-a211-eda17cfd9dfa", "name": "Sticky Note6"}, {"parameters": {"content": "## Polling", "height": 260, "width": 180, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1380, 300], "id": "17d8b72f-4917-4b80-bea4-30eb60a60232", "name": "Sticky Note7"}, {"parameters": {"sendTo": "={{ $('On form submission').item.json.Email }}", "subject": "=Marketing Materials: {{ $('On form submission').item.json['Product Title'] }}", "emailType": "text", "message": "=Hey!\n\nHere is your photo: {{ $('Get URL').item.json.data.url }}\n\nHere is your video: {{ $json.output[0] }}\n\nCheers!", "options": {"appendAttribution": false}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1620, 400], "id": "eadc9f48-aa47-4c60-9af4-5030ebc0a68e", "name": "Send Finished Products", "webhookId": "779028aa-2c69-4e60-8d40-ff55f32042bc"}, {"parameters": {"content": "## Done", "height": 260, "width": 180, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1580, 300], "id": "8dc216a5-57cd-4a4a-997a-f3875930d918", "name": "Sticky Note8"}, {"parameters": {"promptType": "define", "text": "=Product: {{ $('On form submission').item.json['Product Title'] }}\nDescription: {{ $('On form submission').item.json['Product Description'] }}", "options": {"systemMessage": "=# Overview\nYou are a world-class marketing strategist and an expert text-to-image prompt engineer specializing in creating hyper-realistic, high-quality product photography prompts for AI image generation models.\n\n## Your Objective:\nWhen given a product description, your task is to craft a detailed, professional prompt that results in a hyper-realistic, clean, and visually stunning product image suitable for marketing material, advertising campaigns, or e-commerce platforms.\n\n## Key Requirements:\n- Focus on hyper-realistic, professional studio photography.\n- Maintain a clean, minimalistic, and elegant visual style.\n- Highlight the product as the main subject with sharp detail and perfect lighting.\n- Use backgrounds that complement but do not overpower the product (e.g., soft gradient, light-colored, or pure white).\n- Include professional lighting details such as \"softbox lighting\", \"studio lights\", or \"natural soft shadows\".\n- Emphasize realism, texture, and color accuracy.\n- Maintain a high-end, premium look and feel.\n- If applicable, suggest a subtle setting that enhances the product's story (e.g., a luxury kitchen counter for a high-end blender).\n\n## Output Format:\n- Write a single text-to-image prompt optimized for a professional AI image model.\n- Be direct and descriptive without using excessive words.\n- Avoid unnecessary repetition or adjectives that do not enhance the image quality.\n- Ensure the prompt is complete and ready for direct input into an AI model.\n\n## Tone:\nProfessional, precise, clean, and optimized for maximum realism and marketing impact.\n\n# Example Input:\n\"A premium wireless Bluetooth speaker in matte black, cylindrical shape, modern design.\"\n\n# Example Output:\n\"Hyper-realistic product photo of a premium matte black wireless Bluetooth speaker with a cylindrical modern design, centered on a clean white studio background, softbox lighting with natural shadows, sharp focus on texture and material, minimalistic, professional advertising shot.\"\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1040, 60], "id": "7c76f57f-93dd-483d-a35c-d098aa2ef62c", "name": "Product Photography Agent"}, {"parameters": {"content": "# 🛠️ Setup Guide\n**Author: [<PERSON><PERSON><PERSON> kongolo](whop.com/oki-ai-automation/?a=jadaikongolo)**\n\nFollow these steps to complete your setup:\n\n1. **Connect Google Credentials**  \n   - Allow access to your **Google Drive** and **Gmail**.\n\n2. **Connect [OpenRouter](https://openrouter.ai/) API Key**  \n   - This will enable the chat model for your AI agent.\n\n3. **Connect [OpenAI](https://platform.openai.com/docs/overview) API Key**  \n   - Required for the **Edit Image API** functionality.\n\n4. **Connect [ImageBB](https://imgbb.com/) API Key**  \n   - Needed for the **Git URL HTTP request**.\n\n5. **Connect [Runway](https://runwayml.com/api) API Key**  \n   - Used to **generate** and **Git** the videos.\n\n---\n\nOnce all keys are connected, you're ready to start using the system!\n", "height": 560, "width": 500}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "f5519756-6681-49ea-acac-ebfbd1bd4cfe", "name": "Sticky Note9"}], "pinData": {}, "connections": {"On form submission": {"main": [[{"node": "Upload Photo", "type": "main", "index": 0}]]}, "Upload Photo": {"main": [[{"node": "Product Photography Agent", "type": "main", "index": 0}]]}, "GPT 4.1": {"ai_languageModel": [[{"node": "Product Photography Agent", "type": "ai_languageModel", "index": 0}]]}, "Download File": {"main": [[{"node": "Create Image", "type": "main", "index": 0}]]}, "Create Image": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Get URL", "type": "main", "index": 0}]]}, "Get URL": {"main": [[{"node": "Generate Video", "type": "main", "index": 0}]]}, "Generate Video": {"main": [[{"node": "60 Seconds", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "5 Secs", "type": "main", "index": 0}], [{"node": "Send Finished Products", "type": "main", "index": 0}]]}, "5 Secs": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Get Video": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "60 Seconds": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Product Photography Agent": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0aadcbe0-c877-44eb-b173-06c46aa3e217", "meta": {"instanceId": "8cf060ebda3ec45b5ebb6a30779eaf0c03dfba83865feab3f32adb31b82caa08"}, "id": "pAq6tUb7E4vAwpRq", "tags": []}