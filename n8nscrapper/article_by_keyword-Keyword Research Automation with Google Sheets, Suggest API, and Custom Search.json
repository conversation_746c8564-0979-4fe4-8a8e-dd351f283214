{"name": "article by keyword", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [260, 600], "id": "f0738de4-22b6-48eb-a037-727ecb52b682", "name": "Manual Trigger"}, {"parameters": {"documentId": {"__rl": true, "value": "12GNwRDjdfyDvGbC3Vqiy7lIVdQJcPz3D42xpUOUVpbc", "mode": "list", "cachedResultName": "article by keyword", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/12GNwRDjdfyDvGbC3Vqiy7lIVdQJcPz3D42xpUOUVpbc/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/12GNwRDjdfyDvGbC3Vqiy7lIVdQJcPz3D42xpUOUVpbc/edit#gid=0"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [500, 600], "id": "556149ce-60ab-4188-9c12-06d5ce5846e7", "name": "Fetch Keyword List (Google Sheets)", "credentials": {"googleSheetsOAuth2Api": {"id": "cdFAZ07RzxJajS8M", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "const rows = items; // allrows from Google Sheet\n\n// Count the days since July 5, 2025\nconst startDate = new Date(\"2025-07-04\");\nconst today = new Date();\nconst dayDiff = Math.floor((today - startDate) / (1000 * 60 * 60 * 24));\n\n// Calculate today's keyword index\nconst index = dayDiff % rows.length;\nconst selectedKeyword = rows[index].json.Title || rows[index].json.Keyword || \"Unknown\";\n\nreturn [\n  {\n    json: {\n      index,\n      keyword: selectedKeyword,\n      date: today.toISOString()\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [740, 600], "id": "3bc6997f-a6ba-4e23-8c3a-19f2eff35354", "name": "Select Keyword of the Day"}, {"parameters": {"values": {"string": [{"name": "seed_keyword", "value": "={{ $json.keyword }}"}]}, "options": {}}, "id": "9458a3a7-a748-429b-8388-bfe9c6df111c", "name": "Set Selected Keyword", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [1000, 600]}, {"parameters": {"url": "https://suggestqueries.google.com/complete/search", "options": {}, "queryParametersUi": {"parameter": [{"name": "client", "value": "firefox"}, {"name": "q", "value": "={{$json.keyword}}"}]}}, "id": "bcf65001-8156-4c75-aebb-f58a7ea556e5", "name": "Fetch Autocomplete Suggestions (Google Suggest API)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 2, "position": [1260, 600]}, {"parameters": {"functionCode": "if (Array.isArray($json) && Array.isArray($json[1])) {\n  const suggestions = $json[1];\n  return suggestions.map(k => ({ json: { keyword: k } }));\n} else {\n  throw new Error(\"Unexpected response format from Google Suggest API\");\n}"}, "id": "16332f94-ad54-4c87-9a43-f4c9afd68a21", "name": "Extract Suggestion Keywords", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [520, 840]}, {"parameters": {"url": "https://www.googleapis.com/customsearch/v1", "sendQuery": true, "queryParameters": {"parameters": [{"name": "key", "value": "AIzaSyBRA9R-cH99PUVmMhlmsemFM6sIifoIeAw"}, {"name": "cx", "value": "d140610362cb44788"}, {"name": "q", "value": "={{ $json.keyword }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [780, 840], "id": "03299970-288c-461a-82e2-dd5216bb5beb", "name": "Search Keywords (Google Custom Search API)"}, {"parameters": {"jsCode": "const results = $json.items || [];\nreturn results.map(item => ({\n  json: {\n    title: item.title,\n    link: item.link,\n    snippet: item.snippet\n  }\n}));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1040, 840], "id": "e8263f69-97e2-4d8a-aa8c-a30a9abfbf9a", "name": "Format Search Results"}, {"parameters": {"content": "## 📒 Keyword Research Automation with Google Sheets, Suggest API, and Custom Search\n\nThis workflow automatically selects a new keyword each day from a Google Sheet, enriches it with autocomplete suggestions from the Google Suggest API, and performs a search using the Google Custom Search API.\n\n### 🔎 Who’s it for\n- SEO specialists\n- Content creators\n- Digital marketers\n- Anyone doing daily keyword research\n\n### ⚙️ What it does\n1. Reads a list of seed keywords from Google Sheets.\n2. Picks one keyword per day based on date rotation.\n3. Fetches autocomplete suggestions via Google Suggest API.\n4. Runs Google Custom Search for each suggestion.\n5. Returns a clean list of search result titles, links, and snippets.\n\n### 🧰 Requirements\n- Connected **Google Sheets account** (OAuth2)\n- **Custom Search API Key** and **Search Engine ID**\n- Google Sheet with at least one column: `Title` or `Keyword`\n\n### 🛠 How to set up\n1. Replace the Sheet ID and Sheet Name in the Google Sheets node.\n2. Enter your Google API Key and Search Engine ID (preferably using environment variables, not hardcoded).\n3. Trigger the workflow manually or schedule it.\n\n### 🧑‍🎓 Tips to customize\n- Add filters for search results.\n- Send final output to Notion, Telegram, or Airtable.\n- Modify keyword selection to use random or weighted logic.\n- Integrate volume/CPC data from external keyword APIs.\n\n✅ Built with best practices: Sticky notes, descriptive node names, no hardcoded secrets, and clean formatting.\n\n🔗 Example result usage: SEO content planning, blog writing, or niche discovery.\n\n#n8n #SEOautomation #GoogleSheets #CustomSearch #AIworkflow\n", "height": 1120, "width": 540}, "type": "n8n-nodes-base.stickyNote", "position": [-420, 200], "typeVersion": 1, "id": "e4d2fcf8-0c39-4c3b-a282-a998d76def16", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Manual Trigger": {"main": [[{"node": "Fetch Keyword List (Google Sheets)", "type": "main", "index": 0}]]}, "Fetch Keyword List (Google Sheets)": {"main": [[{"node": "Select Keyword of the Day", "type": "main", "index": 0}]]}, "Select Keyword of the Day": {"main": [[{"node": "Set Selected Keyword", "type": "main", "index": 0}]]}, "Set Selected Keyword": {"main": [[{"node": "Fetch Autocomplete Suggestions (Google Suggest API)", "type": "main", "index": 0}]]}, "Fetch Autocomplete Suggestions (Google Suggest API)": {"main": [[{"node": "Extract Suggestion Keywords", "type": "main", "index": 0}]]}, "Extract Suggestion Keywords": {"main": [[{"node": "Search Keywords (Google Custom Search API)", "type": "main", "index": 0}]]}, "Search Keywords (Google Custom Search API)": {"main": [[{"node": "Format Search Results", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "cf5ff3b8-22ca-4292-8126-9b03fc220eee", "meta": {"templateCredsSetupCompleted": true, "instanceId": "ba73835d772fb31a8c93dbcda773c1ef34e21227b9c152c1ee6fdf0eab995f28"}, "id": "25aupqaGLB1zpP51", "tags": []}