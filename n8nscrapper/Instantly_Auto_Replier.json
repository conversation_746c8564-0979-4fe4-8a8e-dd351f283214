{"name": "Instantly Auto Replier", "nodes": [{"parameters": {"httpMethod": "POST", "path": "instantly-test", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-640, -160], "id": "3539f0c8-32ed-4c01-a239-823471bbf3f1", "name": "Webhook", "webhookId": "7e8e7c4b-e290-4151-972c-0c45102690d0"}, {"parameters": {"assignments": {"assignments": [{"id": "b3960f99-51bb-4cfb-9bec-0fafebee435e", "name": "targetEmail", "value": "={{ $('Webhook').item.json.body.lead_email }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-440, -160], "id": "ec613343-6c15-4e26-8b7b-341542387836", "name": "<PERSON>"}, {"parameters": {"url": "https://api.instantly.ai/api/v2/emails", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\n  \"Authorization\": \"Bearer TOKEN_HERE\",\n  \"Content-Type\": \"application/json\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-240, -160], "id": "6a8c8c70-2635-43a3-b2d2-be64245007d6", "name": "Get_ALL_Data"}, {"parameters": {"method": "POST", "url": "https://api.instantly.ai/api/v2/emails/reply", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer TOKEN_HERE"}, {"name": "Content-Type\t", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "eaccount", "value": "={{ $json.eaccount }}"}, {"name": "reply_to_uuid", "value": "={{ $json.reply_to_uuid }}"}, {"name": "subject", "value": "={{ $json.subject }}"}, {"name": "body", "value": "={{ $json.body }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, 240], "id": "251808c4-23f2-4370-a15b-8350aac578d4", "name": "HTTP Request2", "alwaysOutputData": true}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "=You are helpful sales assitant, Your job is to reply to emails that you recive\nWrite the reply naturally and professionally without using any hyphens, dashes, or typical AI-generated phrases. Make sure the message sounds like it was written by a real person. Avoid cliches and robotic expressions. Use simple and clear language.\n\nAlways return the message content in plain HTMl format like this  \n\"Hey <PERSON><br><br>Thanks for showing your interest<br><br>....\"", "role": "system"}, {"content": "=Write a personalized reply message . You will be provided firsTname and lead's message  Use the lead's first name for greeting and writer personalized reply baed on the lead's message like this  \"Hey <PERSON><br><br>...\"\n\n\n\nResources:\n- Notion doc: https://www.notion.so/AI-automated-outbound-lead-gen-system-1de286acfb2480858e8afbb3404baaf9e8ef9e5df44db8\n- Calendly: https://calendly.com/shahrukh-majeed-scoodia/30-min-discover-call\n\nRules:\n\nIf the lead asks to book a call, reply with thanks and Calend<PERSON> link only. Use this template:\n\nHey {firstName}<br><br>Thanks for showing interest<br><br>Let me know what time works for you this week or next week or feel free to book a time here: <br><br>https://calendly.com/shahrukh-majeed-scoodia/30-min-discover-call\n\nIf the lead asks for docs or product details, reply with this template:\n\nHey {firstName}<br><br>Thanks for showing interest<br><br>Here is the link to the doc: <br><br>https://www.notion.so/AI-automated-outbound-lead-gen-system-1de286acfb2480858e8afbb3404baaf9e8ef9e5df44db8<br><br>But most people get more value over a quick call<br><br>On the call you get a complete walk through of the system. Not only that you get a free resource custom built around your business that explains what you can do right away to see improvements<br><br>Let me know what time works or here is the link to book a call with our Automation expert Shahrukh: <br><br>https://calendly.com/shahrukh-majeed-scoodia/30-min-discover-call\n<br><br>\nLooking forward to hearing from you\n<br><br>\nThanks\n\nIf the lead is generally interested but not specific, reply with the product doc plus call offer using the same template above.\n\nMake sure the message sounds natural, human, and friendly. No hyphens, dashes, or AI-like phrasing. \n\nUse the variables like {firstName} in the message and fill them accordingly.\n\n\n- Use spartan/lactonic voice/tone\n- If the email is out of office or has some data in it like I think it won't work for me etc alwats reference that message\n- Do not include signature or name at the end of the email like [YourName] Or [Name]\n- The mail should be ready to send there should be no varianble or fill in the blanks\n\n- If the message is about asking for a Video slightly shift towards the Doc and say Here's the link to doc with all the info {Or USe some more smooth sentence}\n-Always return the message content in plain HTMl format like this  \n\"Hey Steven<br><br>Thanks for showing your interest<br><br>....\""}, {"content": "=First Name: \"{{ $('Webhook').item.json.body.firstName }}\"\n\nLead's message:\n\"{{ $('Webhook').item.json.body.reply_text }}\""}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-660, 240], "id": "0d973fe7-ef31-48a6-9c2e-500836c5ee5f", "name": "Write_message", "credentials": {"openAiApi": {"id": "ZIq5QksSwup6CQcx", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// Get target email from Webhook or Edit Fields node\nconst targetEmail = $node['Webhook']?.json.lead_email || $node['Edit Fields']?.json.targetEmail;\n\nif (!targetEmail) {\n  console.log('Missing target email from Webhook or Edit Fields node');\n  return [];\n}\n\n// Get all email items array from Get_ALL_Data node\nconst allEmails = $node['Get_ALL_Data']?.json.items;\n\nif (!allEmails || allEmails.length === 0) {\n  console.log('No emails found in Get_ALL_Data node');\n  return [];\n}\n\n// Log for debugging\nconsole.log('Target Email:', targetEmail);\nconsole.log('All leads:', allEmails.map(e => e.lead));\n\n// Find item where lead matches target email (case insensitive, trimmed)\nconst match = allEmails.find(item => \n  item.lead?.toLowerCase().trim() === targetEmail.toLowerCase().trim()\n);\n\nif (!match) {\n  console.log(`No match found for ${targetEmail}`);\n  return [];\n}\n\n// Return matched item as output\nreturn [{ json: match }];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-40, -160], "id": "c5c7ec1f-ec9a-4f14-858b-772fb158d37f", "name": "Match_values", "alwaysOutputData": false}, {"parameters": {"jsCode": "return [{\n  json: {\n    eaccount: $node[\"Match_values\"].json.eaccount,\n    reply_to_uuid: $node[\"Match_values\"].json.id,\n    subject: $node[\"Webhook\"].json.body.reply_subject,\n    body: {\n      text: $node[\"Write_message\"].json.message.content\n    }\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-360, 240], "id": "9465f0d1-d95d-4f83-9880-56f056cf43cc", "name": "Code1"}, {"parameters": {"content": "# Step-1\n\n## Get Positive Replies from Instantly", "height": 360, "width": 920}, "type": "n8n-nodes-base.stickyNote", "position": [-700, -340], "typeVersion": 1, "id": "cd7d1390-00b4-49a0-8cbf-5ae9c47e496b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# Step-2\n\n## Generate Email Replies to book meetings", "height": 400, "width": 500, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [-700, 60], "typeVersion": 1, "id": "ab7e207f-eb70-4492-a445-a6ee3d18d260", "name": "Sticky Note1"}, {"parameters": {"content": "# Step-3\n\n## Send Replies Automatically", "height": 400, "width": 380, "color": 7}, "type": "n8n-nodes-base.stickyNote", "position": [-160, 60], "typeVersion": 1, "id": "1e9030ce-3463-4b05-bdce-523ec12b79a9", "name": "Sticky Note2"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "shahrukh123.app.n8n.cloud", "user-agent": "axios/0.21.4", "content-length": "2207", "accept": "application/json, text/plain, */*", "accept-encoding": "gzip, br", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "*************", "cf-ew-via": "15", "cf-ipcountry": "US", "cf-ray": "957e3a11e30c6fce-IAD", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "x-forwarded-for": "*************, *************", "x-forwarded-host": "shahrukh123.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-64-799ffc96d7-lflxw", "x-is-trusted": "yes", "x-real-ip": "*************"}, "params": {}, "query": {}, "body": {"timestamp": "2025-06-30T14:05:05.064Z", "event_type": "lead_interested", "workspace": "475fd13d-9ec8-43a3-a6b1-3dcbef4352a2", "campaign_id": "617b482a-3a5e-4ea4-be3f-f589f298513d", "unibox_url": null, "campaign_name": "Executive coaches (US)(350)", "reply_text_snippet": "Please send info to read. Thank you.     <PERSON><PERSON><PERSON>,  <PERSON>  ", "lead_email": "<EMAIL>", "email": "<EMAIL>", "website": "http://www.domain.com", "jobTitle": "Executive Coach, Business Strategist, & President", "lastName": "<PERSON><PERSON><PERSON>", "linkedIn": "http://www.linkedin.com/in/LinkedInID", "firstName": "<PERSON>", "IceBreaker": "Hey <PERSON>,\n\nWas looking into Victory Exec Group and was really impressed especially how you blend servant leadership with hard-nosed business strategy.\n\nBeen building a system that helps exec coaches and strategists reach senior leaders hungry for practical, actionable growth plans. It's an automated outbound lead gen system.", "companyName": "Company Name", "step": 1, "variant": 3, "reply_subject": "Email_Subject", "reply_text": "Please send info to read. Thank you. Since<PERSON>y, <PERSON>. Example Acme Leadership Group, LLC. 123.456.7890 ‘Empowering Leaders for Impact’ Book a Meeting: https://calendly.com/john-acmeleadershipgroup/new-meeting To Learn More, Visit: www.AcmeLeadershipGroup.com www.ExampleNetwork.org www.AcmeTrainingHub.com -----Original Message----- From: <PERSON> l<PERSON>@exampleagency.com Sent: Monday, June 30, 2025 8:37 AM To: <NAME_EMAIL> Subject: RE: <PERSON> | follow-up  Hey <PERSON>, Was looking into Acme Leadership Group and was really impressed especially how you blend servant leadership with hard-nosed business strategy. Been building a system that helps exec coaches and strategists reach senior leaders hungry for practical, actionable growth plans. It's an automated outbound lead gen system.  Used by teams like NorthBridge & ApexBlue to get 30+ leads/month. Costs cents to run. The best part? We’ll set it up free. No charge Want the detailed doc(2min read) or a quick 10-min call? Cheers, <PERSON> Account executive ExampleAgency.io"}, "webhookUrl": "https://shahrukh123.app.n8n.cloud/webhook/instantly-test", "executionMode": "production"}}]}, "connections": {"Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Get_ALL_Data", "type": "main", "index": 0}]]}, "Get_ALL_Data": {"main": [[{"node": "Match_values", "type": "main", "index": 0}]]}, "Write_message": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Match_values": {"main": [[{"node": "Write_message", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "6681d841-821b-43ef-91b8-2dd3426fc410", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c9840290fc1888e0b2801dc34df4349f100dc0147e8772f033f2351423c4a993"}, "id": "GlIrAbfX0bjOfWJp", "tags": [{"createdAt": "2025-06-30T14:14:00.681Z", "updatedAt": "2025-06-30T14:14:00.681Z", "id": "rOIFigwJWbPW1fP2", "name": "Gumroad"}]}