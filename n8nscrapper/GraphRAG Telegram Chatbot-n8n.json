{"id": "dHmXMjhXw6tqnf8y", "meta": {"instanceId": "2a26454b0172ffcb8d70ba77c235b1209f92cd71bf06c79ba609c7173b416d68", "templateCredsSetupCompleted": true}, "name": "Telegram AI Chatbot Agent with InfraNodus GraphRAG Knowledge Base", "tags": [{"id": "66wgFoDi9Xjl74M3", "name": "Support", "createdAt": "2025-05-21T17:06:32.355Z", "updatedAt": "2025-05-21T17:06:32.355Z"}, {"id": "9QurS5Kb1CkCYrnG", "name": "Product", "createdAt": "2025-05-21T17:06:29.704Z", "updatedAt": "2025-05-21T17:06:29.704Z"}, {"id": "IzYV3Gv55x1h73cK", "name": "Ideation", "createdAt": "2025-05-20T14:53:20.662Z", "updatedAt": "2025-05-20T14:53:20.662Z"}, {"id": "aECLdyckoPZ29skg", "name": "Writing", "createdAt": "2025-05-20T14:53:23.085Z", "updatedAt": "2025-05-20T14:53:23.085Z"}, {"id": "ciIq4giMNXaJiyiQ", "name": "Thinking Tools", "createdAt": "2025-05-20T14:53:17.515Z", "updatedAt": "2025-05-20T14:53:17.515Z"}, {"id": "hCzs9EJfBuD0RxuX", "name": "Other", "createdAt": "2025-05-21T17:06:34.674Z", "updatedAt": "2025-05-21T17:06:34.674Z"}, {"id": "sJk9cUvmMU8FkJXv", "name": "AI", "createdAt": "2025-05-20T13:16:15.636Z", "updatedAt": "2025-05-20T13:16:15.636Z"}], "nodes": [{"id": "736e717e-bad8-4943-9ae2-955cf588112d", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-260, -800], "parameters": {"color": 6, "width": 540, "height": 760, "content": "## AI Chatbot Agent with Experts\n\n### Uses the InfraNodus knowledge graphs and its Graph RAG to retrieve relevant information.\n\nUse your [InfraNodus graph](https://infranodus.com) as the knowledge base for your AI chatbot. \n\nUpload any data to InfraNodus, generate separate knowledge graphs, then connect them as tools to the agent, so it can decide which \"expert\" to use. InfraNodus' Graph RAG will provide high-quality responses that will augment the chatbot's answers.\n\nVideo demo: [https://www.youtube.com/watch?v=kS0QTUvcH6E](https://www.youtube.com/watch?v=kS0QTUvcH6E)\n\nDetailed description: [Nodus Labs support portal](https://support.noduslabs.com/hc/en-us/articles/20174217658396-Using-InfraNodus-Knowledge-Graphs-as-Experts-for-AI-Chatbot-Agents-in-n8n)\n\nInfraNodus API key can be obtained at [InfraNodus.Com](https://infranodus.com/use-case/ai-knowledge-graphs)\n\n\n[![Video tutorial](https://img.youtube.com/vi/kS0QTUvcH6E/sddefault.jpg)](https://www.youtube.com/watch?v=kS0QTUvcH6E)"}, "typeVersion": 1}, {"id": "e966551c-01db-4b3e-a233-632ce1c24d2f", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [620, 260], "parameters": {"width": 200, "height": 540, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Chat Memory\n\nWe use the Simple Memory node to track the conversation's context so that the user can refer to previous messages as they converse with the model."}, "typeVersion": 1}, {"id": "d1507b1d-238f-4ff1-a181-e8120c838714", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [420, -260], "parameters": {"width": 460, "height": 400, "content": "## 3. AI Agent Workflow\n\n### Chooses which tool (expert) to use, depending on the user's message. Then receives the responses and synthesizes the final answer.\n\nMake sure you describe the tools available well both in the Agent's System Prompt and in the tools' descriptions. "}, "typeVersion": 1}, {"id": "9cd0245e-a848-487a-bca7-22f8cf1ec95f", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [-260, 20], "parameters": {"height": 500, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 1. Telegram Trigger\n\nThe conversation will be triggered when you send a message to your Telegram bot. To set up a bot, simply message `/newbot` to `@botfather` in Telegram. It takes 30 seconds. "}, "typeVersion": 1}, {"id": "dc80cc79-24e7-4a43-8869-ba1ef48e2026", "name": "OpenAI Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [400, 320], "parameters": {"model": "gpt-4o", "options": {}}, "credentials": {"openAiApi": {"id": "07wFa8Wa4mMRCHAj", "name": "OpenAi account 2"}}, "typeVersion": 1}, {"id": "a9000f01-714b-4f85-86f4-29592178eb44", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [680, 300], "parameters": {"sessionKey": "={{ $('Receive a Message on Telegram').item.json.message.chat.id }}", "sessionIdType": "customKey"}, "typeVersion": 1.3}, {"id": "3ec5c404-69f4-468f-a5cc-9500e207e2ed", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [20, 20], "parameters": {"width": 260, "height": 500, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 2. Give Feedback to User\n\nThis node gives feedback to the user that the Agent is working."}, "typeVersion": 1}, {"id": "c4edb9b5-7293-490d-a718-ebaf0bb3aeb0", "name": "Receive a Message on Telegram", "type": "n8n-nodes-base.telegramTrigger", "position": [-200, 40], "webhookId": "aa8d195c-01c2-435b-9d88-87b38306a6a6", "parameters": {"updates": ["message"], "additionalFields": {}}, "credentials": {"telegramApi": {"id": "RoCnmnDFLL2S3f81", "name": "Dmitry's Books Bot"}}, "typeVersion": 1.2}, {"id": "3023c0bc-f219-4b24-b889-726112808b70", "name": "Send \"Typing...\" message to User", "type": "n8n-nodes-base.telegram", "position": [100, 40], "webhookId": "845f29dd-9da3-4ac9-86d1-22280acbb46a", "parameters": {"chatId": "={{ $json.message.chat.id }}", "operation": "sendChatAction"}, "credentials": {"telegramApi": {"id": "RoCnmnDFLL2S3f81", "name": "Dmitry's Books Bot"}}, "typeVersion": 1.2}, {"id": "a698860d-e2dd-4679-be0a-d8c36d12f9ba", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1080, -260], "parameters": {"width": 260, "height": 380, "content": "## 4. Respond to the User\n\nOnce the response is generated, send it back to the user's chat in Telegram."}, "typeVersion": 1}, {"id": "879d4e7a-c2c7-47eb-aaca-4847e61d6886", "name": "Send Telegram Message to User", "type": "n8n-nodes-base.telegram", "position": [1160, -40], "webhookId": "a6b9e331-2b1a-4056-a9fc-5d10b7196c54", "parameters": {"text": "={{ $json.output }}", "chatId": "={{ $('Receive a Message on Telegram').item.json.message.chat.id }}", "additionalFields": {"appendAttribution": false}}, "credentials": {"telegramApi": {"id": "RoCnmnDFLL2S3f81", "name": "Dmitry's Books Bot"}}, "typeVersion": 1.2}, {"id": "1bc915c9-8494-4c74-b508-80484e9bc290", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [540, 0], "parameters": {"text": "={{ $('Receive a Message on Telegram').item.json.message.text }}", "options": {"systemMessage": "You are well-versed on <PERSON>'s books through the tools you have access to. When you receive a user's query, you can modify it to suit better each tool's context. Always access at least one of the tools and deliver an augmented response.\n\nWhen you're generating a response, attempt to combine perspectives where they fit and point out discrepancies when they exist."}, "promptType": "define"}, "typeVersion": 1.9}, {"id": "75db20b3-7a82-49db-a574-66b0ce0a9560", "name": "Waves into Patterns Book Expert", "type": "n8n-nodes-base.httpRequestTool", "position": [1120, 300], "parameters": {"url": "https://infranodus.com/api/v1/graphAndAdvice?doNotSave=true&addStats=true&optimize=develop&includeGraph=false&includeGraphSummary=true&includeStatements=true", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "special_agents_manual"}, {"name": "prompt", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters1_Value', `User's request adjusted to suit this context`, 'string') }}"}, {"name": "requestMode", "value": "response"}, {"name": "aiTopics", "value": "true"}]}, "genericAuthType": "httpBearerAuth", "toolDescription": "Make a request to The Waves into <PERSON><PERSON><PERSON> book which is well-versed in the questions of natural cycles, variability, fractal states, dynamics, and nonequilibrium stability. Including the following topics:\n\n<MainTopics>: \n1. System Influence: scale influence multiple \n2. Pattern Dynamics: pattern variability fractal \n3. Change Balance: dynamic wave time \n4. Center Transformation: center cycle dominant \n5. Growth Stages: growth exploration period \n6. Long Strategy: term state long \n7. Seasonal Relations: moon summer natural \n8. Flow Connection: exist breaking exploring \n</MainTopics>"}, "credentials": {"httpBearerAuth": {"id": "kg0rA5r8siL8LbrU", "name": "InfraNodus Circadian API Key"}}, "typeVersion": 4.2}, {"id": "791b109a-f7f4-4391-86ce-4db6315fcd95", "name": "Special Agent's Manual Book Expert", "type": "n8n-nodes-base.httpRequestTool", "position": [900, 300], "parameters": {"url": "https://infranodus.com/api/v1/graphAndAdvice?doNotSave=true&addStats=true&optimize=develop&includeGraph=false&includeGraphSummary=true&includeStatements=true", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "special_agents_manual"}, {"name": "prompt", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters1_Value', `User's request adjusted to suit this context`, 'string') }}"}, {"name": "requestMode", "value": "response"}, {"name": "aiTopics", "value": "true"}]}, "genericAuthType": "httpBearerAuth", "toolDescription": "Make a request to The Special Agent's Manual book which is well-versed in the questions of human agency, speciality, infiltration and tension dynamics, strategies, identities, finding the special in you as well as the following topics:\n\n<MainTopics>: \n1. Agent Activation: agent special activation \n2. System Dynamics: system body operating \n3. Order Flow: action time order \n4. Creative Process: create identity entity \n5. Movement Patterns: movement wave breathing \n6. Incoming Impact: incoming impulse impact \n7. Fiction Venture: fiction high risk \n8. Social Framework: preset possibility level \n</MainTopics>"}, "credentials": {"httpBearerAuth": {"id": "kg0rA5r8siL8LbrU", "name": "InfraNodus Circadian API Key"}}, "typeVersion": 4.2}, {"id": "c1a5628c-aa40-451b-b9ac-0cd9ba98dce1", "name": "The Flow and the Notion Book", "type": "n8n-nodes-base.httpRequestTool", "position": [1340, 300], "parameters": {"url": "https://infranodus.com/api/v1/graphAndAdvice?doNotSave=true&addStats=true&optimize=develop&includeGraph=false&includeGraphSummary=true&includeStatements=true", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "the_flow_and_notion"}, {"name": "prompt", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters1_Value', `User's request adjusted to suit this context`, 'string') }}"}, {"name": "requestMode", "value": "response"}, {"name": "aiTopics", "value": "true"}]}, "genericAuthType": "httpBearerAuth", "toolDescription": "The Flow and the Notion book which is well-versed in the questions of creating with someone, dreaming, making new shapes, dissipating ideas, art, and life. Including the following topics:\n\n<MainTopics>: \n1. Shape Dynamics: shape outline concentric \n2. Time Alignment: long practice time \n3. Man's Journey: bow man candle \n4. Notion Flow: image flow paranyushkin \n5. Essence Realization: process essence essential \n6. Life Choices: slave thing trace \n7. Wave Patterns: found light movement \n8. Synchrony Mediation: book circadian word \n9. Complete Vision: multiple synchrony mediate \n10. Instruction Guide: full \ninstruction foreword : instruction foreword \n</MainTopics>"}, "credentials": {"httpBearerAuth": {"id": "kg0rA5r8siL8LbrU", "name": "InfraNodus Circadian API Key"}}, "typeVersion": 4.2}, {"id": "e7cc5883-e814-4ed8-95fb-8c7294466e31", "name": "The Polysingularity Letters Book", "type": "n8n-nodes-base.httpRequestTool", "position": [1540, 300], "parameters": {"url": "https://infranodus.com/api/v1/graphAndAdvice?doNotSave=true&addStats=true&optimize=develop&includeGraph=false&includeGraphSummary=true&includeStatements=true", "method": "POST", "options": {}, "sendBody": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "name", "value": "polysingularity_letters"}, {"name": "prompt", "value": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('parameters1_Value', `User's request adjusted to suit this context`, 'string') }}"}, {"name": "requestMode", "value": "response"}, {"name": "aiTopics", "value": "true"}]}, "genericAuthType": "httpBearerAuth", "toolDescription": "The Polysingularity Letters book which is well-versed in the questions of polysingularity, multiplicity, networks, networking, art, and creative approach. And also these topics:\n\n<MainTopics>: \n1. Community Dynamics: network community connected \n2. Global Equilibrium: state global change \n3. Polysingular Practice: polysingularity thinking exist \n4. Temporal Relations: time moment thing \n5. Consciousness Source: social cognition view \n6. Meaning Creation: make orthodox meaning \n7. Dmitry Connections: post dmitry minute \n8. Linguistic Variations: wa ë ww \n</MainTopics>"}, "credentials": {"httpBearerAuth": {"id": "6lSuuSDajZrvI2GM", "name": "InfraNodus API Key"}}, "typeVersion": 4.2}, {"id": "e9b348cc-d6ea-4a46-99cf-cf395a50ab6e", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "position": [-260, 560], "parameters": {"color": 3, "width": 540, "height": 820, "content": "## Setting up a Telegram bot \n\n### It takes only 30 seconds to set up a Telegram bot. Once set up, create a Telegram credential with its access token and use it in all the Telegram nodes above. \n\n1. Create a new chat in Telegram with the [@botfather](https://t.me/botfather) user at [https://t.me/botfather](https://t.me/botfather). \n\n2. Type in `/newbot`\n\n3. Give it a name (that's not the username)\n\n4. Give it a username (e.g. @my_new_bot)\n\n5. You will receive an **API token**. Copy that token and use it to set up the Telegram credentials for the Telegram nodes above. \n\n\nOPTIONAL SETTINGS FOR TELEGRAM GROUPS USE:\n\n6. Note, that if you want to make it possible to add your Telegram bots to groups, you will need to give them permissions to access groups and to read groups' messages. To do that, type in `/mybots`, and list your bots, then choose the bot you created. \n\n7. Then click on Bot Settings > Allow Groups > Yes (the bot will be allowed to join groups)\n\n8. Then choose Settings > Group Privacy > Turn Off (to allow the bot to read groups' messages)\n\n9. Note that your bots won't be able to respond to each other (that's Telegram limitation to avoid spamming and infinite loops), but they will be able to participate in your group's conversations. In that case, you might want to set up an additional filter in the workflow above where the bot will only respond if addressed directly using its username (so that it doesn't respond to every message in the group but only when asked directly)."}, "typeVersion": 1}, {"id": "75e639fa-33dc-4fbc-a34d-dee9e5550969", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [840, 260], "parameters": {"width": 200, "height": 740, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Expert #1\n\nAdd your InfraNodus graph here via the HTTP node using its name in the `body.name` field.\n\nDescribe what the expert does in the Description of the tool. You can use auto-generated Graph RAG summary from InfraNodus > Graph > Project Notes\n\n![Book Screenshot](https://i.ibb.co/rfxsJ4MV/circadian-special-agents-manual.png)"}, "typeVersion": 1}, {"id": "8bb5f9fd-3a31-4c67-8684-b7190a342b2a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1060, 260], "parameters": {"width": 200, "height": 740, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Expert #2\n\nAdd your InfraNodus graph here via the HTTP node using its name in the `body.name` field.\n\nDescribe what the expert does in the Description of the tool. You can use auto-generated Graph RAG summary from InfraNodus > Graph > Project Notes\n\n![waves into patterns screen](https://i.ibb.co/1tDJSgVq/circadian-waves-into-patterns.png)"}, "typeVersion": 1}, {"id": "e2e4dd57-fdb2-4ed3-940f-085e4b1a0934", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1280, 260], "parameters": {"width": 200, "height": 640, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Expert #3\n\nYou can add more experts here. Just make to give them descriptive names, so the agent knows which one to connect to when it has a question. \n\n![flow and notion image](https://i.ibb.co/prLbFG0w/circadian-the-flow-and-notion.png)\n"}, "typeVersion": 1}, {"id": "af7c969d-e48d-4d36-8e71-a7c70b79763d", "name": "Sticky Note9", "type": "n8n-nodes-base.stickyNote", "position": [1500, 260], "parameters": {"width": 200, "height": 640, "content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## Expert #4\n\nYou can add more experts here. Just make to give them descriptive names, so the agent knows which one to connect to when it has a question. \n\n![infranodus graph](https://i.ibb.co/hRqxn8JN/circadian-conversation-book.png)"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "5ad178b3-764a-40eb-a074-e768b615960f", "connections": {"AI Agent": {"main": [[{"node": "Send Telegram Message to User", "type": "main", "index": 0}]]}, "OpenAI Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "The Flow and the Notion Book": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Receive a Message on Telegram": {"main": [[{"node": "Send \"Typing...\" message to User", "type": "main", "index": 0}]]}, "Waves into Patterns Book Expert": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Send \"Typing...\" message to User": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "The Polysingularity Letters Book": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Special Agent's Manual Book Expert": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}}