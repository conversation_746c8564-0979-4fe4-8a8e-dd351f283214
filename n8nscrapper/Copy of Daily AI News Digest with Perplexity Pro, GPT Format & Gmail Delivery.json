{"name": "Daily AI News Digest with Perplexity Pro, GPT Format & Gmail Delivery", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 9}]}}, "id": "708a8c6b-d521-4dd3-a093-ddc21c95c122", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [1220, 500], "typeVersion": 1.2}, {"parameters": {"subject": "AI News Update in the last 24 hours", "emailType": "text", "message": "={{ $json.output }}", "options": {}}, "id": "645b1d90-c9f8-4ec9-9266-48eb3f6fa70c", "name": "Send a message", "type": "n8n-nodes-base.gmail", "position": [2180, 500], "webhookId": "c90e86e9-2fcb-459d-8141-b9b819c3839b", "typeVersion": 2.1}, {"parameters": {"promptType": "define", "text": "={{ $json.choices[0].message.content }}", "options": {"systemMessage": "You are a helpful formatter assistant, you will receive a chunk of AI Headlines and news, your job is to format it such that it is in an easily readable format for email."}}, "id": "2cfe4f99-2215-4a20-9d9a-b8c683c5eba4", "name": "Formatter AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1740, 500], "typeVersion": 2}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "id": "f7bc9b62-e051-430b-a269-395b22da6157", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1760, 700], "typeVersion": 1.2}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Research Agent').item.json.id }}"}, "id": "93da545a-da0d-4c65-ab59-962aa1257771", "name": "Simple Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [1920, 700], "typeVersion": 1.3}, {"parameters": {"model": "sonar-pro", "messages": {"message": [{"content": "You are a cutting‑edge AI news researcher. Every time you run, search for and retrieve the most recent developments in artificial intelligence published within the last 24 hours. \n\nFor each news item you return:\n- Include the full headline.\n- Provide a one‑sentence summary.\n- Unfurl the full URL (no shortened links or footnote markers).\n- List the publication date and the source name.", "role": "system"}, {"content": "What are the latest headlines in AI Development in the past 24 hours? Include any model launches or notable market news."}]}, "options": {"searchRecency": "day"}, "requestOptions": {}}, "id": "3a109ff0-4f5e-4da3-bda9-6103923d9ad8", "name": "Research Agent", "type": "n8n-nodes-base.perplexity", "position": [1460, 500], "typeVersion": 1}, {"parameters": {"content": "Scheduled Trigger", "height": 560}, "id": "f006dbcc-794d-477e-8835-a1762e5a6719", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1140, 300], "typeVersion": 1}, {"parameters": {"content": "Perplexity Research Agent", "height": 560}, "id": "044d8c27-bc57-4e29-b57b-757794aaaf49", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1400, 300], "typeVersion": 1}, {"parameters": {"content": "Formatter Agent", "height": 560, "width": 400}, "id": "43d45d64-c592-4a1e-8b4f-f4d3bfa83d37", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1660, 300], "typeVersion": 1}, {"parameters": {"content": "Gmail Output Node", "height": 560, "width": 320}, "id": "b324e00e-9838-49a9-b758-c3c6a53011ab", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2080, 300], "typeVersion": 1}, {"parameters": {"content": "🧠 AI News Update Every 24 Hours (with Perplexity + GPT Formatter)\nDescription:\nThis workflow automatically delivers a clean, AI-curated summary of the latest AI news headlines from the past 24 hours directly to your inbox — every morning at 9 AM.\n\nFor step-by-step video tutorial for this build, watch here:\nhttps://youtu.be/O-DLvaMVLso\n\n🧰 How It Works:\n🕘 Schedule Trigger\n Runs daily at 9 AM to start the workflow.\n\n🔎 Perplexity AI Agent\n Searches for AI-related headlines published in the last 24 hours, including:\n\nHeadline\n\n1-sentence summary\n\nSource\n\nFull URL\n\n🧠 GPT Formatter AI Agent\n Uses an OpenAI language model (GPT-4.1-mini) to reformat raw news data into a clean, readable email update.\n\n🧷 Memory Buffer (Optional)\n Gives the formatter context and continuity if you want to personalize formatting further over time.\n\n📧 Gmail Node\n Sends the formatted AI news digest to your inbox (or your team’s) daily.\n\n📦 Tools & APIs Required:\n✅ Perplexity AI API\n\n✅ OpenAI API\n\n✅ Gmail Account (OAuth2 credentials)\n\n🔄 Use Cases:\nDaily AI trend monitoring for individuals or teams\n\nAutomating internal market intelligence\n\nResearch triggers for blog or content creation\n\nEmail digests for newsletters or Slack updates\n\n🛠️ Customizable Ideas:\nSwap Gmail for Slack, Telegram, Discord, etc.\n\nModify the topic (e.g., Climate Tech, Crypto News)\n\nAdd sentiment analysis or AI-generated commentary\n\nSend summary to Google Docs or Notion instead\n\n", "height": 1120, "width": 720}, "id": "505ab0d1-a982-497a-b920-8ac3fd95c1b0", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [380, 240], "typeVersion": 1}], "pinData": {}, "connections": {"Simple Memory": {"ai_memory": [[{"node": "Formatter AI Agent", "type": "ai_memory", "index": 0}]]}, "Research Agent": {"main": [[{"node": "Formatter AI Agent", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Research Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Formatter AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Formatter AI Agent": {"main": [[{"node": "Send a message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "5469", "instanceId": "891c33193ec5bdb4c1a77ff7c065e5231e5b0f64c45fba7050f966c2d82d9c2c"}, "tags": []}