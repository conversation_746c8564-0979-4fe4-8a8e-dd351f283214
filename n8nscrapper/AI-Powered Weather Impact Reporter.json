{"name": "Weather Impact Report", "nodes": [{"parameters": {"content": "## Weather Impact Report", "height": 520, "width": 1100}, "id": "e3fa53ec-9cd7-4480-8c4d-7dd608cef030", "name": "<PERSON>y", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-500, -250]}, {"parameters": {"httpMethod": "POST", "path": "weather_impact_report"}, "id": "bf9d8b40-4657-431e-bce3-d75c9fa88f3b", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-300, 0]}, {"parameters": {"chunkSize": 400, "chunkOverlap": 40}, "id": "a244c3f0-7d3a-4068-a8c3-4b76994b785b", "name": "Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [-100, 0]}, {"parameters": {"model": "default"}, "id": "99b21724-e094-4243-9a3b-f21687a15ae3", "name": "Embeddings", "type": "@n8n/n8n-nodes-langchain.embeddingsHuggingFace", "typeVersion": 1, "position": [100, 0], "credentials": {"huggingFaceApi": {"id": "HF_API", "name": "HuggingFace"}}}, {"parameters": {"mode": "insert", "indexName": "weather_impact_report"}, "id": "2747d45f-2b07-4769-9b22-87a8acf5a350", "name": "Insert", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [300, 0], "credentials": {"supabaseApi": {"id": "SUPABASE_API", "name": "Supabase account"}}}, {"parameters": {"indexName": "weather_impact_report"}, "id": "d78405a7-df63-49eb-8279-1a13fb9a8a8d", "name": "Query", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [300, -180], "credentials": {"supabaseApi": {"id": "SUPABASE_API", "name": "Supabase account"}}}, {"parameters": {"name": "Supabase"}, "id": "c09f609d-822d-4535-ad5e-9ea4f49511c0", "name": "Tool", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [480, -180]}, {"parameters": {}, "id": "efcabced-1d59-42e0-885e-fc2e6bb2d207", "name": "Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [480, -40]}, {"parameters": {}, "id": "********-bd6e-4659-9644-529e4e7f7c03", "name": "Cha<PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1, "position": [480, -340], "credentials": {"anthropicApi": {"id": "ANTHROPIC_API", "name": "Anthropic"}}}, {"parameters": {"promptType": "define", "text": "={{ $json }}"}, "id": "2f140a83-a29b-4f43-871b-363f52ba7064", "name": "Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [760, -40]}, {"parameters": {"operation": "append", "documentId": "SHEET_ID", "sheetName": "Log"}, "id": "a29854b3-52cc-479a-813c-0379b778a0ac", "name": "Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [960, -40], "credentials": {"googleSheetsOAuth2Api": {"id": "SHEETS_API", "name": "Sheets"}}}], "connections": {"Webhook": {"main": [[{"node": "Splitter", "type": "main", "index": 0}, {"node": "Memory", "type": "main", "index": 0}]]}, "Splitter": {"main": [[{"node": "Embeddings", "type": "main", "index": 0}]], "ai_textSplitter": [[{"node": "Insert", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings": {"ai_embedding": [[{"node": "Insert", "type": "ai_embedding", "index": 0}, {"node": "Query", "type": "ai_embedding", "index": 0}]]}, "Insert": {"ai_document": [[]]}, "Query": {"ai_vectorStore": [[{"node": "Tool", "type": "ai_vectorStore", "index": 0}]]}, "Tool": {"ai_tool": [[{"node": "Agent", "type": "ai_tool", "index": 0}]]}, "Memory": {"ai_memory": [[{"node": "Agent", "type": "ai_memory", "index": 0}]]}, "Chat": {"ai_languageModel": [[{"node": "Agent", "type": "ai_languageModel", "index": 0}]]}, "Agent": {"main": [[{"node": "Sheet", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}