{"name": "Website which scrapes Latest Trending AI Products", "nodes": [{"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "box", "cssSelector": "div.Box", "returnValue": "html"}]}, "options": {}}, "id": "7c8db83a-2b53-4bc2-aec8-0824ea52965c", "name": "Extract Box", "type": "n8n-nodes-base.html", "position": [-120, -620], "typeVersion": 1.2}, {"parameters": {"fieldToSplitOut": "repositories", "options": {}}, "id": "1fa6fa3b-d784-4e36-898d-d1649d2c0fd8", "name": "Turn to a list", "type": "n8n-nodes-base.splitOut", "position": [320, -620], "typeVersion": 1}, {"parameters": {"operation": "extractHtmlContent", "dataPropertyName": "repositories", "extractionValues": {"values": [{"key": "repository", "cssSelector": "<PERSON><PERSON>"}, {"key": "language", "cssSelector": "span.d-inline-block"}, {"key": "description", "cssSelector": "p"}, {"key": "star", "cssSelector": "a[href$=\"/stargazers\"]"}]}, "options": {}}, "id": "67f30e96-9e87-439d-a148-22365d976192", "name": "Extract repository data", "type": "n8n-nodes-base.html", "position": [540, -620], "typeVersion": 1.2}, {"parameters": {"operation": "extractHtmlContent", "dataPropertyName": "box", "extractionValues": {"values": [{"key": "repositories", "cssSelector": "article.Box-row", "returnValue": "html", "returnArray": true}]}, "options": {"trimValues": true, "cleanUpText": true}}, "id": "12982772-50ab-4344-9352-97d14987b234", "name": "Extract all repositories", "type": "n8n-nodes-base.html", "position": [100, -620], "typeVersion": 1.2}, {"parameters": {"maxItems": 30}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [320, -420], "id": "087fc5d2-52fa-4aae-b337-af315d3ff56d", "name": "Limit"}, {"parameters": {"url": "https://hacker-news.firebaseio.com/v0/topstories.json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [100, -420], "id": "93180a07-608a-4942-a257-02d12a5eed0a", "name": "Get Hacker News' Trending"}, {"parameters": {"url": "https://github.com/trending", "options": {}}, "id": "77f68cb0-6e20-4a33-ad08-143152b358ad", "name": "Get Github Trending", "type": "n8n-nodes-base.httpRequest", "position": [-340, -620], "typeVersion": 4.2}, {"parameters": {"url": "=https://hacker-news.firebaseio.com/v0/item/{{ $json }}.json", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [540, -420], "id": "910ff37a-91c5-41af-ab46-f0ef78a96910", "name": "Get metadata for each github repo"}, {"parameters": {"assignments": {"assignments": [{"id": "e13c5b75-ae3d-42ae-a7dd-94544f0c5ac8", "name": "source", "value": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": "a2bd790a-784e-4d72-9a4e-92be22edea8f", "name": "name", "type": "string", "value": "={{ $json.repository }}"}, {"id": "baff9a9f-020a-4968-bb80-a4a91a94144a", "name": "url", "type": "string", "value": "=https://github.com/{{ $json.repository.replaceAll(' ','') }}"}, {"id": "27a44ce9-4b5b-44b2-94d9-eb5b2ae81dcd", "name": "description", "type": "string", "value": "={{ $json.description }}"}, {"id": "c8e036d0-5f57-4740-87b5-fd67d9c73910", "name": "engagement_score", "value": "={{ $json.star }}", "type": "string"}, {"id": "f5c48a02-b55d-4167-a823-53ac1d851ee5", "name": "created_at", "type": "string", "value": "={{$now}}"}]}, "includeOtherFields": "=", "options": {}}, "id": "7f0f53bf-6e13-43c3-8832-118c44ad6313", "name": "Set Result Variables for Github Trending", "type": "n8n-nodes-base.set", "position": [760, -620], "typeVersion": 3.4}, {"parameters": {"assignments": {"assignments": [{"id": "7c1efd48-c8fc-4046-abca-7af84f00695d", "name": "source", "value": "HackerNews", "type": "string"}, {"id": "43722101-0658-4903-bf54-cf6422580b94", "name": "name", "value": "={{ $json.title }}", "type": "string"}, {"id": "441f0ae7-1d51-424c-8aa6-0bf2d17536de", "name": "url", "value": "=https://news.ycombinator.com/item?id={{ $json.id }}", "type": "string"}, {"id": "bc75d054-1000-4e52-bcec-0550627d5196", "name": "engagement_score", "value": "={{ $json.score }}", "type": "number"}, {"id": "c8d469d2-81ab-47bf-b9af-1f7282d7c647", "name": "created_at", "value": "={{ $json.time }}", "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [760, -420], "id": "9508675c-2a97-436a-b75f-6567c4773cee", "name": "Set Result Variables for HackerNews Trending"}, {"parameters": {"jsCode": "// Fetch all input items\nconst items = await $input.all();\n\n// Flatten posts from all inputs\nconst results = [];\n\nfor (const item of items) {\n  const edges = item.json.data?.posts?.edges ?? [];\n\n  for (const edge of edges) {\n    const node = edge.node;\n    results.push({\n      source: \"Product Hunt\", \n      name: node.name,\n      url: node.url,\n      description: node.description,\n      engagement_score: node.votesCount,\n      created_at: new Date().toISOString()\n    });\n  }\n}\n\n// Return formatted output\nreturn results.map(data => ({ json: data }));\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [760, -220], "id": "4c640138-bba2-41c9-9161-452933250c50", "name": "Set Result Variables for Product Hunt's Trending"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [980, -420], "id": "687c0f9c-2ddc-43db-ab22-53b623b235ed", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// =================================================================\n// Part 1: Data Preparation (No changes here)\n// =================================================================\n\nconst allData = items.map(i => i.json);\n\nconst getScore = (item) => {\n  if (item.source === 'Github') {\n    return parseInt(String(item.engagement_score).replace(/,/g, '')) || 0;\n  }\n  return parseInt(item.engagement_score) || 0;\n};\n\nconst productHuntItems = allData\n  .filter(item => item.source === 'Product Hunt');\n\nconst hackerNewsItems = allData\n  .filter(item => item.source === 'HackerNews');\n\nconst githubItems = allData\n  .filter(item => item.source === 'Github');\n\n\n// =================================================================\n// Part 2: HTML Generation\n// =================================================================\n\nconst formatScoreForDisplay = (score) => {\n  return score.toLocaleString('en-US');\n};\n\nfunction generateListItems(items, source) {\n  return items.map((item, index) => `\n    <li class=\"list-item\">\n      <span class=\"rank\">${index + 1}</span>\n      <a href=\"${item.url}\" target=\"_blank\" class=\"title\">${item.name}</a>\n      <span class=\"score\">${formatScoreForDisplay(getScore(item))} ${source === 'HackerNews' ? 'points' : '★'}</span>\n    </li>\n  `).join('');\n}\n\nconst htmlContent = `\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <link href=\"https://cdn.jsdelivr.net/npm/@n8n/chat/dist/style.css\" rel=\"stylesheet\" />\n    <script type=\"module\">\n        import { createChat } from 'https://cdn.jsdelivr.net/npm/@n8n/chat/dist/chat.bundle.es.js';\n\ncreateChat({\n\twebhookUrl: window.location.origin + '/webhook/c70e463d-26d2-4514-adda-a0605bf9a159/chat',\n\twebhookConfig: {\n\t\tmethod: 'POST',\n\t\theaders: {}\n\t},\n\ttarget: '#n8n-chat',\n\tmode: 'window',\n\tchatInputKey: 'chatInput',\n\tchatSessionKey: 'sessionId',\n\tloadPreviousSession: true,\n\tmetadata: {},\n\tshowWelcomeScreen: false,\n\tdefaultLanguage: 'en',\n\tinitialMessages: [\n\t\t'Hi there! 👋',\n\t\t'My name is Yong Sheng. How can I assist you today?'\n\t],\n\ti18n: {\n\t\ten: {\n\t\t\ttitle: 'Hi there! 👋',\n\t\t\tsubtitle: \"Start a chat. We're here to help you 24/7.\",\n\t\t\tfooter: '',\n\t\t\tgetStarted: 'New Conversation',\n\t\t\tinputPlaceholder: 'Type your question..',\n\t\t},\n\t},\n});\n    </script>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Today's Tech & Product Trends</title>\n    <link href=\"https://fonts.googleapis.com/icon?family=Material+Icons\" rel=\"stylesheet\">\n    <style>\n        body { background-color: #2D2D2D; font-family: Arial, sans-serif; color: #FFFFFF; margin: 0; padding: 20px 30px; }\n        .header { display: flex; align-items: center; position: fixed; top: 0; left: 0; right: 0; padding: 20px 30px; background-color: #2D2D2D; z-index: 1000; border-bottom: 1px solid #444; }\n        .logo { display: flex; align-items: center; font-size: 24px; font-weight: bold; }\n        .logo .now { background-color: #FF4500; color: #000000; padding: 2px 6px; border-radius: 4px; margin-left: 8px; }\n        .version { font-size: 12px; color: #A9A9A9; margin-left: 10px; align-self: flex-end; position: relative; top: 4px; }\n        .navigation { margin-left: 30px; }\n        .navigation a { color: #A9A9A9; margin: 0 10px; text-decoration: none; font-size: 14px; }\n        .navigation a:hover { color: #FFFFFF; }\n        .header-icons { margin-left: auto; display: flex; align-items: center; }\n        .header-icons .material-icons { color: #FFFFFF; cursor: pointer; }\n        .main-content { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; padding-top: 80px; }\n        .card { border-radius: 12px; padding: 15px; }\n        .ph-card { background-color: #4E3A3A; }\n        .hn-card { background-color: #5A4C3A; }\n        .gh-card { background-color: #3A404D; }\n        .card-header { display: flex; align-items: center; margin-bottom: 15px; }\n        .card-header .icon { width: 24px; height: 24px; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 16px; }\n        .ph-icon { background-color: #DA552F; color: #FFFFFF; }\n        .hn-icon { background-color: #FF6600; color: #FFFFFF; }\n        .gh-icon { font-family: 'Material Icons'; font-size: 24px; }\n        .card-header-title { color: #FFFFFF; font-weight: bold; margin-left: 10px; }\n        .update-time { color: #C0C0C0; font-size: 12px; margin-left: 10px; }\n        .card-header-actions { margin-left: auto; display: flex; }\n        .card-header-actions .material-icons { color: #C0C0C0; margin-left: 10px; cursor: pointer; }\n        .list { list-style: none; padding: 0; margin: 0; }\n        .list-item { display: flex; align-items: center; padding: 10px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1); font-size: 14px; }\n        .list-item:last-child { border-bottom: none; }\n        .list-item .rank { color: #A9A9A9; width: 25px; text-align: left; }\n        .list-item .title { color: #FFFFFF; text-decoration: none; flex-grow: 1; }\n        .list-item .title:hover { text-decoration: underline; }\n        .list-item .score { color: #C0C0C0; margin-left: 15px; white-space: nowrap; }\n        @media (max-width: 1200px) { .main-content { grid-template-columns: repeat(2, 1fr); } }\n        @media (max-width: 768px) { .main-content { grid-template-columns: 1fr; } .navigation { display: none; } }\n\n        .insights-section { padding-top: 10px; text-align: left; }\n        #insights-btn { background-color: #FF4500; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-weight: bold; cursor: pointer; transition: background-color: 0.3s; margin-bottom: 15px; }\n        #insights-btn:hover { background-color: #e03d00; }\n        #insights-btn:disabled { background-color: #555; cursor: not-allowed; }\n        .insights-container { \n            display: none; \n            background-color: #3a404d; \n            border-radius: 8px; \n            padding: 15px; \n            margin-bottom: 20px; \n            border: 1px solid #555; \n            font-size: 14px; \n            line-height: 1.6; \n            /* --- CHANGE HERE --- */\n            text-align: left;\n        }\n\n    </style>\n</head>\n<body>\n    <header class=\"header\"></header>\n    <h1>Today's Tech & Product Trends</h1>\n    <p>- Author's website: <a href=\"https://tanyongsheng.com\" style=\"color: white;\" target=\"_blank\">https://tanyongsheng.com</a></p>\n    <p>- Get the n8n template of this workflow here: <a href=\"https://www.tanyongsheng.com/optin/download-the-free-n8n-template-for-ai-powered-tech-trend-dashboard/\" style=\"color: white;\" target=\"_blank\">https://www.tanyongsheng.com/optin/download-the-free-n8n-template-for-ai-powered-tech-trend-dashboard/</a></p>\n    <div class=\"insights-section\">\n        <button id=\"insights-btn\" onclick=\"getInsights()\">Click Here to get more Insights ✨</button>\n        <div id=\"insights-container\"></div>\n    </div>\n\n    <main class=\"main-content\" style=\"padding-top: 0;\">\n        <div class=\"card ph-card\">\n            <div class=\"card-header\"><div class=\"icon ph-icon\">P</div><span class=\"card-header-title\">Product Hunt</span></div>\n            <ul class=\"list\">${generateListItems(productHuntItems, 'ProductHunt')}</ul>\n        </div>\n        <div class=\"card hn-card\">\n            <div class=\"card-header\"><div class=\"icon hn-icon\">Y</div><span class=\"card-header-title\">Hacker News</span></div>\n            <ul class=\"list\">${generateListItems(hackerNewsItems, 'HackerNews')}</ul>\n        </div>\n        <div class=\"card gh-card\">\n            <div class=\"card-header\"><span class=\"gh-icon\">hub</span><span class=\"card-header-title\">Github Today</span></div>\n            <ul class=\"list\">${generateListItems(githubItems, 'Github')}</ul>\n        </div>\n    </main>\n\n    <script>\n        async function getInsights() {\n            const button = document.getElementById('insights-btn');\n            const container = document.getElementById('insights-container');\n            const apiUrl = window.location.origin + '/webhook/get-trending-ai-insights';\n\n            button.disabled = true;\n            button.textContent = 'Analyzing...';\n            container.style.display = 'block';\n            container.innerHTML = '<p>Please wait, fetching insights...</p>';\n\n            try {\n                const response = await fetch(apiUrl, { method: 'GET' });\n\n                if (!response.ok) {\n                    throw new Error(\\`API Error: \\${response.statusText}\\`);\n                }\n\n                const data = await response.json();\n                container.innerHTML = (data.text ? data.text + '<br><br>' : '<p>No insights received.</p><br><br>');\n\n\n            } catch (error) {\n                container.innerHTML = \\`<p style=\"color: #ff8a8a;\">Error: Could not fetch insights. \\${error.message}</p>\\`;\n            } finally {\n                button.disabled = false;\n                button.textContent = 'Click Here to get more Insights ✨';\n            }\n        }\n    </script>\n</body>\n</html>\n`;\n\nreturn [{ json: { html: htmlContent } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [120, 780], "id": "d11fdc42-1c42-4b15-8024-b21882bfcc3b", "name": "Wrap dynamic data to HTML with javascript"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-200, 1320], "id": "38dd17fc-f6a6-40ae-8848-1359516c0fe6", "name": "Simple Memory"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-560, -420], "id": "6296fb75-016d-4b44-8b45-6d2fa68df5b4", "name": "Get trending news about AI"}, {"parameters": {"description": "Call this tool if the user is asking about any news about trends in github repo, product hunt, and hacker news.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-80, 1320], "id": "c9cab627-eba5-42f8-aeb3-101ddd9150c4", "name": "Get trending news and products"}, {"parameters": {"path": "get-trending-ai-insights", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-560, 120], "id": "969eb8f0-ab6b-4b12-9eb8-01b0f1c83709", "name": "LLM API - Get Summary", "webhookId": "aa2ace9e-6b82-4686-9ee3-7b84ad59aa46"}, {"parameters": {"content": "# AI-Powered Tech Trend Dashboard\n\nThis workflow is designed to create a dynamic webpage that aggregates and displays the latest trending information from GitHub, Hacker News, and Product Hunt. It also includes an AI-powered chat agent and an on-demand insights generation feature.\n\n![](https://i.imgur.com/FkUGAcY.png)", "height": 860, "width": 840}, "type": "n8n-nodes-base.stickyNote", "position": [-1620, -780], "typeVersion": 1, "id": "886b0170-26db-4352-accd-a4df1e2afe38", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Part 1: Get Trending from Product Hunt, Github", "height": 760, "width": 2180}, "type": "n8n-nodes-base.stickyNote", "position": [-700, -780], "typeVersion": 1, "id": "d84d3b4f-dbe2-4b84-9cd6-27ab801a9dab", "name": "Sticky Note1"}, {"parameters": {"authentication": "oAuth2", "endpoint": "https://api.producthunt.com/v2/api/graphql", "requestFormat": "json", "query": "=query PostRanking{\n  posts(postedAfter:\"{{new Date(new Date(Date.now()).getTime() - (1000*60*60*1*24)).toUTCString()}}\", order:RANKING, first:20, postedBefore:\"{{new Date(Date.now()).toUTCString()}}\"){\n    edges {\n      node {\n        name\n        tagline\n        description\n        votesCount\n        reviewsRating\n        url\n      }\n    }\n  }\n}", "headerParametersUi": {"parameter": [{"name": "Authorization", "value": "Bearer YOUR-TOKE<PERSON>"}]}}, "name": "Get Product Hunt's trending", "type": "n8n-nodes-base.graphql", "position": [540, -220], "typeVersion": 1, "id": "7a31d829-73e4-45d1-946d-a211d3c42035", "credentials": {"oAuth2Api": {"id": "uvCHddty3vzfi2Du", "name": "Product Hunt OAuth2.0"}}}, {"parameters": {"content": "Remember to add the OAuth 2.0 credentials here for Product Hunt: https://www.producthunt.com/v2/oauth/applications/new", "height": 140, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [260, -220], "typeVersion": 1, "id": "b08f8697-138a-4180-9c33-1597ba2b7346", "name": "Sticky Note2"}, {"parameters": {"content": "## Part 2: API endpoint which analyze Trending Tech & Product Data from Product Hunt, Hacker News, and Github", "height": 440, "width": 2180}, "type": "n8n-nodes-base.stickyNote", "position": [-700, 20], "typeVersion": 1, "id": "5f051d95-312e-46d2-82ec-8e60ba46b01d", "name": "Sticky Note3"}, {"parameters": {"promptType": "define", "text": "=Analyze the latest trending data from Product Hunt, Hacker News, and GitHub. Use bigram or trigram frequency (or similar techniques) to identify the most commonly mentioned topics or keywords. Based on those, determine the dominant trends or themes. Then, highlight a few notable products, projects, or news items that align with those key topics. Conclude with a short (under 100 words), friendly recommendation suggesting specific tools or news the user should explore right now.\n\nSample response:\n```\nBased on the trending data, several key themes emerge:\n\nAI and LLMs: A significant portion of the trending items revolve around AI, Large Language Models (LLMs), and AI agents. Projects like \"WebAgent,\" \"Biomni,\" \"Hands-On-Large-Language-Models,\" and \"verl\" highlight the continued interest in developing and applying AI technologies. The launch of \"Grok 4\" and related discussions further emphasize this trend.\nDeveloper Tools and Productivity: Several items focus on tools that enhance developer productivity, including \"Terraform,\" \"Helm,\" and \"pybind11.\" This indicates a strong interest in streamlining development workflows and infrastructure management.\nAutomation and Efficiency: There's a noticeable trend towards automation, particularly in areas like web browsing (\"MCP-B\"), API backend development (\"Builduo\"), and even automating the process of making money online (\"MoneyPrinterV2\").\nAI-Powered Solutions for Various Niches: Many new products leverage AI to solve specific problems, such as \"HelloCV AI\" for resume/CV enhancement, \"HeronAI\" for business analytics, and \"LLM SEO Index Crawler Check\" for SEO optimization.\nNotable Products/Projects:\n\nWebAgent (Github): A web agent for information seeking.\nGrok 4 (Product Hunt/Hacker News): xAI's advanced AI model.\nBiomni (Github/Hacker News): A general-purpose biomedical AI agent.\nMCP-B (Hacker News): A Protocol for AI Browser Automation\nHelloCV AI (Product Hunt): AI-powered personal site creation from a CV.\nRecommendation:\n\nGiven the strong trends around AI and developer tools, I recommend exploring \"Grok 4\" to see the latest advancements in AI models. For developers, \"MCP-B\" and \"Terraform\" could be valuable tools to investigate for browser automation and infrastructure management, respectively. Also, check out \"HelloCV AI\" if you want a quick and easy way to create a personal website from your resume using AI.\n```\n\nInput data about trending on Product Hunt, Hacker News, and Github:\n```\n{{ JSON.stringify($json) }}\n```\n\nFormat the response using markdown with section headings starting from level 2 (##).", "messages": {"messageValues": [{"message": "You are a helpful assistant to answer user's query which asks for the latest trending information from Product Hunt, Hacker News, or GitHub"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [100, 120], "id": "0e8c025b-898d-4aaf-b0f3-00966c45d01c", "name": "Analyze Trending Data", "executeOnce": true, "retryOnFail": true, "waitBetweenTries": 5000}, {"parameters": {"workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-340, 120], "id": "3513e53f-894a-4885-bdcf-63ebccc7b7c7", "name": "Execute Workflow - Get trending news about AI"}, {"parameters": {"workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-100, 780], "id": "65b48c4f-f1bb-46bb-95d0-0d895f4e88e0", "name": "Execute Workflow - Get trending news about AI1"}, {"parameters": {"mode": "markdownToHtml", "markdown": "={{ $json.text }}", "destinationKey": "text", "options": {"simpleLineBreaks": false, "smartIndentationFix": false}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [480, 120], "id": "880bdaa1-3d97-46e9-a583-bb0d84132fbe", "name": "Convert Markdown to HTML content"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [680, 120], "id": "34a9d07e-274d-433d-a0a8-80fbe68c5864", "name": "End of task for this API endpoint"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "source"}, {"fieldToAggregate": "name"}, {"fieldToAggregate": "url"}, {"fieldToAggregate": "description"}, {"fieldToAggregate": "engagement_score"}, {"fieldToAggregate": "created_at"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [-120, 120], "id": "5b72a504-1af7-4d22-bce2-ab1b0c7d6ace", "name": "Aggregate JSON input to be fed into LLM"}, {"parameters": {"content": "## Part 3: Display the Trending Data as a Website", "height": 460, "width": 2180}, "type": "n8n-nodes-base.stickyNote", "position": [-700, 520], "typeVersion": 1, "id": "8c3e490c-89c1-4dc6-9345-90f673452887", "name": "Sticky Note4"}, {"parameters": {"path": "ai-tech-news", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-540, 780], "id": "746a6f92-d5e6-43fa-9fd5-c5165d0827fb", "name": "Website API", "webhookId": "cd365253-680b-433e-a6ec-f9e1acbcd96c"}, {"parameters": {"respondWith": "text", "responseBody": "={{ $json.html }}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [340, 780], "id": "821e0cac-00fd-4ae0-a153-377e467aeba3", "name": "Display Website"}, {"parameters": {"assignments": {"assignments": [{"id": "af418c5d-05eb-4086-bcb9-21b0ae578a23", "name": "chatWebhookUrl", "value": "=https://n8n.tanyongsheng.site/webhook/c70e463d-26d2-4514-adda-a0605bf9a159/chat", "type": "string"}, {"id": "66708175-68df-4694-8917-1c1b725c5830", "name": "insightsApiUrl", "value": "=https://n8n.tanyongsheng.site/webhook/get-trending-ai-insights", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-320, 780], "id": "f39a4ec9-33a6-4c2d-9692-a8e37299800d", "name": "Set Configuration for Website API"}, {"parameters": {"content": "Remember to copy:\n1. the URL from the chat trigger node: \"AI Chatbot on website\" for `chatWebhookUrl` parameter\n2. the URL from the webhook trigger node: \"LLM API - Get Summary\" for `insightsApiUrl` parameter", "height": 380, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-400, 580], "typeVersion": 1, "id": "ffbc1e5f-4553-43a5-ba53-0cac215b813f", "name": "Sticky Note5"}, {"parameters": {"public": true, "mode": "webhook", "options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-540, 1100], "id": "0157aa56-ed8e-4d5a-a8fe-9566663cc45b", "name": "AI Chatbot on website", "webhookId": "c70e463d-26d2-4514-adda-a0605bf9a159"}, {"parameters": {"options": {"systemMessage": "You are a helpful assistant. When the user asks anything about trending products, companies, or technologies — including asking about specific product names — always respond by calling the tool named \"Get trending news and products\" to retrieve the latest data from Product Hunt, Hacker News, or GitHub. Use the tool output to answer the user's question as accurately as possible. If the product is not found, let the user know it may not be trending or recent."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-288, 1100], "id": "79492f33-ae98-481c-9ada-0134b4f2106c", "name": "AI agent"}, {"parameters": {"content": "## Part 4: AI Chatbot embedded on the website", "height": 460, "width": 2180}, "type": "n8n-nodes-base.stickyNote", "position": [-700, 1020], "typeVersion": 1, "id": "972021ff-eefe-4bfe-a9d1-bc397a97626c", "name": "Sticky Note6"}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-360, 1320], "id": "98ba10d0-ee49-4fa3-97d3-9542c4520761", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "nmExFjYge4V2gn5h", "name": "Google Gemini - <EMAIL>"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {"maxOutputTokens": 1000}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [20, 320], "id": "5f104ed8-be35-4840-b8b4-b400b6f7fe81", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "nmExFjYge4V2gn5h", "name": "Google Gemini - <EMAIL>"}}}], "pinData": {}, "connections": {"Extract Box": {"main": [[{"node": "Extract all repositories", "type": "main", "index": 0}]]}, "Turn to a list": {"main": [[{"node": "Extract repository data", "type": "main", "index": 0}]]}, "Extract repository data": {"main": [[{"node": "Set Result Variables for Github Trending", "type": "main", "index": 0}]]}, "Extract all repositories": {"main": [[{"node": "Turn to a list", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Get metadata for each github repo", "type": "main", "index": 0}]]}, "Get Hacker News' Trending": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Get Github Trending": {"main": [[{"node": "Extract Box", "type": "main", "index": 0}]]}, "Get metadata for each github repo": {"main": [[{"node": "Set Result Variables for HackerNews Trending", "type": "main", "index": 0}]]}, "Set Result Variables for Product Hunt's Trending": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "Set Result Variables for Github Trending": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Set Result Variables for HackerNews Trending": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[]]}, "Wrap dynamic data to HTML with javascript": {"main": [[{"node": "Display Website", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI agent", "type": "ai_memory", "index": 0}]]}, "Get trending news about AI": {"main": [[{"node": "Get Github Trending", "type": "main", "index": 0}, {"node": "Get Product Hunt's trending", "type": "main", "index": 0}, {"node": "Get Hacker News' Trending", "type": "main", "index": 0}]]}, "Get trending news and products": {"ai_tool": [[{"node": "AI agent", "type": "ai_tool", "index": 0}]]}, "LLM API - Get Summary": {"main": [[{"node": "Execute Workflow - Get trending news about AI", "type": "main", "index": 0}]]}, "Get Product Hunt's trending": {"main": [[{"node": "Set Result Variables for Product Hunt's Trending", "type": "main", "index": 0}]]}, "Analyze Trending Data": {"main": [[{"node": "Convert Markdown to HTML content", "type": "main", "index": 0}]]}, "Execute Workflow - Get trending news about AI": {"main": [[{"node": "Aggregate JSON input to be fed into LLM", "type": "main", "index": 0}]]}, "Execute Workflow - Get trending news about AI1": {"main": [[{"node": "Wrap dynamic data to HTML with javascript", "type": "main", "index": 0}]]}, "Convert Markdown to HTML content": {"main": [[{"node": "End of task for this API endpoint", "type": "main", "index": 0}]]}, "Aggregate JSON input to be fed into LLM": {"main": [[{"node": "Analyze Trending Data", "type": "main", "index": 0}]]}, "Website API": {"main": [[{"node": "Set Configuration for Website API", "type": "main", "index": 0}]]}, "Set Configuration for Website API": {"main": [[{"node": "Execute Workflow - Get trending news about AI1", "type": "main", "index": 0}]]}, "AI Chatbot on website": {"main": [[{"node": "AI agent", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI agent", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "Analyze Trending Data", "type": "ai_languageModel", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "02841dfd-8ae7-4903-b3ff-cb088ca57d06", "meta": {"templateCredsSetupCompleted": true, "instanceId": "026c7d9c79f996f7b3cdb95e16f2001d74b6d84760304809dd5c24556eef89aa"}, "id": "E2wa76Lhe23Ai1eL", "tags": []}