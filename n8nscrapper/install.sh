#!/bin/bash

echo "🚀 Installing N8N Workflow Scraper..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version 16+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install Node.js dependencies"
    exit 1
fi

echo "✅ Node.js dependencies installed"

# Check if Python is available (optional)
if command -v python3 &> /dev/null; then
    echo "🐍 Python 3 detected, installing Python dependencies..."
    pip3 install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        echo "✅ Python dependencies installed"
    else
        echo "⚠️  Failed to install Python dependencies (optional)"
    fi
else
    echo "⚠️  Python 3 not found. Python dependencies skipped (optional)."
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs workflows browser-data

# Copy environment file
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from template"
    echo "📝 Please review and modify .env file as needed"
else
    echo "✅ .env file already exists"
fi

# Make scripts executable
chmod +x src/cli.js
chmod +x src/scraper.js

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Review and modify .env file if needed"
echo "2. Run the scraper:"
echo "   npm start"
echo "   or"
echo "   node src/scraper.js"
echo ""
echo "📖 For more options:"
echo "   node src/cli.js --help"
echo ""
echo "🔧 Configuration:"
echo "   Edit .env file to customize settings"
echo ""
