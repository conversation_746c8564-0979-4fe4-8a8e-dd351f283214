{"name": "Deep Company Research/Linkedin Workflow", "nodes": [{"parameters": {"method": "POST", "url": "https://api.firecrawl.dev/v1/scrape", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "url", "value": "=https://www.trustpilot.com/review/{{ $json.website }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1900, 940], "id": "fc77c2f1-d551-457e-bdd8-f8a0d85a3453", "name": "Trustpilot Review", "credentials": {"httpHeaderAuth": {"id": "hzdaPGuP3lllSl9X", "name": "Firecrawl api"}}}, {"parameters": {"assignments": {"assignments": [{"id": "1d441989-25fa-4c17-aefa-fe48e2aa5f73", "name": "data.metadata.ogTitle", "value": "={{ $json.data.markdown }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2120, 940], "id": "b3cce19e-e83f-4e1e-907f-01f5c3665bd3", "name": "<PERSON>"}, {"parameters": {"jsCode": "// Get the input data\nconst inputItems = items;\nconst outputItems = [];\n\nfor (let i = 0; i < inputItems.length; i++) {\n  try {\n    const item = inputItems[i].json;\n    \n    // Get the main content\n    const content = item?.data?.metadata?.ogTitle || \"\";\n    \n    // Extract company name\n    let companyName = \"Unknown\";\n    const companyNameMatch = content.match(/# ([A-Za-z0-9]+)Reviews(\\d+)/);\n    if (companyNameMatch) {\n      companyName = companyNameMatch[1];\n    }\n    \n    // Extract trust score\n    let trustScore = \"N/A\";\n    const trustScoreMatch = content.match(/TrustScore (\\d+\\.?\\d*) out of 5/);\n    if (trustScoreMatch) {\n      trustScore = trustScoreMatch[1];\n    }\n    \n    // Extract total reviews\n    let totalReviews = \"0\";\n    const totalReviewsMatch = content.match(/Reviews(\\d+)/);\n    if (totalReviewsMatch) {\n      totalReviews = totalReviewsMatch[1];\n    }\n    \n    // Extract reviews using a different approach\n    const reviews = [];\n    \n    // Find all review blocks\n    // Look for patterns like \"![Rated X out of 5 stars]\"\n    const ratingMatches = [...content.matchAll(/!\\[Rated (\\d) out of 5 stars\\]/g)];\n    \n    for (let j = 0; j < ratingMatches.length; j++) {\n      const ratingMatch = ratingMatches[j];\n      const rating = ratingMatch[1]; // This contains the actual rating (1-5)\n      \n      // Get the position where this rating appears in the content\n      const startPos = ratingMatch.index;\n      \n      // Define the end position (either the next rating or the end of content)\n      const endPos = (j < ratingMatches.length - 1) ? ratingMatches[j + 1].index : content.length;\n      \n      // Extract this review's block of text\n      const reviewBlock = content.substring(startPos, endPos);\n      \n      // Extract review title\n      let title = \"\";\n      const titleMatch = reviewBlock.match(/\\*\\*([^*]+)\\*\\*/);\n      if (titleMatch) {\n        title = titleMatch[1].trim();\n      } else {\n        // Try alternate title formats\n        const altTitleMatch = reviewBlock.match(/\\n\\n\\[([^\\]]+)\\]/);\n        if (altTitleMatch) {\n          title = altTitleMatch[1].trim();\n        }\n      }\n      \n      // Extract review content - find text after the rating that's not part of metadata\n      let reviewContent = \"\";\n      // First look for text between the title and \"Date of experience\"\n      if (reviewBlock.includes(\"**Date of experience:**\")) {\n        const textBeforeDate = reviewBlock.split(\"**Date of experience:**\")[0];\n        \n        // Extract the main content, skipping the title and other metadata\n        const contentMatch = textBeforeDate.match(/\\]\\(https:\\/\\/www\\.trustpilot\\.com\\/reviews\\/[^)]+\\) ([\\s\\S]+)$/);\n        if (contentMatch) {\n          reviewContent = contentMatch[1].trim();\n        } else {\n          // If no URL pattern, try a simpler approach\n          const simpleMatch = textBeforeDate.match(/\\n\\n([^*\\n]+(?:\\n[^*\\n]+)*)\\n\\n$/);\n          if (simpleMatch) {\n            reviewContent = simpleMatch[1].trim();\n          }\n        }\n      } else {\n        // If there's no date pattern, get content after any metadata\n        const contentMatch = reviewBlock.match(/\\n\\n([^*\\n\\[]+(?:\\n[^*\\n\\[]+)*)\\n*$/);\n        if (contentMatch) {\n          reviewContent = contentMatch[1].trim();\n        }\n      }\n      \n      // Extract experience date\n      let experienceDate = \"\";\n      const expDateMatch = reviewBlock.match(/\\*\\*Date of experience:\\*\\* ([^\\n]+)/);\n      if (expDateMatch) {\n        experienceDate = expDateMatch[1].trim();\n      }\n      \n      // Fallback for review content - if nothing extracted properly\n      if (!reviewContent && title) {\n        // Just get all text in the block after the title\n        const titlePos = reviewBlock.indexOf(title);\n        if (titlePos > -1) {\n          reviewContent = reviewBlock.substring(titlePos + title.length).replace(/^\\*+|\\n+|\\*+$/g, '').trim();\n        }\n      }\n      \n      // Add the review if we have at least something\n      if (title || reviewContent) {\n        reviews.push({\n          rating: rating,\n          title: title || \"No title\",\n          content: reviewContent || \"No content\",\n          experience_date: experienceDate\n        });\n      }\n    }\n    \n    // Output the structured data\n    outputItems.push({\n      json: {\n        company: companyName,\n        total_reviews: totalReviews,\n        trust_score: trustScore,\n        reviews: reviews\n      }\n    });\n  } catch (error) {\n    // If there's an error, output a default object\n    outputItems.push({\n      json: {\n        company: \"Error extracting data\",\n        total_reviews: \"0\",\n        trust_score: \"N/A\",\n        reviews: [],\n        error: error.message\n      }\n    });\n  }\n}\n\nreturn outputItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2340, 940], "id": "cdf99048-203e-4ed5-bd27-93f3b4e203a2", "name": "Code"}, {"parameters": {"promptType": "define", "text": "=Company Name: {{ $json.company }}\n\nReviews: {{ $('Edit Fields').item.json.data.metadata.ogTitle }}\n\nExpected Output:\n\n{{ $json.company }} - Customer Review Summary\n\nOverall Company Feedback: Summarize the general sentiment about the company based on customer experiences. Highlight both positive and negative aspects.\n\nProduct Sentiment Analysis: Identify key products or services mentioned in the reviews. For each product, indicate whether the sentiment is mostly positive, negative, or mixed. Example:\n\nProduct: Positive sentiment\nProduct: Negative sentiment\nKey Improvement Areas: Suggest the top 2-3 areas where the company can improve based on the negative feedback.\n\nEnsure that the final output starts with the company name as the heading, followed by a structured summary."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [2560, 940], "id": "398d5514-99c4-4dac-b9cb-6165985b17f5", "name": "Trustpilot Review Analyser"}, {"parameters": {"url": "=https://r.jina.ai/{{ $('Google Sheets2').item.json.website }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {Your Jina API}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2936, 940], "id": "5ac2ec60-81f3-48c5-80a7-8c869275102f", "name": "HTTP Request"}, {"parameters": {"text": "=## Task Overview\nYou are provided with the text content from a company's website. Your task is to analyze this content and generate a comprehensive company profile in JSON format.\n\nHere is the website content: {{ $json.data }}\n\n## Objective\nCreate a structured JSON object that captures key information about the company, including:\n\nCompany basics (name, industry, location)\nBusiness overview\nProducts/Services\nKey features and value propositions\nTarget audience/market\nCompany culture and values (if available)\n\n## Output Format\nThe output should be a valid JSON object with the following structure:\n{\n    \"company_basics\": {\n        \"name\": \"\",\n        \"industry\": \"\",\n        \"headquarters\": \"\",\n        \"founded\": \"\",\n        \"website\": \"\"\n    },\n    \"business_overview\": {\n        \"description\": \"\",\n        \"mission_statement\": \"\",\n        \"key_offerings\": []\n    },\n    \"products_and_services\": {\n        \"main_offerings\": [],\n        \"key_features\": [],\n        \"pricing_model\": \"\"\n    },\n    \"market_position\": {\n        \"target_audience\": \"\",\n        \"unique_value_proposition\": \"\",\n        \"competitive_advantages\": []\n    },\n    \"company_culture\": {\n        \"values\": [],\n        \"work_environment\": \"\",\n        \"team_highlights\": \"\"\n    },\n    \"additional_info\": {\n        \"certifications\": [],\n        \"partnerships\": [],\n        \"notable_achievements\": []\n    }\n}\n\n## Instructions\n\nContent Analysis:\n\nCarefully review all provided website content\nExtract relevant information for each section\nFocus on factual information directly stated in the content\nDo not make assumptions or include external information\n\n\n## Data Organization:\n\n- Organize information into appropriate categories\n- Use clear, concise language\n- Maintain original terminology where appropriate\n- Include specific details when available\n\n\n## Quality Guidelines:\n\n- Ensure accuracy of extracted information\n- Use professional language\n- Avoid marketing language or subjective claims\n- Include only information present in the source content\n\n\n## Field-Specific Guidelines:\n\n### Company Basics:\n\n- Extract official company name and location\n- Identify primary industry sector\n- Include founding date if available\n- Include official website URL\n\n### Business Overview:\n\n- Summarize main business activities\n- Include mission statement if explicitly stated\n- List primary offerings or services\n\n### Products and Services:\n\n- List main products/services\n- Detail key features and capabilities\n- Include pricing model information if available\n\n### Market Position:\n\n- Identify stated target market\n- Extract unique selling propositions\n- List competitive advantages mentioned\n\n### Company Culture:\n\n- Include stated company values\n- Describe work environment if mentioned\n- List team/employee highlights\n\n## Additional Information:\n\n- Include relevant certifications\n- List notable partnerships\n- Include significant achievements\n\n\n### Handling Missing Information:\n\n- If information for a field is not available, use null\n- Do not make assumptions to fill gaps\n- Only include explicitly stated information\n\n\n\n## Validation Checklist\n\n- All JSON syntax is valid\n- Information is accurately extracted from source\n- No external information is included\n- Missing information is properly handled\n- Language is professional and objective\n- All sections are properly populated where information is available\n\n## Important Notes\n\n- Focus on extracting factual information only\n- Do not include subjective interpretations\n- Maintain original context of information\n- Exclude promotional or marketing language\n- Handle missing information appropriately\n- Ensure JSON structure remains consistent", "schemaType": "manual", "inputSchema": "{\n    \"company_basics\": {\n        \"name\": \"\",\n        \"industry\": \"\",\n        \"headquarters\": \"\",\n        \"founded\": \"\",\n        \"website\": \"\"\n    },\n    \"business_overview\": {\n        \"description\": \"\",\n        \"mission_statement\": \"\",\n        \"key_offerings\": []\n    },\n    \"products_and_services\": {\n        \"main_offerings\": [],\n        \"key_features\": [],\n        \"pricing_model\": \"\"\n    },\n    \"market_position\": {\n        \"target_audience\": \"\",\n        \"unique_value_proposition\": \"\",\n        \"competitive_advantages\": []\n    },\n    \"company_culture\": {\n        \"values\": [],\n        \"work_environment\": \"\",\n        \"team_highlights\": \"\"\n    },\n    \"additional_info\": {\n        \"certifications\": [],\n        \"partnerships\": [],\n        \"notable_achievements\": []\n    }\n}\n", "options": {"systemPromptTemplate": "You are an expert extraction algorithm.\nOnly extract relevant information from the text.\nIf you do not know the value of an attribute asked to extract, you may omit the attribute's value."}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [3156, 940], "id": "5ddb8d73-2dfd-40be-ae6d-b3be43f0fc82", "name": "Website Analysis"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [3532, 940], "id": "029291e6-c735-422e-99cb-e93f713a6165", "name": "Loop Over Items5", "alwaysOutputData": false}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [3972, 915], "id": "6db5310d-2869-44da-a4ea-324504af5f76", "name": "Wait4", "webhookId": "be28fef4-78dd-478a-a01d-4b19b9aa351a"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1nUgViiiLVCt2zqnul9Lwg26t4LfTVKypL7bQohSOLXA", "mode": "list", "cachedResultName": "Ultimate Company Research", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nUgViiiLVCt2zqnul9Lwg26t4LfTVKypL7bQohSOLXA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Scrape data", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nUgViiiLVCt2zqnul9Lwg26t4LfTVKypL7bQohSOLXA/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"company_name": "={{ $('Google Sheets2').item.json.company_name }}", "company_website": "={{ $json.website }}", "company_reviews": "={{ $('Trustpilot Review Analyser').item.json.text }}", "Company_business_profile": "={{ $('Website Analysis').item.json.output.business_overview.description }}\n\nKey offerings: {{ $('Website Analysis').item.json.output.business_overview.key_offerings[0] }}, {{ $('Website Analysis').item.json.output.business_overview.key_offerings[1] }}\n\nProducts and Services: {{ $('Website Analysis').item.json.output.products_and_services.main_offerings[0] }}, {{ $('Website Analysis').item.json.output.products_and_services.main_offerings[2] }}\n\nTarget Audience: {{ $('Website Analysis').item.json.output.market_position.target_audience }}\n\nUnique Values: {{ $('Website Analysis').item.json.output.market_position.unique_value_proposition }}\n\nCompetitive Advantage: {{ $('Website Analysis').item.json.output.market_position.competitive_advantages[0] }}, {{ $('Website Analysis').item.json.output.market_position.competitive_advantages[1] }} ,{{ $('Website Analysis').item.json.output.market_position.competitive_advantages[2] }}\n\nCompany Culture: {{ $('Website Analysis').item.json.output.company_culture.values[0] }}\n\nAcheivements: {{ $('Website Analysis').item.json.output.additional_info.notable_achievements[0] }} ,{{ $('Website Analysis').item.json.output.additional_info.notable_achievements[1] }} ,{{ $('Website Analysis').item.json.output.additional_info.notable_achievements[2] }}", "company_linkedin_extract": "={{ $json.description }}", "company_followers_on_linkedin": "={{ $json.follower_count }}", "company_about_us": "={{ $json.tagline }}", "company_industry": "={{ $('Website Analysis').item.json.output.company_basics.industry }}", "company_headquarters": "={{ $('GetCompanyInfos1').item.json.hq.city }}", "company_founded": "={{ $('GetCompanyInfos1').item.json.founded_year }}", "company_size": "={{ $('Wait4').item.json.company_size_on_linkedin }}"}, "matchingColumns": [], "schema": [{"id": "company_name", "displayName": "company_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_website", "displayName": "company_website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_reviews", "displayName": "company_reviews", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company_business_profile", "displayName": "Company_business_profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_linkedin_extract", "displayName": "company_linkedin_extract", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_followers_on_linkedin", "displayName": "company_followers_on_linkedin", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_about_us", "displayName": "company_about_us", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_industry", "displayName": "company_industry", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_size", "displayName": "company_size", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_headquarters", "displayName": "company_headquarters", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "company_founded", "displayName": "company_founded", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [3752, 940], "id": "b3e63b34-feac-46ef-afc2-65f212210f4c", "name": "Google Sheets1", "credentials": {"googleSheetsOAuth2Api": {"id": "NL2OcQ6fxWPJs5vu", "name": "Google Sheets account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1nUgViiiLVCt2zqnul9Lwg26t4LfTVKypL7bQohSOLXA", "mode": "list", "cachedResultName": "Ultimate Company Research", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nUgViiiLVCt2zqnul9Lwg26t4LfTVKypL7bQohSOLXA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": *********, "mode": "list", "cachedResultName": "Company list", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1nUgViiiLVCt2zqnul9Lwg26t4LfTVKypL7bQohSOLXA/edit#gid=*********"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1680, 940], "id": "7870bfec-f263-44d6-acda-30e69ef0534b", "name": "Google Sheets2", "credentials": {"googleSheetsOAuth2Api": {"id": "NL2OcQ6fxWPJs5vu", "name": "Google Sheets account"}}}, {"parameters": {"url": "=https://nubela.co/proxycurl/api/linkedin/company?url={{ $('Google Sheets2').item.json.linkedin_profile_url }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3752, 740], "id": "9f805b1f-9830-4ff4-b309-064d42fa2e81", "name": "GetCompanyInfos1", "credentials": {"httpHeaderAuth": {"id": "GgXZrxlOmHHf28yQ", "name": "Neb<PERSON> api"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [1460, 940], "id": "2df5a1af-3498-4339-b0a8-26498ae462a4", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-06-05", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2648, 1160], "id": "925f10af-5de4-48e9-b234-2daa0caceb8c", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "nscaoDHmHaSsUvs0", "name": "Google Gemini(PaLM) Api account"}}}], "pinData": {}, "connections": {"Trustpilot Review": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Trustpilot Review Analyser", "type": "main", "index": 0}]]}, "Trustpilot Review Analyser": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Website Analysis", "type": "main", "index": 0}]]}, "Loop Over Items5": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}], [{"node": "GetCompanyInfos1", "type": "main", "index": 0}]]}, "Wait4": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}]]}, "Website Analysis": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}]]}, "Google Sheets2": {"main": [[{"node": "Trustpilot Review", "type": "main", "index": 0}]]}, "GetCompanyInfos1": {"main": [[{"node": "Wait4", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Google Sheets2", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Trustpilot Review Analyser", "type": "ai_languageModel", "index": 0}, {"node": "Website Analysis", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "2bb43c50-2a36-4be4-a118-f60821b5520d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0f6b06392e6ba9de49771585b7e4cdeacdf7cdc06484fbe6acd8ba3e84e73b87"}, "id": "ZQfeVLeD0TbQe5rd", "tags": []}