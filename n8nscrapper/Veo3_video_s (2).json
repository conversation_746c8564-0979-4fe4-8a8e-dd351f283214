{"name": "Veo3 video's", "nodes": [{"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [1460, 1380], "id": "494be2e0-d914-48b7-a527-23744bfbb029", "name": "Schedule Trigger"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1bgLjO2e8eV9WjwfLmqgdftXJlit4HgfR2ddufqocNAA", "mode": "list", "cachedResultName": " Veo3 ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1bgLjO2e8eV9WjwfLmqgdftXJlit4HgfR2ddufqocNAA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/16xf34Rc-TWs7rUCSFAsIS_1zaodLQsjN5dTaWMGlZOE/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"id": "==ROW()-1", "idea": "={{ $json.output[0].Idea }}", "caption": "={{ $json.output[0].Caption }}", "production": "={{ $json.output[0].Status }}", "environment_prompt": "={{ $json.output[0].Environment }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "idea", "displayName": "idea", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "caption", "displayName": "caption", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "production", "displayName": "production", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "environment_prompt", "displayName": "environment_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "final_output", "displayName": "final_output", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2020, 1380], "id": "d94e0d4b-a5f8-4a0a-b138-e69fafc90eb2", "name": "Log the Idea", "credentials": {"googleSheetsOAuth2Api": {"id": "CeAHu441xLGxVGr0", "name": "Google Sheets account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [2240, 1140], "id": "044c7e27-1334-447a-ac6a-009679b7d212", "name": "Think"}, {"parameters": {"method": "POST", "url": "https://queue.fal.run/fal-ai/veo3", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "contentType": "raw", "rawContentType": "application/json", "body": "={ \"prompt\": \"{{ $json.output }}\" }\n", "options": {"batching": {"batch": {"batchSize": 1, "batchInterval": 2000}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1700, 1620], "id": "701d71af-443f-4b95-a137-2a85159f3c07", "name": "Create Video", "credentials": {"httpHeaderAuth": {"id": "9D5c3LBOQQLvIHey", "name": "Header Auth account"}}}, {"parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2140, 1620], "id": "1b3980ad-3ec8-44f1-b219-abe9f8b44c01", "name": "Get Video", "credentials": {"httpHeaderAuth": {"id": "9D5c3LBOQQLvIHey", "name": "Header Auth account"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1bgLjO2e8eV9WjwfLmqgdftXJlit4HgfR2ddufqocNAA", "mode": "list", "cachedResultName": " Veo3 ", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1bgLjO2e8eV9WjwfLmqgdftXJlit4HgfR2ddufqocNAA/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1bgLjO2e8eV9WjwfLmqgdftXJlit4HgfR2ddufqocNAA/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"idea": "={{ $('Log the Idea').first().json.idea }}", "production": "done", "final_output": "={{ $json.video.url }}"}, "matchingColumns": ["idea"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "idea", "displayName": "idea", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "caption", "displayName": "caption", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "production", "displayName": "production", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "environment_prompt", "displayName": "environment_prompt", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "final_output", "displayName": "final_output", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2400, 1620], "id": "d2849ad7-262f-40d9-b615-5359829b2b75", "name": "Log Final Video", "credentials": {"googleSheetsOAuth2Api": {"id": "CeAHu441xLGxVGr0", "name": "Google Sheets account"}}}, {"parameters": {"promptType": "define", "text": "Generate one immersive, cinematic short video idea based on the topic: {{ $json.topic }}. Format your output exactly as a single-line JSON array using the structure and rules defined in the system message.\n", "hasOutputParser": true, "options": {"systemMessage": "=Generate 1 immersive, cinematic short video idea based on the topic: {{ $json.topic }}.\n\nYour idea should feel like a real moment caught on camera — eerie, mysterious, surprising, or emotionally charged.\n\n🔒 RULES:\n- Only return 1 idea.\n- Keep it short and vivid (under 50 words).\n- Use **strong visual language**.\n- It must describe the full scene in motion: WHO, WHERE, WHAT happens, and how the camera captures it.\n- Use second-person or third-person cinematic POV.\n- Add ambient details: sounds, lighting, motion, mood.\n\n📽️ OUTPUT FORMAT (JSON, single-line):\n\n[\n  {\n    \"Scene\": \"Your immersive short scene here (e.g. man sees a double in subway, reacts, camera shakes)\",\n    \"Status\": \"for production\"\n  }\n]\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1660, 1380], "id": "f17614c8-098d-4d3d-bae9-391d4616d0d8", "name": "Ideas AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "gpt-4.1"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1800, 1160], "id": "b560bb80-f86e-4167-acc9-9903e8d6daa6", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "orpgZXeEUU3PfnlP", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Give me a Veo3 prompt for this idea:\n{{ $json.idea }}\n\nThis is the environment:\n{{ $json.environment_prompt }}\n\n", "hasOutputParser": true, "options": {"systemMessage": "=You are an AI screenwriter that takes vivid short video ideas and turns them into realistic, cinematic prompts designed for AI video generation.\n\nThe output must read like a scene pulled from a short film — immersive, shot handheld, and believable. Use first-person or third-person language. The style should mimic a human recording something strange or emotional on their phone.\n\nYour goal: Convert the provided “scene idea” into a full video prompt that describes:\n- What's happening visually\n- How the subject is moving or reacting\n- What the camera sees and how it moves\n- What can be heard in the background\n- The atmosphere or emotional tone\n\nKeep the output:\n- As one single paragraph\n- Between 750–1500 characters\n- Realistic, grounded, and fully visual\n- No formatting, no titles, no line breaks\n\nDo not name the character. Do not include more than one human character. The tone should feel natural — like a real found clip someone might capture during a strange or emotional moment. Use sensory detail to enhance immersion (sounds, shadows, flickering lights, breath, weather, movement, etc).\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [2220, 1380], "id": "c5f4309c-785e-43ff-87fa-7180f082cc3f", "name": "Prompts AI Agent"}, {"parameters": {"amount": 600}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1920, 1620], "id": "561b6a47-439a-40a9-8681-47bcc362f4a0", "name": "Wait for Veo3", "webhookId": "a7bf329e-b1ca-4f59-b954-d57e551210f6"}, {"parameters": {"jsonSchemaExample": "[\n  {\n    \"Caption\": \"Diver Removes Nets Off Whale 🐋 #whalerescue #marinelife #oceanrescue #seahelpers #love #nature #instagood #explore #viral #savenature #oceanguardians #cleanoceans\",\n    \"Idea\": \"<PERSON><PERSON> carefully cuts tangled net from distressed whale in open sea\",\n    \"Environment\": \"Open ocean, sunlight beams through water, diver and whale, cinematic realism\",\n    \"Status\": \"for production\"\n  }\n]\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2000, 1160], "id": "473799c1-d1ca-4bf6-ba46-aff2777aa760", "name": "<PERSON><PERSON><PERSON>"}], "pinData": {}, "connections": {"Log the Idea": {"main": [[{"node": "Prompts AI Agent", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Ideas AI Agent", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "Ideas AI Agent", "type": "ai_tool", "index": 0}, {"node": "Prompts AI Agent", "type": "ai_tool", "index": 0}]]}, "Create Video": {"main": [[{"node": "Wait for Veo3", "type": "main", "index": 0}]]}, "Get Video": {"main": [[{"node": "Log Final Video", "type": "main", "index": 0}]]}, "Ideas AI Agent": {"main": [[{"node": "Log the Idea", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Ideas AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "Prompts AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Prompts AI Agent": {"main": [[{"node": "Create Video", "type": "main", "index": 0}]]}, "Wait for Veo3": {"main": [[{"node": "Get Video", "type": "main", "index": 0}]]}, "Parser": {"ai_outputParser": [[{"node": "Ideas AI Agent", "type": "ai_outputParser", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "14612639-6349-4d07-824e-9d4db5338f13", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e8cfc05d8e90a08f695217f193e26ee3d714795344c268045825ab016f014958"}, "id": "WkxRnhogkid283jj", "tags": []}