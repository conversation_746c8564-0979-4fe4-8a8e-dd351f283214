{"name": "<PERSON><PERSON><PERSON>l", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-540, 120], "id": "36631571-0ae6-4d15-a1eb-555ba077e501", "name": "<PERSON>eg<PERSON>", "webhookId": "eec6aedd-f880-4170-ae9e-357345afc279", "credentials": {"telegramApi": {"id": "mqY27fm9DRYO5bWP", "name": "<PERSON><PERSON><PERSON>l"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"leftValue": "={{ $json.message.text }}", "rightValue": "", "operator": {"type": "boolean", "operation": "exists", "singleValue": true}, "id": "a212d577-bd54-4c38-bee2-473e2ebd40cc"}], "combinator": "and"}, "renameOutput": true, "outputKey": "TEXT"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "loose", "version": 2}, "conditions": [{"id": "f27087ca-ffa2-4078-9ec5-c1548e1ddc59", "leftValue": "={{ $('Telegram Trigger').item.json.message.voice.file_id }}", "rightValue": "", "operator": {"type": "boolean", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "VOICE"}]}, "looseTypeValidation": true, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-300, 120], "id": "becc019c-4cb6-4c1a-a894-dd74f8116e4e", "name": "Switch"}, {"parameters": {"resource": "file", "fileId": "={{ $json.message.voice.file_id }}"}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-120, 220], "id": "1b33b7d3-6931-4350-bcfb-ee3b35729476", "name": "Get Audio", "webhookId": "7d05e543-1234-47c6-bcd3-ce65af6c2c54", "credentials": {"telegramApi": {"id": "mqY27fm9DRYO5bWP", "name": "<PERSON><PERSON><PERSON>l"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "binaryPropertyName": "=data", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [80, 220], "id": "faf1da8a-db10-4644-ac76-aa2d8ad52afa", "name": "Transcribe Voice", "credentials": {"openAiApi": {"id": "69u8soNsS7cRQgSV", "name": "<PERSON>'s OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "4589aba7-b996-4fa4-b320-7621285a27f2", "name": "text", "value": "={{ $json.message.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-20, 0], "id": "a0a22b9e-cd44-4777-8507-d3a8abb488ee", "name": "Set Text"}, {"parameters": {"promptType": "define", "text": "=Text to analyze: {{ $json.text }}", "hasOutputParser": true, "options": {"systemMessage": "=Eres un agente inteligente que tiene acceso a una herramienta capaz de investigar en internet.\n\nHerramientas:\n\n1. Perplexity Sonar: IA que puede buscar en internet\n\nInstrucciones:\n\nRecibirás un tema a investigar.\n\nDebes:\n\nExtrae las ideas principales que expliquen todo entendiblemente, el carrusel debe tener de 8-15 slides dependiendo la complejidad y extensión del contenido.\n\nEs importante que el carrusel siga un storytelling y sea congruente.\n\nLas ideas deben ser claras, relevantes y enfocadas en aportar valor, porque esto lo ocuparé para hacer un carrusel después, entonces debe contener información completa.\n\nIMPORTANTE:\n\nIgnora información irrelevante como menús, comentarios o contenido promocional.\n\nResponde en formato JSON, así:\n\n{\n  \"source\": \"url o tema\",\n  \"scraped_summary\": [\n    { \"id\": 1, \"idea\": \"Frase resumen de la idea 1\" },\n    { \"id\": 2, \"idea\": \"Frase resumen de la idea 2\" },\n    { \"id\": 3, \"idea\": \"Frase resumen de la idea 3\" },\n    { \"id\": 4, \"idea\": \"Frase resumen de la idea 4\" },\n    { \"id\": 5, \"idea\": \"Frase resumen de la idea 5\" }\n  ]\n}\n\nEs fundamental que las ideas estén organizadas siguiendo una estructura narrativa lógica (storytelling), de modo que cada slide fluya naturalmente hacia el siguiente y construya una secuencia coherente, atractiva y fácil de entender para el lector del carrusel."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [280, 80], "id": "********-aaf7-4327-b036-12a4e3162d5a", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [240, 400], "id": "7e6e0852-63a8-4d26-b96b-c258899781fa", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "69u8soNsS7cRQgSV", "name": "<PERSON>'s OpenAi account"}}}, {"parameters": {"toolDescription": "Internet search.", "method": "POST", "url": "https://api.perplexity.ai/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "content-type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{\n  JSON.stringify({\n    model: \"sonar\",\n    messages: [\n      {\n        role: \"user\",\n        content: `Investiga sobre el siguiente tema que el usuario quiere: ${$json.text} y dame un resumen con información actualizada y de calidad`\n      }\n    ],\n    max_tokens: 3000,\n    temperature: 0.4\n  })\n}}\n", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [420, 400], "id": "********-6cec-422b-bd87-e7a85508aff2", "name": "Perplexity", "credentials": {"httpHeaderAuth": {"id": "xU85xXaPe7l2kb4o", "name": "Perplexity API Key"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"source\": \"url o tema\",\n  \"scraped_summary\": [\n    { \"id\": 1, \"idea\": \"Frase resumen de la idea 1\" },\n    { \"id\": 2, \"idea\": \"Frase resumen de la idea 2\" },\n    { \"id\": 3, \"idea\": \"Frase resumen de la idea 3\" },\n    { \"id\": 4, \"idea\": \"Frase resumen de la idea 4\" },\n    { \"id\": 5, \"idea\": \"Frase resumen de la idea 5\" }\n  ]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [580, 400], "id": "9c8889d6-b4d5-4be1-a930-caca3dc0233e", "name": "Output Parser"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "51a892d0-bf7f-4121-aad7-6bcf3ddef2f3", "leftValue": "={{ $json.output.scraped_summary }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [660, 80], "id": "8909f229-0530-4e5e-956f-139293ae5b04", "name": "If"}, {"parameters": {"jsCode": "// Este código es para un nodo \"Code\" de tipo \"Function\" en n8n\n\n// Accedemos al arreglo de ideas dentro del output del mensaje\nconst input = items[0].json.output.scraped_summary;\n\n// Unimos todas las ideas en un solo string con formato de lista numerada\nconst ideasText = input.map(entry => `${entry.id}. ${entry.idea}`).join('\\n\\n');\n\n// Devolvemos el string como único output\nreturn [\n  {\n    json: {\n      ideas_text: ideasText\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, -40], "id": "8484631d-9081-4007-9c41-29a4ee41b2c5", "name": "Code"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "=Eres un copywriter especializado en contenido técnico para LinkedIn.\n\nVas a recibir un texto que resume conceptos clave extraídos de un artículo técnico.\n\nTu tarea:\nReescribir cada idea en 1 frase impactante, técnica y clara para un carrusel de LinkedIn.\n\nMantén el tema principal de cada idea (no cambies de tema).\n\nUsa estructura de mini historia en cada frase:\n→ Presenta brevemente el problema → Menciona el impacto → Insinúa solución o esperanza.\n\nEl tono debe ser profesional, informativo y atractivo (no sensacionalista, no clickbait vacío).\n\nCada frase debe tener entre 12 y 20 palabras máximo.\n\nEl número de slides dependerá del tema pero no te limites a un número.\n\nDevuélvelo en el siguiente formato JSON:\n{\n  \"slides\": [\n    { \"slide\": 1, \"text\": \"Frase lista para Slide 1\" },\n    { \"slide\": 2, \"text\": \"Frase lista para Slide 2\" },\n    { \"slide\": 3, \"text\": \"Frase lista para Slide 3\" },\n    { \"slide\": 4, \"text\": \"Frase lista para Slide 4\" },\n    { \"slide\": 5, \"text\": \"Frase lista para Slide 5\" }\n  ]\n}\n\nEJEMPLOS:\n\nIdea Original | Reescritura Mejorada\n\n84% de fraude en ATMs, necesidad de IoT seguro | \"El 84% de los fraudes en ATMs revelan la urgencia de reforzar la conectividad IoT como defensa principal.\"\n\nIoT encripta y detecta fraudes | \"La encriptación IoT permite detectar amenazas en tiempo real antes de que los atacantes actúen.\"\n\nRed de ATMs desconectados = vulnerabilidad | \"Un cajero desconectado es un blanco perfecto: el uptime IoT es vital para la seguridad continua.\"\n\nMonitoreo IoT detecta amenazas temprano | \"Con monitoreo IoT, los ataques se detectan y neutralizan antes de comprometer los cajeros automáticos.\"\n\nSeguridad integral: IoT + físico + humano | \"La seguridad efectiva combina conectividad segura, protección física y supervisión humana estratégica.\"\n\nSi recibes un texto que tú pienses que no hace mucho sentido, corrígelo y adáptalo para que haga sentido.\n", "role": "system"}, {"content": "={{ $json.ideas_text }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1180, -40], "id": "230f7259-3ab7-43f6-9927-214126166b3a", "name": "OpenAI", "credentials": {"openAiApi": {"id": "69u8soNsS7cRQgSV", "name": "<PERSON>'s OpenAi account"}}}, {"parameters": {"jsCode": "// Este código es para un nodo \"Code\" tipo \"Function\" en n8n\n\n// Accedemos al array de slides\nconst slides = items[0].json.message.content.slides;\n\n// Unimos todos los textos de los slides en un solo string\nconst combinedText = slides.map(slide => `${slide.slide}. ${slide.text}`).join('\\n\\n');\n\n// Devolvemos el resultado como un solo campo\nreturn [\n  {\n    json: {\n      combined_slides_text: combinedText\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 260], "id": "b1c0bcd9-16c0-45ed-80b2-f84dab11a756", "name": "Code1"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1", "mode": "list", "cachedResultName": "GPT-4.1"}, "messages": {"values": [{"content": "=Eres un agente IA experto en generar prompts de estilo infográfico para carruseles de LinkedIn.  \nSe te proporcionará el esquema del carrusel para cada slide y tu tarea es crear una única prompt final de alta calidad para cada frase, que sirva directamente como input para el modelo de imágenes de ChatGPT.\n\n**Tu salida** debe ser un JSON EXACTO con esta estructura, **incluyendo un elemento en prompts por cada slide recibido**:\n\n{\n  \"estilo\": \"infográfico\",\n  \"formato\": \"1:1\",\n  \"resolucion\": \"alta\",\n  \"tipografia\": \"Sora\",\n  \"prompts\": [\n    {\n      \"slide\": <número de slide 1>,\n      \"prompt\": \"<instrucción infográfica para slide 1>\"\n    },\n    {\n      \"slide\": <número de slide 2>,\n      \"prompt\": \"<instrucción infográfica para slide 2>\"\n    }\n    ...\n  ]\n}\n\nPara cada “prompt”:\n\n1. Inicia con “Estilo infográfico:”.\n2. Describe el fondo (por ejemplo: “fondo blanco con degradado gris, sección superior verde suave”).\n3. Menciona el gráfico o diagrama central (barras, pastel, embudo, íconos minimalistas).\n4. Detalla la ubicación y formato del texto (Fuente Sora, tamaño, color, posición).\n5. Incorpora el título y la descripción de forma clara.\n6. Añade íconos indicados, explicando su estilo lineal.\n7. Aplica la paleta de colores sugerida, usando los hex proporcionados o la paleta corporativa (verde, blanco).\n8. Asegura que el mensaje siga una narrativa de venta: guía al lector del problema a la acción final (CTA).\n\nColores: Azul Claro, Blanco  \nTipografía: Winky Rough  \nIconos: Minimalistas\n\n---\n\n**Ejemplo de output**\n\n{\n  \"slide\": 1,\n  \"prompt\": \"Estilo infográfico: Crea una portada para un carrusel de 5 slides titulado “Cómo vender tu producto en 5 pasos” alta resolución, fondo gris muy claro. En la parte superior, un gran título en Fuente Sora bold: “Cómo vender tu producto en 5 pasos”. Debajo, un ícono de megáfono en estilo lineal y un pequeño gráfico de barras estilizado que muestre un ligero crecimiento (10%). Usa colores corporativos (azul oscuro para el título, gris para texto secundario, highlight verde para detalles).\"\n},\n\n{\n  \"slide\": 2,\n  \"prompt\": \"Estilo infográfico: Identifica a tu audiencia. Fondo blanco con una sección superior en azul suave. A la izquierda, un diagrama de pastel dividido en tres segmentos (Mercado, Demografía, Necesidades) con porcentajes ficticios. A la derecha, un texto breve en Sora regular: “Paso 1: Conoce a tu cliente ideal” y debajo “Segmenta por comportamiento, edad y motivaciones”. Incluye pequeños iconos para persona, lupa y gráfico.\"\n},\n\n{\n  \"slide\": 3,\n  \"prompt\": \"Estilo infográfico: Define tu propuesta de valor. Fondo gris claro, encabezado en Sora bold: “Paso 2: Propuesta de valor única”. Debajo, tres columnas con íconos de bombilla, escudo y moneda, cada uno con un título corto (“Innovación”, “Confianza”, “ROI”) y una descripción en Sora regular de dos líneas. Usa líneas finas para separar columnas y puntos de color verde como bullets.\"\n},\n\n{\n  \"slide\": 4,\n  \"prompt\": \"Estilo infográfico: Supera las objeciones. Fondo blanco, diagrama de embudo de ventas en el centro con cuatro niveles (Conocimiento, Interés, Deseo, Acción), cada nivel en un tono distinto de azul. Al lado derecho, tres burbujas de texto en Sora: “¿Es caro?”, “¿Funciona para mí?”, “¿Y el soporte?”. Bajo cada burbuja, una flecha apuntando al embudo y una respuesta breve en verde: “Coste vs Valor”, “Casos de éxito”, “Atención 24/7”.\"\n},\n\n{\n  \"slide\": 5,\n  \"prompt\": \"Estilo infográfico: Cierra la venta y CTA. Fondo degradado suave de gris a blanco, encabezado en Sora bold: “Paso 5: Cierre y acción”. Incluye un ícono de apretón de manos y un gran botón simulado en verde con texto “Empieza hoy” en Sora white. Debajo, tres indicadores numerados en círculo (1, 2, 3) con textos breves: “Agenda demo”, “Prueba gratis”, “Recibe asesoría”.\"\n}\n\n---\n\nImportante:\n- No debes generar imágenes con fondo transparente.\n- Sé muy específico con el diseño visual. No uses frases vagas como “imagen atractiva”.\n- No repitas estructuras entre slides: cada prompt debe tener composición única.\n- Asegúrate de que los prompts sean útiles como instrucciones para un modelo de generación de imágenes.\n- No inventes datos o gráficos no sugeridos por el contexto del mensaje del slide.\n- Usa un estilo profesional, claro y persuasivo.\n\n", "role": "system"}, {"content": "={{ $json.combined_slides_text }}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1300, 260], "id": "8ffb4f6c-bb7d-45cb-911a-1aa78bf5b645", "name": "OpenAI1", "credentials": {"openAiApi": {"id": "69u8soNsS7cRQgSV", "name": "<PERSON>'s OpenAi account"}}}, {"parameters": {"fieldToSplitOut": "message.content.prompts", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1100, 560], "id": "8bb22765-68a6-4412-89d2-49c9b2e8f25f", "name": "Split Out"}, {"parameters": {"operation": "toBinary", "sourceProperty": "data[0].b64_json", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1500, 560], "id": "18eb61b0-6f23-4985-9544-deb42fee5ead", "name": "Convert to File"}, {"parameters": {"operation": "sendPhoto", "chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}\n", "binaryData": true, "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1680, 560], "id": "c15c5766-8c81-45c1-a74a-10abd3030f41", "name": "Telegram", "webhookId": "3a3bd19c-4e06-41f7-8b26-2e46ee225df4", "credentials": {"telegramApi": {"id": "mqY27fm9DRYO5bWP", "name": "<PERSON><PERSON><PERSON>l"}}}, {"parameters": {"method": "POST", "url": "https://api.openai.com/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "gpt-image-1"}, {"name": "prompt", "value": "={{ $json.prompt }}"}, {"name": "size", "value": "1024x1536"}, {"name": "quality", "value": "medium"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, 560], "id": "c859c552-d614-4ebf-ae14-f928f8084d74", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "mewLlumiVMTZbKWy", "name": "Open AI images"}}}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "text": "Lo siento, ha ocurrido un error, intentalo mas tarde", "additionalFields": {}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [880, 180], "id": "2d16c1ce-a489-424f-aa05-bcae82b23fe1", "name": "Error", "webhookId": "c8bf0d07-fe62-420d-8b91-db8670b53adc", "credentials": {"telegramApi": {"id": "mqY27fm9DRYO5bWP", "name": "<PERSON><PERSON><PERSON>l"}}}], "pinData": {"Split Out": [{"json": {"slide": 1, "prompt": "Estilo infográfico: Ilustra el reto de la IA en la personalización de mercados. Fondo blanco con un degradado azul claro en diagonal desde la esquina superior izquierda. Al centro, un gráfico de red neuronal conectado a varios iconos mínimos de usuarios y carritos de compras. En la parte superior, título grande en fuente Sora bold azul oscuro: “La IA enfrenta el reto de personalizar mercados”. Debajo, subtítulo en Sora regular gris: “Impactando la experiencia del usuario con precisión avanzada”. Usa íconos minimalistas en línea (usuario, target, chip de IA). Paleta de azul claro (#A6D0F5), blanco y detalles en azul oscuro (#1E4066), siguiendo narrativa de introducción al reto."}}, {"json": {"slide": 2, "prompt": "Estilo infográfico: Demuestra automatización con IA. Fondo azul claro con sección inferior en blanco estilizada en curva. A la izquierda, figura de ChatGPT (ícono cerebral digital) rodeado de íconos mínimos de texto, gr<PERSON><PERSON>o, y video, conectados por flechas lineales en azul oscuro. A la derecha, texto en Sora bold: “Herramientas como ChatGPT automatizan contenido complejo”. Abajo, en Sora regular gris: “Revoluciona la producción de textos, gráficos y videos”. Aplica colores azul claro y blanco, detalles en azul oscuro para énfasis."}}, {"json": {"slide": 3, "prompt": "Estilo infográfico: Explica análisis predictivo IA. Fondo blanco con una banda superior azul claro. En el centro, un gráfico de línea ascendente (estilo dashboard) con áreas sombreadas azul y puntos de datos. <PERSON><PERSON> al gráfico, ícono lineal de lupa sobre gráficos. Encabezado a la izquierda, Sora bold azul oscuro: “Análisis predictivo: Anticipa comportamientos”, y debajo, en Sora regular: “Estrategias de marketing más efectivas y oportunas”. Utiliza azul claro y blanco, resaltando el avance narrativo hacia soluciones IA."}}, {"json": {"slide": 4, "prompt": "Estilo infográfico: Muestra crecimiento de interés en IA. Fondo blanco con diseño geométrico azul claro en esquinas. En el centro-derecha, gran número “69%” en Sora extracondensed bold azul oscuro, acompañado de ícono de gráfico de barras ascendentes (líneas finas), y a la izquierda siluetas minimalistas de profesionales. Texto superior: “El interés en IA crece”, en Sora bold azul, y pequeño texto debajo: “69% de profesionales ven su potencial transformador en marketing digital”. Uso de azul claro y blanco, narrativa de validación social."}}, {"json": {"slide": 5, "prompt": "Estilo infográfico: Enumera tendencias próximas IA. Fondo suave con degradado vertical de azul claro a blanco. En el centro, tres bloques horizontales con íconos lineales: usuario personalizado, gráfico predictivo, y asistente digital. Cada bloque tiene título breve en Sora bold: “Personalización”, “Análisis predictivo”, “Herramientas generativas”, y debajo, descripción en Sora regular gris. Bordes finos azules separan bloques. Incluye detalles en azul oscuro. Narra evolución y visión de futuro."}}, {"json": {"slide": 6, "prompt": "Estilo infográfico: Refleja transformación del marketing. Fondo blanco con gran círculo azul claro translúcido en el centro. En el interior, flechas giratorias conectando íconos mínimos de marketing digital (megáfono, correo, gráfico). Encima, encabezado en Sora bold azul oscuro: “La adopción continua de IA impulsa la transformación”. Abajo, texto en Sora regular: “Transformación profunda en el panorama del marketing digital actual”. Paleta azul claro y blanco, enfatizando el avance revolucionario."}}, {"json": {"slide": 7, "prompt": "Estilo infográfico: Visualiza el beneficio de la automatización. Fondo azul claro con sección media blanca simulando una hoja de reporte saliendo de una impresora mínima. A su alrededor, dos medidores circulares (velocidad y ahorro) en estilo lineal y con acentos azules oscuros. Título superior en Sora bold: “Automatizar marketing con IA”, color azul oscuro, y debajo, en Sora regular: “Reduce tiempo y costos, manteniendo calidad superior”. Mensaje de eficiencia y calidad."}}, {"json": {"slide": 8, "prompt": "Estilo infográfico: Destaca el aumento de ROI con IA. Fondo blanco con una curva ascendente azul claro que atraviesa el slide de izquierda a derecha. Encima, un gran icono de ROI en línea (flecha y moneda) al centro, flanqueado por dos íconos mínimos de recursos optimizados y gráfico de resultados. Texto superior: “La IA eleva el ROI en campañas” en Sora bold azul oscuro, y subtítulo debajo: “Optimiza recursos y maximiza resultados medibles”. Finaliza con paleta azul claro y blanco, llamado a la acción implícito."}}]}, "connections": {"Telegram Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Set Text", "type": "main", "index": 0}], [{"node": "Get Audio", "type": "main", "index": 0}]]}, "Get Audio": {"main": [[{"node": "Transcribe Voice", "type": "main", "index": 0}]]}, "Set Text": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Transcribe Voice": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Perplexity": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "AI Agent": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Code", "type": "main", "index": 0}], [{"node": "Error", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "cf870893-2dbd-4b03-a2db-e9065f2de2df", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d98f85476dd64d1458e540159bc5b761d2b8c6fc529b12e58106fb06ce6b92f0"}, "id": "qwbLSDgqEG8rpvft", "tags": []}