{"name": "Twitter Viral tweet scraping", "nodes": [{"parameters": {"fields": {"values": [{"name": "pagesToScrape", "type": "numberValue", "numberValue": "5"}, {"name": "searchTerm", "stringValue": "={{ $json.chatInput }}"}]}, "options": {}}, "id": "6ef05bfc-4993-4383-9566-0cbb73e7e1ba", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 3, "position": [100, 15]}, {"parameters": {"jsCode": "const pagesToScrape = $input.first().json.pagesToScrape;\nconst searchTerm = $input.first().json.searchTerm;\n\nconst pages = [];\nfor (let i = 1; i <= pagesToScrape; i++) {\n  // Create consecutive 1-day ranges\n  const daysAgo = i - 1; // 0, 1, 2 days ago\n  const since = new Date();\n  since.setDate(since.getDate() - daysAgo - 1);\n  const until = new Date();\n  until.setDate(until.getDate() - daysAgo);\n  \n  pages.push({\n    json: {\n      page_number: i,\n      search_term: searchTerm,\n      since: since.toISOString().split('T')[0],\n      until: until.toISOString().split('T')[0],\n      total_pages: pagesToScrape\n    }\n  });\n}\n\nreturn pages;\n"}, "id": "35b40266-33f3-4763-83eb-fc7ef7efe8fd", "name": "Generate Page Numbers", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, 15]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [540, 15], "id": "ca52314b-fdb6-4f15-8702-5436ae4e281a", "name": "Loop Over Items"}, {"parameters": {"jsCode": "// This approach handles both single tweets and collections\n// It focuses on properly formatting the output for n8n\n// First, let's log what we're working with for debugging\nconsole.log(\"Input item structure:\", JSON.stringify($input.item, null, 2));\n\n// Function to format the date in a more human-readable way\nfunction formatDate(dateString) {\n  if (!dateString) return '';\n  \n  try {\n    const date = new Date(dateString);\n    // Format: \"March 13, 2025 at 19:25\"\n    return date.toLocaleString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  } catch (error) {\n    console.log(\"Error formatting date:\", error);\n    return dateString; // Return original if parsing fails\n  }\n}\n\n// Check if this is a Twitter Search result with multiple tweets\nif ($input.item.json.tweets && Array.isArray($input.item.json.tweets) && $input.item.json.tweets.length > 0) {\n  // This is a collection of tweets\n  // In n8n, to output multiple items, we need to use an array of objects with a json property\n  const items = $input.item.json.tweets.map(tweet => {\n    return {\n      json: {\n        tweetId: tweet.id || '',\n        url: tweet.url || '',\n        content: tweet.text || '',\n        likeCount: tweet.likeCount || 0,\n        retweetCount: tweet.retweetCount || 0,\n        replyCount: tweet.replyCount || 0,\n        quoteCount: tweet.quoteCount || 0,\n        viewCount: tweet.viewCount || 0,\n        createdAt: formatDate(tweet.createdAt)\n      }\n    };\n  });\n  \n  console.log(`Processing ${items.length} tweets`);\n  \n  // Return all items\n  return items;\n} else {\n  // This is a single tweet, just extract and return its data\n  const tweetData = {\n    tweetId: $input.item.json.id || '',\n    url: $input.item.json.url || '',\n    content: $input.item.json.text || '',\n    likeCount: $input.item.json.likeCount || 0,\n    retweetCount: $input.item.json.retweetCount || 0,\n    replyCount: $input.item.json.replyCount || 0,\n    quoteCount: $input.item.json.quoteCount || 0,\n    viewCount: $input.item.json.viewCount || 0,\n    createdAt: formatDate($input.item.json.createdAt)\n  };\n  \n  console.log(\"Processing single tweet\");\n  \n  // Return as a single item\n  return {\n    json: tweetData\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [980, -60], "id": "b3e63062-0576-483b-baa9-c12e58da2601", "name": "Extract Key Information"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "https://docs.google.com/spreadsheets/d/1DZQgLy4HEpkvpNn3O8pQ-4pbCjg7pqIBSlMUoOgqNCw/edit?gid=0#gid=0", "mode": "url"}, "sheetName": {"__rl": true, "value": "https://docs.google.com/spreadsheets/d/1DZQgLy4HEpkvpNn3O8pQ-4pbCjg7pqIBSlMUoOgqNCw/edit?gid=0#gid=0", "mode": "url"}, "columns": {"mappingMode": "defineBelow", "value": {"URL": "={{ $json.url }}", "Content": "={{ $json.content }}", "Likes": "={{ $json.likeCount }}", "Retweets": "={{ $json.retweetCount }}", "Replies": "={{ $json.replyCount }}", "Quotes": "={{ $json.quoteCount }}", "Views": "={{ $json.viewCount }}", "Date": "={{ $json.createdAt }}"}, "matchingColumns": [], "schema": [{"id": "URL", "displayName": "URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Content", "displayName": "Content", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON>s", "displayName": "<PERSON>s", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Retweets", "displayName": "Retweets", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Replies", "displayName": "Replies", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Quotes", "displayName": "Quotes", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Views", "displayName": "Views", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Date", "displayName": "Date", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1420, 15], "id": "63b8e019-cbe8-4ccf-af95-79a397b3dbcf", "name": "Add to Source", "credentials": {"googleSheetsOAuth2Api": {"id": "UR2pyx6qRAOU8xP9", "name": "Satya Google sheets"}}}, {"parameters": {"url": "https://api.twitterapi.io/twitter/tweet/advanced_search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $json.search_term }} since:{{ $json.since }} until:{{ $json.until }}"}, {"name": "queryType", "value": "Top"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-Key", "value": "{Your API Key}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, -60], "id": "c50f8730-5cfd-40ec-9552-68d59f922bc9", "name": "Find Tweets"}, {"parameters": {"sortFieldsUi": {"sortField": [{"fieldName": "viewCount", "order": "descending"}]}, "options": {}}, "type": "n8n-nodes-base.sort", "typeVersion": 1, "position": [1200, -60], "id": "582ee416-6c0d-46f8-9d20-615bd74bf533", "name": "Sort"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-120, 15], "id": "ac005284-c1a8-416a-907e-af81fabdc463", "name": "When chat message received", "webhookId": "e99a7756-3ddf-4775-be69-5df4a258edb2"}], "pinData": {}, "connections": {"Configuration": {"main": [[{"node": "Generate Page Numbers", "type": "main", "index": 0}]]}, "Generate Page Numbers": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Find Tweets", "type": "main", "index": 0}]]}, "Extract Key Information": {"main": [[{"node": "Sort", "type": "main", "index": 0}]]}, "Add to Source": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Find Tweets": {"main": [[{"node": "Extract Key Information", "type": "main", "index": 0}]]}, "Sort": {"main": [[{"node": "Add to Source", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "19780295-cf90-48a8-b078-f888b5e5ef07", "meta": {"templateCredsSetupCompleted": true, "instanceId": "e5f2670795215a7072140b28f54d4f2bb3399d965ee4f36ec6d70e4c54248298"}, "id": "fvEXPNE44W25e32l", "tags": []}