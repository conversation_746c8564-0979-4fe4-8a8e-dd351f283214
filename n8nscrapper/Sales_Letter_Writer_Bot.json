{"name": "Sales Letter Writer", "flow": [{"id": 1, "module": "jotform:watchForSubmissions", "version": 1, "parameters": {"__IMTHOOK__": 413932}, "mapper": {}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"parameters": {"__IMTHOOK__": {"data": {"editable": "true"}, "label": "Sales Letter Form"}}}, "parameters": [{"name": "__IMTHOOK__", "type": "hook:jotform", "label": "Webhook", "required": true}], "interface": [{"name": "formID", "type": "number", "label": "Form ID"}, {"name": "submissionID", "type": "number", "label": "Submission ID"}, {"name": "webhookURL", "type": "text", "label": "Webhook URL"}, {"name": "ip", "type": "text", "label": "IP"}, {"name": "fromTitle", "type": "text", "label": "Form title"}, {"name": "pretty", "type": "text", "label": "Pretty"}, {"name": "username", "type": "text", "label": "Username"}, {"name": "rawRequest", "type": "text", "label": "RAW request"}, {"name": "type", "type": "text", "label": "Type"}, {"name": "request", "spec": [{"name": "slug", "type": "text", "label": "Slug"}, {"name": "event_id", "type": "text", "label": "Event ID"}, {"name": "path", "type": "text", "label": "Path"}, {"name": "q3_salesletter", "type": "text", "label": "Please enter the sales letter example WITH brackets around variables such as characteristics, thoughts, feelings, and products:"}, {"name": "q4_avatar", "type": "text", "label": "Please enter the customer avatar:"}, {"name": "q5_niche", "type": "text", "label": "Please enter the niche:"}, {"name": "q7_successstory", "type": "text", "label": "Please enter a creative story depicting the type of success your product can achieve for your customer:"}, {"name": "q8_product", "type": "text", "label": "Please enter your product:"}, {"name": "q9_write_style", "type": "text", "label": "Choose the tone/style of writing"}], "type": "collection", "label": "Request"}, {"name": "formTitle", "type": "text", "label": "Form Title"}]}}, {"id": 2, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 579692}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You are a sales letter writer who writes sales copy in the style of {{1.request.q9_write_style}}"}, {"role": "user", "content": "Create a sales page headline for the provided product, targeted at the provided avatar, in the provided niche. Make the title compelling to entice readers to read on.\nproduct - {{1.request.q8_product}}\navatar - {{1.request.q4_avatar}}\nniche - {{1.request.q5_niche}}\nConstraints: Do not include pretext or context, only return the sales page headline."}], "max_tokens": "250", "temperature": "1.1"}, "metadata": {"designer": {"x": 300, "y": 0, "name": "Title Writer"}, "restore": {"expect": {"echo": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}]}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "anthonylee991 OpenAI"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["prompt", "chat"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature"}, {"name": "top_p", "type": "number", "label": "Top p"}, {"name": "n_completions", "type": "number", "label": "N"}, {"name": "echo", "type": "boolean", "label": "Echo"}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "value", "type": "text", "label": "Parameter Value"}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content", "required": true}], "type": "array", "label": "Messages", "required": true}], "advanced": true}}, {"id": 6, "module": "util:SetVariables", "version": 1, "parameters": {}, "mapper": {"scope": "roundtrip", "variables": [{"name": "gary_halbert", "value": "Dear [Mr. <PERSON>],\nDid you know that your [family name] was [recorded] with a [coat-of-arms in ancient heraldic archives] more than seven centuries ago? My [husband] and I discovered this while doing some research for some [friends] of ours who have the [same last name] as you do. We've had [an artist] [recreate the coat-of-arms] exactly as described in the [ancient records]. This [drawing], along with other [information about the name], has been [printed up into an attractive one-page report].The bottom half of [the report] tells the [story of the very old and distinguished family name of <PERSON>]. It [tells what the name means, its origin, the original family motto, its place in history and about famous people who share it]. The [top half] has a [large, beautiful reproduction] of an [artist's drawing of the earliest known coat-of-arms for the name of <PERSON>]. This entire [report] is [documented, authentic and printed on parchment-like paper] suitable for framing.The [report] so [delighted our friends] that we have had a few [extra copies] made in order to share this information with other people [of the same name]. [Framed], these [reports] make [distinctive wall decorations] and they are [great gifts for relatives]. It should be remembered that we have not [traced anyone's individual family tree] but have [researched back through several centuries] to find out about [the earliest people named <PERSON>]. All we are asking for them is [enough to cover the added expenses of having the extra copies printed and mailed]. If you are interested, please let us know right away as our [supply is pretty slim]. Just [verify that we have your correct name] and [address] and send the [correct amount in cash or check] for the [number of reports] you want. We'll send them promptly by return mail."}, {"name": "david_ogilvy", "value": "At [60 miles an hour] the [loudest noise] in this new [Rolls-Royce] comes from the[ electric clock” ]. What makes [Rolls-Royce the best car] in the world? “There is really no magic about it - is merely [patient attention to detail]” says an eminent [Rolls-Royce engineer]. \n]“At 60 miles an hour, the loudest noise comes from the electric clock,”] reports the [Technical Editor of THE MOTOR]. [Three mufflers tune out sound frequencies – acoustically].\nEvery [Rolls-Royce engine] is [run] for seven hours [at full throttle] before installation, and each [car is test-driven] for hundreds of [miles over varying road surfaces].\nThe [Rolls-Royce] is designed as an [owner-driven car]. It is [eighteen inches shorter] than the [largest domestic cars].\nThe [car] has [power steering, power brakes, and an automatic gear-shift]. It is very easy to [drive] and to [park]. No [chauffeur] is required.\nThe finished [car] [spends a week in the final test shop, being fine-tuned]. Here it is [subjected to 98 separate ordeals]. For example, [the engineers] use a [stethoscope to listen for axle-whine]. \nThe [Rolls-Royce] is guaranteed for three years. With a [new network of dealers] and [parts-depots] from Coast to Coast, [service] is no problem.\nThe [Rolls-Royce radiator] has never [changed], except that when [Sir <PERSON> died in 1933 the monogram RR was changed from red to black].\nThe [coachwork] is given [five coats of primer paint, and hand rubbed between each coat, before nine coats of finishing paint go on].\nBy moving a switch on the [steering column], you can adjust the [shock-absorbers to suit road conditions].\nA [picnic table, veneered in French walnut, slides out from under the dash]. [Two more swing out behind the front seats]. You\ncan get such optional extras as [an Espresso coffee-making machine, a dictating machine, a bed, hot and cold water for washing, an electric razor or a telephone]. \nThere are [three separate systems of power brakes, two hydraulic and one mechanical]. [Damage to one will not affect the others]. The [Rolls-Royce] is a very safe [car – and also a very lively car]. It [cruises] serenely at [eighty-five]. [Top speed is in excess of 100 m.p.h.]\nThe [Bentley is made by Rolls-Royce]. Except for the [radiators, they are identical motor cars], manufactured by the same [engineers] in the same works. People who feel diffident about [driving a Rolls-Royce] can buy a [Bentley]. PRICE. The [Rolls-Royce illustrated in this advertisemen]t – f.o.b. principal ports of entry – costs [$13,995]. If you would like the rewarding experience of [driving a Rolls-Royce or Bentley], write or telephone to one of the [dealers listed on opposite page. Rolls-Royce Inc., 10 Rockefeller Plaza, New York 20, N. Y. Circle 5-1144.]"}, {"name": "eugene_schwartz", "value": "NOW READ [300 BUSINESS MAGAZINES IN 30 MINUTES] And get the [guts of every one of their most valuable ideas -- in a super-condensed form you just can't forget]! Dear Reader: a special free invitation [Introducing Boardroom Reports] -- for the [generalist], the [executive who wants to know every important new development the specialists know . . . from every vital area . . . but wants to know them all in minutes not hours . . . and wants to know, in seconds exactly what each means to him]! Now the [Editors] invite you to [read the latest issue] with their compliments --free. [Boardroom] takes you where the destiny-shaping big decisions are made! Where [top specialists] determine the [fate of whole industries]. Like this . . . Just one recent example -- do you know the simple change in [accounting procedure] that virtually [destroyed one of the great growth industries]? It was first hinted at in a [specialized journal for CPA's] . . . and only a handful of [executives] in the industry itself realized its [devastating impact] till months later. [Boardroom Reports scans that accounting publication, and over two dozen more in the same specialist area alone]. Therefore it would have [flashed you the decision itself . . . plus its consequences to profit and loss . . . and Wall Street's probable catastrophic reaction to it -- only days after the specialists themselves learned about it]! Think about being [plugged into it]. [How to hire people without making mistakes][ HIRE IN SUCH SPECIALIST FIELDS! YOU'LL BE BRIEFED ON HUNDREDS OF SUCH SPECIALIST MAGAZINES AND NEWSLETTERS]! In just such a way, the future of [your business and your career] is being [decided by specialists operating behind tightly closed doors]. They are the [experts in each of the vital areas on which Boardroom Reports reports to you]: [accounting] . . . [advertising] . . . [banking] . . [computers] . . [construction] . . . [distribution] . . [economics] . . . [finance] . . [government] . . .[insurance] . . . [labor] . . . [law] . . . [management] . . . [marketing] . . . [personnel] . . . [production] . . . [sales and merchandising] . . . [taxes] . . . [transportation] and well over a dozen more. They [make tomorrow today]. They [pass the new laws]. [Create the new sales ideas. Find the new tax loopholes. Break through old production hang-ups. Invent the new financing methods. Devise new ways around old costs. Create the new profit opportunities that only they nee for that one golden moment]. And then they [carefully report these new breakthroughs to their peers] partly for [prestige and money] -- partly from [professional ethics]. And [boardroom reports scans these magazines like a giant computer]. [Actually reads over 3,000 different sources every month]. But [gives you the guts -- the super-quintessential thoughts -- the monumental breakthroughs --the most important and potentially most profitable ideas]. HOW? By [completely filtering out the cluttering detail and translating the jargon into \"How-can-I-make-money-from-it-today\" language]. For example, [six pages of pioneering complex technical reports are boiled ruthlessly down into just six lines of mind-expanding main thoughts]. And then [half a dozen new vital research reports are put into a single flash-read page, smaller than half a page of an ordinary business magazine]. And then, [you get the top-level leaks, the life or death glimpses into the future that never reach the regular business press at all].  Like these .. [How to speed up an insurance claim when you're getting the runaround]. Do you know, for example, that [telephone company executives knew JIM turndown was coming, months before anyone else]? The [indicator was flashed loud and clear to them alone]. But what did they do with this information? Immediately [called a telephone-conference of their top operating management and slashed costs, cut back growth plans, and more]. No [public announcement was made]. Nothing appeared in the [regular business press]. But several of the [top business correspondents in that field knew the story, and would have released it -- if an Instant-Wire publication like Boardroom Reports had only existed then]. [Leaks] like this -- [non-released signals] -- happen every day in a dozen [key centers]. They concern such matters as: [What Washington's really up to. What industry leaders are thinking, but not talking about. Where the smart money is going. Who's quietly expanding. Cutting back. Switching horses in midstream]. And [Boardroom Reports is plugged into over three thousand such sensitive sources]. And their [previously silent indicators] will be [relayed to you in each issue in just a few electrifying lines]: What is it! How do these [experts interpret it]? [WHAT DOES IT MEAN TO YOU]? PLUS -- VALUABLE BONUS EXTRAS IN EACH [ISSUE AN OPPORTUNITY TO PICK THE BRAINS OF AMERICA'S TOP BUSINESS CONSULTANTS]! The final step in a thoroughly rounded view of [the business world in each issue -- Boardroom Reports gives you its eyeball-to-eyeball confrontations with America's top consultants. Razor-sharp questions and answers -- with every superfluous word removed -- with staff consultants from the world renowned \"think tanks\" and the knowing specialist firms like: Arthur D. Little, Inc . . . McKinsey F. Co . . . Hudson Institute . . Boston Consulting . . . Partners of international accounting and law firms. Private consultants of the stature of Peter Drucker . . . Fred Adler . . Robert Half. Every issue has at least three of chase men in headline-form: defining . . . exposing . . challenging . . . clarifying . . . predicting . . . even sticking out his neck]. Meaningful [insight that unifies in a single successful vision, with surrounding pages of future-shaping]. Customers pay fast if [one magic sentence is added to the bill specialist decisions that the ordinary executive never even sees]. WHAT IT IS, REALLY, IS A WHOLE NEW [BUSINESS COMMUNICATION SYSTEM]. IF IT WERE ELECTRONIC WE'D HAVE TO CHARGE YOU $1,000 A YEAR FOR IT. BUT, BECAUSE IT'S [ON PAPER], WE CAN GIVE IT TO YOU FOR only [$29.95]. FIRST [ISSUE] IS YOURS FREE."}, {"name": "claude_hopkins", "value": "Such famous [Beauty Specialists] as [DELORD et BION, Paris] recommend this as most important of all [home beauty treatments] [\"Palm and olive oils, as expertly combined in Palmolive Soap, have a most healing greet and yet provide the deep cleansing which is the very foundation of beauty.\"] OF THE many celebrated [beauty establishments] in [Paris], one of the largest and most typically [Parisian] is that of [Delord et Bion], capitalized over [500,000 francs]. This unusual growth to prominence has come within comparatively few years and is due to a unique combination of the scientific and the aesthetic, as represented in the [two members of this now internationally recognized firm]. The scientific atmosphere which surrounds the [Paris beauty salon of Delord et Bion] is authentic. [Monsieur <PERSON>] is skilled in [beauty culture] and has many years of experience. [Monsieur <PERSON>] brings to this [partnership] modern [ideas of technique] which add the final assurance of perfection to a [Delord et Bion treatment]. [They themselves] feel that much of their success is due to the emphasis they place on [home care of the skin]. In this [Paris Salon], on the [Avenue Victor Hugo], one learns of a [rule of skin care based on soap combining oils of palm and olives] .. . a rule which has become international in use and is recommended by experts all over the world. If you were to consult [<PERSON><PERSON> or <PERSON> of Paris], they, too, would advise it. [<PERSON>, of Bet-1114 Madame <PERSON>, of London; <PERSON><PERSON><PERSON>, of Vienna], advocate this same treatment to their [aristocratic patrons]. Why [palm and olive oils] \"We advise all our clients to use the following method,\" say [<PERSON>ord et Bion]. [\"First they must use Palmolive Soap because this soap definitely helps the beauty expert in his work of complexion im-provement. \"Palm and olive oils, as expertly combined in Palmolive Soap, have a most healing effect and yet provide the deep cleansing which is the very foundation of beauty. In addition to Palmolive we prescribe Modernism—in decoration as well as in heauly treatments. The use of our cream No. 65. Scientifically prepared, it whitens and softens the skin, agrees with all complexions and retains the powder,\" say Delord et Bion. \"We also recommend our powder No. 65, which has a very pleasant perfume and is specially made to be used with our cream No. 65.\"] [How best to use Palmolive - With both hands massage a lather of Palmolive Soap and warm water well into the skin two minutes, allowing it to penetrate the pores. Then rinse; first with warm water, gradually with colder. A final rinse with ice water is a refreshing astringent. For a dry skin, a touch of cold cream before adding powder and rouge; for oily skin, an astringent lotion.] When start [cosmopolitans] seek the  most [Parisian] atmosphere possible for their [beauty treatments], they invariably visit the [salon of Delord et Bion]. These two [inimitably French specialists] provide just that mixture of the scientific and the aesthetic which delights a discriminating [feminine clientele], A simple [treatment], yet it undoubtedly explains why [Palmolive] is one of the two largest selling [soaps in France]—known the world over as [home of exquisite cosmetics]. Here in [America], and in [forty-eight other countries], it is more generally used than [any other soap], [priories formula embodying the precious oils of palm and olive, famous since the days of Cleopatra for prolonging health and beauty]."}, {"name": "frank_kern", "value": "Would You Like Me To Personally [Double, Triple, or Even Quadruple Your Business]...For Free? From The Desk Of [<PERSON>].\nDear Friend, I’m looking for a [“dream” client] that I can bring in [massive windfalls] for. If you’re [that client], I will personally [work with you one-on-one in your business] to help you [double, triple, or maybe even quadruple your revenue for the next 12 months]. You Pay Nothing Out Of Pocket, Ever. Here's why. The first thing I’m going to do for you is to personally [help you create a strategic plan to bring in immediate money].There’s no charge for this and it only takes about 45-60minutes for us to do together.(After doing this type of thing for almost 14 years straight, I’vegotten pretty good at fast results). Anyway, I’ll even [do most of the heavily lifting for you] ....[telling you exactly what to send, how to position your offer, and how to bring in back-end money as well]. At the end of this initial planning session one of these three things will happen:\n1. [You love the plan and decide to implement it on your own.] If this is the case, I’ll wish you the best of luck and ask that you keep in touch with me to let me know how you’re doing.\n2. [You love the plan and ask to become my client so I can personally help you execute, maximize, and profit from it ASAP]. If that’s the case, we’ll knock it out of the park ...And that’s apromise. I’ve never had a [client leave] - and I’m pretty sure [they’re not sticking around because of my good looks]!.\n3. [In the unlikely and unprecedented event that you feel likeyou wasted your time, I will send you $1,400.00 as payment immediately]. No questions asked. Your time is your most valuable asset you have, and I respect that. It really is that simple and there’s no catch. Think about this. The “worst” that can happen is you get [$1,400 for “wasting” 45-60 minutes of your time]. The best that can happen is [we work together one on one to increase sales and profit several times over]. That’s Why This Is The Closest Thing To [FREE MONEY] You’ll Ever See. Here’s how it’ll work: First, we [get on the phone one on one and go over yourbusiness. I take a look at what you’ve got, what you’re doing, and what you want to achieve going forward. Once we have those “raw materials”, I help you come up with a strategic plan of action to immediately increase your profits dramatically]. There are a number of ways I might do this for you. For example, I might [show you how to restructure your offer for a better price point, how to create recurring revenue (even without a continuity program), how to pull in buyers from untapped sources, or how to reactivate past customers]. And if you have a [list of prospects], we’re bound to whip up a [quick promotion you can run within days] ...and have the [cash register ringing over and over again]. And like I said, there’s no charge for this. So Why Would I Offer It? Two reasons: First of all, [I enjoy it. This type of thing is what I do best, and it makes me very, very happy to see someone achieve financial success (and all that comes with it) as a result of the help I give them]. Second of all, [it’s how I attract consulting clients. Here’s how that works: Assuming you’re happy and you want me to crank out these types of plans for you all the time, you’ll probably want to continue working together long term so I can help you implement them]. If this is the case, I might invite you to [become a consulting client. My “fee” is $4,860 a month] ...but if you think about it, it really doesn’t “cost” you anything. Why? Because I expect to [make you much more than $4,860 in the first month] ...and if we keep working together over the next 12months, I’m confident I can [double your entire business ...at minimum]. Actually, I can give you [a plan to make more than $4,860 during our first conversation - which is free]! So you’ll see the value by the time we hang up the phone -without ever spending a dime. And look. If you [don’t want to become a client], don’t worry about it. You won’t get any sales pitch or pressure from me of any kind, ever. In fact, here’s my “GIANT BALLS PROMISE” to you: [You Find Our Conversation To Be Incredibly Valuable Or I’ll Pay You $1,400.00 Immediately To Compensate You For Your Time]. Now, obviously this is an amazing offer which you’ll probably never see from any other [“Internet guru”] in the world.Think about it. I’m [personally generating a profit-plan for you] up front- for free - and then letting you [pay me later if (and only if) you decide to work together long term]."}]}, "metadata": {"designer": {"x": 600, "y": 0, "name": "Save Sales Letters"}, "restore": {"expect": {"scope": {"label": "One cycle"}, "variables": {"items": [null, null, null, null, null]}}}, "expect": [{"name": "variables", "spec": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "value", "type": "any", "label": "Variable value"}], "type": "array", "label": "Variables"}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}], "interface": [{"name": "gary_halbert", "type": "any", "label": "gary_halbert"}, {"name": "david_ogilvy", "type": "any", "label": "david_ogilvy"}, {"name": "eugene_schwartz", "type": "any", "label": "eugene_schwartz"}, {"name": "claude_hopkins", "type": "any", "label": "claude_hopkins"}, {"name": "frank_kern", "type": "any", "label": "frank_kern"}]}}, {"id": 7, "module": "util:SetVariable2", "version": 1, "parameters": {}, "mapper": {"name": "sales_letter", "scope": "roundtrip", "value": "{{if(1.request.q9_write_style = \"<PERSON> (Sarcastic & Conversational)\"; 6.gary_halbert; ignore)}}\n{{if(1.request.q9_write_style = \"<PERSON> (Authoritative & Sophisticated)\"; 6.david_ogilvy; ignore)}}\n{{if(1.request.q9_write_style = \"<PERSON> (Analytical & Persuasive)\"; 6.eugene_schwartz; ignore)}}\n{{if(1.request.q9_write_style = \"<PERSON> (Informative & Straight-forward)\"; 6.claude_hopkins; ignore)}}\n{{if(1.request.q9_write_style = \"<PERSON> (Relatable & Subtle)\"; 6.frank_kern; ignore)}}"}, "metadata": {"designer": {"x": 900, "y": 0, "name": "Set Letter Style"}, "restore": {"expect": {"scope": {"label": "One cycle"}}}, "expect": [{"name": "name", "type": "text", "label": "Variable name", "required": true}, {"name": "scope", "type": "select", "label": "Variable lifetime", "required": true, "validate": {"enum": ["roundtrip", "execution"]}}, {"name": "value", "type": "any", "label": "Variable value"}], "interface": [{"name": "sales_letter", "type": "any", "label": "sales_letter"}]}}, {"id": 8, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 579692}, "mapper": {"model": "gpt-4", "select": "chat", "messages": [{"role": "system", "content": "You are a sales letter writer who writes sales copy in the style of {{1.request.q9_write_style}}"}, {"role": "user", "content": "Create a sales page headline for the provided product, targeted at the provided avatar, in the provided niche. Make the title compelling to entice readers to read on.\nproduct - {{1.request.q8_product}}\navatar - {{1.request.q4_avatar}}\nniche - {{1.request.q5_niche}}\nConstraints: Do not include pretext or context, only return the sales page headline."}, {"role": "assistant", "content": "{{2.choices[].message.content}}"}, {"role": "user", "content": "Use the sales letter below for inspiration to write a sales page, replacing each [] with characteristics, thoughts, concepts, products, and feelings related to {{1.request.q4_avatar}} in the {{1.request.q5_niche}} niche. Use the story of {{1.request.q7_successstory}} with a visceral emotionally charged output with similar context. Keep a similar structure and format, leading with a more conversational and thought provoking tone using direct-response style language that inspires visceral feeling and emotional connection: {{trim(replace(7.sales_letter; \"\"\"\"; space))}}\nConstraints: Do not include pretext or context, only return the sales letter."}], "max_tokens": "1500", "temperature": "1.1"}, "metadata": {"designer": {"x": 1200, "y": 0, "name": "Sales Page Writer"}, "restore": {"expect": {"echo": {"mode": "chose"}, "model": {"mode": "chose", "label": "gpt-4"}, "select": {"label": "Create a Chat Completion"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}]}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "anthonylee991 OpenAI"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["prompt", "chat"]}}, {"name": "max_tokens", "type": "number", "label": "<PERSON>"}, {"name": "temperature", "type": "number", "label": "Temperature"}, {"name": "top_p", "type": "number", "label": "Top p"}, {"name": "n_completions", "type": "number", "label": "N"}, {"name": "echo", "type": "boolean", "label": "Echo"}, {"name": "additionalParameters", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "value", "type": "text", "label": "Parameter Value"}], "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "messages", "spec": [{"name": "role", "type": "select", "label": "Role", "options": [{"label": "System", "value": "system"}, {"label": "User", "value": "user"}, {"label": "Assistant", "value": "assistant"}], "required": true}, {"name": "content", "type": "text", "label": "Message Content", "required": true}], "type": "array", "label": "Messages", "required": true}], "advanced": true}}, {"id": 4, "module": "google-docs:createADocument", "version": 1, "parameters": {"__IMTCONN__": 645045}, "mapper": {"name": "SALES LETTER GPT4", "footer": false, "header": false, "content": "{{trim(replace(2.choices[].message.content; \"\"\"\"; space))}}\n{{space}}\n{{trim(replace(8.choices[].message.content; \"\"\"\"; space))}}", "folderId": "/", "destination": "drive"}, "metadata": {"designer": {"x": 1500, "y": 0}, "restore": {"expect": {"folderId": {"mode": "chose", "path": ["/"]}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "anthonylee991 Sheets (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "content", "type": "text", "label": "Content", "required": true}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "header", "type": "boolean", "label": "Insert a Header", "required": true}, {"name": "footer", "type": "boolean", "label": "Insert a Footer", "required": true}, {"name": "folderId", "type": "folder", "label": "New Document's Location", "required": true}]}}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": [], "notes": [{"id": 4, "text": "Google Docs:\nThe output from the A.I. goes to a Google Doc. You can, of course, modify this to have it output wherever you wish.", "filter": false}, {"id": 8, "text": "Sales Page Writer:\nThis is where GPT writes the body of the sales page.", "filter": false}, {"id": 7, "text": "Set Letter Style:\nBased on the form input, this sets the tone/style of the A.I. writer.", "filter": false}, {"id": 6, "text": "Save Sales Letters:\nThis module saves sales letters from a handful of the greatest copywriters of all time into Make.", "filter": false}, {"id": 2, "text": "Title Writer:\nThis is where GPT writes a headline for the sales page based on the customer avatar information from the form.", "filter": false}, {"id": 1, "text": "Jotform:\nThis is where your inputs from the Jotform are sent to Make.com.\nYou don't have to use Jotform as your input. You can also likely use Typeform, Chatfuel, or a number of other platforms.\n\nThe important thing is getting the variables into Make.", "filter": false}]}, "zone": "us1.make.com"}}