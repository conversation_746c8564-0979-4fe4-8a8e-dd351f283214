{"id": "xFSHqOmnuGuB4QRY", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Google Maps Business Scraper & Lead Enricher with Bright Data & Google Gemini", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ZOwtAMLepQaGW76t", "name": "Building Blocks", "createdAt": "2025-04-13T15:23:40.462Z", "updatedAt": "2025-04-13T15:23:40.462Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "4b4d84f7-dfc4-4b9e-8e43-ce02ac282e3e", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-320, -20], "parameters": {}, "typeVersion": 1}, {"id": "6a4375de-d7f8-4973-9cce-53aee11765ed", "name": "Set input fields", "type": "n8n-nodes-base.set", "position": [-60, -20], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "0ac91db2-9848-40d4-b942-cd7288597ded", "name": "url", "type": "string", "value": "https://www.google.com/maps/search/"}, {"id": "7142f0df-3c68-4ce0-8e9b-25101db31312", "name": "webhook_notification_url", "type": "string", "value": "https://webhook.site/c9118da2-1c54-460f-a83a-e5131b7098db"}, {"id": "f5b7e10d-bcd8-439c-8e84-ba4af11f2cd2", "name": "search", "type": "string", "value": "dentists+in+texas/?q=dentists+in+texas"}, {"id": "3a0bcec2-ec6d-457f-87a5-b73f4d826b2a", "name": "zone", "type": "string", "value": "serp_api1"}, {"id": "a9de809f-2026-44bb-a2a9-58495df78bfb", "name": "start", "type": "string", "value": "0"}, {"id": "feff2b6f-2897-4135-a09a-e0799a2197ea", "name": "num", "type": "string", "value": "20"}]}}, "typeVersion": 3.4}, {"id": "6205bdfc-5b31-44cd-8b9c-37de800e8a63", "name": "Perform Bright Data Web Request", "type": "n8n-nodes-base.httpRequest", "position": [180, -20], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}/{{ $json.search }}"}, {"name": "format", "value": "raw"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "85c38904-c3d8-4ec1-80f3-5d06a12ba53c", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [400, 160], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "f53c2c94-5ff7-49e0-a72d-d40b0504b59f", "name": "Google Maps Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [400, -20], "parameters": {"text": "=Extract Google Maps content \n\n{{ $json.data }}\n\nMake sure to return the data in JSON", "batching": {}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.7}, {"id": "5b849d5c-da41-489b-b2df-2a6e6c9eee40", "name": "Structured Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [580, 160], "parameters": {"jsonSchemaExample": "[\n      {\n        \"name\": \"Texas City Dental - Dentist in Texas City\",\n        \"entity_id\": [\n          \"9673584215857663405\",\n          \"6574027299904374997\"\n        ],\n        \"details_url\": \"/g/11h7ftffbx\",\n        \"reviews\": 674,\n        \"rating\": 4.8,\n        \"address\": \"3448 Palmer Hwy, Texas City, TX 77590\",\n        \"appointment_link\": \"https://txcitydental.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Dental laboratory\",\n          \"Denture care center\",\n          \"Endodontist\",\n          \"Oral surgeon\",\n          \"Orthodontist\",\n          \"Pediatric dentist\",\n          \"Periodontist\",\n          \"Teeth whitening service\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n      {\n        \"name\": \"Texas Dental\",\n        \"entity_id\": [\n          \"9677148940277542199\",\n          \"15703975205538868636\"\n        ],\n        \"details_url\": \"/g/1tgcmpr0\",\n        \"reviews\": 1354,\n        \"rating\": 4.9,\n        \"address\": \"5132 Village Creek Dr, Plano, TX 75093\",\n        \"appointment_link\": \"https://www.texasdentalpa.com/?utm_source=GBPlisting&utm_medium=organic\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental implants periodontist\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n      {\n        \"name\": \"North Texas Dental\",\n        \"entity_id\": [\n          \"9677259612218511763\",\n          \"14671019117677462783\"\n        ],\n        \"details_url\": \"/g/1wd3wg17\",\n        \"reviews\": 1623,\n        \"rating\": 4.9,\n        \"address\": \"2515 Masters St, Sherman, TX 75090\",\n        \"appointment_link\": \"https://www.ntdsherman.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Dental implants periodontist\",\n          \"Denture care center\",\n          \"Teeth whitening service\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n      {\n        \"name\": \"6 to 9 Dental Texas\",\n        \"entity_id\": [\n          \"9681711930004589935\",\n          \"14121951165737821532\"\n        ],\n        \"details_url\": \"/g/1ts2_px7\",\n        \"reviews\": 113,\n        \"rating\": 4.9,\n        \"address\": \"2603 SE Military Dr Ste 103, San Antonio, TX 78223\",\n        \"appointment_link\": \"http://www.vphumphrey2dds.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Dental clinic\",\n          \"Dental implants provider\",\n          \"Emergency dental service\",\n          \"Endodontist\",\n          \"Oral surgeon\",\n          \"Prosthodontist\",\n          \"Teeth whitening service\",\n          \"Walk-in clinic\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Cosmetic Dental Texas\",\n        \"entity_id\": [\n          \"9673942104093032889\",\n          \"6644082296499224433\"\n        ],\n        \"details_url\": \"/g/1ts_6_5b\",\n        \"reviews\": 234,\n        \"rating\": 4.6,\n        \"address\": \"2101 Crawford St STE 103, Houston, TX 77002\",\n        \"appointment_link\": \"https://cosmeticdentaltexas.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental hygienist\",\n          \"Health consultant\",\n          \"Teeth whitening service\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Natural Dentistry of North Texas\",\n        \"entity_id\": [\n          \"9677855844448877255\",\n          \"6619454455705348065\"\n        ],\n        \"details_url\": \"/g/1w45608g\",\n        \"reviews\": 109,\n        \"rating\": 4.9,\n        \"address\": \"1645 N Town E Blvd #532, Mesquite, TX 75150\",\n        \"appointment_link\": \"https://www.naturaldentistryofnorthtexas.com/?utm_source=local&utm_medium=organic\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental clinic\",\n          \"Dental implants periodontist\",\n          \"Denture care center\",\n          \"Doctor\",\n          \"Emergency dental service\",\n          \"Orthodontist\",\n          \"Pediatric dentist\",\n          \"Teeth whitening service\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Heart of Texas Smiles General Cosmetic Dentistry\",\n        \"entity_id\": [\n          \"9681881174741169099\",\n          \"15832460407262855114\"\n        ],\n        \"details_url\": \"/g/1tcvv8l5\",\n        \"reviews\": 1531,\n        \"rating\": 4.9,\n        \"address\": \"117 Burnett Ct, Waco, TX 76712\",\n        \"appointment_link\": \"https://www.wacofamilydental.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental clinic\",\n          \"Dental hygienist\",\n          \"Dental implants provider\",\n          \"Orthodontist\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Smile 4 Texas Dental Center\",\n        \"entity_id\": [\n          \"9678099339216434095\",\n          \"2187168089587898937\"\n        ],\n        \"details_url\": \"/g/12hn7tnrq\",\n        \"reviews\": 812,\n        \"rating\": 4.9,\n        \"address\": \"1400 Blalock Rd D1, Houston, TX 77055\",\n        \"appointment_link\": \"https://www.smile4texas.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental clinic\",\n          \"Dental hygienist\",\n          \"Dental implants provider\",\n          \"Emergency dental service\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Dental Center of Texas\",\n        \"entity_id\": [\n          \"9673948559547960793\",\n          \"10154247486966080020\"\n        ],\n        \"details_url\": \"/g/1q665w1n4\",\n        \"reviews\": 155,\n        \"rating\": 4.7,\n        \"address\": \"23922 Cinco Village Center Blvd #100, Katy, TX 77494\",\n        \"appointment_link\": \"http://www.dentalcentertexas.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Dental clinic\",\n          \"Orthodontist\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Central Texas Family Dental\",\n        \"entity_id\": [\n          \"9674049914126201417\",\n          \"3494220525942607347\"\n        ],\n        \"details_url\": \"/g/1tdvkknl\",\n        \"reviews\": 188,\n        \"rating\": 4.9,\n        \"address\": \"4104 E Stan Schlueter Loop #4, Killeen, TX 76542\",\n        \"appointment_link\": \"https://www.centexdds.com/\",\n        \"category\": [\n          \"Dentist\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"Texas Dentistry and Braces\",\n        \"entity_id\": [\n          \"9727332640569304831\",\n          \"9357406137681505810\"\n        ],\n        \"details_url\": \"/g/1tl8l4rw\",\n        \"reviews\": null,\n        \"rating\": null,\n        \"address\": \"315 N Galloway Ave # A, Mesquite, TX 75149\",\n        \"appointment_link\": \"https://mesquite.txdentistryandbraces.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental clinic\",\n          \"Dental implants provider\",\n          \"Denture care center\",\n          \"Emergency dental service\",\n          \"Orthodontist\",\n          \"Pediatric dentist\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n       {\n        \"name\": \"West Texas Dental Associates\",\n        \"entity_id\": [\n          \"9673946025354891219\",\n          \"2619898000467686041\"\n        ],\n        \"details_url\": \"/g/113fzvfyr\",\n        \"reviews\": 496,\n        \"rating\": 5.0,\n        \"address\": \"3315 64th St STE A, Lubbock, TX 79413\",\n        \"appointment_link\": \"http://www.westtxdental.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      },\n      {\n        \"name\": \"Texas Dental Specialists - Dr. Omar Yousuf\",\n        \"entity_id\": [\n          \"9675220924038729639\",\n          \"12674832566120650126\"\n        ],\n        \"details_url\": \"/g/113ddy7s1\",\n        \"reviews\": 296,\n        \"rating\": 4.9,\n        \"address\": \"5940 W Parker Rd STE 103, Plano, TX 75093\",\n        \"appointment_link\": \"https://www.texasdentalspecialists.com/\",\n        \"category\": [\n          \"Dentist\",\n          \"Cosmetic dentist\",\n          \"Dental implants provider\",\n          \"Dental clinic\",\n          \"Emergency dental service\",\n          \"Oral surgeon\"\n        ],\n        \"related_terms\": [\n          \"dentist\",\n          \"cosmetic_dentist\",\n          \"dental_clinic\",\n          \"dental_implants_periodontist\",\n          \"teeth_whitening_service\",\n          \"orthodontist\",\n          \"denture_care_center\",\n          \"pediatric_dentist\"\n        ]\n      }\n    ]"}, "typeVersion": 1.2}, {"id": "8fa11221-0bae-498d-9f29-c6147740909c", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [60, -440], "parameters": {"color": 5, "width": 440, "height": 240, "content": "## LLM Usages\n\nGoogle Gemini LLM is being utilized for the structured data extraction handling."}, "typeVersion": 1}, {"id": "11884d28-95bc-48ea-b1c8-847f33425a8b", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "position": [-380, -800], "parameters": {"color": 7, "width": 400, "height": 340, "content": "## Logo\n\n\n![logo](https://images.seeklogo.com/logo-png/43/1/brightdata-logo-png_seeklogo-439974.png)\n"}, "typeVersion": 1}, {"id": "c692962a-4910-46e5-991b-2202b90e7572", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [-380, -440], "parameters": {"width": 400, "height": 240, "content": "## Note\n\nDeals with the Google Maps data extraction by utilizing the Bright Data and Google Gemini LLM for transforming the profile into a structured JSON response.\n\n**Please make sure to set the input fields node with the filtering criteria, Bright Data zone name, Webhook notification URL**\n"}, "typeVersion": 1}, {"id": "7109a239-d8d0-4399-bd7a-1c1eb0d9a333", "name": "Create a binary data for Structured Data Extract", "type": "n8n-nodes-base.function", "position": [1100, -120], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "aa031e65-4c46-4ddb-907d-1d9e54e88f68", "name": "Write the structured content to disk", "type": "n8n-nodes-base.readWriteFile", "position": [1340, -120], "parameters": {"options": {}, "fileName": "=d:\\GoogleMaps_Response.json", "operation": "write"}, "typeVersion": 1}, {"id": "bc02801c-b1f0-4b80-a43e-a2efe9efcc6a", "name": "Initiate a Webhook Notification for Structured Data", "type": "n8n-nodes-base.httpRequest", "position": [1100, 80], "parameters": {"url": "={{ $('Set input fields').item.json.webhook_notification_url }}", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "response", "value": "={{ $json.output.toJsonString() }}"}]}}, "typeVersion": 4.2}, {"id": "dd2601bf-e0a2-4cfb-a1b3-96b3125d810e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [760, -240], "parameters": {"color": 4, "width": 780, "height": 740, "content": "## Output Data Handling"}, "typeVersion": 1}, {"id": "362c4677-ea35-43c0-9dad-777bbafacad3", "name": "Update Google Sheets for Structured Data", "type": "n8n-nodes-base.googleSheets", "position": [1100, 300], "parameters": {"columns": {"value": {"name": "={{ $json.output[0].name }}", "rating": "={{ $json.output[0].rating }}", "address": "={{ $json.output[0].address }}", "reviews": "={{ $json.output[0].reviews }}", "category": "={{ $json.output[0].category.toJsonString() }}", "details_url": "={{ $json.output[0].details_url }}", "related_terms": "={{ $json.output[0].related_terms.toJsonString() }}", "appointment_link": "={{ $json.output[0].appointment_link }}"}, "schema": [{"id": "name", "type": "string", "display": true, "removed": false, "required": false, "displayName": "name", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "details_url", "type": "string", "display": true, "required": false, "displayName": "details_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "reviews", "type": "string", "display": true, "required": false, "displayName": "reviews", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "rating", "type": "string", "display": true, "required": false, "displayName": "rating", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "address", "type": "string", "display": true, "required": false, "displayName": "address", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "appointment_link", "type": "string", "display": true, "required": false, "displayName": "appointment_link", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "category", "type": "string", "display": true, "required": false, "displayName": "category", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "related_terms", "type": "string", "display": true, "required": false, "displayName": "related_terms", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "entity_id", "type": "string", "display": true, "removed": false, "required": false, "displayName": "entity_id", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "autoMapInputData", "matchingColumns": ["name"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}, "operation": "appendOrUpdate", "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BQ61qiNXdToLm465n-gtJiwdoQGSxua7St_iHAqMi10/edit#gid=0", "cachedResultName": "Sheet1"}, "documentId": {"__rl": true, "mode": "list", "value": "1BQ61qiNXdToLm465n-gtJiwdoQGSxua7St_iHAqMi10", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BQ61qiNXdToLm465n-gtJiwdoQGSxua7St_iHAqMi10/edit?usp=drivesdk", "cachedResultName": "GoogleMaps-LeadGen"}}, "credentials": {"googleSheetsOAuth2Api": {"id": "Zjoxh2BUZ6VXGQhA", "name": "Google Sheets account"}}, "typeVersion": 4.5}, {"id": "bb25e12c-b61a-40e5-bdd8-80412da4b64b", "name": "Code", "type": "n8n-nodes-base.code", "position": [800, 300], "parameters": {"jsCode": "return $input.first().json.output"}, "typeVersion": 2}, {"id": "9830a8f1-02f1-4503-842e-c511937ae60d", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [1100, 660], "parameters": {"options": {}}, "typeVersion": 3}, {"id": "bfa9e131-7896-4377-aca5-f2241ae2e155", "name": "Search Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1800, 680], "parameters": {"text": "=Extract the Yelp URL and Desc\n\n{{ $json.result.content[0].text }}\n\nMake sure to return the data in JSON", "batching": {}, "promptType": "define", "hasOutputParser": true}, "retryOnFail": true, "typeVersion": 1.7}, {"id": "83af94c4-ef2c-4ccb-b1da-b47062041df7", "name": "Google Gemini Chat Model for Google Search", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1800, 860], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "885afe32-d794-41a4-b532-510ea291bd55", "name": "Structured Output Parser for Google Search", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [1980, 860], "parameters": {"schemaType": "manual", "inputSchema": "{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"url\": {\n\t\t\t\"type\": \"string\"\n\t\t},\n\t\t\"desc\": {\n\t\t\t\"type\": \"string\"\n\t\t}\n\t}\n}"}, "typeVersion": 1.2}, {"id": "1f040a8f-b122-4cae-8ad6-55c953917bc3", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [1300, 680], "webhookId": "2f6a2861-e0ff-4265-95bc-143f755d0c76", "parameters": {}, "typeVersion": 1.1}, {"id": "076b2f50-fcb0-465f-8953-4e9446d9a70a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [760, -400], "parameters": {"color": 3, "width": 440, "height": 120, "content": "## Disclaimer\nThis template is only available on n8n self-hosted as it's making use of the community node for MCP Client."}, "typeVersion": 1}, {"id": "3d8eb777-fe62-4473-8a4c-d12c43324e74", "name": "MCP Search Client", "type": "n8n-nodes-mcp.mcpClient", "position": [1540, 680], "parameters": {"toolName": "search_engine", "operation": "executeTool", "toolParameters": "={\n  \"query\": \"{{ $json.name }} in Yelp\"\n} "}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "typeVersion": 1}, {"id": "7546160a-fd9f-48c5-b6c0-f8fbf3171b87", "name": "MCP Client for Web Scraping", "type": "n8n-nodes-mcp.mcpClient", "position": [2240, 680], "parameters": {"toolName": "scrape_as_markdown", "operation": "executeTool", "toolParameters": "={\n  \"url\": \"{{ $json.output.url }}\"\n} "}, "credentials": {"mcpClientApi": {"id": "JtatFSfA2kkwctYa", "name": "MCP Client (STDIO) account"}}, "typeVersion": 1}, {"id": "10f51630-b469-4ae3-ba29-0f1633262e86", "name": "Create a binary data for Structured Data Extract for Yelp", "type": "n8n-nodes-base.function", "position": [2500, 680], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "142eb53d-be26-4fcc-bab4-3448dbeb96c0", "name": "Write the Yelp content to disk", "type": "n8n-nodes-base.readWriteFile", "position": [2740, 680], "parameters": {"options": {}, "fileName": "=d:\\Yelp_Response_{{ new Date().toISOString().replace(/[:.]/g, '-')}}.json", "operation": "write"}, "typeVersion": 1}, {"id": "626051c8-037d-4dad-a8d2-3268ede2e803", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1040, 580], "parameters": {"color": 5, "width": 1880, "height": 440, "content": "## Data Enrichment with Yelp data extraction\n"}, "typeVersion": 1}, {"id": "16b82333-c8df-4614-b1ef-3e58078f61f7", "name": "Sticky Note6", "type": "n8n-nodes-base.stickyNote", "position": [1620, -240], "parameters": {"color": 6, "width": 660, "height": 260, "content": "## Step 1 : Set the Input Fields\n\nurl : https://www.google.com/maps/search/\nwebhook notification url: https://webhook.site/c9118da2-1c54-460f-a83a-e5131b7098db\nsearch : dentists+in+texas/?q=dentists+in+texas\nzone : serp_api1\nstart : 0\nnum : 20\n"}, "typeVersion": 1}, {"id": "680fec55-f61a-4870-bd01-6308ee0828c0", "name": "Sticky Note7", "type": "n8n-nodes-base.stickyNote", "position": [2320, -240], "parameters": {"color": 6, "width": 660, "height": 260, "content": "## Step 2 : Set the Credentials\n\nSet the credentials for \n\n1. Bright Data\n2. Google Gemini "}, "typeVersion": 1}, {"id": "7448179e-4dc2-4665-9986-12aa7d925fb4", "name": "Sticky Note8", "type": "n8n-nodes-base.stickyNote", "position": [1620, 80], "parameters": {"color": 6, "width": 660, "height": 240, "content": "## Step 3 : Configure or Set the Output Nodes\n\n1. Disk location to persist\n2. Google Sheet"}, "typeVersion": 1}], "active": false, "pinData": {"Search Data Extractor": [{"json": {"output": {"url": "https://www.yelp.com/biz/texas-city-dental-texas-city", "desc": "TEXAS CITY DENTAL - Updated May 2025 - 18 Photos Yelp"}}}], "Google Maps Data Extractor": [{"json": {"output": [{"name": "Texas City Dental - Dentist in Texas City", "rating": 4.8, "address": "3448 Palmer <PERSON>, Texas City, TX 77590", "reviews": 674, "category": ["Dentist", "Dental laboratory", "Denture care center", "Endodontist", "Oral surgeon", "Orthodontist", "Pediatric dentist", "Periodontist", "Teeth whitening service"], "entity_id": ["9673584215857663405", "6574027299904374997"], "details_url": "/g/11h7ftffbx", "related_terms": ["dentist", "cosmetic_dentist", "dental_clinic", "dental_implants_periodontist", "teeth_whitening_service", "orthodontist", "denture_care_center", "pediatric_dentist"], "appointment_link": "https://txcitydental.com/"}, {"name": "Texas Dental", "rating": 4.9, "address": "5132 Village Creek Dr, Plano, TX 75093", "reviews": 1354, "category": ["Dentist", "Cosmetic dentist", "Dental implants periodontist"], "entity_id": ["9677148940277542199", "15703975205538868636"], "details_url": "/g/1tgcmpr0", "related_terms": ["dentist", "cosmetic_dentist", "dental_clinic"], "appointment_link": "https://patient.modento.io/plano-texas-dental/consult"}, {"name": "North Texas Dental", "rating": 4.9, "address": "2515 Masters St, Sherman, TX 75090", "reviews": 1623, "category": ["Dentist", "Dental implants periodontist", "Denture care center", "Teeth whitening service"], "entity_id": ["9677259612218511763", "14671019117677462783"], "details_url": "/g/1wd3wg17", "related_terms": ["dentist", "dental_implants_periodontist", "denture_care_center", "teeth_whitening_service"], "appointment_link": "https://www.ntdsherman.com/#myModal"}, {"name": "6 to 9 Dental Texas", "rating": 4.9, "address": "2603 SE Military Dr Ste 103, San Antonio, TX 78223", "reviews": 113, "category": ["Dentist", "Dental clinic", "Dental implants provider", "Emergency dental service", "Endodontist", "Oral surgeon", "Prosthodontist", "Teeth whitening service", "Walk-in clinic"], "entity_id": ["9681711930004589935", "14121951165737821532"], "details_url": "/g/1ts2_px7", "related_terms": ["dentist", "dental_clinic", "dental_implants_provider", "emergency_dental_service", "endodontist", "oral_surgeon", "prosthodontist", "teeth_whitening_service", "walk_in_clinic"], "appointment_link": "http://www.vphumphrey2dds.com/"}, {"name": "Cosmetic Dental Texas", "rating": 4.6, "address": "2101 Crawford St STE 103, Houston, TX 77002", "reviews": 234, "category": ["Dentist", "Cosmetic dentist", "Dental hygienist", "Health consultant", "Teeth whitening service"], "entity_id": ["9673942104093032889", "6644082296499224433"], "details_url": "/g/1ts_6_5b", "related_terms": ["dentist", "cosmetic_dentist", "dental_hygienist", "health_consultant", "teeth_whitening_service"], "appointment_link": "https://cosmeticdentaltexas.com/"}, {"name": "Texas Dental", "rating": 4.9, "address": "6206 Farm-To-Market Rd 78, San Antonio, TX 78244", "reviews": 567, "category": ["Dentist", "Cosmetic dentist", "Dental clinic", "Dental hygienist", "Dental implants provider", "Emergency dental service", "Orthodontist", "Pediatric dentist", "Teeth whitening service"], "entity_id": ["9681881174741169099", "15832460407262855114"], "details_url": "/g/1tcvv8l5", "related_terms": ["dentist", "cosmetic_dentist", "dental_clinic", "dental_hygienist", "dental_implants_provider", "emergency_dental_service", "orthodontist", "pediatric_dentist", "teeth_whitening_service"], "appointment_link": "https://www.texas-dental.com/"}, {"name": "Natural Dentistry of North Texas", "rating": 4.9, "address": "1645 N Town E Blvd #532, Mesquite, TX 75150", "reviews": 109, "category": ["Dentist", "Cosmetic dentist", "Dental clinic", "Dental implants periodontist", "Denture care center", "Doctor", "Emergency dental service", "Orthodontist", "Pediatric dentist", "Teeth whitening service"], "entity_id": ["9677855844448877255", "6619454455705348065"], "details_url": "/g/1w45608g", "related_terms": ["dentist", "cosmetic_dentist", "dental_clinic", "dental_implants_periodontist", "denture_care_center", "doctor", "emergency_dental_service", "orthodontist", "pediatric_dentist", "teeth_whitening_service"], "appointment_link": "https://www.naturaldentistryofnorthtexas.com/?utm_source=local&utm_medium=organic&utm_campaign=gmb&utm_term=home"}, {"name": "Heart of Texas Smiles General & Cosmetic Dentistry", "rating": 4.9, "address": "117 <PERSON>, Waco, TX 76712", "reviews": 1531, "category": ["Dentist", "Cosmetic dentist", "Dental clinic", "Dental hygienist", "Dental implants provider", "Orthodontist"], "entity_id": ["9678099339216434095", "2187168089587898937"], "details_url": "/g/12hn7tnrq", "related_terms": ["dentist", "cosmetic_dentist", "dental_clinic", "dental_hygienist", "dental_implants_provider", "orthodontist"], "appointment_link": "https://www.wacofamilydental.com/request-an-appointment.html"}, {"name": "Smile 4 Texas Dental Center", "rating": 4.9, "address": "1400 Blalock Rd D1, Houston, TX 77055", "reviews": 812, "category": ["Dentist", "Cosmetic dentist", "Dental clinic", "Dental hygienist", "Dental implants provider", "Emergency dental service"], "entity_id": ["9673948559547960793", "10154247486966080020"], "details_url": "/g/1q665w1n4", "related_terms": ["pediatric_dentist", "dental_clinic", "dental_implants_periodontist", "dentist", "endodontist", "prosthodontist"], "appointment_link": "http://www.dentalclinicsoftexas.com/"}, {"name": "Texas Dental Center", "rating": 4.9, "address": "9801 Bissonnet St Ste K, Houston, TX 77036", "reviews": 920, "category": ["Dentist", "Cosmetic dentist", "Dental implants periodontist", "Dental clinic", "Pediatric dentist"], "entity_id": ["9673946025354891219", "2619898000467686041"], "details_url": "/g/113fzvfyr", "related_terms": ["dentist", "cosmetic_dentist", "dental_implants_periodontist", "dental_clinic", "pediatric_dentist"], "appointment_link": "https://www.texasdentalcenter.com/?utm_source=GMB&utm_medium=Organic&utm_campaign=LocalBusiness"}, {"name": "Ace Dental of Harker Heights", "rating": 4.6, "address": "300 E Central Texas Expy Suite 300, Harker Heights, TX 76548", "reviews": 687, "category": ["Dentist", "Cosmetic dentist", "Dental clinic", "Dental implants periodontist", "Dental implants provider", "Emergency dental service", "Oral surgeon", "Periodontist", "Teeth whitening service"], "entity_id": ["9674049914126201417", "3494220525942607347"], "details_url": "/g/1tdvkknl", "related_terms": ["dentist", "cosmetic_dentist", "dental_implants_periodontist", "dental_implants_provider", "emergency_dental_service", "oral_surgeon", "periodontist", "teeth_whitening_service"], "appointment_link": "https://harkerheights.acedentaloftexas.com/schedule-online"}, {"name": "West Texas Dental Associates", "rating": 5, "address": "3315 64th St STE A, Lubbock, TX 79413", "reviews": 496, "category": ["Dentist", "Cosmetic dentist"], "entity_id": ["9675220387914363341", "14302348036659727856"], "details_url": "/g/11f0x068sr", "related_terms": ["dentist", "cosmetic_dentist"], "appointment_link": "http://www.westtxdental.com/"}, {"name": "Central Texas Family Dental", "rating": 4.9, "address": "4104 <PERSON> #4, Killeen, TX 76542", "reviews": 188, "category": ["Dentist"], "entity_id": ["9727332640569304831", "9357406137681505810"], "details_url": "/g/1tl8l4rw", "related_terms": ["dentist"], "appointment_link": "https://www.centexdds.com/"}, {"name": "Texas Kids Dental Care", "rating": 4.8, "address": "3650 Joe Battle Blvd, El Paso, TX 79938", "reviews": 965, "category": ["Dentist"], "entity_id": ["9675220924038729639", "12674832566120650126"], "details_url": "/g/113ddy7s1", "related_terms": ["pediatric_dentist", "dental_clinic", "dental_implants_periodontist", "dentist", "endodontist", "prosthodontist"], "appointment_link": "http://texaskidsdental.com/"}, {"name": "Texas Dental Specialists - Dr. <PERSON>", "rating": 4.9, "address": "5940 W Parker Rd STE 103, Plano, TX 75093", "reviews": 296, "category": ["Dentist", "Cosmetic dentist", "Dental implants provider", "Dental clinic", "Emergency dental service", "Oral surgeon"], "entity_id": ["9720815084433700583", "17775872543482837529"], "details_url": "/g/11bxgblfk0", "related_terms": ["dentist", "cosmetic_dentist", "dental_implants_provider", "dental_clinic", "emergency_dental_service", "oral_surgeon"], "appointment_link": "https://www.texasdentalspecialists.com/"}]}}]}, "settings": {"executionOrder": "v1"}, "versionId": "9b2792c7-0797-4d4b-b2a3-d6c05e6d16e7", "connections": {"Code": {"main": [[{"node": "Update Google Sheets for Structured Data", "type": "main", "index": 0}, {"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "MCP Search Client", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Wait", "type": "main", "index": 0}]]}, "Set input fields": {"main": [[{"node": "Perform Bright Data Web Request", "type": "main", "index": 0}]]}, "MCP Search Client": {"main": [[{"node": "Search Data Extractor", "type": "main", "index": 0}]]}, "Search Data Extractor": {"main": [[{"node": "MCP Client for Web Scraping", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Google Maps Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Google Maps Data Extractor", "type": "ai_outputParser", "index": 0}]]}, "Google Maps Data Extractor": {"main": [[{"node": "Code", "type": "main", "index": 0}, {"node": "Create a binary data for Structured Data Extract", "type": "main", "index": 0}, {"node": "Initiate a Webhook Notification for Structured Data", "type": "main", "index": 0}]]}, "MCP Client for Web Scraping": {"main": [[{"node": "Create a binary data for Structured Data Extract for Yelp", "type": "main", "index": 0}]]}, "Write the Yelp content to disk": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Perform Bright Data Web Request": {"main": [[{"node": "Google Maps Data Extractor", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set input fields", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for Google Search": {"ai_languageModel": [[{"node": "Search Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser for Google Search": {"ai_outputParser": [[{"node": "Search Data Extractor", "type": "ai_outputParser", "index": 0}]]}, "Create a binary data for Structured Data Extract": {"main": [[{"node": "Write the structured content to disk", "type": "main", "index": 0}]]}, "Create a binary data for Structured Data Extract for Yelp": {"main": [[{"node": "Write the Yelp content to disk", "type": "main", "index": 0}]]}}}