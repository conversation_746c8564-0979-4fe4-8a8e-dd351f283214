{"name": "Veo 3 FAL video generation", "nodes": [{"parameters": {"rule": {"interval": [{"field": "seconds"}]}}, "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [20, 605], "id": "4edae95f-3433-4ba5-9731-d5f95149ca9e"}, {"parameters": {"promptType": "define", "text": "Generate a creative video idea for a Yeti vlogger character. The video should be something that would go viral on social media platforms like TikTok. Make it funny, relatable, and visually engaging. Focus on winter/mountain themes that would work well with a Yeti character.\n\nYeti is showing what cooks in a day, meat fish or otherwise. Start with a cooking scene", "hasOutputParser": true, "options": {}}, "name": "Ideas AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [272, 505], "id": "90cd0f42-4509-49e0-bdd4-236272f26261"}, {"parameters": {}, "name": "Think Ideas", "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [360, 725], "id": "763bc1f7-d755-4718-ac49-5ed1549eaa65"}, {"parameters": {"jsonSchemaExample": "{\n  \"idea\": \"A Yeti vlogger giving winter survival tips while accidentally demonstrating what NOT to do\",\n  \"caption\": \"When the Yeti gives advice but chaos ensues! 🏔️❄️ #YetiLife #WinterFails #Vlogger #Funny\",\n  \"environment\": \"Snowy mountain landscape with pine trees, overcast sky, natural outdoor lighting\",\n  \"status\": \"for_production\"\n}"}, "name": "Ideas Output Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [480, 725], "id": "5dc4703d-cc10-4233-83e7-147d0158b2f8"}, {"parameters": {"promptType": "define", "text": "=Create a detailed Veo3 video prompt for this idea: {{ $json.output.idea }}\n\nEnvironment setting: {{ $json.output.environment }}\n\nMake sure the prompt is optimized for high-quality, engaging video generation that would work well for social media content. Be extremely descriptive and specific. Include subject, context, action, style, camera movement, and composition. Specify visual details like lighting, colors, and atmosphere. Mention video style (realistic, cinematic, documentary, etc.). Include camera movement instructions (static shot, tracking, aerial view, etc.). Be clear about the main action or movement. Specify the mood and tone. Keep prompts under 500 words but be as detailed as possible. Output ONLY the final video prompt text, nothing else.\n\n\nCreate only the first scene and what the character says.\n\nThe shot must be vlog style -  A cinematic handheld selfie-style video shot. Do not add quotes \"\" around dialogues.\n\n!!IMPORTANT!! - YOU MUST include in the prompt something like \"slightly amused expression as he whispers/screams/shouts/other \" relevant to the scene before you add \"He/She says:\" in the next line. Refer to the example prompt. What the character says MUST be on the same line as \"He/She says\"\n\nYou MUST include Time of day, Lens, Film stock, and audio as seen in the example prompt.\n\nYou MUST always include  \"Vertical video format\" and \"Subtitles: Off\" in the prompt.\n\n<example_prompt>\nA cinematic handheld selfie-style video shot, showing a young man in ancient Middle Eastern robes with shoulder-length dark hair and a short beard, holding the camera like a selfie cam. He’s inside a dimly lit stone den with rugged cave walls. Behind him, several large lions slowly pace or rest, casting shadows in the flickering torchlight. The man speaks directly into the camera he holds, slightly amused expression as he whispers.\nHe says: Alright, welcome to my crib. That one is asleep, he gets grouchy in the mornings.\n\nHe looks happy as he whispers, as if he knows a secret. He pans the camera to show the lions in the background behind him. \n\nThe light pours from the cave entrance above that illuminates the dark cave chamber with the lions below. \n\nVertical video format. \nTime of Day: day\nLens: ultra-wide selfie lens, shallow depth of field\nFilm Stock: vintage 35mm documentary style, selfie camera view\nAudio: (implied) ambient lion growls \nBackground: Lions sleeping behind the young man.\nSubtitles: Off\n</example_prompt>", "options": {}}, "name": "Prompts AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [680, 605], "id": "9192add6-c3e2-433e-9f5d-87ac7bb178b2"}, {"parameters": {}, "name": "Think Prompts", "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [768, 825], "id": "1e552bbe-c04c-41cd-a528-b633175d3c59"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-mini", "mode": "id"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [240, 725], "id": "588a802c-23f7-4246-bf32-308f790f858d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "lDvwxzCoHaDIgIrM", "name": "OpenAi account"}}}, {"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [280, 1040], "id": "5490be2c-f90b-4778-a8b7-8cc1f41d592d", "name": "Wait1", "webhookId": "15a2f9bf-6947-485d-a13b-1f2e6091ac97", "disabled": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "743ebece-a200-436a-a7cb-62f33f1f7938", "leftValue": "={{ $json.status }}", "rightValue": "COMPLETED", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [680, 1040], "id": "89a35df0-3a0c-4655-8358-2d71d70e45a9", "name": "If1"}, {"parameters": {"url": "=https://queue.fal.run/fal-ai/veo3/requests/{{ $json.request_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "authorization", "value": "Key 921e5646-9b39-47d6-8fc9-baf729863c45:342dd72ef4ba33c53133f4140d5d7c94"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [960, 1020], "id": "3864779b-1c92-4127-8e6f-ae637f7e4d7f", "name": "Get Images"}, {"parameters": {"url": "={{ $json.status_url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "authorization", "value": "Key 921e5646-9b39-47d6-8fc9-baf729863c45:342dd72ef4ba33c53133f4140d5d7c94"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 960], "id": "77cde681-4ce8-436d-8ed6-9787cbabaf94", "name": "Check Status"}, {"parameters": {"method": "POST", "url": "https://queue.fal.run/fal-ai/veo3", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "authorization", "value": "Key 921e5646-9b39-47d6-8fc9-baf729863c45:342dd72ef4ba33c53133f4140d5d7c94"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "prompt", "value": "={{ $json.output }}"}, {"name": "aspect_ratio", "value": "16:9"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [20, 1040], "id": "ebb94e36-f127-427f-8dab-0ae0a00ba857", "name": "FAL1"}, {"parameters": {"content": "### Veo3 Video Generation with Fal AI\n\nThis workflow automates the entire video creation pipeline, from generating an idea to rendering a video using the Veo3 model hosted by Fal AI.\n\n**Workflow Breakdown:**\n\n1.  **Schedule Trigger**: Kicks off the workflow automatically at a set interval.\n\n2.  **Ideas AI Agent**: Generates a creative, viral video concept based on an initial prompt about a Yeti vlogger.\n\n3.  **Ideas Output Parser**: Structures the AI-generated idea into a clean JSON format (`idea`, `caption`, `environment`).\n\n4.  **Prompts AI Agent**: Takes the structured idea and crafts a highly detailed, descriptive prompt optimized for the Veo3 video model. This prompt includes specifics on camera shots, character actions, dialogue, lighting, and mood.\n\n5.  **FAL1 (HTTP Request)**: Sends the final, detailed prompt to the Fal AI API endpoint to start the video generation job.\n\n6.  **Polling Loop (`Wait` -> `Check Status` -> `If`)**: After submitting the job, the workflow enters a loop. It waits for 10 seconds, checks the status of the video generation, and continues looping until the status is `COMPLETED`.\n\n7.  **Get Images (HTTP Request)**: Once the video is ready, this node fetches the final video file from the URL provided by Fal AI.\n\n---\n\n### How to Set Up Fal AI\n\nTo use this workflow, you need to configure your Fal AI credentials.\n\n1.  **Create Account**: Sign up for an account on [fal.ai](https://fal.ai).\n\n2.  **Get API Key**: Navigate to the **Keys** section in your Fal AI dashboard and generate a new API key. The key will be in the format `Key YOUR_KEY_ID:YOUR_KEY_SECRET`.\n\n3.  **Update HTTP Nodes**: You must copy your full Fal AI key and paste it into the `Authorization` field in the headers of the three **HTTP Request** nodes:\n    * `FAL1`\n    * `Check Status`\n    * `Get Images`\n\n---\n\n### Fal Veo3 Documentation\n\nThe Veo3 model on Fal AI is very new. For detailed information on its capabilities and advanced prompt engineering, refer to the official documentation:\n\n* **Fal AI Veo3 Model Page**: [https://fal.ai/models/fal-ai/veo3](https://fal.ai/models/fal-ai/veo3)\n* **Official Fal AI Blog**: [https://blog.fal.ai/veo-3/](https://blog.fal.ai/veo-3/)\n* **General Fal AI Docs**: [https://docs.fal.ai/](https://docs.fal.ai/)\n", "height": 1600, "width": 360}, "name": "Workflow Explanation & Setup1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-520, 240], "id": "6a476167-5cc6-4832-bf05-5118354956ee"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Ideas AI Agent", "type": "main", "index": 0}]]}, "Ideas AI Agent": {"main": [[{"node": "Prompts AI Agent", "type": "main", "index": 0}]]}, "Think Ideas": {"ai_tool": [[{"node": "Ideas AI Agent", "type": "ai_tool", "index": 0}]]}, "Ideas Output Parser": {"ai_outputParser": [[{"node": "Ideas AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Prompts AI Agent": {"main": [[{"node": "FAL1", "type": "main", "index": 0}]]}, "Think Prompts": {"ai_tool": [[{"node": "Prompts AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Ideas AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "Prompts AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Wait1": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Get Images", "type": "main", "index": 0}], [{"node": "Wait1", "type": "main", "index": 0}]]}, "Get Images": {"main": [[]]}, "Check Status": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "FAL1": {"main": [[{"node": "Wait1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c46cc3a2-21d9-457a-b549-be3837f74aa4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b8253bd61ad9e23db8a3868c074c173e560d91393b5f2d52b37201d5af5b9b67"}, "id": "J28shUm6xpsfxeOJ", "tags": []}