{"nodes": [{"name": "<PERSON><PERSON>", "type": "n8n-nodes-base.netlifyTrigger", "position": [450, 300], "webhookId": "df7efc17-09bb-4409-9f6f-09bd5e59546e", "parameters": {"event": "submissionCreated", "formId": "615ad58f9f491e00070abac5", "siteId": "b585059c-a19a-487c-831f-c57af6f13bd1"}, "credentials": {"netlifyApi": "Netlify account"}, "typeVersion": 1}, {"name": "Set", "type": "n8n-nodes-base.set", "position": [650, 300], "parameters": {"values": {"string": [{"name": "Name", "value": "={{$json[\"name\"]}}"}, {"name": "Email", "value": "={{$json[\"email\"]}}"}, {"name": "Role", "value": "={{$json[\"role\"][0]}}"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"name": "Airtable", "type": "n8n-nodes-base.airtable", "position": [850, 300], "parameters": {"table": "Table 1", "options": {}, "operation": "append", "application": "apphwBsFxzjDPDBA8"}, "credentials": {"airtableApi": "Airtable Credentials @n8n"}, "typeVersion": 1}], "connections": {"Set": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Netlify Trigger": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}}