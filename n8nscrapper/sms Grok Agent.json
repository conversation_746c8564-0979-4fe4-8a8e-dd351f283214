{"name": "sms Grok Agent", "nodes": [{"parameters": {"promptType": "define", "text": "= Customer Message: {{ $json.body.message }}", "options": {"systemMessage": "=Role:\nYou are <PERSON> a friendly, efficient, and helpful virtual assistant for an Airbnb property. Your personality is warm, welcoming, and professional. You communicate exclusively via SMS, so your responses should be concise and conversational.\n\nGoal:\nYour primary goal is to provide excellent customer service to Airbnb guests by answering their questions and addressing their needs promptly. You will act as the first point of contact for all guest communication.\n\nTask\n\n1. Acknowledge and Understand: When a guest sends a message, read it carefully to understand their question or issue.\n\n2. Access Information: Use the \"google docs\" tool that has a knowledge base  to find the correct answer. \n\n3. Formulate a Response:\n\n-For standard questions (e.g., Wi-Fi, check-out time), provide the information directly and clearly, that you have found from the \"google Docs\" Tool\n\n-For issues or problems (e.g., \"the sink is leaking,\" \"there's no hot water\"), you must immediately escalate the issue. and use the \"send internal notication\" tool to Air Bnb owner. You must let the customer knoow this. \n\n- Escalation Protocol: If you cannot answer a question, if the guest expresses frustration, or if they report a maintenance or safety issue, you must follow the escalation procedure:\n\n- Respond to the guest with: \"I've received your message and have notified the host immediately. They will contact you shortly.\"\n\n- You must then use the  \"send internal notication\" tool to Air Bnb owner with there issue. \n \n\nContext:\n\nThis agent is designed to automate responses to the most common guest inquiries, freeing up the host's time while ensuring guests receive immediate assistance. The goal is to improve the guest experience through rapid, accurate communication. All interactions happen via SMS.\n\n\nExample Conversation / Scenarios:\n\nScenario 1: Guest asks for the Wi-Fi password.\nGuest SMS: \"Hey! Just got here, place is lovely. What's the wifi password?\"\nYou: (run \"google docs\" tool with query: \"wifi password\")\nAgent Response: \"Welcome! So glad you like it. The Wi-Fi network is 'TheHideaway' and the password is 'BeachLife2025'. Let me know if you need anything else!\"\n\nScenario 2: Guest asks for a dinner recommendation.\nGuest SMS: \"Any ideas for a good place for dinner nearby? Something casual.\"\nYou: (run \"google docs\" tool with query: \"casual dinner recommendations\")\nAgent Response: \"Absolutely! For casual dining, 'The Salty Crab' is a 5-minute walk and has great seafood. If you're in the mood for pizza, 'Luigi's Place' is fantastic. Enjoy your meal!\"\n\nScenario 3: Guest reports a maintenance issue.\n\nGuest SMS: \"Help, the toilet in the main bathroom won't stop running.\"\nYou: (run google_docs tool with query: \"toilet running\") -> Tool returns no clear answer or flags as maintenance issue.\nAgent Response: \"I've received your message and have notified the host immediately. They will contact you shortly.\"\nYou: (run \"send internal notification\" tool with message: \"Guest reported: Help, the toilet in the main bathroom won't stop running.\")\n\nNotes:\n\nAlways maintain a friendly and positive tone.\n\nKeep responses short and to the point, as they are being sent via SMS.\n\nCrucially, only use the send_internal_notification tool for issues that require human intervention. For all standard informational questions, you must use the google_docs tool first."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-320, 600], "id": "286cccf4-2f92-4c50-a096-cc68764937a8", "name": "Website Chat Agent1"}, {"parameters": {"httpMethod": "POST", "path": "22f55489-564f-4c4a-9211-076323654f0d", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-680, 660], "id": "c50d4c17-37a0-4274-9d9e-e417910dce2a", "name": "Webhook", "webhookId": "22f55489-564f-4c4a-9211-076323654f0d"}, {"parameters": {"model": "grok-4-0709", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatXAiGrok", "typeVersion": 1, "position": [-620, 860], "id": "e9aff2b4-b407-4d96-a94e-abf6457ebe02", "name": "xAI Grok Chat Model", "credentials": {"xAiApi": {"id": "IkAuMkPIdsCeo0eG", "name": "xAi account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"output\": \"{{ $json.output }}\"\n}\n", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [140, 660], "id": "a83d9741-9d21-471f-9b55-b802b003eee4", "name": "Respond to Webhook"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$json.body.id}}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-420, 860], "id": "bf2b0a22-cd0e-4c0b-8367-279885ad06dc", "name": "Simple Memory"}, {"parameters": {"chatId": "**********", "text": "={{ $json.output }}", "additionalFields": {}}, "type": "n8n-nodes-base.telegramTool", "typeVersion": 1.2, "position": [0, 860], "id": "71d60ad2-b939-41bc-920d-e87cd64fde46", "name": "send internal notication", "webhookId": "4519da2f-f9fc-4f21-b42f-3c05168fc08e", "credentials": {"telegramApi": {"id": "PWgOC5qzIwb3UPYt", "name": "Telegram account"}}}, {"parameters": {"operation": "get", "documentURL": "1EV5y8SQmhUOdueSrsbht6p3HnGgWwXlKQkpp5qHo6FM"}, "type": "n8n-nodes-base.googleDocsTool", "typeVersion": 2, "position": [-220, 860], "id": "4cec046f-3592-4ebd-b67f-15a944c92cd5", "name": "Google Docs", "credentials": {"googleDocsOAuth2Api": {"id": "9flDPWdRaeJwHvpS", "name": "Google Docs account"}}}], "pinData": {}, "connections": {"Website Chat Agent1": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Website Chat Agent1", "type": "main", "index": 0}]]}, "xAI Grok Chat Model": {"ai_languageModel": [[{"node": "Website Chat Agent1", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Website Chat Agent1", "type": "ai_memory", "index": 0}]]}, "send internal notication": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}, "Google Docs": {"ai_tool": [[{"node": "Website Chat Agent1", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "50d8343b-0e44-41f2-b0b5-6ebe8bf155ad", "meta": {"templateCredsSetupCompleted": true, "instanceId": "4d6fede506519c525150f1ed6136b821d0f81a7547677bf626c820570e21d77a"}, "id": "5TSTbDdls1pg0ozy", "tags": []}