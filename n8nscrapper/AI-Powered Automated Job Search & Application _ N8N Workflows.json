{"nodes": [{"parameters": {"assignments": {"assignments": [{"id": "99d327d4-7bed-4b76-9150-75247e4eaf0f", "name": "job_title", "type": "string", "value": "={{ $(&apos;Webhook&apos;).item.json.body.jobSearchKeyword }}"}]}, "options": {}}, "id": "70caa4ed-feff-499c-9f62-76c63ef44258", "name": "3️⃣Set Job Title", "type": "n8n-nodes-base.set", "position": [816, 240], "typeVersion": 3.4}, {"parameters": {"url": "=https://api.adzuna.com/v1/api/jobs/us/search/1?app_id=YOUR_ADZUNA_APP_ID&app_key=YOUR_ADZUNA_APP_KEY&results_per_page=20&what={{ $json.job_title }}\n", "options": {}}, "id": "dc1a078c-8be1-4d90-99c8-0b87e29e0284", "name": "4️⃣Get Jobs from Adzuna", "type": "n8n-nodes-base.httpRequest", "position": [1040, 240], "typeVersion": 4}, {"parameters": {"model": "deepseek/deepseek-r1", "options": {}}, "id": "95f22931-9258-4860-aa06-d5d94e2454bb", "name": "OpenRouter <PERSON>", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [2288, 400], "typeVersion": 1, "credentials": {}}, {"parameters": {"promptType": "define", "text": "=Extracts the top skills and qualifications from this job description: \n{{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_description }}", "options": {"systemMessage": "You&apos;re an AI expert that extracts the top skills and qualifications from a job description. Return as JSON array."}}, "id": "18664c66-67a7-4b7c-881a-cd90f346a523", "name": "7️⃣Extract Skills from Job Description", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1920, 240], "executeOnce": true, "typeVersion": 2}, {"parameters": {"fieldToSplitOut": "results", "options": {}}, "id": "43312e89-42a8-4206-b192-35d7a9cf6ccc", "name": "📤 Split Jobs ", "type": "n8n-nodes-base.splitOut", "position": [1248, 240], "typeVersion": 1, "alwaysOutputData": false}, {"parameters": {"assignments": {"assignments": [{"id": "fa733dce-d1b9-4d5a-8d62-062259a5c288", "name": "job_description", "type": "string", "value": "={{ $json.description }}"}, {"id": "138373bf-97ba-43e1-bd43-6caf42af494d", "name": "job_url", "type": "string", "value": "={{ $json.redirect_url }}"}, {"id": "f023780b-9999-4167-88b0-9053a2735065", "name": "job_title", "type": "string", "value": "={{ $json.title }}"}, {"id": "ae7ada2d-8fdc-445d-ae84-fcad4a1d9233", "name": "company", "type": "string", "value": "={{ $json.company.display_name }}"}]}, "options": {}}, "id": "8469870e-775c-4674-beb1-47fb096de7e3", "name": "🧪 Extract Job Info", "type": "n8n-nodes-base.set", "position": [1696, 240], "typeVersion": 3.4}, {"parameters": {"jsCode": "const seen = new Set();\n\nreturn items.filter(item => {\n  const job = item.json;\n  const key = `${job.title}-${job.company.display_name}-${job.description.substring(0, 100)}`;\n  \n  if (seen.has(key)) return false;\n  seen.add(key);\n  return true;\n});\n"}, "id": "8b85d7c5-49e8-4b9e-a89b-bed683d6f023", "name": "Filter Duplicates", "type": "n8n-nodes-base.code", "position": [1472, 240], "typeVersion": 2}, {"parameters": {"httpMethod": "POST", "path": "jobmonkeyad<PERSON>na", "responseMode": "responseNode", "options": {"binaryPropertyName": "data"}}, "id": "197d6478-9cbb-4dea-a3dc-e7264e48f247", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [368, 240], "webhookId": "YOUR_WEBHOOK_ID", "typeVersion": 2}, {"parameters": {"model": "deepseek/deepseek-r1", "options": {}}, "id": "05b840e4-d44c-40d9-b095-8f3d6145ebaf", "name": "OpenRouter Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [448, 704], "typeVersion": 1, "credentials": {}}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "loose"}, "combinator": "and", "conditions": [{"id": "bd5a9af0-b569-4a75-9744-20699a5021d0", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ JSON.parse($json.output).score.toNumber() }}", "rightValue": 1}]}, "looseTypeValidation": true, "options": {}}, "id": "277ecd45-73a1-445f-bc51-99f8b49e82f5", "name": "📈 IF Score ≥ 3", "type": "n8n-nodes-base.if", "position": [736, 480], "typeVersion": 2.2}, {"parameters": {"promptType": "define", "text": "=Evaluate how well this resume&apos;s {{ $(&apos;Extract from File&apos;).item.json.text }} matches the job&apos;s required skills: {{ $json.output }}. \n\nRate it and ouput a score between 1 and 5\n", "options": {"systemMessage": "You are an expert AI assistant who evaluates resume-job fit. Focus only on comparing the work experience section of the resume to the job skills. Be strict but fair. Return only JSON. Do not make up data.\n\nYou&apos;re an AI assistant that scores how well a resume matches a job description. Return JSON: { \"score\": \"1-5\" }. Output a score between 1 and 5."}}, "id": "484d3a3c-82d2-4a03-b01b-d22d0f87c082", "name": "9️⃣Resume Match Score ", "type": "@n8n/n8n-nodes-langchain.agent", "position": [368, 480], "typeVersion": 2}, {"parameters": {"model": "deepseek/deepseek-r1:free", "options": {}}, "id": "ddd54c3a-e6c5-4ab5-aaab-bb07492df04a", "name": "OpenRouter Chat Model3", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [1440, 704], "typeVersion": 1, "credentials": {}}, {"parameters": {"promptType": "define", "text": "=Write a cover letter based on the Tailored resume:{{ $json.output }} and make it match the Job description from Adzuna: {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_description }} for a {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_title }} at {{ $(&apos;🧪 Extract Job Info&apos;).item.json.company }}\nI will be sending your output by email so please generate it as html that will well formatted fo rhuman reading.\n\nKeep it ATS-optimized and human-readable.\n", "options": {"systemMessage": "=You&apos;re an AI that writes a personalized cover letter tailored for a job based on the tailored resume."}}, "id": "bd16d8d2-d0e0-41ac-b8ec-651e78de5cfb", "name": "🔥Write Cover Letter", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1328, 480], "typeVersion": 2}, {"parameters": {"promptType": "define", "text": "=If the {{ $json.output }} is between 3 and 5, rewrite this resume:  {{ $(&apos;Extract from File&apos;).item.json.text }} to highlight the following skills \n\nLet it be tailored for the role of {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_title }} at {{ $(&apos;🧪 Extract Job Info&apos;).item.json.company }}. Format the output as structured JSON with fields:\n\n- name\n- contact (email, phone)\n- education (degree, institution)\n- experience (title, company, duration, highlights[])\n- skills[]\n- certifications[]\n- summary\nYou can add other fields if necessary and they exist in the source resume user submitted. But do not make up things that don&apos;t exist. You must be factual.\n\nI will be sending your output by email so please generate it as html that will well formatted fo rhuman reading.\n\nKeep it ATS-optimized and human-readable.\n", "options": {"systemMessage": "=You&apos;re an AI that rewrites resumes to highlight specific skills and job fit.\n\n# ATS Resume Optimization Instructions\n\nYou are an expert resume optimizer specializing in beating Applicant Tracking Systems (ATS) while maintaining authenticity and readability for human recruiters.\n\n## Your Task\nRestructure the provided resume to maximize ATS compatibility and keyword matching for the specific job description, while ensuring the content remains truthful and compelling.\n\n## Input Data\n- **Original Resume**: {{ $(&apos;Extract from File&apos;).item.json.text }}\n- **Target Job Description**: {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_description }}\n- **Company Name**: {{ $(&apos;🧪 Extract Job Info&apos;).item.json.company }}\n\n## Optimization Requirements\n\n### 1. Format Optimization\n- Use clean, simple formatting with standard fonts\n- Employ standard section headings: \"Professional Summary,\" \"Work Experience,\" \"Education,\" \"Skills,\" \"Certifications\"\n- Remove any complex formatting, tables, or graphics\n- Ensure consistent formatting throughout\n- Use bullet points with standard symbols (•, -, *)\n\n### 2. Keyword Analysis & Integration\n- **Extract Keywords**: Identify all relevant keywords from the job description including:\n  - Technical skills and software\n  - Soft skills and competencies  \n  - Industry terminology and jargon\n  - Required qualifications and certifications\n  - Job titles and role-specific terms\n  - Action verbs used in job requirements\n\n- **Strategic Placement**: Integrate keywords naturally into:\n  - Professional summary (3-4 most critical keywords)\n  - Skills section (comprehensive keyword list)\n  - Work experience descriptions (context-appropriate usage)\n  - Education section (relevant coursework, projects)\n\n- **Keyword Matching Rules**:\n  - Use exact phrasing from job description when possible\n  - Include both full terms and abbreviations (e.g., \"Artificial Intelligence (AI)\")\n  - Maintain natural language flow\n  - Avoid keyword stuffing\n\n### 3. Content Restructuring\n\n#### Professional Summary\n- Create a 3-4 line summary that mirrors the job requirements\n- Lead with the most important keywords for the role\n- Quantify experience where possible\n- Align career trajectory with the position\n\n#### Work Experience\n- Reorder and emphasize experiences most relevant to the target role\n- Rewrite bullet points to incorporate job description keywords\n- Quantify achievements with specific metrics\n- Use action verbs that match the job posting\n- Highlight transferable skills for career changers\n\n#### Skills Section\n- Create a comprehensive skills section with relevant keywords\n- Organize into categories: Technical Skills, Software Proficiency, Languages, Certifications\n- Include both hard and soft skills mentioned in job description\n- Use exact terminology from the posting\n\n#### Education & Certifications\n- Highlight relevant coursework, projects, or academic achievements\n- Include any certifications that match job requirements\n- Add relevant keywords in descriptions\n\n### 4. ATS Compatibility Rules\n- Use standard date formats (MM/YYYY or Month YYYY)\n- Spell out abbreviations at least once\n- Use standard job titles when possible\n- Include city, state format for locations\n- Avoid headers and footers\n- Use clear section breaks\n\n### 5. Quality Assurance\n- Ensure all information remains truthful and accurate\n- Maintain professional tone and readability\n- Verify keyword integration feels natural\n- Check that the resume tells a coherent career story\n- Ensure the final version would appeal to human recruiters\n\n## Output Format\nProvide the optimized resume in clean, ATS-friendly format with:\n\n1. **Optimization Summary**: Brief explanation of key changes made\n2. **Keyword Integration Report**: List of keywords incorporated and their placement\n3. **ATS Compatibility Score**: Your assessment of how well the resume will perform\n4. **Complete Optimized Resume**: Full restructured resume ready for application\n\n## Important Notes\n- Never fabricate experience, skills, or qualifications\n- Focus on reframing existing experience rather than inventing new content\n- Maintain the candidate&apos;s authentic voice and career narrative\n- Prioritize the most critical 15-20 keywords rather than trying to include everything\n- Balance ATS optimization with human readability\n\n\nRemember: The goal is to help qualified candidates get past initial ATS screening to reach human recruiters, not to misrepresent their qualifications.\n"}}, "id": "ac58e6d2-4900-4aae-bbc2-0bb734a1b036", "name": "8️⃣Rewrite Resume", "type": "@n8n/n8n-nodes-langchain.agent", "position": [960, 480], "typeVersion": 2}, {"parameters": {"model": "deepseek/deepseek-r1", "options": {}}, "id": "397d0eb5-e605-452c-83db-bf09b776d5c7", "name": "OpenRouter Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [1056, 704], "typeVersion": 1, "credentials": {}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "mode": "list", "value": "YOUR_GOOGLE_SHEET_ID", "cachedResultUrl": "YOUR_GOOGLE_SHEET_URL", "cachedResultName": "Job Search Automation Product Database"}, "sheetName": {"__rl": true, "mode": "list", "value": 1612746852, "cachedResultUrl": "YOUR_GOOGLE_SHEET_URL#gid=1612746852", "cachedResultName": "update sheet"}, "columns": {"value": {"status": "draft", "job_url": "={{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_url }}", "cover_letter": "={{ $json.output }}", "tailored_resume": "={{ $(&apos;8️⃣Rewrite Resume&apos;).item.json.output }}"}, "schema": [{"id": "id", "type": "string", "display": true, "removed": true, "required": false, "displayName": "id", "defaultMatch": true, "canBeUsedToMatch": true}, {"id": "job_title", "type": "string", "display": true, "required": false, "displayName": "job_title", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_description", "type": "string", "display": true, "required": false, "displayName": "job_description", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "job_url", "type": "string", "display": true, "required": false, "displayName": "job_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "company", "type": "string", "display": true, "required": false, "displayName": "company", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "resume", "type": "string", "display": true, "removed": false, "required": false, "displayName": "resume", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "email", "type": "string", "display": true, "removed": false, "required": false, "displayName": "email", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "status", "type": "string", "display": true, "removed": false, "required": false, "displayName": "status", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "tailored_resume", "type": "string", "display": true, "removed": false, "required": false, "displayName": "tailored_resume", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "cover_letter", "type": "string", "display": true, "removed": false, "required": false, "displayName": "cover_letter", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["status"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "efe315eb-d796-4e23-8870-0bfad16f35b1", "name": "Upate sheets", "type": "n8n-nodes-base.googleSheets", "position": [1712, 480], "typeVersion": 4.6, "credentials": {}}, {"parameters": {"sendTo": "={{ $(&apos;Webhook&apos;).item.json.body.email }}", "subject": "=Job Application for {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_title }}", "message": "=<html>\n  <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n    <p>Hi,</p>\n    <p>\n      As requested, below are the Cover Letter and Tailored Resume\n      with which you can apply for the \n      <strong>{{ $json.cover_letter }}</strong> position at \n      <strong>{{ $(&apos;🧪 Extract Job Info&apos;).item.json.company }}</strong>.\n    </p>\n\n    <hr style=\"margin: 20px 0;\">\n\n    <p><strong>Your Cover Letter:</strong></p>\n    <div style=\"background: #f9f9f9; padding: 10px; border: 1px solid #ccc;\">\n      {{ $json.cover_letter }}\n    </div>\n\n    <hr style=\"margin: 20px 0;\">\n\n    <p><strong>Your Tailored Resume:</strong></p>\n    <div style=\"background: #f9f9f9; padding: 10px; border: 1px solid #ccc;\">\n      {{ $json.tailored_resume }}\n    </div>\n\n    <hr style=\"margin: 20px 0;\">\n\n    <p><strong>NEXT STEPS:</strong></p>\n    <ul>\n      <li>Review the cover letter and tailored resume to make sure you are satisfied.</li>\n      <li>Then, copy and paste the materials into your email.</li>\n      <li>Get the recruiter&apos;s details at the link below and send off your application.</li>\n    </ul>\n\n    <p>\n      🔗 <a href=\"{{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_url }}\" target=\"_blank\" style=\"color: #1a0dab;\">\n        View Job Posting on Adzuna\n      </a>\n    </p>\n\n    <hr style=\"margin-top: 30px;\">\n  </body>\n</html>\n", "options": {"appendAttribution": false, "replyToSenderOnly": true}}, "id": "96617cf6-7968-4ca2-9fd4-9fecd78b1b9a", "name": "📧Gmail", "type": "n8n-nodes-base.gmail", "position": [1936, 480], "webhookId": "YOUR_GMAIL_WEBHOOK_ID", "typeVersion": 2.1, "credentials": {}}, {"parameters": {"respondWith": "text", "responseBody": "=Hi,\nAs requested, below are the Cover Letter and Tailored Resume with which you can apply for the {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_title }} position at {{ $(&apos;🧪 Extract Job Info&apos;).item.json.company }}.\n\nYour Cover Letter:\n{{ $(&apos;Upate sheets&apos;).item.json.cover_letter }}\n\nYour Tailored Resume:\n{{ $(&apos;Upate sheets&apos;).item.json.tailored_resume }}\n\nNEXT STEPS:\n* Review the cover letter and tailored resume to make sure you are satisfied.\n* Then, copy and paste the materials into your email.\n* Get the recruiter&apos;s details at the link below and send off your application.\n\n🔗 View Job Posting on Adzuna: {{ $(&apos;🧪 Extract Job Info&apos;).item.json.job_url }}\n\nThank you\nThe JobMonkey team", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "text/html"}]}}}, "id": "ad39b65c-d383-446f-9b36-404d83a8d640", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [2128, 480], "typeVersion": 1.4}, {"parameters": {"operation": "pdf", "binaryPropertyName": "data0", "options": {}}, "id": "486d3479-52a2-40e9-b4c9-7f3b3fde8915", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [592, 240], "typeVersion": 1}, {"parameters": {"content": "## Step 2: Analyze Job, Match Resume to Job and Write a Customized Resume for the New Job=>\n", "height": 80, "width": 860, "color": 5}, "id": "65b7c9b6-c7aa-4547-a221-44e82eaf271d", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [-576, 512], "typeVersion": 1}, {"parameters": {"content": "## Step 1:Retrieve Job Data from adzuna.com Via API=>\n", "height": 80, "width": 620, "color": 5}, "id": "68b96518-9cda-4135-bbd6-c0db2717fa4e", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [-320, 272], "typeVersion": 1}], "connections": {"3️⃣Set Job Title": {"main": [[{"node": "4️⃣Get Jobs from Adzuna", "type": "main", "index": 0}]]}, "4️⃣Get Jobs from Adzuna": {"main": [[{"node": "📤 Split Jobs ", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "7️⃣Extract Skills from Job Description", "type": "ai_languageModel", "index": 0}]]}, "7️⃣Extract Skills from Job Description": {"main": [[{"node": "9️⃣Resume Match Score ", "type": "main", "index": 0}]]}, "📤 Split Jobs ": {"main": [[{"node": "Filter Duplicates", "type": "main", "index": 0}]]}, "🧪 Extract Job Info": {"main": [[{"node": "7️⃣Extract Skills from Job Description", "type": "main", "index": 0}]]}, "Filter Duplicates": {"main": [[{"node": "🧪 Extract Job Info", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "OpenRouter Chat Model1": {"ai_languageModel": [[{"node": "9️⃣Resume Match Score ", "type": "ai_languageModel", "index": 0}]]}, "📈 IF Score ≥ 3": {"main": [[], [{"node": "8️⃣Rewrite Resume", "type": "main", "index": 0}]]}, "9️⃣Resume Match Score ": {"main": [[{"node": "📈 IF Score ≥ 3", "type": "main", "index": 0}]]}, "OpenRouter Chat Model3": {"ai_languageModel": [[{"node": "🔥Write Cover Letter", "type": "ai_languageModel", "index": 0}]]}, "🔥Write Cover Letter": {"main": [[{"node": "Upate sheets", "type": "main", "index": 0}]]}, "8️⃣Rewrite Resume": {"main": [[{"node": "🔥Write Cover Letter", "type": "main", "index": 0}]]}, "OpenRouter Chat Model2": {"ai_languageModel": [[{"node": "8️⃣Rewrite Resume", "type": "ai_languageModel", "index": 0}]]}, "Upate sheets": {"main": [[{"node": "📧Gmail", "type": "main", "index": 0}]]}, "📧Gmail": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "3️⃣Set Job Title", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"instanceId": "2f281fde0de64f39316b38bf7aeff647de66c777fd2c9178aac0dbc0cd948eca", "templateCredsSetupCompleted": true}}