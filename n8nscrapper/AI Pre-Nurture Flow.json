{"name": "Pre-Call Nurture Flow", "flow": [{"id": 12, "module": "airtable:webhook", "version": 3, "parameters": {"__IMTHOOK__": 709812}, "mapper": {}, "metadata": {"designer": {"x": 0, "y": 300}, "restore": {"parameters": {"__IMTHOOK__": {"data": {"editable": "false"}, "label": "Draft outreach My Airtable webhook"}}}, "parameters": [{"name": "__IMTHOOK__", "type": "hook:airtable", "label": "Webhook", "required": true}]}}, {"id": 8, "module": "airtable:ActionGetRecord", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "filter": {"name": "", "conditions": [[{"a": "{{12.recordId}}", "o": "exist"}]]}, "mapper": {"id": "{{12.recordId}}", "base": "appyIUnoXEbjMcDIC", "table": "tblJ0dL96najl4cDJ", "useColumnId": false}, "metadata": {"designer": {"x": 300, "y": 300}, "restore": {"expect": {"base": {"mode": "chose", "label": "CRM Lead Nurturing"}, "table": {"mode": "chose", "label": "🖥️ CRM"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Lead's Name", "type": "text", "label": "Lead's Name"}, {"name": "Company Name", "type": "text", "label": "Company Name"}, {"name": "Type of Reminder", "type": "text", "label": "Type of Reminder"}, {"name": "Booking Date", "time": true, "type": "date", "label": "Booking Date"}, {"name": "15 Min Date&Time", "type": "text", "label": "15 Min Date&Time"}, {"name": "15 Min Reminder", "type": "text", "label": "15 Min Reminder"}, {"name": "15 Min Reminder?", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "1 Day Date&Time", "type": "text", "label": "1 Day Date&Time"}, {"name": "1 Day Reminder", "type": "text", "label": "1 Day Reminder"}, {"name": "1 Day Reminder?", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "2 Day Date&Time", "type": "text", "label": "2 Day Date&Time"}, {"name": "2 Day Reminder", "type": "text", "label": "2 Day Reminder"}, {"name": "2 Day Reminder?", "type": "boolean", "label": "2 Day Reminder?"}, {"name": "Pipeline Stage", "type": "text", "label": "Pipeline Stage"}, {"name": "Date Added", "type": "date", "label": "Date Added"}, {"name": "RecordID", "type": "text", "label": "RecordID"}, {"name": "Email", "type": "text", "label": "Email"}, {"name": "Call Transcription", "type": "text", "label": "Call Transcription", "multiline": true}, {"name": "📧 Email History", "spec": {"label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"name": "Last <PERSON><PERSON>", "type": "array", "label": "Last <PERSON><PERSON>"}, {"name": "Follow-Up Step", "type": "text", "label": "Follow-Up Step"}, {"name": "Exclude from Sequence", "type": "boolean", "label": "Exclude from Sequence"}]}}, {"id": 10, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 1797049}, "mapper": {"model": "o3-mini", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are a first name extractor & date formatter."}, {"role": "user", "content": "Your role is to extract both the first name and the booking date from the given input and return them in JSON format.\n\n\t•\tFirst Name: Extract only the first word from the full name, properly capitalized (first letter uppercase, rest lowercase), even if the input is all lowercase or all uppercase.\n\t•\tBooking Date: Format the booking date as “Month Day, Year” (e.g., “April 28, 2025”) by extracting only the date portion (ignore the time)."}, {"role": "user", "content": "{\"full_name\":\"<PERSON>\",\"booking_date\":\"April 28, 2025 4:00 PM\"}"}, {"role": "assistant", "content": "{\"first_name\":\"<PERSON>\", \"call_date\":\"April 28, 2025\"}"}, {"role": "user", "content": "{\"full_name\":\"anthony gorda<PERSON>\",\"booking_date\":\"May 2, 2025 9:30 AM\"}"}, {"role": "assistant", "content": "{\"first_name\":\"<PERSON>\", \"call_date\":\"May 2, 2025\"}"}, {"role": "user", "content": "{\"full_name\":\"{{8.`Lead's Name`}}\", \"booking_date\":\"{{8.`Booking Date`}}\"}"}], "max_tokens": "2048", "temperature": ".5", "n_completions": "1", "response_format": "json_object", "parseJSONResponse": true}, "metadata": {"designer": {"x": 600, "y": 300, "name": "Date & First Name"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "o3-mini (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}, {"role": {"mode": "chose", "label": "Assistant"}}, {"role": {"mode": "chose", "label": "User"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "JSON Object"}, "reasoning_effort": {"mode": "chose", "label": "Empty"}, "parseJSONResponse": {"mode": "chose"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "Cohort OpenAI connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "reasoning_effort", "type": "select", "label": "Reasoning Effort", "validate": {"enum": ["low", "medium", "high"]}}, {"name": "parseJSONResponse", "type": "boolean", "label": "Parse JSON Response", "required": true}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}], "advanced": true}}, {"id": 15, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 900, "y": 300}}, "routes": [{"flow": [{"id": 9, "module": "email:ActionSendEmail", "version": 7, "parameters": {"account": 2102505, "saveAfterSent": false}, "filter": {"name": "15 Min Reminder", "conditions": [[{"a": "{{8.`15 <PERSON>`}}", "b": "1", "o": "text:equal"}]]}, "mapper": {"to": ["<EMAIL>"], "subject": "See you in ~15 mins", "contentType": "html", "attachments": [], "cc": [], "bcc": [], "from": "", "sender": "", "replyTo": "", "inReplyTo": "", "references": [], "priority": "normal", "headers": [], "html": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystem, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n            font-size: 15px;\n            line-height: 1.5;\n            color: #000;\n        }\n    </style>\n</head>\n<body>\n\n<p>Hey {{10.result.first_name}},</p>\n\n<p>Very excited for our call in about 15 minutes.</p>\n\n<p>Quick heads up — might be 30 seconds late, just wrapping a call before we chat. </p>\n\n<p>If you need the link for our call, you can join directly here: <a href=\"{{8.`Meeting Link`}}\" target=\"_blank\">Join the call</a></p>\n\n<p>Talk soon!</p>\n\n<p>- Aryan</p>\n\n</body>\n</html>"}, "metadata": {"designer": {"x": 1200, "y": 0}, "restore": {"parameters": {"account": {"label": "My Google Restricted connection (<EMAIL>)", "data": {"scoped": "true", "connection": "google-restricted"}}, "saveAfterSent": {"label": "No"}}, "expect": {"to": {"mode": "chose", "items": [null]}, "contentType": {"label": "HTML"}, "attachments": {"mode": "chose"}, "cc": {"mode": "chose"}, "bcc": {"mode": "chose"}, "references": {"mode": "chose"}, "priority": {"label": "Normal"}}}, "parameters": [{"name": "account", "type": "account:smtp,google-restricted,microsoft-smtp-imap", "label": "Connection", "required": true}, {"name": "saveAfterSent", "type": "select", "label": "Save message after sending", "required": true, "validate": {"enum": [true, false]}}, {"type": "hidden"}], "expect": [{"name": "to", "type": "array", "label": "To", "required": true, "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "contentType", "type": "select", "label": "Content Type", "required": true, "validate": {"enum": ["html", "text"]}}, {"name": "attachments", "type": "array", "label": "Attachments", "spec": [{"name": "fileName", "label": "File name", "type": "filename", "required": true, "semantic": "file:name"}, {"name": "data", "label": "Data", "type": "buffer", "required": true, "semantic": "file:data"}, {"name": "cid", "label": "Content-ID", "type": "text"}]}, {"name": "cc", "type": "array", "label": "Copy recipient", "spec": {"name": "value", "type": "email", "label": "Email address"}}, {"name": "bcc", "type": "array", "label": "Blind copy recipient", "spec": {"name": "value", "type": "email", "label": "Email address"}}, {"name": "from", "type": "text", "label": "From"}, {"name": "sender", "type": "text", "label": "Sender"}, {"name": "replyTo", "type": "text", "label": "Reply-To"}, {"name": "inReplyTo", "type": "text", "label": "In-Reply-To"}, {"name": "references", "type": "array", "label": "References", "spec": {"type": "text", "label": "Reference", "required": true, "name": "value"}}, {"name": "priority", "type": "select", "label": "Priority", "validate": {"enum": ["high", "normal", "low"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "key", "label": "Key", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text", "required": true}]}, {"name": "html", "type": "text", "label": "Content"}], "advanced": true}}, {"id": 13, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{8.id}}", "base": "appyIUnoXEbjMcDIC", "table": "tblJ0dL96najl4cDJ", "record": {"fldqFMTc7DjZJ3jmw": true}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1500, "y": 0}, "restore": {"expect": {"base": {"label": "CRM Lead Nurturing"}, "table": {"label": "🖥️ CRM"}, "record": {"nested": {"fld7LPfzbfUgAZ5ZX": {"mode": "chose"}, "fld8hJUTEJL2Y97LF": {"mode": "chose"}, "fld9XcQETR2DJWSrf": {"mode": "edit"}, "fldB95KYJm9pfLDjH": {"mode": "edit"}, "fldC1KLk9mtyEwx5w": {"mode": "edit"}, "fldD8QewifDslc3DH": {"mode": "edit"}, "flddxAMKOSQPlw4vd": {"mode": "edit"}, "fldqFMTc7DjZJ3jmw": {"mode": "chose"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fld9VRttplLll5LgB", "type": "text", "label": "Lead's Name"}, {"name": "fldYAmykfIvhRSZdM", "type": "text", "label": "Company Name"}, {"mode": "edit", "name": "flddxAMKOSQPlw4vd", "type": "select", "label": "Type of Reminder"}, {"name": "fldWf0gqfLA7eWPaY", "time": true, "type": "date", "label": "Booking Date"}, {"name": "fldqFMTc7DjZJ3jmw", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "fldB95KYJm9pfLDjH", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "fld9XcQETR2DJWSrf", "type": "boolean", "label": "2 Day Reminder?"}, {"mode": "edit", "name": "fldC1KLk9mtyEwx5w", "type": "select", "label": "Pipeline Stage"}, {"name": "fldbF5nzbTsEp3dwX", "type": "text", "label": "Email"}, {"name": "fldNCshOTbX4KVyNn", "type": "text", "label": "Call Transcription"}, {"name": "fld7LPfzbfUgAZ5ZX", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"mode": "edit", "name": "fldD8QewifDslc3DH", "type": "select", "label": "Follow-Up Step"}, {"name": "fld8hJUTEJL2Y97LF", "type": "boolean", "label": "Exclude from Sequence"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Lead's Name", "type": "text", "label": "Lead's Name"}, {"name": "Company Name", "type": "text", "label": "Company Name"}, {"name": "Type of Reminder", "type": "text", "label": "Type of Reminder"}, {"name": "Booking Date", "time": true, "type": "date", "label": "Booking Date"}, {"name": "15 Min Date&Time", "type": "text", "label": "15 Min Date&Time"}, {"name": "15 Min Reminder", "type": "text", "label": "15 Min Reminder"}, {"name": "15 Min Reminder?", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "1 Day Date&Time", "type": "text", "label": "1 Day Date&Time"}, {"name": "1 Day Reminder", "type": "text", "label": "1 Day Reminder"}, {"name": "1 Day Reminder?", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "2 Day Date&Time", "type": "text", "label": "2 Day Date&Time"}, {"name": "2 Day Reminder", "type": "text", "label": "2 Day Reminder"}, {"name": "2 Day Reminder?", "type": "boolean", "label": "2 Day Reminder?"}, {"name": "Pipeline Stage", "type": "text", "label": "Pipeline Stage"}, {"name": "Date Added", "type": "date", "label": "Date Added"}, {"name": "RecordID", "type": "text", "label": "RecordID"}, {"name": "Email", "type": "text", "label": "Email"}, {"name": "Call Transcription", "type": "text", "label": "Call Transcription", "multiline": true}, {"name": "📧 Email History", "spec": {"label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"name": "Last <PERSON><PERSON>", "type": "array", "label": "Last <PERSON><PERSON>"}, {"name": "Follow-Up Step", "type": "text", "label": "Follow-Up Step"}, {"name": "Exclude from Sequence", "type": "boolean", "label": "Exclude from Sequence"}]}}]}, {"flow": [{"id": 16, "module": "email:ActionSendEmail", "version": 7, "parameters": {"account": 2102505, "saveAfterSent": false}, "filter": {"name": "1 Day Reminder", "conditions": [[{"a": "{{8.`1 <PERSON> Reminder`}}", "b": "1", "o": "text:equal"}]]}, "mapper": {"to": ["<EMAIL>"], "subject": "Some quick thoughts before our chat tomorrow ", "contentType": "html", "attachments": [], "cc": [], "bcc": [], "from": "", "sender": "", "replyTo": "", "inReplyTo": "", "references": [], "priority": "normal", "headers": [], "html": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystem, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n            font-size: 15px;\n            line-height: 1.5;\n            color: #000;\n        }\n    </style>\n</head>\n<body>\n\n<p>Hey {{10.result.first_name}},</p>\n\n<p><em>Just thinking about this before our call tomorrow ({{10.result.call_date}})...</em></p>\n\n<p>Had a conversation recently with a CEO who shared how they'd poured a ton of cash into tools like Copilot, Fathom, Fireflies & still, no real impact six months later.</p>\n\n<p>It’s something we see all the time — shiny tools piling up, but no real infrastructure pulling them together into real revenue gains.</p>\n\n<p>So I recorded a short breakdown of how real operators are building revenue systems that actually work — figured you’d find it useful before we dive deeper.</p>\n\n<p>Here’s the video: <strong><a href=\"https://www.youtube.com/watch?v=M2cCwn5k2Zs&ab_channel=AryanMahajan\" target=\"_blank\">Watch it here</a></strong></p>\n\n<p>If you need the link for our call, you can join directly here: <a href=\"{{8.`Meeting Link`}}\" target=\"_blank\">Join the call</a></p>\n\n<p>See you on the call,</p>\n\n<p>Aryan</p>\n\n</body>\n</html>"}, "metadata": {"designer": {"x": 1200, "y": 300}, "restore": {"parameters": {"account": {"label": "My Google Restricted connection (<EMAIL>)", "data": {"scoped": "true", "connection": "google-restricted"}}, "saveAfterSent": {"label": "No"}}, "expect": {"to": {"mode": "chose", "items": [null]}, "contentType": {"label": "HTML"}, "attachments": {"mode": "chose"}, "cc": {"mode": "chose"}, "bcc": {"mode": "chose"}, "references": {"mode": "chose"}, "priority": {"label": "Normal"}}}, "parameters": [{"name": "account", "type": "account:smtp,google-restricted,microsoft-smtp-imap", "label": "Connection", "required": true}, {"name": "saveAfterSent", "type": "select", "label": "Save message after sending", "required": true, "validate": {"enum": [true, false]}}, {"type": "hidden"}], "expect": [{"name": "to", "type": "array", "label": "To", "required": true, "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "contentType", "type": "select", "label": "Content Type", "required": true, "validate": {"enum": ["html", "text"]}}, {"name": "attachments", "type": "array", "label": "Attachments", "spec": [{"name": "fileName", "label": "File name", "type": "filename", "required": true, "semantic": "file:name"}, {"name": "data", "label": "Data", "type": "buffer", "required": true, "semantic": "file:data"}, {"name": "cid", "label": "Content-ID", "type": "text"}]}, {"name": "cc", "type": "array", "label": "Copy recipient", "spec": {"name": "value", "type": "email", "label": "Email address"}}, {"name": "bcc", "type": "array", "label": "Blind copy recipient", "spec": {"name": "value", "type": "email", "label": "Email address"}}, {"name": "from", "type": "text", "label": "From"}, {"name": "sender", "type": "text", "label": "Sender"}, {"name": "replyTo", "type": "text", "label": "Reply-To"}, {"name": "inReplyTo", "type": "text", "label": "In-Reply-To"}, {"name": "references", "type": "array", "label": "References", "spec": {"type": "text", "label": "Reference", "required": true, "name": "value"}}, {"name": "priority", "type": "select", "label": "Priority", "validate": {"enum": ["high", "normal", "low"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "key", "label": "Key", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text", "required": true}]}, {"name": "html", "type": "text", "label": "Content"}], "advanced": true}}, {"id": 20, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{8.id}}", "base": "appyIUnoXEbjMcDIC", "table": "tblJ0dL96najl4cDJ", "record": {"fldB95KYJm9pfLDjH": true}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1500, "y": 300}, "restore": {"expect": {"base": {"label": "CRM Lead Nurturing"}, "table": {"label": "🖥️ CRM"}, "record": {"nested": {"fld7LPfzbfUgAZ5ZX": {"mode": "chose"}, "fld8hJUTEJL2Y97LF": {"mode": "chose"}, "fld9XcQETR2DJWSrf": {"mode": "edit"}, "fldB95KYJm9pfLDjH": {"mode": "chose"}, "fldC1KLk9mtyEwx5w": {"mode": "edit"}, "fldD8QewifDslc3DH": {"mode": "edit"}, "flddxAMKOSQPlw4vd": {"mode": "edit"}, "fldqFMTc7DjZJ3jmw": {"mode": "edit"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fld9VRttplLll5LgB", "type": "text", "label": "Lead's Name"}, {"name": "fldYAmykfIvhRSZdM", "type": "text", "label": "Company Name"}, {"mode": "edit", "name": "flddxAMKOSQPlw4vd", "type": "select", "label": "Type of Reminder"}, {"name": "fldWf0gqfLA7eWPaY", "time": true, "type": "date", "label": "Booking Date"}, {"name": "fldqFMTc7DjZJ3jmw", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "fldB95KYJm9pfLDjH", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "fld9XcQETR2DJWSrf", "type": "boolean", "label": "2 Day Reminder?"}, {"mode": "edit", "name": "fldC1KLk9mtyEwx5w", "type": "select", "label": "Pipeline Stage"}, {"name": "fldbF5nzbTsEp3dwX", "type": "text", "label": "Email"}, {"name": "fldNCshOTbX4KVyNn", "type": "text", "label": "Call Transcription"}, {"name": "fld7LPfzbfUgAZ5ZX", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"mode": "edit", "name": "fldD8QewifDslc3DH", "type": "select", "label": "Follow-Up Step"}, {"name": "fld8hJUTEJL2Y97LF", "type": "boolean", "label": "Exclude from Sequence"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Lead's Name", "type": "text", "label": "Lead's Name"}, {"name": "Company Name", "type": "text", "label": "Company Name"}, {"name": "Type of Reminder", "type": "text", "label": "Type of Reminder"}, {"name": "Booking Date", "time": true, "type": "date", "label": "Booking Date"}, {"name": "15 Min Date&Time", "type": "text", "label": "15 Min Date&Time"}, {"name": "15 Min Reminder", "type": "text", "label": "15 Min Reminder"}, {"name": "15 Min Reminder?", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "1 Day Date&Time", "type": "text", "label": "1 Day Date&Time"}, {"name": "1 Day Reminder", "type": "text", "label": "1 Day Reminder"}, {"name": "1 Day Reminder?", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "2 Day Date&Time", "type": "text", "label": "2 Day Date&Time"}, {"name": "2 Day Reminder", "type": "text", "label": "2 Day Reminder"}, {"name": "2 Day Reminder?", "type": "boolean", "label": "2 Day Reminder?"}, {"name": "Pipeline Stage", "type": "text", "label": "Pipeline Stage"}, {"name": "Date Added", "type": "date", "label": "Date Added"}, {"name": "RecordID", "type": "text", "label": "RecordID"}, {"name": "Email", "type": "text", "label": "Email"}, {"name": "Call Transcription", "type": "text", "label": "Call Transcription", "multiline": true}, {"name": "📧 Email History", "spec": {"label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"name": "Last <PERSON><PERSON>", "type": "array", "label": "Last <PERSON><PERSON>"}, {"name": "Follow-Up Step", "type": "text", "label": "Follow-Up Step"}, {"name": "Exclude from Sequence", "type": "boolean", "label": "Exclude from Sequence"}]}}]}, {"flow": [{"id": 18, "module": "email:ActionSendEmail", "version": 7, "parameters": {"account": 2102505, "saveAfterSent": false}, "filter": {"name": "2 Day Reminder", "conditions": [[{"a": "{{8.`2 <PERSON> Reminder`}}", "b": "1", "o": "text:equal"}]]}, "mapper": {"to": ["<EMAIL>"], "subject": "Excited for our call on {{10.result.call_date}} — sending this ahead of time", "contentType": "html", "attachments": [], "cc": [], "bcc": [], "from": "", "sender": "", "replyTo": "", "inReplyTo": "", "references": [], "priority": "normal", "headers": [], "html": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystem, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n            font-size: 15px;\n            line-height: 1.5;\n            color: #000;\n        }\n    </style>\n</head>\n<body>\n\n<p>Hey {{10.result.first_name}},</p>\n\n<p><em>Really looking forward to our call on {{10.result.call_date}}.</em></p>\n\n<p>Wanted to send something over ahead of it — a quick walkthrough we put together showing how business owners are navigating this massive shift in automation and AI right now.</p>\n\n<p>It breaks down:</p>\n<ul>\n<li>What's actually working at the ground level</li>\n<li>Real examples of companies adjusting their revenue systems fast</li>\n<li>The key principles that separate those seeing results vs. those getting left behind</li>\n</ul>\n\n<p>Here’s the video: <strong><a href=\"https://screen.studio/share/XOz3Nbji\" target=\"_blank\">Watch the breakdown</a></strong></p>\n\n<p>If you need the link for our call, you can join directly here: <a href=\"{{8.`Meeting Link`}}\" target=\"_blank\">Join the call</a></p>\n\n<p>Catch you soon,</p>\n\n<p>- Aryan</p>\n\n</body>\n</html>"}, "metadata": {"designer": {"x": 1200, "y": 600}, "restore": {"parameters": {"account": {"label": "My Google Restricted connection (<EMAIL>)", "data": {"scoped": "true", "connection": "google-restricted"}}, "saveAfterSent": {"label": "No"}}, "expect": {"to": {"mode": "chose", "items": [null]}, "contentType": {"label": "HTML"}, "attachments": {"mode": "chose"}, "cc": {"mode": "chose"}, "bcc": {"mode": "chose"}, "references": {"mode": "chose"}, "priority": {"label": "Normal"}}}, "parameters": [{"name": "account", "type": "account:smtp,google-restricted,microsoft-smtp-imap", "label": "Connection", "required": true}, {"name": "saveAfterSent", "type": "select", "label": "Save message after sending", "required": true, "validate": {"enum": [true, false]}}, {"type": "hidden"}], "expect": [{"name": "to", "type": "array", "label": "To", "required": true, "spec": {"name": "value", "type": "email", "label": "Email address", "required": true}}, {"name": "subject", "type": "text", "label": "Subject"}, {"name": "contentType", "type": "select", "label": "Content Type", "required": true, "validate": {"enum": ["html", "text"]}}, {"name": "attachments", "type": "array", "label": "Attachments", "spec": [{"name": "fileName", "label": "File name", "type": "filename", "required": true, "semantic": "file:name"}, {"name": "data", "label": "Data", "type": "buffer", "required": true, "semantic": "file:data"}, {"name": "cid", "label": "Content-ID", "type": "text"}]}, {"name": "cc", "type": "array", "label": "Copy recipient", "spec": {"name": "value", "type": "email", "label": "Email address"}}, {"name": "bcc", "type": "array", "label": "Blind copy recipient", "spec": {"name": "value", "type": "email", "label": "Email address"}}, {"name": "from", "type": "text", "label": "From"}, {"name": "sender", "type": "text", "label": "Sender"}, {"name": "replyTo", "type": "text", "label": "Reply-To"}, {"name": "inReplyTo", "type": "text", "label": "In-Reply-To"}, {"name": "references", "type": "array", "label": "References", "spec": {"type": "text", "label": "Reference", "required": true, "name": "value"}}, {"name": "priority", "type": "select", "label": "Priority", "validate": {"enum": ["high", "normal", "low"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "key", "label": "Key", "type": "text", "required": true}, {"name": "value", "label": "Value", "type": "text", "required": true}]}, {"name": "html", "type": "text", "label": "Content"}], "advanced": true}}, {"id": 21, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{8.id}}", "base": "appyIUnoXEbjMcDIC", "table": "tblJ0dL96najl4cDJ", "record": {"fld9XcQETR2DJWSrf": true}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1500, "y": 600}, "restore": {"expect": {"base": {"label": "CRM Lead Nurturing"}, "table": {"label": "🖥️ CRM"}, "record": {"nested": {"fld7LPfzbfUgAZ5ZX": {"mode": "chose"}, "fld8hJUTEJL2Y97LF": {"mode": "chose"}, "fld9XcQETR2DJWSrf": {"mode": "chose"}, "fldB95KYJm9pfLDjH": {"mode": "edit"}, "fldC1KLk9mtyEwx5w": {"mode": "edit"}, "fldD8QewifDslc3DH": {"mode": "edit"}, "flddxAMKOSQPlw4vd": {"mode": "edit"}, "fldqFMTc7DjZJ3jmw": {"mode": "edit"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fld9VRttplLll5LgB", "type": "text", "label": "Lead's Name"}, {"name": "fldYAmykfIvhRSZdM", "type": "text", "label": "Company Name"}, {"mode": "edit", "name": "flddxAMKOSQPlw4vd", "type": "select", "label": "Type of Reminder"}, {"name": "fldWf0gqfLA7eWPaY", "time": true, "type": "date", "label": "Booking Date"}, {"name": "fldqFMTc7DjZJ3jmw", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "fldB95KYJm9pfLDjH", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "fld9XcQETR2DJWSrf", "type": "boolean", "label": "2 Day Reminder?"}, {"mode": "edit", "name": "fldC1KLk9mtyEwx5w", "type": "select", "label": "Pipeline Stage"}, {"name": "fldbF5nzbTsEp3dwX", "type": "text", "label": "Email"}, {"name": "fldNCshOTbX4KVyNn", "type": "text", "label": "Call Transcription"}, {"name": "fld7LPfzbfUgAZ5ZX", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"mode": "edit", "name": "fldD8QewifDslc3DH", "type": "select", "label": "Follow-Up Step"}, {"name": "fld8hJUTEJL2Y97LF", "type": "boolean", "label": "Exclude from Sequence"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Lead's Name", "type": "text", "label": "Lead's Name"}, {"name": "Company Name", "type": "text", "label": "Company Name"}, {"name": "Type of Reminder", "type": "text", "label": "Type of Reminder"}, {"name": "Booking Date", "time": true, "type": "date", "label": "Booking Date"}, {"name": "15 Min Date&Time", "type": "text", "label": "15 Min Date&Time"}, {"name": "15 Min Reminder", "type": "text", "label": "15 Min Reminder"}, {"name": "15 Min Reminder?", "type": "boolean", "label": "15 Min Reminder?"}, {"name": "1 Day Date&Time", "type": "text", "label": "1 Day Date&Time"}, {"name": "1 Day Reminder", "type": "text", "label": "1 Day Reminder"}, {"name": "1 Day Reminder?", "type": "boolean", "label": "1 Day Reminder?"}, {"name": "2 Day Date&Time", "type": "text", "label": "2 Day Date&Time"}, {"name": "2 Day Reminder", "type": "text", "label": "2 Day Reminder"}, {"name": "2 Day Reminder?", "type": "boolean", "label": "2 Day Reminder?"}, {"name": "Pipeline Stage", "type": "text", "label": "Pipeline Stage"}, {"name": "Date Added", "type": "date", "label": "Date Added"}, {"name": "RecordID", "type": "text", "label": "RecordID"}, {"name": "Email", "type": "text", "label": "Email"}, {"name": "Call Transcription", "type": "text", "label": "Call Transcription", "multiline": true}, {"name": "📧 Email History", "spec": {"label": "Record ID"}, "type": "array", "label": "📧 Email History"}, {"name": "Last <PERSON><PERSON>", "type": "array", "label": "Last <PERSON><PERSON>"}, {"name": "Follow-Up Step", "type": "text", "label": "Follow-Up Step"}, {"name": "Exclude from Sequence", "type": "boolean", "label": "Exclude from Sequence"}]}}]}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}