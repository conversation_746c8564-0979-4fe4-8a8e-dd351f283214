{"name": "AI Cold Outreach Scraper", "nodes": [{"parameters": {"httpMethod": "POST", "path": "051fc608-eabc-4e27-ac07-6c64ca086dd8", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [0, 0], "id": "f454cd9f-22dc-4938-b4ba-7b5b8342a3d1", "name": "Webhook", "webhookId": "051fc608-eabc-4e27-ac07-6c64ca086dd8"}, {"parameters": {"url": "=https://api.apify.com/v2/datasets/{{ $json.body.resource.defaultDatasetId }}/items", "sendHeaders": true, "specifyHeaders": "json", "jsonHeaders": "{\n  \"Accept\": \"application/json\",\n  \"Authorization\": \"Bearer \"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "608eb2fe-9297-4cef-aaa1-d98412cd07e2", "name": "HTTP Request"}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "1RzAXT7TxOVmBpTGI8OjOKQDwyZ0iRMh6d_0z2tTqi0Y", "mode": "list", "cachedResultName": "Icebreakers", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RzAXT7TxOVmBpTGI8OjOKQDwyZ0iRMh6d_0z2tTqi0Y/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RzAXT7TxOVmBpTGI8OjOKQDwyZ0iRMh6d_0z2tTqi0Y/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $json.title }}", "price": "={{ $json.price }}", "categoryName": "={{ $json.categoryName }}", "address": "={{ $json.address }}", "countryCode": "={{ $json.countryCode }}", "phone": "={{ $json.phone }}", "latLong": "={{ $json.location.lat }}, {{ $json.location.lng }}", "totalScore": "={{ $json.totalScore }}", "categories": "={{ $json.categories.join(\", \") }}", "url": "={{ $json.url }}", "emails": "={{ $json.emails.join(\", \") }}", "description": "={{ $json.description }}", "website": "={{ $json.website }}"}, "matchingColumns": [], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "price", "displayName": "price", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "categoryName", "displayName": "categoryName", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "countryCode", "displayName": "countryCode", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "latLong", "displayName": "latLong", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "totalScore", "displayName": "totalScore", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "categories", "displayName": "categories", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "emails", "displayName": "emails", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email1", "displayName": "email1", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email2", "displayName": "email2", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email3", "displayName": "email3", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"useAppend": true}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [440, 0], "id": "01652f1d-6247-4b41-bd04-2e566ac5c8d1", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "nmsHzpjs4OT2U0w8", "name": "Google Sheets account"}}}, {"parameters": {"promptType": "define", "text": "=You are a business development representative for Code Creations, an innovative technology and AI company.\n\nYou are targeting restaurants in London and your task is to generate a sequence of 3 emails that will be sent to leads that wee want to work with. Each email will be sent 5 days apart, unless they reply to the previously sent one.\n\nYou must personalise each email to the contextual information about the restaurant provided. You must make a light-hearted joke about how AI could help their specific niche. Then, give a punchy one line statement to entice them to respond to our email for more information about how we can optimise, grow and improve their business with AI.\n\nYou must keep the icebreaker to below 250 characters.\n\nYou must output your results into the following valid JSON format:\n{\n\"email1\": string,\n\"email2\": string,\n\"email3\": string,\n}\n\nHere is the contextual data about the restuarant that we're targeting:\n- Title: {{ $json.title }}\n- Categories: {{ $json.categories }}\n- Address: {{ $json.address }}\n- Description: {{ $json.description }}\n", "hasOutputParser": true}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [740, 0], "id": "5fa0ff05-81e6-4a3a-8d45-92bc77f6d363", "name": "Basic LLM Chain"}, {"parameters": {"model": {"__rl": true, "value": "claude-3-5-haiku-20241022", "mode": "list", "cachedResultName": "<PERSON> 3.5"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [660, 180], "id": "cf3c0e7b-80e0-4baa-bb1e-51ee11f63a93", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "47iJM1sGaBK8FmdC", "name": "Anthropic API Key"}}}, {"parameters": {"options": {"prompt": "Instructions:\n--------------\n{instructions}\n--------------\nCompletion:\n--------------\n{completion}\n--------------\n\nAbove, the Completion did not satisfy the constraints given in the Instructions.\nError:\n--------------\n{error}\n--------------\n\nPlease try again. Please only respond with an answer that satisfies the constraints laid out in the Instructions:"}}, "type": "@n8n/n8n-nodes-langchain.outputParserAutofixing", "typeVersion": 1, "position": [880, 220], "id": "89d5e910-8e51-4a6e-84ff-23f0eb3cc76a", "name": "Auto-fixing Output Parser"}, {"parameters": {"model": {"__rl": true, "value": "claude-3-5-haiku-20241022", "mode": "list", "cachedResultName": "<PERSON> 3.5"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.3, "position": [840, 420], "id": "b9cc1493-52a1-4428-a6ea-adbe13060c90", "name": "Anthropic Chat Model1", "credentials": {"anthropicApi": {"id": "47iJM1sGaBK8FmdC", "name": "Anthropic API Key"}}}, {"parameters": {"jsonSchemaExample": "{\n\t\"email1\": \"string\",\n\t\"email2\": \"string\",\n\t\"email3\": \"string\" \n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1020, 440], "id": "e52ec6fc-1e4e-4d23-a7ab-1ea8498c390d", "name": "Structured Output Parser"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1RzAXT7TxOVmBpTGI8OjOKQDwyZ0iRMh6d_0z2tTqi0Y", "mode": "list", "cachedResultName": "Icebreakers", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RzAXT7TxOVmBpTGI8OjOKQDwyZ0iRMh6d_0z2tTqi0Y/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Contacts", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1RzAXT7TxOVmBpTGI8OjOKQDwyZ0iRMh6d_0z2tTqi0Y/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"title": "={{ $('Google Sheets').item.json.title }}", "email1": "={{ $json.output.email1 }}", "email2": "={{ $json.output.email2 }}", "email3": "={{ $json.output.email3 }}"}, "matchingColumns": ["title"], "schema": [{"id": "title", "displayName": "title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "price", "displayName": "price", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "categoryName", "displayName": "categoryName", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "address", "displayName": "address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "countryCode", "displayName": "countryCode", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "latLong", "displayName": "latLong", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "totalScore", "displayName": "totalScore", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "categories", "displayName": "categories", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "url", "displayName": "url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "emails", "displayName": "emails", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "website", "displayName": "website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "description", "displayName": "description", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email1", "displayName": "email1", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email2", "displayName": "email2", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email3", "displayName": "email3", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1100, 0], "id": "6216a842-de81-42c4-bb12-defe44cb7542", "name": "Google Sheets1", "credentials": {"googleSheetsOAuth2Api": {"id": "nmsHzpjs4OT2U0w8", "name": "Google Sheets account"}}}], "pinData": {"Webhook": [{"json": {"headers": {"host": "halotechlab.app.n8n.cloud", "user-agent": "axios/1.8.4", "content-length": "2292", "accept": "application/json, text/plain, */*", "accept-encoding": "gzip, br", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "***********", "cf-ew-via": "15", "cf-ipcountry": "US", "cf-ray": "95568e26512ae1c6-IAD", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "x-apify-request-origin": "WEBHOOK", "x-apify-webhook": "yo", "x-apify-webhook-dispatch-id": "SBagnG8XfTjQBBOkK", "x-forwarded-for": "***********, *************", "x-forwarded-host": "halotechlab.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-43-869467f569-txt6p", "x-is-trusted": "yes", "x-real-ip": "***********"}, "params": {}, "query": {}, "body": {"userId": "4HNdXv9hqfDz5OL70", "createdAt": "2025-06-25T18:33:47.131Z", "eventType": "ACTOR.RUN.SUCCEEDED", "eventData": {"actorId": "WnMxbsRLNbPeYL6ge", "actorRunId": "MZpV1KiD3787vuGEF"}, "resource": {"id": "MZpV1KiD3787vuGEF", "actId": "WnMxbsRLNbPeYL6ge", "userId": "4HNdXv9hqfDz5OL70", "startedAt": "2025-06-25T18:32:05.595Z", "finishedAt": "2025-06-25T18:33:41.034Z", "status": "SUCCEEDED", "statusMessage": "Scraping finished. You can view all scraped places laid out on a map on: https://api.apify.com/v2/key-value-stores/Czot15cupi2kdieYO/records/results-map. It can take some time to fully load for large datasets.", "isStatusMessageTerminal": true, "meta": {"origin": "WEB", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "stats": {"inputBodyLen": 201, "migrationCount": 0, "rebootCount": 0, "restartCount": 0, "durationMillis": 95322, "resurrectCount": 0, "runTimeSecs": 95.322, "metamorph": 0, "computeUnits": 0.026478333333333333, "memAvgBytes": 153704723.54079714, "memMaxBytes": 190050304, "memCurrentBytes": 0, "cpuAvgUsage": 5.030122390241775, "cpuMaxUsage": 91.6882175226586, "cpuCurrentUsage": 0, "netRxBytes": 5716329, "netTxBytes": 524478}, "options": {"build": "latest", "timeoutSecs": 3600000, "memoryMbytes": 1024, "maxItems": 550, "diskMbytes": 2048}, "buildId": "e1UngkULnf7kqZLvw", "exitCode": 0, "defaultKeyValueStoreId": "Czot15cupi2kdieYO", "defaultDatasetId": "kr4YQ1hMDEyfB5LTF", "defaultRequestQueueId": "L3fJsHx6RF9IOygBA", "pricingInfo": {"pricingModel": "PRICE_PER_DATASET_ITEM", "reasonForChange": "As we have announced, there is a new, improved version of this <PERSON>. If you want to use it the same way as before, please migrate to compass/google-maps-lead-extractor.", "unitName": "Result", "pricePerUnitUsd": 0.009, "createdAt": "2024-10-09T11:07:57.135Z", "startedAt": "2024-10-09T11:07:57.135Z", "apifyMarginPercentage": 0.2, "notifiedAboutChangeAt": "2024-10-09T11:09:19.374Z"}, "platformUsageBillingModel": "DEVELOPER", "generalAccess": "FOLLOW_USER_SETTING", "buildNumber": null, "containerUrl": "https://x7wtdwpko12r.runs.apify.net", "links": {"publicRunUrl": "https://console.apify.com/view/runs/MZpV1KiD3787vuGEF", "consoleRunUrl": "https://console.apify.com/actors/WnMxbsRLNbPeYL6ge/runs/MZpV1KiD3787vuGEF", "apiRunUrl": "https://api.apify.com/v2/actor-runs/MZpV1KiD3787vuGEF"}}}, "webhookUrl": "https://halotechlab.app.n8n.cloud/webhook-test/051fc608-eabc-4e27-ac07-6c64ca086dd8", "executionMode": "test"}}], "HTTP Request": [{"json": {"title": "Zaza", "description": "Vibrant, stylish venue for dishes from all around Italy, plus cocktails & imported wines.", "price": "$$", "categoryName": "Italian restaurant", "address": "The Duck House, 2 High St, Ruislip HA4 7AR, United Kingdom", "neighborhood": null, "street": "The Duck House, 2 High St", "city": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "HA4 7AR", "state": null, "countryCode": "GB", "website": "http://www.zazarestaurant.co.uk/", "phone": "+44 1895 347172", "phoneUnformatted": "+441895347172", "claimThisBusiness": false, "location": {"lat": 51.57703, "lng": -0.427329}, "totalScore": 4.6, "permanentlyClosed": false, "temporarilyClosed": false, "placeId": "ChIJdz8p6l9sdkgR70y64yNF1L4", "categories": ["Italian restaurant"], "fid": "0x48766c5fea293f77:0xbed44523e3ba4cef", "cid": "13750691582715776239", "reviewsCount": 961, "imagesCount": 473, "imageCategories": [], "scrapedAt": "2025-06-25T18:32:12.056Z", "reserveTableUrl": "https://www.google.com/maps/reserve/v/dine/c/HdcL3TSQ7A8?source=pa&opi=79508299&hl=en-US&gei=q0BcaPeJBMfLp84PnrerwQM&sourceurl=https://www.google.com/service/MapsSearchService/Search?hl%3Den%26authuser%3D0%26gl%3Dus%26q%3Drestaurant%26tbm%3Dmap", "googleFoodUrl": null, "hotelAds": [], "openingHours": [{"day": "Monday", "hours": "12 to 10:30 PM"}, {"day": "Tuesday", "hours": "12 to 10:30 PM"}, {"day": "Wednesday", "hours": "12 to 10:30 PM"}, {"day": "Thursday", "hours": "12 to 10:30 PM"}, {"day": "Friday", "hours": "12 to 11 PM"}, {"day": "Saturday", "hours": "12 to 11 PM"}, {"day": "Sunday", "hours": "12 to 10 PM"}], "peopleAlsoSearch": [], "placesTags": [], "reviewsTags": [], "additionalInfo": {"Service options": [{"Takeout": true}, {"Dine-in": true}], "Highlights": [{"Great coffee": true}, {"Great dessert": true}, {"Great wine list": true}], "Popular for": [{"Lunch": true}, {"Dinner": true}, {"Solo dining": true}], "Accessibility": [{"Wheelchair accessible seating": true}], "Offerings": [{"Alcohol": true}, {"Beer": true}, {"Cocktails": true}, {"Coffee": true}, {"Food": true}, {"Hard liquor": true}, {"Healthy options": true}, {"Vegan options": true}, {"Vegetarian options": true}, {"Wine": true}], "Dining options": [{"Brunch": true}, {"Lunch": true}, {"Dinner": true}, {"Dessert": true}, {"Seating": true}, {"Table service": true}], "Amenities": [{"Restroom": true}, {"Wi-Fi": true}, {"Free Wi-Fi": true}], "Atmosphere": [{"Casual": true}, {"Cozy": true}, {"Historic": true}, {"Romantic": true}, {"Trendy": true}, {"Upscale": true}], "Crowd": [{"Groups": true}], "Planning": [{"Lunch reservations recommended": true}, {"Reservations required": true}, {"Dinner reservations recommended": true}, {"Accepts reservations": true}], "Payments": [{"Credit cards": true}, {"Debit cards": true}, {"NFC mobile payments": true}, {"Credit cards": true}], "Children": [{"Good for kids": true}, {"High chairs": true}, {"Kids' menu": true}], "Parking": [{"Paid parking lot": true}, {"Paid street parking": true}, {"Usually plenty of parking": true}]}, "gasPrices": [], "url": "https://www.google.com/maps/search/?api=1&query=Zaza&query_place_id=ChIJdz8p6l9sdkgR70y64yNF1L4", "searchPageUrl": "https://www.google.com/maps/search/restaurant/@51.5745282773077,-0.****************,13z?hl=en", "searchString": "restaurant", "language": "en", "rank": 4, "isAdvertisement": false, "imageUrl": "https://lh3.googleusercontent.com/p/AF1QipNGsgZF3-IUHdI0OSvfowuQcUYWmQCVe769fzP_=w447-h240-k-no", "kgmid": "/g/1tdb8b59", "domain": "zaza.co.uk", "emails": [], "phones": ["020 8866 3883", "0208 420 4224"], "phonesUncertain": ["020 8866 3883", "0208 245 5293", "0208 363 6974", "0208 420 4224", "1442 767 055", "1494 727228", "1582 462555", "1895 347172"], "linkedIns": [], "twitters": [], "instagrams": ["https://www.instagram.com/zazarestaurants"], "facebooks": ["https://www.facebook.com/Zaza.Restaurants"], "youtubes": [], "tiktoks": [], "pinterests": [], "discords": [], "reviews": null, "leadsEnrichment": []}}, {"json": {"title": "Ten Restaurant", "price": null, "categoryName": "Restaurant", "address": "10 Victoria Rd, Ruislip HA4 0AA, United Kingdom", "neighborhood": null, "street": "10 Victoria Rd", "city": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "HA4 0AA", "state": null, "countryCode": "GB", "website": "https://www.tenrestaurant.co.uk/", "phone": "+44 1895 676067", "phoneUnformatted": "+441895676067", "claimThisBusiness": false, "location": {"lat": 51.5734259, "lng": -0.4133856}, "totalScore": 4.7, "permanentlyClosed": false, "temporarilyClosed": false, "placeId": "ChIJ_daihVttdkgRTbUvrILIbBk", "categories": ["Restaurant", "Bar", "Brunch restaurant", "Cocktail bar", "Lunch restaurant", "Mediterranean restaurant", "Modern British restaurant", "Seafood restaurant", "Steak house"], "fid": "0x48766d5b85a2d6fd:0x196cc882ac2fb54d", "cid": "1832059611993060685", "reviewsCount": 442, "imagesCount": 424, "imageCategories": [], "scrapedAt": "2025-06-25T18:32:12.056Z", "reserveTableUrl": "https://www.google.com/maps/reserve/v/dine/c/LMORSKG5yPY?source=pa&opi=79508299&hl=en-US&gei=q0BcaPeJBMfLp84PnrerwQM&sourceurl=https://www.google.com/service/MapsSearchService/Search?hl%3Den%26authuser%3D0%26gl%3Dus%26q%3Drestaurant%26tbm%3Dmap", "googleFoodUrl": null, "hotelAds": [], "openingHours": [{"day": "Monday", "hours": "Closed"}, {"day": "Tuesday", "hours": "5 to 10 PM"}, {"day": "Wednesday", "hours": "5 to 10 PM"}, {"day": "Thursday", "hours": "5 to 10 PM"}, {"day": "Friday", "hours": "5 PM to 12 AM"}, {"day": "Saturday", "hours": "12 PM to 12 AM"}, {"day": "Sunday", "hours": "12 to 8 PM"}], "additionalOpeningHours": {"Happy hours": [{"day": "Monday", "hours": "Closed"}, {"day": "Tuesday", "hours": "6–8 PM"}, {"day": "Wednesday", "hours": "6–8 PM"}, {"day": "Thursday", "hours": "6–8 PM"}, {"day": "Friday", "hours": "5–7 PM"}, {"day": "Saturday", "hours": "Closed"}, {"day": "Sunday", "hours": "6–8 PM"}], "Brunch": [{"day": "Monday", "hours": "Closed"}, {"day": "Tuesday", "hours": "Closed"}, {"day": "Wednesday", "hours": "Closed"}, {"day": "Thursday", "hours": "Closed"}, {"day": "Friday", "hours": "Closed"}, {"day": "Saturday", "hours": "12–3 PM"}, {"day": "Sunday", "hours": "Closed"}]}, "peopleAlsoSearch": [], "placesTags": [], "reviewsTags": [], "additionalInfo": {"Service options": [{"Takeout": true}, {"Dine-in": true}, {"Delivery": false}], "Highlights": [{"Great cocktails": true}, {"Great coffee": true}, {"Great dessert": true}, {"Great wine list": true}, {"Live music": true}], "Popular for": [{"Lunch": true}, {"Dinner": true}, {"Solo dining": true}], "Accessibility": [{"Wheelchair accessible entrance": true}, {"Wheelchair accessible restroom": true}, {"Wheelchair accessible seating": true}], "Offerings": [{"Alcohol": true}, {"Beer": true}, {"Cocktails": true}, {"Coffee": true}, {"Happy hour drinks": true}, {"Hard liquor": true}, {"Late-night food": true}, {"Small plates": true}, {"Vegetarian options": true}, {"Wine": true}], "Dining options": [{"Brunch": true}, {"Lunch": true}, {"Dinner": true}, {"Dessert": true}, {"Seating": true}, {"Table service": true}], "Amenities": [{"Bar onsite": true}, {"Restroom": true}, {"Wi-Fi": true}, {"Free Wi-Fi": true}], "Atmosphere": [{"Cozy": true}, {"Trendy": true}], "Crowd": [{"Family-friendly": true}, {"Groups": true}], "Planning": [{"Brunch reservations recommended": true}, {"Reservations required": true}, {"Dinner reservations recommended": true}, {"Accepts reservations": true}], "Payments": [{"Credit cards": true}, {"Debit cards": true}, {"NFC mobile payments": true}, {"Credit cards": true}], "Children": [{"Good for kids": true}, {"High chairs": true}, {"Kids' menu": true}], "Parking": [{"Free street parking": true}, {"Paid street parking": true}, {"Usually plenty of parking": true}]}, "gasPrices": [], "url": "https://www.google.com/maps/search/?api=1&query=Ten%20Restaurant&query_place_id=ChIJ_daihVttdkgRTbUvrILIbBk", "searchPageUrl": "https://www.google.com/maps/search/restaurant/@51.5745282773077,-0.****************,13z?hl=en", "searchString": "restaurant", "language": "en", "rank": 3, "isAdvertisement": false, "imageUrl": "https://lh3.googleusercontent.com/p/AF1QipMf5ar7iCV5_nFovFHA9P8wXJszWbnj-99uFcWl=w408-h272-k-no", "kgmid": "/g/11ry094sdy", "domain": "tenrestaurant.co.uk", "emails": ["<EMAIL>", "<EMAIL>"], "phones": ["+441895676067", "01895676067"], "phonesUncertain": ["+44 1895 676067", "1630139609700", "1727861080261", "3507163", "939292546", "33045290", "1644408013996", "1727861080266", "+2021-12-07", "1671744195106", "20221222", "777777777777777", "6928134", "1630581894288", "1729546228578", "586035864173979", "10571577330", "1644413189167", "1727861080275", "70468615", "1630582003015", "1630582015965", "322834645669291", "295384615384615", "102468539312604", "1727866526438", "1727866526505", "+2024-10-02", "101516709833538", "1727866462745", "1727866462819", "100841067377477", "248891163425452", "440027501718857", "1727866595809", "1727866597455", "109251099163273", "487983477281261", "216013500843802", "1727867038293", "1727867044517", "1727867038409", "115417274099635", "1727866551651", "1727866551747", "114141016582285", "1727866735440", "1727866735516", "1727867420061", "1727867420166", "1644409633669", "1727861080292", "408901698", "505344021376085", "720045002812675", "6342540988", "6379130", "79101373", "1668879118587", "1668879118663", "639100813", "761487574", "1668881895111", "1668881895171", "1668960826576", "1668960846208", "1895 676067"], "linkedIns": [], "twitters": [], "instagrams": ["https://www.instagram.com/tenrestaurant.ruislip"], "facebooks": ["https://www.facebook.com/TenRestaurant.Ruislip"], "youtubes": [], "tiktoks": ["https://www.tiktok.com/@tenrestaurant.ruislip"], "pinterests": [], "discords": [], "reviews": null, "leadsEnrichment": []}}, {"json": {"title": "<PERSON><PERSON>", "description": "Traditional Italian restaurant over 2 floors with a cheery vibe, smart decor and classic menu.", "price": "£20–30", "categoryName": "Italian restaurant", "address": "119 High St, Uxbridge UB8 1JT, United Kingdom", "neighborhood": null, "street": "119 High St", "city": "Uxbridge", "postalCode": "UB8 1JT", "state": null, "countryCode": "GB", "website": "http://www.nonarosa.com/", "phone": "+44 1895 233570", "phoneUnformatted": "+441895233570", "claimThisBusiness": false, "location": {"lat": 51.5486217, "lng": -0.4812991}, "totalScore": 4.4, "permanentlyClosed": false, "temporarilyClosed": false, "placeId": "ChIJY3CYBntudkgRlPBWvTTl5LI", "categories": ["Italian restaurant", "Bar", "Cocktail bar", "Delivery service", "Takeout Restaurant", "Restaurant", "Wedding venue"], "fid": "0x48766e7b06987063:0xb2e4e534bd56f094", "cid": "12890680048118853780", "reviewsCount": 1300, "imagesCount": 666, "imageCategories": [], "scrapedAt": "2025-06-25T18:32:12.056Z", "googleFoodUrl": null, "hotelAds": [], "openingHours": [{"day": "Monday", "hours": "12 to 10 PM"}, {"day": "Tuesday", "hours": "12 to 10 PM"}, {"day": "Wednesday", "hours": "12 to 10 PM"}, {"day": "Thursday", "hours": "12 to 10 PM"}, {"day": "Friday", "hours": "12 to 11:30 PM"}, {"day": "Saturday", "hours": "12 to 11:30 PM"}, {"day": "Sunday", "hours": "12 to 10 PM"}], "peopleAlsoSearch": [], "placesTags": [], "reviewsTags": [], "additionalInfo": {"Service options": [{"Outdoor seating": true}, {"Curbside pickup": true}, {"No-contact delivery": true}, {"Delivery": true}, {"Takeout": true}, {"Dine-in": true}], "Highlights": [{"Fireplace": true}, {"Great cocktails": true}, {"Great coffee": true}, {"Great dessert": true}, {"Great wine list": true}, {"Rooftop seating": true}], "Popular for": [{"Lunch": true}, {"Dinner": true}, {"Solo dining": true}], "Accessibility": [{"Wheelchair accessible entrance": false}, {"Wheelchair accessible parking lot": false}], "Offerings": [{"Alcohol": true}, {"Beer": true}, {"Cocktails": true}, {"Coffee": true}, {"Food": true}, {"Free water refills": true}, {"Halal food": true}, {"Happy hour drinks": true}, {"Happy hour food": true}, {"Hard liquor": true}, {"Healthy options": true}, {"Late-night food": true}, {"Organic dishes": true}, {"Small plates": true}, {"Vegan options": true}, {"Vegetarian options": true}, {"Wine": true}], "Dining options": [{"Brunch": true}, {"Lunch": true}, {"Dinner": true}, {"Catering": true}, {"Dessert": true}, {"Seating": true}, {"Table service": true}], "Amenities": [{"Bar onsite": true}, {"Restroom": true}, {"Wi-Fi": true}, {"Free Wi-Fi": true}], "Atmosphere": [{"Casual": true}, {"Cozy": true}, {"Romantic": true}, {"Trendy": true}, {"Upscale": true}], "Crowd": [{"Family-friendly": true}, {"Groups": true}, {"LGBTQ+ friendly": true}, {"Transgender safespace": true}], "Planning": [{"Brunch reservations recommended": true}, {"Lunch reservations recommended": true}, {"Dinner reservations recommended": true}, {"Accepts reservations": true}], "Payments": [{"Credit cards": true}, {"Debit cards": true}, {"NFC mobile payments": true}, {"Credit cards": true}], "Children": [{"Good for kids": true}, {"High chairs": true}, {"Kids' menu": true}], "Parking": [{"Paid parking lot": true}, {"Paid street parking": true}]}, "gasPrices": [], "url": "https://www.google.com/maps/search/?api=1&query=Nonna%20Rosa&query_place_id=ChIJY3CYBntudkgRlPBWvTTl5LI", "searchPageUrl": "https://www.google.com/maps/search/restaurant/@51.5745282773077,-0.****************,13z?hl=en", "searchString": "restaurant", "language": "en", "rank": 5, "isAdvertisement": false, "imageUrl": "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nqUPjThuApwRgZEwTXI_DrUqnVMqFV-GArOa_LRg2wwiXcN59MWSWUaaUduObTMVD8qS1z27Stx5ETlATsE95lZWOJknXNC8Wj8r3y2lkc08_qzkUrb_UnIi_JEMaaXd00XzFY=w408-h306-k-no", "kgmid": "/g/1tklq112", "domain": "nonarosa.com", "emails": ["<EMAIL>"], "phones": [], "phonesUncertain": ["1895 233 570"], "linkedIns": [], "twitters": ["https://twitter.com/NonnaRosaItalia"], "instagrams": ["https://www.instagram.com/nonnarosaitalia/"], "facebooks": ["https://www.facebook.com/Nonna"], "youtubes": [], "tiktoks": [], "pinterests": [], "discords": [], "reviews": null, "leadsEnrichment": []}}, {"json": {"title": "Reis Grill & Bar", "price": null, "categoryName": "Restaurant", "address": "85 High St, Ruislip HA4 8JB, United Kingdom", "neighborhood": "<PERSON><PERSON><PERSON><PERSON>", "street": "85 High St", "city": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "HA4 8JB", "state": null, "countryCode": "GB", "website": "http://www.reisgrillandbar.co.uk/", "phone": "+44 1895 904263", "phoneUnformatted": "+441895904263", "claimThisBusiness": false, "location": {"lat": 51.5740484, "lng": -0.4240304}, "totalScore": 4.8, "permanentlyClosed": false, "temporarilyClosed": false, "placeId": "ChIJyfO-W1dtdkgRirFBFqnhWx4", "categories": ["Restaurant", "Cocktail bar", "Mediterranean restaurant", "Turkish restaurant"], "fid": "0x48766d575bbef3c9:0x1e5be1a91641b18a", "cid": "2187590160357634442", "reviewsCount": 898, "imagesCount": 650, "imageCategories": [], "scrapedAt": "2025-06-25T18:32:12.053Z", "googleFoodUrl": null, "hotelAds": [], "openingHours": [{"day": "Monday", "hours": "5 to 11 PM"}, {"day": "Tuesday", "hours": "5 to 11 PM"}, {"day": "Wednesday", "hours": "5 to 11 PM"}, {"day": "Thursday", "hours": "5 to 11 PM"}, {"day": "Friday", "hours": "12 to 11 PM"}, {"day": "Saturday", "hours": "12 to 11 PM"}, {"day": "Sunday", "hours": "12 to 11 PM"}], "peopleAlsoSearch": [], "placesTags": [], "reviewsTags": [], "additionalInfo": {"Service options": [{"Delivery": true}, {"Takeout": true}, {"Dine-in": true}], "Highlights": [{"Great cocktails": true}, {"Great dessert": true}, {"Great wine list": true}], "Popular for": [{"Lunch": true}, {"Dinner": true}, {"Solo dining": true}], "Accessibility": [{"Wheelchair accessible entrance": true}, {"Wheelchair accessible restroom": true}, {"Wheelchair accessible seating": true}], "Offerings": [{"Alcohol": true}, {"Beer": true}, {"Cocktails": true}, {"Coffee": true}, {"Hard liquor": true}, {"Late-night food": true}, {"Small plates": true}, {"Wine": true}], "Dining options": [{"Lunch": true}, {"Dinner": true}, {"Catering": true}, {"Dessert": true}, {"Table service": true}], "Amenities": [{"Bar onsite": true}, {"Restroom": true}], "Atmosphere": [{"Casual": true}, {"Cozy": true}, {"Romantic": true}, {"Trendy": true}, {"Upscale": true}], "Crowd": [{"Family-friendly": true}, {"Groups": true}], "Planning": [{"Dinner reservations recommended": true}, {"Accepts reservations": true}], "Payments": [{"Credit cards": true}, {"Debit cards": true}, {"NFC mobile payments": true}, {"Credit cards": true}], "Children": [{"Kids' menu": true}], "Parking": [{"Free street parking": true}, {"Paid street parking": true}, {"Usually plenty of parking": true}]}, "gasPrices": [], "url": "https://www.google.com/maps/search/?api=1&query=Reis%20Grill%20%26%20Bar&query_place_id=ChIJyfO-W1dtdkgRirFBFqnhWx4", "searchPageUrl": "https://www.google.com/maps/search/restaurant/@51.5745282773077,-0.****************,13z?hl=en", "searchString": "restaurant", "language": "en", "rank": 1, "isAdvertisement": false, "imageUrl": "https://lh3.googleusercontent.com/gps-cs-s/AC9h4nqWBAD8-7amnZaI8bE9h2iQbo-QDcGl5ClXMY7xUE3-G1qi8nHXi8HFSM3jw0y3UZqKeQyuj5mv1RsDrrKz2qCFnLEHAfQOAmpU0Da1vqgdczBDTIzTN9L7_EHuP73wcG5wMMw36A=w533-h240-k-no", "kgmid": "/g/11s7sv6r7v", "domain": "reisgrillandbar.co.uk", "emails": ["<EMAIL>"], "phones": ["01895904263"], "phonesUncertain": ["1895 904263"], "linkedIns": [], "twitters": [], "instagrams": ["https://www.instagram.com/reisgrillandbar/"], "facebooks": ["https://www.facebook.com/reisgrillandbar"], "youtubes": [], "tiktoks": [], "pinterests": [], "discords": [], "reviews": null, "leadsEnrichment": []}}, {"json": {"title": "<PERSON><PERSON>", "price": "$$", "categoryName": "Restaurant", "address": "35A Green Ln, Northwood HA6 2PX, United Kingdom", "neighborhood": null, "street": "35A Green Ln", "city": "Northwood", "postalCode": "HA6 2PX", "state": null, "countryCode": "GB", "website": "https://www.mokadining.co.uk/", "phone": "+44 20 3026 1415", "phoneUnformatted": "+442030261415", "claimThisBusiness": false, "location": {"lat": 51.6120229, "lng": -0.4257299}, "totalScore": 4.8, "permanentlyClosed": false, "temporarilyClosed": false, "placeId": "ChIJ6wSwJQtrdkgRP2Ekow0qjWk", "categories": ["Restaurant"], "fid": "0x48766b0b25b004eb:0x698d2a0da324613f", "cid": "7605781583758647615", "reviewsCount": 3165, "imagesCount": 1116, "imageCategories": [], "scrapedAt": "2025-06-25T18:32:12.055Z", "reserveTableUrl": "https://www.google.com/maps/reserve/v/dine/c/GKIQ5SRcRmU?source=pa&opi=79508299&hl=en-US&gei=q0BcaPeJBMfLp84PnrerwQM&sourceurl=https://www.google.com/service/MapsSearchService/Search?hl%3Den%26authuser%3D0%26gl%3Dus%26q%3Drestaurant%26tbm%3Dmap", "googleFoodUrl": null, "hotelAds": [], "openingHours": [{"day": "Monday", "hours": "5:30 to 10:30 PM"}, {"day": "Tuesday", "hours": "11:30 AM to 2:30 PM, 5:30 to 10:30 PM"}, {"day": "Wednesday", "hours": "11:30 AM to 2:30 PM, 5:30 to 10:30 PM"}, {"day": "Thursday", "hours": "11:30 AM to 2:30 PM, 5:30 to 10:30 PM"}, {"day": "Friday", "hours": "11:30 AM to 2:30 PM, 5:30 PM to 1:30 AM"}, {"day": "Saturday", "hours": "11:30 AM to 2:30 PM, 5:30 PM to 1:30 AM"}, {"day": "Sunday", "hours": "11:30 AM to 10:30 PM"}], "peopleAlsoSearch": [], "placesTags": [], "reviewsTags": [], "additionalInfo": {"Service options": [{"Delivery": true}, {"Takeout": true}, {"Dine-in": true}], "Highlights": [{"Great beer selection": true}, {"Great cocktails": true}, {"Great dessert": true}, {"Great tea selection": true}, {"Great wine list": true}], "Popular for": [{"Lunch": true}, {"Dinner": true}], "Accessibility": [{"Wheelchair accessible entrance": true}, {"Wheelchair accessible parking lot": true}, {"Wheelchair accessible restroom": true}, {"Wheelchair accessible seating": true}], "Offerings": [{"Alcohol": true}, {"Beer": true}, {"Cocktails": true}, {"Coffee": true}, {"Halal food": true}, {"Hard liquor": true}, {"Healthy options": true}, {"Late-night food": true}, {"Small plates": true}, {"Vegan options": true}, {"Vegetarian options": true}, {"Wine": true}], "Dining options": [{"Brunch": true}, {"Lunch": true}, {"Dinner": true}, {"Catering": true}, {"Dessert": true}, {"Seating": true}, {"Table service": true}], "Amenities": [{"Bar onsite": true}, {"Restroom": true}], "Atmosphere": [{"Cozy": true}, {"Romantic": true}, {"Trendy": true}, {"Upscale": true}], "Crowd": [{"Groups": true}], "Planning": [{"Brunch reservations recommended": true}, {"Reservations required": true}, {"Dinner reservations recommended": true}, {"Accepts reservations": true}], "Payments": [{"Credit cards": true}, {"Debit cards": true}, {"NFC mobile payments": true}, {"Credit cards": true}], "Children": [{"High chairs": true}, {"Kids' menu": true}], "Parking": [{"Free parking lot": true}, {"Free street parking": true}, {"Paid parking lot": true}, {"Usually plenty of parking": true}]}, "gasPrices": [], "url": "https://www.google.com/maps/search/?api=1&query=Moka%20Dining&query_place_id=ChIJ6wSwJQtrdkgRP2Ekow0qjWk", "searchPageUrl": "https://www.google.com/maps/search/restaurant/@51.5745282773077,-0.****************,13z?hl=en", "searchString": "restaurant", "language": "en", "rank": 2, "isAdvertisement": false, "imageUrl": "https://lh3.googleusercontent.com/gps-cs-s/AC9h4npwt4hN8p_eO6NdT5mjOJOxi5qg_Io8BMLp4mGz5BAr1-avx6y0Bbft1e9TbOlweUqp47lC9qaJLmbq5OLsCuy8YAnb8Tc7SesDs3f2SiXPLF-XTyhyAdxm2NaUyTA_6INSBLKA=w408-h306-k-no", "kgmid": "/g/11rd7gwz2k", "domain": "mokadining.co.uk", "emails": ["<EMAIL>"], "phones": ["02030261415", "+442030261415"], "phonesUncertain": ["02030261415", "020 3026 1415"], "linkedIns": [], "twitters": [], "instagrams": ["https://www.instagram.com/mokadining", "https://www.instagram.com/mokadiningnorthwood/", "https://www.instagram.com/mokadining/"], "facebooks": ["https://www.facebook.com/mokadiningnorthwood"], "youtubes": [], "tiktoks": ["https://www.tiktok.com/@mokadiningnorthwood"], "pinterests": [], "discords": [], "reviews": null, "leadsEnrichment": []}}]}, "connections": {"Webhook": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Auto-fixing Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Anthropic Chat Model1": {"ai_languageModel": [[{"node": "Auto-fixing Output Parser", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Auto-fixing Output Parser", "type": "ai_outputParser", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "dad64bf2-b36d-4864-8bc7-9cc14bd643fc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a0c1ca2e851d4b6570d763cf034a55daef24ff4c4e596ab36081981851aa2764"}, "id": "YHC1rQ7qdzAeycFX", "tags": []}