{"name": "3. POV Image Agent", "nodes": [{"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "5cfd004a-1108-4b51-a177-ea2204320ca8", "leftValue": "={{ $json.Status }}", "rightValue": "Scenes Generated", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [180, -40], "id": "70d59945-0ac9-4a1c-962e-2152f19587ff", "name": "If"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-120, -40], "id": "af3be467-2e82-459f-8144-74ae0e2c01ac", "name": "When clicking ‘Test workflow’"}, {"parameters": {"operation": "search", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "filterByFormula": "Status = \"Scenes Generated\"", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [40, -40], "id": "e803071d-780a-4cc7-823d-98b4381effba", "name": "Airtable", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json.id }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [380, -220], "id": "30404b05-6018-4c5d-bd15-43a6e59d7d5b", "name": "Get Record", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tbl47t5XjP64vCYHx", "mode": "list", "cachedResultName": "POV Scenes", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tbl47t5XjP64vCYHx"}, "id": "={{ $json['[\\'POV Scenes\\']'] }}", "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [920, 0], "id": "ca16db13-d389-4648-8da3-b0d48a232e85", "name": "Airtable1", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"fieldToSplitOut": "['POV Scenes']", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [560, -220], "id": "d0ef813d-d321-4c0d-b427-cf3de131235e", "name": "Split Out"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [780, -220], "id": "00744189-e7b8-4007-96c4-34a07979a9fe", "name": "Loop Over Items"}, {"parameters": {"promptType": "define", "text": "=Here is the POV scene:\n\nScene Description: {{ $json.Description }}\nTheme: {{ $('Get Record').item.json.Theme }}\nTone/Mood: {{ $('Get Record').item.json['Tone/Mood'] }}\n\nPlease generate an image prompt to fully depict this specific scene in a POV manner. ", "hasOutputParser": true, "messages": {"messageValues": [{"message": "=# Role\n\nYou are an Image Prompt Creation Agent specialized in generating first-person (POV) image prompts for an AI image generator.\n\n# Task\n\nUsing the provided input, craft a **concise prompt (under 50 words)** that instructs the AI to produce an image with a clear first-person perspective. Your prompt must:\n\n- **Emphasize First-Person POV:** Clearly state that the scene is viewed from a first-person perspective (e.g., “First-person view of…” or “POV view of…”).  \n- **Indicate Visible Body Parts:** When relevant, specify that hands, arms, or feet should be visible to enhance the POV effect.  \n- **Include Scene Details:** Mention key elements such as lighting, atmosphere, environment, and objects.  \n- **Reflect Theme & Tone:** Integrate the provided theme and tone/mood to ensure the image evokes the desired emotions.  \n- **Maintain Visual Style:** Specify a consistent style (e.g., photorealistic, cinematic, digital art) that matches the theme.\n\n# Input\n\nYou will receive:\n\n- **Scene Description:** A POV caption describing the scene.  \n- **Theme:** The underlying theme.  \n- **Tone/Mood:** The intended emotional tone.\n\n# Output\n\nReturn your generated prompt in the following JSON format:\n```json\n{\n  \"prompt\": \"<insert detailed image prompt here>\"\n}\n\n\n#Example\n##Example 1\nInput: \nScene Description: \"POV: Entering a dimly lit, dust-filled room with ancient archives.\"\nTheme: \"Mystery & Legacy\"\nTone/Mood: \"Haunting, Thought-Provoking\"\n\nOutput:\n{\n  \"prompt\": \"First-person view of entering a dim, dust-filled archive room. A hand reaches for ancient scrolls under soft lantern light, evoking mystery and legacy.\"\n}\n\n##Example 2\nInput:\nScene Description: \"POV: Standing on a crumbling castle wall at dawn.\"\nTheme: \"Hope & Renewal\"\nTone/Mood: \"Epic, Uplifting\"\n\nOutput:\n{\n  \"prompt\": \"First-person view from a crumbling castle wall at dawn. A hand grips cold stone as sunrise bathes the scene in epic, uplifting light.\"\n}\n\n##Example 3:\nInput:\nScene Description: \"POV: Walking through a neon-lit alley in a futuristic metropolis.\"\nTheme: \"Rebellion & Futurism\"\nTone/Mood: \"Edgy, Intense\"\n\nOutput:\n{\n  \"prompt\": \"First-person view of a neon-lit alley in a futuristic city. A visible hand holds a high-tech gadget, capturing an edgy, intense vibe of rebellion.\"\n}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [1120, 0], "id": "26158c86-1a7f-4648-b5c0-04cf30e753c5", "name": "Basic LLM Chain"}, {"parameters": {"model": "=qwen/qwen-2.5-72b-instruct", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1120, 240], "id": "08f2275d-8847-4396-936b-bf97dceb69fb", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "y1vjMptfuqUZLu87", "name": "Openrouter Key 1"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"prompt\": \"<insert detailed image prompt here>\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1300, 240], "id": "27b2858b-afaf-4f77-be12-d27c8dc4d88c", "name": "Structured Output Parser"}, {"parameters": {"method": "POST", "url": "https://api.replicate.com/v1/models/black-forest-labs/flux-1.1-pro/predictions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Prefer", "value": "wait"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"input\": {\n    \"prompt\": \"{{ $json.output.prompt }}\",\n    \"prompt_upsampling\": true,\n    \"aspect_ratio\": \"9:16\"\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1480, 0], "id": "ac44e49c-445c-40c9-804c-cce059a0fb1d", "name": "HTTP Request", "credentials": {"httpHeaderAuth": {"id": "xlhyRcOYSjjkmrtL", "name": "Replicate"}}}, {"parameters": {"url": "={{ $json.output }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1660, 0], "id": "a42d19bf-f764-4733-80cc-d979076e21f6", "name": "HTTP Request1"}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsT61yq58HsS2ZM", "mode": "list", "cachedResultName": "POV Images", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsT61yq58HsS2ZM"}, "columns": {"mappingMode": "defineBelow", "value": {"Image Prompt": "={{ $('HTTP Request1').item.json.input.prompt }}", "Image URL": "={{ $json.webContentLink }}", "POV Scenes": "={{ [$('Loop Over Items').item.json['[\\'POV Scenes\\']']] }}", "POV Main": "={{ [$('Airtable').item.json.id ]}}"}, "matchingColumns": [], "schema": [{"id": "Scene Number", "displayName": "Scene Number", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "number", "readOnly": false, "removed": true}, {"id": "Image Prompt", "displayName": "Image Prompt", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "Image URL", "displayName": "Image URL", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": false}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}, {"id": "POV Main", "displayName": "POV Main", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1900, 240], "id": "a8c4bd1f-d582-4508-9f1a-7ec4096e24d3", "name": "Upload Images", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"operation": "update", "base": {"__rl": true, "value": "appJ3fDAoRBETWkA2", "mode": "list", "cachedResultName": "POV Content ", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2"}, "table": {"__rl": true, "value": "tblsK6gpL3sX3Xk3I", "mode": "list", "cachedResultName": "POV Main", "cachedResultUrl": "https://airtable.com/appJ3fDAoRBETWkA2/tblsK6gpL3sX3Xk3I"}, "columns": {"mappingMode": "defineBelow", "value": {"Status": "Image Generated", "id": "={{ $('Airtable').item.json.id }}"}, "matchingColumns": ["id"], "schema": [{"id": "id", "displayName": "id", "required": false, "defaultMatch": true, "display": true, "type": "string", "readOnly": true}, {"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Concept (Detailed <PERSON>rio)", "displayName": "Concept (Detailed <PERSON>rio)", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Theme", "displayName": "Theme", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "<PERSON><PERSON>/<PERSON>od", "displayName": "<PERSON><PERSON>/<PERSON>od", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "POV Scenes", "displayName": "POV Scenes", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Images", "displayName": "POV Images", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV VIdeos", "displayName": "POV VIdeos", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Music", "displayName": "POV Music", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV SFX", "displayName": "POV SFX", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "POV Voiceovers", "displayName": "POV Voiceovers", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "array", "readOnly": false, "removed": true}, {"id": "Final Render Video", "displayName": "Final Render Video", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": false, "removed": true}, {"id": "Status", "displayName": "Status", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Pending", "value": "Pending"}, {"name": "Scenes Generated", "value": "Scenes Generated"}, {"name": "Image Generated", "value": "Image Generated"}, {"name": "Videos Generated ", "value": "Videos Generated "}, {"name": "Music Generated", "value": "Music Generated"}, {"name": "SFX Generated", "value": "SFX Generated"}, {"name": "Voicevers Generated", "value": "Voicevers Generated"}, {"name": "Materials Uploaded", "value": "Materials Uploaded"}, {"name": "Ready To Upload", "value": "Ready To Upload"}, {"name": "Completed", "value": "Completed"}, {"name": "", "value": ""}], "readOnly": false, "removed": false}, {"id": "Instagram", "displayName": "Instagram", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Facebook", "displayName": "Facebook", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "Youtube", "displayName": "Youtube", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "options", "options": [{"name": "Uploaded", "value": "Uploaded"}], "readOnly": false, "removed": true}, {"id": "created time", "displayName": "created time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "last modified time", "displayName": "last modified time", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}, {"id": "record id", "displayName": "record id", "required": false, "defaultMatch": false, "canBeUsedToMatch": true, "display": true, "type": "string", "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.airtable", "typeVersion": 2.1, "position": [1000, -220], "id": "09315467-168b-4c8b-a45b-2fc85e680be0", "name": "Airtable2", "credentials": {"airtableTokenApi": {"id": "HlCvwpeDhqxCdX9c", "name": "Airtable Access 1"}}}, {"parameters": {"content": "## POV Image Agent\n", "height": 840, "width": 2340, "color": 7}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-160, -340], "id": "1cbd8a81-e437-4c1d-9c25-e865108638f2", "name": "<PERSON><PERSON>"}, {"parameters": {"name": "={{ $('Airtable').item.json.Name }}_{{ $('Airtable1').item.json['Scene Number'] }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1dKT0N587hztFdE_KhaCQZnZphD1i0ljF", "mode": "list", "cachedResultName": "1. POV Images", "cachedResultUrl": "https://drive.google.com/drive/folders/1dKT0N587hztFdE_KhaCQZnZphD1i0ljF"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1880, 0], "id": "94cc6024-451f-4c2e-9457-13c06ab3750c", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "9WRrz2c8ZemVzlPL", "name": "Google Drive account 2"}}}], "pinData": {}, "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "Airtable", "type": "main", "index": 0}]]}, "Airtable": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Get Record", "type": "main", "index": 0}]]}, "Get Record": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Airtable2", "type": "main", "index": 0}], [{"node": "Airtable1", "type": "main", "index": 0}]]}, "Airtable1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Basic LLM Chain", "type": "ai_outputParser", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Upload Images": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Upload Images", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "d9ca2b08-61cb-4ea9-9d12-b237d676a0af", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d8c459f4ebcadd20c3258ade09582fe0f02de933363f6184259e0c9a36784090"}, "id": "CKyJySeq6M1q3ba9", "tags": [{"createdAt": "2025-03-24T09:10:16.506Z", "updatedAt": "2025-03-24T09:10:16.506Z", "id": "RqZ45jZ8VcYyMMZW", "name": "W4: POV Content Machine"}]}