{"name": "Create a QuickBooks invoice on a new Onfleet Task creation", "nodes": [{"name": "Onfleet Trigger", "type": "n8n-nodes-base.onfleetTrigger", "position": [460, 300], "webhookId": "6d6a2bee-f83e-4ebd-a1d5-8708c34393dc", "parameters": {"triggerOn": "taskCreated", "additionalFields": {}}, "typeVersion": 1}, {"name": "QuickBooks Online", "type": "n8n-nodes-base.quickbooks", "position": [680, 300], "parameters": {"Line": [], "resource": "invoice", "operation": "create", "additionalFields": {"Balance": 0, "TxnDate": "", "ShipAddr": "", "BillEmail": ""}}, "typeVersion": 1}], "active": false, "settings": {}, "connections": {"Onfleet Trigger": {"main": [[{"node": "QuickBooks Online", "type": "main", "index": 0}]]}}}