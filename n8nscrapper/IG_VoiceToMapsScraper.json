{"name": "IG_VoiceToMapsScraper", "nodes": [{"parameters": {"updates": ["*"], "additionalFields": {}}, "id": "1927db1a-ec14-4c08-b378-fe15d23babd2", "name": "Listen for incoming events", "type": "n8n-nodes-base.telegramTrigger", "position": [580, -40], "webhookId": "e010f207-11f9-4cdd-9155-7ec300f39372", "typeVersion": 1, "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.message.text }}", "rightValue": "/", "id": "d68f5557-eaf3-430c-b3e0-4c0bbb147c45"}]}, "renameOutput": true, "outputKey": "Text"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "dd41bbf0-bee0-450b-9160-b769821a4abc", "operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.message.voice}}", "rightValue": ""}], "combinator": "and"}, "renameOutput": true, "outputKey": "Voice"}]}, "options": {"fallbackOutput": "extra"}}, "id": "2c70ad3e-c10c-4ee0-ac04-8f959a8fd561", "name": "Determine content type", "type": "n8n-nodes-base.switch", "position": [860, -40], "typeVersion": 3.2}, {"parameters": {"operation": "sendChatAction", "chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}"}, "id": "de2550f9-f018-4535-9236-d1bb3a41a6bb", "name": "Send Typing action", "type": "n8n-nodes-base.telegram", "position": [820, -260], "typeVersion": 1.2, "webhookId": "3e16821f-e31c-4ac3-b00a-3269d7b3a4d9", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "file", "fileId": "={{$json.message.voice.file_id}}"}, "id": "ffe0636c-63e2-4ef0-8109-5afcba88d5ab", "name": "Download voice file", "type": "n8n-nodes-base.telegram", "position": [1060, 40], "typeVersion": 1.2, "webhookId": "c3ab6823-3c09-4918-91d3-714528a68c1b", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"resource": "audio", "operation": "transcribe", "options": {"temperature": 0.7}}, "id": "d28d2a64-5e2b-4318-817a-fa0ea4048f75", "name": "Convert audio to text", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1240, 40], "typeVersion": 1.5, "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "bccbce0a-7786-49c9-979a-7a285cb69f78", "name": "CombinedMessage", "type": "string", "value": "={{ $json.message && $json.message.text ? $json.message.text : ($json.text ? $json.text : '') }}"}, {"id": "5b1fc9f5-1408-4099-88cc-a23725c9eddb", "name": "Message Type ", "type": "string", "value": "={{ $json?.message?.text && !$json?.text ? \"text query\" : (!$json?.message?.text && $json?.text ? \"voice message\" : \"unknown type message\") }}"}, {"id": "1e9a17fa-ec5d-49dc-9ff6-1f28b57fb02e", "name": "Source Type", "type": "string", "value": "={{ $('Listen for incoming events').item.json.message.forward_origin ? \" forwarded\" : \"\" }}"}]}, "options": {}}, "id": "1a0203d2-a593-46d1-baf3-722f2699fcdb", "name": "Combine content and set properties", "type": "n8n-nodes-base.set", "position": [1440, -100], "typeVersion": 3.4}, {"parameters": {"promptType": "define", "text": "=Task: \n {{ $('Convert audio to text').item.json.text }}\n\nExtract a short few word long query to send to the google places api. Only respond with the query.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1680, -100], "id": "8ccd91e2-23e4-4dc9-a53c-c53b3bbc5fcd", "name": "AI Agent"}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "url": "https://maps.googleapis.com/maps/api/place/textsearch/json", "options": {}, "queryParametersUi": {"parameter": [{"name": "query", "value": "={{ $json.output }}"}]}}, "id": "c31c9800-2dde-48d3-b425-a9d27ff0bc27", "name": "Search Google Maps", "type": "n8n-nodes-base.httpRequest", "position": [2080, -100], "typeVersion": 2, "credentials": {"googleOAuth2Api": {"id": "cuVkWQ3y4X2jlQRL", "name": "Google account"}, "httpQueryAuth": {"id": "wD8oIAenE0XCztmp", "name": "Google Maps Auth"}}}, {"parameters": {"functionCode": "const places = $json.results || [];\nreturn places.map(place => ({\n  json: {\n    Name: place.name,\n    Address: place.formatted_address,\n    Rating: place.rating || '',\n    PlaceId: place.place_id\n  }\n}));"}, "id": "8f17a287-92c3-48cb-9eda-f19b7da22741", "name": "Extract Places", "type": "n8n-nodes-base.function", "position": [2280, -100], "typeVersion": 1}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpQueryAuth", "url": "https://maps.googleapis.com/maps/api/place/details/json", "options": {}, "queryParametersUi": {"parameter": [{"name": "place_id", "value": "={{ $json.PlaceId }}"}, {"name": "fields", "value": "website,international_phone_number"}]}}, "id": "7c91ea6c-2f83-4d53-8a20-da95366d9451", "name": "Get Place Details", "type": "n8n-nodes-base.httpRequest", "position": [2440, 0], "typeVersion": 2, "credentials": {"httpQueryAuth": {"id": "wD8oIAenE0XCztmp", "name": "Google Maps Auth"}}}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "a669c0f4-5d54-4410-94d3-40c62a347714", "name": "Merge Data", "type": "n8n-nodes-base.merge", "position": [2620, -80], "typeVersion": 2}, {"parameters": {"chatId": "={{ $('Listen for incoming events').first().json.message.from.id }}", "text": "=Sorry, {{ $('Listen for incoming events').first().json.message.from.first_name }}! This command is not supported yet. Please send text or voice messages.", "additionalFields": {"appendAttribution": false, "parse_mode": "<PERSON><PERSON>"}}, "id": "9b385c8e-6565-4d50-9260-5af009d2a6f0", "name": "Send error message", "type": "n8n-nodes-base.telegram", "position": [1060, -260], "typeVersion": 1.2, "webhookId": "73cd5a0e-649a-498e-b45f-a69ecc7e83e9", "credentials": {"telegramApi": {"id": "OAwHKJMP7VU2KFgm", "name": "Telegram account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1680, 80], "id": "51e4e9e3-a6ef-42a5-8f00-1be1bb5cfc3b", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "rnt98gE7HvqfgBVY", "name": "OpenAi account"}}}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "value": "19f1fMQjGrL7V0TWNKDXQ2tZxpFMcdj-kOza55Y5Ik5k", "mode": "list", "cachedResultName": "Maps Leads", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19f1fMQjGrL7V0TWNKDXQ2tZxpFMcdj-kOza55Y5Ik5k/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Tabellenblatt1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/19f1fMQjGrL7V0TWNKDXQ2tZxpFMcdj-kOza55Y5Ik5k/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Name": "={{ $json.Name }}", "Adress": "={{ $json.Address }}", "Rating": "={{ $json.Rating }}", "PlaceId": "={{ $json.PlaceId }}", "Website": "={{ $json.result.website }}", "Phone": "={{ $json.result.international_phone_number.replace('+', '(+)')}}"}, "matchingColumns": [], "schema": [{"id": "Name", "displayName": "Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON>ress", "displayName": "<PERSON>ress", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Rating", "displayName": "Rating", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "PlaceId", "displayName": "PlaceId", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Website", "displayName": "Website", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Phone", "displayName": "Phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2860, -80], "id": "27aaf905-f365-4bd5-bf8a-da83e91b3345", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "jl7xtIhIuErFXCNL", "name": "Google Sheets account"}}}, {"parameters": {"content": "Do you want more like this? Do you need help with this?\n\n🚀 Join the Pro n8n AI Automation Community\n\n➡️ Claim your spot now → [CLICK HERE](https://www.skool.com/pro-ai-automation-dennis-8838)\n\n1 new premium template every week\nVideo walkthroughs and setups\nRequest threads + feedback\nMonthly challenges & exclusive automations\nAccess to the automation library\nStep by step create your custom workflows\n200$ n8n hosting credit for free\n\nUnlock weekly premium templates, step-by-step video walkthroughs, expert feedback threads, exclusive automations, and even $200 in free n8n hosting credit – all in one place.\n\n🟢 Early-bird price: $29 / month (regular $99)\n\n➡️ Claim your spot now → [CLICK HERE](https://www.skool.com/pro-ai-automation-dennis-8838)", "height": 440, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2760, -640], "id": "c064c432-c38b-408b-89d7-5348c59e5e0b", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Listen for incoming events": {"main": [[{"node": "Determine content type", "type": "main", "index": 0}, {"node": "Send Typing action", "type": "main", "index": 0}]]}, "Determine content type": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}], [{"node": "Download voice file", "type": "main", "index": 0}], [{"node": "Send error message", "type": "main", "index": 0}]]}, "Download voice file": {"main": [[{"node": "Convert audio to text", "type": "main", "index": 0}]]}, "Convert audio to text": {"main": [[{"node": "Combine content and set properties", "type": "main", "index": 0}]]}, "Combine content and set properties": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Search Google Maps", "type": "main", "index": 0}]]}, "Search Google Maps": {"main": [[{"node": "Extract Places", "type": "main", "index": 0}]]}, "Extract Places": {"main": [[{"node": "Get Place Details", "type": "main", "index": 0}, {"node": "Merge Data", "type": "main", "index": 0}]]}, "Get Place Details": {"main": [[{"node": "Merge Data", "type": "main", "index": 1}]]}, "Merge Data": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "183c35ba-8233-41c6-9cd2-e8d90da04e5b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "32e39908afbcb49d79cc3b05576c030ecc2871395b7aec4e0fdc88778498f80e"}, "id": "IIjKE8jKiKETWWlG", "tags": []}