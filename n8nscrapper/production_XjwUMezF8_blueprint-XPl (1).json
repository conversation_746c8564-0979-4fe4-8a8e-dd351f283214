{"name": "Autopost to YT Shorts from Google Drive using make and ChatGPT", "flow": [{"id": 8, "module": "google-drive:searchForFilesFolders", "version": 4, "parameters": {"__IMTCONN__": 3099236}, "mapper": {"limit": "1", "select": "list", "folderId": "/1pOh5hHKXb2v0vQMd4HkP0bF96KK0u6db", "retrieve": "file", "destination": "drive"}, "metadata": {"designer": {"x": 0, "y": 150, "name": "Search Google Drive"}, "restore": {"expect": {"select": {"label": "Select from the list"}, "folderId": {"mode": "chose", "path": ["tiktok uploads"]}, "retrieve": {"label": "Files"}, "searchType": {"label": "Empty"}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Demo connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "select", "type": "select", "label": "Select the Method", "required": true, "validate": {"enum": ["map", "list"]}}, {"name": "retrieve", "type": "select", "label": "Retrieve", "required": true, "validate": {"enum": ["file", "folder", "file_folder"]}}, {"name": "searchType", "type": "select", "label": "Search", "validate": {"enum": ["title", "fulltext", "custom"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "Choose a Folder"}]}}, {"id": 7, "module": "google-drive:getAFile", "version": 4, "parameters": {"__IMTCONN__": 3099236}, "mapper": {"file": "{{8.id}}", "select": "map", "formatDrawings": "image/jpeg", "formatDocuments": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "formatSpreadsheets": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "formatPresentations": "application/vnd.openxmlformats-officedocument.presentationml.presentation"}, "metadata": {"designer": {"x": 300, "y": 150, "name": "Download File"}, "restore": {"expect": {"select": {"label": "Enter manually"}, "formatDrawings": {"label": "JPEG"}, "formatDocuments": {"label": "MS Word Document"}, "formatSpreadsheets": {"label": "MS Excel"}, "formatPresentations": {"label": "MS PowerPoint"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Demo connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "select", "type": "select", "label": "Enter a File ID", "required": true, "validate": {"enum": ["map", "value"]}}, {"name": "formatDocuments", "type": "select", "label": "Convert Google Documents Files to Format", "required": true, "validate": {"enum": ["application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/pdf", "application/vnd.oasis.opendocument.text", "text/html", "text/plain", "application/rtf"]}}, {"name": "formatSpreadsheets", "type": "select", "label": "Convert Google Spreadsheets Files to Format", "required": true, "validate": {"enum": ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/x-vnd.oasis.opendocument.spreadsheet", "application/pdf"]}}, {"name": "formatPresentations", "type": "select", "label": "Convert Google Slides Files to Format", "required": true, "validate": {"enum": ["application/vnd.openxmlformats-officedocument.presentationml.presentation", "application/pdf"]}}, {"name": "formatDrawings", "type": "select", "label": "Convert Google Drawings Files to Format", "required": true, "validate": {"enum": ["image/jpeg", "image/png", "image/svg+xml", "application/pdf"]}}, {"name": "file", "type": "text", "label": "File ID", "required": true}]}}, {"id": 10, "module": "openai-gpt-3:CreateTranscription", "version": 1, "parameters": {"__IMTCONN__": 3091321}, "mapper": {"model": "whisper-1", "fileData": "{{7.data}}", "fileName": "{{7.name}}", "temperature": "1", "response_format": "text"}, "metadata": {"designer": {"x": 600, "y": 150, "name": "Transcribe the Video"}, "restore": {"expect": {"model": {"mode": "chose", "label": "Whisper-1"}, "response_format": {"mode": "chose", "label": "Text"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "Testing for Make.com demo"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "fileName", "type": "filename", "label": "File Name", "required": true}, {"name": "fileData", "type": "buffer", "label": "File Data", "required": true}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "prompt", "type": "text", "label": "Prompt"}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["json", "text", "srt", "verbose_json", "vtt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 1, "min": 0}}, {"name": "language", "type": "text", "label": "Language"}], "interface": [{"name": "text", "type": "text", "label": "Text"}]}}, {"id": 11, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 3091321}, "mapper": {"model": "chatgpt-4o-latest", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are an expert in creating catchy youtube descriptions. Create a description for a youtube short that has the transcription shown below. You should include up to 10 hashtags after the description. Stay within 2500 characters used. \n\nTranscription: {{10.text}}"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 900, "y": 150, "name": "Create a description"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "chatgpt-4o-latest (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "Testing for Make.com demo"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publically accessible URL.\nYou can test if your image is publically accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}], "advanced": true}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 12, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 3091321}, "mapper": {"model": "chatgpt-4o-latest", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "You are an expert in creating catchy youtube titles. Create a title for a youtube short that has the transcription shown below. Must not exceed 100 characters.\n\nTranscription: {{10.text}}"}], "max_tokens": "2048", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 1200, "y": 150, "name": "Create a title"}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "chatgpt-4o-latest (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "System"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "Testing for Make.com demo"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publically accessible URL.\nYou can test if your image is publically accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}], "advanced": true}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}]}}, {"id": 9, "module": "youtube:uploadVideo", "version": 4, "parameters": {"__IMTCONN__": 3646232}, "mapper": {"data": "{{7.data}}", "title": "{{12.result}}", "fileName": "{{7.name}}", "categoryId": "26", "description": "{{11.result}}", "privacyStatus": "public", "selfDeclaredMadeForKids": false}, "metadata": {"designer": {"x": 1500, "y": 150}, "restore": {"expect": {"tags": {"mode": "chose"}, "license": {"mode": "chose", "label": "Empty"}, "categoryId": {"mode": "chose", "label": "Howto & Style"}, "embeddable": {"mode": "chose"}, "privacyStatus": {"mode": "chose", "label": "Public"}, "notifySubscribers": {"mode": "chose"}, "selfDeclaredMadeForKids": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "youtube"}, "label": "My YouTube connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:youtube", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "title", "type": "text", "label": "Title", "required": true, "validate": {"max": 100}}, {"name": "fileName", "type": "filename", "label": "File Name", "required": true}, {"name": "data", "type": "buffer", "label": "Data", "required": true}, {"name": "categoryId", "type": "select", "label": "Video Category", "required": true}, {"name": "privacyStatus", "type": "select", "label": "Privacy Status", "required": true, "validate": {"enum": ["private", "unlisted", "public"]}}, {"name": "description", "type": "text", "label": "Description", "validate": {"max": 5000}}, {"name": "selfDeclaredMadeForKids", "type": "boolean", "label": "The video is made for kids", "required": true}, {"name": "tags", "spec": {"name": "value", "type": "text", "label": "Tag", "required": true, "validate": {"max": 500}}, "type": "array", "label": "Tags"}, {"name": "recordingDate", "type": "date", "label": "Recording Date"}, {"name": "license", "type": "select", "label": "License", "validate": {"enum": ["youtube", "creativeCommon"]}}, {"name": "embeddable", "type": "boolean", "label": "Allow Embedding"}, {"name": "notifySubscribers", "type": "boolean", "label": "Notify Subscribers"}, {"type": "hidden"}, {"name": "title", "type": "text", "label": "Title", "required": true, "validate": {"max": 100}}, {"name": "fileName", "type": "filename", "label": "File Name", "required": true}, {"name": "data", "type": "buffer", "label": "Data", "required": true}, {"name": "categoryId", "type": "select", "label": "Video Category", "required": true}, {"name": "privacyStatus", "type": "select", "label": "Privacy Status", "required": true, "validate": {"enum": ["private", "unlisted", "public"]}}, {"name": "description", "type": "text", "label": "Description", "validate": {"max": 5000}}, {"name": "selfDeclaredMadeForKids", "type": "boolean", "label": "The video is made for kids", "required": true}, {"name": "tags", "spec": {"name": "value", "type": "text", "label": "Tag", "required": true, "validate": {"max": 500}}, "type": "array", "label": "Tags"}, {"name": "recordingDate", "type": "date", "label": "Recording Date"}, {"name": "license", "type": "select", "label": "License", "validate": {"enum": ["youtube", "creativeCommon"]}}, {"name": "embeddable", "type": "boolean", "label": "Allow Embedding"}, {"name": "notifySubscribers", "type": "boolean", "label": "Notify Subscribers"}]}}, {"id": 15, "module": "google-drive:searchForFilesFolders", "version": 4, "parameters": {"__IMTCONN__": 3099236}, "mapper": {"limit": "1", "query": "archive", "select": "list", "folderId": "/1pOh5hHKXb2v0vQMd4HkP0bF96KK0u6db", "operator": "=", "retrieve": "folder", "searchType": "title", "destination": "drive"}, "metadata": {"designer": {"x": 1800, "y": 150, "name": "Is there already an archive"}, "restore": {"expect": {"select": {"label": "Select from the list"}, "folderId": {"mode": "chose", "path": ["tiktok uploads"]}, "operator": {"label": "Search for exact term"}, "retrieve": {"label": "Folders"}, "searchType": {"label": "Search within file/folder names"}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Demo connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "select", "type": "select", "label": "Select the Method", "required": true, "validate": {"enum": ["map", "list"]}}, {"name": "retrieve", "type": "select", "label": "Retrieve", "required": true, "validate": {"enum": ["file", "folder", "file_folder"]}}, {"name": "searchType", "type": "select", "label": "Search", "validate": {"enum": ["title", "fulltext", "custom"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "Choose a Folder"}, {"name": "query", "type": "text", "label": "Query", "required": true}, {"name": "operator", "type": "select", "label": "Search options", "required": true, "validate": {"enum": ["=", "contains"]}}]}}, {"id": 16, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 2100, "y": 150}}, "routes": [{"flow": [{"id": 13, "module": "google-drive:createAFolder", "version": 4, "parameters": {"__IMTCONN__": 3099236}, "filter": {"name": "Archive Does not exist", "conditions": [[{"a": "{{15.id}}", "o": "notexist"}]]}, "mapper": {"name": "archive", "shared": false, "folderId": "/1pOh5hHKXb2v0vQMd4HkP0bF96KK0u6db", "destination": "drive"}, "metadata": {"designer": {"x": 2400, "y": 0, "name": "Create archive folder if does not exist"}, "restore": {"expect": {"folderId": {"mode": "chose", "path": ["tiktok uploads"]}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Demo connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "destination", "type": "select", "label": "New Drive Location", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "New Folder Location", "required": true}, {"name": "name", "type": "text", "label": "New Folder's Name"}, {"name": "shared", "type": "boolean", "label": "Share Folder", "required": true}]}}]}, {"flow": [{"id": 17, "module": "google-drive:searchForFilesFolders", "version": 4, "parameters": {"__IMTCONN__": 3099236}, "mapper": {"limit": "1", "query": "archive", "select": "list", "folderId": "/1pOh5hHKXb2v0vQMd4HkP0bF96KK0u6db", "operator": "=", "retrieve": "folder", "searchType": "title", "destination": "drive"}, "metadata": {"designer": {"x": 2400, "y": 300, "name": "Find archive folder"}, "restore": {"expect": {"select": {"label": "Select from the list"}, "folderId": {"mode": "chose", "path": ["tiktok uploads"]}, "operator": {"label": "Search for exact term"}, "retrieve": {"label": "Folders"}, "searchType": {"label": "Search within file/folder names"}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Demo connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "select", "type": "select", "label": "Select the Method", "required": true, "validate": {"enum": ["map", "list"]}}, {"name": "retrieve", "type": "select", "label": "Retrieve", "required": true, "validate": {"enum": ["file", "folder", "file_folder"]}}, {"name": "searchType", "type": "select", "label": "Search", "validate": {"enum": ["title", "fulltext", "custom"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "folderId", "type": "folder", "label": "Choose a Folder"}, {"name": "query", "type": "text", "label": "Query", "required": true}, {"name": "operator", "type": "select", "label": "Search options", "required": true, "validate": {"enum": ["=", "contains"]}}]}}, {"id": 14, "module": "google-drive:moveAFileIntoAFolder", "version": 4, "parameters": {"__IMTCONN__": 3099236}, "mapper": {"file": "{{8.id}}", "move": "drive", "select": "file", "folderId": "{{17.id}}", "destination": "drive"}, "metadata": {"designer": {"x": 2700, "y": 300, "name": "archive video"}, "restore": {"expect": {"file": {"mode": "edit", "path": []}, "move": {"label": "My Drive"}, "select": {"label": "File"}, "folderId": {"mode": "edit", "path": []}, "destination": {"label": "My Drive"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google-restricted"}, "label": "Demo connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google-restricted", "label": "Connection", "required": true}], "expect": [{"type": "hidden"}, {"name": "move", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "destination", "type": "select", "label": "New Drive Location", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "select", "type": "select", "label": "Select File/Folder", "required": true, "validate": {"enum": ["file", "folder"]}}, {"mode": "edit", "name": "file", "type": "file", "label": "File ID", "required": true}, {"name": "folderId", "type": "folder", "label": "New Folder Location", "required": true}]}}]}]}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us1.make.com"}}