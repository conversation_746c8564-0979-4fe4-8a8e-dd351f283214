{"name": "Instagram Scraping", "nodes": [{"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"hashtag\": \"landscaping\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "70fbd013-29a5-4059-bb90-6c53268705b2", "name": "When Executed by Another Workflow"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/reGe1ST3OBgYZSsZJ/runs?waitForFinish=300", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Bearer <YOUR_API_KEY>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"hashtags\": [\n        \"{{ $('When Executed by Another Workflow').item.json.hashtag }}\"\n    ],\n    \"resultsLimit\": 2,\n    \"resultsType\": \"posts\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "54fb8470-a94d-4326-9b21-7d995ae29f72", "name": "HTTP Request"}, {"parameters": {"url": "=https://api.apify.com/v2/datasets/{{ $json.data.defaultDatasetId }}/items?format=json", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer <YOUR_API_KEY>"}, {"name": "="}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [660, 0], "id": "a334cfa2-a68d-4e09-8bfd-a5ab885a69f9", "name": "HTTP Request1"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1100, 0], "id": "7138bc7c-eb13-470c-84cd-e4355a8baf63", "name": "Aggregate"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4", "mode": "list", "cachedResultName": "AI Agent Web Scraping Results", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 1853048620, "mode": "list", "cachedResultName": "Social Media", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/158g438RE2MUl_pKS2A_HTnarZ7XixFh_fUBHZmp8uQ4/edit#gid=1853048620"}, "columns": {"mappingMode": "defineBelow", "value": {"ID": "={{ $json.id }}", "Hashtag": "={{ $('When Executed by Another Workflow').item.json.hashtag }}", "Caption": "={{ $json.caption }}", "Profile": "=https://www.instagram.com/{{ $json.ownerUsername }}"}, "matchingColumns": ["ID"], "schema": [{"id": "ID", "displayName": "ID", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Hashtag", "displayName": "Hashtag", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Caption", "displayName": "Caption", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Profile", "displayName": "Profile", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [880, 0], "id": "6fa5cbbc-0a70-4e3e-9043-b11798b35bc8", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "oHG2DldApzJrCoBY", "name": "Google Sheets account 2"}}}, {"parameters": {"assignments": {"assignments": [{"id": "e389d681-3bc9-45a5-93a3-693e9e1f2baf", "name": "location", "value": "={{ encodeURIComponent(\"New York, US\") }}", "type": "string"}, {"id": "82bd17ab-9f52-47cc-b262-26eeb052cb09", "name": "industry_tag", "value": "landscaping", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 0], "id": "50d0733b-51df-48e1-a8ea-c2cf736efb81", "name": "<PERSON>"}], "pinData": {"When Executed by Another Workflow": [{"json": {"hashtag": "landscaping"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4557560a-d65e-46c9-9b6d-e31208415b1a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "d2017cc6d1e4b956d269a8123bffa72fb7aaa41ad37a73b7c0fb64c7d0e2edae"}, "id": "P3jGQDAnGLyToTCP", "tags": []}