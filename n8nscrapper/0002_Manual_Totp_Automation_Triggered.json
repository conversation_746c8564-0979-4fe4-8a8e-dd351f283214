{"id": "0wfomsVO0TQtQkwU", "meta": {"instanceId": "2e75c9fb3cdcf631da470c0180f0739986baa0ee860de53281e9edc3491b82a3"}, "name": "Complete Guide to Setting Up and Generating TOTP Codes in n8n 🔐", "tags": [], "nodes": [{"id": "0fe95b9a-be2b-4022-829e-8b6c801e5baf", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [-280, -340], "parameters": {}, "typeVersion": 1}, {"id": "02fee6b5-7770-4889-b9bb-89bface8872d", "name": "TOTP", "type": "n8n-nodes-base.totp", "position": [-40, -340], "parameters": {"options": {}}, "credentials": {"totpApi": {"id": "9487Zco8UqMQWnpf", "name": "TOTP account Mars55"}}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"timezone": "Asia/Tehran", "callerPolicy": "workflowsFromSameOwner", "executionOrder": "v1"}, "versionId": "d7a5fff3-3fcd-45cd-ba06-564097567ff5", "connections": {"When clicking ‘Test workflow’": {"main": [[{"node": "TOTP", "type": "main", "index": 0}]]}}}