{"name": "Social Scraper", "flow": [{"id": 1, "module": "airtable:webhook", "version": 3, "parameters": {"__IMTHOOK__": 436772}, "mapper": {}, "metadata": {"designer": {"x": 0, "y": 300}, "restore": {"parameters": {"__IMTHOOK__": {"data": {"editable": "false"}, "label": "INSTG8 CRM Proposal Generator "}}}, "parameters": [{"name": "__IMTHOOK__", "type": "hook:airtable", "label": "Webhook", "required": true}]}}, {"id": 3, "module": "airtable:ActionGetRecord", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{1.recordId}}", "base": "app62VdtYp2c7gydx", "table": "tblaAgdwupzCJCCwq", "useColumnId": false}, "metadata": {"designer": {"x": 300, "y": 300}, "restore": {"expect": {"base": {"mode": "chose", "label": "Content Engine 3.0"}, "table": {"mode": "chose", "label": "Users"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Username", "type": "text", "label": "Username"}, {"name": "YouTube Channel Name", "type": "text", "label": "YouTube Channel Name"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Run # Now", "type": "number", "label": "Run # Now"}, {"name": "Profile Pic", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "Profile Pic"}, {"name": "Nickname", "type": "text", "label": "Nickname"}, {"name": "Signature", "type": "text", "label": "Signature", "multiline": true}, {"name": "BioLink", "type": "text", "label": "BioLink"}, {"name": "Followers", "type": "number", "label": "Followers"}, {"name": "Following", "type": "number", "label": "Following"}, {"name": "Friends", "type": "number", "label": "Friends"}, {"name": "Total Likes", "type": "number", "label": "Total Likes"}, {"name": "Total Videos", "type": "number", "label": "Total Videos"}, {"name": "Run ID", "type": "text", "label": "Run ID"}, {"name": "Platform ID", "type": "text", "label": "Platform ID"}, {"name": "Verified", "type": "boolean", "label": "Verified"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Videos", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "Last Video Created", "type": "array", "label": "Last Video Created"}, {"name": "Oldest Post Date", "type": "text", "label": "Oldest Post Date"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Videos 2", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "Output (Report)", "spec": {"label": "Record ID"}, "type": "array", "label": "Output (Report)"}]}}, {"id": 7, "module": "builtin:BasicRouter", "version": 1, "mapper": null, "metadata": {"designer": {"x": 600, "y": 300}}, "routes": [{"flow": [{"id": 4, "module": "apify:runActor", "version": 1, "parameters": {"__IMTCONN__": 1870996}, "filter": {"name": "TikTok", "conditions": [[{"a": "{{3.Channel}}", "b": "TikTok", "o": "text:equal"}]]}, "mapper": {"actorId": "0FXVyOXXEmdGcV88a", "runSync": false, "inputBody": "{\n    \"oldestPostDate\": \"{{3.`Oldest Post Date`}}\",\n    \"profiles\": [\n        \"{{3.Username}}\"\n    ],\n    \"resultsPerPage\": 1,\n    \"shouldDownloadCovers\": true,\n    \"shouldDownloadSubtitles\": true,\n    \"shouldDownloadVideos\": true\n}"}, "metadata": {"designer": {"x": 900, "y": 0}, "restore": {"expect": {"memory": {"mode": "chose", "label": "Empty"}, "actorId": {"mode": "chose", "label": "clockworks/tiktok-profile-scraper"}, "runSync": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "apify"}, "label": "My Apify connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "actorId", "type": "select", "label": "Actor", "required": true}, {"name": "runSync", "type": "boolean", "label": "Run synchronously", "required": true}, {"name": "inputBody", "type": "text", "label": "Input JSON"}, {"name": "build", "type": "text", "label": "Build"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout"}, {"name": "memory", "type": "select", "label": "Memory", "validate": {"enum": [128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768]}}]}}, {"id": 6, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{1.recordId}}", "base": "app62VdtYp2c7gydx", "table": "tblaAgdwupzCJCCwq", "record": {"fldrxFL0VvAcEbyC2": "{{4.defaultDatasetId}}"}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1200, "y": 0}, "restore": {"expect": {"base": {"label": "Content Engine 3.0"}, "table": {"label": "Users"}, "record": {"nested": {"fld5XSZblnZOLCmTe": {"mode": "chose"}, "fldAhaynubBmfvKal": {"mode": "chose"}, "fldBoyFX262Bhphb4": {"mode": "chose"}, "fldKSEsoGXOGlZ53Y": {"mode": "chose"}, "fldkNktlvOa8WhYwa": {"mode": "edit"}, "fldy2FVOdprlEmO2y": {"mode": "chose"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fldLukkB6DLW8jsg5", "type": "text", "label": "Username"}, {"name": "fldwddGQnsvnB7eFw", "type": "text", "label": "YouTube Channel Name"}, {"mode": "edit", "name": "fldkNktlvOa8WhYwa", "type": "select", "label": "Channel"}, {"name": "fldGDs3retYm9V9mt", "type": "number", "label": "Run # Now"}, {"name": "fldBoyFX262Bhphb4", "spec": [{"name": "url", "type": "text", "label": "File URL"}, {"name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Profile Pic"}, {"name": "fldk9UpuuVyqZKIRn", "type": "text", "label": "Nickname"}, {"name": "fldLLOs008GhG02TW", "type": "text", "label": "Signature"}, {"name": "fld3E72IonPWZiPuq", "type": "text", "label": "BioLink"}, {"name": "fldxIrzx9DEY8coBi", "type": "number", "label": "Followers"}, {"name": "fld05G1G7cFnNAOKU", "type": "number", "label": "Following"}, {"name": "fld2HBLhnWU0WPaAG", "type": "number", "label": "Friends"}, {"name": "fldmXDR2kGjxauw8a", "type": "number", "label": "Total Likes"}, {"name": "fldnbPgJ0l26NxC1Z", "type": "number", "label": "Total Videos"}, {"name": "fldrxFL0VvAcEbyC2", "type": "text", "label": "Run ID"}, {"name": "fldqUhjxq8DfePbtx", "type": "text", "label": "Platform ID"}, {"name": "fldKSEsoGXOGlZ53Y", "type": "boolean", "label": "Verified"}, {"name": "fldAhaynubBmfvKal", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "fldy2FVOdprlEmO2y", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "fld5XSZblnZOLCmTe", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Output (Report)"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Username", "type": "text", "label": "Username"}, {"name": "YouTube Channel Name", "type": "text", "label": "YouTube Channel Name"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Run # Now", "type": "number", "label": "Run # Now"}, {"name": "Profile Pic", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "Profile Pic"}, {"name": "Nickname", "type": "text", "label": "Nickname"}, {"name": "Signature", "type": "text", "label": "Signature", "multiline": true}, {"name": "BioLink", "type": "text", "label": "BioLink"}, {"name": "Followers", "type": "number", "label": "Followers"}, {"name": "Following", "type": "number", "label": "Following"}, {"name": "Friends", "type": "number", "label": "Friends"}, {"name": "Total Likes", "type": "number", "label": "Total Likes"}, {"name": "Total Videos", "type": "number", "label": "Total Videos"}, {"name": "Run ID", "type": "text", "label": "Run ID"}, {"name": "Platform ID", "type": "text", "label": "Platform ID"}, {"name": "Verified", "type": "boolean", "label": "Verified"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Videos", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "Last Video Created", "type": "array", "label": "Last Video Created"}, {"name": "Oldest Post Date", "type": "text", "label": "Oldest Post Date"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Videos 2", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "Output (Report)", "spec": {"label": "Record ID"}, "type": "array", "label": "Output (Report)"}]}}]}, {"flow": [{"id": 8, "module": "apify:runActor", "version": 1, "parameters": {"__IMTCONN__": 1870996}, "filter": {"name": "YouTube", "conditions": [[{"a": "{{3.Channel}}", "b": "YouTube", "o": "text:equal"}]]}, "mapper": {"actorId": "h7sDV53CddomktSi5", "runSync": false, "inputBody": "{\n  \"downloadSubtitles\": true,\n  \"hasCC\": false,\n  \"hasLocation\": false,\n  \"hasSubtitles\": false,\n  \"is360\": false,\n  \"is3D\": false,\n  \"is4K\": false,\n  \"isBought\": false,\n  \"isHD\": false,\n  \"isHDR\": false,\n  \"isLive\": false,\n  \"isVR180\": false,\n  \"maxResultStreams\": 0,\n  \"maxResultsShorts\": 0,\n  \"oldestPostDate\": \"2024-10-01\",\n  \"preferAutoGeneratedSubtitles\": false,\n  \"saveSubsToKVS\": false,\n  \"startUrls\": [\n    {\n      \"url\": \"{{3.URL}}\",\n      \"method\": \"GET\"\n    }\n  ],\n  \"subtitlesFormat\": \"srt\",\n  \"subtitlesLanguage\": \"en\"\n}"}, "metadata": {"designer": {"x": 900, "y": 300}, "restore": {"expect": {"memory": {"mode": "chose", "label": "Empty"}, "actorId": {"mode": "chose", "label": "streamers/youtube-scraper"}, "runSync": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "apify"}, "label": "My Apify connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "actorId", "type": "select", "label": "Actor", "required": true}, {"name": "runSync", "type": "boolean", "label": "Run synchronously", "required": true}, {"name": "inputBody", "type": "text", "label": "Input JSON"}, {"name": "build", "type": "text", "label": "Build"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout"}, {"name": "memory", "type": "select", "label": "Memory", "validate": {"enum": [128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768]}}]}}, {"id": 9, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{1.recordId}}", "base": "app62VdtYp2c7gydx", "table": "tblaAgdwupzCJCCwq", "record": {}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1200, "y": 300}, "restore": {"expect": {"base": {"label": "Content Engine 3.0"}, "table": {"label": "Users"}, "record": {"nested": {"fld5XSZblnZOLCmTe": {"mode": "chose"}, "fldAhaynubBmfvKal": {"mode": "chose"}, "fldBoyFX262Bhphb4": {"mode": "chose"}, "fldKSEsoGXOGlZ53Y": {"mode": "chose"}, "fldkNktlvOa8WhYwa": {"mode": "edit"}, "fldy2FVOdprlEmO2y": {"mode": "chose"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fldLukkB6DLW8jsg5", "type": "text", "label": "Username"}, {"name": "fldwddGQnsvnB7eFw", "type": "text", "label": "YouTube Channel Name"}, {"mode": "edit", "name": "fldkNktlvOa8WhYwa", "type": "select", "label": "Channel"}, {"name": "fldGDs3retYm9V9mt", "type": "number", "label": "Run # Now"}, {"name": "fldBoyFX262Bhphb4", "spec": [{"name": "url", "type": "text", "label": "File URL"}, {"name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Profile Pic"}, {"name": "fldk9UpuuVyqZKIRn", "type": "text", "label": "Nickname"}, {"name": "fldLLOs008GhG02TW", "type": "text", "label": "Signature"}, {"name": "fld3E72IonPWZiPuq", "type": "text", "label": "BioLink"}, {"name": "fldxIrzx9DEY8coBi", "type": "number", "label": "Followers"}, {"name": "fld05G1G7cFnNAOKU", "type": "number", "label": "Following"}, {"name": "fld2HBLhnWU0WPaAG", "type": "number", "label": "Friends"}, {"name": "fldmXDR2kGjxauw8a", "type": "number", "label": "Total Likes"}, {"name": "fldnbPgJ0l26NxC1Z", "type": "number", "label": "Total Videos"}, {"name": "fldrxFL0VvAcEbyC2", "type": "text", "label": "Run ID"}, {"name": "fldqUhjxq8DfePbtx", "type": "text", "label": "Platform ID"}, {"name": "fldKSEsoGXOGlZ53Y", "type": "boolean", "label": "Verified"}, {"name": "fldAhaynubBmfvKal", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "fldy2FVOdprlEmO2y", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "fld5XSZblnZOLCmTe", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Output (Report)"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Username", "type": "text", "label": "Username"}, {"name": "YouTube Channel Name", "type": "text", "label": "YouTube Channel Name"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Run # Now", "type": "number", "label": "Run # Now"}, {"name": "Profile Pic", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "Profile Pic"}, {"name": "Nickname", "type": "text", "label": "Nickname"}, {"name": "Signature", "type": "text", "label": "Signature", "multiline": true}, {"name": "BioLink", "type": "text", "label": "BioLink"}, {"name": "Followers", "type": "number", "label": "Followers"}, {"name": "Following", "type": "number", "label": "Following"}, {"name": "Friends", "type": "number", "label": "Friends"}, {"name": "Total Likes", "type": "number", "label": "Total Likes"}, {"name": "Total Videos", "type": "number", "label": "Total Videos"}, {"name": "Run ID", "type": "text", "label": "Run ID"}, {"name": "Platform ID", "type": "text", "label": "Platform ID"}, {"name": "Verified", "type": "boolean", "label": "Verified"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Videos", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "Last Video Created", "type": "array", "label": "Last Video Created"}, {"name": "Oldest Post Date", "type": "text", "label": "Oldest Post Date"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Videos 2", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "Output (Report)", "spec": {"label": "Record ID"}, "type": "array", "label": "Output (Report)"}]}}]}, {"flow": [{"id": 10, "module": "apify:runActor", "version": 1, "parameters": {"__IMTCONN__": 1870996}, "filter": {"name": "Linkedin", "conditions": [[{"a": "{{3.Channel}}", "b": "YouTube", "o": "text:equal"}]]}, "mapper": {"actorId": "h7sDV53CddomktSi5", "runSync": false, "inputBody": "{\n  \"downloadSubtitles\": true,\n  \"hasCC\": false,\n  \"hasLocation\": false,\n  \"hasSubtitles\": false,\n  \"is360\": false,\n  \"is3D\": false,\n  \"is4K\": false,\n  \"isBought\": false,\n  \"isHD\": false,\n  \"isHDR\": false,\n  \"isLive\": false,\n  \"isVR180\": false,\n  \"maxResultStreams\": 0,\n  \"maxResultsShorts\": 0,\n  \"oldestPostDate\": \"2024-10-01\",\n  \"preferAutoGeneratedSubtitles\": false,\n  \"saveSubsToKVS\": false,\n  \"startUrls\": [\n    {\n      \"url\": \"{{3.URL}}\",\n      \"method\": \"GET\"\n    }\n  ],\n  \"subtitlesFormat\": \"srt\",\n  \"subtitlesLanguage\": \"en\"\n}"}, "metadata": {"designer": {"x": 900, "y": 600}, "restore": {"expect": {"memory": {"mode": "chose", "label": "Empty"}, "actorId": {"mode": "chose", "label": "streamers/youtube-scraper"}, "runSync": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "apify"}, "label": "My Apify connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:apify2,apify", "label": "Connection", "required": true}], "expect": [{"name": "actorId", "type": "select", "label": "Actor", "required": true}, {"name": "runSync", "type": "boolean", "label": "Run synchronously", "required": true}, {"name": "inputBody", "type": "text", "label": "Input JSON"}, {"name": "build", "type": "text", "label": "Build"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout"}, {"name": "memory", "type": "select", "label": "Memory", "validate": {"enum": [128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768]}}]}}, {"id": 11, "module": "airtable:ActionUpdateRecords", "version": 3, "parameters": {"__IMTCONN__": 2006099}, "mapper": {"id": "{{1.recordId}}", "base": "app62VdtYp2c7gydx", "table": "tblaAgdwupzCJCCwq", "record": {}, "typecast": false, "useColumnId": false}, "metadata": {"designer": {"x": 1200, "y": 600}, "restore": {"expect": {"base": {"label": "Content Engine 3.0"}, "table": {"label": "Users"}, "record": {"nested": {"fld5XSZblnZOLCmTe": {"mode": "chose"}, "fldAhaynubBmfvKal": {"mode": "chose"}, "fldBoyFX262Bhphb4": {"mode": "chose"}, "fldKSEsoGXOGlZ53Y": {"mode": "chose"}, "fldkNktlvOa8WhYwa": {"mode": "edit"}, "fldy2FVOdprlEmO2y": {"mode": "chose"}}}, "typecast": {"mode": "chose"}, "useColumnId": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "airtable2"}, "label": "My Airtable Token or Key connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:airtable3,airtable2", "label": "Connection", "required": true}], "expect": [{"name": "base", "type": "select", "label": "Base", "required": true}, {"name": "typecast", "type": "boolean", "label": "Smart links", "required": true}, {"name": "useColumnId", "type": "boolean", "label": "Use Column ID", "required": true}, {"name": "table", "type": "select", "label": "Table", "required": true}, {"name": "id", "type": "text", "label": "Record ID", "required": true}, {"name": "record", "spec": [{"name": "fldLukkB6DLW8jsg5", "type": "text", "label": "Username"}, {"name": "fldwddGQnsvnB7eFw", "type": "text", "label": "YouTube Channel Name"}, {"mode": "edit", "name": "fldkNktlvOa8WhYwa", "type": "select", "label": "Channel"}, {"name": "fldGDs3retYm9V9mt", "type": "number", "label": "Run # Now"}, {"name": "fldBoyFX262Bhphb4", "spec": [{"name": "url", "type": "text", "label": "File URL"}, {"name": "filename", "type": "filename", "label": "File name"}], "type": "array", "label": "Profile Pic"}, {"name": "fldk9UpuuVyqZKIRn", "type": "text", "label": "Nickname"}, {"name": "fldLLOs008GhG02TW", "type": "text", "label": "Signature"}, {"name": "fld3E72IonPWZiPuq", "type": "text", "label": "BioLink"}, {"name": "fldxIrzx9DEY8coBi", "type": "number", "label": "Followers"}, {"name": "fld05G1G7cFnNAOKU", "type": "number", "label": "Following"}, {"name": "fld2HBLhnWU0WPaAG", "type": "number", "label": "Friends"}, {"name": "fldmXDR2kGjxauw8a", "type": "number", "label": "Total Likes"}, {"name": "fldnbPgJ0l26NxC1Z", "type": "number", "label": "Total Videos"}, {"name": "fldrxFL0VvAcEbyC2", "type": "text", "label": "Run ID"}, {"name": "fldqUhjxq8DfePbtx", "type": "text", "label": "Platform ID"}, {"name": "fldKSEsoGXOGlZ53Y", "type": "boolean", "label": "Verified"}, {"name": "fldAhaynubBmfvKal", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "fldy2FVOdprlEmO2y", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "fld5XSZblnZOLCmTe", "spec": {"name": "value", "label": "Record ID"}, "type": "array", "label": "Output (Report)"}], "type": "collection", "label": "Record"}], "interface": [{"name": "id", "type": "text", "label": "ID"}, {"name": "createdTime", "type": "date", "label": "Created Time"}, {"name": "Username", "type": "text", "label": "Username"}, {"name": "YouTube Channel Name", "type": "text", "label": "YouTube Channel Name"}, {"name": "Channel", "type": "text", "label": "Channel"}, {"name": "Run # Now", "type": "number", "label": "Run # Now"}, {"name": "Profile Pic", "spec": [{"name": "id", "type": "text", "label": "ID"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}, {"name": "url", "type": "url", "label": "URL"}, {"name": "filename", "type": "filename", "label": "File name"}, {"name": "size", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Size"}, {"name": "type", "type": "text", "label": "MIME type"}, {"name": "thumbnails", "spec": [{"name": "small", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Small"}, {"name": "large", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Large"}, {"name": "full", "spec": [{"name": "url", "type": "url", "label": "URL"}, {"name": "width", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "height", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Height"}], "type": "collection", "label": "Full"}], "type": "collection", "label": "Thumbnails"}], "type": "array", "label": "Profile Pic"}, {"name": "Nickname", "type": "text", "label": "Nickname"}, {"name": "Signature", "type": "text", "label": "Signature", "multiline": true}, {"name": "BioLink", "type": "text", "label": "BioLink"}, {"name": "Followers", "type": "number", "label": "Followers"}, {"name": "Following", "type": "number", "label": "Following"}, {"name": "Friends", "type": "number", "label": "Friends"}, {"name": "Total Likes", "type": "number", "label": "Total Likes"}, {"name": "Total Videos", "type": "number", "label": "Total Videos"}, {"name": "Run ID", "type": "text", "label": "Run ID"}, {"name": "Platform ID", "type": "text", "label": "Platform ID"}, {"name": "Verified", "type": "boolean", "label": "Verified"}, {"name": "Record ID", "type": "text", "label": "Record ID"}, {"name": "Videos", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos"}, {"name": "Last Video Created", "type": "array", "label": "Last Video Created"}, {"name": "Oldest Post Date", "type": "text", "label": "Oldest Post Date"}, {"name": "Last Modified", "type": "date", "label": "Last Modified"}, {"name": "Videos 2", "spec": {"label": "Record ID"}, "type": "array", "label": "Videos 2"}, {"name": "Output (Report)", "spec": {"label": "Record ID"}, "type": "array", "label": "Output (Report)"}]}}]}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}