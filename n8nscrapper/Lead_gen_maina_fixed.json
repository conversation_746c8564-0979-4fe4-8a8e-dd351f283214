{"name": "Lead Generation Workflow", "nodes": [{"id": "1", "type": "n8n-nodes-base.cron", "position": [200, 300], "parameters": {"triggerTimes": {"item": [{"mode": "everyDay", "hour": 9}]}}, "name": "Daily Trigger"}, {"id": "2", "type": "n8n-nodes-base.googleSheets", "position": [400, 300], "parameters": {"authentication": "serviceAccount", "operation": "read", "sheetId": "your-google-sheet-id", "range": "Leads!A1:E", "options": {"returnAll": true}}, "name": "Fetch Leads"}, {"id": "3", "type": "n8n-nodes-base.executeWorkflow", "position": [600, 300], "parameters": {"workflowId": "sub-workflow-id", "input": "passThrough", "options": {}}, "name": "Enrich and Save Lead"}, {"id": "4", "type": "n8n-nodes-base.slack", "position": [800, 300], "parameters": {"authentication": "accessToken", "channel": "#sales-team", "message": "New leads have been added to the database. Please check your dashboard!", "options": {}}, "name": "Notify Sales Team"}], "connections": {"1": {"main": [[{"node": "2", "type": "main", "index": 0}]]}, "2": {"main": [[{"node": "3", "type": "main", "index": 0}]]}, "3": {"main": [[{"node": "4", "type": "main", "index": 0}]]}}}