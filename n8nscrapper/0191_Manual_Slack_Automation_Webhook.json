{"nodes": [{"name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [160, 300], "parameters": {}, "typeVersion": 1}, {"name": "Download the file", "type": "n8n-nodes-base.httpRequest", "position": [420, 300], "parameters": {"url": "https://n8n.io/_nuxt/img/sync-data-between-apps.a4be8c7.png", "options": {}, "responseFormat": "file"}, "typeVersion": 1}, {"name": "Post to Slack", "type": "n8n-nodes-base.slack", "position": [640, 300], "parameters": {"options": {"channelIds": ["C02GP22NHJ6"], "initialComment": "This is the file"}, "resource": "file", "binaryData": true, "authentication": "oAuth2"}, "credentials": {"slackOAuth2Api": {"id": "124", "name": "cloud_demo"}}, "typeVersion": 1}], "connections": {"Download the file": {"main": [[{"node": "Post to Slack", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "Download the file", "type": "main", "index": 0}]]}}}