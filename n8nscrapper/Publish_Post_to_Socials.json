{"name": "Publish Post to Socials (Community)", "nodes": [{"parameters": {"path": "ad38e725-331e-4ed3-a847-1d0fcb35cd7c", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-560, 0], "id": "c2d25101-bbf7-4c65-a452-304ae63f2d7f", "name": "Webhook", "webhookId": "ad38e725-331e-4ed3-a847-1d0fcb35cd7c"}, {"parameters": {"url": "https://api.baserow.io/api/database/rows/table/[YOUR BASEROW DATABASE ID]/?user_field_names=true", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "user_field_names", "value": "true"}, {"name": "order_by", "value": "-Created on"}, {"name": "size", "value": "1"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-320, -20], "id": "dba45114-52fd-4c0e-9e14-561396b4811f", "name": "Get Row", "credentials": {"httpHeaderAuth": {"id": "c4wnATvfEEngaWOY", "name": "BaseRow"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "80be59c2-4cf9-4a5d-bb51-9f00e88a33ea", "leftValue": "={{ $json.results[0].Status.value }}", "rightValue": "Pending Approval", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-80, -20], "id": "85948dfb-4daf-49f3-a25f-99041ae7a51b", "name": "If"}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "This post has already been approved and posted.", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [-60, 320], "id": "fb800938-423c-40b5-8fec-1acb07464284", "name": "WhatsApp Business Cloud", "webhookId": "6c3fd82e-bea7-48e4-a3e2-73f477c66824", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.results[0]['Request Type'] }}", "rightValue": "Twitter", "operator": {"type": "string", "operation": "equals"}, "id": "a4708f00-38ea-48ee-90c5-2128f4946abb"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Twitter"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "21119ec0-35c9-4ff2-bb05-510bf14a9880", "leftValue": "={{ $json.results[0]['Request Type'] }}", "rightValue": "Instagram", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Instagram"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cd75487d-634e-41cf-96fe-98e47927675b", "leftValue": "={{ $json.results[0]['Request Type'] }}", "rightValue": "Facebook", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Facebook"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1fc0019b-8345-45bf-ad15-6a96d66ed698", "leftValue": "={{ $json.results[0]['Request Type'] }}", "rightValue": "LinkedIn", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "LinkedIn"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [220, -160], "id": "15b83225-5478-4bee-86a6-42f37b222e9f", "name": "Switch"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "IG id", "edge": "media", "options": {"queryParameters": {"parameter": [{"name": "image_url", "value": "={{ $json.results[0]['Image to post'] }}"}, {"name": "caption", "value": "={{ $json.results[0]['Text Body'] }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [680, -300], "id": "8e6a6802-efbf-4548-9973-3ca7632568ba", "name": "Instagram Container", "credentials": {"facebookGraphApi": {"id": "trT4s0yoc0VaOEr8", "name": "Instagram Auto Poster"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "IG id", "edge": "media_publish", "options": {"queryParameters": {"parameter": [{"name": "creation_id", "value": "={{ $json.id }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [960, -400], "id": "b4f7731e-7629-4ee6-9af0-20dadfdb0c46", "name": "Publish IG", "credentials": {"facebookGraphApi": {"id": "trT4s0yoc0VaOEr8", "name": "Instagram Auto Poster"}}}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [1260, -460], "id": "14bb5b93-c5ee-4e8e-af0e-7a3a90f35629", "name": "Baserow", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"person": "fnBs-J9wSm", "text": "={{ $json.results[0]['Text Body'] }}", "shareMediaCategory": "IMAGE", "additionalFields": {"visibility": "PUBLIC"}}, "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [860, 460], "id": "948154b0-7c26-4a03-8b70-e3bcdd298242", "name": "LinkedIn", "credentials": {"linkedInOAuth2Api": {"id": "2raSmaRHQqZoGz4A", "name": "LinkedIn account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.results[0]['Post type'] }}", "rightValue": "Image and Text", "operator": {"type": "string", "operation": "equals"}, "id": "efb54dab-6f8e-45e7-8bd1-c79235988a7e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image and Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d57d344a-613d-4e1a-b81c-1a23734f6d2e", "leftValue": "={{ $json.results[0]['Post type'] }}", "rightValue": "Text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [580, 80], "id": "16f869d3-82eb-4143-a7d0-9f83cd5ac150", "name": "Switch1"}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "me", "edge": "feed", "options": {"queryParameters": {"parameter": [{"name": "message", "value": "={{ $json.results[0]['Text Body'] }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1020, 260], "id": "fb98bd0d-215f-49e0-b71b-57fef8e0aaae", "name": "Facebook Text", "credentials": {"facebookGraphApi": {"id": "DANNoPbbWgaSIVyY", "name": "Facebook April25"}}}, {"parameters": {"httpRequestMethod": "POST", "graphApiVersion": "v22.0", "node": "me", "edge": "photos", "options": {"queryParameters": {"parameter": [{"name": "message", "value": "={{ $json.results[0]['Text Body'] }}"}, {"name": "url", "value": "={{ $json.results[0]['Image to post'] }}"}]}}}, "type": "n8n-nodes-base.facebookGraphApi", "typeVersion": 1, "position": [1000, -40], "id": "62ae1616-4af9-49b2-bc88-15a99e4c5cf6", "name": "Facebook Image & Text", "credentials": {"facebookGraphApi": {"id": "DANNoPbbWgaSIVyY", "name": "Facebook April25"}}}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [1300, -100], "id": "bcced566-e892-4499-93c8-f9fbaf76c878", "name": "Baserow1", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [1300, 280], "id": "13f35bab-5572-4a1e-98f6-2599f1ba7c61", "name": "Baserow2", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your Instagram Post has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1460, -480], "id": "497fc2bd-246d-448d-91b9-2121304b1074", "name": "WhatsApp Instagram Post", "webhookId": "72c15bc9-e050-4c78-b604-8cfe5cf84ae3", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your Facebook Post has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1520, -140], "id": "0b8345db-d7c3-4b78-9a89-43eb3b1e8b32", "name": "WhatsApp Facebook Post", "webhookId": "771bdc97-26e9-4175-b725-7ac8c45f1829", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your Facebook Post has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1500, 240], "id": "23da4970-9b59-4438-b6e5-0ce16727806f", "name": "WhatsApp Facebook Post1", "webhookId": "8ff12003-663f-47f7-8e5c-6332ed462e19", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"text": "={{ $json.results[0]['Text Body'] }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [720, -520], "id": "102627f4-c999-49fc-9648-b8cfcbfa6302", "name": "X", "credentials": {"twitterOAuth2Api": {"id": "RExiHS6NdJIiNQTX", "name": "X account"}}}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [980, -620], "id": "8ac3be7d-9cbb-48cc-a73e-41ccba3110c4", "name": "Baserow3", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your Tweet has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1260, -680], "id": "94d7efee-1dd1-4d95-9edc-022eb0253a18", "name": "WhatsApp X Post", "webhookId": "384d5774-5233-4be7-b5de-ad1f921aefe7", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.results[0]['Post type'] }}", "rightValue": "Image and Text", "operator": {"type": "string", "operation": "equals"}, "id": "efb54dab-6f8e-45e7-8bd1-c79235988a7e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image and Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d57d344a-613d-4e1a-b81c-1a23734f6d2e", "leftValue": "={{ $json.results[0]['Post type'] }}", "rightValue": "Text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [420, -500], "id": "496a4de5-43dc-4427-94d3-99c197ba91fa", "name": "Switch2"}, {"parameters": {"url": "={{ $json.results[0]['Image to post'] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [620, -680], "id": "320ba5c3-5247-4a39-8a2b-c72a24cf0877", "name": "Image"}, {"parameters": {"method": "POST", "url": "https://upload.twitter.com/1.1/media/upload.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "twitterOAuth1Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer [your twitter/x OAuth token. I used OAuth not OAuth2.]"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "media", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [840, -860], "id": "aadc7954-0bef-4cd2-9ab7-e223dff2673f", "name": "Upload image", "credentials": {"twitterOAuth2Api": {"id": "RExiHS6NdJIiNQTX", "name": "X account"}, "twitterOAuth1Api": {"id": "Y0w1rQF5tiQMNu3C", "name": "X OAuth account"}}}, {"parameters": {"text": "={{ $('Image').item.json.results[0]['Text Body'] }}", "additionalFields": {"attachments": "={{ $json.media_id_string }}"}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [1040, -940], "id": "cbb5f24e-57f6-4f8a-8a80-1b5d4e1807a4", "name": "X1", "credentials": {"twitterOAuth2Api": {"id": "RExiHS6NdJIiNQTX", "name": "X account"}}}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [1240, -1020], "id": "7fa3a686-71b1-44a1-b956-f32ad9b3b5bc", "name": "Baserow4", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your Tweet has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1420, -1040], "id": "c521edfd-0300-47ca-b270-b5f169bb1092", "name": "WhatsApp X Post1", "webhookId": "e8b56f70-eef4-483f-b8ea-32750cdc1c5d", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.results[0]['Post type'] }}", "rightValue": "Image and Text", "operator": {"type": "string", "operation": "equals"}, "id": "efb54dab-6f8e-45e7-8bd1-c79235988a7e"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Image and Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d57d344a-613d-4e1a-b81c-1a23734f6d2e", "leftValue": "={{ $json.results[0]['Post type'] }}", "rightValue": "Text", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Text"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [320, 200], "id": "ee945ba4-40fe-4d92-84cf-fc0e48e91865", "name": "Switch3"}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [1080, 780], "id": "********-7a64-487f-a7fe-a6eef56ebc38", "name": "Baserow5", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your LinkedIn Post has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1380, 800], "id": "34f3eeaf-a88a-454a-83be-cfb1cae8fe16", "name": "WhatsApp LinkedIn Post Text", "webhookId": "1e73b64f-3a19-4734-acb5-819799fa9f1b", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"url": "={{ $json.results[0]['Image to post'] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [620, 320], "id": "022908e8-a00e-40f1-b47c-1fbb728eff9b", "name": "Image2"}, {"parameters": {"operation": "update", "databaseId": 212959, "tableId": 511224, "rowId": "={{ $('Switch').item.json.results[0].id }}", "fieldsUi": {"fieldValues": [{"fieldId": 4048176, "fieldValue": "Already Posted"}]}}, "type": "n8n-nodes-base.baserow", "typeVersion": 1, "position": [1080, 540], "id": "b00b9a3b-ef4b-4fd2-8543-757e2327fb66", "name": "Baserow6", "credentials": {"baserowApi": {"id": "4BYE5XcUdxN0ARz2", "name": "Baserow account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "***************", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "Your LinkedIn Post has been successfully posted! 😀🙌", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1460, 560], "id": "7348c4aa-5294-4a1b-955f-faf5fa9445b3", "name": "WhatsApp LinkedIn Post Text1", "webhookId": "e4b98eaf-0459-4e93-9720-380a27943475", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"person": "fnBs-J9wSm", "text": "={{ $json.results[0]['Text Body'] }}", "additionalFields": {"visibility": "PUBLIC"}}, "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [700, 660], "id": "d3583fbc-6ee5-4aa9-ac73-1a236333aaaf", "name": "LinkedIn1", "credentials": {"linkedInOAuth2Api": {"id": "2raSmaRHQqZoGz4A", "name": "LinkedIn account"}}}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Get Row", "type": "main", "index": 0}]]}, "Get Row": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Switch", "type": "main", "index": 0}], [{"node": "WhatsApp Business Cloud", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Switch2", "type": "main", "index": 0}], [{"node": "Instagram Container", "type": "main", "index": 0}], [{"node": "Switch1", "type": "main", "index": 0}], [{"node": "Switch3", "type": "main", "index": 0}]]}, "Instagram Container": {"main": [[{"node": "Publish IG", "type": "main", "index": 0}]]}, "Publish IG": {"main": [[{"node": "Baserow", "type": "main", "index": 0}]]}, "Switch1": {"main": [[{"node": "Facebook Image & Text", "type": "main", "index": 0}], [{"node": "Facebook Text", "type": "main", "index": 0}]]}, "Facebook Image & Text": {"main": [[{"node": "Baserow1", "type": "main", "index": 0}]]}, "Facebook Text": {"main": [[{"node": "Baserow2", "type": "main", "index": 0}]]}, "Baserow": {"main": [[{"node": "WhatsApp Instagram Post", "type": "main", "index": 0}]]}, "Baserow1": {"main": [[{"node": "WhatsApp Facebook Post", "type": "main", "index": 0}]]}, "Baserow2": {"main": [[{"node": "WhatsApp Facebook Post1", "type": "main", "index": 0}]]}, "X": {"main": [[{"node": "Baserow3", "type": "main", "index": 0}]]}, "Baserow3": {"main": [[{"node": "WhatsApp X Post", "type": "main", "index": 0}]]}, "Switch2": {"main": [[{"node": "Image", "type": "main", "index": 0}], [{"node": "X", "type": "main", "index": 0}]]}, "Image": {"main": [[{"node": "Upload image", "type": "main", "index": 0}]]}, "Upload image": {"main": [[{"node": "X1", "type": "main", "index": 0}]]}, "X1": {"main": [[{"node": "Baserow4", "type": "main", "index": 0}]]}, "Baserow4": {"main": [[{"node": "WhatsApp X Post1", "type": "main", "index": 0}]]}, "Switch3": {"main": [[{"node": "Image2", "type": "main", "index": 0}], [{"node": "LinkedIn1", "type": "main", "index": 0}]]}, "Baserow5": {"main": [[{"node": "WhatsApp LinkedIn Post Text", "type": "main", "index": 0}]]}, "Image2": {"main": [[{"node": "LinkedIn", "type": "main", "index": 0}]]}, "LinkedIn": {"main": [[{"node": "Baserow6", "type": "main", "index": 0}]]}, "Baserow6": {"main": [[{"node": "WhatsApp LinkedIn Post Text1", "type": "main", "index": 0}]]}, "LinkedIn1": {"main": [[{"node": "Baserow5", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ceeee800-db40-4941-a11b-9d71eb65c357", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a8fafcb279d93b3f223424ce87564d8ff1d6d229f3169c2fb88370c4a5f6eb0e"}, "id": "yIilNolRSBjODa83", "tags": []}