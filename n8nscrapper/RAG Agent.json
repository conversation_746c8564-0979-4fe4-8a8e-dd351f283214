{"name": "RAG Agent", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-540, -80], "id": "bcc14299-f2c3-48ee-9fed-27031e4ccf26", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "fileFolder", "filter": {"folderId": {"__rl": true, "value": "1Ou04FwAqqzKVmBk4cpPw5Xctcr4Z-JjJ", "mode": "list", "cachedResultName": "Prompt Engineering", "cachedResultUrl": "https://drive.google.com/drive/folders/1Ou04FwAqqzKVmBk4cpPw5Xctcr4Z-JjJ"}}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-320, -80], "id": "c6bb0708-09e9-4bdd-b922-5912ef73ae10", "name": "Search files and folders", "credentials": {"googleDriveOAuth2Api": {"id": "A6mPcnikbVNpddFF", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-100, -80], "id": "f3904e1a-b732-47a9-847f-34f07d82f3af", "name": "Get Content", "credentials": {"googleDriveOAuth2Api": {"id": "A6mPcnikbVNpddFF", "name": "Google Drive account"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [120, -80], "id": "aa5e726a-c7ea-4ef2-bb6c-b00fa33ff92f", "name": "Loop Over Items"}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "promptagent", "mode": "list", "cachedResultName": "promptagent"}, "options": {"pineconeNamespace": "Prompt Agent"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.3, "position": [380, -60], "id": "6a9680c4-2d3f-46e2-8e6c-9e09980f9c58", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "ffFsBVAeGcvnddo5", "name": "PineconeApi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [320, 160], "id": "84d116a4-225c-4221-b68c-100586916b7b", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "qMaaD0ZhL16QsZpI", "name": "OpenAi account"}}}, {"parameters": {"dataType": "binary", "textSplittingMode": "custom", "options": {}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1.1, "position": [420, 120], "id": "949fe252-48f0-422c-8d3e-a92a73ecf374", "name": "Default Data Loader"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.textSplitterRecursiveCharacterTextSplitter", "typeVersion": 1, "position": [480, 240], "id": "343d188c-6af3-4e01-bbab-f907ce451427", "name": "Recursive Character Text Splitter"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Search files and folders", "type": "main", "index": 0}]]}, "Search files and folders": {"main": [[{"node": "Get Content", "type": "main", "index": 0}]]}, "Get Content": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Recursive Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Pinecone Vector Store": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "cbb70904-cdc2-4e92-bb40-f81e4be36b73", "meta": {"templateCredsSetupCompleted": true, "instanceId": "9598ce8946c9cbdbc69c2a632c70b86983d9dd732b900497b5b7a148002fd110"}, "id": "DBNHGIZgcSAXRlxX", "tags": []}