{"name": "Request Publication Response (Community)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "0ca20df2-edf2-4ae2-bd3f-f8b451896df5", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-980, -60], "id": "161888fe-3a06-4071-90a6-48b3dcdbbfe5", "name": "Webhook", "webhookId": "0ca20df2-edf2-4ae2-bd3f-f8b451896df5", "credentials": {"httpHeaderAuth": {"id": "Dy8YPyFG7SOMTqZp", "name": "Head<PERSON> Auth account 3"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "8fb0326b-ad7b-4f67-b893-c833459e1897", "leftValue": "={{ $json.headers.authorization }}", "rightValue": "YOUR KEY", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-780, -60], "id": "d407dece-5f3e-4bb8-b776-fbea1bb63eb5", "name": "API Key Success"}, {"parameters": {"respondWith": "text", "responseBody": "You do not have access to this.", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-280, 120], "id": "ce109dc1-6a82-4a17-9019-2fd79d0f7fba", "name": "Respond to Webhook"}, {"parameters": {"assignments": {"assignments": [{"id": "264c9a83-ddbe-4418-b236-2e1baeeed92b", "name": "publication_webhook", "value": "WEBHOOK FROM PUBLISH TO SOCIAL", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-500, -140], "id": "90484ec8-53be-46bb-a2c1-5d6aee4857b9", "name": "<PERSON>"}, {"parameters": {"method": "POST", "url": "https://api.baserow.io/api/database/rows/table/[YOUR BASEROW DATABASE ID]/?user_field_names=true", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "user_field_names", "value": "true"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "Request Type", "value": "={{ $('API Key Success').item.json.body.request_type }}"}, {"name": "Status", "value": "Pending Approval"}, {"name": "Timestamp"}, {"name": "Post type", "value": "={{ $('Webhook').item.json.body.post_type }}"}, {"name": "Text Body", "value": "={{ $('Webhook').item.json.body.text_message }}"}, {"name": "Image to post", "value": "={{ $json.image_url }}"}, {"name": "publication_webhook", "value": "={{ $('Edit Fields').item.json.publication_webhook }}"}, {"name": "chat_id", "value": "={{ $('API Key Success').item.json.body.chat_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, -160], "id": "42dbac3a-10d2-4f64-a9be-6a8091ffeaba", "name": "BaseRow Social", "credentials": {"httpHeaderAuth": {"id": "c4wnATvfEEngaWOY", "name": "BaseRow"}}}, {"parameters": {"operation": "send", "phoneNumberId": "571789329356205", "recipientPhoneNumber": "[the phone number you want to whatsapp confirmation to post to go to]", "textBody": "=This is the Agent Safeguard. We've received a request to publish a {{ $json['Request Type'] }} post.\n\nDetails: Post Type: {{ $json['Post type'] }} \nText: {{ $json['Text Body'] }}\nImage: {{ $json['Image to post'] }}\n\nClick the following link to approve.  \n\n{{ $json.publication_webhook }}\n\nOtherwise ignore this message.", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [1100, -120], "id": "1dc73a05-4c34-44f8-9a5f-baad74e955c0", "name": "WhatsApp Business Cloud", "webhookId": "11117ea8-126b-4c4c-9d8e-025cdfa8eaca", "credentials": {"whatsAppApi": {"id": "repJ1az9JGVR6Ap7", "name": "WhatsApp account out 501"}}}, {"parameters": {"respondWith": "text", "responseBody": "The request for publication of the post has been received. Please check your WhatsApp to approve.", "options": {"responseCode": 200}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1320, -120], "id": "25f5caa4-649b-40ca-a188-794796c28031", "name": "Respond to Webhook1"}, {"parameters": {"method": "POST", "url": "https://upload.uploadcare.com/base/", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "image_url", "inputDataFieldName": "data"}, {"name": "UPLOADCARE_PUB_KEY"}, {"name": "UPLOADCARE_STORE", "value": "0"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, -300], "id": "f73eca90-ae98-4746-84ac-d849545efb31", "name": "UploadCare"}, {"parameters": {"url": "={{ $('API Key Success').item.json.body.image_url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, -300], "id": "2fdf1ad9-9dac-4779-8ef9-7633d4c7d3b4", "name": "Get image"}, {"parameters": {"assignments": {"assignments": [{"id": "ef5e6c7f-98f4-4c4b-9dce-de00981105ef", "name": "image_url", "value": "=https://ucarecdn.com/{{ $json.image_url }}/", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [420, -280], "id": "844f6ff6-3dfa-485d-b00b-6125ff7774c8", "name": "Edit Fields1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "752588e4-522a-4fe6-b4d0-dcfc424e5f7d", "leftValue": "={{ $('API Key Success').item.json.body.post_type }}", "rightValue": "Text", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-280, -140], "id": "5a2750e0-f087-46bf-a32f-ac9b5fdd6612", "name": "If"}, {"parameters": {"amount": 15}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [900, -160], "id": "e7b53ca5-9483-43a3-b1a7-15b04f086643", "name": "Wait", "webhookId": "d47f4601-3987-4e24-b650-b18f2ac92fd1"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "API Key Success", "type": "main", "index": 0}]]}, "API Key Success": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "BaseRow Social": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "WhatsApp Business Cloud": {"main": [[{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "UploadCare": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Get image": {"main": [[{"node": "UploadCare", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "BaseRow Social", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Get image", "type": "main", "index": 0}], [{"node": "BaseRow Social", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "WhatsApp Business Cloud", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0f2bcdd7-17af-4829-92c1-afe1bc680d8d", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a8fafcb279d93b3f223424ce87564d8ff1d6d229f3169c2fb88370c4a5f6eb0e"}, "id": "c8kk9QVimi4AsaV3", "tags": []}