{"name": "AIS calendar assistant", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-520, -100], "id": "ba2c1aa6-fb96-4162-bb20-56041d6eead7", "name": "Schedule Trigger"}, {"parameters": {"promptType": "define", "text": "=🎯 Goal:\nYou are a smart calendar assistant. Your job is to automatically review the user's calendar for the current week and generate a clean, structured summary of all upcoming appointments and events. The goal is to keep the user informed and prepared at a glance.\n\n📋 Tasks:\nRead all calendar events scheduled between Monday and Sunday of the current week.\n\n\nFor each event, extract:\n\n\nDay and date\n\n\nStart time\n\n\nEvent title\n\n\nLocation (if available)\n\n\nVideo call link or notes (optional, if included in the event)\n\n\nGroup events by day and sort them by time.\n\n\nIf no events exist for a day, skip it in the summary.\n\n\nFormat the response clearly and professionally, suitable for delivery via chat, email, or notification.\n\n\n\n🧾 Example Output (Expected Format):\n\n📅 Your Weekly Schedule: June 24 – June 30\nMonday, June 24\n9:00 AM — Team Standup (Zoom)\n\n\n3:00 PM — Dentist Appointment (SmileCare Clinic)\n\n\nWednesday, June 26\n1:00 PM — Project Review Meeting (Boardroom)\n\n\nFriday, June 28\n10:30 AM — Client Call with <PERSON> (Google Meet)\n\n\n\n💡 Notes:\nIf the week is empty, return:\n\n\n “📅 You have no scheduled events for this week.”\n\n\n\nAll times should be shown in the user’s local time zone.\n\n\nThe summary must be brief, easy to scan, and suitable for a quick morning check-in.\nOut put only the summary and do not add anything on your own to the summary.\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-300, -100], "id": "b31a02e6-f052-4ece-8043-d079a197d10e", "name": "AI Agent"}, {"parameters": {"model": "deepseek/deepseek-chat-v3-0324:free", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-440, 140], "id": "584281d6-31b3-4403-ad70-377cd16eb9cd", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "cek1PP8cr536DkaS", "name": "AIS OpenRouter ac"}}}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "options": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [-200, 140], "id": "06b5da50-3fb7-49c7-b67a-acb70e669d69", "name": "Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "Jv9MPYXEOwoHMJ6K", "name": "AIS Google Calendar ac"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08MDB1KDA4", "mode": "list", "cachedResultName": "all-slack-n8n-workflow"}, "text": "={{ $json.output }}", "otherOptions": {"includeLinkToWorkflow": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [100, -100], "id": "f67d3f3d-5948-4e19-9c75-9f0650b5622b", "name": "<PERSON><PERSON>ck", "webhookId": "0ebd227e-d291-4578-8b03-77d4f771f130", "credentials": {"slackApi": {"id": "ojut8kcEZq9tiisu", "name": "AIS Slack account"}}}, {"parameters": {"content": "# Calendar Assistant\n\n", "height": 640, "width": 1260, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-740, -260], "id": "83ae9d57-4340-43be-9581-eb0130f0a8ed", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Google Calendar": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "a662cc4f-18d1-47c2-a74d-79af3a373b7a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "558d88703fb65b2d0e44613bc35916258b0f0bf983c5d4730c00c424b77ca36a"}, "id": "n5byfQThMVev4VrX", "tags": []}