{"name": "Daily Tasks  List - Template", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-160, 40], "id": "30e05263-01e9-47f3-8f08-f4ec5e6e2030", "name": "Schedule Trigger"}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "eventdata", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [500, 40], "id": "0dfe541c-4134-4b63-8dba-9e47f04d5c48", "name": "Aggregate"}, {"parameters": {"jsCode": "// Loop over input items and add a new field called 'myNewField' to the JSON of each one\nfor (const item of $input.all()) {\n  // Check if item.json exists and has the required structure\n  if (item.json && typeof item.json === 'object') {\n    delete item.json.htmlLink;\n    delete item.json.etag;\n    delete item.json.iCalUID;\n    \n    // Safely delete nested properties\n    if (item.json.start && typeof item.json.start === 'object') {\n      delete item.json.start.timeZone;\n    }\n    if (item.json.end && typeof item.json.end === 'object') {\n      delete item.json.end.timeZone;\n    }\n    \n    delete item.json.kind;\n    delete item.json.reminders;\n    delete item.json.organizer;\n    delete item.json.creator;\n    delete item.json.created;\n    delete item.json.updated;\n    delete item.json.status;\n    delete item.json.eventType;\n    delete item.json.id;\n  }\n}\n\nreturn $input.all();"}, "id": "b22c060a-7363-4797-b1e6-f055e20da6fa", "name": "simplify_evens_json", "type": "n8n-nodes-base.code", "position": [360, 40], "typeVersion": 2, "alwaysOutputData": true}, {"parameters": {"promptType": "define", "text": "=Here is the calendar data: {{ JSON.stringify($json) }}\n\nFind all events scheduled for today {{ $now }} and create a daily schedule summary.", "options": {"systemMessage": "=You are generating a daily schedule summary from Google Calendar events. \n\nFind all events from the provided data and list them in chronological order for today. Include the time, title, and description for each event.\n\nFormat as a friendly daily summary.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [760, 40], "id": "aeff7b37-00c6-448c-9fc0-7cbfd8ee1c31", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [760, 220], "id": "5e4476b7-8069-4a48-b338-e01ed93d451d", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "yFBpQzkbIvosJRgs", "name": "OpenAI - mmmar<PERSON><EMAIL>"}}}, {"parameters": {"chatId": "1378743444", "text": "={{ $json.output }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [1160, 40], "id": "d137e07d-0aa7-4e25-a318-5042a5028984", "name": "Send message", "webhookId": "ed4ca9fd-6a97-4c2c-930c-798895ec9d62", "credentials": {"telegramApi": {"id": "qVlRpUiAXyh5cvNs", "name": "marcusm_assistant_bot"}}}, {"parameters": {"content": "## Daily Trigger", "height": 240, "width": 220}, "type": "n8n-nodes-base.stickyNote", "position": [-220, -20], "typeVersion": 1, "id": "481dbc8c-aa14-4211-82e3-bd40d4208611", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## Get Events", "height": 240, "width": 220, "color": 2}, "type": "n8n-nodes-base.stickyNote", "position": [60, -20], "typeVersion": 1, "id": "76fc533a-95c8-4bcc-9337-1bec0166a5f7", "name": "Sticky Note1"}, {"parameters": {"content": "## Simplify Data", "height": 240, "width": 320, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [320, -20], "typeVersion": 1, "id": "756a2fcc-8128-407e-8958-39943a06d205", "name": "Sticky Note2"}, {"parameters": {"content": "## Create Task List", "height": 380, "width": 400, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [680, -20], "typeVersion": 1, "id": "6a941db9-39fb-4eaf-aa3c-798fb51ac4b0", "name": "Sticky Note3"}, {"parameters": {"content": "## Send Message", "height": 240, "width": 280, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [1120, -20], "typeVersion": 1, "id": "b1d8c663-4a00-4119-b83b-485ee448920b", "name": "Sticky Note4"}, {"parameters": {"operation": "getAll", "calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "returnAll": true, "options": {}}, "type": "n8n-nodes-base.googleCalendar", "typeVersion": 1.3, "position": [120, 40], "id": "4ef3fc5d-c17c-406e-89ed-e85dc9723ad2", "name": "Calendar ", "alwaysOutputData": true, "credentials": {"googleCalendarOAuth2Api": {"id": "7cHYs1QLpUKbvhN4", "name": "<EMAIL>"}}}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Calendar ", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "simplify_evens_json": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Send message", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Calendar ": {"main": [[{"node": "simplify_evens_json", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "bfd04009-b77a-4130-bbfc-6afbfdc83dc9", "meta": {"instanceId": "069cc30b71ab087be24d51218b9d3ef9b2a96a675589ff764e6e5a5b9b9cf572"}, "id": "BvSTwqgC4AGaJXG7", "tags": []}